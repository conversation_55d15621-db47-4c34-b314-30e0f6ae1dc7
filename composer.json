{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "ext-imap": "*", "ext-json": "*", "ext-mailparse": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-zip": "*", "ajcastro/eager-load-pivot-relations": "^0.3.0", "alcohol/iso4217": "^4.1", "arietimmerman/laravel-scim-server": "^v1.1", "aws/aws-sdk-php": "^3.258", "calendar/icsfile": "^5.4", "chrome-php/chrome": "^1.9", "cybercog/laravel-clickhouse": "dev-master", "fruitcake/laravel-cors": "^2.0", "giggsey/libphonenumber-for-php": "^8.12", "goetas-webservices/xsd2php": "^0.4.7", "google/apiclient": "^2.0", "graphp/algorithms": "^0.9@dev", "graphp/graph": "^1@dev", "grnhse/greenhouse-tools-php": "^2.2", "guzzlehttp/guzzle": "^7.0.1", "hfig/mapi": "^1.1", "hyn/multi-tenant": "^5.8", "icamys/php-sitemap-generator": "^4.3", "inertiajs/inertia-laravel": "^1.3", "intervention/image": "^2.5", "jeroennoten/laravel-adminlte": "3.2.0", "jms/serializer": "^3.12", "jokubasr/lithuanian-names-declension": "^1.1", "jotform/jotform-api-php": "dev-master", "jwadhams/json-logic-php": "^1.5", "kevinrob/guzzle-cache-middleware": "^5.1", "kirschbaum-development/laravel-openapi-validator": "^1.0.0", "laracasts/flash": "^3.1", "laracasts/utilities": "^3.0", "laraform/laraform-laravel": "^1.0", "laravel/framework": "^9.19", "laravel/socialite": "^5.2", "laravel/tinker": "^2.0", "laravel/ui": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "league/iso3166": "^4.3", "league/oauth2-client": "^2.6", "league/oauth2-google": "^4.0", "lukemadhanga/php-document-parser": "^0.1.5", "maatwebsite/excel": "^3.1", "messente/messente-api-php": "^3.1", "microsoft/microsoft-graph": "^1.56", "mll-lab/graphql-php-scalars": "^5", "mll-lab/laravel-graphql-playground": "^2.6", "nuwave/lighthouse": "^5.45", "openai-php/client": "0.10.3", "owen-it/laravel-auditing": "^13.0", "parsecsv/php-parsecsv": "^1.3", "pgvector/pgvector": "^0.1.4", "php-ffmpeg/php-ffmpeg": "^1.3", "php-imap/php-imap": "^4.1", "php-mime-mail-parser/php-mime-mail-parser": "^7.0", "phpoffice/phpspreadsheet": "^1.15", "phpoffice/phpword": "1.1.0", "phpro/soap-client": "^3.2.0", "pusher/pusher-php-server": "^7.2", "segmentio/analytics-php": "^3.8.0", "sentry/sentry-laravel": "^3.8.1", "smalot/pdfparser": "^0.14.0", "socialiteproviders/keycloak": "^5.3", "soundasleep/html2text": "^2.1", "spatie/browsershot": "^3.36", "spatie/image-optimizer": "^1.3", "spatie/laravel-server-side-rendering": "^1.4", "spatie/temporary-directory": "^2.2", "staudenmeir/laravel-cte": "^1.6", "stolt/json-lines": "^3.0", "stripe/stripe-php": "^7.100", "symfony/dom-crawler": "^5.0", "symfony/intl": "^7.0", "theiconic/name-parser": "^1.2", "tightenco/ziggy": "^2.4", "twilio/sdk": "^6.11", "umbrellio/laravel-pg-extensions": "^5.3", "unsplash/unsplash": "^3.1.0", "vstelmakh/url-highlight": "^v3.0.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.2", "barryvdh/laravel-ide-helper": "^2.9", "brianium/paratest": "^6.6", "fakerphp/faker": "^1.23", "friendsofphp/php-cs-fixer": "^3.0", "haydenpierce/class-finder": "^0.4.4", "kkomelin/laravel-translatable-string-exporter": "^1.21", "laravel/pint": "^1.21", "laravel/telescope": "^4.8", "mockery/mockery": "^1.0", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.0", "spatie/laravel-ignition": "^1.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": false}}, "extra": {"laravel": {"dont-discover": ["laravel/telescope", "pgvector/pgvector"]}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "composer", "url": "https://composer.laraform.io"}], "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "pint": ["./vendor/bin/pint --diff=master --test"], "pint-fix": ["./vendor/bin/pint --diff=master"]}}