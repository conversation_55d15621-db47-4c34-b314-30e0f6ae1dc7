{"version": 3, "file": "rsa.min.js", "sources": ["../../../../../../node_modules/ramda/es/internal/_placeholder.js", "../../../../../../node_modules/ramda/es/internal/_isPlaceholder.js", "../../../../../../node_modules/ramda/es/internal/_curry1.js", "../../../../../../node_modules/ramda/es/internal/_curry2.js", "../../../../../../node_modules/ramda/es/internal/_curry3.js", "../../../../../../node_modules/ramda/es/internal/_has.js", "../../../../../../node_modules/ramda/es/type.js", "../../../../../../node_modules/ramda/es/internal/_isObject.js", "../../../../../../node_modules/ramda/es/internal/_isInteger.js", "../../../../../../node_modules/ramda/es/internal/_nth.js", "../../../../../../node_modules/ramda/es/internal/_isString.js", "../../../../../../node_modules/ramda/es/internal/_clone.js", "../../../../../../node_modules/ramda/es/internal/_cloneRegExp.js", "../../../../../../node_modules/ramda/es/clone.js", "../../../../../../node_modules/ramda/es/internal/_path.js", "../../../../../../node_modules/ramda/es/mergeWithKey.js", "../../../../../../node_modules/ramda/es/mergeDeepWithKey.js", "../../../../../../node_modules/ramda/es/mergeDeepWith.js", "../../../../../../node_modules/ramda/es/path.js", "../../../../../../node_modules/ramda/es/pickBy.js", "../../../../../analytics-js-common/src/utilities/checks.ts", "../../../../../analytics-js-common/src/utilities/object.ts", "../../../../../analytics-js-common/src/utilities/string.ts", "../../../../../analytics-js-common/src/utilities/eventMethodOverloads.ts", "../../../../../analytics-js-common/src/constants/loggerContexts.ts", "../../../../src/constants/app.ts", "../../../../src/constants/queryParams.ts", "../../../../src/constants/timeouts.ts", "../../../../src/components/utilities/globals.ts", "../../../../src/components/preloadBuffer/index.ts", "../../../../../analytics-js-common/src/constants/logMessages.ts", "../../../../../analytics-js-common/src/utilities/json.ts", "../../../../../analytics-js-common/src/utilities/errors.ts", "../../../../../analytics-js-common/src/services/ExternalSrcLoader/jsFileLoader.ts", "../../../../../analytics-js-common/src/constants/htmlAttributes.ts", "../../../../../analytics-js-common/src/services/ExternalSrcLoader/ExternalSrcLoader.ts", "../../../../../analytics-js-common/src/constants/timeouts.ts", "../../../../../../node_modules/@preact/signals-core/dist/signals-core.module.js", "../../../../../analytics-js-common/src/services/BufferQueue/BufferQueue.ts", "../../../../src/services/Logger/Logger.ts", "../../../../../analytics-js-common/src/types/Storage.ts", "../../../../src/constants/logMessages.ts", "../../../../../analytics-js-common/src/constants/urls.ts", "../../../../src/constants/urls.ts", "../../../../src/components/configManager/constants.ts", "../../../../src/state/slices/loadOptions.ts", "../../../../src/components/userSessionManager/constants.ts", "../../../../src/state/slices/session.ts", "../../../../src/state/slices/capabilities.ts", "../../../../src/state/slices/reporting.ts", "../../../../src/state/slices/source.ts", "../../../../src/state/slices/lifecycle.ts", "../../../../src/state/slices/consents.ts", "../../../../src/state/slices/metrics.ts", "../../../../src/state/slices/context.ts", "../../../../src/state/slices/nativeDestinations.ts", "../../../../src/state/slices/eventBuffer.ts", "../../../../src/state/slices/plugins.ts", "../../../../src/state/slices/storage.ts", "../../../../src/state/slices/serverCookies.ts", "../../../../src/state/slices/dataPlaneEvents.ts", "../../../../src/state/index.ts", "../../../../src/services/PluginEngine/PluginEngine.ts", "../../../../src/constants/errors.ts", "../../../../src/services/ErrorHandler/ErrorHandler.ts", "../../../../src/services/ErrorHandler/processError.ts", "../../../../src/services/ErrorHandler/constant.ts", "../../../../../analytics-js-common/src/utilities/destinations.ts", "../../../../src/components/pluginsManager/pluginNames.ts", "../../../../src/components/pluginsManager/federatedModulesBuildPluginImports.ts", "../../../../src/components/pluginsManager/pluginsInventory.ts", "../../../../src/components/pluginsManager/PluginsManager.ts", "../../../../src/services/HttpClient/xhr/xhrResponseHandler.ts", "../../../../src/services/HttpClient/xhr/xhrRequestHandler.ts", "../../../../src/services/HttpClient/HttpClient.ts", "../../../../../analytics-js-common/src/constants/storages.ts", "../../../../src/constants/storage.ts", "../../../../src/services/StoreManager/types.ts", "../../../../src/components/capabilitiesManager/detection/browser.ts", "../../../../src/components/capabilitiesManager/detection/dom.ts", "../../../../src/components/capabilitiesManager/detection/screen.ts", "../../../../src/components/capabilitiesManager/detection/storage.ts", "../../../../src/services/StoreManager/component-cookie/index.ts", "../../../../src/services/StoreManager/top-domain/index.ts", "../../../../src/services/StoreManager/storages/defaultOptions.ts", "../../../../src/services/StoreManager/storages/CookieStorage.ts", "../../../../src/services/StoreManager/storages/InMemoryStorage.ts", "../../../../../../node_modules/storejs/dist/store.js", "../../../../src/services/StoreManager/storages/LocalStorage.ts", "../../../../src/services/StoreManager/storages/sessionStorage.ts", "../../../../src/services/StoreManager/storages/storageEngine.ts", "../../../../src/services/StoreManager/Store.ts", "../../../../src/services/StoreManager/StoreManager.ts", "../../../../src/services/StoreManager/utils.ts", "../../../../../analytics-js-common/src/utilities/url.ts", "../../../../src/components/configManager/util/validate.ts", "../../../../src/components/utilities/url.ts", "../../../../src/components/configManager/util/dataPlaneResolver.ts", "../../../../../analytics-js-common/src/constants/consent.ts", "../../../../../analytics-js-common/src/constants/integrationsConfig.ts", "../../../../src/components/utilities/consent.ts", "../../../../src/components/configManager/util/commonUtil.ts", "../../../../src/components/utilities/statsCollection.ts", "../../../../../analytics-js-common/src/utilities/detect.ts", "../../../../src/components/configManager/ConfigManager.ts", "../../../../src/components/configManager/util/cdnPaths.ts", "../../../../src/components/utilities/destinations.ts", "../../../../../analytics-js-common/src/utilities/timezone.ts", "../../../../src/components/utilities/page.ts", "../../../../src/components/capabilitiesManager/polyfill/index.ts", "../../../../src/components/capabilitiesManager/CapabilitiesManager.ts", "../../../../src/components/capabilitiesManager/detection/clientHint.ts", "../../../../src/components/capabilitiesManager/detection/adBlockers.ts", "../../../../../../node_modules/@lukeed/uuid/secure/index.mjs", "../../../../../../node_modules/@lukeed/csprng/browser/index.mjs", "../../../../../../node_modules/@lukeed/uuid/dist/index.mjs", "../../../../../analytics-js-common/src/utilities/crypto.ts", "../../../../../analytics-js-common/src/utilities/uuId.ts", "../../../../src/components/eventManager/constants.ts", "../../../../src/components/utilities/number.ts", "../../../../src/components/userSessionManager/utils.ts", "../../../../src/components/eventManager/utilities.ts", "../../../../../analytics-js-common/src/utilities/timestamp.ts", "../../../../src/components/eventManager/RudderEventFactory.ts", "../../../../src/components/eventManager/EventManager.ts", "../../../../src/components/userSessionManager/UserSessionManager.ts", "../../../../src/components/pluginsManager/defaultPluginsList.ts", "../../../../src/components/eventRepository/constants.ts", "../../../../src/components/eventRepository/utils.ts", "../../../../src/components/eventRepository/EventRepository.ts", "../../../../src/components/core/utilities.ts", "../../../../src/components/core/Analytics.ts", "../../../../src/components/utilities/loadOptions.ts", "../../../../src/app/RudderAnalytics.ts", "../../../../src/browser.ts"], "sourcesContent": ["export default {\n  '@@functional/placeholder': true\n};", "import _placeholder from \"./_placeholder.js\";\nexport default function _isPlaceholder(a) {\n  return a === _placeholder;\n}", "import _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Optimized internal one-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curry1(fn) {\n  return function f1(a) {\n    if (arguments.length === 0 || _isPlaceholder(a)) {\n      return f1;\n    } else {\n      return fn.apply(this, arguments);\n    }\n  };\n}", "import _curry1 from \"./_curry1.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Optimized internal two-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curry2(fn) {\n  return function f2(a, b) {\n    switch (arguments.length) {\n      case 0:\n        return f2;\n      case 1:\n        return _isPlaceholder(a) ? f2 : _curry1(function (_b) {\n          return fn(a, _b);\n        });\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f2 : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b);\n        }) : fn(a, b);\n    }\n  };\n}", "import _curry1 from \"./_curry1.js\";\nimport _curry2 from \"./_curry2.js\";\nimport _isPlaceholder from \"./_isPlaceholder.js\";\n\n/**\n * Optimized internal three-arity curry function.\n *\n * @private\n * @category Function\n * @param {Function} fn The function to curry.\n * @return {Function} The curried function.\n */\nexport default function _curry3(fn) {\n  return function f3(a, b, c) {\n    switch (arguments.length) {\n      case 0:\n        return f3;\n      case 1:\n        return _isPlaceholder(a) ? f3 : _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        });\n      case 2:\n        return _isPlaceholder(a) && _isPlaceholder(b) ? f3 : _isPlaceholder(a) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _curry1(function (_c) {\n          return fn(a, b, _c);\n        });\n      default:\n        return _isPlaceholder(a) && _isPlaceholder(b) && _isPlaceholder(c) ? f3 : _isPlaceholder(a) && _isPlaceholder(b) ? _curry2(function (_a, _b) {\n          return fn(_a, _b, c);\n        }) : _isPlaceholder(a) && _isPlaceholder(c) ? _curry2(function (_a, _c) {\n          return fn(_a, b, _c);\n        }) : _isPlaceholder(b) && _isPlaceholder(c) ? _curry2(function (_b, _c) {\n          return fn(a, _b, _c);\n        }) : _isPlaceholder(a) ? _curry1(function (_a) {\n          return fn(_a, b, c);\n        }) : _isPlaceholder(b) ? _curry1(function (_b) {\n          return fn(a, _b, c);\n        }) : _isPlaceholder(c) ? _curry1(function (_c) {\n          return fn(a, b, _c);\n        }) : fn(a, b, c);\n    }\n  };\n}", "export default function _has(prop, obj) {\n  return Object.prototype.hasOwnProperty.call(obj, prop);\n}", "import _curry1 from \"./internal/_curry1.js\";\n\n/**\n * Gives a single-word string description of the (native) type of a value,\n * returning such answers as 'Object', 'Number', 'Array', or 'Null'. Does not\n * attempt to distinguish user Object types any further, reporting them all as\n * 'Object'.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Type\n * @sig * -> String\n * @param {*} val The value to test\n * @return {String}\n * @example\n *\n *      R.type({}); //=> \"Object\"\n *      R.type(1); //=> \"Number\"\n *      R.type(false); //=> \"Boolean\"\n *      R.type('s'); //=> \"String\"\n *      R.type(null); //=> \"Null\"\n *      R.type([]); //=> \"Array\"\n *      R.type(/[A-z]/); //=> \"RegExp\"\n *      R.type(() => {}); //=> \"Function\"\n *      R.type(async () => {}); //=> \"AsyncFunction\"\n *      R.type(undefined); //=> \"Undefined\"\n *      R.type(BigInt(123)); //=> \"BigInt\"\n */\nvar type = /*#__PURE__*/_curry1(function type(val) {\n  return val === null ? 'Null' : val === undefined ? 'Undefined' : Object.prototype.toString.call(val).slice(8, -1);\n});\nexport default type;", "export default function _isObject(x) {\n  return Object.prototype.toString.call(x) === '[object Object]';\n}", "/**\n * Determine if the passed argument is an integer.\n *\n * @private\n * @param {*} n\n * @category Type\n * @return {Boolean}\n */\nexport default Number.isInteger || function _isInteger(n) {\n  return n << 0 === n;\n};", "import _isString from \"./_isString.js\";\nexport default function _nth(offset, list) {\n  var idx = offset < 0 ? list.length + offset : offset;\n  return _isString(list) ? list.charAt(idx) : list[idx];\n}", "export default function _isString(x) {\n  return Object.prototype.toString.call(x) === '[object String]';\n}", "import _cloneRegExp from \"./_cloneRegExp.js\";\nimport type from \"../type.js\";\n\n/**\n * Copies an object.\n *\n * @private\n * @param {*} value The value to be copied\n * @param {Boolean} deep Whether or not to perform deep cloning.\n * @return {*} The copied value.\n */\nexport default function _clone(value, deep, map) {\n  map || (map = new _ObjectMap());\n\n  // this avoids the slower switch with a quick if decision removing some milliseconds in each run.\n  if (_isPrimitive(value)) {\n    return value;\n  }\n  var copy = function copy(copiedValue) {\n    // Check for circular and same references on the object graph and return its corresponding clone.\n    var cachedCopy = map.get(value);\n    if (cachedCopy) {\n      return cachedCopy;\n    }\n    map.set(value, copiedValue);\n    for (var key in value) {\n      if (Object.prototype.hasOwnProperty.call(value, key)) {\n        copiedValue[key] = deep ? _clone(value[key], true, map) : value[key];\n      }\n    }\n    return copiedValue;\n  };\n  switch (type(value)) {\n    case 'Object':\n      return copy(Object.create(Object.getPrototypeOf(value)));\n    case 'Array':\n      return copy(Array(value.length));\n    case 'Date':\n      return new Date(value.valueOf());\n    case 'RegExp':\n      return _cloneRegExp(value);\n    case 'Int8Array':\n    case 'Uint8Array':\n    case 'Uint8ClampedArray':\n    case 'Int16Array':\n    case 'Uint16Array':\n    case 'Int32Array':\n    case 'Uint32Array':\n    case 'Float32Array':\n    case 'Float64Array':\n    case 'BigInt64Array':\n    case 'BigUint64Array':\n      return value.slice();\n    default:\n      return value;\n  }\n}\nfunction _isPrimitive(param) {\n  var type = typeof param;\n  return param == null || type != 'object' && type != 'function';\n}\nvar _ObjectMap = /*#__PURE__*/function () {\n  function _ObjectMap() {\n    this.map = {};\n    this.length = 0;\n  }\n  _ObjectMap.prototype.set = function (key, value) {\n    var hashedKey = this.hash(key);\n    var bucket = this.map[hashedKey];\n    if (!bucket) {\n      this.map[hashedKey] = bucket = [];\n    }\n    bucket.push([key, value]);\n    this.length += 1;\n  };\n  _ObjectMap.prototype.hash = function (key) {\n    var hashedKey = [];\n    for (var value in key) {\n      hashedKey.push(Object.prototype.toString.call(key[value]));\n    }\n    return hashedKey.join();\n  };\n  _ObjectMap.prototype.get = function (key) {\n    /**\n     * depending on the number of objects to be cloned is faster to just iterate over the items in the map just because the hash function is so costly,\n     * on my tests this number is 180, anything above that using the hash function is faster.\n     */\n    if (this.length <= 180) {\n      for (var p in this.map) {\n        var bucket = this.map[p];\n        for (var i = 0; i < bucket.length; i += 1) {\n          var element = bucket[i];\n          if (element[0] === key) {\n            return element[1];\n          }\n        }\n      }\n      return;\n    }\n    var hashedKey = this.hash(key);\n    var bucket = this.map[hashedKey];\n    if (!bucket) {\n      return;\n    }\n    for (var i = 0; i < bucket.length; i += 1) {\n      var element = bucket[i];\n      if (element[0] === key) {\n        return element[1];\n      }\n    }\n  };\n  return _ObjectMap;\n}();", "export default function _cloneRegExp(pattern) {\n  return new RegExp(pattern.source, pattern.flags ? pattern.flags : (pattern.global ? 'g' : '') + (pattern.ignoreCase ? 'i' : '') + (pattern.multiline ? 'm' : '') + (pattern.sticky ? 'y' : '') + (pattern.unicode ? 'u' : '') + (pattern.dotAll ? 's' : ''));\n}", "import _clone from \"./internal/_clone.js\";\nimport _curry1 from \"./internal/_curry1.js\";\n\n/**\n * Creates a deep copy of the source that can be used in place of the source\n * object without retaining any references to it.\n * The source object may contain (nested) `Array`s and `Object`s,\n * `Number`s, `String`s, `Boolean`s and `Date`s.\n * `Function`s are assigned by reference rather than copied.\n *\n * Dispatches to a `clone` method if present.\n *\n * Note that if the source object has multiple nodes that share a reference,\n * the returned object will have the same structure, but the references will\n * be pointed to the location within the cloned value.\n *\n * @func\n * @memberOf R\n * @since v0.1.0\n * @category Object\n * @sig {*} -> {*}\n * @param {*} value The object or array to clone\n * @return {*} A deeply cloned copy of `val`\n * @example\n *\n *      const objects = [{}, {}, {}];\n *      const objectsClone = R.clone(objects);\n *      objects === objectsClone; //=> false\n *      objects[0] === objectsClone[0]; //=> false\n */\nvar clone = /*#__PURE__*/_curry1(function clone(value) {\n  return value != null && typeof value.clone === 'function' ? value.clone() : _clone(value, true);\n});\nexport default clone;", "import _isInteger from \"./_isInteger.js\";\nimport _nth from \"./_nth.js\";\nexport default function _path(pathAr, obj) {\n  var val = obj;\n  for (var i = 0; i < pathAr.length; i += 1) {\n    if (val == null) {\n      return undefined;\n    }\n    var p = pathAr[i];\n    if (_isInteger(p)) {\n      val = _nth(p, val);\n    } else {\n      val = val[p];\n    }\n  }\n  return val;\n}", "import _curry3 from \"./internal/_curry3.js\";\nimport _has from \"./internal/_has.js\";\n\n/**\n * Creates a new object with the own properties of the two provided objects. If\n * a key exists in both objects, the provided function is applied to the key\n * and the values associated with the key in each object, with the result being\n * used as the value associated with the key in the returned object.\n *\n * @func\n * @memberOf R\n * @since v0.19.0\n * @category Object\n * @sig ((String, a, a) -> a) -> {a} -> {a} -> {a}\n * @param {Function} fn\n * @param {Object} l\n * @param {Object} r\n * @return {Object}\n * @see R.mergeDeepWithKey, R.merge, R.mergeWith\n * @example\n *\n *      let concatValues = (k, l, r) => k == 'values' ? R.concat(l, r) : r\n *      R.mergeWithKey(concatValues,\n *                     { a: true, thing: 'foo', values: [10, 20] },\n *                     { b: true, thing: 'bar', values: [15, 35] });\n *      //=> { a: true, b: true, thing: 'bar', values: [10, 20, 15, 35] }\n * @symb R.mergeWith<PERSON>ey(f, { x: 1, y: 2 }, { y: 5, z: 3 }) = { x: 1, y: f('y', 2, 5), z: 3 }\n */\nvar mergeWithKey = /*#__PURE__*/_curry3(function mergeWithKey(fn, l, r) {\n  var result = {};\n  var k;\n  l = l || {};\n  r = r || {};\n  for (k in l) {\n    if (_has(k, l)) {\n      result[k] = _has(k, r) ? fn(k, l[k], r[k]) : l[k];\n    }\n  }\n  for (k in r) {\n    if (_has(k, r) && !_has(k, result)) {\n      result[k] = r[k];\n    }\n  }\n  return result;\n});\nexport default mergeWithKey;", "import _curry3 from \"./internal/_curry3.js\";\nimport _isObject from \"./internal/_isObject.js\";\nimport mergeWithKey from \"./mergeWithKey.js\";\n\n/**\n * Creates a new object with the own properties of the two provided objects.\n * If a key exists in both objects:\n * - and both associated values are also objects then the values will be\n *   recursively merged.\n * - otherwise the provided function is applied to the key and associated values\n *   using the resulting value as the new value associated with the key.\n * If a key only exists in one object, the value will be associated with the key\n * of the resulting object.\n *\n * @func\n * @memberOf R\n * @since v0.24.0\n * @category Object\n * @sig ((String, a, a) -> a) -> {a} -> {a} -> {a}\n * @param {Function} fn\n * @param {Object} lObj\n * @param {Object} rObj\n * @return {Object}\n * @see R.mergeWith<PERSON>ey, R.mergeDeepWith\n * @example\n *\n *      let concatValues = (k, l, r) => k == 'values' ? R.concat(l, r) : r\n *      R.mergeDeepWithKey(concatValues,\n *                         { a: true, c: { thing: 'foo', values: [10, 20] }},\n *                         { b: true, c: { thing: 'bar', values: [15, 35] }});\n *      //=> { a: true, b: true, c: { thing: 'bar', values: [10, 20, 15, 35] }}\n */\nvar mergeDeepWithKey = /*#__PURE__*/_curry3(function mergeDeepWithKey(fn, lObj, rObj) {\n  return mergeWithKey(function (k, lVal, rVal) {\n    if (_isObject(lVal) && _isObject(rVal)) {\n      return mergeDeepWithKey(fn, lVal, rVal);\n    } else {\n      return fn(k, lVal, rVal);\n    }\n  }, lObj, rObj);\n});\nexport default mergeDeepWithKey;", "import _curry3 from \"./internal/_curry3.js\";\nimport mergeDeepWith<PERSON>ey from \"./mergeDeepWithKey.js\";\n\n/**\n * Creates a new object with the own properties of the two provided objects.\n * If a key exists in both objects:\n * - and both associated values are also objects then the values will be\n *   recursively merged.\n * - otherwise the provided function is applied to associated values using the\n *   resulting value as the new value associated with the key.\n * If a key only exists in one object, the value will be associated with the key\n * of the resulting object.\n *\n * @func\n * @memberOf R\n * @since v0.24.0\n * @category Object\n * @sig ((a, a) -> a) -> {a} -> {a} -> {a}\n * @param {Function} fn\n * @param {Object} lObj\n * @param {Object} rObj\n * @return {Object}\n * @see R.mergeWith, R.mergeDeepWithKey\n * @example\n *\n *      R.mergeDeepWith(R.concat,\n *                      { a: true, c: { values: [10, 20] }},\n *                      { b: true, c: { values: [15, 35] }});\n *      //=> { a: true, b: true, c: { values: [10, 20, 15, 35] }}\n */\nvar mergeDeepWith = /*#__PURE__*/_curry3(function mergeDeepWith(fn, lObj, rObj) {\n  return mergeDeepWithKey(function (k, lVal, rVal) {\n    return fn(lVal, rVal);\n  }, lObj, rObj);\n});\nexport default mergeDeepWith;", "import _curry2 from \"./internal/_curry2.js\";\nimport _path from \"./internal/_path.js\";\n\n/**\n * Retrieves the value at a given path. The nodes of the path can be arbitrary strings or non-negative integers.\n * For anything else, the value is unspecified. Integer paths are meant to index arrays, strings are meant for objects.\n *\n * @func\n * @memberOf R\n * @since v0.2.0\n * @category Object\n * @typedefn Idx = String | Int | Symbol\n * @sig [Idx] -> {a} -> a | Undefined\n * @sig Idx = String | NonNegativeInt\n * @param {Array} path The path to use.\n * @param {Object} obj The object or array to retrieve the nested property from.\n * @return {*} The data at `path`.\n * @see R.prop, R.nth, <PERSON>.assocPath, <PERSON>.dissocPath\n * @example\n *\n *      R.path(['a', 'b'], {a: {b: 2}}); //=> 2\n *      R.path(['a', 'b'], {c: {b: 2}}); //=> undefined\n *      R.path(['a', 'b', 0], {a: {b: [1, 2, 3]}}); //=> 1\n *      R.path(['a', 'b', -2], {a: {b: [1, 2, 3]}}); //=> 2\n *      R.path([2], {'2': 2}); //=> 2\n *      R.path([-2], {'-2': 'a'}); //=> undefined\n */\n\nvar path = /*#__PURE__*/_curry2(_path);\nexport default path;", "import _curry2 from \"./internal/_curry2.js\";\n\n/**\n * Returns a partial copy of an object containing only the keys that satisfy\n * the supplied predicate.\n *\n * @func\n * @memberOf R\n * @since v0.8.0\n * @category Object\n * @sig ((v, k) -> Boolean) -> {k: v} -> {k: v}\n * @param {Function} pred A predicate to determine whether or not a key\n *        should be included on the output object.\n * @param {Object} obj The object to copy from\n * @return {Object} A new object with only properties that satisfy `pred`\n *         on it.\n * @see R.pick, R.filter\n * @example\n *\n *      const isUpperCase = (val, key) => key.toUpperCase() === key;\n *      R.pickBy(isUpperCase, {a: 1, b: 2, A: 3, B: 4}); //=> {A: 3, B: 4}\n */\nvar pickBy = /*#__PURE__*/_curry2(function pickBy(test, obj) {\n  var result = {};\n  for (var prop in obj) {\n    if (test(obj[prop], prop, obj)) {\n      result[prop] = obj[prop];\n    }\n  }\n  return result;\n});\nexport default pickBy;", "/**\n * A function to check given value is a function\n * @param value input value\n * @returns boolean\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst isFunction = (value: any): value is Function =>\n  typeof value === 'function' && Boolean(value.constructor && value.call && value.apply);\n\n/**\n * A function to check given value is a string\n * @param value input value\n * @returns boolean\n */\nconst isString = (value: any): value is string => typeof value === 'string';\n\n/**\n * A function to check given value is null or not\n * @param value input value\n * @returns boolean\n */\nconst isNull = (value: any): value is null => value === null;\n\n/**\n * A function to check given value is undefined\n * @param value input value\n * @returns boolean\n */\nconst isUndefined = (value: any): value is undefined => typeof value === 'undefined';\n\n/**\n * A function to check given value is null or undefined\n * @param value input value\n * @returns boolean\n */\nconst isNullOrUndefined = (value: any): boolean => isNull(value) || isUndefined(value);\n\n/**\n * A function to check given value is defined\n * @param value input value\n * @returns boolean\n */\nconst isDefined = (value: any): boolean => !isUndefined(value);\n\n/**\n * A function to check given value is defined and not null\n * @param value input value\n * @returns boolean\n */\nconst isDefinedAndNotNull = (value: any): boolean => !isNullOrUndefined(value);\n\n/**\n * A function to check given value is defined and not null\n * @param value input value\n * @returns boolean\n */\nconst isDefinedNotNullAndNotEmptyString = (value: any): boolean =>\n  isDefinedAndNotNull(value) && value !== '';\n\n/**\n * Determines if the input is an instance of Error\n * @param obj input value\n * @returns true if the input is an instance of Error and false otherwise\n */\nconst isTypeOfError = (obj: any): obj is Error => obj instanceof Error;\n\nexport {\n  isFunction,\n  isString,\n  isNull,\n  isUndefined,\n  isNullOrUndefined,\n  isTypeOfError,\n  isDefined,\n  isDefinedAndNotNull,\n  isDefinedNotNullAndNotEmptyString,\n};\n", "import { clone, mergeDeepWith, path, pickBy } from 'ramda';\nimport { isDefined, isDefinedAndNotNull, isNull } from './checks';\n\nconst getValueByPath = (obj: Record<string, any>, keyPath: string): any => {\n  const pathParts = keyPath.split('.');\n  return path(pathParts, obj);\n};\n\nconst hasValueByPath = (obj: Record<string, any>, path: string): boolean =>\n  Boolean(getValueByPath(obj, path));\n\n/**\n * Checks if the input is an object literal or built-in object type and not null\n * @param value Input value\n * @returns true if the input is an object and not null\n */\nconst isObjectAndNotNull = (value: any): value is object =>\n  !isNull(value) && typeof value === 'object' && !Array.isArray(value);\n\n/**\n * Checks if the input is an object literal and not null\n * @param value Input value\n * @returns true if the input is an object and not null\n */\nconst isObjectLiteralAndNotNull = <T>(value?: T): value is T =>\n  !isNull(value) && Object.prototype.toString.call(value) === '[object Object]';\n\nconst mergeDeepRightObjectArrays = (\n  leftValue: any | any[],\n  rightValue: any | any[],\n): any | any[] => {\n  if (!Array.isArray(leftValue) || !Array.isArray(rightValue)) {\n    return clone(rightValue);\n  }\n\n  const mergedArray = clone(leftValue);\n  rightValue.forEach((value, index) => {\n    mergedArray[index] =\n      Array.isArray(value) || isObjectAndNotNull(value)\n        ? // eslint-disable-next-line @typescript-eslint/no-use-before-define\n          mergeDeepRight(mergedArray[index], value)\n        : value;\n  });\n  return mergedArray;\n};\n\nconst mergeDeepRight = <T = Record<string, any>>(\n  leftObject: Record<string, any>,\n  rightObject: Record<string, any>,\n): T => mergeDeepWith(mergeDeepRightObjectArrays, leftObject, rightObject);\n\n/**\n Checks if the input is a non-empty object literal type and not undefined or null\n * @param value input any\n * @returns boolean\n */\nconst isNonEmptyObject = <T>(value?: T): value is T =>\n  isObjectLiteralAndNotNull(value) && Object.keys(value as any).length > 0;\n\n/**\n * A utility to recursively remove undefined values from an object\n * @param obj input object\n * @returns a new object\n */\nconst removeUndefinedValues = <T = Record<string, any>>(obj: T): T => {\n  const result = pickBy(isDefined, obj) as Record<string, any>;\n  Object.keys(result).forEach(key => {\n    const value = result[key];\n    if (isObjectLiteralAndNotNull(value)) {\n      result[key] = removeUndefinedValues(value);\n    }\n  });\n\n  return result as T;\n};\n\n/**\n * A utility to recursively remove undefined and null values from an object\n * @param obj input object\n * @returns a new object\n */\nconst removeUndefinedAndNullValues = <T = Record<string, any>>(obj: T): T => {\n  const result = pickBy(isDefinedAndNotNull, obj) as Record<string, any>;\n  Object.keys(result).forEach(key => {\n    const value = result[key];\n    if (isObjectLiteralAndNotNull(value)) {\n      result[key] = removeUndefinedAndNullValues(value);\n    }\n  });\n\n  return result as T;\n};\n\n/**\n * A utility to get all the values from an object\n * @param obj Input object\n * @returns an array of values from the input object\n */\nconst getObjectValues = <T = Record<string, any>>(obj: T): any[] => {\n  const result: any[] = [];\n  Object.keys(obj as Record<string, any>).forEach(key => {\n    result.push((obj as Record<string, any>)[key]);\n  });\n\n  return result;\n};\n\nexport {\n  getValueByPath,\n  hasValueByPath,\n  mergeDeepRightObjectArrays,\n  mergeDeepRight,\n  isObjectAndNotNull,\n  isNonEmptyObject,\n  isObjectLiteralAndNotNull,\n  removeUndefinedValues,\n  removeUndefinedAndNullValues,\n  getObjectValues,\n};\n", "import type { Nullable } from '../types/Nullable';\nimport { isNullOrUndefined, isString } from './checks';\n\n// TODO: see if bundle size is bumped up if we use ramda trim instead of custom\nconst trim = (value: string): string => value.replace(/^\\s+|\\s+$/gm, '');\n\nconst removeDoubleSpaces = (value: string): string => value.replace(/ {2,}/g, ' ');\n\n/**\n * A function to convert values to string\n * @param val input value\n * @returns stringified value\n */\nconst tryStringify = (val?: any): Nullable<string> | undefined => {\n  let retVal = val;\n  if (!isString(val) && !isNullOrUndefined(val)) {\n    try {\n      retVal = JSON.stringify(val);\n    } catch (e) {\n      retVal = null;\n    }\n  }\n  return retVal;\n};\n\n// The following text encoding and decoding is done before base64 encoding to prevent\n// https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n\n/**\n * Converts a base64 encoded string to bytes array\n * @param base64Str base64 encoded string\n * @returns bytes array\n */\nconst base64ToBytes = (base64Str: string): Uint8Array => {\n  const binString = (globalThis as typeof window).atob(base64Str);\n  const bytes = binString.split('').map(char => char.charCodeAt(0));\n  return new Uint8Array(bytes);\n};\n\n/**\n * Converts a bytes array to base64 encoded string\n * @param bytes bytes array to be converted to base64\n * @returns base64 encoded string\n */\nconst bytesToBase64 = (bytes: Uint8Array): string => {\n  const binString = Array.from(bytes, x => String.fromCodePoint(x)).join('');\n  return (globalThis as typeof window).btoa(binString);\n};\n\n/**\n * Encodes a string to base64 even with unicode characters\n * @param value input string\n * @returns base64 encoded string\n */\nconst toBase64 = (value: string): string => bytesToBase64(new TextEncoder().encode(value));\n\n/**\n * Decodes a base64 encoded string\n * @param value base64 encoded string\n * @returns decoded string\n */\nconst fromBase64 = (value: string): string => new TextDecoder().decode(base64ToBytes(value));\n\nexport { trim, removeDoubleSpaces, tryStringify, toBase64, fromBase64 };\n", "import { clone } from 'ramda';\nimport type { ApiObject } from '../types/ApiObject';\nimport type { ApiCallback, ApiOptions } from '../types/EventApi';\nimport type { Nullable } from '../types/Nullable';\nimport { isObjectLiteralAndNotNull, mergeDeepRight } from './object';\nimport { isDefined, isDefinedAndNotNull, isFunction, isNull, isString } from './checks';\nimport { tryStringify } from './string';\nimport type { IdentifyTraits } from '../types/traits';\n\nexport type PageCallOptions = {\n  category?: string;\n  name?: string;\n  properties?: Nullable<ApiObject>;\n  options?: Nullable<ApiOptions>;\n  callback?: ApiCallback;\n};\n\nexport type TrackCallOptions = {\n  name: string;\n  properties?: Nullable<ApiObject>;\n  options?: Nullable<ApiOptions>;\n  callback?: ApiCallback;\n};\n\nexport type IdentifyCallOptions = {\n  userId?: string | null;\n  traits?: Nullable<IdentifyTraits>;\n  options?: Nullable<ApiOptions>;\n  callback?: ApiCallback;\n};\n\nexport type AliasCallOptions = {\n  to?: Nullable<string>;\n  from?: Nullable<string>;\n  options?: Nullable<ApiOptions>;\n  callback?: ApiCallback;\n};\n\nexport type GroupCallOptions = {\n  groupId?: Nullable<string>;\n  traits?: Nullable<ApiObject>;\n  options?: Nullable<ApiOptions>;\n  callback?: ApiCallback;\n};\n\n// TODO: is there any specific reason why we set the overloaded values to null instead of undefined?\n//   if yes make them null instead of omitting in overloaded cases\n\n/*\n * Normalise the overloaded arguments of the page call facade\n */\nconst pageArgumentsToCallOptions = (\n  category?: string | Nullable<ApiObject> | ApiCallback,\n  name?: string | Nullable<ApiOptions> | Nullable<ApiObject> | ApiCallback,\n  properties?: Nullable<ApiOptions> | Nullable<ApiObject> | ApiCallback,\n  options?: Nullable<ApiOptions> | ApiCallback,\n  callback?: ApiCallback,\n): PageCallOptions => {\n  const payload: PageCallOptions = {\n    category: category as string,\n    name: name as string,\n    properties: properties as Nullable<ApiObject>,\n    options: options as Nullable<ApiOptions>,\n  };\n\n  if (isFunction(callback)) {\n    payload.callback = callback;\n  }\n\n  if (isFunction(options)) {\n    payload.category = category as string;\n    payload.name = name as string;\n    payload.properties = properties as Nullable<ApiObject>;\n    delete payload.options;\n    payload.callback = options as ApiCallback;\n  }\n\n  if (isFunction(properties)) {\n    payload.category = category as string;\n    payload.name = name as string;\n    delete payload.properties;\n    delete payload.options;\n    payload.callback = properties as ApiCallback;\n  }\n\n  if (isFunction(name)) {\n    payload.category = category as string;\n    delete payload.name;\n    delete payload.properties;\n    delete payload.options;\n    payload.callback = name as ApiCallback;\n  }\n\n  if (isFunction(category)) {\n    delete payload.category;\n    delete payload.name;\n    delete payload.properties;\n    delete payload.options;\n    payload.callback = category as ApiCallback;\n  }\n\n  if (isObjectLiteralAndNotNull(category)) {\n    delete payload.name;\n    delete payload.category;\n    payload.properties = category as Nullable<ApiObject>;\n    payload.options = name as Nullable<ApiOptions>;\n  } else if (isObjectLiteralAndNotNull(name)) {\n    delete payload.name;\n    payload.properties = name as Nullable<ApiObject>;\n    payload.options = !isFunction(properties) ? (properties as Nullable<ApiOptions>) : null;\n  }\n\n  // if the category argument alone is provided b/w category and name,\n  // use it as name and set category to undefined\n  if (isString(category) && !isString(name)) {\n    delete payload.category;\n    payload.name = category as string;\n  }\n\n  // Rest of the code is just to clean up undefined values\n  // and set some proper defaults\n  // Also, to clone the incoming object type arguments\n  if (!isDefined(payload.category)) {\n    delete payload.category;\n  }\n\n  if (!isDefined(payload.name)) {\n    delete payload.name;\n  }\n\n  payload.properties = payload.properties ? clone(payload.properties) : {};\n\n  if (isDefined(payload.options)) {\n    payload.options = clone(payload.options);\n  } else {\n    delete payload.options;\n  }\n\n  // add name and category to properties\n  payload.properties = mergeDeepRight(\n    isObjectLiteralAndNotNull(payload.properties) ? payload.properties : {},\n    {\n      name: isString(payload.name) ? payload.name : null,\n      category: isString(payload.category) ? payload.category : null,\n    },\n  );\n\n  return payload;\n};\n\n/*\n * Normalise the overloaded arguments of the track call facade\n */\nconst trackArgumentsToCallOptions = (\n  event: string,\n  properties?: Nullable<ApiObject> | ApiCallback,\n  options?: Nullable<ApiOptions> | ApiCallback,\n  callback?: ApiCallback,\n): TrackCallOptions => {\n  const payload: TrackCallOptions = {\n    name: event,\n    properties: properties as Nullable<ApiObject>,\n    options: options as Nullable<ApiOptions>,\n  };\n\n  if (isFunction(callback)) {\n    payload.callback = callback;\n  }\n\n  if (isFunction(options)) {\n    payload.properties = properties as Nullable<ApiObject>;\n    delete payload.options;\n    payload.callback = options as ApiCallback;\n  }\n\n  if (isFunction(properties)) {\n    delete payload.properties;\n    delete payload.options;\n    payload.callback = properties as ApiCallback;\n  }\n\n  // Rest of the code is just to clean up undefined values\n  // and set some proper defaults\n  // Also, to clone the incoming object type arguments\n  payload.properties = isDefinedAndNotNull(payload.properties) ? clone(payload.properties) : {};\n\n  if (isDefined(payload.options)) {\n    payload.options = clone(payload.options);\n  } else {\n    delete payload.options;\n  }\n\n  return payload;\n};\n\n/*\n * Normalise the overloaded arguments of the identify call facade\n */\nconst identifyArgumentsToCallOptions = (\n  userId?: Nullable<IdentifyTraits | string | number>,\n  traits?: Nullable<IdentifyTraits> | Nullable<ApiOptions> | ApiCallback,\n  options?: Nullable<ApiOptions> | ApiCallback,\n  callback?: ApiCallback,\n): IdentifyCallOptions => {\n  const payload: IdentifyCallOptions = {\n    userId: userId as string,\n    traits: traits as Nullable<IdentifyTraits>,\n    options: options as Nullable<ApiOptions>,\n  };\n\n  if (isFunction(callback)) {\n    payload.callback = callback;\n  }\n\n  if (isFunction(options)) {\n    payload.userId = userId as string;\n    payload.traits = traits as Nullable<IdentifyTraits>;\n    delete payload.options;\n    payload.callback = options as ApiCallback;\n  }\n\n  if (isFunction(traits)) {\n    payload.userId = userId as string;\n    delete payload.traits;\n    delete payload.options;\n    payload.callback = traits as ApiCallback;\n  }\n\n  if (isObjectLiteralAndNotNull(userId) || isNull(userId)) {\n    // Explicitly set null to prevent resetting the existing value\n    // in the Analytics class\n    payload.userId = null;\n    payload.traits = userId as Nullable<IdentifyTraits>;\n    payload.options = traits as Nullable<ApiOptions>;\n  }\n\n  // Rest of the code is just to clean up undefined values\n  // and set some proper defaults\n  // Also, to clone the incoming object type arguments\n  if (isDefined(payload.userId)) {\n    payload.userId = tryStringify(payload.userId);\n  } else {\n    delete payload.userId;\n  }\n\n  if (isObjectLiteralAndNotNull(payload.traits)) {\n    payload.traits = clone(payload.traits);\n  } else {\n    delete payload.traits;\n  }\n\n  if (isDefined(payload.options)) {\n    payload.options = clone(payload.options);\n  } else {\n    delete payload.options;\n  }\n\n  return payload;\n};\n\n/*\n * Normalise the overloaded arguments of the alias call facade\n */\nconst aliasArgumentsToCallOptions = (\n  to?: Nullable<string> | ApiCallback,\n  from?: string | Nullable<ApiOptions> | ApiCallback,\n  options?: Nullable<ApiOptions> | ApiCallback,\n  callback?: ApiCallback,\n): AliasCallOptions => {\n  const payload: AliasCallOptions = {\n    to: to as string,\n    from: from as string,\n    options: options as Nullable<ApiOptions>,\n  };\n\n  if (isFunction(callback)) {\n    payload.callback = callback;\n  }\n\n  if (isFunction(options)) {\n    payload.to = to as string;\n    payload.from = from as string;\n    delete payload.options;\n    payload.callback = options as ApiCallback;\n  }\n\n  if (isFunction(from)) {\n    payload.to = to as string;\n    delete payload.from;\n    delete payload.options;\n    payload.callback = from as ApiCallback;\n  } else if (isObjectLiteralAndNotNull(from) || isNull(from)) {\n    payload.to = to as string;\n    delete payload.from;\n    payload.options = from as Nullable<ApiOptions>;\n  }\n\n  if (isFunction(to)) {\n    delete payload.to;\n    delete payload.from;\n    delete payload.options;\n    payload.callback = to as ApiCallback;\n  } else if (isObjectLiteralAndNotNull(to) || isNull(to)) {\n    delete payload.to;\n    delete payload.from;\n    payload.options = to as Nullable<ApiOptions>;\n  }\n\n  // Rest of the code is just to clean up undefined values\n  // and set some proper defaults\n  // Also, to clone the incoming object type arguments\n  if (isDefined(payload.to)) {\n    payload.to = tryStringify(payload.to);\n  } else {\n    delete payload.to;\n  }\n\n  if (isDefined(payload.from)) {\n    payload.from = tryStringify(payload.from);\n  } else {\n    delete payload.from;\n  }\n\n  if (isDefined(payload.options)) {\n    payload.options = clone(payload.options);\n  } else {\n    delete payload.options;\n  }\n\n  return payload;\n};\n\n/*\n * Normalise the overloaded arguments of the group call facade\n */\nconst groupArgumentsToCallOptions = (\n  groupId: string | number | Nullable<ApiObject> | ApiCallback,\n  traits?: Nullable<ApiOptions> | Nullable<ApiObject> | ApiCallback,\n  options?: Nullable<ApiOptions> | ApiCallback,\n  callback?: ApiCallback,\n): GroupCallOptions => {\n  const payload: GroupCallOptions = {\n    groupId: groupId as string,\n    traits: traits as Nullable<ApiObject>,\n    options: options as Nullable<ApiOptions>,\n  };\n\n  if (isFunction(callback)) {\n    payload.callback = callback;\n  }\n\n  if (isFunction(options)) {\n    payload.groupId = groupId as string;\n    payload.traits = traits as Nullable<ApiObject>;\n    delete payload.options;\n    payload.callback = options as ApiCallback;\n  }\n\n  if (isFunction(traits)) {\n    payload.groupId = groupId as string;\n    delete payload.traits;\n    delete payload.options;\n    payload.callback = traits as ApiCallback;\n  }\n\n  // TODO: why do we enable overload for group that only passes callback? is there any use case?\n  if (isFunction(groupId)) {\n    // Explicitly set null to prevent resetting the existing value\n    payload.groupId = null;\n    delete payload.traits;\n    delete payload.options;\n    payload.callback = groupId as ApiCallback;\n  } else if (isObjectLiteralAndNotNull(groupId) || isNull(groupId)) {\n    // Explicitly set null to prevent resetting the existing value\n    // in the Analytics class\n    payload.groupId = null;\n    payload.traits = groupId as Nullable<ApiObject>;\n    payload.options = !isFunction(traits) ? (traits as Nullable<ApiOptions>) : null;\n  }\n\n  // Rest of the code is just to clean up undefined values\n  // and set some proper defaults\n  // Also, to clone the incoming object type arguments\n  if (isDefined(payload.groupId)) {\n    payload.groupId = tryStringify(payload.groupId);\n  } else {\n    delete payload.groupId;\n  }\n\n  payload.traits = isObjectLiteralAndNotNull(payload.traits) ? clone(payload.traits) : {};\n\n  if (isDefined(payload.options)) {\n    payload.options = clone(payload.options);\n  } else {\n    delete payload.options;\n  }\n\n  return payload;\n};\n\nexport {\n  pageArgumentsToCallOptions,\n  trackArgumentsToCallOptions,\n  identifyArgumentsToCallOptions,\n  aliasArgumentsToCallOptions,\n  groupArgumentsToCallOptions,\n};\n", "const CAPABILITIES_MANAGER = 'CapabilitiesManager';\nconst CONFIG_MANAGER = 'ConfigManager';\nconst EVENT_MANAGER = 'EventManager';\nconst PLUGINS_MANAGER = 'PluginsManager';\nconst USER_SESSION_MANAGER = 'UserSessionManager';\nconst ERROR_HANDLER = 'ErrorHandler';\nconst PLUGIN_ENGINE = 'PluginEngine';\nconst STORE_MANAGER = 'StoreManager';\nconst READY_API = 'readyApi';\nconst LOAD_CONFIGURATION = 'LoadConfiguration';\nconst EVENT_REPOSITORY = 'EventRepository';\nconst EXTERNAL_SRC_LOADER = 'ExternalSrcLoader';\nconst HTTP_CLIENT = 'HttpClient';\nconst RS_APP = 'RudderStackApplication';\nconst ANALYTICS_CORE = 'AnalyticsCore';\n\nexport {\n  CAPABILITIES_MANAGER,\n  CONFIG_MANAGER,\n  EVENT_MANAGER,\n  PLUGINS_MANAGER,\n  USER_SESSION_MANAGER,\n  ERROR_HANDLER,\n  PLUGIN_ENGINE,\n  STORE_MANAGER,\n  READY_API,\n  LOAD_CONFIGURATION,\n  EVENT_REPOSITORY,\n  EXTERNAL_SRC_LOADER,\n  HTTP_CLIENT,\n  RS_APP,\n  ANALYTICS_CORE,\n};\n", "const APP_NAME = 'RudderLabs JavaScript SDK';\nconst APP_VERSION = '__PACKAGE_VERSION__';\nconst APP_NAMESPACE = 'com.rudderlabs.javascript';\nconst MODULE_TYPE = '__MODULE_TYPE__';\nconst IS_LEGACY_BUILD = __IS_LEGACY_BUILD__;\nconst ADBLOCK_PAGE_CATEGORY = 'RudderJS-Initiated';\nconst ADBLOCK_PAGE_NAME = 'ad-block page request';\nconst ADBLOCK_PAGE_PATH = '/ad-blocked';\nconst GLOBAL_PRELOAD_BUFFER = 'preloadedEventsBuffer';\n\nconst CONSENT_TRACK_EVENT_NAME = 'Consent Management Interaction';\n\nexport {\n  APP_NAME,\n  APP_VERSION,\n  APP_NAMESPACE,\n  MODULE_TYPE,\n  IS_LEGACY_BUILD,\n  ADBLOCK_PAGE_CATEGORY,\n  ADBLOCK_PAGE_NAME,\n  ADBLOCK_PAGE_PATH,\n  GLOBAL_PRELOAD_BUFFER,\n  CONSENT_TRACK_EVENT_NAME,\n};\n", "const QUERY_PARAM_TRAIT_PREFIX = 'ajs_trait_';\nconst QUERY_PARAM_PROPERTY_PREFIX = 'ajs_prop_';\nconst QUERY_PARAM_ANONYMOUS_ID_KEY = 'ajs_aid';\nconst QUERY_PARAM_USER_ID_KEY = 'ajs_uid';\nconst QUERY_PARAM_TRACK_EVENT_NAME_KEY = 'ajs_event';\n\nexport {\n  QUERY_PARAM_TRAIT_PREFIX,\n  QUERY_PARAM_PROPERTY_PREFIX,\n  QUERY_PARAM_ANONYMOUS_ID_KEY,\n  QUERY_PARAM_USER_ID_KEY,\n  QUERY_PARAM_TRACK_EVENT_NAME_KEY,\n};\n", "const DEFAULT_XHR_TIMEOUT_MS = 10 * 1000; // 10 seconds\nconst DEFAULT_COOKIE_MAX_AGE_MS = 31536000 * 1000; // 1 year\nconst DEFAULT_SESSION_TIMEOUT_MS = 30 * 60 * 1000; // 30 minutes\nconst MIN_SESSION_TIMEOUT_MS = 10 * 1000; // 10 seconds\nconst DEFAULT_DATA_PLANE_EVENTS_BUFFER_TIMEOUT_MS = 10 * 1000; // 10 seconds\nconst DEBOUNCED_TIMEOUT_MS = 250; // 250 milliseconds\n\nexport {\n  DEFAULT_XHR_TIMEOUT_MS,\n  DEFAULT_COOKIE_MAX_AGE_MS,\n  DEFAULT_SESSION_TIMEOUT_MS,\n  MIN_SESSION_TIMEOUT_MS,\n  DEFAULT_DATA_PLANE_EVENTS_BUFFER_TIMEOUT_MS,\n  DEBOUNCED_TIMEOUT_MS,\n};\n", "import type { DebouncedFunction } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport { DEBOUNCED_TIMEOUT_MS } from '../../constants/timeouts';\nimport type { ExposedGlobals, IRudderStackGlobals } from '../../app/IRudderStackGlobals';\n\n/**\n * Create globally accessible RudderStackGlobals object\n */\nconst createExposedGlobals = (analyticsInstanceId = 'app') => {\n  if (!(globalThis as typeof window).RudderStackGlobals) {\n    (globalThis as typeof window).RudderStackGlobals = {} as IRudderStackGlobals;\n  }\n\n  if (!(globalThis as typeof window).RudderStackGlobals[analyticsInstanceId]) {\n    (globalThis as typeof window).RudderStackGlobals[analyticsInstanceId] =\n      {} as IRudderStackGlobals;\n  }\n};\n\n/**\n * Add move values to globally accessible RudderStackGlobals object per analytics instance\n */\nconst setExposedGlobal = (keyName: string, value?: any, analyticsInstanceId = 'app') => {\n  createExposedGlobals(analyticsInstanceId);\n  ((globalThis as typeof window).RudderStackGlobals[analyticsInstanceId] as ExposedGlobals)[\n    keyName\n  ] = value;\n};\n\n/**\n * Get values from globally accessible RudderStackGlobals object by analytics instance\n */\nconst getExposedGlobal = (\n  keyName: string,\n  analyticsInstanceId = 'app',\n): Partial<ExposedGlobals> => {\n  createExposedGlobals(analyticsInstanceId);\n  return ((globalThis as typeof window).RudderStackGlobals[analyticsInstanceId] as ExposedGlobals)[\n    keyName\n  ];\n};\n\nfunction debounce(func: DebouncedFunction, thisArg: any, delay: number = DEBOUNCED_TIMEOUT_MS) {\n  let timeoutId: number;\n\n  return (...args: any[]) => {\n    (globalThis as typeof window).clearTimeout(timeoutId);\n\n    timeoutId = (globalThis as typeof window).setTimeout(() => {\n      func.apply(thisArg, args);\n    }, delay);\n  };\n}\n\nexport { createExposedGlobals, setExposedGlobal, getExposedGlobal, debounce };\n", "import { isFunction } from '@rudderstack/analytics-js-common/utilities/checks';\nimport {\n  aliasArgumentsToCallOptions,\n  groupArgumentsToCallOptions,\n  identifyArgumentsToCallOptions,\n  pageArgumentsToCallOptions,\n  trackArgumentsToCallOptions,\n} from '@rudderstack/analytics-js-common/utilities/eventMethodOverloads';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { clone } from 'ramda';\nimport type { PreloadedEventCall } from './types';\nimport {\n  QUERY_PARAM_ANONYMOUS_ID_KEY,\n  QUERY_PARAM_PROPERTY_PREFIX,\n  QUERY_PARAM_TRACK_EVENT_NAME_KEY,\n  QUERY_PARAM_TRAIT_PREFIX,\n  QUERY_PARAM_USER_ID_KEY,\n} from '../../constants/queryParams';\nimport type { IAnalytics } from '../core/IAnalytics';\nimport { getExposedGlobal, setExposedGlobal } from '../utilities/globals';\nimport { GLOBAL_PRELOAD_BUFFER } from '../../constants/app';\n\n/**\n * Parse query string params into object values for keys that start with a defined prefix\n */\nconst getEventDataFromQueryString = (\n  params: URLSearchParams,\n  dataTypeNamePrefix: string,\n): Record<string, Nullable<string>> => {\n  const data: Record<string, Nullable<string>> = {};\n\n  params.forEach((value, key) => {\n    if (key.startsWith(dataTypeNamePrefix)) {\n      // remove prefix from key name\n      const dataKey = key.substring(dataTypeNamePrefix.length);\n      // add new key value pair in generated object\n      data[dataKey] = params.get(key);\n    }\n  });\n\n  return data;\n};\n\n/**\n * Parse query string into preload buffer events & push into existing array before any other events\n */\nconst retrieveEventsFromQueryString = (argumentsArray: PreloadedEventCall[] = []) => {\n  // Mapping for trait and properties values based on key prefix\n  const eventArgumentToQueryParamMap = {\n    trait: QUERY_PARAM_TRAIT_PREFIX,\n    properties: QUERY_PARAM_PROPERTY_PREFIX,\n  };\n\n  const queryObject = new URLSearchParams(globalThis.location.search);\n\n  // Add track events with name and properties\n  if (queryObject.get(QUERY_PARAM_TRACK_EVENT_NAME_KEY)) {\n    argumentsArray.unshift([\n      'track',\n      queryObject.get(QUERY_PARAM_TRACK_EVENT_NAME_KEY),\n      getEventDataFromQueryString(queryObject, eventArgumentToQueryParamMap.properties),\n    ]);\n  }\n\n  // Set userId and user traits\n  if (queryObject.get(QUERY_PARAM_USER_ID_KEY)) {\n    argumentsArray.unshift([\n      'identify',\n      queryObject.get(QUERY_PARAM_USER_ID_KEY),\n      getEventDataFromQueryString(queryObject, eventArgumentToQueryParamMap.trait),\n    ]);\n  }\n\n  // Set anonymousID\n  if (queryObject.get(QUERY_PARAM_ANONYMOUS_ID_KEY)) {\n    argumentsArray.unshift(['setAnonymousId', queryObject.get(QUERY_PARAM_ANONYMOUS_ID_KEY)]);\n  }\n};\n\n/**\n * Retrieve an existing buffered load method call and remove from the existing array\n */\nconst getPreloadedLoadEvent = (preloadedEventsArray: PreloadedEventCall[]): PreloadedEventCall => {\n  const loadMethodName = 'load';\n  let loadEvent: PreloadedEventCall = [];\n\n  /**\n   * Iterate the buffered API calls until we find load call and process it separately\n   */\n  let i = 0;\n  while (i < preloadedEventsArray.length) {\n    if (\n      preloadedEventsArray[i] &&\n      (preloadedEventsArray[i] as PreloadedEventCall)[0] === loadMethodName\n    ) {\n      loadEvent = clone(preloadedEventsArray[i] as PreloadedEventCall);\n      preloadedEventsArray.splice(i, 1);\n      break;\n    }\n    i += 1;\n  }\n\n  return loadEvent;\n};\n\n/**\n * Promote consent events to the top of the preloaded events array\n * @param preloadedEventsArray Preloaded events array\n * @returns None\n */\nconst promotePreloadedConsentEventsToTop = (preloadedEventsArray: PreloadedEventCall[]): void => {\n  const consentMethodName = 'consent';\n  const consentEvents = preloadedEventsArray.filter(\n    bufferedEvent => bufferedEvent[0] === consentMethodName,\n  );\n\n  const nonConsentEvents = preloadedEventsArray.filter(\n    bufferedEvent => bufferedEvent[0] !== consentMethodName,\n  );\n\n  // Remove all elements and add consent events first followed by non consent events\n  // eslint-disable-next-line unicorn/no-useless-spread\n  preloadedEventsArray.splice(\n    0,\n    preloadedEventsArray.length,\n    ...consentEvents,\n    ...nonConsentEvents,\n  );\n};\n\n/**\n * Retrieve any existing events that were triggered before SDK load and enqueue in buffer\n */\nconst retrievePreloadBufferEvents = (instance: IAnalytics) => {\n  const preloadedEventsArray = (getExposedGlobal(GLOBAL_PRELOAD_BUFFER) ||\n    []) as PreloadedEventCall[];\n\n  // Get events that are pre-populated via query string params\n  retrieveEventsFromQueryString(preloadedEventsArray);\n\n  // Enqueue the non load events in the buffer of the global rudder analytics singleton\n  if (preloadedEventsArray.length > 0) {\n    instance.enqueuePreloadBufferEvents(preloadedEventsArray);\n    setExposedGlobal(GLOBAL_PRELOAD_BUFFER, []);\n  }\n};\n\nconst consumePreloadBufferedEvent = (event: any, analyticsInstance: IAnalytics) => {\n  const methodName = event.shift();\n  let callOptions;\n\n  if (isFunction((analyticsInstance as any)[methodName])) {\n    switch (methodName) {\n      case 'page':\n        callOptions = pageArgumentsToCallOptions(...(event as [any]));\n        break;\n      case 'track':\n        callOptions = trackArgumentsToCallOptions(...(event as [any]));\n        break;\n      case 'identify':\n        callOptions = identifyArgumentsToCallOptions(...(event as [any]));\n        break;\n      case 'alias':\n        callOptions = aliasArgumentsToCallOptions(...(event as [any]));\n        break;\n      case 'group':\n        callOptions = groupArgumentsToCallOptions(...(event as [any]));\n        break;\n      default:\n        (analyticsInstance as any)[methodName](...event);\n        break;\n    }\n\n    if (callOptions) {\n      (analyticsInstance as any)[methodName](callOptions);\n    }\n  }\n};\n\nexport {\n  getEventDataFromQueryString,\n  retrieveEventsFromQueryString,\n  getPreloadedLoadEvent,\n  retrievePreloadBufferEvents,\n  consumePreloadBufferedEvent,\n  promotePreloadedConsentEventsToTop,\n};\n", "const LOG_CONTEXT_SEPARATOR = ':: ';\n\nconst SCRIPT_ALREADY_EXISTS_ERROR = (id: string): string =>\n  `A script with the id \"${id}\" is already loaded. Skipping the loading of this script to prevent conflicts.`;\n\nconst SCRIPT_LOAD_ERROR = (id: string, url: string): string =>\n  `Failed to load the script with the id \"${id}\" from URL \"${url}\".`;\n\nconst SCRIPT_LOAD_TIMEOUT_ERROR = (id: string, url: string, timeout: number): string =>\n  `A timeout of ${timeout} ms occurred while trying to load the script with id \"${id}\" from URL \"${url}\".`;\n\nconst CIRCULAR_REFERENCE_WARNING = (context: string, key: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}A circular reference has been detected in the object and the property \"${key}\" has been dropped from the output.`;\n\nconst JSON_STRINGIFY_WARNING = `Failed to convert the value to a JSON string.`;\n\nexport {\n  LOG_CONTEXT_SEPARATOR,\n  SCRIPT_ALREADY_EXISTS_ERROR,\n  SCRIPT_LOAD_ERROR,\n  SCRIPT_LOAD_TIMEOUT_ERROR,\n  CIRCULAR_REFERENCE_WARNING,\n  JSON_STRINGIFY_WARNING\n}\n", "import type { ILogger } from '../types/Logger';\nimport type { Nullable } from '../types/Nullable';\nimport { isNull, isNullOrUndefined } from './checks';\nimport { CIRCULAR_REFERENCE_WARNING, JSON_STRINGIFY_WARNING } from '../constants/logMessages';\n\nconst JSON_STRINGIFY = 'JSONStringify';\n\nconst getCircularReplacer = (\n  excludeNull?: boolean,\n  excludeKeys?: string[],\n  logger?: ILogger,\n): ((key: string, value: any) => any) => {\n  const ancestors: any[] = [];\n\n  // Here we do not want to use arrow function to use \"this\" in function context\n  // eslint-disable-next-line func-names\n  return function (key, value): any {\n    if (excludeKeys?.includes(key)) {\n      return undefined;\n    }\n\n    if (excludeNull && isNullOrUndefined(value)) {\n      return undefined;\n    }\n\n    if (typeof value !== 'object' || isNull(value)) {\n      return value;\n    }\n\n    // `this` is the object that value is contained in, i.e., its direct parent.\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore-next-line\n    while (ancestors.length > 0 && ancestors[ancestors.length - 1] !== this) {\n      ancestors.pop();\n    }\n\n    if (ancestors.includes(value)) {\n      logger?.warn(CIRCULAR_REFERENCE_WARNING(JSON_STRINGIFY, key));\n      return '[Circular Reference]';\n    }\n\n    ancestors.push(value);\n    return value;\n  };\n};\n\n/**\n * Utility method for JSON stringify object excluding null values & circular references\n *\n * @param {*} value input\n * @param {boolean} excludeNull if it should exclude nul or not\n * @param {function} logger optional logger methods for warning\n * @returns string\n */\nconst stringifyWithoutCircular = <T = Record<string, any> | any[] | number | string>(\n  value?: Nullable<T>,\n  excludeNull?: boolean,\n  excludeKeys?: string[],\n  logger?: ILogger,\n): Nullable<string> => {\n  try {\n    return JSON.stringify(value, getCircularReplacer(excludeNull, excludeKeys, logger));\n  } catch (err) {\n    logger?.warn(JSON_STRINGIFY_WARNING, err);\n    return null;\n  }\n};\n\nexport { stringifyWithoutCircular };\n", "import { isTypeOfError } from './checks';\nimport { stringifyWithoutCircular } from './json';\n\n/**\n * Get mutated error with issue prepended to error message\n * @param err Original error\n * @param issue Issue to prepend to error message\n * @returns Instance of Error with message prepended with issue\n */\nconst getMutatedError = (err: any, issue: string): Error => {\n  let finalError = err;\n  if (!isTypeOfError(err)) {\n    finalError = new Error(`${issue}: ${stringifyWithoutCircular(err as Record<string, any>)}`);\n  } else {\n    (finalError as Error).message = `${issue}: ${err.message}`;\n  }\n  return finalError;\n};\n\nexport { getMutatedError };\n", "import { getMutatedError } from '../../utilities/errors';\nimport { EXTERNAL_SOURCE_LOAD_ORIGIN } from '../../constants/htmlAttributes';\nimport {\n  SCRIPT_ALREADY_EXISTS_ERROR,\n  SCRIPT_LOAD_ERROR,\n  SCRIPT_LOAD_TIMEOUT_ERROR,\n} from '../../constants/logMessages';\n\n/**\n * Create the DOM element to load a script marked as RS SDK originated\n *\n * @param {*} url The URL of the script to be loaded\n * @param {*} id ID for the script tag\n * @param {*} async Whether to load the script in async mode. Defaults to `true` [optional]\n * @param {*} onload callback to invoke onload [optional]\n * @param {*} onerror callback to invoke onerror [optional]\n * @param {*} extraAttributes key/value pair with html attributes to add in html tag [optional]\n *\n * @returns HTMLScriptElement\n */\nconst createScriptElement = (\n  url: string,\n  id: string,\n  async = true,\n  onload: ((this: GlobalEventHandlers, ev: Event) => any) | null = null,\n  onerror: OnErrorEventHandler = null,\n  extraAttributes: Record<string, string> = {},\n) => {\n  const scriptElement = document.createElement('script');\n  scriptElement.type = 'text/javascript';\n  scriptElement.onload = onload;\n  scriptElement.onerror = onerror;\n  scriptElement.src = url;\n  scriptElement.id = id;\n  scriptElement.async = async;\n\n  Object.keys(extraAttributes).forEach(attributeName => {\n    scriptElement.setAttribute(attributeName, extraAttributes[attributeName] as string);\n  });\n\n  scriptElement.setAttribute('data-loader', EXTERNAL_SOURCE_LOAD_ORIGIN);\n\n  return scriptElement;\n};\n\n/**\n * Add script DOM element to DOM\n *\n * @param {*} newScriptElement the script element to add\n *\n * @returns\n */\nconst insertScript = (newScriptElement: HTMLScriptElement) => {\n  // First try to add it to the head\n  const headElements = document.getElementsByTagName('head');\n  if (headElements.length > 0) {\n    headElements[0]?.insertBefore(newScriptElement, headElements[0]?.firstChild);\n    return;\n  }\n\n  // Else wise add it before the first script tag\n  const scriptElements = document.getElementsByTagName('script');\n  if (scriptElements.length > 0 && scriptElements[0]?.parentNode) {\n    scriptElements[0]?.parentNode.insertBefore(newScriptElement, scriptElements[0]);\n    return;\n  }\n\n  // Create a new head element and add the script as fallback\n  const headElement = document.createElement('head');\n  headElement.appendChild(newScriptElement);\n\n  const htmlElement = document.getElementsByTagName('html')[0];\n  htmlElement?.insertBefore(headElement, htmlElement.firstChild);\n};\n\n/**\n * Loads external js file as a script html tag\n *\n * @param {*} url The URL of the script to be loaded\n * @param {*} id ID for the script tag\n * @param {*} timeout loading timeout\n * @param {*} async Whether to load the script in async mode. Defaults to `true` [optional]\n * @param {*} extraAttributes key/value pair with html attributes to add in html tag [optional]\n *\n * @returns\n */\nconst jsFileLoader = (\n  url: string,\n  id: string,\n  timeout: number,\n  async = true,\n  extraAttributes?: Record<string, string>,\n): Promise<string | undefined> =>\n  new Promise((resolve, reject) => {\n    const scriptExists = document.getElementById(id);\n    if (scriptExists) {\n      reject(new Error(SCRIPT_ALREADY_EXISTS_ERROR(id)));\n    }\n\n    try {\n      let timeoutID: number;\n\n      const onload = () => {\n        (globalThis as typeof window).clearTimeout(timeoutID);\n        resolve(id);\n      };\n\n      const onerror = () => {\n        (globalThis as typeof window).clearTimeout(timeoutID);\n        reject(new Error(SCRIPT_LOAD_ERROR(id, url)));\n      };\n\n      // Create the DOM element to load the script and add it to the DOM\n      insertScript(createScriptElement(url, id, async, onload, onerror, extraAttributes));\n\n      // Reject on timeout\n      timeoutID = (globalThis as typeof window).setTimeout(() => {\n        reject(new Error(SCRIPT_LOAD_TIMEOUT_ERROR(id, url, timeout)));\n      }, timeout);\n    } catch (err) {\n      reject(getMutatedError(err, SCRIPT_LOAD_ERROR(id, url)));\n    }\n  });\n\nexport { jsFileLoader, insertScript, createScriptElement };\n", "const EXTERNAL_SOURCE_LOAD_ORIGIN = 'RS_JS_SDK';\n\nexport { EXTERNAL_SOURCE_LOAD_ORIGIN };\n", "import { EXTERNAL_SRC_LOADER } from '../../constants/loggerContexts';\nimport { DEFAULT_EXT_SRC_LOAD_TIMEOUT_MS } from '../../constants/timeouts';\nimport { isFunction } from '../../utilities/checks';\nimport type { IErrorHandler } from '../../types/ErrorHandler';\nimport type { ILogger } from '../../types/Logger';\nimport type { IExternalSourceLoadConfig, IExternalSrcLoader } from './types';\nimport { jsFileLoader } from './jsFileLoader';\n\n/**\n * Service to load external resources/files\n */\nclass ExternalSrcLoader implements IExternalSrcLoader {\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n  hasErrorHandler = false;\n  timeout: number;\n\n  constructor(\n    errorHandler?: IErrorHandler,\n    logger?: ILogger,\n    timeout = DEFAULT_EXT_SRC_LOAD_TIMEOUT_MS,\n  ) {\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.timeout = timeout;\n    this.hasErrorHandler = Boolean(this.errorHandler);\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Load external resource of type javascript\n   */\n  loadJSFile(config: IExternalSourceLoadConfig) {\n    const { url, id, timeout, async, callback, extraAttributes } = config;\n    const isFireAndForget = !isFunction(callback);\n\n    jsFileLoader(url, id, timeout || this.timeout, async, extraAttributes)\n      .then((id?: string) => {\n        if (!isFireAndForget) {\n          callback(id);\n        }\n      })\n      .catch(err => {\n        this.onError(err);\n        if (!isFireAndForget) {\n          callback();\n        }\n      });\n  }\n\n  /**\n   * Handle errors\n   */\n  onError(error: unknown) {\n    if (this.hasErrorHandler) {\n      this.errorHandler?.onError(error, EXTERNAL_SRC_LOADER);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { ExternalSrcLoader };\n", "const DEFAULT_EXT_SRC_LOAD_TIMEOUT_MS = 10 * 1000; // 10 seconds\n\nexport { DEFAULT_EXT_SRC_LOAD_TIMEOUT_MS };\n", "var i=Symbol.for(\"preact-signals\");function t(){if(!(s>1)){var i,t=!1;while(void 0!==h){var r=h;h=void 0;f++;while(void 0!==r){var o=r.o;r.o=void 0;r.f&=-3;if(!(8&r.f)&&c(r))try{r.c()}catch(r){if(!t){i=r;t=!0}}r=o}}f=0;s--;if(t)throw i}else s--}function r(i){if(s>0)return i();s++;try{return i()}finally{t()}}var o=void 0;function n(i){var t=o;o=void 0;try{return i()}finally{o=t}}var h=void 0,s=0,f=0,v=0;function e(i){if(void 0!==o){var t=i.n;if(void 0===t||t.t!==o){t={i:0,S:i,p:o.s,n:void 0,t:o,e:void 0,x:void 0,r:t};if(void 0!==o.s)o.s.n=t;o.s=t;i.n=t;if(32&o.f)i.S(t);return t}else if(-1===t.i){t.i=0;if(void 0!==t.n){t.n.p=t.p;if(void 0!==t.p)t.p.n=t.n;t.p=o.s;t.n=void 0;o.s.n=t;o.s=t}return t}}}function u(i){this.v=i;this.i=0;this.n=void 0;this.t=void 0}u.prototype.brand=i;u.prototype.h=function(){return!0};u.prototype.S=function(i){if(this.t!==i&&void 0===i.e){i.x=this.t;if(void 0!==this.t)this.t.e=i;this.t=i}};u.prototype.U=function(i){if(void 0!==this.t){var t=i.e,r=i.x;if(void 0!==t){t.x=r;i.e=void 0}if(void 0!==r){r.e=t;i.x=void 0}if(i===this.t)this.t=r}};u.prototype.subscribe=function(i){var t=this;return E(function(){var r=t.value,n=o;o=void 0;try{i(r)}finally{o=n}})};u.prototype.valueOf=function(){return this.value};u.prototype.toString=function(){return this.value+\"\"};u.prototype.toJSON=function(){return this.value};u.prototype.peek=function(){var i=o;o=void 0;try{return this.value}finally{o=i}};Object.defineProperty(u.prototype,\"value\",{get:function(){var i=e(this);if(void 0!==i)i.i=this.i;return this.v},set:function(i){if(i!==this.v){if(f>100)throw new Error(\"Cycle detected\");this.v=i;this.i++;v++;s++;try{for(var r=this.t;void 0!==r;r=r.x)r.t.N()}finally{t()}}}});function d(i){return new u(i)}function c(i){for(var t=i.s;void 0!==t;t=t.n)if(t.S.i!==t.i||!t.S.h()||t.S.i!==t.i)return!0;return!1}function a(i){for(var t=i.s;void 0!==t;t=t.n){var r=t.S.n;if(void 0!==r)t.r=r;t.S.n=t;t.i=-1;if(void 0===t.n){i.s=t;break}}}function l(i){var t=i.s,r=void 0;while(void 0!==t){var o=t.p;if(-1===t.i){t.S.U(t);if(void 0!==o)o.n=t.n;if(void 0!==t.n)t.n.p=o}else r=t;t.S.n=t.r;if(void 0!==t.r)t.r=void 0;t=o}i.s=r}function y(i){u.call(this,void 0);this.x=i;this.s=void 0;this.g=v-1;this.f=4}(y.prototype=new u).h=function(){this.f&=-3;if(1&this.f)return!1;if(32==(36&this.f))return!0;this.f&=-5;if(this.g===v)return!0;this.g=v;this.f|=1;if(this.i>0&&!c(this)){this.f&=-2;return!0}var i=o;try{a(this);o=this;var t=this.x();if(16&this.f||this.v!==t||0===this.i){this.v=t;this.f&=-17;this.i++}}catch(i){this.v=i;this.f|=16;this.i++}o=i;l(this);this.f&=-2;return!0};y.prototype.S=function(i){if(void 0===this.t){this.f|=36;for(var t=this.s;void 0!==t;t=t.n)t.S.S(t)}u.prototype.S.call(this,i)};y.prototype.U=function(i){if(void 0!==this.t){u.prototype.U.call(this,i);if(void 0===this.t){this.f&=-33;for(var t=this.s;void 0!==t;t=t.n)t.S.U(t)}}};y.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var i=this.t;void 0!==i;i=i.x)i.t.N()}};Object.defineProperty(y.prototype,\"value\",{get:function(){if(1&this.f)throw new Error(\"Cycle detected\");var i=e(this);this.h();if(void 0!==i)i.i=this.i;if(16&this.f)throw this.v;return this.v}});function w(i){return new y(i)}function _(i){var r=i.u;i.u=void 0;if(\"function\"==typeof r){s++;var n=o;o=void 0;try{r()}catch(t){i.f&=-2;i.f|=8;g(i);throw t}finally{o=n;t()}}}function g(i){for(var t=i.s;void 0!==t;t=t.n)t.S.U(t);i.x=void 0;i.s=void 0;_(i)}function p(i){if(o!==this)throw new Error(\"Out-of-order effect\");l(this);o=i;this.f&=-2;if(8&this.f)g(this);t()}function b(i){this.x=i;this.u=void 0;this.s=void 0;this.o=void 0;this.f=32}b.prototype.c=function(){var i=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var t=this.x();if(\"function\"==typeof t)this.u=t}finally{i()}};b.prototype.S=function(){if(1&this.f)throw new Error(\"Cycle detected\");this.f|=1;this.f&=-9;_(this);a(this);s++;var i=o;o=this;return p.bind(this,i)};b.prototype.N=function(){if(!(2&this.f)){this.f|=2;this.o=h;h=this}};b.prototype.d=function(){this.f|=8;if(!(1&this.f))g(this)};function E(i){var t=new b(i);try{t.c()}catch(i){t.d();throw i}return t.d.bind(t)}export{u as Signal,r as batch,w as computed,E as effect,d as signal,n as untracked};//# sourceMappingURL=signals-core.module.js.map\n", "import type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\n\n/**\n * A buffer queue to serve as a store for any type of data\n */\nclass BufferQueue<T = any> {\n  items: T[];\n\n  constructor() {\n    this.items = [];\n  }\n\n  enqueue(item: T) {\n    this.items.push(item);\n  }\n\n  dequeue(): Nullable<T> | undefined {\n    if (this.items.length === 0) {\n      return null;\n    }\n    return this.items.shift();\n  }\n\n  isEmpty(): boolean {\n    return this.items.length === 0;\n  }\n\n  size(): number {\n    return this.items.length;\n  }\n\n  clear() {\n    this.items = [];\n  }\n}\n\nexport { BufferQueue };\n", "import { isString, isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { ILogger, LogLevel } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { LoggerProvider } from './types';\n\nconst LOG_LEVEL_MAP: Record<LogLevel, number> = {\n  LOG: 0,\n  INFO: 1,\n  DEBUG: 2,\n  WARN: 3,\n  ERROR: 4,\n  NONE: 5,\n};\n\nconst DEFAULT_LOG_LEVEL = 'ERROR';\nconst LOG_MSG_PREFIX = 'RS SDK';\nconst LOG_MSG_PREFIX_STYLE = 'font-weight: bold; background: black; color: white;';\nconst LOG_MSG_STYLE = 'font-weight: normal;';\n\n/**\n * Service to log messages/data to output provider, default is console\n */\nclass Logger implements ILogger {\n  minLogLevel: number;\n  scope?: string;\n  logProvider: LoggerProvider;\n\n  constructor(minLogLevel: LogLevel = DEFAULT_LOG_LEVEL, scope = '', logProvider = console) {\n    this.minLogLevel = LOG_LEVEL_MAP[minLogLevel];\n    this.scope = scope;\n    this.logProvider = logProvider;\n  }\n\n  log(...data: any[]) {\n    this.outputLog('LOG', data);\n  }\n\n  info(...data: any[]) {\n    this.outputLog('INFO', data);\n  }\n\n  debug(...data: any[]) {\n    this.outputLog('DEBUG', data);\n  }\n\n  warn(...data: any[]) {\n    this.outputLog('WARN', data);\n  }\n\n  error(...data: any[]) {\n    this.outputLog('ERROR', data);\n  }\n\n  outputLog(logMethod: LogLevel, data: any[]) {\n    if (this.minLogLevel <= LOG_LEVEL_MAP[logMethod]) {\n      this.logProvider[\n        logMethod.toLowerCase() as Exclude<Lowercase<LogLevel>, Lowercase<'NONE'>>\n      ]?.(...this.formatLogData(data));\n    }\n  }\n\n  setScope(scopeVal: string) {\n    this.scope = scopeVal || this.scope;\n  }\n\n  // TODO: should we allow to change the level via global variable on run time\n  //  to assist on the fly debugging?\n  setMinLogLevel(logLevel: LogLevel) {\n    this.minLogLevel = LOG_LEVEL_MAP[logLevel];\n    if (isUndefined(this.minLogLevel)) {\n      this.minLogLevel = LOG_LEVEL_MAP[DEFAULT_LOG_LEVEL];\n    }\n  }\n\n  /**\n   * Formats the console message using `scope` and styles\n   */\n  formatLogData(data: any[]) {\n    if (Array.isArray(data) && data.length > 0) {\n      // prefix SDK identifier\n      let msg = `%c ${LOG_MSG_PREFIX}`;\n\n      // format the log message using `scope`\n      if (this.scope) {\n        msg = `${msg} - ${this.scope}`;\n      }\n\n      // trim whitespaces for original message\n      const originalMsg = isString(data[0]) ? data[0].trim() : '';\n\n      // prepare the final message\n      msg = `${msg} %c ${originalMsg}`;\n\n      const styledLogArgs = [\n        msg,\n        LOG_MSG_PREFIX_STYLE, // add style for the prefix\n        LOG_MSG_STYLE, // reset the style for the actual message\n      ];\n\n      // add first it if it was not a string msg\n      if (!isString(data[0])) {\n        styledLogArgs.push(data[0]);\n      }\n\n      // append rest of the original arguments\n      styledLogArgs.push(...data.slice(1));\n      return styledLogArgs;\n    }\n\n    return data;\n  }\n}\n\nconst defaultLogger = new Logger();\n\nexport {\n  Logger,\n  DEFAULT_LOG_LEVEL,\n  LOG_LEVEL_MAP,\n  LOG_MSG_PREFIX,\n  LOG_MSG_PREFIX_STYLE,\n  LOG_MSG_STYLE,\n  defaultLogger,\n};\n", "import type { UserSessionKey } from './UserSessionStorage';\n\nexport type StorageEncryptionVersion = 'legacy' | 'v3'; // default is v3\n\nexport type StorageType =\n  | 'cookieStorage'\n  | 'localStorage'\n  | 'memoryStorage'\n  | 'sessionStorage'\n  | 'none';\n\nexport const SUPPORTED_STORAGE_TYPES = [\n  'localStorage',\n  'memoryStorage',\n  'cookieStorage',\n  'sessionStorage',\n  'none',\n];\n\nexport const DEFAULT_STORAGE_TYPE = 'cookieStorage';\n\nexport type StorageEncryption = {\n  version: StorageEncryptionVersion;\n};\n\nexport type LoadOptionStorageEntry = {\n  type: StorageType;\n};\n\nexport type StorageOpts = {\n  encryption?: StorageEncryption;\n  migrate?: boolean;\n  type?: StorageType;\n  cookie?: CookieOptions;\n  entries?: {\n    [key in UserSessionKey]?: LoadOptionStorageEntry;\n  };\n};\n\nexport type CookieOptions = {\n  maxage?: number;\n  expires?: Date;\n  path?: string;\n  domain?: string;\n  samesite?: string;\n  secure?: boolean;\n};\n\nexport type CookieSameSite = 'Strict' | 'Lax' | 'None';\n", "import type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport {\n  type StorageType,\n  SUPPORTED_STORAGE_TYPES,\n} from '@rudderstack/analytics-js-common/types/Storage';\nimport { LOG_CONTEXT_SEPARATOR } from '@rudderstack/analytics-js-common/constants/logMessages';\nimport type {\n  DeliveryType,\n  StorageStrategy,\n} from '@rudderstack/analytics-js-common/types/LoadOptions';\n\n// CONSTANT\nconst SOURCE_CONFIG_OPTION_ERROR = `\"getSourceConfig\" must be a function. Please make sure that it is defined and returns a valid source configuration object.`;\nconst INTG_CDN_BASE_URL_ERROR = `Failed to load the SDK as the CDN base URL for integrations is not valid.`;\nconst PLUGINS_CDN_BASE_URL_ERROR = `Failed to load the SDK as the CDN base URL for plugins is not valid.`;\nconst DATA_PLANE_URL_ERROR = `Failed to load the SDK as the data plane URL could not be determined. Please check that the data plane URL is set correctly and try again.`;\nconst SOURCE_CONFIG_RESOLUTION_ERROR = `Unable to process/parse source configuration response.`;\nconst SOURCE_DISABLED_ERROR = `The source is disabled. Please enable the source in the dashboard to send events.`;\nconst XHR_PAYLOAD_PREP_ERROR = `Failed to prepare data for the request.`;\nconst EVENT_OBJECT_GENERATION_ERROR = `Failed to generate the event object.`;\nconst PLUGIN_EXT_POINT_MISSING_ERROR = `Failed to invoke plugin because the extension point name is missing.`;\nconst PLUGIN_EXT_POINT_INVALID_ERROR = `Failed to invoke plugin because the extension point name is invalid.`;\n\n// ERROR\nconst UNSUPPORTED_CONSENT_MANAGER_ERROR = (\n  context: string,\n  selectedConsentManager: string,\n  consentManagersToPluginNameMap: Record<string, PluginName>,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The consent manager \"${selectedConsentManager}\" is not supported. Please choose one of the following supported consent managers: \"${Object.keys(\n    consentManagersToPluginNameMap,\n  )}\".`;\n\nconst REPORTING_PLUGIN_INIT_FAILURE_ERROR = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Failed to initialize the error reporting plugin.`;\n\nconst NOTIFY_FAILURE_ERROR = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Failed to notify the error.`;\n\nconst PLUGIN_NAME_MISSING_ERROR = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Plugin name is missing.`;\n\nconst PLUGIN_ALREADY_EXISTS_ERROR = (context: string, pluginName: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Plugin \"${pluginName}\" already exists.`;\n\nconst PLUGIN_NOT_FOUND_ERROR = (context: string, pluginName: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Plugin \"${pluginName}\" not found.`;\n\nconst PLUGIN_ENGINE_BUG_ERROR = (context: string, pluginName: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Plugin \"${pluginName}\" not found in plugins but found in byName. This indicates a bug in the plugin engine. Please report this issue to the development team.`;\n\nconst PLUGIN_DEPS_ERROR = (context: string, pluginName: string, notExistDeps: string[]): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Plugin \"${pluginName}\" could not be loaded because some of its dependencies \"${notExistDeps}\" do not exist.`;\n\nconst PLUGIN_INVOCATION_ERROR = (\n  context: string,\n  extPoint: string | undefined,\n  pluginName: string,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}Failed to invoke the \"${extPoint}\" extension point of plugin \"${pluginName}\".`;\n\nconst STORAGE_UNAVAILABILITY_ERROR_PREFIX = (context: string, storageType: StorageType): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The \"${storageType}\" storage type is `;\n\nconst SOURCE_CONFIG_FETCH_ERROR = (reason: Error | undefined): string =>\n  `Failed to fetch the source config. Reason: ${reason}`;\n\nconst WRITE_KEY_VALIDATION_ERROR = (writeKey?: string): string =>\n  `The write key \"${writeKey}\" is invalid. It must be a non-empty string. Please check that the write key is correct and try again.`;\n\nconst DATA_PLANE_URL_VALIDATION_ERROR = (dataPlaneUrl: string | undefined): string =>\n  `The data plane URL \"${dataPlaneUrl}\" is invalid. It must be a valid URL string. Please check that the data plane URL is correct and try again.`;\n\nconst READY_API_CALLBACK_ERROR = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The callback is not a function.`;\n\nconst XHR_DELIVERY_ERROR = (\n  prefix: string,\n  status: number,\n  statusText: string,\n  url: string,\n): string => `${prefix} with status: ${status}, ${statusText} for URL: ${url}.`;\n\nconst XHR_REQUEST_ERROR = (prefix: string, e: ProgressEvent | undefined, url: string): string =>\n  `${prefix} due to timeout or no connection (${e ? e.type : ''}) for URL: ${url}.`;\n\nconst XHR_SEND_ERROR = (prefix: string, url: string): string => `${prefix} for URL: ${url}`;\n\nconst STORE_DATA_SAVE_ERROR = (key: string): string =>\n  `Failed to save the value for \"${key}\" to storage`;\n\nconst STORE_DATA_FETCH_ERROR = (key: string): string =>\n  `Failed to retrieve or parse data for \"${key}\" from storage`;\n\nconst DATA_SERVER_URL_INVALID_ERROR = (url: string) =>\n  `The server side cookies functionality is disabled as the provided data server URL, \"${url}\" is invalid.`;\n\nconst DATA_SERVER_REQUEST_FAIL_ERROR = (status?: number) =>\n  `The server responded with status ${status} while setting the cookies. As a fallback, the cookies will be set client side.`;\nconst FAILED_SETTING_COOKIE_FROM_SERVER_ERROR = (key: string) =>\n  `The server failed to set the ${key} cookie. As a fallback, the cookies will be set client side.`;\nconst FAILED_SETTING_COOKIE_FROM_SERVER_GLOBAL_ERROR = `Failed to set/remove cookies via server. As a fallback, the cookies will be managed client side.`;\n\n// WARNING\nconst STORAGE_TYPE_VALIDATION_WARNING = (\n  context: string,\n  storageType: any,\n  defaultStorageType: StorageType,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The storage type \"${storageType}\" is not supported. Please choose one of the following supported types: \"${SUPPORTED_STORAGE_TYPES}\". The default type \"${defaultStorageType}\" will be used instead.`;\n\nconst UNSUPPORTED_ERROR_REPORTING_PROVIDER_WARNING = (\n  context: string,\n  selectedErrorReportingProvider: string | undefined,\n  errorReportingProvidersToPluginNameMap: Record<string, PluginName>,\n  defaultProvider: string,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The error reporting provider \"${selectedErrorReportingProvider}\" is not supported. Please choose one of the following supported providers: \"${Object.keys(\n    errorReportingProvidersToPluginNameMap,\n  )}\". The default provider \"${defaultProvider}\" will be used instead.`;\n\nconst UNSUPPORTED_STORAGE_ENCRYPTION_VERSION_WARNING = (\n  context: string,\n  selectedStorageEncryptionVersion: string | undefined,\n  storageEncryptionVersionsToPluginNameMap: Record<string, PluginName>,\n  defaultVersion: string,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The storage encryption version \"${selectedStorageEncryptionVersion}\" is not supported. Please choose one of the following supported versions: \"${Object.keys(\n    storageEncryptionVersionsToPluginNameMap,\n  )}\". The default version \"${defaultVersion}\" will be used instead.`;\n\nconst STORAGE_DATA_MIGRATION_OVERRIDE_WARNING = (\n  context: string,\n  storageEncryptionVersion: string | undefined,\n  defaultVersion: string,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The storage data migration has been disabled because the configured storage encryption version (${storageEncryptionVersion}) is not the latest (${defaultVersion}). To enable storage data migration, please update the storage encryption version to the latest version.`;\n\nconst UNSUPPORTED_RESIDENCY_SERVER_REGION_WARNING = (\n  context: string,\n  selectedResidencyServerRegion: string | undefined,\n  defaultRegion: string,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The residency server region \"${selectedResidencyServerRegion}\" is not supported. Please choose one of the following supported regions: \"US, EU\". The default region \"${defaultRegion}\" will be used instead.`;\n\nconst RESERVED_KEYWORD_WARNING = (\n  context: string,\n  property: string,\n  parentKeyPath: string,\n  reservedElements: string[],\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The \"${property}\" property defined under \"${parentKeyPath}\" is a reserved keyword. Please choose a different property name to avoid conflicts with reserved keywords (${reservedElements}).`;\n\nconst INVALID_CONTEXT_OBJECT_WARNING = (logContext: string): string =>\n  `${logContext}${LOG_CONTEXT_SEPARATOR}Please make sure that the \"context\" property in the event API's \"options\" argument is a valid object literal with key-value pairs.`;\n\nconst UNSUPPORTED_BEACON_API_WARNING = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The Beacon API is not supported by your browser. The events will be sent using XHR instead.`;\n\nconst TIMEOUT_NOT_NUMBER_WARNING = (\n  context: string,\n  timeout: number | undefined,\n  defaultValue: number,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The session timeout value \"${timeout}\" is not a number. The default timeout of ${defaultValue} ms will be used instead.`;\n\nconst TIMEOUT_ZERO_WARNING = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The session timeout value is 0, which disables the automatic session tracking feature. If you want to enable session tracking, please provide a positive integer value for the timeout.`;\n\nconst TIMEOUT_NOT_RECOMMENDED_WARNING = (\n  context: string,\n  timeout: number,\n  minTimeout: number,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The session timeout value ${timeout} ms is less than the recommended minimum of ${minTimeout} ms. Please consider increasing the timeout value to ensure optimal performance and reliability.`;\n\nconst INVALID_SESSION_ID_WARNING = (\n  context: string,\n  sessionId: number | undefined,\n  minSessionIdLength: number,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The provided session ID (${sessionId}) is either invalid, not a positive integer, or not at least \"${minSessionIdLength}\" digits long. A new session ID will be auto-generated instead.`;\n\nconst STORAGE_QUOTA_EXCEEDED_WARNING = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The storage is either full or unavailable, so the data will not be persisted. Switching to in-memory storage.`;\n\nconst STORAGE_UNAVAILABLE_WARNING = (\n  context: string,\n  entry: string,\n  selectedStorageType: string,\n  finalStorageType: string,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The storage type \"${selectedStorageType}\" is not available for entry \"${entry}\". The SDK will initialize the entry with \"${finalStorageType}\" storage type instead.`;\n\nconst WRITE_KEY_NOT_A_STRING_ERROR = (context: string, writeKey: string | undefined): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The write key \"${writeKey}\" is not a string. Please check that the write key is correct and try again.`;\n\nconst EMPTY_GROUP_CALL_ERROR = (context: string): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The group() method must be called with at least one argument.`;\n\nconst READY_CALLBACK_INVOKE_ERROR = `Failed to invoke the ready callback`;\n\nconst API_CALLBACK_INVOKE_ERROR = `API Callback Invocation Failed`;\nconst NATIVE_DEST_PLUGIN_INITIALIZE_ERROR = `NativeDestinationQueuePlugin initialization failed`;\nconst DATAPLANE_PLUGIN_INITIALIZE_ERROR = `XhrQueuePlugin initialization failed`;\nconst DMT_PLUGIN_INITIALIZE_ERROR = `DeviceModeTransformationPlugin initialization failed`;\n\nconst NATIVE_DEST_PLUGIN_ENQUEUE_ERROR = `NativeDestinationQueuePlugin event enqueue failed`;\nconst DATAPLANE_PLUGIN_ENQUEUE_ERROR = `XhrQueuePlugin event enqueue failed`;\n\nconst INVALID_CONFIG_URL_WARNING = (context: string, configUrl: string | undefined): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The provided source config URL \"${configUrl}\" is invalid. Using the default source config URL instead.`;\n\nconst POLYFILL_SCRIPT_LOAD_ERROR = (scriptId: string, url: string): string =>\n  `Failed to load the polyfill script with ID \"${scriptId}\" from URL ${url}.`;\n\nconst COOKIE_DATA_ENCODING_ERROR = `Failed to encode the cookie data.`;\n\nconst UNSUPPORTED_PRE_CONSENT_STORAGE_STRATEGY = (\n  context: string,\n  selectedStrategy: StorageStrategy | undefined,\n  defaultStrategy: StorageStrategy,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The pre-consent storage strategy \"${selectedStrategy}\" is not supported. Please choose one of the following supported strategies: \"none, session, anonymousId\". The default strategy \"${defaultStrategy}\" will be used instead.`;\n\nconst UNSUPPORTED_PRE_CONSENT_EVENTS_DELIVERY_TYPE = (\n  context: string,\n  selectedDeliveryType: DeliveryType | undefined,\n  defaultDeliveryType: DeliveryType,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The pre-consent events delivery type \"${selectedDeliveryType}\" is not supported. Please choose one of the following supported types: \"immediate, buffer\". The default type \"${defaultDeliveryType}\" will be used instead.`;\n\nconst generateMisconfiguredPluginsWarning = (\n  context: string,\n  configurationStatus: string,\n  missingPlugins: PluginName[],\n  shouldAddMissingPlugins: boolean,\n): string => {\n  const isSinglePlugin = missingPlugins.length === 1;\n  const pluginsString = isSinglePlugin\n    ? ` '${missingPlugins[0]}' plugin was`\n    : ` ['${missingPlugins.join(\"', '\")}'] plugins were`;\n  const baseWarning = `${context}${LOG_CONTEXT_SEPARATOR}${configurationStatus}, but${pluginsString} not configured to load.`;\n  if (shouldAddMissingPlugins) {\n    return `${baseWarning} So, ${isSinglePlugin ? 'the plugin' : 'those plugins'} will be loaded automatically.`;\n  }\n  return `${baseWarning} Ignore if this was intentional. Otherwise, consider adding ${isSinglePlugin ? 'it' : 'them'} to the 'plugins' load API option.`;\n};\n\nconst INVALID_POLYFILL_URL_WARNING = (\n  context: string,\n  customPolyfillUrl: string | undefined,\n): string =>\n  `${context}${LOG_CONTEXT_SEPARATOR}The provided polyfill URL \"${customPolyfillUrl}\" is invalid. The default polyfill URL will be used instead.`;\n\n// DEBUG\n\nexport {\n  UNSUPPORTED_CONSENT_MANAGER_ERROR,\n  UNSUPPORTED_ERROR_REPORTING_PROVIDER_WARNING,\n  UNSUPPORTED_STORAGE_ENCRYPTION_VERSION_WARNING,\n  STORAGE_DATA_MIGRATION_OVERRIDE_WARNING,\n  UNSUPPORTED_RESIDENCY_SERVER_REGION_WARNING,\n  RESERVED_KEYWORD_WARNING,\n  INVALID_CONTEXT_OBJECT_WARNING,\n  UNSUPPORTED_BEACON_API_WARNING,\n  TIMEOUT_NOT_NUMBER_WARNING,\n  TIMEOUT_ZERO_WARNING,\n  TIMEOUT_NOT_RECOMMENDED_WARNING,\n  INVALID_SESSION_ID_WARNING,\n  REPORTING_PLUGIN_INIT_FAILURE_ERROR,\n  NOTIFY_FAILURE_ERROR,\n  PLUGIN_NAME_MISSING_ERROR,\n  PLUGIN_ALREADY_EXISTS_ERROR,\n  PLUGIN_NOT_FOUND_ERROR,\n  PLUGIN_ENGINE_BUG_ERROR,\n  PLUGIN_DEPS_ERROR,\n  PLUGIN_INVOCATION_ERROR,\n  STORAGE_QUOTA_EXCEEDED_WARNING,\n  STORAGE_UNAVAILABLE_WARNING,\n  STORAGE_UNAVAILABILITY_ERROR_PREFIX,\n  SOURCE_CONFIG_FETCH_ERROR,\n  SOURCE_CONFIG_OPTION_ERROR,\n  INTG_CDN_BASE_URL_ERROR,\n  PLUGINS_CDN_BASE_URL_ERROR,\n  DATA_PLANE_URL_ERROR,\n  WRITE_KEY_VALIDATION_ERROR,\n  DATA_PLANE_URL_VALIDATION_ERROR,\n  READY_API_CALLBACK_ERROR,\n  XHR_DELIVERY_ERROR,\n  XHR_REQUEST_ERROR,\n  XHR_SEND_ERROR,\n  XHR_PAYLOAD_PREP_ERROR,\n  STORE_DATA_SAVE_ERROR,\n  STORE_DATA_FETCH_ERROR,\n  EVENT_OBJECT_GENERATION_ERROR,\n  PLUGIN_EXT_POINT_MISSING_ERROR,\n  PLUGIN_EXT_POINT_INVALID_ERROR,\n  STORAGE_TYPE_VALIDATION_WARNING,\n  WRITE_KEY_NOT_A_STRING_ERROR,\n  EMPTY_GROUP_CALL_ERROR,\n  READY_CALLBACK_INVOKE_ERROR,\n  API_CALLBACK_INVOKE_ERROR,\n  INVALID_CONFIG_URL_WARNING,\n  POLYFILL_SCRIPT_LOAD_ERROR,\n  COOKIE_DATA_ENCODING_ERROR,\n  UNSUPPORTED_PRE_CONSENT_STORAGE_STRATEGY,\n  UNSUPPORTED_PRE_CONSENT_EVENTS_DELIVERY_TYPE,\n  SOURCE_CONFIG_RESOLUTION_ERROR,\n  NATIVE_DEST_PLUGIN_INITIALIZE_ERROR,\n  DATAPLANE_PLUGIN_INITIALIZE_ERROR,\n  DMT_PLUGIN_INITIALIZE_ERROR,\n  NATIVE_DEST_PLUGIN_ENQUEUE_ERROR,\n  DATAPLANE_PLUGIN_ENQUEUE_ERROR,\n  DATA_SERVER_URL_INVALID_ERROR,\n  DATA_SERVER_REQUEST_FAIL_ERROR,\n  FAILED_SETTING_COOKIE_FROM_SERVER_ERROR,\n  FAILED_SETTING_COOKIE_FROM_SERVER_GLOBAL_ERROR,\n  generateMisconfiguredPluginsWarning,\n  INVALID_POLYFILL_URL_WARNING,\n  SOURCE_DISABLED_ERROR,\n};\n", "const CDN_INT_DIR = 'js-integrations';\nconst CDN_PLUGINS_DIR = 'plugins';\nconst URL_PATTERN = new RegExp(\n  '^(https?:\\\\/\\\\/)' + // protocol\n    '(' +\n    '((([a-zA-Z\\\\d]([a-zA-Z\\\\d-]*[a-zA-Z\\\\d])*)\\\\.)+[a-zA-Z]{2,}|' + // domain name\n    'localhost|' + // localhost\n    '((25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]?)\\\\.){3}' + // OR IP (v4) address first 3 octets\n    '(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]?))' + // last octet of IP address\n    ')' +\n    '(\\\\:\\\\d+)?' + // port\n    '(\\\\/[-a-zA-Z\\\\d%_.~+]*)*' + // path\n    '(\\\\?[;&a-zA-Z\\\\d%_.~+=-]*)?' + // query string\n    '(\\\\#[-a-zA-Z\\\\d_]*)?$', // fragment locator\n);\n\nexport { CDN_INT_DIR, CDN_PLUGINS_DIR, URL_PATTERN };\n", "import { CDN_INT_DIR, CDN_PLUGINS_DIR } from '@rudderstack/analytics-js-common/constants/urls';\nimport { IS_LEGACY_BUILD } from './app';\n\nconst BUILD_TYPE = IS_LEGACY_BUILD ? 'legacy' : 'modern';\nconst SDK_CDN_BASE_URL = 'https://cdn.rudderlabs.com';\nconst CDN_ARCH_VERSION_DIR = 'v3';\nconst DEST_SDK_BASE_URL = `${SDK_CDN_BASE_URL}/${CDN_ARCH_VERSION_DIR}/${BUILD_TYPE}/${CDN_INT_DIR}`;\nconst PLUGINS_BASE_URL = `${SDK_CDN_BASE_URL}/${CDN_ARCH_VERSION_DIR}/${BUILD_TYPE}/${CDN_PLUGINS_DIR}`;\nconst DEFAULT_CONFIG_BE_URL = 'https://api.rudderstack.com';\n\nexport {\n  BUILD_TYPE,\n  SDK_CDN_BASE_URL,\n  CDN_ARCH_VERSION_DIR,\n  DEST_SDK_BASE_URL,\n  P<PERSON>UGINS_BASE_URL,\n  DEFAULT_CONFIG_BE_URL,\n};\n", "import type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\n\nconst DEFAULT_ERROR_REPORTING_PROVIDER = 'bugsnag';\nconst DEFAULT_STORAGE_ENCRYPTION_VERSION = 'v3';\nconst DEFAULT_DATA_PLANE_EVENTS_TRANSPORT = 'xhr';\n\nexport const ConsentManagersToPluginNameMap: Record<string, PluginName> = {\n  oneTrust: 'OneTrustConsentManager',\n  ketch: 'KetchConsentManager',\n  custom: 'CustomConsentManager',\n};\n\nexport const ErrorReportingProvidersToPluginNameMap: Record<string, PluginName> = {\n  [DEFAULT_ERROR_REPORTING_PROVIDER]: 'Bugsnag',\n};\n\nexport const StorageEncryptionVersionsToPluginNameMap: Record<string, PluginName> = {\n  [DEFAULT_STORAGE_ENCRYPTION_VERSION]: 'StorageEncryption',\n  legacy: 'StorageEncryptionLegacy',\n};\n\nexport const DataPlaneEventsTransportToPluginNameMap: Record<string, PluginName> = {\n  [DEFAULT_DATA_PLANE_EVENTS_TRANSPORT]: 'XhrQueue',\n  beacon: 'BeaconQueue',\n};\n\nconst DEFAULT_DATA_SERVICE_ENDPOINT = 'rsaRequest';\n\nexport {\n  DEFAULT_ERROR_REPORTING_PROVIDER,\n  DEFAULT_STORAGE_ENCRYPTION_VERSION,\n  DEFAULT_DATA_PLANE_EVENTS_TRANSPORT,\n  DEFAULT_DATA_SERVICE_ENDPOINT,\n};\n", "import { signal } from '@preact/signals-core';\nimport { clone } from 'ramda';\nimport type { LoadOptions } from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport type { LoadOptionsState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport {\n  DEFAULT_DATA_PLANE_EVENTS_BUFFER_TIMEOUT_MS,\n  DEFAULT_SESSION_TIMEOUT_MS,\n} from '../../constants/timeouts';\nimport { DEFAULT_CONFIG_BE_URL } from '../../constants/urls';\nimport { DEFAULT_STORAGE_ENCRYPTION_VERSION } from '../../components/configManager/constants';\n\nconst defaultLoadOptions: LoadOptions = {\n  logLevel: 'ERROR',\n  configUrl: DEFAULT_CONFIG_BE_URL,\n  loadIntegration: true,\n  sessions: {\n    autoTrack: true,\n    timeout: DEFAULT_SESSION_TIMEOUT_MS,\n  },\n  sameSiteCookie: 'Lax',\n  polyfillIfRequired: true,\n  integrations: { All: true },\n  useBeacon: false,\n  beaconQueueOptions: {},\n  destinationsQueueOptions: {},\n  queueOptions: {},\n  lockIntegrationsVersion: false,\n  uaChTrackLevel: 'none',\n  plugins: [],\n  useGlobalIntegrationsConfigInEvents: false,\n  bufferDataPlaneEventsUntilReady: false,\n  dataPlaneEventsBufferTimeout: DEFAULT_DATA_PLANE_EVENTS_BUFFER_TIMEOUT_MS,\n  storage: {\n    encryption: {\n      version: DEFAULT_STORAGE_ENCRYPTION_VERSION,\n    },\n    migrate: true,\n  },\n  sendAdblockPageOptions: {},\n  useServerSideCookies: false,\n};\n\nconst loadOptionsState: LoadOptionsState = signal(clone(defaultLoadOptions));\n\nexport { loadOptionsState };\n", "import type { ApiObject } from '@rudderstack/analytics-js-common/types/ApiObject';\nimport type { SessionInfo } from '@rudderstack/analytics-js-common/types/Session';\n\nconst USER_SESSION_STORAGE_KEYS = {\n  userId: 'rl_user_id',\n  userTraits: 'rl_trait',\n  anonymousId: 'rl_anonymous_id',\n  groupId: 'rl_group_id',\n  groupTraits: 'rl_group_trait',\n  initialReferrer: 'rl_page_init_referrer',\n  initialReferringDomain: 'rl_page_init_referring_domain',\n  sessionInfo: 'rl_session',\n  authToken: 'rl_auth_token',\n};\n\nconst DEFAULT_USER_SESSION_VALUES = {\n  userId: '',\n  userTraits: {} as ApiObject,\n  anonymousId: '',\n  groupId: '',\n  groupTraits: {} as ApiObject,\n  initialReferrer: '',\n  initialReferringDomain: '',\n  sessionInfo: {} as SessionInfo,\n  authToken: null,\n};\n\nexport { USER_SESSION_STORAGE_KEYS, DEFAULT_USER_SESSION_VALUES };\n", "import { signal } from '@preact/signals-core';\nimport type { SessionState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport type { SessionInfo } from '@rudderstack/analytics-js-common/types/Session';\nimport { DEFAULT_USER_SESSION_VALUES } from '../../components/userSessionManager/constants';\nimport { DEFAULT_SESSION_TIMEOUT_MS } from '../../constants/timeouts';\n\nconst defaultSessionConfiguration: SessionInfo = {\n  autoTrack: true,\n  timeout: DEFAULT_SESSION_TIMEOUT_MS,\n};\n\nconst sessionState: SessionState = {\n  userId: signal(DEFAULT_USER_SESSION_VALUES.userId),\n  userTraits: signal(DEFAULT_USER_SESSION_VALUES.userTraits),\n  anonymousId: signal(DEFAULT_USER_SESSION_VALUES.anonymousId),\n  groupId: signal(DEFAULT_USER_SESSION_VALUES.groupId),\n  groupTraits: signal(DEFAULT_USER_SESSION_VALUES.groupTraits),\n  initialReferrer: signal(DEFAULT_USER_SESSION_VALUES.initialReferrer),\n  initialReferringDomain: signal(DEFAULT_USER_SESSION_VALUES.initialReferringDomain),\n  sessionInfo: signal(DEFAULT_USER_SESSION_VALUES.sessionInfo),\n  authToken: signal(DEFAULT_USER_SESSION_VALUES.authToken),\n};\n\nexport { sessionState, defaultSessionConfiguration };\n", "import { signal } from '@preact/signals-core';\nimport type { CapabilitiesState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst capabilitiesState: CapabilitiesState = {\n  isOnline: signal(true),\n  storage: {\n    isLocalStorageAvailable: signal(false),\n    isCookieStorageAvailable: signal(false),\n    isSessionStorageAvailable: signal(false),\n  },\n  isBeaconAvailable: signal(false),\n  isLegacyDOM: signal(false),\n  isUaCHAvailable: signal(false),\n  isCryptoAvailable: signal(false),\n  isIE11: signal(false),\n  isAdBlocked: signal(false),\n};\n\nexport { capabilitiesState };\n", "import { signal } from '@preact/signals-core';\nimport type { ReportingState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst reportingState: ReportingState = {\n  isErrorReportingEnabled: signal(false),\n  isMetricsReportingEnabled: signal(false),\n  errorReportingProviderPluginName: signal(undefined),\n  isErrorReportingPluginLoaded: signal(false),\n};\n\nexport { reportingState };\n", "import { signal } from '@preact/signals-core';\nimport type { SourceConfigState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst sourceConfigState: SourceConfigState = signal(undefined);\n\nexport { sourceConfigState };\n", "import { signal } from '@preact/signals-core';\nimport type { LifecycleState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport { DEST_SDK_BASE_URL, PLUGINS_BASE_URL } from '../../constants/urls';\n\nconst lifecycleState: LifecycleState = {\n  activeDataplaneUrl: signal(undefined),\n  integrationsCDNPath: signal(DEST_SDK_BASE_URL),\n  pluginsCDNPath: signal(PLUGINS_BASE_URL),\n  sourceConfigUrl: signal(undefined),\n  status: signal(undefined),\n  initialized: signal(false),\n  logLevel: signal('ERROR'),\n  loaded: signal(false),\n  readyCallbacks: signal([]),\n  writeKey: signal(undefined),\n  dataPlaneUrl: signal(undefined),\n};\n\nexport { lifecycleState };\n", "import { signal } from '@preact/signals-core';\nimport type { ConsentsState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst consentsState: ConsentsState = {\n  enabled: signal(false),\n  initialized: signal(false),\n  data: signal({}),\n  activeConsentManagerPluginName: signal(undefined),\n  preConsent: signal({ enabled: false }),\n  postConsent: signal({}),\n  resolutionStrategy: signal('and'),\n  provider: signal(undefined),\n  metadata: signal(undefined),\n};\n\nexport { consentsState };\n", "import { signal } from '@preact/signals-core';\nimport type { MetricsState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst metricsState: MetricsState = {\n  retries: signal(0),\n  dropped: signal(0),\n  sent: signal(0),\n  queued: signal(0),\n  triggered: signal(0),\n};\n\nexport { metricsState };\n", "import { signal } from '@preact/signals-core';\nimport type { ContextState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport { APP_NAME, APP_NAMESPACE, APP_VERSION, MODULE_TYPE } from '../../constants/app';\n\nconst contextState: ContextState = {\n  app: signal({\n    name: APP_NAME,\n    namespace: APP_NAMESPACE,\n    version: APP_VERSION,\n    installType: MODULE_TYPE,\n  }),\n  traits: signal(null),\n  library: signal({\n    name: APP_NAME,\n    version: APP_VERSION,\n    snippetVersion: (globalThis as typeof window).RudderSnippetVersion,\n  }),\n  userAgent: signal(''),\n  device: signal(null),\n  network: signal(null),\n  os: signal({\n    name: '',\n    version: '',\n  }),\n  locale: signal(null),\n  screen: signal({\n    density: 0,\n    width: 0,\n    height: 0,\n    innerWidth: 0,\n    innerHeight: 0,\n  }),\n  'ua-ch': signal(undefined),\n  timezone: signal(undefined),\n};\n\nexport { contextState };\n", "import { signal } from '@preact/signals-core';\nimport type { NativeDestinationsState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst nativeDestinationsState: NativeDestinationsState = {\n  configuredDestinations: signal([]),\n  activeDestinations: signal([]),\n  loadOnlyIntegrations: signal({}),\n  failedDestinations: signal([]),\n  loadIntegration: signal(true),\n  initializedDestinations: signal([]),\n  clientDestinationsReady: signal(false),\n  integrationsConfig: signal({}),\n};\n\nexport { nativeDestinationsState };\n", "import { signal } from '@preact/signals-core';\nimport type { EventBufferState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst eventBufferState: EventBufferState = {\n  toBeProcessedArray: signal([]),\n  readyCallbacksArray: signal([]),\n};\n\nexport { eventBufferState };\n", "import { signal } from '@preact/signals-core';\nimport type { PluginsState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst pluginsState: PluginsState = {\n  ready: signal(false),\n  loadedPlugins: signal([]),\n  failedPlugins: signal([]),\n  pluginsToLoadFromConfig: signal([]),\n  activePlugins: signal([]),\n  totalPluginsToLoad: signal(0),\n};\n\nexport { pluginsState };\n", "import { signal } from '@preact/signals-core';\nimport type { StorageState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst storageState: StorageState = {\n  encryptionPluginName: signal(undefined),\n  migrate: signal(false),\n  type: signal(undefined),\n  cookie: signal(undefined),\n  entries: signal({}),\n  trulyAnonymousTracking: signal(false),\n};\n\nexport { storageState };\n", "import { signal } from '@preact/signals-core';\nimport type { ServerCookiesState } from '@rudderstack/analytics-js-common/types/ApplicationState';\n\nconst serverSideCookiesState: ServerCookiesState = {\n  isEnabledServerSideCookies: signal(false),\n  dataServiceUrl: signal(undefined),\n};\n\nexport { serverSideCookiesState };\n", "import { signal } from '@preact/signals-core';\nimport type { DataPlaneEventsState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\n\nconst dataPlaneEventsState: DataPlaneEventsState = {\n  eventsQueuePluginName: signal<PluginName | undefined>(undefined),\n  deliveryEnabled: signal(true), // Delivery should always happen\n};\n\nexport { dataPlaneEventsState };\n", "import { clone } from 'ramda';\nimport type { ApplicationState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport { loadOptionsState } from './slices/loadOptions';\nimport { sessionState } from './slices/session';\nimport { capabilitiesState } from './slices/capabilities';\nimport { reportingState } from './slices/reporting';\nimport { sourceConfigState } from './slices/source';\nimport { lifecycleState } from './slices/lifecycle';\nimport { consentsState } from './slices/consents';\nimport { metricsState } from './slices/metrics';\nimport { contextState } from './slices/context';\nimport { nativeDestinationsState } from './slices/nativeDestinations';\nimport { eventBufferState } from './slices/eventBuffer';\nimport { pluginsState } from './slices/plugins';\nimport { storageState } from './slices/storage';\nimport { serverSideCookiesState } from './slices/serverCookies';\nimport { dataPlaneEventsState } from './slices/dataPlaneEvents';\n\nconst defaultStateValues: ApplicationState = {\n  capabilities: capabilitiesState,\n  consents: consentsState,\n  context: contextState,\n  eventBuffer: eventBufferState,\n  lifecycle: lifecycleState,\n  loadOptions: loadOptionsState,\n  metrics: metricsState,\n  nativeDestinations: nativeDestinationsState,\n  plugins: pluginsState,\n  reporting: reportingState,\n  session: sessionState,\n  source: sourceConfigState,\n  storage: storageState,\n  serverCookies: serverSideCookiesState,\n  dataPlaneEvents: dataPlaneEventsState,\n};\n\nconst state: ApplicationState = {\n  ...clone(defaultStateValues),\n};\n\nconst resetState = () => {\n  state.capabilities = clone(defaultStateValues.capabilities);\n  state.consents = clone(defaultStateValues.consents);\n  state.context = clone(defaultStateValues.context);\n  state.eventBuffer = clone(defaultStateValues.eventBuffer);\n  state.lifecycle = clone(defaultStateValues.lifecycle);\n  state.loadOptions = clone(defaultStateValues.loadOptions);\n  state.metrics = clone(defaultStateValues.metrics);\n  state.nativeDestinations = clone(defaultStateValues.nativeDestinations);\n  state.plugins = clone(defaultStateValues.plugins);\n  state.reporting = clone(defaultStateValues.reporting);\n  state.session = clone(defaultStateValues.session);\n  state.source = clone(defaultStateValues.source);\n  state.storage = clone(defaultStateValues.storage);\n  state.serverCookies = clone(defaultStateValues.serverCookies);\n  state.dataPlaneEvents = clone(defaultStateValues.dataPlaneEvents);\n};\n\nexport { state, resetState };\n", "import { isFunction } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { getValueByPath, hasValueByPath } from '@rudderstack/analytics-js-common/utilities/object';\nimport type {\n  ExtensionPlugin,\n  IPluginEngine,\n  PluginEngineConfig,\n} from '@rudderstack/analytics-js-common/types/PluginEngine';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { PLUGIN_ENGINE } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { defaultLogger } from '../Logger';\nimport {\n  PLUGIN_ALREADY_EXISTS_ERROR,\n  PLUGIN_DEPS_ERROR,\n  PLUGIN_ENGINE_BUG_ERROR,\n  PLUGIN_EXT_POINT_INVALID_ERROR,\n  PLUGIN_EXT_POINT_MISSING_ERROR,\n  PLUGIN_INVOCATION_ERROR,\n  PLUGIN_NAME_MISSING_ERROR,\n  PLUGIN_NOT_FOUND_ERROR,\n} from '../../constants/logMessages';\n\n// TODO: create chained invoke to take the output frm first plugin and pass\n//  to next or return the value if it is the last one instead of an array per\n//  plugin that is the normal invoke\n// TODO: add invoke method for extension point that we know only one plugin can be used. add invokeMultiple and invokeSingle methods\nclass PluginEngine implements IPluginEngine {\n  plugins: ExtensionPlugin[] = [];\n  byName: Record<string, ExtensionPlugin> = {};\n  cache: Record<string, ExtensionPlugin[]> = {};\n  config: PluginEngineConfig = { throws: true };\n  logger?: ILogger;\n\n  constructor(options: PluginEngineConfig = {}, logger?: ILogger) {\n    this.config = {\n      throws: true,\n      ...options,\n    };\n\n    this.logger = logger;\n  }\n\n  register(plugin: ExtensionPlugin, state?: Record<string, any>) {\n    if (!plugin.name) {\n      const errorMessage = PLUGIN_NAME_MISSING_ERROR(PLUGIN_ENGINE);\n      if (this.config.throws) {\n        throw new Error(errorMessage);\n      } else {\n        this.logger?.error(errorMessage, plugin);\n      }\n    }\n\n    if (this.byName[plugin.name]) {\n      const errorMessage = PLUGIN_ALREADY_EXISTS_ERROR(PLUGIN_ENGINE, plugin.name);\n      if (this.config.throws) {\n        throw new Error(errorMessage);\n      } else {\n        this.logger?.error(errorMessage);\n      }\n    }\n\n    this.cache = {};\n    this.plugins = this.plugins.slice();\n    let pos = this.plugins.length;\n\n    this.plugins.forEach((pluginItem: ExtensionPlugin, index: number) => {\n      if (pluginItem.deps?.includes(plugin.name)) {\n        pos = Math.min(pos, index);\n      }\n    });\n\n    this.plugins.splice(pos, 0, plugin);\n\n    this.byName[plugin.name] = plugin;\n\n    if (isFunction(plugin.initialize)) {\n      plugin.initialize(state);\n    }\n  }\n\n  unregister(name: string) {\n    const plugin = this.byName[name];\n\n    if (!plugin) {\n      const errorMessage = PLUGIN_NOT_FOUND_ERROR(PLUGIN_ENGINE, name);\n      if (this.config.throws) {\n        throw new Error(errorMessage);\n      } else {\n        this.logger?.error(errorMessage);\n      }\n    }\n\n    const index = this.plugins.indexOf(plugin as ExtensionPlugin);\n\n    if (index === -1) {\n      const errorMessage = PLUGIN_ENGINE_BUG_ERROR(PLUGIN_ENGINE, name);\n      if (this.config.throws) {\n        throw new Error(errorMessage);\n      } else {\n        this.logger?.error(errorMessage);\n      }\n    }\n\n    this.cache = {};\n    delete this.byName[name];\n    this.plugins = this.plugins.slice();\n    this.plugins.splice(index, 1);\n  }\n\n  getPlugin(name: string): ExtensionPlugin | undefined {\n    return this.byName[name];\n  }\n\n  getPlugins(extPoint?: string): ExtensionPlugin[] {\n    const lifeCycleName = extPoint ?? '.';\n\n    if (!this.cache[lifeCycleName]) {\n      this.cache[lifeCycleName] = this.plugins.filter(plugin => {\n        if (plugin.deps?.some(dependency => !this.byName[dependency])) {\n          // If deps not exist, then not load it.\n          const notExistDeps = plugin.deps.filter(dependency => !this.byName[dependency]);\n          this.logger?.error(PLUGIN_DEPS_ERROR(PLUGIN_ENGINE, plugin.name, notExistDeps));\n          return false;\n        }\n        return lifeCycleName === '.' ? true : hasValueByPath(plugin, lifeCycleName);\n      });\n    }\n\n    return this.cache[lifeCycleName] as ExtensionPlugin[];\n  }\n\n  // This method allows to process this.plugins so that it could\n  // do some unified pre-process before application starts.\n  processRawPlugins(callback: (plugins: ExtensionPlugin[]) => any) {\n    callback(this.plugins);\n    this.cache = {};\n  }\n\n  invoke<T = any>(extPoint?: string, allowMultiple = true, ...args: any[]): Nullable<T>[] {\n    let extensionPointName = extPoint;\n\n    if (!extensionPointName) {\n      throw new Error(PLUGIN_EXT_POINT_MISSING_ERROR);\n    }\n\n    const noCall = extensionPointName.startsWith('!');\n    const throws = this.config.throws ?? extensionPointName.endsWith('!');\n\n    // eslint-disable-next-line unicorn/better-regex\n    extensionPointName = extensionPointName.replace(/(^!|!$)/g, '');\n\n    if (!extensionPointName) {\n      throw new Error(PLUGIN_EXT_POINT_INVALID_ERROR);\n    }\n\n    const extensionPointNameParts = extensionPointName.split('.');\n    extensionPointNameParts.pop();\n\n    const pluginMethodPath = extensionPointNameParts.join('.');\n    const pluginsToInvoke = allowMultiple\n      ? this.getPlugins(extensionPointName)\n      : [this.getPlugins(extensionPointName)[0] as ExtensionPlugin];\n\n    return pluginsToInvoke.map(plugin => {\n      const method = getValueByPath(plugin, extensionPointName as string);\n\n      if (!isFunction(method) || noCall) {\n        return method;\n      }\n\n      try {\n        return method.apply(getValueByPath(plugin, pluginMethodPath), args);\n      } catch (err) {\n        // When a plugin failed, doesn't break the app\n        if (throws) {\n          throw err;\n        } else {\n          this.logger?.error(\n            PLUGIN_INVOCATION_ERROR(PLUGIN_ENGINE, extensionPointName, plugin.name),\n            err,\n          );\n        }\n      }\n\n      return null;\n    });\n  }\n\n  invokeSingle<T = any>(extPoint?: string, ...args: any[]): Nullable<T> {\n    return this.invoke(extPoint, false, ...args)[0];\n  }\n\n  invokeMultiple<T = any>(extPoint?: string, ...args: any[]): Nullable<T>[] {\n    return this.invoke(extPoint, true, ...args);\n  }\n}\n\nconst defaultPluginEngine = new PluginEngine({ throws: true }, defaultLogger);\n\nexport { PluginEngine, defaultPluginEngine };\n", "const FAILED_REQUEST_ERR_MSG_PREFIX = 'The request failed';\nconst ERROR_MESSAGES_TO_BE_FILTERED = [FAILED_REQUEST_ERR_MSG_PREFIX];\n\nexport { FAILED_REQUEST_ERR_MSG_PREFIX, ERROR_MESSAGES_TO_BE_FILTERED };\n", "import type { IPluginEngine } from '@rudderstack/analytics-js-common/types/PluginEngine';\nimport { removeDoubleSpaces } from '@rudderstack/analytics-js-common/utilities/string';\nimport { isTypeOfError } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type {\n  ErrorState,\n  IErrorHandler,\n  PreLoadErrorData,\n  SDKError,\n} from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IExternalSrcLoader } from '@rudderstack/analytics-js-common/services/ExternalSrcLoader/types';\nimport { ERROR_HANDLER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { LOG_CONTEXT_SEPARATOR } from '@rudderstack/analytics-js-common/constants/logMessages';\nimport { BufferQueue } from '@rudderstack/analytics-js-common/services/BufferQueue/BufferQueue';\nimport {\n  NOTIFY_FAILURE_ERROR,\n  REPORTING_PLUGIN_INIT_FAILURE_ERROR,\n} from '../../constants/logMessages';\nimport { state } from '../../state';\nimport { defaultPluginEngine } from '../PluginEngine';\nimport { defaultLogger } from '../Logger';\nimport { isAllowedToBeNotified, processError } from './processError';\n\n/**\n * A service to handle errors\n */\nclass ErrorHandler implements IErrorHandler {\n  logger?: ILogger;\n  pluginEngine?: IPluginEngine;\n  errReportingClient?: any;\n  errorBuffer: BufferQueue<PreLoadErrorData>;\n\n  // If no logger is passed errors will be thrown as unhandled error\n  constructor(logger?: ILogger, pluginEngine?: IPluginEngine) {\n    this.logger = logger;\n    this.pluginEngine = pluginEngine;\n    this.errorBuffer = new BufferQueue();\n    this.attachEffect();\n  }\n\n  attachEffect() {\n    if (state.reporting.isErrorReportingPluginLoaded.value === true) {\n      while (this.errorBuffer.size() > 0) {\n        const errorToProcess = this.errorBuffer.dequeue();\n\n        if (errorToProcess) {\n          // send it to the plugin\n        }\n      }\n    }\n  }\n\n  attachErrorListeners() {\n    if ('addEventListener' in (globalThis as typeof window)) {\n      (globalThis as typeof window).addEventListener('error', (event: ErrorEvent | Event) => {\n        this.onError(event, undefined, undefined, undefined, 'unhandledException');\n      });\n\n      (globalThis as typeof window).addEventListener(\n        'unhandledrejection',\n        (event: PromiseRejectionEvent) => {\n          this.onError(event, undefined, undefined, undefined, 'unhandledPromiseRejection');\n        },\n      );\n    } else {\n      this.logger?.debug(`Failed to attach global error listeners.`);\n    }\n  }\n\n  init(externalSrcLoader: IExternalSrcLoader) {\n    if (!this.pluginEngine) {\n      return;\n    }\n\n    try {\n      const extPoint = 'errorReporting.init';\n      const errReportingInitVal = this.pluginEngine.invokeSingle(\n        extPoint,\n        state,\n        this.pluginEngine,\n        externalSrcLoader,\n        this.logger,\n      );\n      if (errReportingInitVal instanceof Promise) {\n        errReportingInitVal\n          .then((client: any) => {\n            this.errReportingClient = client;\n          })\n          .catch(err => {\n            this.logger?.error(REPORTING_PLUGIN_INIT_FAILURE_ERROR(ERROR_HANDLER), err);\n          });\n      }\n    } catch (err) {\n      this.onError(err, ERROR_HANDLER);\n    }\n  }\n\n  onError(\n    error: SDKError,\n    context = '',\n    customMessage = '',\n    shouldAlwaysThrow = false,\n    errorType = 'handled',\n  ) {\n    // Error handling is already implemented in processError method\n    let errorMessage = processError(error);\n\n    // If no error message after we normalize, then we swallow/ignore the errors\n    if (!errorMessage) {\n      return;\n    }\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const errorState: ErrorState = {\n      severity: 'error',\n      unhandled: errorType !== 'handled',\n      severityReason: { type: errorType },\n    };\n    errorMessage = removeDoubleSpaces(\n      `${context}${LOG_CONTEXT_SEPARATOR}${customMessage} ${errorMessage}`,\n    );\n\n    let normalizedError = new Error(errorMessage);\n    if (isTypeOfError(error)) {\n      normalizedError = Object.create(error, {\n        message: { value: errorMessage },\n      });\n    }\n    if (errorType === 'handled') {\n      // TODO: Remove the below line once the new Reporting plugin is ready\n      this.notifyError(normalizedError as Error);\n\n      if (this.logger) {\n        this.logger.error(errorMessage);\n\n        if (shouldAlwaysThrow) {\n          throw normalizedError;\n        }\n      } else {\n        throw normalizedError;\n      }\n    }\n\n    // eslint-disable-next-line sonarjs/no-all-duplicated-branches\n    if (\n      state.reporting.isErrorReportingEnabled.value &&\n      !state.reporting.isErrorReportingPluginLoaded.value\n    ) {\n      // buffer the error\n      // TODO: un-comment the below line once the plugin is ready\n      // this.errorBuffer.enqueue({ error, errorState });\n    } else {\n      // send it to plugin\n    }\n  }\n\n  /**\n   * Add breadcrumbs to add insight of a user's journey before an error\n   * occurred and send to external error monitoring service via a plugin\n   *\n   * @param {string} breadcrumb breadcrumbs message\n   */\n  leaveBreadcrumb(breadcrumb: string) {\n    if (this.pluginEngine) {\n      try {\n        this.pluginEngine.invokeSingle(\n          'errorReporting.breadcrumb',\n          this.pluginEngine,\n          this.errReportingClient,\n          breadcrumb,\n          this.logger,\n        );\n      } catch (err) {\n        this.onError(err, ERROR_HANDLER, 'errorReporting.breadcrumb');\n      }\n    }\n  }\n\n  /**\n   * Send handled errors to external error monitoring service via a plugin\n   *\n   * @param {Error} error Error instance from handled error\n   */\n  notifyError(error: Error) {\n    if (this.pluginEngine && isAllowedToBeNotified(error)) {\n      try {\n        this.pluginEngine.invokeSingle(\n          'errorReporting.notify',\n          this.pluginEngine,\n          this.errReportingClient,\n          error,\n          state,\n          this.logger,\n        );\n      } catch (err) {\n        // Not calling onError here as we don't want to go into infinite loop\n        this.logger?.error(NOTIFY_FAILURE_ERROR(ERROR_HANDLER), err);\n      }\n    }\n  }\n}\n\nconst defaultErrorHandler = new ErrorHandler(defaultLogger, defaultPluginEngine);\n\nexport { ErrorHandler, defaultErrorHandler };\n", "import { stringifyWithoutCircular } from '@rudderstack/analytics-js-common/utilities/json';\nimport { isString } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { ErrorTarget, SDKError } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport { ERROR_MESSAGES_TO_BE_FILTERED } from '../../constants/errors';\nimport { LOAD_ORIGIN } from './constant';\n\n/**\n * Utility method to normalise errors\n */\nconst processError = (error: SDKError): string => {\n  let errorMessage;\n\n  try {\n    if (isString(error)) {\n      errorMessage = error;\n    } else if (error instanceof Error) {\n      errorMessage = error.message;\n    } else if (error instanceof ErrorEvent) {\n      errorMessage = error.message;\n    }\n    // TODO: remove this block once all device mode integrations start using the v3 script loader module (TS)\n    else if (error instanceof Event) {\n      const eventTarget = error.target as ErrorTarget;\n      // Discard all the non-script loading errors\n      if (eventTarget && eventTarget.localName !== 'script') {\n        return '';\n      }\n      // Discard script errors that are not originated at SDK or from native SDKs\n      if (\n        eventTarget?.dataset &&\n        (eventTarget.dataset.loader !== LOAD_ORIGIN ||\n          eventTarget.dataset.isnonnativesdk !== 'true')\n      ) {\n        return '';\n      }\n      errorMessage = `Error in loading a third-party script from URL ${eventTarget?.src} with ID ${eventTarget?.id}.`;\n    } else {\n      errorMessage = (error as any).message\n        ? (error as any).message\n        : stringifyWithoutCircular(error as Record<string, any>);\n    }\n  } catch (e) {\n    errorMessage = `Unknown error: ${(e as Error).message}`;\n  }\n\n  return errorMessage;\n};\n\n/**\n * A function to determine whether the error should be promoted to notify or not\n * @param {Error} error\n * @returns\n */\nconst isAllowedToBeNotified = (error: Error) => {\n  if (error.message) {\n    return !ERROR_MESSAGES_TO_BE_FILTERED.some(e => error.message.includes(e));\n  }\n  return true;\n};\n\nexport { processError, isAllowedToBeNotified };\n", "const LOAD_ORIGIN = 'RS_JS_SDK';\n\nexport { LOAD_ORIGIN };\n", "import type { Destination } from '../types/Destination';\n\n/**\n * A function to filter and return non cloud mode destinations\n * @param destination\n *\n * @returns boolean\n */\nconst isNonCloudDestination = (destination: Destination): boolean =>\n  Boolean(\n    destination.config.connectionMode !== 'cloud' ||\n      destination.config.useNativeSDKToSend === true || // this is the older flag for hybrid mode destinations\n      destination.config.useNativeSDK === true,\n  );\n\nconst isHybridModeDestination = (destination: Destination): boolean =>\n  Boolean(\n    destination.config.connectionMode === 'hybrid' ||\n      destination.config.useNativeSDKToSend === true,\n  );\n\n/**\n * A function to filter and return non cloud mode destinations\n * @param destinations\n *\n * @returns destinations\n */\nconst getNonCloudDestinations = (destinations: Destination[]): Destination[] | [] =>\n  destinations.filter(isNonCloudDestination);\n\nexport { isNonCloudDestination, getNonCloudDestinations, isHybridModeDestination };\n", "import type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\n\n/**\n * List of plugin names that are loaded as direct imports in all builds\n */\nconst localPluginNames: PluginName[] = [];\n\n/**\n * List of plugin names that are loaded as dynamic imports in modern builds\n */\nconst pluginNamesList: PluginName[] = [\n  'BeaconQueue',\n  'Bugsnag',\n  'CustomConsentManager',\n  'DeviceModeDestinations',\n  'DeviceModeTransformation',\n  'ErrorReporting',\n  'ExternalAnonymousId',\n  'GoogleLinker',\n  'KetchConsentManager',\n  'NativeDestinationQueue',\n  'OneTrustConsentManager',\n  'StorageEncryption',\n  'StorageEncryptionLegacy',\n  'StorageMigrator',\n  'XhrQueue',\n];\n\nexport { localPluginNames, pluginNamesList };\n", "import type { ExtensionPlugin } from '@rudderstack/analytics-js-common/types/PluginEngine';\nimport type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport type { PluginMap } from './types';\nimport { pluginNamesList } from './pluginNames';\n\n/**\n * Get the lazy loaded dynamic import for a plugin name\n */\nconst getFederatedModuleImport = (\n  pluginName: PluginName,\n): (() => Promise<ExtensionPlugin>) | undefined => {\n  switch (pluginName) {\n    case 'BeaconQueue':\n      return () => import('rudderAnalyticsRemotePlugins/BeaconQueue');\n    case 'Bugsnag':\n      return () => import('rudderAnalyticsRemotePlugins/Bugsnag');\n    case 'CustomConsentManager':\n      return () => import('rudderAnalyticsRemotePlugins/CustomConsentManager');\n    case 'DeviceModeDestinations':\n      return () => import('rudderAnalyticsRemotePlugins/DeviceModeDestinations');\n    case 'DeviceModeTransformation':\n      return () => import('rudderAnalyticsRemotePlugins/DeviceModeTransformation');\n    case 'ErrorReporting':\n      return () => import('rudderAnalyticsRemotePlugins/ErrorReporting');\n    case 'ExternalAnonymousId':\n      return () => import('rudderAnalyticsRemotePlugins/ExternalAnonymousId');\n    case 'GoogleLinker':\n      return () => import('rudderAnalyticsRemotePlugins/GoogleLinker');\n    case 'KetchConsentManager':\n      return () => import('rudderAnalyticsRemotePlugins/KetchConsentManager');\n    case 'NativeDestinationQueue':\n      return () => import('rudderAnalyticsRemotePlugins/NativeDestinationQueue');\n    case 'OneTrustConsentManager':\n      return () => import('rudderAnalyticsRemotePlugins/OneTrustConsentManager');\n    case 'StorageEncryption':\n      return () => import('rudderAnalyticsRemotePlugins/StorageEncryption');\n    case 'StorageEncryptionLegacy':\n      return () => import('rudderAnalyticsRemotePlugins/StorageEncryptionLegacy');\n    case 'StorageMigrator':\n      return () => import('rudderAnalyticsRemotePlugins/StorageMigrator');\n    case 'XhrQueue':\n      return () => import('rudderAnalyticsRemotePlugins/XhrQueue');\n    default:\n      return undefined;\n  }\n};\n\n/**\n * Map of active plugin names to their dynamic import\n */\nconst federatedModulesBuildPluginImports = (\n  activePluginNames: PluginName[],\n): PluginMap<Promise<ExtensionPlugin>> => {\n  const remotePlugins = {} as PluginMap<Promise<ExtensionPlugin>>;\n\n  activePluginNames.forEach(pluginName => {\n    if (pluginNamesList.includes(pluginName)) {\n      const lazyLoadImport = getFederatedModuleImport(pluginName);\n      if (lazyLoadImport) {\n        remotePlugins[pluginName] = lazyLoadImport;\n      }\n    }\n  });\n\n  return remotePlugins;\n};\n\nexport { federatedModulesBuildPluginImports };\n", "import type { ExtensionPlugin } from '@rudderstack/analytics-js-common/types/PluginEngine';\nimport type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport type { PluginMap } from './types';\nimport { getBundledBuildPluginImports } from './bundledBuildPluginImports';\nimport { federatedModulesBuildPluginImports } from './federatedModulesBuildPluginImports';\n\n/**\n * Map of mandatory plugin names and direct imports\n */\nconst getMandatoryPluginsMap = (): PluginMap => ({}) as PluginMap;\n\n/**\n * Map of optional plugin names and direct imports for legacy builds\n */\nconst getOptionalPluginsMap = (): PluginMap => {\n  if (!__BUNDLE_ALL_PLUGINS__) {\n    return {} as PluginMap;\n  }\n\n  return getBundledBuildPluginImports();\n};\n\n/**\n * Map of optional plugin names and dynamic imports for modern builds\n */\nconst getRemotePluginsMap = (\n  activePluginNames: PluginName[],\n): PluginMap<Promise<ExtensionPlugin>> => {\n  if (__BUNDLE_ALL_PLUGINS__) {\n    return {} as PluginMap<Promise<ExtensionPlugin>>;\n  }\n\n  return federatedModulesBuildPluginImports?.(activePluginNames) || {};\n};\n\nconst pluginsInventory: PluginMap = {\n  ...getMandatoryPluginsMap(),\n  ...getOptionalPluginsMap(),\n};\n\nconst remotePluginsInventory = (\n  activePluginNames: PluginName[],\n): PluginMap<Promise<ExtensionPlugin>> => ({\n  ...getRemotePluginsMap(activePluginNames),\n});\n\nexport { pluginsInventory, remotePluginsInventory, getMandatoryPluginsMap };\n", "import { batch, effect } from '@preact/signals-core';\nimport type {\n  ExtensionPlugin,\n  IPluginEngine,\n} from '@rudderstack/analytics-js-common/types/PluginEngine';\nimport { getNonCloudDestinations } from '@rudderstack/analytics-js-common/utilities/destinations';\nimport type {\n  IPluginsManager,\n  PluginName,\n} from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport type { IErrorHandler } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { PLUGINS_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { isDefined, isFunction } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { generateMisconfiguredPluginsWarning } from '@rudderstack/analytics-js/constants/logMessages';\nimport { setExposedGlobal } from '../utilities/globals';\nimport { state } from '../../state';\nimport {\n  ErrorReportingProvidersToPluginNameMap,\n  ConsentManagersToPluginNameMap,\n  StorageEncryptionVersionsToPluginNameMap,\n  DataPlaneEventsTransportToPluginNameMap,\n} from '../configManager/constants';\nimport { pluginNamesList } from './pluginNames';\nimport {\n  getMandatoryPluginsMap,\n  pluginsInventory,\n  remotePluginsInventory,\n} from './pluginsInventory';\nimport type { PluginsGroup } from './types';\n\n// TODO: we may want to add chained plugins that pass their value to the next one\n// TODO: add retry mechanism for getting remote plugins\n// TODO: add timeout error mechanism for marking remote plugins that failed to load as failed in state\nclass PluginsManager implements IPluginsManager {\n  engine: IPluginEngine;\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n\n  constructor(engine: IPluginEngine, errorHandler?: IErrorHandler, logger?: ILogger) {\n    this.engine = engine;\n\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Orchestrate the plugin loading and registering\n   */\n  init() {\n    state.lifecycle.status.value = 'pluginsLoading';\n    // Expose pluginsCDNPath to global object, so it can be used in the promise that determines\n    // remote plugin cdn path to support proxied plugin remotes\n    if (!__BUNDLE_ALL_PLUGINS__) {\n      setExposedGlobal('pluginsCDNPath', state.lifecycle.pluginsCDNPath.value);\n    }\n    this.setActivePlugins();\n    this.registerLocalPlugins();\n    this.registerRemotePlugins();\n    this.attachEffects();\n  }\n\n  /**\n   * Update state based on plugin loaded status\n   */\n  // eslint-disable-next-line class-methods-use-this\n  attachEffects() {\n    effect(() => {\n      const isAllPluginsReady =\n        state.plugins.activePlugins.value.length === 0 ||\n        state.plugins.loadedPlugins.value.length + state.plugins.failedPlugins.value.length ===\n          state.plugins.totalPluginsToLoad.value;\n\n      if (isAllPluginsReady) {\n        batch(() => {\n          state.plugins.ready.value = true;\n          // TODO: decide what to do if a plugin fails to load for any reason.\n          //  Should we stop here or should we progress?\n          state.lifecycle.status.value = 'pluginsReady';\n        });\n      }\n    });\n  }\n\n  /**\n   * Determine the list of plugins that should be loaded based on sourceConfig & load options\n   */\n  // eslint-disable-next-line class-methods-use-this\n  getPluginsToLoadBasedOnConfig(): PluginName[] {\n    // This contains the default plugins if load option has been omitted by user\n    let pluginsToLoadFromConfig = state.plugins.pluginsToLoadFromConfig.value as PluginName[];\n\n    if (!pluginsToLoadFromConfig) {\n      return [];\n    }\n\n    const pluginGroupsToProcess: PluginsGroup[] = [\n      {\n        configurationStatus: () => isDefined(state.dataPlaneEvents.eventsQueuePluginName.value),\n        configurationStatusStr: 'Data plane events delivery is enabled',\n        activePluginName: state.dataPlaneEvents.eventsQueuePluginName.value,\n        supportedPlugins: Object.values(DataPlaneEventsTransportToPluginNameMap),\n        shouldAddMissingPlugins: true,\n      },\n      {\n        configurationStatus: () =>\n          isDefined(state.reporting.errorReportingProviderPluginName.value),\n        configurationStatusStr: 'Error reporting is enabled',\n        activePluginName: state.reporting.errorReportingProviderPluginName.value,\n        basePlugins: ['ErrorReporting'],\n        supportedPlugins: Object.values(ErrorReportingProvidersToPluginNameMap),\n      },\n      {\n        configurationStatus: () =>\n          getNonCloudDestinations(state.nativeDestinations.configuredDestinations.value).length > 0,\n        configurationStatusStr: 'Device mode destinations are connected to the source',\n        supportedPlugins: ['DeviceModeDestinations', 'NativeDestinationQueue'] as PluginName[],\n      },\n      {\n        configurationStatus: () =>\n          getNonCloudDestinations(state.nativeDestinations.configuredDestinations.value).some(\n            destination => destination.shouldApplyDeviceModeTransformation,\n          ),\n        configurationStatusStr:\n          'Device mode transformations are enabled for at least one destination',\n        supportedPlugins: ['DeviceModeTransformation'] as PluginName[],\n      },\n      {\n        configurationStatus: () => isDefined(state.consents.activeConsentManagerPluginName.value),\n        configurationStatusStr: 'Consent management is enabled',\n        activePluginName: state.consents.activeConsentManagerPluginName.value,\n        supportedPlugins: Object.values(ConsentManagersToPluginNameMap),\n      },\n      {\n        configurationStatus: () => isDefined(state.storage.encryptionPluginName.value),\n        configurationStatusStr: 'Storage encryption is enabled',\n        activePluginName: state.storage.encryptionPluginName.value,\n        supportedPlugins: Object.values(StorageEncryptionVersionsToPluginNameMap),\n      },\n      {\n        configurationStatus: () => state.storage.migrate.value,\n        configurationStatusStr: 'Storage migration is enabled',\n        supportedPlugins: ['StorageMigrator'],\n      },\n    ];\n\n    const addMissingPlugins = false;\n    pluginGroupsToProcess.forEach(group => {\n      if (group.configurationStatus()) {\n        pluginsToLoadFromConfig = pluginsToLoadFromConfig.filter(\n          group.activePluginName\n            ? pluginName =>\n                !(\n                  pluginName !== group.activePluginName &&\n                  group.supportedPlugins.includes(pluginName)\n                )\n            : pluginName => isDefined(pluginName), // pass through\n        );\n\n        this.addMissingPlugins(group, addMissingPlugins, pluginsToLoadFromConfig);\n      } else {\n        pluginsToLoadFromConfig = pluginsToLoadFromConfig.filter(\n          group.basePlugins !== undefined\n            ? pluginName =>\n                !(\n                  (group.basePlugins as PluginName[]).includes(pluginName) ||\n                  group.supportedPlugins.includes(pluginName)\n                )\n            : pluginName => !group.supportedPlugins.includes(pluginName),\n        );\n      }\n    });\n\n    return [...(Object.keys(getMandatoryPluginsMap()) as PluginName[]), ...pluginsToLoadFromConfig];\n  }\n\n  private addMissingPlugins(\n    group: PluginsGroup,\n    addMissingPlugins: boolean,\n    pluginsToLoadFromConfig: PluginName[],\n  ) {\n    const shouldAddMissingPlugins = group.shouldAddMissingPlugins || addMissingPlugins;\n    let pluginsToConfigure;\n    if (group.activePluginName) {\n      pluginsToConfigure = [...(group.basePlugins || []), group.activePluginName] as PluginName[];\n    } else {\n      pluginsToConfigure = [...group.supportedPlugins];\n    }\n\n    const missingPlugins = pluginsToConfigure.filter(\n      pluginName => !pluginsToLoadFromConfig.includes(pluginName),\n    );\n    if (missingPlugins.length > 0) {\n      if (shouldAddMissingPlugins) {\n        pluginsToLoadFromConfig.push(...missingPlugins);\n      }\n\n      this.logger?.warn(\n        generateMisconfiguredPluginsWarning(\n          PLUGINS_MANAGER,\n          group.configurationStatusStr,\n          missingPlugins,\n          shouldAddMissingPlugins,\n        ),\n      );\n    }\n  }\n\n  /**\n   * Determine the list of plugins that should be activated\n   */\n  setActivePlugins() {\n    const pluginsToLoad = this.getPluginsToLoadBasedOnConfig();\n    // Merging available mandatory and optional plugin name list\n    const availablePlugins = [...Object.keys(pluginsInventory), ...pluginNamesList];\n    const activePlugins: PluginName[] = [];\n    const failedPlugins: string[] = [];\n\n    pluginsToLoad.forEach(pluginName => {\n      if (availablePlugins.includes(pluginName)) {\n        activePlugins.push(pluginName);\n      } else {\n        failedPlugins.push(pluginName);\n      }\n    });\n\n    if (failedPlugins.length > 0) {\n      this.onError(\n        new Error(\n          `Ignoring loading of unknown plugins: ${failedPlugins.join(\n            ',',\n          )}. Mandatory plugins: ${Object.keys(getMandatoryPluginsMap()).join(\n            ',',\n          )}. Load option plugins: ${state.plugins.pluginsToLoadFromConfig.value.join(',')}`,\n        ),\n      );\n    }\n\n    batch(() => {\n      state.plugins.totalPluginsToLoad.value = pluginsToLoad.length;\n      state.plugins.activePlugins.value = activePlugins;\n      state.plugins.failedPlugins.value = failedPlugins;\n    });\n  }\n\n  /**\n   * Register plugins that are direct imports to PluginEngine\n   */\n  registerLocalPlugins() {\n    Object.values(pluginsInventory).forEach(localPlugin => {\n      if (\n        isFunction(localPlugin) &&\n        state.plugins.activePlugins.value.includes(localPlugin().name)\n      ) {\n        this.register([localPlugin()]);\n      }\n    });\n  }\n\n  /**\n   * Register plugins that are dynamic imports to PluginEngine\n   */\n  registerRemotePlugins() {\n    const remotePluginsList = remotePluginsInventory(\n      state.plugins.activePlugins.value as PluginName[],\n    );\n\n    Promise.all(\n      Object.keys(remotePluginsList).map(async remotePluginKey => {\n        await remotePluginsList[remotePluginKey as PluginName]()\n          .then((remotePluginModule: any) => this.register([remotePluginModule.default()]))\n          .catch(err => {\n            // TODO: add retry here if dynamic import fails\n            state.plugins.failedPlugins.value = [\n              ...state.plugins.failedPlugins.value,\n              remotePluginKey,\n            ];\n            this.onError(err, remotePluginKey);\n          });\n      }),\n    ).catch(err => {\n      this.onError(err);\n    });\n  }\n\n  /**\n   * Extension point invoke that allows multiple plugins to be registered to it with error handling\n   */\n  invokeMultiple<T = any>(extPoint?: string, ...args: any[]): Nullable<T>[] {\n    try {\n      return this.engine.invokeMultiple(extPoint, ...args);\n    } catch (e) {\n      this.onError(e, extPoint);\n      return [];\n    }\n  }\n\n  /**\n   * Extension point invoke that allows a single plugin to be registered to it with error handling\n   */\n  invokeSingle<T = any>(extPoint?: string, ...args: any[]): Nullable<T> {\n    try {\n      return this.engine.invokeSingle(extPoint, ...args);\n    } catch (e) {\n      this.onError(e, extPoint);\n      return null;\n    }\n  }\n\n  /**\n   * Plugin engine register with error handling\n   */\n  register(plugins: ExtensionPlugin[]) {\n    plugins.forEach(plugin => {\n      try {\n        this.engine.register(plugin, state);\n      } catch (e) {\n        state.plugins.failedPlugins.value = [...state.plugins.failedPlugins.value, plugin.name];\n        this.onError(e);\n      }\n    });\n  }\n\n  // TODO: Implement reset API instead\n  unregisterLocalPlugins() {\n    Object.values(pluginsInventory).forEach(localPlugin => {\n      try {\n        this.engine.unregister(localPlugin().name);\n      } catch (e) {\n        this.onError(e);\n      }\n    });\n  }\n\n  /**\n   * Handle errors\n   */\n  onError(error: unknown, customMessage?: string): void {\n    if (this.errorHandler) {\n      this.errorHandler.onError(error, PLUGINS_MANAGER, customMessage);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { PluginsManager };\n", "import { isFunction } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { getMutatedError } from '@rudderstack/analytics-js-common/utilities/errors';\n\n/**\n * Utility to parse XHR JSON response\n */\nconst responseTextToJson = <T = any>(\n  responseText?: string,\n  onError?: (message: Error | unknown) => void,\n): T | undefined => {\n  try {\n    return JSON.parse(responseText || '');\n  } catch (err) {\n    const error = getMutatedError(err, 'Failed to parse response data');\n    if (isFunction(onError)) {\n      onError(error);\n    } else {\n      throw error;\n    }\n  }\n\n  return undefined;\n};\n\nexport { responseTextToJson };\n", "/* eslint-disable prefer-promise-reject-errors */\nimport { mergeDeepRight } from '@rudderstack/analytics-js-common/utilities/object';\nimport { stringifyWithoutCircular } from '@rudderstack/analytics-js-common/utilities/json';\nimport { isNull } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type {\n  IXHRRequestOptions,\n  ResponseDetails,\n} from '@rudderstack/analytics-js-common/types/HttpClient';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { getMutatedError } from '@rudderstack/analytics-js-common/utilities/errors';\nimport { DEFAULT_XHR_TIMEOUT_MS } from '../../../constants/timeouts';\nimport { FAILED_REQUEST_ERR_MSG_PREFIX } from '../../../constants/errors';\nimport {\n  XHR_PAYLOAD_PREP_ERROR,\n  XHR_DELIVERY_ERROR,\n  XHR_REQUEST_ERROR,\n  XHR_SEND_ERROR,\n} from '../../../constants/logMessages';\n\nconst DEFAULT_XHR_REQUEST_OPTIONS: Partial<IXHRRequestOptions> = {\n  headers: {\n    Accept: 'application/json',\n    'Content-Type': 'application/json;charset=UTF-8',\n  },\n  method: 'GET',\n};\n\n/**\n * Utility to create request configuration based on default options\n */\nconst createXhrRequestOptions = (\n  url: string,\n  options?: Partial<IXHRRequestOptions>,\n  basicAuthHeader?: string,\n): IXHRRequestOptions => {\n  const requestOptions: IXHRRequestOptions = mergeDeepRight(\n    DEFAULT_XHR_REQUEST_OPTIONS,\n    options || {},\n  );\n\n  if (basicAuthHeader) {\n    requestOptions.headers = mergeDeepRight(requestOptions.headers, {\n      Authorization: basicAuthHeader,\n    });\n  }\n\n  requestOptions.url = url;\n\n  return requestOptions;\n};\n\n/**\n * Utility implementation of XHR, fetch cannot be used as it requires explicit\n * origin allowed values and not wildcard for CORS requests with credentials and\n * this is not supported by our sourceConfig API\n */\nconst xhrRequest = (\n  options: IXHRRequestOptions,\n  timeout = DEFAULT_XHR_TIMEOUT_MS,\n  logger?: ILogger,\n): Promise<ResponseDetails> =>\n  new Promise((resolve, reject) => {\n    let payload;\n    if (options.sendRawData === true) {\n      payload = options.data;\n    } else {\n      payload = stringifyWithoutCircular(options.data, false, [], logger);\n      if (isNull(payload)) {\n        reject({\n          error: new Error(XHR_PAYLOAD_PREP_ERROR),\n          undefined,\n          options,\n        });\n        // return and don't process further if the payload could not be stringified\n        return;\n      }\n    }\n\n    const xhr = new XMLHttpRequest();\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    const xhrReject = (e?: ProgressEvent) => {\n      reject({\n        error: new Error(\n          XHR_DELIVERY_ERROR(\n            FAILED_REQUEST_ERR_MSG_PREFIX,\n            xhr.status,\n            xhr.statusText,\n            options.url,\n          ),\n        ),\n        xhr,\n        options,\n      });\n    };\n    const xhrError = (e?: ProgressEvent) => {\n      reject({\n        error: new Error(XHR_REQUEST_ERROR(FAILED_REQUEST_ERR_MSG_PREFIX, e, options.url)),\n        xhr,\n        options,\n      });\n    };\n\n    xhr.ontimeout = xhrError;\n    xhr.onerror = xhrError;\n\n    xhr.onload = () => {\n      if (xhr.status >= 200 && xhr.status < 400) {\n        resolve({\n          response: xhr.responseText,\n          xhr,\n          options,\n        });\n      } else {\n        xhrReject();\n      }\n    };\n\n    xhr.open(options.method, options.url);\n    if (options.withCredentials === true) {\n      xhr.withCredentials = true;\n    }\n    // The timeout property may be set only in the time interval between a call to the open method\n    // and the first call to the send method in legacy browsers\n    xhr.timeout = timeout;\n\n    Object.keys(options.headers).forEach(headerName => {\n      if (options.headers[headerName]) {\n        xhr.setRequestHeader(headerName, options.headers[headerName] as string);\n      }\n    });\n\n    try {\n      xhr.send(payload);\n    } catch (err) {\n      reject({\n        error: getMutatedError(err, XHR_SEND_ERROR(FAILED_REQUEST_ERR_MSG_PREFIX, options.url)),\n        xhr,\n        options,\n      });\n    }\n  });\n\nexport { createXhrRequestOptions, xhrRequest, DEFAULT_XHR_REQUEST_OPTIONS };\n", "import { isFunction } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type {\n  IAsyncRequestConfig,\n  IHttpClient,\n  IRequestConfig,\n  ResponseDetails,\n} from '@rudderstack/analytics-js-common/types/HttpClient';\nimport type { IErrorHandler } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { toBase64 } from '@rudderstack/analytics-js-common/utilities/string';\nimport { HTTP_CLIENT } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { defaultErrorHandler } from '../ErrorHandler';\nimport { defaultLogger } from '../Logger';\nimport { responseTextToJson } from './xhr/xhrResponseHandler';\nimport { createXhrRequestOptions, xhrRequest } from './xhr/xhrRequestHandler';\n\n// TODO: should we add any debug level loggers?\n\n/**\n * Service to handle data communication with APIs\n */\nclass HttpClient implements IHttpClient {\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n  basicAuthHeader?: string;\n  hasErrorHandler = false;\n\n  constructor(errorHandler?: IErrorHandler, logger?: ILogger) {\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.hasErrorHandler = Boolean(this.errorHandler);\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Implement requests in a blocking way\n   */\n  async getData<T = any>(\n    config: IRequestConfig,\n  ): Promise<{ data: T | string | undefined; details?: ResponseDetails }> {\n    const { url, options, timeout, isRawResponse } = config;\n\n    try {\n      const data = await xhrRequest(\n        createXhrRequestOptions(url, options, this.basicAuthHeader),\n        timeout,\n        this.logger,\n      );\n      return {\n        data: isRawResponse ? data.response : responseTextToJson<T>(data.response, this.onError),\n        details: data,\n      };\n    } catch (reason) {\n      this.onError((reason as ResponseDetails).error ?? reason);\n      return { data: undefined, details: reason as ResponseDetails };\n    }\n  }\n\n  /**\n   * Implement requests in a non-blocking way\n   */\n  getAsyncData<T = any>(config: IAsyncRequestConfig<T>) {\n    const { callback, url, options, timeout, isRawResponse } = config;\n    const isFireAndForget = !isFunction(callback);\n\n    xhrRequest(createXhrRequestOptions(url, options, this.basicAuthHeader), timeout, this.logger)\n      .then((data: ResponseDetails) => {\n        if (!isFireAndForget) {\n          callback(\n            isRawResponse ? data.response : responseTextToJson<T>(data.response, this.onError),\n            data,\n          );\n        }\n      })\n      .catch((data: ResponseDetails) => {\n        this.onError(data.error ?? data);\n        if (!isFireAndForget) {\n          callback(undefined, data);\n        }\n      });\n  }\n\n  /**\n   * Handle errors\n   */\n  onError(error: unknown) {\n    if (this.hasErrorHandler) {\n      this.errorHandler?.onError(error, HTTP_CLIENT);\n    } else {\n      throw error;\n    }\n  }\n\n  /**\n   * Set basic authentication header (eg writekey)\n   */\n  setAuthHeader(value: string, noBtoa = false) {\n    const authVal = noBtoa ? value : toBase64(`${value}:`);\n    this.basicAuthHeader = `Basic ${authVal}`;\n  }\n\n  /**\n   * Clear basic authentication header\n   */\n  resetAuthHeader() {\n    this.basicAuthHeader = undefined;\n  }\n}\n\nconst defaultHttpClient = new HttpClient(defaultErrorHandler, defaultLogger);\n\nexport { HttpClient, defaultHttpClient };\n", "import type { StorageType } from '../types/Storage';\n\nconst COOKIE_STORAGE: StorageType = 'cookieStorage';\nconst LOCAL_STORAGE: StorageType = 'localStorage';\nconst SESSION_STORAGE: StorageType = 'sessionStorage';\nconst MEMORY_STORAGE: StorageType = 'memoryStorage';\nconst NO_STORAGE: StorageType = 'none';\n\nexport { COOKIE_STORAGE, LOCAL_STORAGE, SESSION_STORAGE, MEMORY_STORAGE, NO_STORAGE };\n", "import type { UserSessionKey } from '@rudderstack/analytics-js-common/types/UserSessionStorage';\n\nconst STORAGE_TEST_COOKIE = 'test_rudder_cookie';\nconst STORAGE_TEST_LOCAL_STORAGE = 'test_rudder_ls';\nconst STORAGE_TEST_SESSION_STORAGE = 'test_rudder_ss';\nconst STORAGE_TEST_TOP_LEVEL_DOMAIN = '__tld__';\nconst STOREJS_IS_INCOGNITO = '_Is_Incognit';\n\nconst CLIENT_DATA_STORE_NAME = 'clientData';\n\nconst CLIENT_DATA_STORE_COOKIE = 'clientDataInCookie';\nconst CLIENT_DATA_STORE_LS = 'clientDataInLocalStorage';\nconst CLIENT_DATA_STORE_MEMORY = 'clientDataInMemory';\nconst CLIENT_DATA_STORE_SESSION = 'clientDataInSessionStorage';\n\nconst USER_SESSION_KEYS: UserSessionKey[] = [\n  'userId',\n  'userTraits',\n  'anonymousId',\n  'groupId',\n  'groupTraits',\n  'initialReferrer',\n  'initialReferringDomain',\n  'sessionInfo',\n  'authToken',\n];\n\nexport {\n  STORAGE_TEST_COOKIE,\n  STORAGE_TEST_LOCAL_STORAGE,\n  STORAGE_TEST_SESSION_STORAGE,\n  STORAGE_TEST_TOP_LEVEL_DOMAIN,\n  STOREJS_IS_INCOGNITO,\n  CLIENT_DATA_STORE_NAME,\n  CLIENT_DATA_STORE_COOKIE,\n  CLIENT_DATA_STORE_LS,\n  CLIENT_DATA_STORE_MEMORY,\n  CLIENT_DATA_STORE_SESSION,\n  USER_SESSION_KEYS,\n};\n", "import {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  MEMORY_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport type {\n  ICookieStorageOptions,\n  IInMemoryStorageOptions,\n  ILocalStorageOptions,\n  ISessionStorageOptions,\n} from '@rudderstack/analytics-js-common/types/Store';\nimport {\n  CLIENT_DATA_STORE_COOKIE,\n  CLIENT_DATA_STORE_LS,\n  CLIENT_DATA_STORE_MEMORY,\n  CLIENT_DATA_STORE_SESSION,\n} from '../../constants/storage';\n\nexport type StoreManagerOptions = {\n  cookieStorageOptions?: Partial<ICookieStorageOptions>;\n  localStorageOptions?: Partial<ILocalStorageOptions>;\n  inMemoryStorageOptions?: Partial<IInMemoryStorageOptions>;\n  sessionStorageOptions?: Partial<ISessionStorageOptions>;\n};\n\nexport const storageClientDataStoreNameMap: Record<string, string> = {\n  [COOKIE_STORAGE]: CLIENT_DATA_STORE_COOKIE,\n  [LOCAL_STORAGE]: CLIENT_DATA_STORE_LS,\n  [MEMORY_STORAGE]: CLIENT_DATA_STORE_MEMORY,\n  [SESSION_STORAGE]: CLIENT_DATA_STORE_SESSION,\n};\n", "import {\n  isUndefined,\n  isNull,\n  isNullOrUndefined,\n  isFunction,\n} from '@rudderstack/analytics-js-common/utilities/checks';\n\nconst isBrowser = (): boolean => !isUndefined(globalThis) && !isUndefined(globalThis.document);\n\nconst isNode = (): boolean =>\n  !isUndefined(process) && !isNull(process.versions) && !isNull(process.versions.node);\n\nconst hasCrypto = (): boolean =>\n  !isNullOrUndefined(globalThis.crypto) && isFunction(globalThis.crypto.getRandomValues);\n\n// eslint-disable-next-line compat/compat -- We are checking for the existence of navigator.userAgentData\nconst hasUAClientHints = (): boolean => !isNullOrUndefined(globalThis.navigator.userAgentData);\n\nconst hasBeacon = (): boolean =>\n  !isNullOrUndefined(globalThis.navigator.sendBeacon) &&\n  isFunction(globalThis.navigator.sendBeacon);\n\nconst isIE11 = (): boolean => Boolean(globalThis.navigator.userAgent.match(/Trident.*rv:11\\./));\n\nexport { isBrowser, isNode, hasCrypto, hasUAClientHints, hasBeacon, isIE11 };\n", "import { isFunction } from '@rudderstack/analytics-js-common/utilities/checks';\n\nconst isDatasetAvailable = (): boolean => {\n  const testElement = globalThis.document.createElement('div');\n  testElement.setAttribute('data-a-b', 'c');\n  return testElement.dataset ? testElement.dataset.aB === 'c' : false;\n};\n\nconst legacyJSEngineRequiredPolyfills: Record<string, () => boolean> = {\n  // Ideally, we should separate the checks for URL and URLSearchParams but\n  // the polyfill service serves them under the same feature name, \"URL\".\n  URL: () => !isFunction(globalThis.URL) || !isFunction(globalThis.URLSearchParams),\n  Promise: () => !isFunction(globalThis.Promise),\n  'Number.isNaN': () => !isFunction(globalThis.Number.isNaN),\n  'Number.isInteger': () => !isFunction(globalThis.Number.isInteger),\n  'Array.from': () => !isFunction(globalThis.Array.from),\n  'Array.prototype.find': () => !isFunction(globalThis.Array.prototype.find),\n  'Array.prototype.includes': () => !isFunction(globalThis.Array.prototype.includes),\n  'String.prototype.endsWith': () => !isFunction(globalThis.String.prototype.endsWith),\n  'String.prototype.startsWith': () => !isFunction(globalThis.String.prototype.startsWith),\n  'String.prototype.includes': () => !isFunction(globalThis.String.prototype.includes),\n  'String.prototype.replaceAll': () => !isFunction(globalThis.String.prototype.replaceAll),\n  'String.fromCodePoint': () => !isFunction(globalThis.String.fromCodePoint),\n  'Object.entries': () => !isFunction(globalThis.Object.entries),\n  'Object.values': () => !isFunction(globalThis.Object.values),\n  'Object.assign': () => !isFunction(globalThis.Object.assign),\n  'Object.fromEntries': () => !isFunction(globalThis.Object.fromEntries),\n  'Element.prototype.dataset': () => !isDatasetAvailable(),\n  // Ideally, we should separate the checks for TextEncoder and TextDecoder but\n  // the polyfill service serves them under the same feature name, \"TextEncoder\".\n  TextEncoder: () => !isFunction(globalThis.TextEncoder) || !isFunction(globalThis.TextDecoder),\n  requestAnimationFrame: () =>\n    !isFunction(globalThis.requestAnimationFrame) || !isFunction(globalThis.cancelAnimationFrame),\n  CustomEvent: () => !isFunction(globalThis.CustomEvent),\n  'navigator.sendBeacon': () => !isFunction(globalThis.navigator.sendBeacon),\n  // Note, the polyfill service serves both ArrayBuffer and Uint8Array under the same feature name, \"ArrayBuffer\".\n  ArrayBuffer: () => !isFunction(globalThis.Uint8Array),\n  Set: () => !isFunction(globalThis.Set),\n};\n\nconst isLegacyJSEngine = (): boolean => {\n  const requiredCapabilitiesList = Object.keys(legacyJSEngineRequiredPolyfills);\n  let needsPolyfill = false;\n\n  /* eslint-disable-next-line unicorn/no-for-loop */\n  for (let i = 0; i < requiredCapabilitiesList.length; i++) {\n    const isCapabilityMissing =\n      legacyJSEngineRequiredPolyfills[requiredCapabilitiesList[i] as string];\n\n    if (isFunction(isCapabilityMissing) && isCapabilityMissing()) {\n      needsPolyfill = true;\n      break;\n    }\n  }\n\n  return needsPolyfill;\n};\n\nexport { isDatasetAvailable, legacyJSEngineRequiredPolyfills, isLegacyJSEngine };\n", "import type { ScreenInfo } from '@rudderstack/analytics-js-common/types/EventContext';\n\nconst getScreenDetails = (): ScreenInfo => {\n  let screenDetails: ScreenInfo = {\n    density: 0,\n    width: 0,\n    height: 0,\n    innerWidth: 0,\n    innerHeight: 0,\n  };\n\n  screenDetails = {\n    width: globalThis.screen.width,\n    height: globalThis.screen.height,\n    density: globalThis.devicePixelRatio,\n    innerWidth: globalThis.innerWidth,\n    innerHeight: globalThis.innerHeight,\n  };\n\n  return screenDetails;\n};\n\nexport { getScreenDetails };\n", "import { CAPABILITIES_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport type { IStorage } from '@rudderstack/analytics-js-common/types/Store';\nimport type { StorageType } from '@rudderstack/analytics-js-common/types/Storage';\nimport {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  MEMORY_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport {\n  STORAGE_TEST_COOKIE,\n  STORAGE_TEST_LOCAL_STORAGE,\n  STORAGE_TEST_SESSION_STORAGE,\n} from '../../../constants/storage';\nimport { STORAGE_UNAVAILABILITY_ERROR_PREFIX } from '../../../constants/logMessages';\n\nconst isStorageQuotaExceeded = (e: DOMException | any): boolean => {\n  const matchingNames = ['QuotaExceededError', 'NS_ERROR_DOM_QUOTA_REACHED']; // [everything except Firefox, Firefox]\n  const matchingCodes = [22, 1014]; // [everything except Firefox, Firefox]\n  const isQuotaExceededError = matchingNames.includes(e.name) || matchingCodes.includes(e.code);\n\n  return e instanceof DOMException && isQuotaExceededError;\n};\n\n// TODO: also check for SecurityErrors\n//  https://developer.mozilla.org/en-US/docs/Web/API/Window/localStorage#exceptions\nconst isStorageAvailable = (\n  type: StorageType = LOCAL_STORAGE,\n  storageInstance?: IStorage,\n  logger?: ILogger,\n) => {\n  let storage;\n  let testData;\n\n  try {\n    switch (type) {\n      case MEMORY_STORAGE:\n        return true;\n      case COOKIE_STORAGE:\n        storage = storageInstance;\n        testData = STORAGE_TEST_COOKIE;\n        break;\n      case LOCAL_STORAGE:\n        storage = storageInstance ?? globalThis.localStorage;\n        testData = STORAGE_TEST_LOCAL_STORAGE; // was STORAGE_TEST_LOCAL_STORAGE in ours and generateUUID() in segment retry one\n        break;\n      case SESSION_STORAGE:\n        storage = storageInstance ?? globalThis.sessionStorage;\n        testData = STORAGE_TEST_SESSION_STORAGE;\n        break;\n      default:\n        return false;\n    }\n\n    if (!storage) {\n      return false;\n    }\n\n    storage.setItem(testData, 'true');\n    if (storage.getItem(testData)) {\n      storage.removeItem(testData);\n      return true;\n    }\n    return false;\n  } catch (err) {\n    const msgPrefix = STORAGE_UNAVAILABILITY_ERROR_PREFIX(CAPABILITIES_MANAGER, type);\n    let reason = 'unavailable';\n    if (isStorageQuotaExceeded(err)) {\n      reason = 'full';\n    }\n    logger?.warn(`${msgPrefix}${reason}.`, err);\n    return false;\n  }\n};\n\nexport { isStorageQuotaExceeded, isStorageAvailable };\n", "import { isNull } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type { CookieOptions } from '@rudderstack/analytics-js-common/types/Storage';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { COOKIE_DATA_ENCODING_ERROR } from '../../../constants/logMessages';\n\n/**\n * Encode.\n */\nconst encode = (value: any, logger?: ILogger): string | undefined => {\n  try {\n    return encodeURIComponent(value);\n  } catch (err) {\n    logger?.error(COOKIE_DATA_ENCODING_ERROR, err);\n    return undefined;\n  }\n};\n\n/**\n * Decode\n */\nconst decode = (value: string): string | undefined => {\n  try {\n    return decodeURIComponent(value);\n  } catch (err) {\n    // Do nothing as non-RS SDK cookies may not be URI encoded\n    return undefined;\n  }\n};\n\n/**\n * Parse cookie `str`\n */\nconst parse = (str: string): Record<string, string | undefined> => {\n  const obj: Record<string, any> = {};\n  const pairs = str.split(/\\s*;\\s*/);\n  let pair;\n\n  if (!pairs[0]) {\n    return obj;\n  }\n\n  // TODO: Decode only the cookies that are needed by the SDK\n  pairs.forEach(pairItem => {\n    pair = pairItem.split('=');\n    const keyName = pair[0] ? decode(pair[0]) : undefined;\n\n    if (keyName) {\n      obj[keyName] = pair[1] ? decode(pair[1]) : undefined;\n    }\n  });\n\n  return obj;\n};\n\n/**\n * Set cookie `name` to `value`\n */\nconst set = (\n  name?: string,\n  value?: Nullable<string | number>,\n  optionsConfig?: CookieOptions,\n  logger?: ILogger,\n) => {\n  const options: CookieOptions = { ...optionsConfig } || {};\n  let cookieString = `${encode(name, logger)}=${encode(value, logger)}`;\n\n  if (isNull(value)) {\n    options.maxage = -1;\n  }\n\n  if (options.maxage) {\n    options.expires = new Date(+new Date() + options.maxage);\n  }\n\n  if (options.path) {\n    cookieString += `; path=${options.path}`;\n  }\n\n  if (options.domain) {\n    cookieString += `; domain=${options.domain}`;\n  }\n\n  if (options.expires) {\n    cookieString += `; expires=${options.expires.toUTCString()}`;\n  }\n\n  if (options.samesite) {\n    cookieString += `; samesite=${options.samesite}`;\n  }\n\n  if (options.secure) {\n    cookieString += `; secure`;\n  }\n\n  globalThis.document.cookie = cookieString;\n};\n\n/**\n * Return all cookies\n */\nconst all = (): Record<string, string | undefined> => {\n  const cookieStringValue = globalThis.document.cookie;\n  return parse(cookieStringValue);\n};\n\n/**\n * Get cookie `name`\n */\n\nconst get = (name: string): string => (all() as any)[name];\n\n/**\n * Set or get cookie `name` with `value` and `options` object\n */\n// eslint-disable-next-line func-names\nconst cookie = function (\n  name?: string,\n  value?: Nullable<string | number>,\n  options?: CookieOptions,\n  logger?: ILogger,\n): void | any {\n  switch (arguments.length) {\n    case 4:\n    case 3:\n    case 2:\n      return set(name, value, options, logger);\n    case 1:\n      if (name) {\n        return get(name);\n      }\n      return all();\n    default:\n      return all();\n  }\n};\n\nexport { cookie };\n", "import { STORAGE_TEST_TOP_LEVEL_DOMAIN } from '../../../constants/storage';\nimport { cookie } from '../component-cookie';\n\nconst legacyGetHostname = (href: string): string => {\n  const l = document.createElement('a');\n  l.href = href;\n  return l.hostname;\n};\n\n/**\n * Levels returns all levels of the given url\n *\n * The method returns an empty array when the hostname is an ip.\n */\nconst levelsFunc = (url: string): string[] => {\n  // This is called before the polyfills load thus new URL cannot be used\n  const host =\n    typeof globalThis.URL !== 'function' ? legacyGetHostname(url) : new URL(url).hostname;\n  const parts = host?.split('.') ?? [];\n  const last = parts[parts.length - 1];\n  const levels: string[] = [];\n\n  // Ip address.\n  if (parts.length === 4 && last && last === parseInt(last, 10).toString()) {\n    return levels;\n  }\n\n  // Localhost.\n  if (parts.length <= 1) {\n    // Fix to support localhost\n    if (parts[0] && parts[0].indexOf('localhost') !== -1) {\n      return ['localhost'];\n    }\n    return levels;\n  }\n\n  // Create levels.\n  for (let i = parts.length - 2; i >= 0; i -= 1) {\n    levels.push(parts.slice(i).join('.'));\n  }\n\n  return levels;\n};\n\n/**\n * Get the top domain.\n *\n * The function constructs the levels of domain and attempts to set a global\n * cookie on each one when it succeeds it returns the top level domain.\n *\n * The method returns an empty string when the hostname is an ip.\n */\nconst domain = (url: string): string => {\n  const levels = levelsFunc(url);\n\n  // Lookup the real top level one.\n  // eslint-disable-next-line unicorn/no-for-loop\n  for (let i = 0; i < levels.length; i += 1) {\n    const domain = levels[i] as string;\n    const cname = STORAGE_TEST_TOP_LEVEL_DOMAIN;\n    const opts = {\n      domain: `${domain.indexOf('localhost') !== -1 ? '' : '.'}${domain}`,\n    };\n\n    // Set cookie on domain\n    cookie(cname, 1, opts);\n\n    // If successful\n    if (cookie(cname)) {\n      // Remove cookie from domain\n      cookie(cname, null, opts);\n      return domain;\n    }\n  }\n\n  return '';\n};\n\n/*\n * Exports.\n */\n\nexport { domain };\n", "import type {\n  ICookieStorageOptions,\n  IInMemoryStorageOptions,\n  ILocalStorageOptions,\n  ISessionStorageOptions,\n} from '@rudderstack/analytics-js-common/types/Store';\nimport { DEFAULT_COOKIE_MAX_AGE_MS } from '../../../constants/timeouts';\nimport { domain } from '../top-domain';\n\nconst getDefaultCookieOptions = (): ICookieStorageOptions => {\n  const topDomain = `.${domain(globalThis.location.href)}`;\n\n  return {\n    maxage: DEFAULT_COOKIE_MAX_AGE_MS,\n    path: '/',\n    domain: !topDomain || topDomain === '.' ? undefined : topDomain,\n    samesite: 'Lax',\n    enabled: true,\n  };\n};\n\nconst getDefaultLocalStorageOptions = (): ILocalStorageOptions => ({\n  enabled: true,\n});\n\nconst getDefaultSessionStorageOptions = (): ISessionStorageOptions => ({\n  enabled: true,\n});\n\nconst getDefaultInMemoryStorageOptions = (): IInMemoryStorageOptions => ({\n  enabled: true,\n});\n\nexport {\n  getDefaultCookieOptions,\n  getDefaultLocalStorageOptions,\n  getDefaultInMemoryStorageOptions,\n  getDefaultSessionStorageOptions,\n};\n", "import { isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { ICookieStorageOptions, IStorage } from '@rudderstack/analytics-js-common/types/Store';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { COOKIE_STORAGE } from '@rudderstack/analytics-js-common/constants/storages';\nimport { mergeDeepRight } from '@rudderstack/analytics-js-common/utilities/object';\nimport { isStorageAvailable } from '../../../components/capabilitiesManager/detection';\nimport { cookie } from '../component-cookie';\nimport { getDefaultCookieOptions } from './defaultOptions';\n\n/**\n * A storage utility to persist values in cookies via Storage interface\n */\nclass CookieStorage implements IStorage {\n  static globalSingleton: Nullable<CookieStorage> = null;\n  logger?: ILogger;\n  options?: ICookieStorageOptions;\n  isSupportAvailable = true;\n  isEnabled = true;\n  length = 0;\n\n  constructor(options: Partial<ICookieStorageOptions> = {}, logger?: ILogger) {\n    if (CookieStorage.globalSingleton) {\n      // eslint-disable-next-line no-constructor-return\n      return CookieStorage.globalSingleton;\n    }\n\n    this.options = getDefaultCookieOptions();\n    this.logger = logger;\n    this.configure(options);\n\n    CookieStorage.globalSingleton = this;\n  }\n\n  configure(options: Partial<ICookieStorageOptions>): ICookieStorageOptions {\n    this.options = mergeDeepRight(this.options ?? {}, options);\n    if (options.sameDomainCookiesOnly) {\n      delete this.options.domain;\n    }\n    this.isSupportAvailable = isStorageAvailable(COOKIE_STORAGE, this, this.logger);\n    this.isEnabled = Boolean(this.options.enabled && this.isSupportAvailable);\n    return this.options;\n  }\n\n  setItem(key: string, value: Nullable<string>): boolean {\n    cookie(key, value, this.options, this.logger);\n    this.length = Object.keys(cookie()).length;\n    return true;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getItem(key: string): Nullable<string> {\n    const value = cookie(key);\n    return isUndefined(value) ? null : value;\n  }\n\n  removeItem(key: string): boolean {\n    const result = this.setItem(key, null);\n    this.length = Object.keys(cookie()).length;\n    return result;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  clear() {\n    // Not implemented\n    // getting a list of all cookie storage keys and remove all values\n    // sounds risky to do as it will take on all top domain cookies\n    // better to explicitly clear specific ones if needed\n  }\n\n  key(index: number): Nullable<string> {\n    const curKeys = this.keys();\n    return curKeys[index] ?? null;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  keys(): string[] {\n    return Object.keys(cookie());\n  }\n}\n\nexport { CookieStorage };\n", "import type {\n  IInMemoryStorageOptions,\n  IStorage,\n} from '@rudderstack/analytics-js-common/types/Store';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { mergeDeepRight } from '@rudderstack/analytics-js-common/utilities/object';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { defaultLogger } from '../../Logger';\nimport { getDefaultInMemoryStorageOptions } from './defaultOptions';\n\n/**\n * A storage utility to retain values in memory via Storage interface\n */\nclass InMemoryStorage implements IStorage {\n  logger?: ILogger;\n  options: IInMemoryStorageOptions;\n  isEnabled = true;\n  length = 0;\n  data: Record<string, any> = {};\n\n  constructor(options?: IInMemoryStorageOptions, logger?: ILogger) {\n    this.options = getDefaultInMemoryStorageOptions();\n    this.logger = logger;\n    this.configure(options ?? {});\n  }\n\n  configure(options: Partial<IInMemoryStorageOptions>): IInMemoryStorageOptions {\n    this.options = mergeDeepRight(this.options, options);\n    this.isEnabled = Boolean(this.options.enabled);\n    return this.options;\n  }\n\n  setItem(key: string, value: any): any {\n    this.data[key] = value;\n    this.length = Object.keys(this.data).length;\n    return value;\n  }\n\n  getItem(key: string): any {\n    if (key in this.data) {\n      return this.data[key];\n    }\n    return null;\n  }\n\n  removeItem(key: string) {\n    if (key in this.data) {\n      delete this.data[key];\n    }\n    this.length = Object.keys(this.data).length;\n    return null;\n  }\n\n  clear() {\n    this.data = {};\n    this.length = 0;\n  }\n\n  key(index: number): Nullable<string> {\n    const curKeys = this.keys();\n    return curKeys[index] ?? null;\n  }\n\n  keys(): string[] {\n    return Object.keys(this.data);\n  }\n}\n\nconst defaultInMemoryStorage = new InMemoryStorage({}, defaultLogger);\n\nexport { InMemoryStorage, defaultInMemoryStorage };\n", "/**!\n * storejs v2.1.0\n * Local storage localstorage package provides a simple API\n * \n * Copyright (c) 2024 kenny wang <<EMAIL>>\n * https://jaywcjlove.github.io/store.js/\n * \n * Licensed under the MIT license.\n */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.store = factory());\n})(this, (function () { 'use strict';\n\n  function isJSON(obj) {\n    obj = JSON.stringify(obj);\n    if (!/^\\{[\\s\\S]*\\}$/.test(obj)) {\n      return false;\n    }\n    return true;\n  }\n  function stringify(val) {\n    return val === undefined || typeof val === \"function\" ? val + '' : JSON.stringify(val);\n  }\n  function deserialize(value) {\n    if (typeof value !== 'string') {\n      return undefined;\n    }\n    try {\n      return JSON.parse(value);\n    } catch (e) {\n      return value;\n    }\n  }\n  function isFunction(value) {\n    return {}.toString.call(value) === \"[object Function]\";\n  }\n  function isArray(value) {\n    return Object.prototype.toString.call(value) === \"[object Array]\";\n  }\n  // https://github.com/jaywcjlove/store.js/pull/8\n  // Error: QuotaExceededError\n  function dealIncognito(storage) {\n    var _KEY = '_Is_Incognit',\n      _VALUE = 'yes';\n    try {\n      // NOTE: set default storage when not passed in\n      if (!storage) {\n        storage = window.localStorage;\n      }\n      storage.setItem(_KEY, _VALUE);\n      storage.removeItem(_KEY);\n    } catch (e) {\n      var inMemoryStorage = {};\n      inMemoryStorage._data = {};\n      inMemoryStorage.setItem = function (id, val) {\n        return inMemoryStorage._data[id] = String(val);\n      };\n      inMemoryStorage.getItem = function (id) {\n        return inMemoryStorage._data.hasOwnProperty(id) ? inMemoryStorage._data[id] : undefined;\n      };\n      inMemoryStorage.removeItem = function (id) {\n        return delete inMemoryStorage._data[id];\n      };\n      inMemoryStorage.clear = function () {\n        return inMemoryStorage._data = {};\n      };\n      storage = inMemoryStorage;\n    } finally {\n      if (storage.getItem(_KEY) === _VALUE) storage.removeItem(_KEY);\n    }\n    return storage;\n  }\n\n  // deal QuotaExceededError if user use incognito mode in browser\n  var storage = dealIncognito();\n  function Store() {\n    if (!(this instanceof Store)) {\n      return new Store();\n    }\n  }\n  Store.prototype = {\n    set: function set(key, val) {\n      if (key && !isJSON(key)) {\n        storage.setItem(key, stringify(val));\n      } else if (isJSON(key)) {\n        for (var a in key) this.set(a, key[a]);\n      }\n      return this;\n    },\n    get: function get(key) {\n      // Return all entries if no key\n      if (key === undefined) {\n        var ret = {};\n        this.forEach(function (key, val) {\n          return ret[key] = val;\n        });\n        return ret;\n      }\n      if (key.charAt(0) === '?') {\n        return this.has(key.substr(1));\n      }\n      var args = arguments;\n      if (args.length > 1) {\n        var dt = {};\n        for (var i = 0, len = args.length; i < len; i++) {\n          var value = deserialize(storage.getItem(args[i]));\n          if (this.has(args[i])) {\n            dt[args[i]] = value;\n          }\n        }\n        return dt;\n      }\n      return deserialize(storage.getItem(key));\n    },\n    clear: function clear() {\n      storage.clear();\n      return this;\n    },\n    remove: function remove(key) {\n      var val = this.get(key);\n      storage.removeItem(key);\n      return val;\n    },\n    has: function has(key) {\n      return {}.hasOwnProperty.call(this.get(), key);\n    },\n    keys: function keys() {\n      var d = [];\n      this.forEach(function (k) {\n        d.push(k);\n      });\n      return d;\n    },\n    forEach: function forEach(callback) {\n      for (var i = 0, len = storage.length; i < len; i++) {\n        var key = storage.key(i);\n        callback(key, this.get(key));\n      }\n      return this;\n    },\n    search: function search(str) {\n      var arr = this.keys(),\n        dt = {};\n      for (var i = 0, len = arr.length; i < len; i++) {\n        if (arr[i].indexOf(str) > -1) dt[arr[i]] = this.get(arr[i]);\n      }\n      return dt;\n    },\n    len: function len() {\n      return storage.length;\n    }\n  };\n  var _Store = null;\n  function store(key, data) {\n    var argm = arguments;\n    var dt = null;\n    if (!_Store) _Store = Store();\n    if (argm.length === 0) return _Store.get();\n    if (argm.length === 1) {\n      if (typeof key === \"string\") return _Store.get(key);\n      if (isJSON(key)) return _Store.set(key);\n    }\n    if (argm.length === 2 && typeof key === \"string\") {\n      if (!data) return _Store.remove(key);\n      if (data && typeof data === \"string\") return _Store.set(key, data);\n      if (data && isFunction(data)) {\n        dt = null;\n        dt = data(key, _Store.get(key));\n        store.set(key, dt);\n      }\n    }\n    if (argm.length === 2 && isArray(key) && isFunction(data)) {\n      for (var i = 0, len = key.length; i < len; i++) {\n        dt = data(key[i], _Store.get(key[i]));\n        store.set(key[i], dt);\n      }\n    }\n    return store;\n  }\n  for (var a in Store.prototype) store[a] = Store.prototype[a];\n\n  return store;\n\n}));\n", "import store from 'storejs';\nimport type { ILocalStorageOptions, IStorage } from '@rudderstack/analytics-js-common/types/Store';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { mergeDeepRight } from '@rudderstack/analytics-js-common/utilities/object';\nimport { isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { LOCAL_STORAGE } from '@rudderstack/analytics-js-common/constants/storages';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { isStorageAvailable } from '../../../components/capabilitiesManager/detection';\nimport { defaultLogger } from '../../Logger';\nimport { getDefaultLocalStorageOptions } from './defaultOptions';\n\n// TODO: can we remove the storejs dependency to save bundle size?\n//  check if the get, set overloads and search methods are used at all\n//  if we do, ensure we provide types to support overloads as per storejs docs\n//  https://www.npmjs.com/package/storejs\n/**\n * A storage utility to persist values in localstorage via Storage interface\n */\nclass LocalStorage implements IStorage {\n  logger?: ILogger;\n  options: ILocalStorageOptions;\n  isSupportAvailable = true;\n  isEnabled = true;\n  length = 0;\n\n  constructor(options: ILocalStorageOptions = {}, logger?: ILogger) {\n    this.options = getDefaultLocalStorageOptions();\n    this.logger = logger;\n    this.configure(options);\n  }\n\n  configure(options: Partial<ILocalStorageOptions>): ILocalStorageOptions {\n    this.options = mergeDeepRight(this.options, options);\n    this.isSupportAvailable = isStorageAvailable(LOCAL_STORAGE, this, this.logger);\n    this.isEnabled = Boolean(this.options.enabled && this.isSupportAvailable);\n    return this.options;\n  }\n\n  setItem(key: string, value: any) {\n    store.set(key, value);\n    this.length = store.len();\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getItem(key: string): any {\n    const value = store.get(key);\n    return isUndefined(value) ? null : value;\n  }\n\n  removeItem(key: string) {\n    store.remove(key);\n    this.length = store.len();\n  }\n\n  clear() {\n    store.clear();\n    this.length = 0;\n  }\n\n  key(index: number): Nullable<string> {\n    const curKeys = this.keys();\n    return curKeys[index] ?? null;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  keys(): string[] {\n    return store.keys();\n  }\n}\n\nconst defaultLocalStorage = new LocalStorage({}, defaultLogger);\n\nexport { LocalStorage, defaultLocalStorage };\n", "import type {\n  ISessionStorageOptions,\n  IStorage,\n} from '@rudderstack/analytics-js-common/types/Store';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { mergeDeepRight } from '@rudderstack/analytics-js-common/utilities/object';\nimport { isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { SESSION_STORAGE } from '@rudderstack/analytics-js-common/constants/storages';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { isStorageAvailable } from '../../../components/capabilitiesManager/detection';\nimport { defaultLogger } from '../../Logger';\nimport { getDefaultSessionStorageOptions } from './defaultOptions';\n\n/**\n * A storage utility to persist values in SessionStorage via Storage interface\n */\nclass SessionStorage implements IStorage {\n  logger?: ILogger;\n  options: ISessionStorageOptions;\n  isSupportAvailable = true;\n  isEnabled = true;\n  length = 0;\n  store = globalThis.sessionStorage;\n\n  constructor(options: ISessionStorageOptions = {}, logger?: ILogger) {\n    this.options = getDefaultSessionStorageOptions();\n    this.logger = logger;\n    this.configure(options);\n  }\n\n  configure(options: Partial<ISessionStorageOptions>): ISessionStorageOptions {\n    this.options = mergeDeepRight(this.options, options);\n    this.isSupportAvailable = isStorageAvailable(SESSION_STORAGE, this, this.logger);\n    this.isEnabled = Boolean(this.options.enabled && this.isSupportAvailable);\n    return this.options;\n  }\n\n  setItem(key: string, value: any) {\n    this.store.setItem(key, value);\n    this.length = this.store.length;\n  }\n\n  getItem(key: string): any {\n    const value = this.store.getItem(key);\n    return isUndefined(value) ? null : value;\n  }\n\n  removeItem(key: string) {\n    this.store.removeItem(key);\n    this.length = this.store.length;\n  }\n\n  clear() {\n    this.store.clear();\n    this.length = 0;\n  }\n\n  key(index: number): Nullable<string> {\n    return this.store.key(index);\n  }\n\n  keys(): string[] {\n    const keys: string[] = [];\n    for (let i = 0; i < this.store.length; i += 1) {\n      const key = this.store.key(i);\n      if (key !== null) {\n        keys.push(key);\n      }\n    }\n    return keys;\n  }\n}\n\nconst defaultSessionStorage = new SessionStorage({}, defaultLogger);\n\nexport { SessionStorage, defaultSessionStorage };\n", "import type {\n  ICookieStorageOptions,\n  IInMemoryStorageOptions,\n  ILocalStorageOptions,\n  ISessionStorageOptions,\n  IStorage,\n} from '@rudderstack/analytics-js-common/types/Store';\nimport {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  MEMORY_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport type { StorageType } from '@rudderstack/analytics-js-common/types/Storage';\nimport { state } from '@rudderstack/analytics-js/state';\nimport { defaultLogger } from '../../Logger';\nimport { CookieStorage } from './CookieStorage';\nimport { defaultInMemoryStorage } from './InMemoryStorage';\nimport { defaultLocalStorage } from './LocalStorage';\nimport { defaultSessionStorage } from './sessionStorage';\n\n// TODO: create session storage client (similar to localstorage if needed)\n\n/**\n * A utility to retrieve the storage singleton instance by type\n */\nconst getStorageEngine = (type?: StorageType): IStorage => {\n  switch (type) {\n    case LOCAL_STORAGE:\n      return defaultLocalStorage;\n    case SESSION_STORAGE:\n      return defaultSessionStorage;\n    case MEMORY_STORAGE:\n      return defaultInMemoryStorage;\n    case COOKIE_STORAGE:\n      return new CookieStorage({}, defaultLogger);\n    default:\n      return defaultInMemoryStorage;\n  }\n};\n\n/**\n * Configure cookie storage singleton\n */\nconst configureCookieStorageEngine = (options: Partial<ICookieStorageOptions>) => {\n  const cookieStorageOptions = new CookieStorage({}, defaultLogger).configure(options);\n  state.storage.cookie.value = {\n    maxage: cookieStorageOptions.maxage,\n    path: cookieStorageOptions.path,\n    domain: cookieStorageOptions.domain,\n    samesite: cookieStorageOptions.samesite,\n    expires: cookieStorageOptions.expires,\n    secure: cookieStorageOptions.secure,\n  };\n};\n\n/**\n * Configure local storage singleton\n */\nconst configureLocalStorageEngine = (options: Partial<ILocalStorageOptions>) => {\n  defaultLocalStorage.configure(options);\n};\n\n/**\n * Configure in memory storage singleton\n */\nconst configureInMemoryStorageEngine = (options: Partial<IInMemoryStorageOptions>) => {\n  defaultInMemoryStorage.configure(options);\n};\n\n/**\n * Configure session storage singleton\n */\nconst configureSessionStorageEngine = (options: Partial<ISessionStorageOptions>) => {\n  defaultSessionStorage.configure(options);\n};\n\n/**\n * Configure all storage singleton instances\n */\nconst configureStorageEngines = (\n  cookieStorageOptions: Partial<ICookieStorageOptions> = {},\n  localStorageOptions: Partial<ILocalStorageOptions> = {},\n  inMemoryStorageOptions: Partial<IInMemoryStorageOptions> = {},\n  sessionStorageOptions: Partial<ISessionStorageOptions> = {},\n) => {\n  configureCookieStorageEngine(cookieStorageOptions);\n  configureLocalStorageEngine(localStorageOptions);\n  configureInMemoryStorageEngine(inMemoryStorageOptions);\n  configureSessionStorageEngine(sessionStorageOptions);\n};\n\nexport {\n  getStorageEngine,\n  configureCookieStorageEngine,\n  configureLocalStorageEngine,\n  configureInMemoryStorageEngine,\n  configureSessionStorageEngine,\n  configureStorageEngines,\n};\n", "import { trim } from '@rudderstack/analytics-js-common/utilities/string';\nimport { isNullOrUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { stringifyWithoutCircular } from '@rudderstack/analytics-js-common/utilities/json';\nimport type { IStorage, IStore, IStoreConfig } from '@rudderstack/analytics-js-common/types/Store';\nimport type { IErrorHandler } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IPluginsManager } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { LOCAL_STORAGE, MEMORY_STORAGE } from '@rudderstack/analytics-js-common/constants/storages';\nimport { getMutatedError } from '@rudderstack/analytics-js-common/utilities/errors';\nimport { defaultLogger } from '../Logger';\nimport { defaultErrorHandler } from '../ErrorHandler';\nimport { isStorageQuotaExceeded } from '../../components/capabilitiesManager/detection';\nimport {\n  STORAGE_QUOTA_EXCEEDED_WARNING,\n  STORE_DATA_FETCH_ERROR,\n  STORE_DATA_SAVE_ERROR,\n} from '../../constants/logMessages';\nimport { getStorageEngine } from './storages/storageEngine';\n\n/**\n * Store Implementation with dedicated storage\n */\nclass Store implements IStore {\n  id: string;\n  name: string;\n  isEncrypted: boolean;\n  validKeys: Record<string, string>;\n  engine: IStorage;\n  originalEngine: IStorage;\n  noKeyValidation?: boolean;\n  noCompoundKey?: boolean;\n  errorHandler?: IErrorHandler;\n  hasErrorHandler = false;\n  logger?: ILogger;\n  pluginsManager?: IPluginsManager;\n\n  constructor(config: IStoreConfig, engine?: IStorage, pluginsManager?: IPluginsManager) {\n    this.id = config.id;\n    this.name = config.name;\n    this.isEncrypted = config.isEncrypted ?? false;\n    this.validKeys = config.validKeys ?? {};\n    this.engine = engine ?? getStorageEngine(LOCAL_STORAGE);\n    this.noKeyValidation = Object.keys(this.validKeys).length === 0;\n    this.noCompoundKey = config.noCompoundKey;\n    this.originalEngine = this.engine;\n    this.errorHandler = config.errorHandler ?? defaultErrorHandler;\n    this.hasErrorHandler = Boolean(this.errorHandler);\n    this.logger = config.logger ?? defaultLogger;\n    this.pluginsManager = pluginsManager;\n  }\n\n  /**\n   * Ensure the key is valid and with correct format\n   */\n  createValidKey(key: string): string | undefined {\n    const { name, id, validKeys, noKeyValidation, noCompoundKey } = this;\n\n    if (noKeyValidation) {\n      return noCompoundKey ? key : [name, id, key].join('.');\n    }\n\n    // validate and return undefined if invalid key\n    let compoundKey;\n    Object.values(validKeys).forEach(validKeyName => {\n      if (validKeyName === key) {\n        compoundKey = noCompoundKey ? key : [name, id, key].join('.');\n      }\n    });\n\n    return compoundKey;\n  }\n\n  /**\n   * Switch to inMemoryEngine, bringing any existing data with.\n   */\n  swapQueueStoreToInMemoryEngine() {\n    const { name, id, validKeys, noCompoundKey } = this;\n    const inMemoryStorage = getStorageEngine(MEMORY_STORAGE);\n\n    // grab existing data, but only for this page's queue instance, not all\n    // better to keep other queues in localstorage to be flushed later\n    // than to pull them into memory and remove them from durable storage\n    Object.keys(validKeys).forEach(key => {\n      const value = this.get(validKeys[key] as string);\n      const validKey = noCompoundKey ? key : [name, id, key].join('.');\n\n      inMemoryStorage.setItem(validKey, value);\n      // TODO: are we sure we want to drop clientData\n      //  if cookies are not available and localstorage is full?\n      this.remove(key);\n    });\n\n    this.engine = inMemoryStorage;\n  }\n\n  /**\n   * Set value by key.\n   */\n  set(key: string, value: any) {\n    const validKey = this.createValidKey(key);\n\n    if (!validKey) {\n      return;\n    }\n\n    try {\n      // storejs that is used in localstorage engine already stringifies json\n      this.engine.setItem(\n        validKey,\n        this.encrypt(stringifyWithoutCircular(value, false, [], this.logger)),\n      );\n    } catch (err) {\n      if (isStorageQuotaExceeded(err)) {\n        this.logger?.warn(STORAGE_QUOTA_EXCEEDED_WARNING(`Store ${this.id}`));\n        // switch to inMemory engine\n        this.swapQueueStoreToInMemoryEngine();\n        // and save it there\n        this.set(key, value);\n      } else {\n        this.onError(getMutatedError(err, STORE_DATA_SAVE_ERROR(key)));\n      }\n    }\n  }\n\n  /**\n   * Get by Key.\n   */\n  get<T = any>(key: string): Nullable<T> {\n    const validKey = this.createValidKey(key);\n\n    try {\n      if (!validKey) {\n        return null;\n      }\n\n      const str = this.decrypt(this.engine.getItem(validKey));\n\n      if (isNullOrUndefined(str)) {\n        return null;\n      }\n\n      // storejs that is used in localstorage engine already deserializes json strings but swallows errors\n      return JSON.parse(str as string);\n    } catch (err) {\n      this.onError(new Error(`${STORE_DATA_FETCH_ERROR(key)}: ${(err as Error).message}`));\n      return null;\n    }\n  }\n\n  /**\n   * Remove by Key.\n   */\n  remove(key: string) {\n    const validKey = this.createValidKey(key);\n\n    if (validKey) {\n      this.engine.removeItem(validKey);\n    }\n  }\n\n  /**\n   * Get original engine\n   */\n  getOriginalEngine(): IStorage {\n    return this.originalEngine;\n  }\n\n  /**\n   * Decrypt values\n   */\n  decrypt(value?: Nullable<string>): Nullable<string> {\n    if (isNullOrUndefined(value)) {\n      return null;\n    }\n\n    return this.crypto(value as string, 'decrypt');\n  }\n\n  /**\n   * Encrypt value\n   */\n  encrypt(value: Nullable<any>): string {\n    return this.crypto(value, 'encrypt');\n  }\n\n  /**\n   * Extension point to use with encryption plugins\n   */\n  crypto(value: Nullable<any>, mode: 'encrypt' | 'decrypt'): string {\n    const noEncryption =\n      !this.isEncrypted || !value || typeof value !== 'string' || trim(value) === '';\n\n    if (noEncryption) {\n      return value;\n    }\n\n    const extensionPointName = `storage.${mode}`;\n    const formattedValue = this.pluginsManager\n      ? this.pluginsManager.invokeSingle<string>(extensionPointName, value)\n      : value;\n\n    return typeof formattedValue === 'undefined' ? value : formattedValue ?? '';\n  }\n\n  /**\n   * Handle errors\n   */\n  onError(error: unknown) {\n    if (this.hasErrorHandler) {\n      this.errorHandler?.onError(error, `Store ${this.id}`);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { Store };\n", "import type {\n  IStoreConfig,\n  IStoreManager,\n  StoreId,\n} from '@rudderstack/analytics-js-common/types/Store';\nimport type { I<PERSON><PERSON>r<PERSON>and<PERSON> } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IPluginsManager } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport { STORE_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  MEMORY_STORAGE,\n  NO_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport {\n  mergeDeepRight,\n  removeUndefinedValues,\n} from '@rudderstack/analytics-js-common/utilities/object';\nimport {\n  DEFAULT_STORAGE_TYPE,\n  type StorageType,\n} from '@rudderstack/analytics-js-common/types/Storage';\nimport type { UserSessionKey } from '@rudderstack/analytics-js-common/types/UserSessionStorage';\nimport { batch } from '@preact/signals-core';\nimport { isDefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { USER_SESSION_KEYS } from '@rudderstack/analytics-js/constants/storage';\nimport { USER_SESSION_STORAGE_KEYS } from '../../components/userSessionManager/constants';\nimport { STORAGE_UNAVAILABLE_WARNING } from '../../constants/logMessages';\nimport { type StoreManagerOptions, storageClientDataStoreNameMap } from './types';\nimport { state } from '../../state';\nimport { configureStorageEngines, getStorageEngine } from './storages/storageEngine';\nimport { Store } from './Store';\nimport { getStorageTypeFromPreConsentIfApplicable } from './utils';\n\n/**\n * A service to manage stores & available storage client configurations\n */\nclass StoreManager implements IStoreManager {\n  stores: Record<StoreId, Store> = {};\n  isInitialized = false;\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n  pluginsManager?: IPluginsManager;\n  hasErrorHandler = false;\n\n  constructor(pluginsManager?: IPluginsManager, errorHandler?: IErrorHandler, logger?: ILogger) {\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.hasErrorHandler = Boolean(this.errorHandler);\n    this.pluginsManager = pluginsManager;\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Configure available storage client instances\n   */\n  init() {\n    if (this.isInitialized) {\n      return;\n    }\n\n    const loadOptions = state.loadOptions.value;\n    const config: StoreManagerOptions = {\n      cookieStorageOptions: {\n        samesite: loadOptions.sameSiteCookie,\n        secure: loadOptions.secureCookie,\n        domain: loadOptions.setCookieDomain,\n        sameDomainCookiesOnly: loadOptions.sameDomainCookiesOnly,\n        enabled: true,\n      },\n      localStorageOptions: { enabled: true },\n      inMemoryStorageOptions: { enabled: true },\n      sessionStorageOptions: { enabled: true },\n    };\n\n    configureStorageEngines(\n      removeUndefinedValues(\n        mergeDeepRight(config.cookieStorageOptions ?? {}, state.storage.cookie?.value ?? {}),\n      ),\n      removeUndefinedValues(config.localStorageOptions),\n      removeUndefinedValues(config.inMemoryStorageOptions),\n      removeUndefinedValues(config.sessionStorageOptions),\n    );\n\n    this.initClientDataStores();\n    this.isInitialized = true;\n  }\n\n  /**\n   * Create store to persist data used by the SDK like session, used details etc\n   */\n  initClientDataStores() {\n    this.initializeStorageState();\n\n    // TODO: fill in extra config values and bring them in from StoreManagerOptions if needed\n    // TODO: should we pass the keys for all in order to validate or leave free as v1.1?\n\n    // Initializing all the enabled store because previous user data might be in different storage\n    // that needs auto migration\n    const storageTypes = [MEMORY_STORAGE, LOCAL_STORAGE, COOKIE_STORAGE, SESSION_STORAGE];\n\n    storageTypes.forEach(storageType => {\n      if (getStorageEngine(storageType)?.isEnabled) {\n        this.setStore({\n          id: storageClientDataStoreNameMap[storageType] as string,\n          name: storageClientDataStoreNameMap[storageType] as string,\n          isEncrypted: true,\n          noCompoundKey: true,\n          type: storageType,\n        });\n      }\n    });\n  }\n\n  initializeStorageState() {\n    let globalStorageType = state.storage.type.value;\n    let entriesOptions = state.loadOptions.value.storage?.entries;\n\n    // Use the storage options from post consent if anything is defined\n    const postConsentStorageOpts = state.consents.postConsent.value.storage;\n    if (isDefined(postConsentStorageOpts?.type) || isDefined(postConsentStorageOpts?.entries)) {\n      globalStorageType = postConsentStorageOpts?.type;\n      entriesOptions = postConsentStorageOpts?.entries;\n    }\n\n    let trulyAnonymousTracking = true;\n    let storageEntries = {};\n    USER_SESSION_KEYS.forEach(sessionKey => {\n      const key = sessionKey;\n      const storageKey = sessionKey;\n      const configuredStorageType = entriesOptions?.[key]?.type;\n\n      const preConsentStorageType = getStorageTypeFromPreConsentIfApplicable(state, sessionKey);\n\n      // Storage type precedence order: pre-consent strategy > entry type > global type > default\n      const storageType =\n        preConsentStorageType ?? configuredStorageType ?? globalStorageType ?? DEFAULT_STORAGE_TYPE;\n\n      const finalStorageType = this.getResolvedStorageTypeForEntry(storageType, sessionKey);\n\n      if (finalStorageType !== NO_STORAGE) {\n        trulyAnonymousTracking = false;\n      }\n\n      storageEntries = {\n        ...storageEntries,\n        [sessionKey]: {\n          type: finalStorageType,\n          key: USER_SESSION_STORAGE_KEYS[storageKey],\n        },\n      };\n    });\n\n    batch(() => {\n      state.storage.type.value = globalStorageType;\n      state.storage.entries.value = storageEntries;\n      state.storage.trulyAnonymousTracking.value = trulyAnonymousTracking;\n    });\n  }\n\n  private getResolvedStorageTypeForEntry(storageType: StorageType, sessionKey: UserSessionKey) {\n    let finalStorageType = storageType;\n    switch (storageType) {\n      case LOCAL_STORAGE:\n        if (!getStorageEngine(LOCAL_STORAGE)?.isEnabled) {\n          finalStorageType = MEMORY_STORAGE;\n        }\n        break;\n      case SESSION_STORAGE:\n        if (!getStorageEngine(SESSION_STORAGE)?.isEnabled) {\n          finalStorageType = MEMORY_STORAGE;\n        }\n        break;\n      case MEMORY_STORAGE:\n      case NO_STORAGE:\n        break;\n      case COOKIE_STORAGE:\n      default:\n        // First try setting the storage to cookie else to local storage\n        if (getStorageEngine(COOKIE_STORAGE)?.isEnabled) {\n          finalStorageType = COOKIE_STORAGE;\n        } else if (getStorageEngine(LOCAL_STORAGE)?.isEnabled) {\n          finalStorageType = LOCAL_STORAGE;\n        } else if (getStorageEngine(SESSION_STORAGE)?.isEnabled) {\n          finalStorageType = SESSION_STORAGE;\n        } else {\n          finalStorageType = MEMORY_STORAGE;\n        }\n        break;\n    }\n\n    if (finalStorageType !== storageType) {\n      this.logger?.warn(\n        STORAGE_UNAVAILABLE_WARNING(STORE_MANAGER, sessionKey, storageType, finalStorageType),\n      );\n    }\n\n    return finalStorageType;\n  }\n\n  /**\n   * Create a new store\n   */\n  setStore(storeConfig: IStoreConfig): Store {\n    const storageEngine = getStorageEngine(storeConfig.type);\n    this.stores[storeConfig.id] = new Store(storeConfig, storageEngine, this.pluginsManager);\n    return this.stores[storeConfig.id] as Store;\n  }\n\n  /**\n   * Retrieve a store\n   */\n  getStore(id: StoreId): Store | undefined {\n    return this.stores[id];\n  }\n\n  /**\n   * Handle errors\n   */\n  onError(error: unknown) {\n    if (this.hasErrorHandler) {\n      this.errorHandler?.onError(error, STORE_MANAGER);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { StoreManager };\n", "import { NO_STORAGE } from '@rudderstack/analytics-js-common/constants/storages';\nimport type { ApplicationState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport type { StorageType } from '@rudderstack/analytics-js-common/types/Storage';\nimport type { UserSessionKey } from '@rudderstack/analytics-js-common/types/UserSessionStorage';\n\nconst getStorageTypeFromPreConsentIfApplicable = (\n  state: ApplicationState,\n  sessionKey: UserSessionKey,\n) => {\n  let overriddenStorageType: StorageType | undefined;\n  if (state.consents.preConsent.value.enabled) {\n    switch (state.consents.preConsent.value.storage?.strategy) {\n      case 'none':\n        overriddenStorageType = NO_STORAGE;\n        break;\n      case 'session':\n        if (sessionKey !== 'sessionInfo') {\n          overriddenStorageType = NO_STORAGE;\n        }\n        break;\n      case 'anonymousId':\n        if (sessionKey !== 'anonymousId') {\n          overriddenStorageType = NO_STORAGE;\n        }\n        break;\n      default:\n        break;\n    }\n  }\n  return overriddenStorageType;\n};\n\nexport { getStorageTypeFromPreConsentIfApplicable };\n", "import { URL_PATTERN } from '../constants/urls';\nimport { isFunction, isString } from './checks';\n\nconst removeDuplicateSlashes = (str: string): string => str.replace(/\\/{2,}/g, '/');\n\n/**\n * Checks if provided url is valid or not\n * @param url\n * @returns true if `url` is valid and false otherwise\n */\nconst isValidURL = (url: string | undefined): url is string => {\n  if (!isString(url)) {\n    return false;\n  }\n\n  try {\n    // If URL is supported by the browser, we can use it to validate the URL\n    // Otherwise, we can at least check if the URL matches the pattern\n    if (isFunction(globalThis.URL)) {\n      // eslint-disable-next-line no-new\n      new URL(url);\n    }\n    return URL_PATTERN.test(url);\n  } catch (e) {\n    return false;\n  }\n};\n\nexport { removeDuplicateSlashes, isValidURL };\n", "import { isObjectLiteralAndNotNull } from '@rudderstack/analytics-js-common/utilities/object';\nimport { isNullOrUndefined, isString } from '@rudderstack/analytics-js-common/utilities/checks';\nimport {\n  SUPPORTED_STORAGE_TYPES,\n  type StorageType,\n} from '@rudderstack/analytics-js-common/types/Storage';\nimport { isValidURL } from '@rudderstack/analytics-js-common/utilities/url';\nimport {\n  WRITE_KEY_VALIDATION_ERROR,\n  DATA_PLANE_URL_VALIDATION_ERROR,\n} from '../../../constants/logMessages';\n\nconst validateWriteKey = (writeKey?: string) => {\n  if (!isString(writeKey) || (writeKey as string).trim().length === 0) {\n    throw new Error(WRITE_KEY_VALIDATION_ERROR(writeKey));\n  }\n};\n\nconst validateDataPlaneUrl = (dataPlaneUrl?: string) => {\n  if (!isValidURL(dataPlaneUrl)) {\n    throw new Error(DATA_PLANE_URL_VALIDATION_ERROR(dataPlaneUrl));\n  }\n};\n\nconst validateLoadArgs = (writeKey?: string, dataPlaneUrl?: string) => {\n  validateWriteKey(writeKey);\n  validateDataPlaneUrl(dataPlaneUrl);\n};\n\nconst isValidSourceConfig = (res: any): boolean =>\n  isObjectLiteralAndNotNull(res) &&\n  isObjectLiteralAndNotNull(res.source) &&\n  !isNullOrUndefined(res.source.id) &&\n  isObjectLiteralAndNotNull(res.source.config) &&\n  Array.isArray(res.source.destinations);\n\nconst isValidStorageType = (storageType?: StorageType): boolean =>\n  typeof storageType === 'string' && SUPPORTED_STORAGE_TYPES.includes(storageType);\n\nconst getTopDomainUrl = (url: string) => {\n  // Create a URL object\n  const urlObj = new URL(url);\n\n  // Extract the host and protocol\n  const { host, protocol } = urlObj;\n\n  // Split the host into parts\n  const parts: string[] = host.split('.');\n  let topDomain;\n  // Handle different cases, especially for co.uk or similar TLDs\n  if (parts.length > 2) {\n    // Join the last two parts for the top-level domain\n    topDomain = `${parts[parts.length - 2]}.${parts[parts.length - 1]}`;\n  } else {\n    // If only two parts or less, return as it is\n    topDomain = host;\n  }\n  return `${protocol}//${topDomain}`;\n};\n\nconst getDataServiceUrl = (endpoint: string) => {\n  const url = getTopDomainUrl(window.location.href);\n  const formattedEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;\n  return `${url}/${formattedEndpoint}`;\n};\n\nexport {\n  validateLoadArgs,\n  isValidSourceConfig,\n  isValidStorageType,\n  validateWriteKey,\n  validateDataPlaneUrl,\n  getTopDomainUrl,\n  getDataServiceUrl,\n};\n", "import type { UTMParameters } from '@rudderstack/analytics-js-common/types/EventContext';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\n\n/**\n * Removes trailing slash from url\n * @param url\n * @returns url\n */\nconst removeTrailingSlashes = (url: Nullable<string> | undefined): Nullable<string> | undefined =>\n  url?.endsWith('/') ? removeTrailingSlashes(url.substring(0, url.length - 1)) : url;\n\nconst getDomain = (url: string): Nullable<string> => {\n  try {\n    const urlObj = new URL(url);\n    return urlObj.host;\n  } catch (error) {\n    return null;\n  }\n};\n\n/**\n * Get the referring domain from the referrer URL\n * @param referrer Page referrer\n * @returns Page referring domain\n */\nconst getReferringDomain = (referrer: string): string => getDomain(referrer) ?? '';\n\n/**\n * Extracts UTM parameters from the URL\n * @param url Page URL\n * @returns UTM parameters\n */\nconst extractUTMParameters = (url: string): UTMParameters => {\n  const result: UTMParameters = {};\n  try {\n    const urlObj = new URL(url);\n    const UTM_PREFIX = 'utm_';\n    urlObj.searchParams.forEach((value, sParam) => {\n      if (sParam.startsWith(UTM_PREFIX)) {\n        let utmParam = sParam.substring(UTM_PREFIX.length);\n        // Not sure why we're doing this\n        if (utmParam === 'campaign') {\n          utmParam = 'name';\n        }\n        result[utmParam] = value;\n      }\n    });\n  } catch (error) {\n    // Do nothing\n  }\n  return result;\n};\n\n/**\n * To get the URL until the hash\n * @param url The input URL\n * @returns URL until the hash\n */\nconst getUrlWithoutHash = (url: string): string => {\n  let urlWithoutHash = url;\n  try {\n    const urlObj = new URL(url);\n    urlWithoutHash = urlObj.origin + urlObj.pathname + urlObj.search;\n  } catch (error) {\n    // Do nothing\n  }\n  return urlWithoutHash;\n};\n\nexport {\n  removeTrailingSlashes,\n  getReferringDomain,\n  extractUTMParameters,\n  getUrlWithoutHash,\n  getDomain,\n};\n", "import type {\n  RegionDetails,\n  ResidencyServerRegion,\n} from '@rudderstack/analytics-js-common/types/DataResidency';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { CONFIG_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { isValidURL } from '@rudderstack/analytics-js-common/utilities/url';\nimport { UNSUPPORTED_RESIDENCY_SERVER_REGION_WARNING } from '../../../constants/logMessages';\n\nconst DEFAULT_REGION = 'US';\n\n/**\n * A function to get url from source config response\n * @param {array} urls    An array of objects containing urls\n * @returns\n */\nconst getDefaultUrlOfRegion = (urls?: RegionDetails[]) => {\n  let url;\n  if (Array.isArray(urls) && urls.length > 0) {\n    const obj = urls.find(elem => elem.default === true);\n    if (obj && isValidURL(obj.url)) {\n      return obj.url;\n    }\n  }\n  return url;\n};\n\nconst validateResidencyServerRegion = (\n  residencyServerRegion?: ResidencyServerRegion,\n  logger?: ILogger,\n) => {\n  const residencyServerRegions = ['US', 'EU'];\n  if (residencyServerRegion && !residencyServerRegions.includes(residencyServerRegion)) {\n    logger?.warn(\n      UNSUPPORTED_RESIDENCY_SERVER_REGION_WARNING(\n        CONFIG_MANAGER,\n        residencyServerRegion,\n        DEFAULT_REGION,\n      ),\n    );\n    return undefined;\n  }\n  return residencyServerRegion;\n};\n\n/**\n * A function to determine the dataPlaneUrl\n * @param {Object} dataplanes An object containing dataPlaneUrl for different region\n * @param {String} serverUrl dataPlaneUrl provided in the load call\n * @param {String} residencyServerRegion User provided residency server region\n * @param {Logger} logger logger instance\n * @returns The data plane URL string to use\n */\nconst resolveDataPlaneUrl = (\n  dataplanes?: Record<ResidencyServerRegion, RegionDetails[]>,\n  serverUrl?: string,\n  residencyServerRegion?: ResidencyServerRegion,\n  logger?: ILogger,\n) => {\n  // Check if dataPlanes object is present in source config\n  if (dataplanes && Object.keys(dataplanes).length > 0) {\n    const region = validateResidencyServerRegion(residencyServerRegion, logger) ?? DEFAULT_REGION;\n    const regionUrlArr: RegionDetails[] = dataplanes[region] || dataplanes[DEFAULT_REGION];\n\n    const defaultUrl = getDefaultUrlOfRegion(regionUrlArr);\n    if (defaultUrl) {\n      return defaultUrl;\n    }\n  }\n  // return the dataPlaneUrl provided in load API(if available)\n  if (serverUrl) {\n    return serverUrl;\n  }\n\n  // return undefined if data plane url can not be determined\n  return undefined;\n};\n\nexport { resolveDataPlaneUrl };\n", "import type { DeliveryType, StorageStrategy } from '../types/LoadOptions';\n\nconst DEFAULT_PRE_CONSENT_STORAGE_STRATEGY: StorageStrategy = 'none';\nconst DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE: DeliveryType = 'immediate';\n\nexport { DEFAULT_PRE_CONSENT_STORAGE_STRATEGY, DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE };\n", "const DEFAULT_INTEGRATIONS_CONFIG = {\n  All: true,\n};\n\nexport { DEFAULT_INTEGRATIONS_CONFIG };\n", "import { CONFIG_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport type {\n  ConsentManagementOptions,\n  ConsentManagementProvider,\n  Consents,\n  CookieConsentOptions,\n} from '@rudderstack/analytics-js-common/types/Consent';\nimport type { ConsentOptions } from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport {\n  isNonEmptyObject,\n  isObjectLiteralAndNotNull,\n  mergeDeepRight,\n} from '@rudderstack/analytics-js-common/utilities/object';\nimport { UNSUPPORTED_CONSENT_MANAGER_ERROR } from '@rudderstack/analytics-js/constants/logMessages';\nimport { clone } from 'ramda';\nimport { state } from '@rudderstack/analytics-js/state';\nimport { DEFAULT_INTEGRATIONS_CONFIG } from '@rudderstack/analytics-js-common/constants/integrationsConfig';\nimport { isDefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { ConsentManagersToPluginNameMap } from '../configManager/constants';\n\n/**\n * A function to get the name of the consent manager with enabled true set in the load options\n * @param cookieConsentOptions Input provided as load option\n * @returns string|undefined\n *\n * Example input: {\n *   oneTrust:{\n *     enabled: true\n *   }\n * }\n *\n * Output: 'oneTrust'\n */\nconst getUserSelectedConsentManager = (\n  cookieConsentOptions?: CookieConsentOptions,\n): string | undefined => {\n  if (!isNonEmptyObject(cookieConsentOptions)) {\n    return undefined;\n  }\n\n  const validCookieConsentOptions = cookieConsentOptions as CookieConsentOptions;\n  return Object.keys(validCookieConsentOptions).find(\n    e =>\n      e && validCookieConsentOptions[e] && (validCookieConsentOptions[e] as any).enabled === true,\n  );\n};\n\n/**\n * Validates and normalizes the consent options provided by the user\n * @param options Consent options provided by the user\n * @returns Validated and normalized consent options\n */\nconst getValidPostConsentOptions = (options?: ConsentOptions) => {\n  const validOptions: ConsentOptions = {\n    sendPageEvent: false,\n    trackConsent: false,\n    discardPreConsentEvents: false,\n  };\n  if (isObjectLiteralAndNotNull(options)) {\n    const clonedOptions = clone(options);\n\n    validOptions.storage = clonedOptions.storage;\n    if (isDefined(clonedOptions.integrations)) {\n      validOptions.integrations = isObjectLiteralAndNotNull(clonedOptions.integrations)\n        ? clonedOptions.integrations\n        : DEFAULT_INTEGRATIONS_CONFIG;\n    }\n    validOptions.discardPreConsentEvents = clonedOptions.discardPreConsentEvents === true;\n    validOptions.sendPageEvent = clonedOptions.sendPageEvent === true;\n    validOptions.trackConsent = clonedOptions.trackConsent === true;\n    if (isNonEmptyObject(clonedOptions.consentManagement)) {\n      // Override enabled value with the current state value\n      validOptions.consentManagement = mergeDeepRight(clonedOptions.consentManagement, {\n        enabled: state.consents.enabled.value,\n      });\n    }\n  }\n  return validOptions;\n};\n\n/**\n * Validates if the input is a valid consents data\n * @param value Input consents data\n * @returns true if the input is a valid consents data else false\n */\nconst isValidConsentsData = (value: Consents | undefined): value is Consents =>\n  isNonEmptyObject(value) || Array.isArray(value);\n\n/**\n * Retrieves the corresponding provider and plugin name of the selected consent manager from the supported consent managers\n * @param consentManagementOpts consent management options\n * @param logger logger instance\n * @returns Corresponding provider and plugin name of the selected consent manager from the supported consent managers\n */\nconst getConsentManagerInfo = (\n  consentManagementOpts: ConsentManagementOptions,\n  logger?: ILogger,\n) => {\n  let { provider }: { provider?: ConsentManagementProvider } = consentManagementOpts;\n  const consentManagerPluginName = provider ? ConsentManagersToPluginNameMap[provider] : undefined;\n  if (provider && !consentManagerPluginName) {\n    logger?.error(\n      UNSUPPORTED_CONSENT_MANAGER_ERROR(CONFIG_MANAGER, provider, ConsentManagersToPluginNameMap),\n    );\n\n    // Reset the provider value\n    provider = undefined;\n  }\n  return { provider, consentManagerPluginName };\n};\n\n/**\n * Validates and converts the consent management options into a normalized format\n * @param consentManagementOpts Consent management options provided by the user\n * @param logger logger instance\n * @returns An object containing the consent manager plugin name, initialized, enabled and consents data\n */\nconst getConsentManagementData = (\n  consentManagementOpts: ConsentManagementOptions | undefined,\n  logger?: ILogger,\n) => {\n  let consentManagerPluginName: PluginName | undefined;\n  let allowedConsentIds: Consents = [];\n  let deniedConsentIds: Consents = [];\n  let initialized = false;\n  let provider: ConsentManagementProvider | undefined;\n\n  let enabled = consentManagementOpts?.enabled === true;\n  if (isNonEmptyObject<ConsentManagementOptions>(consentManagementOpts) && enabled) {\n    // Get the corresponding plugin name of the selected consent manager from the supported consent managers\n    ({ provider, consentManagerPluginName } = getConsentManagerInfo(consentManagementOpts, logger));\n\n    if (isValidConsentsData(consentManagementOpts.allowedConsentIds)) {\n      allowedConsentIds = consentManagementOpts.allowedConsentIds;\n      initialized = true;\n    }\n\n    if (isValidConsentsData(consentManagementOpts.deniedConsentIds)) {\n      deniedConsentIds = consentManagementOpts.deniedConsentIds;\n      initialized = true;\n    }\n  }\n\n  const consentsData = {\n    allowedConsentIds,\n    deniedConsentIds,\n  };\n\n  // Enable consent management only if consent manager is supported\n  enabled = enabled && Boolean(consentManagerPluginName);\n\n  return {\n    provider,\n    consentManagerPluginName,\n    initialized,\n    enabled,\n    consentsData,\n  };\n};\n\nexport { getUserSelectedConsentManager, getValidPostConsentOptions, getConsentManagementData };\n", "import type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { CONFIG_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { batch } from '@preact/signals-core';\nimport { isDefined, isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { isSDKRunningInChromeExtension } from '@rudderstack/analytics-js-common/utilities/detect';\nimport { DEFAULT_STORAGE_TYPE } from '@rudderstack/analytics-js-common/types/Storage';\nimport type {\n  DeliveryType,\n  StorageStrategy,\n} from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport {\n  DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE,\n  DEFAULT_PRE_CONSENT_STORAGE_STRATEGY,\n} from '@rudderstack/analytics-js-common/constants/consent';\nimport { isObjectLiteralAndNotNull } from '@rudderstack/analytics-js-common/utilities/object';\nimport type {\n  ConsentManagementMetadata,\n  ConsentResolutionStrategy,\n} from '@rudderstack/analytics-js-common/types/Consent';\nimport { clone } from 'ramda';\nimport type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport { isValidURL, removeDuplicateSlashes } from '@rudderstack/analytics-js-common/utilities/url';\nimport { MODULE_TYPE, APP_VERSION } from '@rudderstack/analytics-js/constants/app';\nimport { BUILD_TYPE, DEFAULT_CONFIG_BE_URL } from '@rudderstack/analytics-js/constants/urls';\nimport { state } from '../../../state';\nimport {\n  INVALID_CONFIG_URL_WARNING,\n  STORAGE_DATA_MIGRATION_OVERRIDE_WARNING,\n  STORAGE_TYPE_VALIDATION_WARNING,\n  UNSUPPORTED_BEACON_API_WARNING,\n  UNSUPPORTED_ERROR_REPORTING_PROVIDER_WARNING,\n  UNSUPPORTED_PRE_CONSENT_EVENTS_DELIVERY_TYPE,\n  UNSUPPORTED_PRE_CONSENT_STORAGE_STRATEGY,\n  UNSUPPORTED_STORAGE_ENCRYPTION_VERSION_WARNING,\n} from '../../../constants/logMessages';\nimport {\n  isErrorReportingEnabled,\n  isMetricsReportingEnabled,\n  getErrorReportingProviderNameFromConfig,\n} from '../../utilities/statsCollection';\nimport { getDomain, removeTrailingSlashes } from '../../utilities/url';\nimport type { SourceConfigResponse } from '../types';\nimport {\n  DEFAULT_DATA_SERVICE_ENDPOINT,\n  DEFAULT_ERROR_REPORTING_PROVIDER,\n  DEFAULT_STORAGE_ENCRYPTION_VERSION,\n  ErrorReportingProvidersToPluginNameMap,\n  StorageEncryptionVersionsToPluginNameMap,\n} from '../constants';\nimport { getDataServiceUrl, isValidStorageType } from './validate';\nimport { getConsentManagementData } from '../../utilities/consent';\n\n/**\n * Determines the SDK URL\n * @returns sdkURL\n */\nconst getSDKUrl = (): string | undefined => {\n  const scripts = document.getElementsByTagName('script');\n  const sdkFileNameRegex = /(?:^|\\/)rsa(\\.min)?\\.js$/;\n\n  // eslint-disable-next-line no-restricted-syntax\n  for (const script of scripts) {\n    const src = script.getAttribute('src');\n    if (src && sdkFileNameRegex.test(src)) {\n      return src;\n    }\n  }\n\n  return undefined;\n};\n\n/**\n * Updates the reporting state variables from the source config data\n * @param res Source config\n * @param logger Logger instance\n */\nconst updateReportingState = (res: SourceConfigResponse, logger?: ILogger): void => {\n  state.reporting.isErrorReportingEnabled.value =\n    isErrorReportingEnabled(res.source.config) && !isSDKRunningInChromeExtension();\n  state.reporting.isMetricsReportingEnabled.value = isMetricsReportingEnabled(res.source.config);\n\n  if (state.reporting.isErrorReportingEnabled.value) {\n    const errReportingProvider = getErrorReportingProviderNameFromConfig(res.source.config);\n\n    // Get the corresponding plugin name of the selected error reporting provider from the supported error reporting providers\n    const errReportingProviderPlugin = errReportingProvider\n      ? ErrorReportingProvidersToPluginNameMap[errReportingProvider]\n      : undefined;\n\n    if (!isUndefined(errReportingProvider) && !errReportingProviderPlugin) {\n      // set the default error reporting provider\n      logger?.warn(\n        UNSUPPORTED_ERROR_REPORTING_PROVIDER_WARNING(\n          CONFIG_MANAGER,\n          errReportingProvider,\n          ErrorReportingProvidersToPluginNameMap,\n          DEFAULT_ERROR_REPORTING_PROVIDER,\n        ),\n      );\n    }\n\n    state.reporting.errorReportingProviderPluginName.value =\n      errReportingProviderPlugin ??\n      ErrorReportingProvidersToPluginNameMap[DEFAULT_ERROR_REPORTING_PROVIDER];\n  }\n};\n\nconst updateStorageStateFromLoadOptions = (logger?: ILogger): void => {\n  const {\n    useServerSideCookies,\n    dataServiceEndpoint,\n    storage: storageOptsFromLoad,\n  } = state.loadOptions.value;\n  let storageType = storageOptsFromLoad?.type;\n  if (isDefined(storageType) && !isValidStorageType(storageType)) {\n    logger?.warn(\n      STORAGE_TYPE_VALIDATION_WARNING(CONFIG_MANAGER, storageType, DEFAULT_STORAGE_TYPE),\n    );\n    storageType = DEFAULT_STORAGE_TYPE;\n  }\n\n  let storageEncryptionVersion = storageOptsFromLoad?.encryption?.version;\n  const encryptionPluginName =\n    storageEncryptionVersion && StorageEncryptionVersionsToPluginNameMap[storageEncryptionVersion];\n\n  if (!isUndefined(storageEncryptionVersion) && isUndefined(encryptionPluginName)) {\n    // set the default encryption plugin\n    logger?.warn(\n      UNSUPPORTED_STORAGE_ENCRYPTION_VERSION_WARNING(\n        CONFIG_MANAGER,\n        storageEncryptionVersion,\n        StorageEncryptionVersionsToPluginNameMap,\n        DEFAULT_STORAGE_ENCRYPTION_VERSION,\n      ),\n    );\n    storageEncryptionVersion = DEFAULT_STORAGE_ENCRYPTION_VERSION;\n  } else if (isUndefined(storageEncryptionVersion)) {\n    storageEncryptionVersion = DEFAULT_STORAGE_ENCRYPTION_VERSION;\n  }\n\n  // Allow migration only if the configured encryption version is the default encryption version\n  const configuredMigrationValue = storageOptsFromLoad?.migrate;\n  const finalMigrationVal =\n    (configuredMigrationValue as boolean) &&\n    storageEncryptionVersion === DEFAULT_STORAGE_ENCRYPTION_VERSION;\n\n  if (configuredMigrationValue === true && finalMigrationVal !== configuredMigrationValue) {\n    logger?.warn(\n      STORAGE_DATA_MIGRATION_OVERRIDE_WARNING(\n        CONFIG_MANAGER,\n        storageEncryptionVersion,\n        DEFAULT_STORAGE_ENCRYPTION_VERSION,\n      ),\n    );\n  }\n\n  batch(() => {\n    state.storage.type.value = storageType;\n    let cookieOptions = storageOptsFromLoad?.cookie ?? {};\n\n    if (useServerSideCookies) {\n      state.serverCookies.isEnabledServerSideCookies.value = useServerSideCookies;\n      const dataServiceUrl = getDataServiceUrl(\n        dataServiceEndpoint ?? DEFAULT_DATA_SERVICE_ENDPOINT,\n      );\n      if (isValidURL(dataServiceUrl)) {\n        state.serverCookies.dataServiceUrl.value = removeTrailingSlashes(dataServiceUrl) as string;\n\n        const curHost = getDomain(window.location.href);\n        const dataServiceHost = getDomain(dataServiceUrl);\n\n        // If the current host is different from the data service host, then it is a cross-site request\n        // For server-side cookies to work, we need to set the SameSite=None and Secure attributes\n        if (curHost !== dataServiceHost) {\n          cookieOptions = {\n            ...cookieOptions,\n            samesite: 'None',\n            secure: true,\n          };\n        }\n      } else {\n        state.serverCookies.isEnabledServerSideCookies.value = false;\n      }\n    }\n\n    state.storage.cookie.value = cookieOptions;\n\n    state.storage.encryptionPluginName.value =\n      StorageEncryptionVersionsToPluginNameMap[storageEncryptionVersion as string];\n\n    state.storage.migrate.value = finalMigrationVal;\n  });\n};\n\nconst updateConsentsStateFromLoadOptions = (logger?: ILogger): void => {\n  const { provider, consentManagerPluginName, initialized, enabled, consentsData } =\n    getConsentManagementData(state.loadOptions.value.consentManagement, logger);\n\n  // Pre-consent\n  const preConsentOpts = state.loadOptions.value.preConsent;\n\n  let storageStrategy: StorageStrategy =\n    preConsentOpts?.storage?.strategy ?? DEFAULT_PRE_CONSENT_STORAGE_STRATEGY;\n  const StorageStrategies = ['none', 'session', 'anonymousId'];\n  if (isDefined(storageStrategy) && !StorageStrategies.includes(storageStrategy)) {\n    storageStrategy = DEFAULT_PRE_CONSENT_STORAGE_STRATEGY;\n\n    logger?.warn(\n      UNSUPPORTED_PRE_CONSENT_STORAGE_STRATEGY(\n        CONFIG_MANAGER,\n        preConsentOpts?.storage?.strategy,\n        DEFAULT_PRE_CONSENT_STORAGE_STRATEGY,\n      ),\n    );\n  }\n\n  let eventsDeliveryType: DeliveryType =\n    preConsentOpts?.events?.delivery ?? DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE;\n  const deliveryTypes = ['immediate', 'buffer'];\n  if (isDefined(eventsDeliveryType) && !deliveryTypes.includes(eventsDeliveryType)) {\n    eventsDeliveryType = DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE;\n\n    logger?.warn(\n      UNSUPPORTED_PRE_CONSENT_EVENTS_DELIVERY_TYPE(\n        CONFIG_MANAGER,\n        preConsentOpts?.events?.delivery,\n        DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE,\n      ),\n    );\n  }\n\n  batch(() => {\n    state.consents.activeConsentManagerPluginName.value = consentManagerPluginName;\n    state.consents.initialized.value = initialized;\n    state.consents.enabled.value = enabled;\n    state.consents.data.value = consentsData;\n    state.consents.provider.value = provider;\n\n    state.consents.preConsent.value = {\n      // Only enable pre-consent if it is explicitly enabled and\n      // if it is not already initialized and\n      // if consent management is enabled\n      enabled:\n        state.loadOptions.value.preConsent?.enabled === true &&\n        initialized === false &&\n        enabled === true,\n      storage: {\n        strategy: storageStrategy,\n      },\n      events: {\n        delivery: eventsDeliveryType,\n      },\n    };\n  });\n};\n\n/**\n * Determines the consent management state variables from the source config data\n * @param resp Source config response\n * @param logger Logger instance\n */\nconst updateConsentsState = (resp: SourceConfigResponse): void => {\n  let resolutionStrategy: ConsentResolutionStrategy | undefined =\n    state.consents.resolutionStrategy.value;\n\n  let cmpMetadata: ConsentManagementMetadata | undefined;\n  if (isObjectLiteralAndNotNull(resp.consentManagementMetadata)) {\n    if (state.consents.provider.value) {\n      resolutionStrategy =\n        resp.consentManagementMetadata.providers.find(\n          p => p.provider === state.consents.provider.value,\n        )?.resolutionStrategy ?? state.consents.resolutionStrategy.value;\n    }\n\n    cmpMetadata = resp.consentManagementMetadata;\n  }\n\n  // If the provider is custom, then the resolution strategy is not applicable\n  if (state.consents.provider.value === 'custom') {\n    resolutionStrategy = undefined;\n  }\n\n  batch(() => {\n    state.consents.metadata.value = clone(cmpMetadata);\n    state.consents.resolutionStrategy.value = resolutionStrategy;\n  });\n};\n\nconst updateDataPlaneEventsStateFromLoadOptions = (logger?: ILogger) => {\n  if (state.dataPlaneEvents.deliveryEnabled.value) {\n    const defaultEventsQueuePluginName: PluginName = 'XhrQueue';\n    let eventsQueuePluginName: PluginName = defaultEventsQueuePluginName;\n\n    if (state.loadOptions.value.useBeacon) {\n      if (state.capabilities.isBeaconAvailable.value) {\n        eventsQueuePluginName = 'BeaconQueue';\n      } else {\n        eventsQueuePluginName = defaultEventsQueuePluginName;\n\n        logger?.warn(UNSUPPORTED_BEACON_API_WARNING(CONFIG_MANAGER));\n      }\n    }\n\n    batch(() => {\n      state.dataPlaneEvents.eventsQueuePluginName.value = eventsQueuePluginName;\n    });\n  }\n};\n\nconst getSourceConfigURL = (\n  configUrl: string | undefined,\n  writeKey: string,\n  lockIntegrationsVersion: boolean,\n  logger?: ILogger,\n): string => {\n  const defSearchParams = new URLSearchParams({\n    p: MODULE_TYPE,\n    v: APP_VERSION,\n    build: BUILD_TYPE,\n    writeKey,\n    lockIntegrationsVersion: lockIntegrationsVersion.toString(),\n  });\n\n  let origin = DEFAULT_CONFIG_BE_URL;\n  let searchParams = defSearchParams;\n  let pathname = '/sourceConfig/';\n  let hash = '';\n  if (isValidURL(configUrl)) {\n    const configUrlInstance = new URL(configUrl);\n    if (!(removeTrailingSlashes(configUrlInstance.pathname) as string).endsWith('/sourceConfig')) {\n      configUrlInstance.pathname = `${\n        removeTrailingSlashes(configUrlInstance.pathname) as string\n      }/sourceConfig/`;\n    }\n    configUrlInstance.pathname = removeDuplicateSlashes(configUrlInstance.pathname);\n\n    defSearchParams.forEach((value, key) => {\n      if (configUrlInstance.searchParams.get(key) === null) {\n        configUrlInstance.searchParams.set(key, value);\n      }\n    });\n\n    origin = configUrlInstance.origin;\n    pathname = configUrlInstance.pathname;\n    searchParams = configUrlInstance.searchParams;\n    hash = configUrlInstance.hash;\n  } else {\n    logger?.warn(INVALID_CONFIG_URL_WARNING(CONFIG_MANAGER, configUrl));\n  }\n\n  return `${origin}${pathname}?${searchParams}${hash}`;\n};\n\nexport {\n  getSDKUrl,\n  updateReportingState,\n  updateStorageStateFromLoadOptions,\n  updateConsentsStateFromLoadOptions,\n  updateConsentsState,\n  updateDataPlaneEventsStateFromLoadOptions,\n  getSourceConfigURL,\n};\n", "import type { SourceConfig } from '@rudderstack/analytics-js-common/types/Source';\n\nconst isErrorReportingEnabled = (sourceConfig?: SourceConfig): boolean =>\n  sourceConfig?.statsCollection?.errors?.enabled === true;\n\nconst getErrorReportingProviderNameFromConfig = (sourceConfig?: SourceConfig): string | undefined =>\n  sourceConfig?.statsCollection?.errors?.provider;\n\nconst isMetricsReportingEnabled = (sourceConfig?: SourceConfig): boolean =>\n  sourceConfig?.statsCollection?.metrics?.enabled === true;\n\nexport {\n  isErrorReportingEnabled,\n  getErrorReportingProviderNameFromConfig,\n  isMetricsReportingEnabled,\n};\n", "/**\n * Determines if the SDK is running inside a chrome extension\n * @returns boolean\n */\nconst isSDKRunningInChromeExtension = (): boolean =>\n  !!((window as any).chrome && (window as any).chrome.runtime && (window as any).chrome.runtime.id);\n\nexport { isSDKRunningInChromeExtension };\n", "/* eslint-disable class-methods-use-this */\nimport type {\n  IHttpClient,\n  ResponseDetails,\n} from '@rudderstack/analytics-js-common/types/HttpClient';\nimport { batch, effect } from '@preact/signals-core';\nimport { isFunction, isString } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { <PERSON><PERSON><PERSON>r<PERSON>and<PERSON> } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { Destination } from '@rudderstack/analytics-js-common/types/Destination';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { CONFIG_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { isValidSourceConfig, validateLoadArgs } from './util/validate';\nimport {\n  DATA_PLANE_URL_ERROR,\n  SOURCE_CONFIG_FETCH_ERROR,\n  SOURCE_CONFIG_OPTION_ERROR,\n  SOURCE_CONFIG_RESOLUTION_ERROR,\n  SOURCE_DISABLED_ERROR,\n} from '../../constants/logMessages';\nimport { filterEnabledDestination } from '../utilities/destinations';\nimport { removeTrailingSlashes } from '../utilities/url';\nimport { APP_VERSION } from '../../constants/app';\nimport { state } from '../../state';\nimport { resolveDataPlaneUrl } from './util/dataPlaneResolver';\nimport { getIntegrationsCDNPath, getPluginsCDNPath } from './util/cdnPaths';\nimport type { IConfigManager, SourceConfigResponse } from './types';\nimport {\n  getSourceConfigURL,\n  updateConsentsState,\n  updateConsentsStateFromLoadOptions,\n  updateDataPlaneEventsStateFromLoadOptions,\n  updateReportingState,\n  updateStorageStateFromLoadOptions,\n} from './util/commonUtil';\n\nclass ConfigManager implements IConfigManager {\n  httpClient: IHttpClient;\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n  hasErrorHandler = false;\n\n  constructor(httpClient: IHttpClient, errorHandler?: IErrorHandler, logger?: ILogger) {\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.httpClient = httpClient;\n    this.hasErrorHandler = Boolean(this.errorHandler);\n\n    this.onError = this.onError.bind(this);\n    this.processConfig = this.processConfig.bind(this);\n  }\n\n  attachEffects() {\n    effect(() => {\n      this.logger?.setMinLogLevel(state.lifecycle.logLevel.value);\n    });\n  }\n\n  /**\n   * A function to validate, construct and store loadOption, lifecycle, source and destination\n   * config related information in global state\n   */\n  init() {\n    this.attachEffects();\n\n    validateLoadArgs(state.lifecycle.writeKey.value, state.lifecycle.dataPlaneUrl.value);\n\n    const lockIntegrationsVersion = state.loadOptions.value.lockIntegrationsVersion as boolean;\n\n    // determine the path to fetch integration SDK from\n    const intgCdnUrl = getIntegrationsCDNPath(\n      APP_VERSION,\n      lockIntegrationsVersion,\n      state.loadOptions.value.destSDKBaseURL,\n    );\n    // determine the path to fetch remote plugins from\n    const pluginsCDNPath = getPluginsCDNPath(state.loadOptions.value.pluginsSDKBaseURL);\n\n    updateStorageStateFromLoadOptions(this.logger);\n    updateConsentsStateFromLoadOptions(this.logger);\n    updateDataPlaneEventsStateFromLoadOptions(this.logger);\n\n    const { logLevel, configUrl } = state.loadOptions.value;\n\n    // set application lifecycle state in global state\n    batch(() => {\n      state.lifecycle.integrationsCDNPath.value = intgCdnUrl;\n      state.lifecycle.pluginsCDNPath.value = pluginsCDNPath;\n\n      if (logLevel) {\n        state.lifecycle.logLevel.value = logLevel;\n      }\n\n      state.lifecycle.sourceConfigUrl.value = getSourceConfigURL(\n        configUrl,\n        state.lifecycle.writeKey.value as string,\n        lockIntegrationsVersion,\n        this.logger,\n      );\n    });\n\n    this.getConfig();\n  }\n\n  /**\n   * Handle errors\n   */\n  onError(error: unknown, customMessage?: string, shouldAlwaysThrow?: boolean) {\n    if (this.hasErrorHandler) {\n      this.errorHandler?.onError(error, CONFIG_MANAGER, customMessage, shouldAlwaysThrow);\n    } else {\n      throw error;\n    }\n  }\n\n  /**\n   * A callback function that is executed once we fetch the source config response.\n   * Use to construct and store information that are dependent on the sourceConfig.\n   */\n  processConfig(response?: SourceConfigResponse | string, details?: ResponseDetails) {\n    // TODO: add retry logic with backoff based on rejectionDetails.xhr.status\n    // We can use isErrRetryable utility method\n    if (!response) {\n      this.onError(SOURCE_CONFIG_FETCH_ERROR(details?.error));\n      return;\n    }\n\n    let res: SourceConfigResponse;\n    try {\n      if (isString(response)) {\n        res = JSON.parse(response);\n      } else {\n        res = response;\n      }\n    } catch (err) {\n      this.onError(err, SOURCE_CONFIG_RESOLUTION_ERROR, true);\n      return;\n    }\n\n    if (!isValidSourceConfig(res)) {\n      this.onError(new Error(SOURCE_CONFIG_RESOLUTION_ERROR), undefined, true);\n      return;\n    }\n\n    // Log error and abort if source is disabled\n    if (res.source.enabled === false) {\n      this.logger?.error(SOURCE_DISABLED_ERROR);\n      return;\n    }\n\n    // set the values in state for reporting slice\n    updateReportingState(res, this.logger);\n\n    // determine the dataPlane url\n    const dataPlaneUrl = resolveDataPlaneUrl(\n      res.source.dataplanes,\n      state.lifecycle.dataPlaneUrl.value,\n      state.loadOptions.value.residencyServer,\n      this.logger,\n    );\n\n    if (!dataPlaneUrl) {\n      this.onError(new Error(DATA_PLANE_URL_ERROR), undefined, true);\n      return;\n    }\n    const nativeDestinations: Destination[] =\n      res.source.destinations.length > 0 ? filterEnabledDestination(res.source.destinations) : [];\n\n    // set in the state --> source, destination, lifecycle, reporting\n    batch(() => {\n      // set source related information in state\n      state.source.value = {\n        config: res.source.config,\n        id: res.source.id,\n        workspaceId: res.source.workspaceId,\n      };\n\n      // set device mode destination related information in state\n      state.nativeDestinations.configuredDestinations.value = nativeDestinations;\n\n      // set the desired optional plugins\n      state.plugins.pluginsToLoadFromConfig.value = state.loadOptions.value.plugins ?? [];\n\n      updateConsentsState(res);\n\n      // set application lifecycle state\n      // Cast to string as we are sure that the value is not undefined\n      state.lifecycle.activeDataplaneUrl.value = removeTrailingSlashes(dataPlaneUrl) as string;\n      state.lifecycle.status.value = 'configured';\n    });\n  }\n\n  /**\n   * A function to fetch source config either from /sourceConfig endpoint\n   * or from getSourceConfig load option\n   * @returns\n   */\n  getConfig() {\n    const sourceConfigFunc = state.loadOptions.value.getSourceConfig;\n    if (sourceConfigFunc) {\n      if (!isFunction(sourceConfigFunc)) {\n        throw new Error(SOURCE_CONFIG_OPTION_ERROR);\n      }\n      // Fetch source config from the function\n      const res = sourceConfigFunc();\n\n      if (res instanceof Promise) {\n        res\n          .then(pRes => this.processConfig(pRes as SourceConfigResponse))\n          .catch(err => {\n            this.onError(err, 'SourceConfig');\n          });\n      } else {\n        this.processConfig(res as SourceConfigResponse);\n      }\n    } else {\n      // Fetch source configuration from the configured URL\n      this.httpClient.getAsyncData({\n        url: state.lifecycle.sourceConfigUrl.value as string,\n        options: {\n          headers: {\n            'Content-Type': undefined,\n          },\n        },\n        callback: this.processConfig,\n      });\n    }\n  }\n}\n\nexport { ConfigManager };\n", "import { CDN_INT_DIR, CDN_PLUGINS_DIR } from '@rudderstack/analytics-js-common/constants/urls';\nimport { isValidURL } from '@rudderstack/analytics-js-common/utilities/url';\nimport { CDN_ARCH_VERSION_DIR, DEST_SDK_BASE_URL, PLUGINS_BASE_URL } from '../../../constants/urls';\nimport {\n  INTG_CDN_BASE_URL_ERROR,\n  PLUGINS_CDN_BASE_URL_ERROR,\n} from '../../../constants/logMessages';\nimport { removeTrailingSlashes } from '../../utilities/url';\nimport { getSDKUrl } from './commonUtil';\n\n/**\n * A function that determines integration SDK loading path\n * @param requiredVersion\n * @param lockIntegrationsVersion\n * @param customIntegrationsCDNPath\n * @returns\n */\nconst getIntegrationsCDNPath = (\n  requiredVersion: string,\n  lockIntegrationsVersion: boolean,\n  customIntegrationsCDNPath?: string,\n): string => {\n  let integrationsCDNPath = '';\n\n  // Get the CDN base URL from the user provided URL if any\n  if (customIntegrationsCDNPath) {\n    integrationsCDNPath = removeTrailingSlashes(customIntegrationsCDNPath) as string;\n\n    if (!integrationsCDNPath || !isValidURL(integrationsCDNPath)) {\n      throw new Error(INTG_CDN_BASE_URL_ERROR);\n    }\n\n    return integrationsCDNPath;\n  }\n\n  // Get the base path from the SDK script tag src attribute or use the default path\n  const sdkURL = getSDKUrl();\n  integrationsCDNPath = sdkURL\n    ? sdkURL.split('/').slice(0, -1).concat(CDN_INT_DIR).join('/')\n    : DEST_SDK_BASE_URL;\n\n  // If version is not locked it will always get the latest version of the integrations\n  if (lockIntegrationsVersion) {\n    integrationsCDNPath = integrationsCDNPath.replace(CDN_ARCH_VERSION_DIR, requiredVersion);\n  }\n\n  return integrationsCDNPath;\n};\n\n/**\n * A function that determines plugins SDK loading path\n * @param customPluginsCDNPath\n * @returns\n */\nconst getPluginsCDNPath = (customPluginsCDNPath?: string): string => {\n  let pluginsCDNPath = '';\n\n  // Get the CDN base URL from the user provided URL if any\n  if (customPluginsCDNPath) {\n    pluginsCDNPath = removeTrailingSlashes(customPluginsCDNPath) as string;\n\n    if (!pluginsCDNPath || !isValidURL(pluginsCDNPath)) {\n      throw new Error(PLUGINS_CDN_BASE_URL_ERROR);\n    }\n\n    return pluginsCDNPath;\n  }\n\n  // Get the base path from the SDK script tag src attribute or use the default path\n  const sdkURL = getSDKUrl();\n  pluginsCDNPath = sdkURL\n    ? sdkURL.split('/').slice(0, -1).concat(CDN_PLUGINS_DIR).join('/')\n    : PLUGINS_BASE_URL;\n\n  return pluginsCDNPath;\n};\n\nexport { getIntegrationsCDNPath, getPluginsCDNPath };\n", "import type { Destination } from '@rudderstack/analytics-js-common/types/Destination';\nimport type { ConfigResponseDestinationItem } from '../configManager/types';\n\n/**\n * A function to filter enabled destinations and map to required properties only\n * @param destinations\n *\n * @returns Destination[]\n */\nconst filterEnabledDestination = (destinations: ConfigResponseDestinationItem[]): Destination[] => {\n  const nativeDestinations: Destination[] = [];\n  destinations.forEach((destination: ConfigResponseDestinationItem) => {\n    if (destination.enabled && !destination.deleted) {\n      nativeDestinations.push({\n        id: destination.id,\n        displayName: destination.destinationDefinition.displayName,\n        config: destination.config,\n        shouldApplyDeviceModeTransformation:\n          destination.shouldApplyDeviceModeTransformation || false,\n        propagateEventsUntransformedOnError:\n          destination.propagateEventsUntransformedOnError || false,\n        userFriendlyId: `${destination.destinationDefinition.displayName.replaceAll(' ', '-')}___${\n          destination.id\n        }`,\n      });\n    }\n  });\n  return nativeDestinations;\n};\n\nexport { filterEnabledDestination };\n", "/**\n * To get the timezone of the user\n *\n * @returns string\n */\nconst getTimezone = (): string => {\n  const timezone = new Date().toString().match(/([A-Z]+[+-]\\d+)/);\n  return timezone && timezone[1] ? timezone[1] : 'NA';\n};\n\nexport { getTimezone };\n", "import { isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport { getReferringDomain, getUrlWithoutHash } from './url';\n\n/**\n * Get the referrer URL\n * @returns The referrer URL\n */\nconst getReferrer = (): string => document?.referrer || '$direct';\n\n/**\n * To get the canonical URL of the page\n * @returns canonical URL\n */\nconst getCanonicalUrl = (): string => {\n  const tags = document.getElementsByTagName('link');\n  let canonicalUrl = '';\n\n  for (let i = 0; tags[i]; i += 1) {\n    const tag = tags[i] as HTMLLinkElement;\n    if (tag.getAttribute('rel') === 'canonical' && !canonicalUrl) {\n      canonicalUrl = tag.getAttribute('href') ?? '';\n      break;\n    }\n  }\n\n  return canonicalUrl;\n};\n\nconst getUserAgent = (): Nullable<string> => {\n  if (isUndefined(globalThis.navigator)) {\n    return null;\n  }\n\n  let { userAgent } = globalThis.navigator;\n  const { brave } = globalThis.navigator as any;\n\n  // For supporting Brave browser detection,\n  // add \"Brave/<version>\" to the user agent with the version value from the Chrome component\n  if (brave && Object.getPrototypeOf(brave).isBrave) {\n    // Example:\n    // Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.114 Safari/537.36\n    const matchedArr = userAgent.match(/(chrome)\\/([\\w.]+)/i);\n\n    if (matchedArr) {\n      userAgent = `${userAgent} Brave/${matchedArr[2]}`;\n    }\n  }\n\n  return userAgent;\n};\n\nconst getLanguage = (): Nullable<string> => {\n  if (isUndefined(globalThis.navigator)) {\n    return null;\n  }\n\n  return globalThis.navigator.language ?? (globalThis.navigator as any).browserLanguage;\n};\n\n/**\n * Default page properties\n * @returns Default page properties\n */\nconst getDefaultPageProperties = (): Record<string, any> => {\n  const canonicalUrl = getCanonicalUrl();\n  let path = globalThis.location.pathname;\n  const { href: tabUrl } = globalThis.location;\n  let pageUrl = tabUrl;\n  const { search } = globalThis.location;\n\n  // If valid canonical URL is provided use this as page URL.\n  if (canonicalUrl) {\n    try {\n      const urlObj = new URL(canonicalUrl);\n      // If existing, query params of canonical URL will be used instead of the location.search ones\n      if (urlObj.search === '') {\n        pageUrl = canonicalUrl + search;\n      } else {\n        pageUrl = canonicalUrl;\n      }\n\n      path = urlObj.pathname;\n    } catch (err) {\n      // Do nothing\n    }\n  }\n\n  const url = getUrlWithoutHash(pageUrl);\n  const { title } = document;\n  const referrer = getReferrer();\n  return {\n    path,\n    referrer,\n    referring_domain: getReferringDomain(referrer),\n    search,\n    title,\n    url,\n    tab_url: tabUrl,\n  };\n};\n\nexport { getCanonicalUrl, getReferrer, getUserAgent, getLanguage, getDefaultPageProperties };\n", "import { legacyJSEngineRequiredPolyfills } from '../detection/dom';\n\n// eslint-disable-next-line no-constant-condition\nconst POLYFILL_URL = '__RS_POLYFILLIO_SDK_URL__'\n  ? `__RS_POLYFILLIO_SDK_URL__?version=3.111.0&features=${Object.keys(\n      legacyJSEngineRequiredPolyfills,\n    ).join('%2C')}`\n  : '';\n\nconst POLYFILL_LOAD_TIMEOUT = 10 * 1000; // 10 seconds\n\nconst POLYFILL_SCRIPT_ID = 'rudderstackPolyfill';\n\nexport { POLYFILL_URL, POLYFILL_LOAD_TIMEOUT, POLYFILL_SCRIPT_ID };\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IExternalSrcLoader } from '@rudderstack/analytics-js-common/services/ExternalSrcLoader/types';\nimport { ExternalSrcLoader } from '@rudderstack/analytics-js-common/services/ExternalSrcLoader';\nimport { batch, effect } from '@preact/signals-core';\nimport {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport { CAPABILITIES_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { getTimezone } from '@rudderstack/analytics-js-common/utilities/timezone';\nimport { isValidURL } from '@rudderstack/analytics-js-common/utilities/url';\nimport { isDefinedAndNotNull } from '@rudderstack/analytics-js-common/utilities/checks';\nimport {\n  INVALID_POLYFILL_URL_WARNING,\n  POLYFILL_SCRIPT_LOAD_ERROR,\n} from '../../constants/logMessages';\nimport { getLanguage, getUserAgent } from '../utilities/page';\nimport { getStorageEngine } from '../../services/StoreManager/storages';\nimport { state } from '../../state';\nimport { getUserAgentClientHint } from './detection/clientHint';\nimport type { ICapabilitiesManager } from './types';\nimport { POLYFILL_LOAD_TIMEOUT, POLYFILL_SCRIPT_ID, POLYFILL_URL } from './polyfill';\nimport {\n  getScreenDetails,\n  hasBeacon,\n  hasCrypto,\n  hasUAClientHints,\n  isIE11,\n  isLegacyJSEngine,\n  isStorageAvailable,\n} from './detection';\nimport { detectAdBlockers } from './detection/adBlockers';\nimport { debounce } from '../utilities/globals';\n\n// TODO: replace direct calls to detection methods with state values when possible\nclass CapabilitiesManager implements ICapabilitiesManager {\n  logger?: ILogger;\n  errorHandler?: IErrorHandler;\n  externalSrcLoader: IExternalSrcLoader;\n\n  constructor(errorHandler?: IErrorHandler, logger?: ILogger) {\n    this.logger = logger;\n    this.errorHandler = errorHandler;\n    this.externalSrcLoader = new ExternalSrcLoader(this.errorHandler, this.logger);\n    this.onError = this.onError.bind(this);\n    this.onReady = this.onReady.bind(this);\n  }\n\n  init() {\n    try {\n      this.prepareBrowserCapabilities();\n      this.attachWindowListeners();\n    } catch (err) {\n      this.onError(err);\n    }\n  }\n\n  /**\n   * Detect supported capabilities and set values in state\n   */\n  // eslint-disable-next-line class-methods-use-this\n  detectBrowserCapabilities() {\n    batch(() => {\n      // Storage related details\n      state.capabilities.storage.isCookieStorageAvailable.value = isStorageAvailable(\n        COOKIE_STORAGE,\n        getStorageEngine(COOKIE_STORAGE),\n        this.logger,\n      );\n      state.capabilities.storage.isLocalStorageAvailable.value = isStorageAvailable(\n        LOCAL_STORAGE,\n        undefined,\n        this.logger,\n      );\n      state.capabilities.storage.isSessionStorageAvailable.value = isStorageAvailable(\n        SESSION_STORAGE,\n        undefined,\n        this.logger,\n      );\n\n      // Browser feature detection details\n      state.capabilities.isBeaconAvailable.value = hasBeacon();\n      state.capabilities.isUaCHAvailable.value = hasUAClientHints();\n      state.capabilities.isCryptoAvailable.value = hasCrypto();\n      state.capabilities.isIE11.value = isIE11();\n      state.capabilities.isOnline.value = globalThis.navigator.onLine;\n\n      // Get page context details\n      state.context.userAgent.value = getUserAgent();\n      state.context.locale.value = getLanguage();\n      state.context.screen.value = getScreenDetails();\n      state.context.timezone.value = getTimezone();\n\n      if (hasUAClientHints()) {\n        getUserAgentClientHint((uach?: UADataValues) => {\n          state.context['ua-ch'].value = uach;\n        }, state.loadOptions.value.uaChTrackLevel);\n      }\n    });\n\n    // Ad blocker detection\n    effect(() => {\n      if (\n        state.loadOptions.value.sendAdblockPage === true &&\n        state.lifecycle.sourceConfigUrl.value !== undefined\n      ) {\n        detectAdBlockers(this.errorHandler, this.logger);\n      }\n    });\n  }\n\n  /**\n   * Detect if polyfills are required and then load script from polyfill URL\n   */\n  prepareBrowserCapabilities() {\n    state.capabilities.isLegacyDOM.value = isLegacyJSEngine();\n    const customPolyfillUrl = state.loadOptions.value.polyfillURL;\n    let polyfillUrl = POLYFILL_URL;\n    if (isDefinedAndNotNull(customPolyfillUrl)) {\n      if (isValidURL(customPolyfillUrl)) {\n        polyfillUrl = customPolyfillUrl;\n      } else {\n        this.logger?.warn(INVALID_POLYFILL_URL_WARNING(CAPABILITIES_MANAGER, customPolyfillUrl));\n      }\n    }\n\n    const shouldLoadPolyfill =\n      state.loadOptions.value.polyfillIfRequired &&\n      state.capabilities.isLegacyDOM.value &&\n      isValidURL(polyfillUrl);\n\n    if (shouldLoadPolyfill) {\n      const isDefaultPolyfillService = polyfillUrl !== state.loadOptions.value.polyfillURL;\n      if (isDefaultPolyfillService) {\n        // write key specific callback\n        // NOTE: we're not putting this into RudderStackGlobals as providing the property path to the callback function in the polyfill URL is not possible\n        const polyfillCallbackName = `RS_polyfillCallback_${state.lifecycle.writeKey.value}`;\n\n        const polyfillCallback = (): void => {\n          this.onReady();\n\n          // Remove the entry from window so we don't leave room for calling it again\n          delete (globalThis as any)[polyfillCallbackName];\n        };\n\n        (globalThis as any)[polyfillCallbackName] = polyfillCallback;\n\n        polyfillUrl = `${polyfillUrl}&callback=${polyfillCallbackName}`;\n      }\n\n      this.externalSrcLoader.loadJSFile({\n        url: polyfillUrl,\n        id: POLYFILL_SCRIPT_ID,\n        async: true,\n        timeout: POLYFILL_LOAD_TIMEOUT,\n        callback: (scriptId?: string) => {\n          if (!scriptId) {\n            this.onError(new Error(POLYFILL_SCRIPT_LOAD_ERROR(POLYFILL_SCRIPT_ID, polyfillUrl)));\n          } else if (!isDefaultPolyfillService) {\n            this.onReady();\n          }\n        },\n      });\n    } else {\n      this.onReady();\n    }\n  }\n\n  /**\n   * Attach listeners to window to observe event that update capabilities state values\n   */\n  attachWindowListeners() {\n    globalThis.addEventListener('offline', () => {\n      state.capabilities.isOnline.value = false;\n    });\n\n    globalThis.addEventListener('online', () => {\n      state.capabilities.isOnline.value = true;\n    });\n\n    globalThis.addEventListener(\n      'resize',\n      debounce(() => {\n        state.context.screen.value = getScreenDetails();\n      }, this),\n    );\n  }\n\n  /**\n   * Set the lifecycle status to next phase\n   */\n  // eslint-disable-next-line class-methods-use-this\n  onReady() {\n    this.detectBrowserCapabilities();\n    state.lifecycle.status.value = 'browserCapabilitiesReady';\n  }\n\n  /**\n   * Handles error\n   * @param error The error object\n   */\n  onError(error: unknown): void {\n    if (this.errorHandler) {\n      this.errorHandler.onError(error, CAPABILITIES_MANAGER);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { CapabilitiesManager };\n", "const getUserAgentClientHint = (callback: (uaCH?: UADataValues) => void, level = 'none') => {\n  if (level === 'none') {\n    callback(undefined);\n  }\n  if (level === 'default') {\n    callback(navigator.userAgentData);\n  }\n  if (level === 'full') {\n    navigator.userAgentData\n      ?.getHighEntropyValues([\n        'architecture',\n        'bitness',\n        'brands',\n        'mobile',\n        'model',\n        'platform',\n        'platformVersion',\n        'uaFullVersion',\n        'fullVersionList',\n        'wow64',\n      ])\n      .then((ua: any) => {\n        callback(ua);\n      })\n      .catch(() => {\n        callback();\n      });\n  }\n};\n\nexport { getUserAgentClientHint };\n", "import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport { HttpClient } from '../../../services/HttpClient/HttpClient';\nimport { state } from '../../../state';\n\nconst detectAdBlockers = (errorHandler?: IErrorHandler, logger?: ILogger): void => {\n  // Apparently, '?view=ad' is a query param that is blocked by majority of adblockers\n\n  // Use source config URL here as it is very unlikely to be blocked by adblockers\n  // Only the extra query param should make it vulnerable to adblockers\n  // This will work even if the users proxies it.\n  // The edge case where this doesn't work is when HEAD method is not allowed by the server (user's)\n  const baseUrl = new URL(state.lifecycle.sourceConfigUrl.value as string);\n  const url = `${baseUrl.origin}${baseUrl.pathname}?view=ad`;\n\n  const httpClient = new HttpClient(errorHandler, logger);\n  httpClient.setAuthHeader(state.lifecycle.writeKey.value as string);\n\n  httpClient.getAsyncData({\n    url,\n    options: {\n      // We actually don't need the response from the request, so we are using HEAD\n      method: 'HEAD',\n      headers: {\n        'Content-Type': undefined,\n      },\n    },\n    isRawResponse: true,\n    callback: (result, details) => {\n      // not ad blocked if the request is successful or it is not internally redirected on the client side\n      // Often adblockers instead of blocking the request, they redirect it to an internal URL\n      state.capabilities.isAdBlocked.value =\n        details?.error !== undefined || details?.xhr?.responseURL !== url;\n    },\n  });\n};\n\nexport { detectAdBlockers };\n", "import { random } from '@lukeed/csprng';\n\nvar SIZE=4096, HEX=[], IDX=0, BUFFER;\n\nfor (; IDX < 256; IDX++) {\n\tHEX[IDX] = (IDX + 256).toString(16).substring(1);\n}\n\nexport function v4() {\n\tif (!BUFFER || ((IDX + 16) > SIZE)) {\n\t\tBUFFER = random(SIZE);\n\t\tIDX = 0;\n\t}\n\n\tvar i=0, tmp, out='';\n\tfor (; i < 16; i++) {\n\t\ttmp = BUFFER[IDX + i];\n\t\tif (i==6) out += HEX[tmp & 15 | 64];\n\t\telse if (i==8) out += HEX[tmp & 63 | 128];\n\t\telse out += HEX[tmp];\n\n\t\tif (i & 1 && i > 1 && i < 11) out += '-';\n\t}\n\n\tIDX += 16;\n\treturn out;\n}\n", "export function random(len) {\n\treturn crypto.getRandomValues(new Uint8Array(len));\n}\n", "var IDX=256, HEX=[], BUFFER;\nwhile (IDX--) HEX[IDX] = (IDX + 256).toString(16).substring(1);\n\nexport function v4() {\n\tvar i=0, num, out='';\n\n\tif (!BUFFER || ((IDX + 16) > 256)) {\n\t\tBUFFER = Array(i=256);\n\t\twhile (i--) BUFFER[i] = 256 * Math.random() | 0;\n\t\ti = IDX = 0;\n\t}\n\n\tfor (; i < 16; i++) {\n\t\tnum = BUFFER[IDX + i];\n\t\tif (i==6) out += HEX[num & 15 | 64];\n\t\telse if (i==8) out += HEX[num & 63 | 128];\n\t\telse out += HEX[num];\n\n\t\tif (i & 1 && i > 1 && i < 11) out += '-';\n\t}\n\n\tIDX++;\n\treturn out;\n}\n", "import { isFunction, isNullOrUndefined } from './checks';\n\nconst hasCrypto = (): boolean =>\n  !isNullOrUndefined(globalThis.crypto) && isFunction(globalThis.crypto.getRandomValues);\n\nexport { hasCrypto };\n", "import { v4 as uuidSecure } from '@lukeed/uuid/secure';\nimport { v4 as uuid } from '@lukeed/uuid';\nimport { hasCrypto } from './crypto';\n\nconst generateUUID = (): string => {\n  if (hasCrypto()) {\n    return uuidSecure();\n  }\n\n  return uuid();\n};\n\nexport { generateUUID };\n", "const CHANNEL = 'web';\n\n// These are the top-level elements in the standard RudderStack event spec\nconst TOP_LEVEL_ELEMENTS = ['integrations', 'anonymousId', 'originalTimestamp'];\n\n// Reserved elements in the context of standard RudderStack event spec\n// Typically, these elements are not allowed to be overridden by the user\nconst CONTEXT_RESERVED_ELEMENTS = ['library', 'consentManagement', 'userAgent', 'ua-ch', 'screen'];\n\n// Reserved elements in the standard RudderStack event spec\nconst RESERVED_ELEMENTS = [\n  'id',\n  'anonymous_id',\n  'user_id',\n  'sent_at',\n  'timestamp',\n  'received_at',\n  'original_timestamp',\n  'event',\n  'event_text',\n  'channel',\n  'context_ip',\n  'context_request_ip',\n  'context_passed_ip',\n  'group_id',\n  'previous_id',\n];\n\nexport { CHANNEL, TOP_LEVEL_ELEMENTS, CONTEXT_RESERVED_ELEMENTS, RESERVED_ELEMENTS };\n", "/**\n * A function to check given value is a number or not\n * @param num input value\n * @returns boolean\n */\nconst isNumber = (num: any): boolean => typeof num === 'number' && !Number.isNaN(num);\n\n/**\n * A function to check given number has minimum length or not\n * @param minimumLength     minimum length\n * @param num               input number\n * @returns boolean\n */\nconst hasMinLength = (minimumLength: number, num: number) => num.toString().length >= minimumLength;\n\n/**\n * A function to check given value is a positive integer or not\n * @param num input value\n * @returns boolean\n */\nconst isPositiveInteger = (num: any) => isNumber(num) && num >= 0 && Number.isInteger(num);\n\nexport { isNumber, hasMinLength, isPositiveInteger };\n", "import type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { SessionInfo } from '@rudderstack/analytics-js-common/types/Session';\nimport { USER_SESSION_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport type { StorageType } from '@rudderstack/analytics-js-common/types/Storage';\nimport {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  MEMORY_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport { generateUUID } from '@rudderstack/analytics-js-common/utilities/uuId';\nimport { DEFAULT_SESSION_TIMEOUT_MS } from '../../constants/timeouts';\nimport { INVALID_SESSION_ID_WARNING } from '../../constants/logMessages';\nimport { hasMinLength, isPositiveInteger } from '../utilities/number';\n\nconst MIN_SESSION_ID_LENGTH = 10;\n\n/**\n * A function to validate current session and return true/false depending on that\n * @returns boolean\n */\nconst hasSessionExpired = (expiresAt?: number): boolean => {\n  const timestamp = Date.now();\n  return Boolean(!expiresAt || timestamp > expiresAt);\n};\n\n/**\n * A function to generate session id\n * @returns number\n */\nconst generateSessionId = (): number => Date.now();\n\n/**\n * Function to validate user provided sessionId\n * @param {number} sessionId\n * @param logger logger\n * @returns\n */\nconst isManualSessionIdValid = (sessionId?: number, logger?: ILogger): boolean => {\n  if (\n    !sessionId ||\n    !isPositiveInteger(sessionId) ||\n    !hasMinLength(MIN_SESSION_ID_LENGTH, sessionId)\n  ) {\n    logger?.warn(\n      INVALID_SESSION_ID_WARNING(USER_SESSION_MANAGER, sessionId, MIN_SESSION_ID_LENGTH),\n    );\n    return false;\n  }\n  return true;\n};\n\n/**\n * A function to generate new auto tracking session\n * @param sessionTimeout current timestamp\n * @returns SessionInfo\n */\nconst generateAutoTrackingSession = (sessionTimeout?: number): SessionInfo => {\n  const timestamp = Date.now();\n  const timeout: number = sessionTimeout || DEFAULT_SESSION_TIMEOUT_MS;\n  return {\n    id: timestamp, // set the current timestamp\n    expiresAt: timestamp + timeout, // set the expiry time of the session\n    timeout,\n    sessionStart: undefined,\n    autoTrack: true,\n  };\n};\n\n/**\n * A function to generate new manual tracking session\n * @param id Provided sessionId\n * @param logger Logger module\n * @returns SessionInfo\n */\nconst generateManualTrackingSession = (id?: number, logger?: ILogger): SessionInfo => {\n  const sessionId: number = isManualSessionIdValid(id, logger)\n    ? (id as number)\n    : generateSessionId();\n  return {\n    id: sessionId,\n    sessionStart: undefined,\n    manualTrack: true,\n  };\n};\n\nconst isStorageTypeValidForStoringData = (storageType: StorageType): boolean =>\n  Boolean(\n    storageType === COOKIE_STORAGE ||\n      storageType === LOCAL_STORAGE ||\n      storageType === SESSION_STORAGE ||\n      storageType === MEMORY_STORAGE,\n  );\n\n/**\n * Generate a new anonymousId\n * @returns string anonymousID\n */\nconst generateAnonymousId = (): string => generateUUID();\n\nexport {\n  hasSessionExpired,\n  generateSessionId,\n  generateAutoTrackingSession,\n  generateManualTrackingSession,\n  MIN_SESSION_ID_LENGTH,\n  isStorageTypeValidForStoringData,\n  generateAnonymousId,\n};\n", "import { clone } from 'ramda';\nimport { isString, isUndefined } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { ApiObject } from '@rudderstack/analytics-js-common/types/ApiObject';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type { ApiOptions } from '@rudderstack/analytics-js-common/types/EventApi';\nimport type { RudderContext, RudderEvent } from '@rudderstack/analytics-js-common/types/Event';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IntegrationOpts } from '@rudderstack/analytics-js-common/types/Integration';\nimport {\n  isObjectLiteralAndNotNull,\n  mergeDeepRight,\n} from '@rudderstack/analytics-js-common/utilities/object';\nimport { EVENT_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { generateUUID } from '@rudderstack/analytics-js-common/utilities/uuId';\nimport { getCurrentTimeFormatted } from '@rudderstack/analytics-js-common/utilities/timestamp';\nimport { NO_STORAGE } from '@rudderstack/analytics-js-common/constants/storages';\nimport { DEFAULT_INTEGRATIONS_CONFIG } from '@rudderstack/analytics-js-common/constants/integrationsConfig';\nimport type { StorageType } from '@rudderstack/analytics-js-common/types/Storage';\nimport { state } from '../../state';\nimport {\n  INVALID_CONTEXT_OBJECT_WARNING,\n  RESERVED_KEYWORD_WARNING,\n} from '../../constants/logMessages';\nimport {\n  CHANNEL,\n  CONTEXT_RESERVED_ELEMENTS,\n  RESERVED_ELEMENTS,\n  TOP_LEVEL_ELEMENTS,\n} from './constants';\nimport { getDefaultPageProperties } from '../utilities/page';\nimport { extractUTMParameters } from '../utilities/url';\nimport { generateAnonymousId, isStorageTypeValidForStoringData } from '../userSessionManager/utils';\n\n/**\n * To get the page properties for context object\n * @param pageProps Page properties\n * @returns page properties object for context\n */\nconst getContextPageProperties = (pageProps?: ApiObject): ApiObject => {\n  // Need to get updated page details on each event as an event to notify on SPA URL changes does not seem to exist\n  const curPageProps = getDefaultPageProperties();\n\n  const ctxPageProps: ApiObject = {};\n  Object.keys(curPageProps).forEach((key: string) => {\n    ctxPageProps[key] = pageProps?.[key] || curPageProps[key];\n  });\n\n  ctxPageProps.initial_referrer =\n    pageProps?.initial_referrer || state.session.initialReferrer.value;\n\n  ctxPageProps.initial_referring_domain =\n    pageProps?.initial_referring_domain || state.session.initialReferringDomain.value;\n  return ctxPageProps;\n};\n\n/**\n * Add any missing default page properties using values from options and defaults\n * @param properties Input page properties\n * @param options API options\n */\nconst getUpdatedPageProperties = (\n  properties: ApiObject,\n  options?: Nullable<ApiOptions>,\n): ApiObject => {\n  const optionsPageProps = ((options as ApiOptions)?.page as ApiObject) || {};\n  const pageProps = properties;\n\n  // Need to get updated page details on each event as an event to notify on SPA URL changes does not seem to exist\n  const curPageProps = getDefaultPageProperties();\n\n  Object.keys(curPageProps).forEach((key: string) => {\n    if (isUndefined(pageProps[key])) {\n      pageProps[key] = optionsPageProps[key] || curPageProps[key];\n    }\n  });\n\n  if (isUndefined(pageProps.initial_referrer)) {\n    pageProps.initial_referrer =\n      optionsPageProps.initial_referrer || state.session.initialReferrer.value;\n  }\n\n  if (isUndefined(pageProps.initial_referring_domain)) {\n    pageProps.initial_referring_domain =\n      optionsPageProps.initial_referring_domain || state.session.initialReferringDomain.value;\n  }\n\n  return pageProps;\n};\n\n/**\n * Utility to check for reserved keys in the input object\n * @param obj Generic object\n * @param parentKeyPath Object's parent key path\n * @param logger Logger instance\n */\nconst checkForReservedElementsInObject = (\n  obj: Nullable<ApiObject> | RudderContext | undefined,\n  parentKeyPath: string,\n  logger?: ILogger,\n): void => {\n  if (isObjectLiteralAndNotNull(obj)) {\n    Object.keys(obj as object).forEach(property => {\n      if (\n        RESERVED_ELEMENTS.includes(property) ||\n        RESERVED_ELEMENTS.includes(property.toLowerCase())\n      ) {\n        logger?.warn(\n          RESERVED_KEYWORD_WARNING(EVENT_MANAGER, property, parentKeyPath, RESERVED_ELEMENTS),\n        );\n      }\n    });\n  }\n};\n\n/**\n * Checks for reserved keys in traits, properties, and contextual traits\n * @param rudderEvent Generated rudder event\n * @param logger Logger instance\n */\nconst checkForReservedElements = (rudderEvent: RudderEvent, logger?: ILogger): void => {\n  //  properties, traits, contextualTraits are either undefined or object\n  const { properties, traits, context } = rudderEvent;\n  const { traits: contextualTraits } = context;\n\n  checkForReservedElementsInObject(properties, 'properties', logger);\n  checkForReservedElementsInObject(traits, 'traits', logger);\n  checkForReservedElementsInObject(contextualTraits, 'context.traits', logger);\n};\n\n/**\n * Overrides the top-level event properties with data from API options\n * @param rudderEvent Generated rudder event\n * @param options API options\n */\nconst updateTopLevelEventElements = (rudderEvent: RudderEvent, options: ApiOptions): void => {\n  if (options.anonymousId && isString(options.anonymousId)) {\n    // eslint-disable-next-line no-param-reassign\n    rudderEvent.anonymousId = options.anonymousId;\n  }\n\n  if (isObjectLiteralAndNotNull<IntegrationOpts>(options.integrations)) {\n    // eslint-disable-next-line no-param-reassign\n    rudderEvent.integrations = options.integrations;\n  }\n\n  if (options.originalTimestamp && isString(options.originalTimestamp)) {\n    // eslint-disable-next-line no-param-reassign\n    rudderEvent.originalTimestamp = options.originalTimestamp;\n  }\n};\n\n/**\n * To merge the contextual information in API options with existing data\n * @param rudderContext Generated rudder event\n * @param options API options\n * @param logger Logger instance\n */\nconst getMergedContext = (\n  rudderContext: RudderContext,\n  options: ApiOptions,\n  logger?: ILogger,\n): RudderContext => {\n  let context = rudderContext;\n  Object.keys(options).forEach(key => {\n    if (!TOP_LEVEL_ELEMENTS.includes(key) && !CONTEXT_RESERVED_ELEMENTS.includes(key)) {\n      if (key !== 'context') {\n        context = mergeDeepRight(context, {\n          [key]: options[key],\n        });\n      } else if (!isUndefined(options[key]) && isObjectLiteralAndNotNull(options[key])) {\n        const tempContext: Record<string, any> = {};\n        Object.keys(options[key] as Record<string, any>).forEach(e => {\n          if (!CONTEXT_RESERVED_ELEMENTS.includes(e)) {\n            tempContext[e] = (options[key] as Record<string, any>)[e];\n          }\n        });\n        context = mergeDeepRight(context, {\n          ...tempContext,\n        });\n      } else {\n        logger?.warn(INVALID_CONTEXT_OBJECT_WARNING(EVENT_MANAGER));\n      }\n    }\n  });\n  return context;\n};\n\n/**\n * A function to determine whether SDK should use the integration option provided in load call\n * @returns boolean\n */\nconst shouldUseGlobalIntegrationsConfigInEvents = () =>\n  state.loadOptions.value.useGlobalIntegrationsConfigInEvents &&\n  (isObjectLiteralAndNotNull(state.consents.postConsent.value?.integrations) ||\n    isObjectLiteralAndNotNull(state.nativeDestinations.loadOnlyIntegrations.value));\n\n/**\n * Updates rudder event object with data from the API options\n * @param rudderEvent Generated rudder event\n * @param options API options\n */\nconst processOptions = (rudderEvent: RudderEvent, options?: Nullable<ApiOptions>): void => {\n  // Only allow object type for options\n  if (isObjectLiteralAndNotNull(options)) {\n    updateTopLevelEventElements(rudderEvent, options as ApiOptions);\n    // eslint-disable-next-line no-param-reassign\n    rudderEvent.context = getMergedContext(rudderEvent.context, options as ApiOptions);\n  }\n};\n\n/**\n * Returns the final integrations config for the event based on the global config and event's config\n * @param integrationsConfig Event's integrations config\n * @returns Final integrations config\n */\nconst getEventIntegrationsConfig = (integrationsConfig: IntegrationOpts) => {\n  let finalIntgConfig: IntegrationOpts;\n  if (shouldUseGlobalIntegrationsConfigInEvents()) {\n    finalIntgConfig = clone(\n      state.consents.postConsent.value?.integrations ??\n        state.nativeDestinations.loadOnlyIntegrations.value,\n    );\n  } else if (isObjectLiteralAndNotNull(integrationsConfig)) {\n    finalIntgConfig = integrationsConfig;\n  } else {\n    finalIntgConfig = DEFAULT_INTEGRATIONS_CONFIG;\n  }\n  return finalIntgConfig;\n};\n\n/**\n * Enrich the base event object with data from state and the API options\n * @param rudderEvent RudderEvent object\n * @param options API options\n * @param pageProps Page properties\n * @param logger logger\n * @returns Enriched RudderEvent object\n */\nconst getEnrichedEvent = (\n  rudderEvent: Partial<RudderEvent>,\n  options?: Nullable<ApiOptions>,\n  pageProps?: ApiObject,\n  logger?: ILogger,\n): RudderEvent => {\n  const commonEventData = {\n    channel: CHANNEL,\n    context: {\n      traits: clone(state.session.userTraits.value),\n      sessionId: state.session.sessionInfo.value.id || undefined,\n      sessionStart: state.session.sessionInfo.value.sessionStart || undefined,\n      // Add 'consentManagement' only if consent management is enabled\n      ...(state.consents.enabled.value && {\n        consentManagement: {\n          deniedConsentIds: clone(state.consents.data.value.deniedConsentIds),\n          allowedConsentIds: clone(state.consents.data.value.allowedConsentIds),\n          provider: state.consents.provider.value,\n          resolutionStrategy: state.consents.resolutionStrategy.value,\n        },\n      }),\n      'ua-ch': state.context['ua-ch'].value,\n      app: state.context.app.value,\n      library: state.context.library.value,\n      userAgent: state.context.userAgent.value,\n      os: state.context.os.value,\n      locale: state.context.locale.value,\n      screen: state.context.screen.value,\n      campaign: extractUTMParameters(globalThis.location.href),\n      page: getContextPageProperties(pageProps),\n      timezone: state.context.timezone.value,\n    },\n    originalTimestamp: getCurrentTimeFormatted(),\n    integrations: DEFAULT_INTEGRATIONS_CONFIG,\n    messageId: generateUUID(),\n    userId: rudderEvent.userId || state.session.userId.value,\n  } as Partial<RudderEvent>;\n\n  if (\n    !isStorageTypeValidForStoringData(state.storage.entries.value.anonymousId?.type as StorageType)\n  ) {\n    // Generate new anonymous id for each request\n    commonEventData.anonymousId = generateAnonymousId();\n  } else {\n    // Type casting to string as the user session manager will take care of initializing the value\n    commonEventData.anonymousId = state.session.anonymousId.value as string;\n  }\n\n  // set truly anonymous tracking flag\n  if (state.storage.trulyAnonymousTracking.value) {\n    (commonEventData.context as RudderContext).trulyAnonymousTracking = true;\n  }\n\n  if (rudderEvent.type === 'identify') {\n    (commonEventData.context as RudderContext).traits =\n      state.storage.entries.value.userTraits?.type !== NO_STORAGE\n        ? clone(state.session.userTraits.value)\n        : (rudderEvent.context as RudderContext).traits;\n  }\n\n  if (rudderEvent.type === 'group') {\n    if (rudderEvent.groupId || state.session.groupId.value) {\n      commonEventData.groupId = rudderEvent.groupId || state.session.groupId.value;\n    }\n\n    if (rudderEvent.traits || state.session.groupTraits.value) {\n      commonEventData.traits =\n        state.storage.entries.value.groupTraits?.type !== NO_STORAGE\n          ? clone(state.session.groupTraits.value)\n          : rudderEvent.traits;\n    }\n  }\n\n  const processedEvent = mergeDeepRight(rudderEvent, commonEventData) as RudderEvent;\n  // Set the default values for the event properties\n  // matching with v1.1 payload\n  if (processedEvent.event === undefined) {\n    processedEvent.event = null;\n  }\n\n  if (processedEvent.properties === undefined) {\n    processedEvent.properties = null;\n  }\n\n  processOptions(processedEvent, options);\n  checkForReservedElements(processedEvent, logger);\n\n  // Update the integrations config for the event\n  processedEvent.integrations = getEventIntegrationsConfig(processedEvent.integrations);\n\n  return processedEvent;\n};\n\nexport {\n  getUpdatedPageProperties,\n  getEnrichedEvent,\n  checkForReservedElements,\n  checkForReservedElementsInObject,\n  updateTopLevelEventElements,\n  getContextPageProperties,\n  getMergedContext,\n  processOptions,\n};\n", "/**\n * To get the current timestamp in ISO string format\n * @returns ISO formatted timestamp string\n */\nconst getCurrentTimeFormatted = (): string => {\n  const curDateTime = new Date().toISOString();\n  return curDateTime;\n};\n\nexport { getCurrentTimeFormatted };\n", "import type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type { ApiObject } from '@rudderstack/analytics-js-common/types/ApiObject';\nimport type { APIEvent, ApiOptions } from '@rudderstack/analytics-js-common/types/EventApi';\nimport type { RudderContext, RudderEvent } from '@rudderstack/analytics-js-common/types/Event';\nimport { getEnrichedEvent, getUpdatedPageProperties } from './utilities';\n\nclass RudderEventFactory {\n  logger?: ILogger;\n\n  constructor(logger?: ILogger) {\n    this.logger = logger;\n  }\n\n  /**\n   * Generate a 'page' event based on the user-input fields\n   * @param category Page's category\n   * @param name Page name\n   * @param properties Page properties\n   * @param options API options\n   */\n  generatePageEvent(\n    category?: Nullable<string>,\n    name?: Nullable<string>,\n    properties?: Nullable<ApiObject>,\n    options?: Nullable<ApiOptions>,\n  ): RudderEvent {\n    let props = properties ?? {};\n\n    props.name = name;\n    props.category = category;\n\n    props = getUpdatedPageProperties(props, options);\n\n    const pageEvent: Partial<RudderEvent> = {\n      properties: props,\n      name,\n      category,\n      type: 'page',\n    };\n\n    return getEnrichedEvent(pageEvent, options, props, this.logger);\n  }\n\n  /**\n   * Generate a 'track' event based on the user-input fields\n   * @param event The event name\n   * @param properties Event properties\n   * @param options API options\n   */\n  generateTrackEvent(\n    event: string,\n    properties?: Nullable<ApiObject>,\n    options?: Nullable<ApiOptions>,\n  ): RudderEvent {\n    const trackEvent: Partial<RudderEvent> = {\n      properties,\n      event,\n      type: 'track',\n    };\n\n    return getEnrichedEvent(trackEvent, options, undefined, this.logger);\n  }\n\n  /**\n   * Generate an 'identify' event based on the user-input fields\n   * @param userId New user ID\n   * @param traits new traits\n   * @param options API options\n   */\n  generateIdentifyEvent(\n    userId?: Nullable<string>,\n    traits?: Nullable<ApiObject>,\n    options?: Nullable<ApiOptions>,\n  ): RudderEvent {\n    const identifyEvent: Partial<RudderEvent> = {\n      userId,\n      type: 'identify',\n      context: {\n        traits,\n      } as RudderContext,\n    };\n\n    return getEnrichedEvent(identifyEvent, options, undefined, this.logger);\n  }\n\n  /**\n   * Generate an 'alias' event based on the user-input fields\n   * @param to New user ID\n   * @param from Old user ID\n   * @param options API options\n   */\n  generateAliasEvent(\n    to: Nullable<string>,\n    from?: string,\n    options?: Nullable<ApiOptions>,\n  ): RudderEvent {\n    const aliasEvent: Partial<RudderEvent> = {\n      previousId: from,\n      type: 'alias',\n    };\n\n    const enrichedEvent = getEnrichedEvent(aliasEvent, options, undefined, this.logger);\n    // override the User ID from the API inputs\n    enrichedEvent.userId = to ?? enrichedEvent.userId;\n    return enrichedEvent;\n  }\n\n  /**\n   * Generate a 'group' event based on the user-input fields\n   * @param groupId New group ID\n   * @param traits new group traits\n   * @param options API options\n   */\n  generateGroupEvent(\n    groupId?: Nullable<string>,\n    traits?: Nullable<ApiObject>,\n    options?: Nullable<ApiOptions>,\n  ): RudderEvent {\n    const groupEvent: Partial<RudderEvent> = {\n      type: 'group',\n    };\n\n    if (groupId) {\n      groupEvent.groupId = groupId;\n    }\n\n    if (traits) {\n      groupEvent.traits = traits;\n    }\n\n    return getEnrichedEvent(groupEvent, options, undefined, this.logger);\n  }\n\n  /**\n   * Generates a new RudderEvent object based on the user-input fields\n   * @param event API event parameters object\n   * @returns A RudderEvent object\n   */\n  create(event: APIEvent): RudderEvent | undefined {\n    let eventObj: RudderEvent | undefined;\n    switch (event.type) {\n      case 'page':\n        eventObj = this.generatePageEvent(\n          event.category,\n          event.name,\n          event.properties,\n          event.options,\n        );\n        break;\n      case 'track':\n        eventObj = this.generateTrackEvent(event.name as string, event.properties, event.options);\n        break;\n      case 'identify':\n        eventObj = this.generateIdentifyEvent(event.userId, event.traits, event.options);\n        break;\n      case 'alias':\n        eventObj = this.generateAliasEvent(event.to as Nullable<string>, event.from, event.options);\n        break;\n      case 'group':\n        eventObj = this.generateGroupEvent(event.groupId, event.traits, event.options);\n        break;\n      default:\n        // Do nothing\n        break;\n    }\n    return eventObj;\n  }\n}\n\nexport { RudderEventFactory };\n", "import type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IErrorHandler } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { APIEvent } from '@rudderstack/analytics-js-common/types/EventApi';\nimport { EVENT_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { EVENT_OBJECT_GENERATION_ERROR } from '../../constants/logMessages';\nimport type { IEventManager } from './types';\nimport { RudderEventFactory } from './RudderEventFactory';\nimport type { IEventRepository } from '../eventRepository/types';\nimport type { IUserSessionManager } from '../userSessionManager/types';\n\n/**\n * A service to generate valid event payloads and queue them for processing\n */\nclass EventManager implements IEventManager {\n  eventRepository: IEventRepository;\n  userSessionManager: IUserSessionManager;\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n  eventFactory: RudderEventFactory;\n\n  /**\n   *\n   * @param eventRepository Event repository instance\n   * @param userSessionManager UserSession Manager instance\n   * @param errorHandler Error handler object\n   * @param logger Logger object\n   */\n  constructor(\n    eventRepository: IEventRepository,\n    userSessionManager: IUserSessionManager,\n    errorHandler?: IErrorHandler,\n    logger?: ILogger,\n  ) {\n    this.eventRepository = eventRepository;\n    this.userSessionManager = userSessionManager;\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.eventFactory = new RudderEventFactory(this.logger);\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Initializes the event manager\n   */\n  init() {\n    this.eventRepository.init();\n  }\n\n  resume() {\n    this.eventRepository.resume();\n  }\n\n  /**\n   * Consumes a new incoming event\n   * @param event Incoming event data\n   */\n  addEvent(event: APIEvent) {\n    this.userSessionManager.refreshSession();\n    const rudderEvent = this.eventFactory.create(event);\n    if (rudderEvent) {\n      this.eventRepository.enqueue(rudderEvent, event.callback);\n    } else {\n      this.onError(new Error(EVENT_OBJECT_GENERATION_ERROR));\n    }\n  }\n\n  /**\n   * Handles error\n   * @param error The error object\n   */\n  onError(error: unknown, customMessage?: string, shouldAlwaysThrow?: boolean): void {\n    if (this.errorHandler) {\n      this.errorHandler.onError(error, EVENT_MANAGER, customMessage, shouldAlwaysThrow);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { EventManager };\n", "/* eslint-disable class-methods-use-this */\nimport { batch, effect } from '@preact/signals-core';\nimport {\n  isNonEmptyObject,\n  mergeDeepRight,\n} from '@rudderstack/analytics-js-common/utilities/object';\nimport {\n  isDefinedAndNotNull,\n  isDefinedNotNullAndNotEmptyString,\n  isNullOrUndefined,\n  isString,\n} from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { IPluginsManager } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport type { IStore, IStoreManager } from '@rudderstack/analytics-js-common/types/Store';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IErrorHandler } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { SessionInfo } from '@rudderstack/analytics-js-common/types/Session';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type { ApiObject } from '@rudderstack/analytics-js-common/types/ApiObject';\nimport type { AnonymousIdOptions } from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport { USER_SESSION_MANAGER } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport type { StorageType } from '@rudderstack/analytics-js-common/types/Storage';\nimport {\n  COOKIE_STORAGE,\n  LOCAL_STORAGE,\n  SESSION_STORAGE,\n} from '@rudderstack/analytics-js-common/constants/storages';\nimport type { UserSessionKey } from '@rudderstack/analytics-js-common/types/UserSessionStorage';\nimport type { StorageEntries } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport type {\n  AsyncRequestCallback,\n  IHttpClient,\n} from '@rudderstack/analytics-js-common/types/HttpClient';\nimport { stringifyWithoutCircular } from '@rudderstack/analytics-js-common/utilities/json';\nimport {\n  CLIENT_DATA_STORE_COOKIE,\n  CLIENT_DATA_STORE_LS,\n  CLIENT_DATA_STORE_SESSION,\n  USER_SESSION_KEYS,\n} from '../../constants/storage';\nimport { storageClientDataStoreNameMap } from '../../services/StoreManager/types';\nimport { DEFAULT_SESSION_TIMEOUT_MS, MIN_SESSION_TIMEOUT_MS } from '../../constants/timeouts';\nimport { defaultSessionConfiguration } from '../../state/slices/session';\nimport { state } from '../../state';\nimport { getStorageEngine } from '../../services/StoreManager/storages';\nimport {\n  DATA_SERVER_REQUEST_FAIL_ERROR,\n  FAILED_SETTING_COOKIE_FROM_SERVER_ERROR,\n  FAILED_SETTING_COOKIE_FROM_SERVER_GLOBAL_ERROR,\n  TIMEOUT_NOT_NUMBER_WARNING,\n  TIMEOUT_NOT_RECOMMENDED_WARNING,\n  TIMEOUT_ZERO_WARNING,\n} from '../../constants/logMessages';\nimport {\n  generateAnonymousId,\n  generateAutoTrackingSession,\n  generateManualTrackingSession,\n  hasSessionExpired,\n  isStorageTypeValidForStoringData,\n} from './utils';\nimport { getReferringDomain } from '../utilities/url';\nimport { getReferrer } from '../utilities/page';\nimport { DEFAULT_USER_SESSION_VALUES, USER_SESSION_STORAGE_KEYS } from './constants';\nimport type {\n  CallbackFunction,\n  CookieData,\n  EncryptedCookieData,\n  IUserSessionManager,\n  UserSessionStorageKeysType,\n} from './types';\nimport { isPositiveInteger } from '../utilities/number';\n\nclass UserSessionManager implements IUserSessionManager {\n  storeManager?: IStoreManager;\n  pluginsManager?: IPluginsManager;\n  errorHandler?: IErrorHandler;\n  httpClient?: IHttpClient;\n  logger?: ILogger;\n\n  constructor(\n    errorHandler?: IErrorHandler,\n    logger?: ILogger,\n    pluginsManager?: IPluginsManager,\n    storeManager?: IStoreManager,\n    httpClient?: IHttpClient,\n  ) {\n    this.storeManager = storeManager;\n    this.pluginsManager = pluginsManager;\n    this.logger = logger;\n    this.errorHandler = errorHandler;\n    this.httpClient = httpClient;\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Initialize User session with values from storage\n   */\n  init() {\n    this.syncStorageDataToState();\n\n    // Register the effect to sync with storage\n    this.registerEffects();\n  }\n\n  syncStorageDataToState() {\n    this.migrateStorageIfNeeded();\n    this.migrateDataFromPreviousStorage();\n\n    // get the values from storage and set it again\n    this.setUserId(this.getUserId());\n    this.setUserTraits(this.getUserTraits());\n    this.setGroupId(this.getGroupId());\n    this.setGroupTraits(this.getGroupTraits());\n    const { externalAnonymousIdCookieName, anonymousIdOptions } = state.loadOptions.value;\n    let externalAnonymousId;\n    if (\n      isDefinedAndNotNull(externalAnonymousIdCookieName) &&\n      typeof externalAnonymousIdCookieName === 'string'\n    ) {\n      externalAnonymousId = this.getExternalAnonymousIdByCookieName(externalAnonymousIdCookieName);\n    }\n    this.setAnonymousId(externalAnonymousId ?? this.getAnonymousId(anonymousIdOptions));\n    this.setAuthToken(this.getAuthToken());\n    this.setInitialReferrerInfo();\n    this.configureSessionTracking();\n  }\n\n  configureSessionTracking() {\n    let sessionInfo = this.getSessionInfo();\n    if (this.isPersistenceEnabledForStorageEntry('sessionInfo')) {\n      const configuredSessionTrackingInfo = this.getConfiguredSessionTrackingInfo();\n      const initialSessionInfo = sessionInfo ?? defaultSessionConfiguration;\n      sessionInfo = {\n        ...initialSessionInfo,\n        ...configuredSessionTrackingInfo,\n        autoTrack:\n          configuredSessionTrackingInfo.autoTrack && initialSessionInfo.manualTrack !== true,\n      };\n    }\n\n    state.session.sessionInfo.value = this.isPersistenceEnabledForStorageEntry('sessionInfo')\n      ? (sessionInfo as SessionInfo)\n      : DEFAULT_USER_SESSION_VALUES.sessionInfo;\n\n    // If auto session tracking is enabled start the session tracking\n    if (state.session.sessionInfo.value.autoTrack) {\n      this.startOrRenewAutoTracking(state.session.sessionInfo.value);\n    }\n  }\n\n  setInitialReferrerInfo() {\n    const persistedInitialReferrer = this.getInitialReferrer();\n    const persistedInitialReferringDomain = this.getInitialReferringDomain();\n\n    if (persistedInitialReferrer && persistedInitialReferringDomain) {\n      this.setInitialReferrer(persistedInitialReferrer);\n      this.setInitialReferringDomain(persistedInitialReferringDomain);\n    } else {\n      const initialReferrer = persistedInitialReferrer || getReferrer();\n      this.setInitialReferrer(initialReferrer);\n      this.setInitialReferringDomain(getReferringDomain(initialReferrer));\n    }\n  }\n\n  isPersistenceEnabledForStorageEntry(entryName: UserSessionKey): boolean {\n    return isStorageTypeValidForStoringData(\n      state.storage.entries.value[entryName]?.type as StorageType,\n    );\n  }\n\n  migrateDataFromPreviousStorage() {\n    const entries = state.storage.entries.value as StorageEntries;\n    const storageTypesForMigration = [COOKIE_STORAGE, LOCAL_STORAGE, SESSION_STORAGE];\n    Object.keys(entries).forEach(entry => {\n      const key = entry as UserSessionStorageKeysType;\n      const currentStorage = entries[key]?.type as StorageType;\n      const curStore = this.storeManager?.getStore(\n        storageClientDataStoreNameMap[currentStorage] as string,\n      );\n      if (curStore) {\n        storageTypesForMigration.forEach(storage => {\n          const store = this.storeManager?.getStore(\n            storageClientDataStoreNameMap[storage] as string,\n          );\n          if (store && storage !== currentStorage) {\n            const value = store.get(USER_SESSION_STORAGE_KEYS[key]);\n            if (isDefinedNotNullAndNotEmptyString(value)) {\n              curStore.set(USER_SESSION_STORAGE_KEYS[key], value);\n            }\n\n            store.remove(USER_SESSION_STORAGE_KEYS[key]);\n          }\n        });\n      }\n    });\n  }\n\n  migrateStorageIfNeeded() {\n    if (!state.storage.migrate.value) {\n      return;\n    }\n\n    const persistentStoreNames = [\n      CLIENT_DATA_STORE_COOKIE,\n      CLIENT_DATA_STORE_LS,\n      CLIENT_DATA_STORE_SESSION,\n    ];\n\n    const stores: IStore[] = [];\n    persistentStoreNames.forEach(storeName => {\n      const store = this.storeManager?.getStore(storeName);\n      if (store) {\n        stores.push(store);\n      }\n    });\n\n    Object.keys(USER_SESSION_STORAGE_KEYS).forEach(storageKey => {\n      const storageEntry = USER_SESSION_STORAGE_KEYS[storageKey as UserSessionStorageKeysType];\n      stores.forEach(store => {\n        const migratedVal = this.pluginsManager?.invokeSingle(\n          'storage.migrate',\n          storageEntry,\n          store.engine,\n          this.errorHandler,\n          this.logger,\n        );\n\n        // Skip setting the value if it is null or undefined\n        // as those values indicate there is no need for migration or\n        // migration failed\n        if (!isNullOrUndefined(migratedVal)) {\n          store.set(storageEntry, migratedVal);\n        }\n      });\n    });\n  }\n\n  getConfiguredSessionTrackingInfo(): SessionInfo {\n    let autoTrack = state.loadOptions.value.sessions?.autoTrack !== false;\n\n    // Do not validate any further if autoTrack is disabled\n    if (!autoTrack) {\n      return {\n        autoTrack,\n      };\n    }\n\n    let timeout: number;\n    const configuredSessionTimeout = state.loadOptions.value.sessions?.timeout;\n    if (!isPositiveInteger(configuredSessionTimeout)) {\n      this.logger?.warn(\n        TIMEOUT_NOT_NUMBER_WARNING(\n          USER_SESSION_MANAGER,\n          configuredSessionTimeout,\n          DEFAULT_SESSION_TIMEOUT_MS,\n        ),\n      );\n      timeout = DEFAULT_SESSION_TIMEOUT_MS;\n    } else {\n      timeout = configuredSessionTimeout as number;\n    }\n\n    if (timeout === 0) {\n      this.logger?.warn(TIMEOUT_ZERO_WARNING(USER_SESSION_MANAGER));\n      autoTrack = false;\n    }\n    // In case user provides a timeout value greater than 0 but less than 10 seconds SDK will show a warning\n    // and will proceed with it\n    if (timeout > 0 && timeout < MIN_SESSION_TIMEOUT_MS) {\n      this.logger?.warn(\n        TIMEOUT_NOT_RECOMMENDED_WARNING(USER_SESSION_MANAGER, timeout, MIN_SESSION_TIMEOUT_MS),\n      );\n    }\n    return { timeout, autoTrack };\n  }\n\n  /**\n   * Handles error\n   * @param error The error object\n   */\n  onError(error: unknown, customMessage?: string): void {\n    if (this.errorHandler) {\n      this.errorHandler.onError(error, USER_SESSION_MANAGER, customMessage);\n    } else {\n      throw error;\n    }\n  }\n\n  /**\n   * A function to encrypt the cookie value and return the encrypted data\n   * @param cookieData\n   * @param store\n   * @returns\n   */\n  getEncryptedCookieData(cookieData: CookieData[], store?: IStore): EncryptedCookieData[] {\n    const encryptedCookieData: EncryptedCookieData[] = [];\n    cookieData.forEach(e => {\n      const encryptedValue = store?.encrypt(\n        stringifyWithoutCircular(e.value, false, [], this.logger),\n      );\n      if (isDefinedAndNotNull(encryptedValue)) {\n        encryptedCookieData.push({\n          name: e.name,\n          value: encryptedValue,\n        });\n      }\n    });\n    return encryptedCookieData;\n  }\n\n  /**\n   * A function that makes request to data service to set the cookie\n   * @param encryptedCookieData\n   * @param callback\n   */\n  makeRequestToSetCookie(\n    encryptedCookieData: EncryptedCookieData[],\n    callback: AsyncRequestCallback<any>,\n  ) {\n    this.httpClient?.getAsyncData({\n      url: state.serverCookies.dataServiceUrl.value as string,\n      options: {\n        method: 'POST',\n        data: stringifyWithoutCircular({\n          reqType: 'setCookies',\n          workspaceId: state.source.value?.workspaceId,\n          data: {\n            options: {\n              maxAge: state.storage.cookie.value?.maxage,\n              path: state.storage.cookie.value?.path,\n              domain: state.storage.cookie.value?.domain,\n              sameSite: state.storage.cookie.value?.samesite,\n              secure: state.storage.cookie.value?.secure,\n              expires: state.storage.cookie.value?.expires,\n            },\n            cookies: encryptedCookieData,\n          },\n        }) as string,\n        sendRawData: true,\n        withCredentials: true,\n      },\n      isRawResponse: true,\n      callback,\n    });\n  }\n\n  /**\n   * A function to make an external request to set the cookie from server side\n   * @param key       cookie name\n   * @param value     encrypted cookie value\n   */\n  setServerSideCookie(cookieData: CookieData[], cb?: CallbackFunction, store?: IStore): void {\n    try {\n      // encrypt cookies values\n      const encryptedCookieData = this.getEncryptedCookieData(cookieData, store);\n      if (encryptedCookieData.length > 0) {\n        // make request to data service to set the cookie from server side\n        this.makeRequestToSetCookie(encryptedCookieData, (res, details) => {\n          if (details?.xhr?.status === 200) {\n            cookieData.forEach(each => {\n              const cookieValue = store?.get(each.name);\n              const before = stringifyWithoutCircular(each.value, false, []);\n              const after = stringifyWithoutCircular(cookieValue, false, []);\n              if (after !== before) {\n                this.logger?.debug('Cookie value sent to server side', before);\n                this.logger?.debug('Cookie value set from server side', after);\n                this.logger?.error(FAILED_SETTING_COOKIE_FROM_SERVER_ERROR(each.name));\n                if (cb) {\n                  cb(each.name, each.value);\n                }\n              }\n            });\n          } else {\n            this.logger?.error(DATA_SERVER_REQUEST_FAIL_ERROR(details?.xhr?.status));\n            cookieData.forEach(each => {\n              if (cb) {\n                cb(each.name, each.value);\n              }\n            });\n          }\n        });\n      }\n    } catch (e) {\n      this.onError(e, FAILED_SETTING_COOKIE_FROM_SERVER_GLOBAL_ERROR);\n      cookieData.forEach(each => {\n        if (cb) {\n          cb(each.name, each.value);\n        }\n      });\n    }\n  }\n\n  /**\n   * A function to sync values in storage\n   * @param sessionKey\n   * @param value\n   */\n  syncValueToStorage(\n    sessionKey: UserSessionKey,\n    value: Nullable<ApiObject> | Nullable<string> | undefined,\n  ) {\n    const entries = state.storage.entries.value;\n    const storageType = entries[sessionKey]?.type as StorageType;\n    if (isStorageTypeValidForStoringData(storageType)) {\n      const curStore = this.storeManager?.getStore(\n        storageClientDataStoreNameMap[storageType] as string,\n      );\n      const key = entries[sessionKey]?.key as string;\n      if (value && (isString(value) || isNonEmptyObject(value))) {\n        // if useServerSideCookies load option is set to true\n        // set the cookie from server side\n        if (\n          state.serverCookies.isEnabledServerSideCookies.value &&\n          storageType === COOKIE_STORAGE\n        ) {\n          this.setServerSideCookie(\n            [{ name: key, value }],\n            (cookieName, cookieValue) => {\n              curStore?.set(cookieName, cookieValue);\n            },\n            curStore,\n          );\n        } else {\n          curStore?.set(key, value);\n        }\n      } else {\n        curStore?.remove(key);\n      }\n    }\n  }\n\n  /**\n   * Function to update storage whenever state value changes\n   */\n  registerEffects() {\n    // This will work as long as the user session entry key names are same as the state keys\n    USER_SESSION_KEYS.forEach(sessionKey => {\n      effect(() => {\n        this.syncValueToStorage(sessionKey, state.session[sessionKey].value);\n      });\n    });\n  }\n\n  /**\n   * Sets anonymous id in the following precedence:\n   *\n   * 1. anonymousId: Id directly provided to the function.\n   * 2. rudderAmpLinkerParam: value generated from linker query parm (rudderstack)\n   *    using parseLinker util.\n   * 3. generateUUID: A new unique id is generated and assigned.\n   */\n  setAnonymousId(anonymousId?: string, rudderAmpLinkerParam?: string) {\n    let finalAnonymousId: string | undefined | null = anonymousId;\n    if (this.isPersistenceEnabledForStorageEntry('anonymousId')) {\n      if (!finalAnonymousId && rudderAmpLinkerParam) {\n        const linkerPluginsResult = this.pluginsManager?.invokeSingle(\n          'userSession.anonymousIdGoogleLinker',\n          rudderAmpLinkerParam,\n        );\n        finalAnonymousId = linkerPluginsResult;\n      }\n      finalAnonymousId = finalAnonymousId || generateAnonymousId();\n    } else {\n      finalAnonymousId = DEFAULT_USER_SESSION_VALUES.anonymousId;\n    }\n\n    state.session.anonymousId.value = finalAnonymousId;\n  }\n\n  /**\n   * Fetches anonymousId\n   * @param options option to fetch it from external source\n   * @returns anonymousId\n   */\n  getAnonymousId(options?: AnonymousIdOptions): string {\n    const storage: StorageType = state.storage.entries.value.anonymousId?.type as StorageType;\n    // fetch the anonymousId from storage\n    if (isStorageTypeValidForStoringData(storage)) {\n      let persistedAnonymousId = this.getEntryValue('anonymousId');\n      if (!persistedAnonymousId && options) {\n        // fetch anonymousId from external source\n        const autoCapturedAnonymousId = this.pluginsManager?.invokeSingle<string | undefined>(\n          'storage.getAnonymousId',\n          getStorageEngine,\n          options,\n        );\n        persistedAnonymousId = autoCapturedAnonymousId;\n      }\n      state.session.anonymousId.value = persistedAnonymousId || generateAnonymousId();\n    }\n    return state.session.anonymousId.value as string;\n  }\n\n  getEntryValue(sessionKey: UserSessionKey) {\n    const entries = state.storage.entries.value;\n    const storageType = entries[sessionKey]?.type as StorageType;\n    if (isStorageTypeValidForStoringData(storageType)) {\n      const store = this.storeManager?.getStore(\n        storageClientDataStoreNameMap[storageType] as string,\n      );\n      const storageKey = entries[sessionKey]?.key as string;\n      return store?.get(storageKey) ?? null;\n    }\n    return null;\n  }\n\n  getExternalAnonymousIdByCookieName(key: string) {\n    const storageEngine = getStorageEngine(COOKIE_STORAGE);\n    if (storageEngine?.isEnabled) {\n      return storageEngine.getItem(key) ?? null;\n    }\n    return null;\n  }\n\n  /**\n   * Fetches User Id\n   * @returns\n   */\n  getUserId(): Nullable<string> {\n    return this.getEntryValue('userId');\n  }\n\n  /**\n   * Fetches User Traits\n   * @returns\n   */\n  getUserTraits(): Nullable<ApiObject> {\n    return this.getEntryValue('userTraits');\n  }\n\n  /**\n   * Fetches Group Id\n   * @returns\n   */\n  getGroupId(): Nullable<string> {\n    return this.getEntryValue('groupId');\n  }\n\n  /**\n   * Fetches Group Traits\n   * @returns\n   */\n  getGroupTraits(): Nullable<ApiObject> {\n    return this.getEntryValue('groupTraits');\n  }\n\n  /**\n   * Fetches Initial Referrer\n   * @returns\n   */\n  getInitialReferrer(): Nullable<string> {\n    return this.getEntryValue('initialReferrer');\n  }\n\n  /**\n   * Fetches Initial Referring domain\n   * @returns\n   */\n  getInitialReferringDomain(): Nullable<string> {\n    return this.getEntryValue('initialReferringDomain');\n  }\n\n  /**\n   * Fetches session tracking information from storage\n   * @returns\n   */\n  getSessionInfo(): Nullable<SessionInfo> {\n    return this.getEntryValue('sessionInfo');\n  }\n\n  /**\n   * Fetches auth token from storage\n   * @returns\n   */\n  getAuthToken(): Nullable<string> {\n    return this.getEntryValue('authToken');\n  }\n\n  /**\n   * If session is active it returns the sessionId\n   * @returns\n   */\n  getSessionId(): Nullable<number> {\n    const sessionInfo = this.getSessionInfo() ?? DEFAULT_USER_SESSION_VALUES.sessionInfo;\n    if (\n      (sessionInfo.autoTrack && !hasSessionExpired(sessionInfo.expiresAt)) ||\n      sessionInfo.manualTrack\n    ) {\n      return sessionInfo.id ?? null;\n    }\n    return null;\n  }\n\n  /**\n   * A function to keep the session information up to date in the state\n   * before using it for building event payloads.\n   */\n  refreshSession(): void {\n    let sessionInfo = this.getSessionInfo() ?? DEFAULT_USER_SESSION_VALUES.sessionInfo;\n    if (sessionInfo.autoTrack || sessionInfo.manualTrack) {\n      if (sessionInfo.autoTrack) {\n        this.startOrRenewAutoTracking(sessionInfo);\n        sessionInfo = state.session.sessionInfo.value;\n      }\n\n      // Note that if sessionStart is false, then it's an active session.\n      // So, we needn't update the session info.\n      //\n      // For other scenarios,\n      // 1. If sessionStart is undefined, then it's a new session.\n      //   Mark it as sessionStart.\n      // 2. If sessionStart is true, then need to flip it for the future events.\n      if (sessionInfo.sessionStart === undefined) {\n        sessionInfo = {\n          ...sessionInfo,\n          sessionStart: true,\n        };\n      } else if (sessionInfo.sessionStart) {\n        sessionInfo = {\n          ...sessionInfo,\n          sessionStart: false,\n        };\n      }\n    }\n\n    // Always write to state (in-turn to storage) to keep the session info up to date.\n    state.session.sessionInfo.value = sessionInfo;\n\n    if (state.lifecycle.status.value !== 'readyExecuted') {\n      // Force update the storage as the 'effect' blocks are not getting triggered\n      // when processing preload buffered requests\n      this.syncValueToStorage('sessionInfo', sessionInfo);\n    }\n  }\n\n  /**\n   * Reset state values\n   * @param resetAnonymousId\n   * @param noNewSessionStart\n   * @returns\n   */\n  reset(resetAnonymousId?: boolean, noNewSessionStart?: boolean) {\n    const { session } = state;\n    const { manualTrack, autoTrack } = session.sessionInfo.value;\n\n    batch(() => {\n      session.userId.value = DEFAULT_USER_SESSION_VALUES.userId;\n      session.userTraits.value = DEFAULT_USER_SESSION_VALUES.userTraits;\n      session.groupId.value = DEFAULT_USER_SESSION_VALUES.groupId;\n      session.groupTraits.value = DEFAULT_USER_SESSION_VALUES.groupTraits;\n      session.authToken.value = DEFAULT_USER_SESSION_VALUES.authToken;\n\n      if (resetAnonymousId) {\n        // This will generate a new anonymous ID\n        this.setAnonymousId();\n      }\n\n      if (noNewSessionStart) {\n        return;\n      }\n\n      if (autoTrack) {\n        session.sessionInfo.value = DEFAULT_USER_SESSION_VALUES.sessionInfo;\n        this.startOrRenewAutoTracking(session.sessionInfo.value);\n      } else if (manualTrack) {\n        this.startManualTrackingInternal();\n      }\n    });\n  }\n\n  /**\n   * Set user Id\n   * @param userId\n   */\n  setUserId(userId?: Nullable<string>) {\n    state.session.userId.value =\n      this.isPersistenceEnabledForStorageEntry('userId') && userId\n        ? userId\n        : DEFAULT_USER_SESSION_VALUES.userId;\n  }\n\n  /**\n   * Set user traits\n   * @param traits\n   */\n  setUserTraits(traits?: Nullable<ApiObject>) {\n    state.session.userTraits.value =\n      this.isPersistenceEnabledForStorageEntry('userTraits') && traits\n        ? mergeDeepRight(\n            state.session.userTraits.value ?? DEFAULT_USER_SESSION_VALUES.userTraits,\n            traits,\n          )\n        : DEFAULT_USER_SESSION_VALUES.userTraits;\n  }\n\n  /**\n   * Set group Id\n   * @param groupId\n   */\n  setGroupId(groupId?: Nullable<string>) {\n    state.session.groupId.value =\n      this.isPersistenceEnabledForStorageEntry('groupId') && groupId\n        ? groupId\n        : DEFAULT_USER_SESSION_VALUES.groupId;\n  }\n\n  /**\n   * Set group traits\n   * @param traits\n   */\n  setGroupTraits(traits?: Nullable<ApiObject>) {\n    state.session.groupTraits.value =\n      this.isPersistenceEnabledForStorageEntry('groupTraits') && traits\n        ? mergeDeepRight(\n            state.session.groupTraits.value ?? DEFAULT_USER_SESSION_VALUES.groupTraits,\n            traits,\n          )\n        : DEFAULT_USER_SESSION_VALUES.groupTraits;\n  }\n\n  /**\n   * Set initial referrer\n   * @param referrer\n   */\n  setInitialReferrer(referrer?: string) {\n    state.session.initialReferrer.value =\n      this.isPersistenceEnabledForStorageEntry('initialReferrer') && referrer\n        ? referrer\n        : DEFAULT_USER_SESSION_VALUES.initialReferrer;\n  }\n\n  /**\n   * Set initial referring domain\n   * @param {String} referringDomain\n   */\n  setInitialReferringDomain(referringDomain?: string) {\n    state.session.initialReferringDomain.value =\n      this.isPersistenceEnabledForStorageEntry('initialReferringDomain') && referringDomain\n        ? referringDomain\n        : DEFAULT_USER_SESSION_VALUES.initialReferringDomain;\n  }\n\n  /**\n   * A function to check for existing session details and depending on that create a new session\n   */\n  startOrRenewAutoTracking(sessionInfo: SessionInfo) {\n    if (hasSessionExpired(sessionInfo.expiresAt)) {\n      state.session.sessionInfo.value = generateAutoTrackingSession(sessionInfo.timeout);\n    } else {\n      const timestamp = Date.now();\n      const timeout = sessionInfo.timeout as number;\n      state.session.sessionInfo.value = mergeDeepRight(sessionInfo, {\n        expiresAt: timestamp + timeout, // set the expiry time of the session\n      });\n    }\n  }\n\n  /**\n   * A function method to start a manual session\n   * @param {number} id     session identifier\n   * @returns\n   */\n  start(id?: number) {\n    state.session.sessionInfo.value = generateManualTrackingSession(id, this.logger);\n  }\n\n  /**\n   * An internal function to start manual session\n   */\n  startManualTrackingInternal() {\n    this.start(Date.now());\n  }\n\n  /**\n   * A public method to end an ongoing session.\n   */\n  end() {\n    state.session.sessionInfo.value = DEFAULT_USER_SESSION_VALUES.sessionInfo;\n  }\n\n  /**\n   * Set auth token\n   * @param userId\n   */\n  setAuthToken(token: Nullable<string>) {\n    state.session.authToken.value =\n      this.isPersistenceEnabledForStorageEntry('authToken') && token\n        ? token\n        : DEFAULT_USER_SESSION_VALUES.authToken;\n  }\n}\n\nexport { UserSessionManager };\n", "import type { PluginName } from '@rudderstack/analytics-js-common/types/PluginsManager';\n\n/**\n * Plugins to be loaded in the plugins loadOption is not defined\n */\nconst defaultOptionalPluginsList: PluginName[] = [\n  'BeaconQueue',\n  'Bugsnag',\n  'CustomConsentManager',\n  'DeviceModeDestinations',\n  'DeviceModeTransformation',\n  'ErrorReporting',\n  'ExternalAnonymousId',\n  'GoogleLinker',\n  'KetchConsentManager',\n  'NativeDestinationQueue',\n  'OneTrustConsentManager',\n  'StorageEncryption',\n  'StorageEncryptionLegacy',\n  'StorageMigrator',\n  'XhrQueue',\n];\n\nexport { defaultOptionalPluginsList };\n", "const DATA_PLANE_QUEUE_EXT_POINT_PREFIX = 'dataplaneEventsQueue';\nconst DESTINATIONS_QUEUE_EXT_POINT_PREFIX = 'destinationsEventsQueue';\nconst DMT_EXT_POINT_PREFIX = 'transformEvent';\n\nexport {\n  DATA_PLANE_QUEUE_EXT_POINT_PREFIX,\n  DESTINATIONS_QUEUE_EXT_POINT_PREFIX,\n  DMT_EXT_POINT_PREFIX,\n};\n", "import type { IntegrationOpts } from '@rudderstack/analytics-js-common/types/Integration';\nimport { clone } from 'ramda';\nimport { mergeDeepRight } from '@rudderstack/analytics-js-common/utilities/object';\nimport type { ApplicationState } from '@rudderstack/analytics-js-common/types/ApplicationState';\nimport { DEFAULT_INTEGRATIONS_CONFIG } from '@rudderstack/analytics-js-common/constants/integrationsConfig';\nimport type { RudderEvent } from '@rudderstack/analytics-js-common/types/Event';\n\n/**\n * Filters and returns the user supplied integrations config that should take preference over the destination specific integrations config\n * @param eventIntgConfig User supplied integrations config at event level\n * @param destinationsIntgConfig Cumulative integrations config from all destinations\n * @returns Filtered user supplied integrations config\n */\nconst getOverriddenIntegrationOptions = (\n  eventIntgConfig: IntegrationOpts,\n  destinationsIntgConfig: IntegrationOpts,\n): IntegrationOpts =>\n  Object.keys(eventIntgConfig)\n    .filter(intgName => eventIntgConfig[intgName] !== true || !destinationsIntgConfig[intgName])\n    .reduce((obj: IntegrationOpts, key: string) => {\n      const retVal = clone(obj);\n      retVal[key] = eventIntgConfig[key];\n      return retVal;\n    }, {});\n\n/**\n * Returns the event object with final integrations config\n * @param event RudderEvent object\n * @param state Application state\n * @returns Mutated event with final integrations config\n */\nconst getFinalEvent = (event: RudderEvent, state: ApplicationState) => {\n  const finalEvent = clone(event);\n  // Merge the destination specific integrations config with the event's integrations config\n  // In general, the preference is given to the event's integrations config\n  const eventIntgConfig = event.integrations ?? DEFAULT_INTEGRATIONS_CONFIG;\n  const destinationsIntgConfig = state.nativeDestinations.integrationsConfig.value;\n  const overriddenIntgOpts = getOverriddenIntegrationOptions(\n    eventIntgConfig,\n    destinationsIntgConfig,\n  );\n\n  finalEvent.integrations = mergeDeepRight(destinationsIntgConfig, overriddenIntgOpts);\n  return finalEvent;\n};\n\nconst shouldBufferEventsForPreConsent = (state: ApplicationState): boolean =>\n  state.consents.preConsent.value.enabled &&\n  state.consents.preConsent.value.events?.delivery === 'buffer' &&\n  (state.consents.preConsent.value.storage?.strategy === 'session' ||\n    state.consents.preConsent.value.storage?.strategy === 'none');\n\nexport { getOverriddenIntegrationOptions, getFinalEvent, shouldBufferEventsForPreConsent };\n", "import { clone } from 'ramda';\nimport { effect } from '@preact/signals-core';\nimport type { IHttpClient } from '@rudderstack/analytics-js-common/types/HttpClient';\nimport type { IStoreManager } from '@rudderstack/analytics-js-common/types/Store';\nimport type { IPluginsManager } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport type { I<PERSON><PERSON>r<PERSON>and<PERSON> } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { RudderEvent } from '@rudderstack/analytics-js-common/types/Event';\nimport type { ApiCallback } from '@rudderstack/analytics-js-common/types/EventApi';\nimport { isHybridModeDestination } from '@rudderstack/analytics-js-common/utilities/destinations';\nimport { EVENT_REPOSITORY } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport type { Destination } from '@rudderstack/analytics-js-common/types/Destination';\nimport {\n  API_CALLBACK_INVOKE_ERROR,\n  DATAPLANE_PLUGIN_ENQUEUE_ERROR,\n  DATAPLANE_PLUGIN_INITIALIZE_ERROR,\n  DMT_PLUGIN_INITIALIZE_ERROR,\n  NATIVE_DEST_PLUGIN_ENQUEUE_ERROR,\n  NATIVE_DEST_PLUGIN_INITIALIZE_ERROR,\n} from '../../constants/logMessages';\nimport { HttpClient } from '../../services/HttpClient';\nimport { state } from '../../state';\nimport type { IEventRepository } from './types';\nimport {\n  DATA_PLANE_QUEUE_EXT_POINT_PREFIX,\n  DESTINATIONS_QUEUE_EXT_POINT_PREFIX,\n  DMT_EXT_POINT_PREFIX,\n} from './constants';\nimport { getFinalEvent, shouldBufferEventsForPreConsent } from './utils';\n\n/**\n * Event repository class responsible for queuing events for further processing and delivery\n */\nclass EventRepository implements IEventRepository {\n  errorHandler?: IErrorHandler;\n  logger?: ILogger;\n  pluginsManager: IPluginsManager;\n  httpClient: IHttpClient;\n  storeManager: IStoreManager;\n  dataplaneEventsQueue: any;\n  destinationsEventsQueue: any;\n  dmtEventsQueue: any;\n\n  /**\n   *\n   * @param pluginsManager Plugins manager instance\n   * @param storeManager Store Manager instance\n   * @param errorHandler Error handler object\n   * @param logger Logger object\n   */\n  constructor(\n    pluginsManager: IPluginsManager,\n    storeManager: IStoreManager,\n    errorHandler?: IErrorHandler,\n    logger?: ILogger,\n  ) {\n    this.pluginsManager = pluginsManager;\n    this.errorHandler = errorHandler;\n    this.logger = logger;\n    this.httpClient = new HttpClient(errorHandler, logger);\n    this.storeManager = storeManager;\n    this.onError = this.onError.bind(this);\n  }\n\n  /**\n   * Initializes the event repository\n   */\n  init(): void {\n    try {\n      this.dataplaneEventsQueue = this.pluginsManager.invokeSingle(\n        `${DATA_PLANE_QUEUE_EXT_POINT_PREFIX}.init`,\n        state,\n        this.httpClient,\n        this.storeManager,\n        this.errorHandler,\n        this.logger,\n      );\n    } catch (e) {\n      this.onError(e, DATAPLANE_PLUGIN_INITIALIZE_ERROR);\n    }\n\n    try {\n      this.dmtEventsQueue = this.pluginsManager.invokeSingle(\n        `${DMT_EXT_POINT_PREFIX}.init`,\n        state,\n        this.pluginsManager,\n        this.httpClient,\n        this.storeManager,\n        this.errorHandler,\n        this.logger,\n      );\n    } catch (e) {\n      this.onError(e, DMT_PLUGIN_INITIALIZE_ERROR);\n    }\n\n    try {\n      this.destinationsEventsQueue = this.pluginsManager.invokeSingle(\n        `${DESTINATIONS_QUEUE_EXT_POINT_PREFIX}.init`,\n        state,\n        this.pluginsManager,\n        this.storeManager,\n        this.dmtEventsQueue,\n        this.errorHandler,\n        this.logger,\n      );\n    } catch (e) {\n      this.onError(e, NATIVE_DEST_PLUGIN_INITIALIZE_ERROR);\n    }\n\n    // Start the queue once the client destinations are ready\n    effect(() => {\n      if (state.nativeDestinations.clientDestinationsReady.value === true) {\n        this.destinationsEventsQueue?.start();\n        this.dmtEventsQueue?.start();\n      }\n    });\n\n    const bufferEventsBeforeConsent = shouldBufferEventsForPreConsent(state);\n\n    // Start the queue processing only when the destinations are ready or hybrid mode destinations exist\n    // However, events will be enqueued for now.\n    // At the time of processing the events, the integrations config data from destinations\n    // is merged into the event object\n    let timeoutId: number;\n    effect(() => {\n      const shouldBufferDpEvents =\n        state.loadOptions.value.bufferDataPlaneEventsUntilReady === true &&\n        state.nativeDestinations.clientDestinationsReady.value === false;\n\n      const hybridDestExist = state.nativeDestinations.activeDestinations.value.some(\n        (dest: Destination) => isHybridModeDestination(dest),\n      );\n\n      if (\n        (hybridDestExist === false || shouldBufferDpEvents === false) &&\n        !bufferEventsBeforeConsent &&\n        this.dataplaneEventsQueue?.scheduleTimeoutActive !== true\n      ) {\n        (globalThis as typeof window).clearTimeout(timeoutId);\n        this.dataplaneEventsQueue?.start();\n      }\n    });\n\n    // Force start the data plane events queue processing after a timeout\n    if (state.loadOptions.value.bufferDataPlaneEventsUntilReady === true) {\n      timeoutId = (globalThis as typeof window).setTimeout(() => {\n        if (this.dataplaneEventsQueue?.scheduleTimeoutActive !== true) {\n          this.dataplaneEventsQueue?.start();\n        }\n      }, state.loadOptions.value.dataPlaneEventsBufferTimeout);\n    }\n  }\n\n  resume() {\n    if (this.dataplaneEventsQueue?.scheduleTimeoutActive !== true) {\n      if (state.consents.postConsent.value.discardPreConsentEvents) {\n        this.dataplaneEventsQueue?.clear();\n        this.destinationsEventsQueue?.clear();\n      }\n\n      this.dataplaneEventsQueue?.start();\n    }\n  }\n\n  /**\n   * Enqueues the event for processing\n   * @param event RudderEvent object\n   * @param callback API callback function\n   */\n  enqueue(event: RudderEvent, callback?: ApiCallback): void {\n    let dpQEvent;\n    try {\n      dpQEvent = getFinalEvent(event, state);\n      this.pluginsManager.invokeSingle(\n        `${DATA_PLANE_QUEUE_EXT_POINT_PREFIX}.enqueue`,\n        state,\n        this.dataplaneEventsQueue,\n        dpQEvent,\n        this.errorHandler,\n        this.logger,\n      );\n    } catch (e) {\n      this.onError(e, DATAPLANE_PLUGIN_ENQUEUE_ERROR);\n    }\n\n    try {\n      const dQEvent = clone(event);\n      this.pluginsManager.invokeSingle(\n        `${DESTINATIONS_QUEUE_EXT_POINT_PREFIX}.enqueue`,\n        state,\n        this.destinationsEventsQueue,\n        dQEvent,\n        this.errorHandler,\n        this.logger,\n      );\n    } catch (e) {\n      this.onError(e, NATIVE_DEST_PLUGIN_ENQUEUE_ERROR);\n    }\n\n    // Invoke the callback if it exists\n    try {\n      // Using the event sent to the data plane queue here\n      // to ensure the mutated (if any) event is sent to the callback\n      callback?.(dpQEvent);\n    } catch (error) {\n      this.onError(error, API_CALLBACK_INVOKE_ERROR);\n    }\n  }\n\n  /**\n   * Handles error\n   * @param error The error object\n   * @param customMessage a message\n   * @param shouldAlwaysThrow if it should throw or use logger\n   */\n  onError(error: unknown, customMessage?: string, shouldAlwaysThrow?: boolean): void {\n    if (this.errorHandler) {\n      this.errorHandler.onError(error, EVENT_REPOSITORY, customMessage, shouldAlwaysThrow);\n    } else {\n      throw error;\n    }\n  }\n}\n\nexport { EventRepository };\n", "const dispatchSDKEvent = (event: string): void => {\n  const customEvent = new CustomEvent(event, {\n    detail: { analyticsInstance: (globalThis as typeof window).rudderanalytics },\n    bubbles: true,\n    cancelable: true,\n    composed: true,\n  });\n\n  (globalThis as typeof window).document.dispatchEvent(customEvent);\n};\n\nexport { dispatchSDKEvent };\n", "/* eslint-disable @typescript-eslint/no-unused-vars */\nimport { ExternalSrcLoader } from '@rudderstack/analytics-js-common/services/ExternalSrcLoader';\nimport { batch, effect } from '@preact/signals-core';\nimport { isFunction, isNull } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { IHttpClient } from '@rudderstack/analytics-js-common/types/HttpClient';\nimport { clone } from 'ramda';\nimport type { ILogger } from '@rudderstack/analytics-js-common/types/Logger';\nimport type { IErrorHandler } from '@rudderstack/analytics-js-common/types/ErrorHandler';\nimport type { IExternalSrcLoader } from '@rudderstack/analytics-js-common/services/ExternalSrcLoader/types';\nimport type { IStoreManager } from '@rudderstack/analytics-js-common/types/Store';\nimport type { IPluginsManager } from '@rudderstack/analytics-js-common/types/PluginsManager';\nimport { getMutatedError } from '@rudderstack/analytics-js-common/utilities/errors';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type { ApiObject } from '@rudderstack/analytics-js-common/types/ApiObject';\nimport type {\n  AnonymousIdOptions,\n  ConsentOptions,\n  LoadOptions,\n} from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport type { ApiCallback } from '@rudderstack/analytics-js-common/types/EventApi';\nimport { isObjectAndNotNull } from '@rudderstack/analytics-js-common/utilities/object';\nimport {\n  ANALYTICS_CORE,\n  READY_API,\n} from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport {\n  pageArgumentsToCallOptions,\n  type AliasCallOptions,\n  type GroupCallOptions,\n  type IdentifyCallOptions,\n  type PageCallOptions,\n  type TrackCallOptions,\n  trackArgumentsToCallOptions,\n} from '@rudderstack/analytics-js-common/utilities/eventMethodOverloads';\nimport { BufferQueue } from '@rudderstack/analytics-js-common/services/BufferQueue/BufferQueue';\nimport { defaultLogger } from '../../services/Logger';\nimport { defaultErrorHandler } from '../../services/ErrorHandler';\nimport { defaultPluginEngine } from '../../services/PluginEngine';\nimport { PluginsManager } from '../pluginsManager';\nimport { defaultHttpClient } from '../../services/HttpClient';\nimport { type Store, StoreManager } from '../../services/StoreManager';\nimport { state } from '../../state';\nimport { ConfigManager } from '../configManager/ConfigManager';\nimport type { ICapabilitiesManager } from '../capabilitiesManager/types';\nimport { CapabilitiesManager } from '../capabilitiesManager';\nimport type { IEventManager } from '../eventManager/types';\nimport { EventManager } from '../eventManager';\nimport { UserSessionManager } from '../userSessionManager/UserSessionManager';\nimport type { IUserSessionManager } from '../userSessionManager/types';\nimport type { IConfigManager } from '../configManager/types';\nimport { setExposedGlobal } from '../utilities/globals';\nimport { normalizeLoadOptions } from '../utilities/loadOptions';\nimport { consumePreloadBufferedEvent, retrievePreloadBufferEvents } from '../preloadBuffer';\nimport type { PreloadedEventCall } from '../preloadBuffer/types';\nimport { EventRepository } from '../eventRepository';\nimport type { IEventRepository } from '../eventRepository/types';\nimport {\n  ADBLOCK_PAGE_CATEGORY,\n  ADBLOCK_PAGE_NAME,\n  ADBLOCK_PAGE_PATH,\n  CONSENT_TRACK_EVENT_NAME,\n} from '../../constants/app';\nimport { READY_API_CALLBACK_ERROR, READY_CALLBACK_INVOKE_ERROR } from '../../constants/logMessages';\nimport type { IAnalytics } from './IAnalytics';\nimport { getConsentManagementData, getValidPostConsentOptions } from '../utilities/consent';\nimport { dispatchSDKEvent } from './utilities';\n\n/*\n * Analytics class with lifecycle based on state ad user triggered events\n */\nclass Analytics implements IAnalytics {\n  preloadBuffer: BufferQueue<PreloadedEventCall>;\n  initialized: boolean;\n  logger: ILogger;\n  errorHandler: IErrorHandler;\n  httpClient: IHttpClient;\n  externalSrcLoader: IExternalSrcLoader;\n  capabilitiesManager: ICapabilitiesManager;\n  pluginsManager?: IPluginsManager;\n  storeManager?: IStoreManager;\n  configManager?: IConfigManager;\n  eventRepository?: IEventRepository;\n  eventManager?: IEventManager;\n  userSessionManager?: IUserSessionManager;\n  clientDataStore?: Store;\n\n  /**\n   * Initialize services and components or use default ones if singletons\n   */\n  constructor() {\n    this.preloadBuffer = new BufferQueue();\n    this.initialized = false;\n    this.errorHandler = defaultErrorHandler;\n    this.logger = defaultLogger;\n    this.externalSrcLoader = new ExternalSrcLoader(this.errorHandler, this.logger);\n    this.capabilitiesManager = new CapabilitiesManager(this.errorHandler, this.logger);\n    this.httpClient = defaultHttpClient;\n  }\n\n  /**\n   * Start application lifecycle if not already started\n   */\n  load(\n    writeKey: string,\n    dataPlaneUrl?: string | Partial<LoadOptions>,\n    loadOptions: Partial<LoadOptions> = {},\n  ) {\n    if (state.lifecycle.status.value) {\n      return;\n    }\n\n    let clonedDataPlaneUrl = clone(dataPlaneUrl);\n    let clonedLoadOptions = clone(loadOptions);\n\n    // dataPlaneUrl is not provided\n    if (isObjectAndNotNull(dataPlaneUrl)) {\n      clonedLoadOptions = dataPlaneUrl;\n      clonedDataPlaneUrl = undefined;\n    }\n\n    // Set initial state values\n    batch(() => {\n      state.lifecycle.writeKey.value = writeKey;\n      state.lifecycle.dataPlaneUrl.value = clonedDataPlaneUrl as string | undefined;\n      state.loadOptions.value = normalizeLoadOptions(state.loadOptions.value, clonedLoadOptions);\n      state.lifecycle.status.value = 'mounted';\n    });\n\n    // set log level as early as possible\n    if (state.loadOptions.value.logLevel) {\n      this.logger?.setMinLogLevel(state.loadOptions.value.logLevel);\n    }\n\n    // Expose state to global objects\n    setExposedGlobal('state', state, writeKey);\n\n    // Configure initial config of any services or components here\n\n    // State application lifecycle\n    this.startLifecycle();\n  }\n\n  // Start lifecycle methods\n  /**\n   * Orchestrate the lifecycle of the application phases/status\n   */\n  startLifecycle() {\n    effect(() => {\n      try {\n        switch (state.lifecycle.status.value) {\n          case 'mounted':\n            this.onMounted();\n            break;\n          case 'browserCapabilitiesReady':\n            this.onBrowserCapabilitiesReady();\n            break;\n          case 'configured':\n            this.onConfigured();\n            break;\n          case 'pluginsLoading':\n            break;\n          case 'pluginsReady':\n            this.onPluginsReady();\n            break;\n          case 'initialized':\n            this.onInitialized();\n            break;\n          case 'loaded':\n            this.onLoaded();\n            break;\n          case 'destinationsLoading':\n            break;\n          case 'destinationsReady':\n            this.onDestinationsReady();\n            break;\n          case 'ready':\n            this.onReady();\n            break;\n          case 'readyExecuted':\n          default:\n            break;\n        }\n      } catch (err) {\n        const issue = 'Failed to load the SDK';\n        this.errorHandler.onError(getMutatedError(err, issue), ANALYTICS_CORE);\n      }\n    });\n  }\n\n  onBrowserCapabilitiesReady() {\n    // initialize the preloaded events enqueuing\n    retrievePreloadBufferEvents(this);\n    this.prepareInternalServices();\n    this.loadConfig();\n  }\n\n  onLoaded() {\n    this.processBufferedEvents();\n    // Short-circuit the life cycle and move to the ready state if pre-consent behavior is enabled\n    if (state.consents.preConsent.value.enabled === true) {\n      state.lifecycle.status.value = 'ready';\n    } else {\n      this.loadDestinations();\n    }\n  }\n\n  /**\n   * Load browser polyfill if required\n   */\n  onMounted() {\n    this.capabilitiesManager.init();\n  }\n\n  /**\n   * Enqueue in SDK preload buffer events, used from preloadBuffer component\n   */\n  enqueuePreloadBufferEvents(bufferedEvents: PreloadedEventCall[]) {\n    if (Array.isArray(bufferedEvents)) {\n      bufferedEvents.forEach(bufferedEvent => this.preloadBuffer.enqueue(clone(bufferedEvent)));\n    }\n  }\n\n  /**\n   * Process the buffer preloaded events by passing their arguments to the respective facade methods\n   */\n  processDataInPreloadBuffer() {\n    while (this.preloadBuffer.size() > 0) {\n      const eventToProcess = this.preloadBuffer.dequeue();\n\n      if (eventToProcess) {\n        consumePreloadBufferedEvent([...eventToProcess], this);\n      }\n    }\n  }\n\n  prepareInternalServices() {\n    this.pluginsManager = new PluginsManager(defaultPluginEngine, this.errorHandler, this.logger);\n    this.storeManager = new StoreManager(this.pluginsManager, this.errorHandler, this.logger);\n    this.configManager = new ConfigManager(this.httpClient, this.errorHandler, this.logger);\n    this.userSessionManager = new UserSessionManager(\n      this.errorHandler,\n      this.logger,\n      this.pluginsManager,\n      this.storeManager,\n      this.httpClient,\n    );\n    this.eventRepository = new EventRepository(\n      this.pluginsManager,\n      this.storeManager,\n      this.errorHandler,\n      this.logger,\n    );\n    this.eventManager = new EventManager(\n      this.eventRepository,\n      this.userSessionManager,\n      this.errorHandler,\n      this.logger,\n    );\n  }\n\n  /**\n   * Load configuration\n   */\n  loadConfig() {\n    if (state.lifecycle.writeKey.value) {\n      this.httpClient.setAuthHeader(state.lifecycle.writeKey.value);\n    }\n\n    this.configManager?.init();\n  }\n\n  /**\n   * Initialize the storage and event queue\n   */\n  onPluginsReady() {\n    this.errorHandler.init(this.externalSrcLoader);\n    // Initialize storage\n    this.storeManager?.init();\n    this.userSessionManager?.init();\n\n    // Initialize the appropriate consent manager plugin\n    if (state.consents.enabled.value && !state.consents.initialized.value) {\n      this.pluginsManager?.invokeSingle(`consentManager.init`, state, this.logger);\n\n      if (state.consents.preConsent.value.enabled === false) {\n        this.pluginsManager?.invokeSingle(\n          `consentManager.updateConsentsInfo`,\n          state,\n          this.storeManager,\n          this.logger,\n        );\n      }\n    }\n\n    // Initialize event manager\n    this.eventManager?.init();\n\n    // Mark the SDK as initialized\n    state.lifecycle.status.value = 'initialized';\n  }\n\n  /**\n   * Load plugins\n   */\n  onConfigured() {\n    this.pluginsManager?.init();\n    // TODO: are we going to enable custom plugins to be passed as load options?\n    // registerCustomPlugins(state.loadOptions.value.customPlugins);\n  }\n\n  /**\n   * Trigger onLoaded callback if any is provided in config & emit initialised event\n   */\n  onInitialized() {\n    // Process any preloaded events\n    this.processDataInPreloadBuffer();\n\n    // TODO: we need to avoid passing the window object to the callback function\n    // as this will prevent us from supporting multiple SDK instances in the same page\n    // Execute onLoaded callback if provided in load options\n    if (isFunction(state.loadOptions.value.onLoaded)) {\n      state.loadOptions.value.onLoaded((globalThis as typeof window).rudderanalytics);\n    }\n\n    // Set lifecycle state\n    batch(() => {\n      state.lifecycle.loaded.value = true;\n      state.lifecycle.status.value = 'loaded';\n    });\n\n    this.initialized = true;\n\n    // Emit an event to use as substitute to the onLoaded callback\n    dispatchSDKEvent('RSA_Initialised');\n  }\n\n  /**\n   * Emit ready event\n   */\n  // eslint-disable-next-line class-methods-use-this\n  onReady() {\n    state.lifecycle.status.value = 'readyExecuted';\n    state.eventBuffer.readyCallbacksArray.value.forEach((callback: ApiCallback) => {\n      try {\n        callback();\n      } catch (err) {\n        this.errorHandler.onError(err, ANALYTICS_CORE, READY_CALLBACK_INVOKE_ERROR);\n      }\n    });\n\n    // Emit an event to use as substitute to the ready callback\n    dispatchSDKEvent('RSA_Ready');\n  }\n\n  /**\n   * Consume preloaded events buffer\n   */\n  processBufferedEvents() {\n    // This logic has been intentionally implemented without a simple\n    // for-loop as the individual events that are processed may\n    // add more events to the buffer (this is needed for the consent API)\n    let bufferedEvents = state.eventBuffer.toBeProcessedArray.value;\n    while (bufferedEvents.length > 0) {\n      const bufferedEvent = bufferedEvents.shift();\n      state.eventBuffer.toBeProcessedArray.value = bufferedEvents;\n\n      if (bufferedEvent) {\n        const methodName = bufferedEvent[0];\n        if (isFunction((this as any)[methodName])) {\n          // Send additional arg 'true' to indicate that this is a buffered invocation\n          (this as any)[methodName](...bufferedEvent.slice(1), true);\n        }\n      }\n\n      bufferedEvents = state.eventBuffer.toBeProcessedArray.value;\n    }\n  }\n\n  /**\n   * Load device mode destinations\n   */\n  loadDestinations() {\n    if (state.nativeDestinations.clientDestinationsReady.value) {\n      return;\n    }\n\n    // Set in state the desired activeDestinations to inject in DOM\n    this.pluginsManager?.invokeSingle(\n      'nativeDestinations.setActiveDestinations',\n      state,\n      this.pluginsManager,\n      this.errorHandler,\n      this.logger,\n    );\n\n    const totalDestinationsToLoad = state.nativeDestinations.activeDestinations.value.length;\n    if (totalDestinationsToLoad === 0) {\n      state.lifecycle.status.value = 'destinationsReady';\n      return;\n    }\n\n    // Start loading native integration scripts and create instances\n    state.lifecycle.status.value = 'destinationsLoading';\n    this.pluginsManager?.invokeSingle(\n      'nativeDestinations.load',\n      state,\n      this.externalSrcLoader,\n      this.errorHandler,\n      this.logger,\n    );\n\n    // Progress to next lifecycle phase if all native destinations are initialized or failed\n    effect(() => {\n      const areAllDestinationsReady =\n        totalDestinationsToLoad === 0 ||\n        state.nativeDestinations.initializedDestinations.value.length +\n          state.nativeDestinations.failedDestinations.value.length ===\n          totalDestinationsToLoad;\n\n      if (areAllDestinationsReady) {\n        batch(() => {\n          state.lifecycle.status.value = 'destinationsReady';\n          state.nativeDestinations.clientDestinationsReady.value = true;\n        });\n      }\n    });\n  }\n\n  /**\n   * Move to the ready state\n   */\n  // eslint-disable-next-line class-methods-use-this\n  onDestinationsReady() {\n    // May be do any destination specific actions here\n\n    // Mark the ready status if not already done\n    if (state.lifecycle.status.value !== 'ready') {\n      state.lifecycle.status.value = 'ready';\n    }\n  }\n  // End lifecycle methods\n\n  // Start consumer exposed methods\n  ready(callback: ApiCallback, isBufferedInvocation = false) {\n    const type = 'ready';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, callback],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} invocation`);\n\n    if (!isFunction(callback)) {\n      this.logger.error(READY_API_CALLBACK_ERROR(READY_API));\n      return;\n    }\n\n    /**\n     * If destinations are loaded or no integration is available for loading\n     * execute the callback immediately else push the callbacks to a queue that\n     * will be executed after loading completes\n     */\n    if (state.lifecycle.status.value === 'readyExecuted') {\n      try {\n        callback();\n      } catch (err) {\n        this.errorHandler.onError(err, ANALYTICS_CORE, READY_CALLBACK_INVOKE_ERROR);\n      }\n    } else {\n      state.eventBuffer.readyCallbacksArray.value = [\n        ...state.eventBuffer.readyCallbacksArray.value,\n        callback,\n      ];\n    }\n  }\n\n  page(payload: PageCallOptions, isBufferedInvocation = false) {\n    const type = 'page';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, payload],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} event`);\n    state.metrics.triggered.value += 1;\n\n    this.eventManager?.addEvent({\n      type: 'page',\n      category: payload.category,\n      name: payload.name,\n      properties: payload.properties,\n      options: payload.options,\n      callback: payload.callback,\n    });\n\n    // TODO: Maybe we should alter the behavior to send the ad-block page event even if the SDK is still loaded. It'll be pushed into the to be processed queue.\n\n    // Send automatic ad blocked page event if ad-blockers are detected on the page\n    // Check page category to avoid infinite loop\n    if (\n      state.capabilities.isAdBlocked.value === true &&\n      payload.category !== ADBLOCK_PAGE_CATEGORY\n    ) {\n      this.page(\n        pageArgumentsToCallOptions(\n          ADBLOCK_PAGE_CATEGORY,\n          ADBLOCK_PAGE_NAME,\n          {\n            // 'title' is intentionally omitted as it does not make sense\n            // in v3 implementation\n            path: ADBLOCK_PAGE_PATH,\n          },\n          state.loadOptions.value.sendAdblockPageOptions,\n        ),\n      );\n    }\n  }\n\n  track(payload: TrackCallOptions, isBufferedInvocation = false) {\n    const type = 'track';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, payload],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} event`);\n    state.metrics.triggered.value += 1;\n\n    this.eventManager?.addEvent({\n      type,\n      name: payload.name || undefined,\n      properties: payload.properties,\n      options: payload.options,\n      callback: payload.callback,\n    });\n  }\n\n  identify(payload: IdentifyCallOptions, isBufferedInvocation = false) {\n    const type = 'identify';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, payload],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} event`);\n    state.metrics.triggered.value += 1;\n\n    const shouldResetSession = Boolean(\n      payload.userId && state.session.userId.value && payload.userId !== state.session.userId.value,\n    );\n\n    if (shouldResetSession) {\n      this.reset();\n    }\n\n    // `null` value indicates that previous user ID needs to be retained\n    if (!isNull(payload.userId)) {\n      this.userSessionManager?.setUserId(payload.userId);\n    }\n    this.userSessionManager?.setUserTraits(payload.traits);\n\n    this.eventManager?.addEvent({\n      type,\n      userId: payload.userId,\n      traits: payload.traits,\n      options: payload.options,\n      callback: payload.callback,\n    });\n  }\n\n  alias(payload: AliasCallOptions, isBufferedInvocation = false) {\n    const type = 'alias';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, payload],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} event`);\n    state.metrics.triggered.value += 1;\n\n    const previousId =\n      payload.from ??\n      this.userSessionManager?.getUserId() ??\n      this.userSessionManager?.getAnonymousId();\n\n    this.eventManager?.addEvent({\n      type,\n      to: payload.to,\n      from: previousId,\n      options: payload.options,\n      callback: payload.callback,\n    });\n  }\n\n  group(payload: GroupCallOptions, isBufferedInvocation = false) {\n    const type = 'group';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, payload],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} event`);\n    state.metrics.triggered.value += 1;\n\n    // `null` value indicates that previous group ID needs to be retained\n    if (!isNull(payload.groupId)) {\n      this.userSessionManager?.setGroupId(payload.groupId);\n    }\n\n    this.userSessionManager?.setGroupTraits(payload.traits);\n\n    this.eventManager?.addEvent({\n      type,\n      groupId: payload.groupId,\n      traits: payload.traits,\n      options: payload.options,\n      callback: payload.callback,\n    });\n  }\n\n  reset(resetAnonymousId?: boolean, isBufferedInvocation = false) {\n    const type = 'reset';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, resetAnonymousId],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(\n      `New ${type} invocation, resetAnonymousId: ${resetAnonymousId}`,\n    );\n    this.userSessionManager?.reset(resetAnonymousId);\n  }\n\n  getAnonymousId(options?: AnonymousIdOptions): string | undefined {\n    return this.userSessionManager?.getAnonymousId(options);\n  }\n\n  setAnonymousId(\n    anonymousId?: string,\n    rudderAmpLinkerParam?: string,\n    isBufferedInvocation = false,\n  ): void {\n    const type = 'setAnonymousId';\n    // Buffering is needed as setting the anonymous ID may require invoking the GoogleLinker plugin\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, anonymousId, rudderAmpLinkerParam],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} invocation`);\n    this.userSessionManager?.setAnonymousId(anonymousId, rudderAmpLinkerParam);\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getUserId(): Nullable<string> | undefined {\n    return state.session.userId.value;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getUserTraits(): Nullable<ApiObject> | undefined {\n    return state.session.userTraits.value;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getGroupId(): Nullable<string> | undefined {\n    return state.session.groupId.value;\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getGroupTraits(): Nullable<ApiObject> | undefined {\n    return state.session.groupTraits.value;\n  }\n\n  startSession(sessionId?: number, isBufferedInvocation = false): void {\n    const type = 'startSession';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, sessionId],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} invocation`);\n    this.userSessionManager?.start(sessionId);\n  }\n\n  endSession(isBufferedInvocation = false): void {\n    const type = 'endSession';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New ${type} invocation`);\n    this.userSessionManager?.end();\n  }\n\n  // eslint-disable-next-line class-methods-use-this\n  getSessionId(): Nullable<number> {\n    const sessionId = this.userSessionManager?.getSessionId();\n    return sessionId ?? null;\n  }\n\n  consent(options?: ConsentOptions, isBufferedInvocation = false) {\n    const type = 'consent';\n\n    if (!state.lifecycle.loaded.value) {\n      state.eventBuffer.toBeProcessedArray.value = [\n        ...state.eventBuffer.toBeProcessedArray.value,\n        [type, options],\n      ];\n      return;\n    }\n\n    this.errorHandler.leaveBreadcrumb(`New consent invocation`);\n\n    batch(() => {\n      state.consents.preConsent.value = { ...state.consents.preConsent.value, enabled: false };\n      state.consents.postConsent.value = getValidPostConsentOptions(options);\n\n      const { initialized, consentsData } = getConsentManagementData(\n        state.consents.postConsent.value.consentManagement,\n        this.logger,\n      );\n\n      state.consents.initialized.value = initialized || state.consents.initialized.value;\n      state.consents.data.value = consentsData;\n    });\n\n    // Update consents data in state\n    if (state.consents.enabled.value && !state.consents.initialized.value) {\n      this.pluginsManager?.invokeSingle(\n        `consentManager.updateConsentsInfo`,\n        state,\n        this.storeManager,\n        this.logger,\n      );\n    }\n\n    // Re-init store manager\n    this.storeManager?.initializeStorageState();\n\n    // Re-init user session manager\n    this.userSessionManager?.syncStorageDataToState();\n\n    // Resume event manager to process the events to destinations\n    this.eventManager?.resume();\n\n    this.loadDestinations();\n\n    this.sendTrackingEvents(isBufferedInvocation);\n  }\n\n  sendTrackingEvents(isBufferedInvocation: boolean) {\n    // If isBufferedInvocation is true, then the tracking events will be added to the end of the\n    // events buffer array so that any other preload events (mainly from query string API) will be processed first.\n    if (state.consents.postConsent.value.trackConsent) {\n      const trackOptions = trackArgumentsToCallOptions(CONSENT_TRACK_EVENT_NAME);\n      if (isBufferedInvocation) {\n        state.eventBuffer.toBeProcessedArray.value = [\n          ...state.eventBuffer.toBeProcessedArray.value,\n          ['track', trackOptions],\n        ];\n      } else {\n        this.track(trackOptions);\n      }\n    }\n\n    if (state.consents.postConsent.value.sendPageEvent) {\n      const pageOptions = pageArgumentsToCallOptions();\n      if (isBufferedInvocation) {\n        state.eventBuffer.toBeProcessedArray.value = [\n          ...state.eventBuffer.toBeProcessedArray.value,\n          ['page', pageOptions],\n        ];\n      } else {\n        this.page(pageOptions);\n      }\n    }\n  }\n\n  setAuthToken(token: string): void {\n    this.userSessionManager?.setAuthToken(token);\n  }\n  // End consumer exposed methods\n}\n\nexport { Analytics };\n", "import { clone } from 'ramda';\nimport {\n  isObjectLiteralAndNotNull,\n  mergeDeepRight,\n  removeUndefinedAndNullValues,\n} from '@rudderstack/analytics-js-common/utilities/object';\nimport type {\n  LoadOptions,\n  UaChTrackLevel,\n} from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport type { StorageOpts, CookieSameSite } from '@rudderstack/analytics-js-common/types/Storage';\nimport { isDefined, isString } from '@rudderstack/analytics-js-common/utilities/checks';\nimport { defaultOptionalPluginsList } from '../pluginsManager/defaultPluginsList';\nimport { isNumber } from './number';\n\nconst normalizeLoadOptions = (\n  loadOptionsFromState: LoadOptions,\n  loadOptions: Partial<LoadOptions>,\n): LoadOptions => {\n  // TODO: Maybe add warnings for invalid values\n  const normalizedLoadOpts = clone(loadOptions);\n\n  if (!isString(normalizedLoadOpts.setCookieDomain)) {\n    delete normalizedLoadOpts.setCookieDomain;\n  }\n\n  const cookieSameSiteValues = ['Strict', 'Lax', 'None'];\n  if (!cookieSameSiteValues.includes(normalizedLoadOpts.sameSiteCookie as CookieSameSite)) {\n    delete normalizedLoadOpts.sameSiteCookie;\n  }\n\n  normalizedLoadOpts.secureCookie = normalizedLoadOpts.secureCookie === true;\n\n  const uaChTrackLevels = ['none', 'default', 'full'];\n  if (!uaChTrackLevels.includes(normalizedLoadOpts.uaChTrackLevel as UaChTrackLevel)) {\n    delete normalizedLoadOpts.uaChTrackLevel;\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.integrations)) {\n    delete normalizedLoadOpts.integrations;\n  }\n\n  normalizedLoadOpts.plugins = normalizedLoadOpts.plugins ?? defaultOptionalPluginsList;\n\n  normalizedLoadOpts.useGlobalIntegrationsConfigInEvents =\n    normalizedLoadOpts.useGlobalIntegrationsConfigInEvents === true;\n\n  normalizedLoadOpts.bufferDataPlaneEventsUntilReady =\n    normalizedLoadOpts.bufferDataPlaneEventsUntilReady === true;\n\n  normalizedLoadOpts.sendAdblockPage = normalizedLoadOpts.sendAdblockPage === true;\n\n  normalizedLoadOpts.useServerSideCookies = normalizedLoadOpts.useServerSideCookies === true;\n\n  if (\n    normalizedLoadOpts.dataServiceEndpoint &&\n    typeof normalizedLoadOpts.dataServiceEndpoint !== 'string'\n  ) {\n    delete normalizedLoadOpts.dataServiceEndpoint;\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.sendAdblockPageOptions)) {\n    delete normalizedLoadOpts.sendAdblockPageOptions;\n  }\n\n  if (!isDefined(normalizedLoadOpts.loadIntegration)) {\n    delete normalizedLoadOpts.loadIntegration;\n  } else {\n    normalizedLoadOpts.loadIntegration = normalizedLoadOpts.loadIntegration === true;\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.storage)) {\n    delete normalizedLoadOpts.storage;\n  } else {\n    normalizedLoadOpts.storage = removeUndefinedAndNullValues(normalizedLoadOpts.storage);\n    (normalizedLoadOpts.storage as StorageOpts).migrate =\n      normalizedLoadOpts.storage?.migrate === true;\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.beaconQueueOptions)) {\n    delete normalizedLoadOpts.beaconQueueOptions;\n  } else {\n    normalizedLoadOpts.beaconQueueOptions = removeUndefinedAndNullValues(\n      normalizedLoadOpts.beaconQueueOptions,\n    );\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.destinationsQueueOptions)) {\n    delete normalizedLoadOpts.destinationsQueueOptions;\n  } else {\n    normalizedLoadOpts.destinationsQueueOptions = removeUndefinedAndNullValues(\n      normalizedLoadOpts.destinationsQueueOptions,\n    );\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.queueOptions)) {\n    delete normalizedLoadOpts.queueOptions;\n  } else {\n    normalizedLoadOpts.queueOptions = removeUndefinedAndNullValues(normalizedLoadOpts.queueOptions);\n  }\n\n  normalizedLoadOpts.lockIntegrationsVersion = normalizedLoadOpts.lockIntegrationsVersion === true;\n\n  if (!isNumber(normalizedLoadOpts.dataPlaneEventsBufferTimeout)) {\n    delete normalizedLoadOpts.dataPlaneEventsBufferTimeout;\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.storage?.cookie)) {\n    delete normalizedLoadOpts.storage?.cookie;\n  } else {\n    (normalizedLoadOpts.storage as StorageOpts).cookie = removeUndefinedAndNullValues(\n      normalizedLoadOpts.storage?.cookie,\n    );\n  }\n\n  if (!isObjectLiteralAndNotNull(normalizedLoadOpts.preConsent)) {\n    delete normalizedLoadOpts.preConsent;\n  } else {\n    normalizedLoadOpts.preConsent = removeUndefinedAndNullValues(normalizedLoadOpts.preConsent);\n  }\n\n  const mergedLoadOptions: LoadOptions = mergeDeepRight(loadOptionsFromState, normalizedLoadOpts);\n\n  return mergedLoadOptions;\n};\n\nexport { normalizeLoadOptions };\n", "/* eslint-disable unicorn/prefer-export-from */\nimport { clone } from 'ramda';\nimport {\n  aliasArgumentsToCallOptions,\n  groupArgumentsToCallOptions,\n  identifyArgumentsToCallOptions,\n  pageArgumentsToCallOptions,\n  trackArgumentsToCallOptions,\n} from '@rudderstack/analytics-js-common/utilities/eventMethodOverloads';\nimport type { IRudderAnalytics } from '@rudderstack/analytics-js-common/types/IRudderAnalytics';\nimport type { Nullable } from '@rudderstack/analytics-js-common/types/Nullable';\nimport type {\n  AnonymousIdOptions,\n  ConsentOptions,\n  LoadOptions,\n} from '@rudderstack/analytics-js-common/types/LoadOptions';\nimport type { ApiCallback, ApiOptions } from '@rudderstack/analytics-js-common/types/EventApi';\nimport type { ApiObject } from '@rudderstack/analytics-js-common/types/ApiObject';\nimport { RS_APP } from '@rudderstack/analytics-js-common/constants/loggerContexts';\nimport { isString } from '@rudderstack/analytics-js-common/utilities/checks';\nimport type { IdentifyTraits } from '@rudderstack/analytics-js-common/types/traits';\nimport { GLOBAL_PRELOAD_BUFFER } from '../constants/app';\nimport {\n  getPreloadedLoadEvent,\n  promotePreloadedConsentEventsToTop,\n} from '../components/preloadBuffer';\nimport type { PreloadedEventCall } from '../components/preloadBuffer/types';\nimport { setExposedGlobal } from '../components/utilities/globals';\nimport type { IAnalytics } from '../components/core/IAnalytics';\nimport { Analytics } from '../components/core/Analytics';\nimport { defaultLogger } from '../services/Logger/Logger';\nimport { EMPTY_GROUP_CALL_ERROR, WRITE_KEY_NOT_A_STRING_ERROR } from '../constants/logMessages';\nimport { defaultErrorHandler } from '../services/ErrorHandler';\n\n// TODO: add analytics restart/reset mechanism\n\n/*\n * RudderAnalytics facade singleton that is exposed as global object and will:\n * expose overloaded methods\n * handle multiple Analytics instances\n * consume SDK preload event buffer\n */\nclass RudderAnalytics implements IRudderAnalytics<IAnalytics> {\n  static globalSingleton: Nullable<RudderAnalytics> = null;\n  analyticsInstances: Record<string, IAnalytics> = {};\n  defaultAnalyticsKey = '';\n  logger = defaultLogger;\n\n  // Singleton with constructor bind methods\n  constructor() {\n    if (RudderAnalytics.globalSingleton) {\n      // START-NO-SONAR-SCAN\n      // eslint-disable-next-line no-constructor-return\n      return RudderAnalytics.globalSingleton;\n      // END-NO-SONAR-SCAN\n    }\n    defaultErrorHandler.attachErrorListeners();\n\n    this.setDefaultInstanceKey = this.setDefaultInstanceKey.bind(this);\n    this.getAnalyticsInstance = this.getAnalyticsInstance.bind(this);\n    this.load = this.load.bind(this);\n    this.ready = this.ready.bind(this);\n    this.triggerBufferedLoadEvent = this.triggerBufferedLoadEvent.bind(this);\n    this.page = this.page.bind(this);\n    this.track = this.track.bind(this);\n    this.identify = this.identify.bind(this);\n    this.alias = this.alias.bind(this);\n    this.group = this.group.bind(this);\n    this.reset = this.reset.bind(this);\n    this.getAnonymousId = this.getAnonymousId.bind(this);\n    this.setAnonymousId = this.setAnonymousId.bind(this);\n    this.getUserId = this.getUserId.bind(this);\n    this.getUserTraits = this.getUserTraits.bind(this);\n    this.getGroupId = this.getGroupId.bind(this);\n    this.getGroupTraits = this.getGroupTraits.bind(this);\n    this.startSession = this.startSession.bind(this);\n    this.endSession = this.endSession.bind(this);\n    this.getSessionId = this.getSessionId.bind(this);\n    this.setAuthToken = this.setAuthToken.bind(this);\n    this.consent = this.consent.bind(this);\n\n    RudderAnalytics.globalSingleton = this;\n\n    // start loading if a load event was buffered or wait for explicit load call\n    this.triggerBufferedLoadEvent();\n\n    // Assign to global \"rudderanalytics\" object after processing the preload buffer (if any exists)\n    // for CDN bundling IIFE exports covers this but for npm ESM and CJS bundling has to be done explicitly\n    (globalThis as typeof window).rudderanalytics = this;\n  }\n\n  /**\n   * Set instance to use if no specific writeKey is provided in methods\n   * automatically for the first created instance\n   * TODO: to support multiple analytics instances in the near future\n   */\n  setDefaultInstanceKey(writeKey: string) {\n    if (writeKey) {\n      this.defaultAnalyticsKey = writeKey;\n    }\n  }\n\n  /**\n   * Retrieve an existing analytics instance\n   */\n  getAnalyticsInstance(writeKey?: string): IAnalytics {\n    const instanceId = writeKey ?? this.defaultAnalyticsKey;\n\n    const analyticsInstanceExists = Boolean(this.analyticsInstances[instanceId]);\n\n    if (!analyticsInstanceExists) {\n      this.analyticsInstances[instanceId] = new Analytics();\n    }\n\n    return this.analyticsInstances[instanceId] as IAnalytics;\n  }\n\n  /**\n   * Create new analytics instance and trigger application lifecycle start\n   */\n  load(writeKey: string, dataPlaneUrl: string, loadOptions?: Partial<LoadOptions>) {\n    if (!isString(writeKey)) {\n      this.logger.error(WRITE_KEY_NOT_A_STRING_ERROR(RS_APP, writeKey));\n      return;\n    }\n\n    if (this.analyticsInstances[writeKey]) {\n      return;\n    }\n\n    this.setDefaultInstanceKey(writeKey);\n    this.analyticsInstances[writeKey] = new Analytics();\n    this.getAnalyticsInstance(writeKey).load(writeKey, dataPlaneUrl, loadOptions);\n  }\n\n  /**\n   * Trigger load event in buffer queue if exists and stores the\n   * remaining preloaded events array in global object\n   */\n  triggerBufferedLoadEvent() {\n    const preloadedEventsArray = Array.isArray((globalThis as typeof window).rudderanalytics)\n      ? ((globalThis as typeof window).rudderanalytics as unknown as PreloadedEventCall[])\n      : ([] as PreloadedEventCall[]);\n\n    // The array will be mutated in the below method\n    promotePreloadedConsentEventsToTop(preloadedEventsArray);\n\n    // Get any load method call that is buffered if any\n    // BTW, load method is also removed from the array\n    // So, the Analytics object can directly consume the remaining events\n    const loadEvent: PreloadedEventCall = getPreloadedLoadEvent(preloadedEventsArray);\n\n    // Set the final preloaded events array in global object\n    setExposedGlobal(GLOBAL_PRELOAD_BUFFER, clone(preloadedEventsArray));\n\n    // Process load method if present in the buffered requests\n    if (loadEvent.length > 0) {\n      // Remove the event name from the Buffered Event array and keep only arguments\n      loadEvent.shift();\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      this.load.apply(null, loadEvent);\n    }\n  }\n\n  /**\n   * Get ready callback arguments and forward to ready call\n   */\n  ready(callback: ApiCallback) {\n    this.getAnalyticsInstance().ready(callback);\n  }\n\n  /**\n   * Process page arguments and forward to page call\n   */\n  page(\n    category?: string | Nullable<ApiObject> | ApiCallback,\n    name?: string | Nullable<ApiOptions> | Nullable<ApiObject> | ApiCallback,\n    properties?: Nullable<ApiOptions> | Nullable<ApiObject> | ApiCallback,\n    options?: Nullable<ApiOptions> | ApiCallback,\n    callback?: ApiCallback,\n  ) {\n    this.getAnalyticsInstance().page(\n      pageArgumentsToCallOptions(category, name, properties, options, callback),\n    );\n  }\n\n  /**\n   * Process track arguments and forward to page call\n   */\n  track(\n    event: string,\n    properties?: Nullable<ApiObject> | ApiCallback,\n    options?: Nullable<ApiOptions> | ApiCallback,\n    callback?: ApiCallback,\n  ) {\n    this.getAnalyticsInstance().track(\n      trackArgumentsToCallOptions(event, properties, options, callback),\n    );\n  }\n\n  /**\n   * Process identify arguments and forward to page call\n   */\n  identify(\n    userId?: string | number | Nullable<IdentifyTraits>,\n    traits?: Nullable<IdentifyTraits> | Nullable<ApiOptions> | ApiCallback,\n    options?: Nullable<ApiOptions> | ApiCallback,\n    callback?: ApiCallback,\n  ) {\n    this.getAnalyticsInstance().identify(\n      identifyArgumentsToCallOptions(userId, traits, options, callback),\n    );\n  }\n\n  /**\n   * Process alias arguments and forward to page call\n   */\n  alias(\n    to?: Nullable<string> | ApiCallback,\n    from?: string | Nullable<ApiOptions> | ApiCallback,\n    options?: Nullable<ApiOptions> | ApiCallback,\n    callback?: ApiCallback,\n  ) {\n    this.getAnalyticsInstance().alias(aliasArgumentsToCallOptions(to, from, options, callback));\n  }\n\n  /**\n   * Process group arguments and forward to page call\n   */\n  group(\n    groupId: string | number | Nullable<ApiObject> | ApiCallback,\n    traits?: Nullable<ApiOptions> | Nullable<ApiObject> | ApiCallback,\n    options?: Nullable<ApiOptions> | ApiCallback,\n    callback?: ApiCallback,\n  ) {\n    if (arguments.length === 0) {\n      this.logger.error(EMPTY_GROUP_CALL_ERROR(RS_APP));\n      return;\n    }\n\n    this.getAnalyticsInstance().group(\n      groupArgumentsToCallOptions(groupId, traits, options, callback),\n    );\n  }\n\n  reset(resetAnonymousId?: boolean) {\n    this.getAnalyticsInstance().reset(resetAnonymousId);\n  }\n\n  getAnonymousId(options?: AnonymousIdOptions) {\n    return this.getAnalyticsInstance().getAnonymousId(options);\n  }\n\n  setAnonymousId(anonymousId?: string, rudderAmpLinkerParam?: string) {\n    this.getAnalyticsInstance().setAnonymousId(anonymousId, rudderAmpLinkerParam);\n  }\n\n  getUserId() {\n    return this.getAnalyticsInstance().getUserId();\n  }\n\n  getUserTraits() {\n    return this.getAnalyticsInstance().getUserTraits();\n  }\n\n  getGroupId() {\n    return this.getAnalyticsInstance().getGroupId();\n  }\n\n  getGroupTraits() {\n    return this.getAnalyticsInstance().getGroupTraits();\n  }\n\n  startSession(sessionId?: number) {\n    return this.getAnalyticsInstance().startSession(sessionId);\n  }\n\n  endSession() {\n    return this.getAnalyticsInstance().endSession();\n  }\n\n  getSessionId() {\n    return this.getAnalyticsInstance().getSessionId();\n  }\n\n  setAuthToken(token: string) {\n    return this.getAnalyticsInstance().setAuthToken(token);\n  }\n\n  consent(options?: ConsentOptions) {\n    return this.getAnalyticsInstance().consent(options);\n  }\n}\n\nexport { RudderAnalytics };\n", "import { RudderAnalytics } from './app/RudderAnalytics';\n\n/*\n * Create new RudderAnalytics facade singleton\n */\nconst {\n  setDefaultInstanceKey,\n  getAnalyticsInstance,\n  load,\n  ready,\n  page,\n  track,\n  identify,\n  alias,\n  group,\n  reset,\n  getAnonymousId,\n  setAnonymousId,\n  getUserId,\n  getUserTraits,\n  getGroupId,\n  getGroupTraits,\n  startSession,\n  endSession,\n  getSessionId,\n  consent,\n  setAuthToken,\n} = new RudderAnalytics();\n\n/*\n * Export as global object the RudderAnalytics facade singleton methods\n */\nexport {\n  setDefaultInstanceKey,\n  getAnalyticsInstance,\n  load,\n  ready,\n  page,\n  track,\n  identify,\n  alias,\n  group,\n  reset,\n  getAnonymousId,\n  setAnonymousId,\n  getUserId,\n  getUserTraits,\n  getGroupId,\n  getGroupTraits,\n  startSession,\n  endSession,\n  getSessionId,\n  consent,\n  setAuthToken,\n};\n"], "names": ["_placeholder", "_isPlaceholder", "a", "_curry1", "fn", "f1", "arguments", "length", "apply", "this", "_curry2", "f2", "b", "_b", "_a", "_curry3", "f3", "c", "_c", "_has", "prop", "obj", "Object", "prototype", "hasOwnProperty", "call", "type", "val", "undefined", "toString", "slice", "_isObject", "x", "Number", "isInteger", "n", "_nth", "offset", "list", "idx", "char<PERSON>t", "_clone", "value", "deep", "map", "_ObjectMap", "param", "_isPrimitive", "pattern", "copy", "copiedValue", "cachedCopy", "get", "key", "set", "create", "getPrototypeOf", "Array", "Date", "valueOf", "RegExp", "source", "flags", "global", "ignoreCase", "multiline", "sticky", "unicode", "dotAll", "hashed<PERSON><PERSON>", "hash", "bucket", "push", "join", "p", "i", "element", "clone", "_path", "pathAr", "_isInteger", "mergeWithKey", "l", "r", "k", "result", "mergeDeepWithKey", "lObj", "r<PERSON>bj", "lVal", "rVal", "mergeDeepWith", "path", "pickBy", "test", "isFunction", "Boolean", "constructor", "isString", "isNull", "isUndefined", "isNullOrUndefined", "isDefined", "isDefinedAndNotNull", "isTypeOfError", "Error", "getValueByPath", "keyP<PERSON>", "pathParts", "split", "isObjectAndNotNull", "isArray", "isObjectLiteralAndNotNull", "mergeDeepRightObjectArrays", "leftValue", "rightValue", "mergedArray", "for<PERSON>ach", "index", "mergeDeepRight", "leftObject", "rightObject", "isNonEmptyObject", "keys", "removeUndefinedValues", "removeUndefinedAndNullValues", "tryStringify", "retVal", "JSON", "stringify", "e", "toBase64", "bytes", "binString", "from", "String", "fromCodePoint", "globalThis", "btoa", "bytesToBase64", "TextEncoder", "encode", "pageArgumentsToCallOptions", "category", "name", "properties", "options", "callback", "payload", "trackArgumentsToCallOptions", "event", "identifyArgumentsToCallOptions", "userId", "traits", "aliasArgumentsToCallOptions", "to", "groupArgumentsToCallOptions", "groupId", "CAPABILITIES_MANAGER", "CONFIG_MANAGER", "EVENT_MANAGER", "PLUGINS_MANAGER", "USER_SESSION_MANAGER", "ERROR_HANDLER", "PLUGIN_ENGINE", "STORE_MANAGER", "RS_APP", "ANALYTICS_CORE", "APP_NAME", "APP_VERSION", "ADBLOCK_PAGE_CATEGORY", "GLOBAL_PRELOAD_BUFFER", "QUERY_PARAM_ANONYMOUS_ID_KEY", "QUERY_PARAM_USER_ID_KEY", "QUERY_PARAM_TRACK_EVENT_NAME_KEY", "DEFAULT_SESSION_TIMEOUT_MS", "createExposedGlobals", "analyticsInstanceId", "RudderStackGlobals", "setExposedGlobal", "keyName", "getEventDataFromQueryString", "params", "dataTypeNamePrefix", "data", "startsWith", "dataKey", "substring", "retrievePreloadBufferEvents", "instance", "preloadedEventsArray", "getExposedGlobal", "retrieveEventsFromQueryString", "argumentsArray", "eventArgumentToQueryParamMap", "queryObject", "URLSearchParams", "location", "search", "unshift", "enqueuePreloadBufferEvents", "consumePreloadBufferedEvent", "analyticsInstance", "methodName", "shift", "callOptions", "LOG_CONTEXT_SEPARATOR", "SCRIPT_LOAD_ERROR", "id", "url", "getCircularReplacer", "excludeNull", "excludeKeys", "logger", "ancestors", "includes", "pop", "warn", "CIRCULAR_REFERENCE_WARNING", "context", "stringifyWithoutCircular", "err", "getMutatedError", "issue", "finalError", "message", "js<PERSON>ile<PERSON><PERSON><PERSON>", "timeout", "async", "extraAttributes", "Promise", "resolve", "reject", "document", "getElementById", "SCRIPT_ALREADY_EXISTS_ERROR", "timeoutID", "newScriptElement", "headElements", "getElementsByTagName", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "scriptElements", "parentNode", "headElement", "createElement", "append<PERSON><PERSON><PERSON>", "htmlElement", "insertScript", "createScriptElement", "onload", "onerror", "scriptElement", "src", "attributeName", "setAttribute", "clearTimeout", "setTimeout", "SCRIPT_LOAD_TIMEOUT_ERROR", "ExternalSrcLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "onError", "bind", "loadJSFile", "config", "isFireAndForget", "then", "catch", "error", "Symbol", "for", "t", "s", "h", "f", "o", "v", "S", "u", "d", "U", "y", "g", "_", "E", "brand", "subscribe", "toJSON", "peek", "defineProperty", "N", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "items", "enqueue", "item", "dequeue", "isEmpty", "size", "clear", "LOG_LEVEL_MAP", "LOG", "INFO", "DEBUG", "WARN", "ERROR", "NONE", "DEFAULT_LOG_LEVEL", "defaultLogger", "minLogLevel", "scope", "logProvider", "console", "log", "outputLog", "info", "debug", "logMethod", "toLowerCase", "formatLogData", "setScope", "scopeVal", "setMinLogLevel", "logLevel", "msg", "trim", "styledLogArgs", "SUPPORTED_STORAGE_TYPES", "DEFAULT_STORAGE_TYPE", "SOURCE_CONFIG_RESOLUTION_ERROR", "XHR_REQUEST_ERROR", "prefix", "READY_CALLBACK_INVOKE_ERROR", "CDN_INT_DIR", "CDN_PLUGINS_DIR", "URL_PATTERN", "BUILD_TYPE", "SDK_CDN_BASE_URL", "CDN_ARCH_VERSION_DIR", "DEST_SDK_BASE_URL", "PLUGINS_BASE_URL", "DEFAULT_CONFIG_BE_URL", "DEFAULT_ERROR_REPORTING_PROVIDER", "DEFAULT_STORAGE_ENCRYPTION_VERSION", "DEFAULT_DATA_PLANE_EVENTS_TRANSPORT", "ConsentManagersToPluginNameMap", "oneTrust", "ketch", "custom", "ErrorReportingProvidersToPluginNameMap", "StorageEncryptionVersionsToPluginNameMap", "legacy", "DataPlaneEventsTransportToPluginNameMap", "beacon", "loadOptionsState", "signal", "configUrl", "loadIntegration", "sessions", "autoTrack", "sameSiteCookie", "polyfillIfRequired", "integrations", "All", "useBeacon", "beaconQueueOptions", "destinationsQueueOptions", "queueOptions", "lockIntegrationsVersion", "uaChTrackLevel", "plugins", "useGlobalIntegrationsConfigInEvents", "bufferDataPlaneEventsUntilReady", "dataPlaneEventsBufferTimeout", "storage", "encryption", "version", "migrate", "sendAdblockPageOptions", "useServerSideCookies", "USER_SESSION_STORAGE_KEYS", "userTraits", "anonymousId", "groupTraits", "initialReferrer", "initialReferringDomain", "sessionInfo", "authToken", "DEFAULT_USER_SESSION_VALUES", "defaultSessionConfiguration", "sessionState", "capabilitiesState", "isOnline", "isLocalStorageAvailable", "isCookieStorageAvailable", "isSessionStorageAvailable", "isBeaconAvailable", "isLegacyDOM", "isUaCHAvailable", "isCryptoAvailable", "isIE11", "isAdBlocked", "reportingState", "isErrorReportingEnabled", "isMetricsReportingEnabled", "errorReportingProviderPluginName", "isErrorReportingPluginLoaded", "sourceConfigState", "lifecycleState", "activeDataplaneUrl", "integrationsCDNPath", "pluginsCDNPath", "sourceConfigUrl", "status", "initialized", "loaded", "readyCallbacks", "<PERSON><PERSON><PERSON>", "dataPlaneUrl", "consentsState", "enabled", "activeConsentManagerPluginName", "preConsent", "postConsent", "resolutionStrategy", "provider", "metadata", "metricsState", "retries", "dropped", "sent", "queued", "triggered", "contextState", "app", "namespace", "installType", "library", "snippetVersion", "RudderSnippetVersion", "userAgent", "device", "network", "os", "locale", "screen", "density", "width", "height", "innerWidth", "innerHeight", "timezone", "nativeDestinationsState", "configuredDestinations", "activeDestinations", "loadOnlyIntegrations", "failedDestinations", "initializedDestinations", "clientDestinationsReady", "integrationsConfig", "eventBufferState", "toBeProcessedArray", "readyCallbacksArray", "pluginsState", "ready", "loadedPlugins", "failedPlugins", "pluginsToLoadFromConfig", "activePlugins", "totalPluginsToLoad", "storageState", "encryptionPluginName", "cookie", "entries", "trulyAnonymousTracking", "serverSideCookiesState", "isEnabledServerSideCookies", "dataServiceUrl", "dataPlaneEventsState", "eventsQueuePluginName", "deliveryEnabled", "state", "capabilities", "consents", "eventB<PERSON>er", "lifecycle", "loadOptions", "metrics", "nativeDestinations", "reporting", "session", "serverCookies", "dataPlaneEvents", "defaultPluginEngine", "by<PERSON><PERSON>", "cache", "throws", "register", "plugin", "errorMessage", "PLUGIN_ALREADY_EXISTS_ERROR", "pluginName", "pos", "pluginItem", "deps", "Math", "min", "splice", "initialize", "unregister", "indexOf", "PLUGIN_ENGINE_BUG_ERROR", "getPlugin", "getPlugins", "extPoint", "lifeCycleName", "filter", "some", "dependency", "notExistDeps", "PLUGIN_DEPS_ERROR", "hasV<PERSON>ueByPath", "processRawPlugins", "invoke", "allowMultiple", "args", "extensionPointName", "noCall", "endsWith", "replace", "extensionPointNameParts", "pluginMethodPath", "method", "PLUGIN_INVOCATION_ERROR", "invoke<PERSON><PERSON><PERSON>", "invokeMultiple", "FAILED_REQUEST_ERR_MSG_PREFIX", "ERROR_MESSAGES_TO_BE_FILTERED", "defaultErrorHandler", "pluginEngine", "errorBuffer", "attachEffect", "attachErrorListeners", "addEventListener", "init", "externalSrcLoader", "errReportingInitVal", "client", "errReportingClient", "customMessage", "shouldAlwaysThrow", "errorType", "ErrorEvent", "Event", "eventTarget", "target", "localName", "dataset", "loader", "isnonnativesdk", "processError", "normalizedError", "notifyError", "leaveBreadcrumb", "breadcrumb", "isAllowedToBeNotified", "isNonCloudDestination", "destination", "connectionMode", "useNativeSDKToSend", "useNativeSDK", "getNonCloudDestinations", "destinations", "pluginNamesList", "federatedModulesBuildPluginImports", "activePluginNames", "remotePlugins", "lazyLoadImport", "__federation_method_getRemote", "module", "__federation_method_wrapDefault", "getFederatedModuleImport", "getRemotePluginsMap", "pluginsInventory", "PluginsManager", "engine", "setActivePlugins", "registerLocalPlugins", "registerRemotePlugins", "attachEffects", "effect", "batch", "getPluginsToLoadBasedOnConfig", "pluginGroupsToProcess", "configurationStatus", "configurationStatusStr", "activePluginName", "supportedPlugins", "values", "shouldAddMissingPlugins", "basePlugins", "shouldApplyDeviceModeTransformation", "group", "addMissingPlugins", "pluginsToConfigure", "missingPlugins", "generateMisconfiguredPluginsWarning", "isSinglePlugin", "pluginsString", "baseWarning", "pluginsToLoad", "availablePlugins", "localPlugin", "remotePluginsList", "all", "remotePluginKey", "remotePluginModule", "default", "unregisterLocalPlugins", "responseTextToJson", "responseText", "parse", "DEFAULT_XHR_REQUEST_OPTIONS", "headers", "Accept", "createXhrRequestOptions", "basicAuthHeader", "requestOptions", "Authorization", "xhrRequest", "sendRawData", "xhr", "XMLHttpRequest", "xhrError", "ontimeout", "statusText", "response", "open", "withCredentials", "headerName", "setRequestHeader", "send", "XHR_SEND_ERROR", "HttpClient", "getData", "isRawResponse", "details", "reason", "getAsyncData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noBtoa", "authVal", "reset<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultHttpClient", "COOKIE_STORAGE", "LOCAL_STORAGE", "SESSION_STORAGE", "MEMORY_STORAGE", "NO_STORAGE", "CLIENT_DATA_STORE_COOKIE", "CLIENT_DATA_STORE_LS", "CLIENT_DATA_STORE_SESSION", "USER_SESSION_KEYS", "storageClientDataStoreNameMap", "hasUAClientHints", "navigator", "userAgentData", "legacyJSEngineRequiredPolyfills", "URL", "Number.isNaN", "isNaN", "Number.isInteger", "Array.from", "Array.prototype.find", "find", "Array.prototype.includes", "String.prototype.endsWith", "String.prototype.startsWith", "String.prototype.includes", "String.prototype.replaceAll", "replaceAll", "String.fromCodePoint", "Object.entries", "Object.values", "Object.assign", "assign", "Object.fromEntries", "fromEntries", "Element.prototype.dataset", "isDatasetAvailable", "testElement", "aB", "TextDecoder", "requestAnimationFrame", "cancelAnimationFrame", "CustomEvent", "navigator.sendBeacon", "sendBeacon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "Set", "getScreenDetails", "screenDetails", "devicePixelRatio", "isStorageQuotaExceeded", "isQuotaExceededError", "code", "DOMException", "isStorageAvailable", "storageInstance", "testData", "localStorage", "sessionStorage", "setItem", "getItem", "removeItem", "msgPrefix", "encodeURIComponent", "decode", "decodeURIComponent", "str", "pairs", "pair", "pairItem", "optionsConfig", "cookieString", "maxage", "expires", "domain", "toUTCString", "samesite", "secure", "levelsFunc", "host", "href", "hostname", "legacyGetHostname", "parts", "last", "levels", "parseInt", "getDefaultCookieOptions", "topDomain", "cname", "opts", "Cookie<PERSON>torage", "static", "isSupportAvailable", "isEnabled", "globalSingleton", "configure", "sameDomainCookiesOnly", "defaultInMemoryStorage", "exports", "isJSON", "deserialize", "dealIncognito", "_KEY", "_VALUE", "window", "inMemoryStorage", "_data", "Store", "ret", "has", "substr", "dt", "len", "remove", "arr", "_Store", "store", "argm", "factory", "defaultLocalStorage", "getDefaultLocalStorageOptions", "defaultSessionStorage", "getDefaultSessionStorageOptions", "getStorageEngine", "configureStorageEngines", "cookieStorageOptions", "localStorageOptions", "inMemoryStorageOptions", "sessionStorageOptions", "configureCookieStorageEngine", "configureInMemoryStorageEngine", "configureSessionStorageEngine", "pluginsManager", "isEncrypted", "validKeys", "noKeyValidation", "noCompoundKey", "originalEngine", "createValidKey", "<PERSON><PERSON><PERSON>", "validKeyName", "swapQueueStoreToInMemoryEngine", "<PERSON><PERSON><PERSON>", "encrypt", "STORE_DATA_SAVE_ERROR", "decrypt", "STORE_DATA_FETCH_ERROR", "getOriginalEngine", "crypto", "mode", "noEncryption", "formattedValue", "StoreManager", "stores", "isInitialized", "secureCookie", "set<PERSON><PERSON>ie<PERSON><PERSON>in", "initClientDataStores", "initializeStorageState", "storageType", "setStore", "globalStorageType", "entriesOptions", "postConsentStorageOpts", "storageEntries", "<PERSON><PERSON><PERSON>", "storageKey", "configuredStorageType", "preConsentStorageType", "getStorageTypeFromPreConsentIfApplicable", "overriddenStorageType", "strategy", "finalStorageType", "getResolvedStorageTypeForEntry", "STORAGE_UNAVAILABLE_WARNING", "entry", "selectedStorageType", "storeConfig", "storageEngine", "getStore", "isValidURL", "validateLoadArgs", "WRITE_KEY_VALIDATION_ERROR", "validateWriteKey", "DATA_PLANE_URL_VALIDATION_ERROR", "validateDataPlaneUrl", "getDataServiceUrl", "endpoint", "url<PERSON>bj", "protocol", "getTopDomainUrl", "removeTrailingSlashes", "getDomain", "getReferringDomain", "referrer", "extractUTMParameters", "UTM_PREFIX", "searchParams", "sParam", "utmParam", "DEFAULT_REGION", "validateResidencyServerRegion", "residencyServerRegion", "resolveDataPlaneUrl", "dataplanes", "serverUrl", "defaultUrl", "urls", "elem", "getDefaultUrlOfRegion", "DEFAULT_PRE_CONSENT_STORAGE_STRATEGY", "DEFAULT_PRE_CONSENT_EVENTS_DELIVERY_TYPE", "DEFAULT_INTEGRATIONS_CONFIG", "isValidConsentsData", "getConsentManagerInfo", "consentManagementOpts", "consentManagerPluginName", "UNSUPPORTED_CONSENT_MANAGER_ERROR", "consentManagersToPluginNameMap", "getConsentManagementData", "allowedConsentIds", "deniedConsentIds", "consentsData", "getSDKUrl", "scripts", "sdkFileNameRegex", "script", "getAttribute", "updateReportingState", "res", "sourceConfig", "errorReportingProvidersToPluginNameMap", "defaultProvider", "statsCollection", "errors", "chrome", "runtime", "errReportingProvider", "getErrorReportingProviderNameFromConfig", "errReportingProviderPlugin", "updateStorageStateFromLoadOptions", "dataServiceEndpoint", "storageOptsFromLoad", "isValidStorageType", "STORAGE_TYPE_VALIDATION_WARNING", "defaultStorageType", "storageEncryptionVersion", "UNSUPPORTED_STORAGE_ENCRYPTION_VERSION_WARNING", "storageEncryptionVersionsToPluginNameMap", "defaultVersion", "configuredMigrationValue", "finalMigrationVal", "STORAGE_DATA_MIGRATION_OVERRIDE_WARNING", "cookieOptions", "updateConsentsStateFromLoadOptions", "consentManagement", "preConsentOpts", "storageStrategy", "UNSUPPORTED_PRE_CONSENT_STORAGE_STRATEGY", "selectedStrategy", "eventsDeliveryType", "events", "delivery", "UNSUPPORTED_PRE_CONSENT_EVENTS_DELIVERY_TYPE", "selectedDeliveryType", "defaultDeliveryType", "updateDataPlaneEventsStateFromLoadOptions", "defaultEventsQueuePluginName", "getSourceConfigURL", "defSearchParams", "build", "origin", "pathname", "configUrlInstance", "INVALID_CONFIG_URL_WARNING", "ConfigManager", "httpClient", "processConfig", "intgCdnUrl", "getIntegrationsCDNPath", "requiredVersion", "customIntegrationsCDNPath", "sdkURL", "concat", "destSDKBaseURL", "customPluginsCDNPath", "getPluginsCDNPath", "pluginsSDKBaseURL", "getConfig", "isValidSourceConfig", "residencyServer", "deleted", "displayName", "destinationDefinition", "propagateEventsUntransformedOnError", "userFriendlyId", "filterEnabledDestination", "workspaceId", "resp", "cmpMetadata", "consentManagementMetadata", "providers", "updateConsentsState", "sourceConfigFunc", "getSourceConfig", "pRes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getDefaultPageProperties", "canonicalUrl", "getCanonicalUrl", "tags", "tag", "tabUrl", "pageUrl", "urlWithoutHash", "getUrlWithoutHash", "title", "referring_domain", "tab_url", "POLYFILL_URL", "POLYFILL_SCRIPT_ID", "CapabilitiesManager", "onReady", "prepareBrowserCapabilities", "attachWindowListeners", "detectBrowserCapabilities", "getRandomValues", "match", "onLine", "getUserAgent", "brave", "isBrave", "matchedArr", "language", "browserLanguage", "getTimezone", "getUserAgentClientHint", "level", "getHighEntropyValues", "ua", "uach", "sendAdblockPage", "detectAdBlockers", "baseUrl", "responseURL", "isLegacyJSEngine", "requiredCapabilitiesList", "needsPolyfill", "isCapabilityMissing", "customPolyfillUrl", "polyfillURL", "polyfillUrl", "INVALID_POLYFILL_URL_WARNING", "isDefaultPolyfillService", "polyfillCallbackName", "polyfillCallback", "scriptId", "POLYFILL_SCRIPT_LOAD_ERROR", "func", "thisArg", "delay", "timeoutId", "debounce", "BUFFER", "HEX", "IDX", "v4", "tmp", "out", "generateUUID", "uuidSecure", "num", "random", "uuid", "TOP_LEVEL_ELEMENTS", "CONTEXT_RESERVED_ELEMENTS", "RESERVED_ELEMENTS", "isNumber", "isPositiveInteger", "hasSessionExpired", "expiresAt", "timestamp", "now", "isManualSessionIdValid", "sessionId", "minimumLength", "INVALID_SESSION_ID_WARNING", "minSessionIdLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generateManualTrackingSession", "sessionStart", "manualTrack", "isStorageTypeValidForStoringData", "generateAnonymousId", "getContextPageProperties", "pageProps", "curPageProps", "ctxPageProps", "initial_referrer", "initial_referring_domain", "checkForReservedElementsInObject", "parent<PERSON><PERSON><PERSON><PERSON>", "property", "RESERVED_KEYWORD_WARNING", "reservedElements", "processOptions", "rudderEvent", "updateTopLevelEventElements", "originalTimestamp", "getMergedContext", "rudderContext", "tempContext", "getEventIntegrationsConfig", "finalIntgConfig", "getEnrichedEvent", "commonEventData", "channel", "campaign", "page", "toISOString", "messageId", "processedEvent", "checkForReservedElements", "contextualTraits", "RudderEventFactory", "generatePageEvent", "props", "getUpdatedPageProperties", "optionsPageProps", "generateTrackEvent", "generateIdentifyEvent", "generateAliasEvent", "enrichedEvent", "previousId", "generateGroupEvent", "groupEvent", "eventObj", "EventManager", "eventRepository", "userSessionManager", "eventFactory", "resume", "addEvent", "refreshSession", "UserSessionManager", "storeManager", "syncStorageDataToState", "registerEffects", "migrateStorageIfNeeded", "migrateDataFromPreviousStorage", "setUserId", "getUserId", "setUserTraits", "getUserTraits", "setGroupId", "getGroupId", "setGroupTraits", "getGroupTraits", "externalAnonymousIdCookieName", "anonymousIdOptions", "externalAnonymousId", "getExternalAnonymousIdByCookieName", "setAnonymousId", "getAnonymousId", "setAuthToken", "getAuthToken", "setInitialReferrerInfo", "configureSessionTracking", "getSessionInfo", "isPersistenceEnabledForStorageEntry", "configuredSessionTrackingInfo", "getConfiguredSessionTrackingInfo", "initialSessionInfo", "startOrRenewAutoTracking", "persistedInitialReferrer", "getInitialReferrer", "persistedInitialReferringDomain", "getInitialReferringDomain", "setInitialReferrer", "setInitialReferringDomain", "entryName", "storageTypesForMigration", "currentStorage", "curStore", "isDefinedNotNullAndNotEmptyString", "storeName", "storageEntry", "migratedVal", "configuredSessionTimeout", "TIMEOUT_NOT_NUMBER_WARNING", "defaultValue", "TIMEOUT_NOT_RECOMMENDED_WARNING", "minTimeout", "getEncryptedCookieData", "cookieData", "encryptedCookieData", "encryptedValue", "makeRequestToSetCookie", "reqType", "maxAge", "sameSite", "cookies", "setServerSideCookie", "cb", "each", "cookieValue", "before", "after", "syncValueToStorage", "cookieName", "rudderAmpLinkerParam", "finalAnonymousId", "linkerPluginsResult", "persistedAnonymousId", "getEntryValue", "autoCapturedAnonymousId", "getSessionId", "reset", "resetAnonymousId", "noNewSessionStart", "startManualTrackingInternal", "referringDomain", "sessionTimeout", "generateAutoTrackingSession", "start", "end", "token", "defaultOptionalPluginsList", "DATA_PLANE_QUEUE_EXT_POINT_PREFIX", "DESTINATIONS_QUEUE_EXT_POINT_PREFIX", "getFinalEvent", "finalEvent", "eventIntgConfig", "destinationsIntgConfig", "overriddenIntgOpts", "getOverriddenIntegrationOptions", "intgName", "reduce", "EventRepository", "dataplaneEventsQueue", "dmtEventsQueue", "destinationsEventsQueue", "bufferEventsBeforeConsent", "shouldBufferEventsForPreConsent", "shouldBufferDpEvents", "dest", "isHybridModeDestination", "scheduleTimeoutActive", "discardPreConsentEvents", "dpQEvent", "dQEvent", "dispatchSDKEvent", "customEvent", "detail", "rudderanalytics", "bubbles", "cancelable", "composed", "dispatchEvent", "Analytics", "preloadBuffer", "capabilitiesManager", "load", "clonedDataPlaneUrl", "clonedLoadOptions", "normalizeLoadOptions", "loadOptionsFromState", "normalizedLoadOpts", "startLifecycle", "onMounted", "onBrowserCapabilitiesReady", "onConfigured", "onPluginsReady", "onInitialized", "onLoaded", "onDestinationsReady", "prepareInternalServices", "loadConfig", "processBufferedEvents", "loadDestinations", "bufferedEvents", "bufferedEvent", "processDataInPreloadBuffer", "eventToProcess", "config<PERSON><PERSON><PERSON>", "eventManager", "totalDestinationsToLoad", "isBufferedInvocation", "track", "identify", "alias", "startSession", "endSession", "consent", "validOptions", "sendPageEvent", "trackConsent", "clonedOptions", "getValidPostConsentOptions", "sendTrackingEvents", "trackOptions", "pageOptions", "RudderAnalytics", "analyticsInstances", "defaultAnalyticsKey", "setDefaultInstanceKey", "getAnalyticsInstance", "triggerBufferedLoadEvent", "instanceId", "WRITE_KEY_NOT_A_STRING_ERROR", "consentMethodName", "consentEvents", "nonConsentEvents", "promotePreloadedConsentEventsToTop", "loadEvent", "getPreloadedLoadEvent"], "mappings": "6CAAA,MAAAA,EAAe,CACb,4BAA4B,YCANC,EAAeC,GACrC,OAAOA,IAAMF,CACf,UCOwBG,EAAQC,GAC9B,OAAgB,SAAAC,EAAGH,GACjB,OAAyB,IAArBI,UAAUC,QAAgBN,EAAeC,GACpCG,EAEAD,EAAGI,MAAMC,KAAMH,UAE1B,CACF,CCPe,SAASI,EAAQN,GAC9B,OAAO,SAASO,EAAGT,EAAGU,GACpB,OAAQN,UAAUC,QAChB,KAAM,EACJ,OAAOI,EACT,KAAM,EACJ,OAAOV,EAAeC,GAAKS,EAAKR,GAAQ,SAAUU,GAChD,OAAOT,EAAGF,EAAGW,EACf,IACF,QACE,OAAOZ,EAAeC,IAAMD,EAAeW,GAAKD,EAAKV,EAAeC,GAAKC,GAAQ,SAAUW,GACzF,OAAOV,EAAGU,EAAIF,EAChB,IAAKX,EAAeW,GAAKT,GAAQ,SAAUU,GACzC,OAAOT,EAAGF,EAAGW,EACf,IAAKT,EAAGF,EAAGU,GAEjB,CACF,CChBwB,SAAAG,EAAQX,GAC9B,OAAgB,SAAAY,EAAGd,EAAGU,EAAGK,GACvB,OAAQX,UAAUC,QAChB,KAAM,EACJ,OAAOS,EACT,KAAK,EACH,OAAOf,EAAeC,GAAKc,EAAKN,GAAQ,SAAUG,EAAIK,GACpD,OAAOd,EAAGF,EAAGW,EAAIK,EACnB,IACF,OACE,OAAOjB,EAAeC,IAAMD,EAAeW,GAAKI,EAAKf,EAAeC,GAAKQ,GAAQ,SAAUI,EAAII,GAC7F,OAAOd,EAAGU,EAAIF,EAAGM,EACnB,IAAKjB,EAAeW,GAAKF,GAAQ,SAAUG,EAAIK,GAC7C,OAAOd,EAAGF,EAAGW,EAAIK,EACnB,IAAKf,GAAQ,SAAUe,GACrB,OAAOd,EAAGF,EAAGU,EAAGM,EAClB,IACF,QACE,OAAOjB,EAAeC,IAAMD,EAAeW,IAAMX,EAAegB,GAAKD,EAAKf,EAAeC,IAAMD,EAAeW,GAAKF,GAAQ,SAAUI,EAAID,GACvI,OAAOT,EAAGU,EAAID,EAAII,EACpB,IAAKhB,EAAeC,IAAMD,EAAegB,GAAKP,GAAQ,SAAUI,EAAII,GAClE,OAAOd,EAAGU,EAAIF,EAAGM,EACnB,IAAKjB,EAAeW,IAAMX,EAAegB,GAAKP,GAAQ,SAAUG,EAAIK,GAClE,OAAOd,EAAGF,EAAGW,EAAIK,EACnB,IAAKjB,EAAeC,GAAKC,GAAQ,SAAUW,GACzC,OAAOV,EAAGU,EAAIF,EAAGK,EACnB,IAAKhB,EAAeW,GAAKT,GAAQ,SAAUU,GACzC,OAAOT,EAAGF,EAAGW,EAAII,EACnB,IAAKhB,EAAegB,GAAKd,GAAQ,SAAUe,GACzC,OAAOd,EAAGF,EAAGU,EAAGM,EAClB,IAAKd,EAAGF,EAAGU,EAAGK,GAEpB,CACF,CC7Ce,SAASE,EAAKC,EAAMC,GACjC,OAAOC,OAAOC,UAAUC,eAAeC,KAAKJ,EAAKD,EACnD,CC2BA,IAAIM,EAAoBvB,GAAQ,SAAcwB,GAC5C,OAAe,OAARA,EAAe,YAAiBC,IAARD,EAAoB,YAAcL,OAAOC,UAAUM,SAASJ,KAAKE,GAAKG,MAAM,GAAI,EACjH,IC/Be,SAASC,EAAUC,GAChC,MAA6C,oBAAtCV,OAAOC,UAAUM,SAASJ,KAAKO,EACxC,CCMA,QAAeC,OAAOC,WAAa,SAAoBC,GACrD,OAAOA,EAAK,KAAMA,CACpB,ECTe,SAASC,EAAKC,EAAQC,GACnC,ICFgCN,EDE5BO,EAAMF,EAAS,EAAIC,EAAK/B,OAAS8B,EAASA,EAC9C,OCHgCL,EDGfM,ECF4B,oBAAtChB,OAAOC,UAAUM,SAASJ,KAAKO,GDEbM,EAAKE,OAAOD,GAAOD,EAAKC,EACnD,UEOwBE,EAAOC,EAAOC,EAAMC,GAI1C,GAHAA,IAAQA,EAAM,IAAIC,GA6CpB,SAAsBC,GACpB,IAAIpB,SAAcoB,EAClB,OAAgB,MAATA,GAAyB,UAARpB,GAA4B,YAARA,CAC9C,CA7CMqB,CAAaL,GACf,OAAOA,EAET,IClBmCM,EDkB/BC,EAAO,SAAcC,GAEvB,IAAIC,EAAaP,EAAIQ,IAAIV,GACzB,GAAIS,EACF,OAAOA,EAGT,IAAK,IAAIE,KADTT,EAAIU,IAAIZ,EAAOQ,GACCR,EACVpB,OAAOC,UAAUC,eAAeC,KAAKiB,EAAOW,KAC9CH,EAAYG,GAAcZ,EAAOC,EAAMW,IAAM,EAAMT,IAGvD,OAAOM,CACT,EACA,OAAQxB,EAAKgB,IACX,IAAK,SACH,OAAOO,EAAK3B,OAAOiC,OAAOjC,OAAOkC,eAAed,KAClD,IAAK,QACH,OAAOO,EAAKQ,MAAMf,EAAMnC,SAC1B,IAAK,OACH,WAAWmD,KAAKhB,EAAMiB,WACxB,IAAK,SACH,OCxC+BX,EDwCXN,ECvCb,IAAAkB,OAAOZ,EAAQa,OAAQb,EAAQc,MAAQd,EAAQc,OAASd,EAAQe,OAAS,IAAM,KAAOf,EAAQgB,WAAa,IAAM,KAAOhB,EAAQiB,UAAY,IAAM,KAAOjB,EAAQkB,OAAS,IAAM,KAAOlB,EAAQmB,QAAU,IAAM,KAAOnB,EAAQoB,OAAS,IAAM,KDwCtP,IAAK,YACL,IAAK,aACL,IAAK,oBACL,IAAK,aACL,IAAK,cACL,IAAK,aACL,IAAK,cACL,IAAK,eACL,IAAK,eACL,IAAK,gBACL,IAAK,iBACH,OAAO1B,EAAMZ,QACf,QACE,OAAOY,EAEb,CAKA,IAAIG,EAA0B,WAC5B,SAASA,IACPpC,KAAKmC,IAAM,GACXnC,KAAKF,OAAS,CAChB,CA8CA,OA7CAsC,EAAWtB,UAAU+B,IAAM,SAAUD,EAAKX,GACxC,IAAI2B,EAAY5D,KAAK6D,KAAKjB,GACtBkB,EAAS9D,KAAKmC,IAAIyB,GACjBE,IACH9D,KAAKmC,IAAIyB,GAAaE,EAAS,IAEjCA,EAAOC,KAAK,CAACnB,EAAKX,IAClBjC,KAAKF,QAAU,CACjB,EACAsC,EAAWtB,UAAU+C,KAAO,SAAUjB,GACpC,IAAIgB,EAAY,GAChB,IAAK,IAAI3B,KAASW,EAChBgB,EAAUG,KAAKlD,OAAOC,UAAUM,SAASJ,KAAK4B,EAAIX,KAEpD,OAAO2B,EAAUI,MACnB,EACA5B,EAAWtB,UAAU6B,IAAM,SAAUC,GAKnC,GAAI5C,KAAKF,QAAU,IACjB,IAAK,IAAImE,KAASjE,KAACmC,IAEjB,IADA,IAAI2B,EAAS9D,KAAKmC,IAAI8B,GACbC,EAAI,EAAGA,EAAIJ,EAAOhE,OAAQoE,GAAK,EAAG,CAEzC,IADIC,EAAUL,EAAOI,IACT,KAAOtB,EACjB,OAAOuB,EAAQ,EAEnB,KARJ,CAYA,IAAIP,EAAY5D,KAAK6D,KAAKjB,GAE1B,GADIkB,EAAS9D,KAAKmC,IAAIyB,GAItB,IAASM,EAAI,EAAGA,EAAIJ,EAAOhE,OAAQoE,GAAK,EAAG,CACzC,IAAIC,EACJ,IADIA,EAAUL,EAAOI,IACT,KAAOtB,EACjB,OAAOuB,EAAQ,EAEnB,CAXA,CAYF,EACO/B,CACT,CAnD8B,GE/B1BgC,EAAqB1E,GAAQ,SAAeuC,GAC9C,OAAgB,MAATA,GAAwC,mBAAhBA,EAAMmC,MAAuBnC,EAAMmC,QAAUpC,EAAOC,EACrF,IC9Be,SAASoC,EAAMC,EAAQ1D,GAEpC,IADA,IAAIM,EAAMN,EACDsD,EAAI,EAAGA,EAAII,EAAOxE,OAAQoE,GAAK,EAAG,CACzC,GAAW,MAAPhD,EACF,OAEF,IAAI+C,EAAIK,EAAOJ,GAEbhD,EADEqD,EAAWN,GACPtC,EAAKsC,EAAG/C,GAERA,EAAI+C,EAEd,CACA,OAAO/C,CACT,CCYA,IAAIsD,EAA4BlE,GAAQ,SAAsBX,EAAI8E,EAAGC,GACnE,IACIC,EADAC,EAAS,CAAA,EAIb,IAAKD,KADLD,EAAIA,GAAK,CAAE,EADXD,EAAIA,GAAK,CAAE,EAGL/D,EAAKiE,EAAGF,KACVG,EAAOD,GAAKjE,EAAKiE,EAAGD,GAAK/E,EAAGgF,EAAGF,EAAEE,GAAID,EAAEC,IAAMF,EAAEE,IAGnD,IAAKA,KAAKD,EACJhE,EAAKiE,EAAGD,KAAOhE,EAAKiE,EAAGC,KACzBA,EAAOD,GAAKD,EAAEC,IAGlB,OAAOC,CACT,ICZIC,EAAgCvE,GAAQ,SAASuE,EAAiBlF,EAAImF,EAAMC,GAC9E,OAAOP,GAAa,SAAUG,EAAGK,EAAMC,GACrC,OAAI3D,EAAU0D,IAAS1D,EAAU2D,GACxBJ,EAAiBlF,EAAIqF,EAAMC,GAE3BtF,EAAGgF,EAAGK,EAAMC,EAEvB,GAAGH,EAAMC,EACX,ICVIG,EAA6B5E,GAAQ,SAAuBX,EAAImF,EAAMC,GACxE,OAAOF,GAAiB,SAAUF,EAAGK,EAAMC,GACzC,OAAOtF,EAAGqF,EAAMC,EAClB,GAAGH,EAAMC,EACX,ICNII,EAAoBlF,EAAQoE,GCN5Be,EAAsBnF,GAAQ,SAAgBoF,EAAMzE,GACtD,IAAIgE,EAAS,CAAA,EACb,IAAK,IAAIjE,KAAQC,EACXyE,EAAKzE,EAAID,GAAOA,EAAMC,KACxBgE,EAAOjE,GAAQC,EAAID,IAGvB,OAAOiE,CACT,ICxBA,MAAMU,EAAcrD,GACD,mBAAVA,GAAwBsD,QAAQtD,EAAMuD,aAAevD,EAAMjB,MAAQiB,EAAMlC,OAO5E0F,EAAYxD,GAAiD,iBAAVA,EAOnDyD,EAAUzD,GAAwC,OAAVA,EAOxC0D,EAAe1D,QAAoD,IAAVA,EAOzD2D,EAAqB3D,GAAwByD,EAAOzD,IAAU0D,EAAY1D,GAO1E4D,EAAa5D,IAAyB0D,EAAY1D,GAOlD6D,EAAuB7D,IAAyB2D,EAAkB3D,GAelE8D,EAAiBnF,GAA2BA,aAAeoF,MC7D3DC,EAAiBA,CAACrF,EAA0BsF,KAChD,MAAMC,EAAYD,EAAQE,MAAM,KAChC,OAAOjB,EAAKgB,EAAWvF,EAAI,EAWvByF,EAAsBpE,IACzByD,EAAOzD,IAA2B,iBAAVA,IAAuBe,MAAMsD,QAAQrE,GAO1DsE,EAAgCtE,IACnCyD,EAAOzD,IAAoD,oBAA1CpB,OAAOC,UAAUM,SAASJ,KAAKiB,GAE7CuE,EAA6BA,CACjCC,EACAC,KAEA,IAAK1D,MAAMsD,QAAQG,KAAezD,MAAMsD,QAAQI,GAC9C,OAAOtC,EAAMsC,GAGf,MAAMC,EAAcvC,EAAMqC,GAQ1B,OAPAC,EAAWE,SAAQ,CAAC3E,EAAO4E,KACzBF,EAAYE,GACV7D,MAAMsD,QAAQrE,IAAUoE,EAAmBpE,GAEvC6E,EAAeH,EAAYE,GAAQ5E,GACnCA,CAAK,IAEN0E,CAAW,EAGdG,EAAiBA,CACrBC,EACAC,IACM9B,EAAcsB,EAA4BO,EAAYC,GAOxDC,EAAuBhF,GAC3BsE,EAA0BtE,IAAUpB,OAAOqG,KAAKjF,GAAcnC,OAAS,EAOnEqH,EAAkDvG,IACtD,MAAMgE,EAASQ,EAAOS,EAAWjF,GAQjC,OAPAC,OAAOqG,KAAKtC,GAAQgC,SAAQhE,IAC1B,MAAMX,EAAQ2C,EAAOhC,GACjB2D,EAA0BtE,KAC5B2C,EAAOhC,GAAOuE,EAAsBlF,GACtC,IAGK2C,CAAM,EAQTwC,EAAyDxG,IAC7D,MAAMgE,EAASQ,EAAOU,EAAqBlF,GAQ3C,OAPAC,OAAOqG,KAAKtC,GAAQgC,SAAQhE,IAC1B,MAAMX,EAAQ2C,EAAOhC,GACjB2D,EAA0BtE,KAC5B2C,EAAOhC,GAAOwE,EAA6BnF,GAC7C,IAGK2C,CAAM,EC7ETyC,EAAgBnG,IACpB,IAAIoG,EAASpG,EACb,IAAKuE,EAASvE,KAAS0E,EAAkB1E,GACvC,IACEoG,EAASC,KAAKC,UAAUtG,EAC1B,CAAE,MAAOuG,GACPH,EAAS,IACX,CAEF,OAAOA,CAAM,EAgCTI,EAAYzF,GAVK0F,KACrB,MAAMC,EAAY5E,MAAM6E,KAAKF,GAAOpG,GAAKuG,OAAOC,cAAcxG,KAAIyC,KAAK,IACvE,OAAQgE,WAA6BC,KAAKL,EAAU,EAQVM,EAAc,IAAIC,aAAcC,OAAOnG,ICH7EoG,EAA6BA,CACjCC,EACAC,EACAC,EACAC,EACAC,KAEA,MAAMC,EAA2B,CAC/BL,SAAUA,EACVC,KAAMA,EACNC,WAAYA,EACZC,QAASA,GAqFX,OAlFInD,EAAWoD,KACbC,EAAQD,SAAWA,GAGjBpD,EAAWmD,KACbE,EAAQL,SAAWA,EACnBK,EAAQJ,KAAOA,EACfI,EAAQH,WAAaA,SACdG,EAAQF,QACfE,EAAQD,SAAWD,GAGjBnD,EAAWkD,KACbG,EAAQL,SAAWA,EACnBK,EAAQJ,KAAOA,SACRI,EAAQH,kBACRG,EAAQF,QACfE,EAAQD,SAAWF,GAGjBlD,EAAWiD,KACbI,EAAQL,SAAWA,SACZK,EAAQJ,YACRI,EAAQH,kBACRG,EAAQF,QACfE,EAAQD,SAAWH,GAGjBjD,EAAWgD,YACNK,EAAQL,gBACRK,EAAQJ,YACRI,EAAQH,kBACRG,EAAQF,QACfE,EAAQD,SAAWJ,GAGjB/B,EAA0B+B,WACrBK,EAAQJ,YACRI,EAAQL,SACfK,EAAQH,WAAaF,EACrBK,EAAQF,QAAUF,GACThC,EAA0BgC,YAC5BI,EAAQJ,KACfI,EAAQH,WAAaD,EACrBI,EAAQF,QAAWnD,EAAWkD,GAAqD,KAAtCA,GAK3C/C,EAAS6C,KAAc7C,EAAS8C,YAC3BI,EAAQL,SACfK,EAAQJ,KAAOD,GAMZzC,EAAU8C,EAAQL,kBACdK,EAAQL,SAGZzC,EAAU8C,EAAQJ,cACdI,EAAQJ,KAGjBI,EAAQH,WAAaG,EAAQH,WAAapE,EAAMuE,EAAQH,YAAc,CAAE,EAEpE3C,EAAU8C,EAAQF,SACpBE,EAAQF,QAAUrE,EAAMuE,EAAQF,gBAEzBE,EAAQF,QAIjBE,EAAQH,WAAa1B,EACnBP,EAA0BoC,EAAQH,YAAcG,EAAQH,WAAa,CAAA,EACrE,CACED,KAAM9C,EAASkD,EAAQJ,MAAQI,EAAQJ,KAAO,KAC9CD,SAAU7C,EAASkD,EAAQL,UAAYK,EAAQL,SAAW,OAIvDK,CAAO,EAMVC,EAA8BA,CAClCC,EACAL,EACAC,EACAC,KAEA,MAAMC,EAA4B,CAChCJ,KAAMM,EACNL,WAAYA,EACZC,QAASA,GA8BX,OA3BInD,EAAWoD,KACbC,EAAQD,SAAWA,GAGjBpD,EAAWmD,KACbE,EAAQH,WAAaA,SACdG,EAAQF,QACfE,EAAQD,SAAWD,GAGjBnD,EAAWkD,YACNG,EAAQH,kBACRG,EAAQF,QACfE,EAAQD,SAAWF,GAMrBG,EAAQH,WAAa1C,EAAoB6C,EAAQH,YAAcpE,EAAMuE,EAAQH,YAAc,CAAE,EAEzF3C,EAAU8C,EAAQF,SACpBE,EAAQF,QAAUrE,EAAMuE,EAAQF,gBAEzBE,EAAQF,QAGVE,CAAO,EAMVG,EAAiCA,CACrCC,EACAC,EACAP,EACAC,KAEA,MAAMC,EAA+B,CACnCI,OAAQA,EACRC,OAAQA,EACRP,QAASA,GAkDX,OA/CInD,EAAWoD,KACbC,EAAQD,SAAWA,GAGjBpD,EAAWmD,KACbE,EAAQI,OAASA,EACjBJ,EAAQK,OAASA,SACVL,EAAQF,QACfE,EAAQD,SAAWD,GAGjBnD,EAAW0D,KACbL,EAAQI,OAASA,SACVJ,EAAQK,cACRL,EAAQF,QACfE,EAAQD,SAAWM,IAGjBzC,EAA0BwC,IAAWrD,EAAOqD,MAG9CJ,EAAQI,OAAS,KACjBJ,EAAQK,OAASD,EACjBJ,EAAQF,QAAUO,GAMhBnD,EAAU8C,EAAQI,QACpBJ,EAAQI,OAAS1B,EAAasB,EAAQI,eAE/BJ,EAAQI,OAGbxC,EAA0BoC,EAAQK,QACpCL,EAAQK,OAAS5E,EAAMuE,EAAQK,eAExBL,EAAQK,OAGbnD,EAAU8C,EAAQF,SACpBE,EAAQF,QAAUrE,EAAMuE,EAAQF,gBAEzBE,EAAQF,QAGVE,CAAO,EAMVM,EAA8BA,CAClCC,EACArB,EACAY,EACAC,KAEA,MAAMC,EAA4B,CAChCO,GAAIA,EACJrB,KAAMA,EACNY,QAASA,GAyDX,OAtDInD,EAAWoD,KACbC,EAAQD,SAAWA,GAGjBpD,EAAWmD,KACbE,EAAQO,GAAKA,EACbP,EAAQd,KAAOA,SACRc,EAAQF,QACfE,EAAQD,SAAWD,GAGjBnD,EAAWuC,IACbc,EAAQO,GAAKA,SACNP,EAAQd,YACRc,EAAQF,QACfE,EAAQD,SAAWb,IACVtB,EAA0BsB,IAASnC,EAAOmC,MACnDc,EAAQO,GAAKA,SACNP,EAAQd,KACfc,EAAQF,QAAUZ,GAGhBvC,EAAW4D,WACNP,EAAQO,UACRP,EAAQd,YACRc,EAAQF,QACfE,EAAQD,SAAWQ,IACV3C,EAA0B2C,IAAOxD,EAAOwD,aAC1CP,EAAQO,UACRP,EAAQd,KACfc,EAAQF,QAAUS,GAMhBrD,EAAU8C,EAAQO,IACpBP,EAAQO,GAAK7B,EAAasB,EAAQO,WAE3BP,EAAQO,GAGbrD,EAAU8C,EAAQd,MACpBc,EAAQd,KAAOR,EAAasB,EAAQd,aAE7Bc,EAAQd,KAGbhC,EAAU8C,EAAQF,SACpBE,EAAQF,QAAUrE,EAAMuE,EAAQF,gBAEzBE,EAAQF,QAGVE,CAAO,EAMVQ,EAA8BA,CAClCC,EACAJ,EACAP,EACAC,KAEA,MAAMC,EAA4B,CAChCS,QAASA,EACTJ,OAAQA,EACRP,QAASA,GAqDX,OAlDInD,EAAWoD,KACbC,EAAQD,SAAWA,GAGjBpD,EAAWmD,KACbE,EAAQS,QAAUA,EAClBT,EAAQK,OAASA,SACVL,EAAQF,QACfE,EAAQD,SAAWD,GAGjBnD,EAAW0D,KACbL,EAAQS,QAAUA,SACXT,EAAQK,cACRL,EAAQF,QACfE,EAAQD,SAAWM,GAIjB1D,EAAW8D,IAEbT,EAAQS,QAAU,YACXT,EAAQK,cACRL,EAAQF,QACfE,EAAQD,SAAWU,IACV7C,EAA0B6C,IAAY1D,EAAO0D,MAGtDT,EAAQS,QAAU,KAClBT,EAAQK,OAASI,EACjBT,EAAQF,QAAWnD,EAAW0D,GAA6C,KAAlCA,GAMvCnD,EAAU8C,EAAQS,SACpBT,EAAQS,QAAU/B,EAAasB,EAAQS,gBAEhCT,EAAQS,QAGjBT,EAAQK,OAASzC,EAA0BoC,EAAQK,QAAU5E,EAAMuE,EAAQK,QAAU,CAAA,EAEjFnD,EAAU8C,EAAQF,SACpBE,EAAQF,QAAUrE,EAAMuE,EAAQF,gBAEzBE,EAAQF,QAGVE,CAAO,EC7YVU,EAAuB,sBACvBC,EAAiB,gBACjBC,EAAgB,eAChBC,EAAkB,iBAClBC,EAAuB,qBACvBC,EAAgB,eAChBC,EAAgB,eAChBC,EAAgB,eAMhBC,EAAS,yBACTC,EAAiB,gBCdjBC,EAAW,4BACXC,GAAc,QAIdC,GAAwB,qBAGxBC,GAAwB,wBCNxBC,GAA+B,UAC/BC,GAA0B,UAC1BC,GAAmC,YCFnCC,GAA6B,KCK7BC,GAAuBA,CAACC,EAAsB,SAC5CxC,WAA6ByC,qBAChCzC,WAA6ByC,mBAAqB,CAAyB,GAGxEzC,WAA6ByC,mBAAmBD,KACnDxC,WAA6ByC,mBAAmBD,GAC/C,GACJ,EAMIE,GAAmBA,CAACC,EAAiB1I,EAAauI,EAAsB,SAC5ED,GAAqBC,GACnBxC,WAA6ByC,mBAAmBD,GAChDG,GACE1I,CAAK,ECAX,MAAM2I,GAA8BA,CAClCC,EACAC,KAEA,MAAMC,EAAyC,GAW/C,OATAF,EAAOjE,SAAQ,CAAC3E,EAAOW,KACrB,GAAIA,EAAIoI,WAAWF,GAAqB,CAEtC,MAAMG,EAAUrI,EAAIsI,UAAUJ,EAAmBhL,QAEjDiL,EAAKE,GAAWJ,EAAOlI,IAAIC,EAC7B,KAGKmI,CAAI,EA6FPI,GAA+BC,IACnC,MAAMC,EDvGiBC,EACvBX,EACAH,EAAsB,SAEtBD,GAAqBC,GACZxC,WAA6ByC,mBAAmBD,GACvDG,ICiG4BW,CAAiBpB,KAC7C,GAzFkCqB,EAACC,EAAuC,MAE5E,MAAMC,EHhDyB,aGgDzBA,EH/C4B,YGoD5BC,EAAc,IAAIC,gBAAgB3D,WAAW4D,SAASC,QAGxDH,EAAY/I,IAAI0H,KAClBmB,EAAeM,QAAQ,CACrB,QACAJ,EAAY/I,IAAI0H,IAChBO,GAA4Bc,EAAaD,KAKzCC,EAAY/I,IAAIyH,KAClBoB,EAAeM,QAAQ,CACrB,WACAJ,EAAY/I,IAAIyH,IAChBQ,GAA4Bc,EAAaD,KAKzCC,EAAY/I,IAAIwH,KAClBqB,EAAeM,QAAQ,CAAC,iBAAkBJ,EAAY/I,IAAIwH,KAC5D,EA8DAoB,CAA8BF,GAG1BA,EAAqBvL,OAAS,IAChCsL,EAASW,2BAA2BV,GACpCX,GAAiBR,GAAuB,IAC1C,EAGI8B,GAA8BA,CAACnD,EAAYoD,KAC/C,MAAMC,EAAarD,EAAMsD,QACzB,IAAIC,EAEJ,GAAI9G,EAAY2G,EAA0BC,IAAc,CACtD,OAAQA,GACN,IAAK,OACHE,EAAc/D,KAA+BQ,GAC7C,MACF,IAAK,QACHuD,EAAcxD,KAAgCC,GAC9C,MACF,IAAK,WACHuD,EAActD,KAAmCD,GACjD,MACF,IAAK,QACHuD,EAAcnD,KAAgCJ,GAC9C,MACF,IAAK,QACHuD,EAAcjD,KAAgCN,GAC9C,MACF,QACGoD,EAA0BC,MAAerD,GAI1CuD,GACDH,EAA0BC,GAAYE,EAE3C,GChLIC,GAAwB,MAKxBC,GAAoBA,CAACC,EAAYC,IACrC,0CAA0CD,gBAAiBC,MCCvDC,GAAsBA,CAC1BC,EACAC,EACAC,KAEA,MAAMC,EAAmB,GAIzB,OAAO,SAAUjK,EAAKX,GACpB,KAAI0K,GAAaG,SAASlK,IAItB8J,GAAe9G,EAAkB3D,IAArC,CAIA,GAAqB,iBAAVA,GAAsByD,EAAOzD,GACtC,OAAOA,EAMT,KAAO4K,EAAU/M,OAAS,GAAK+M,EAAUA,EAAU/M,OAAS,KAAOE,MACjE6M,EAAUE,MAGZ,OAAIF,EAAUC,SAAS7K,IACrB2K,GAAQI,KD1BqBC,EAACC,EAAiBtK,IACnD,GAAGsK,IAAUb,4EAA+FzJ,uCCyB3FqK,CAhCI,gBAgCuCrK,IACjD,yBAGTiK,EAAU9I,KAAK9B,GACRA,EAnBP,CAoBF,CAAC,EAWGkL,GAA2BA,CAC/BlL,EACAyK,EACAC,EACAC,KAEA,IACE,OAAOrF,KAAKC,UAAUvF,EAAOwK,GAAoBC,EAAaC,EAAaC,GAC7E,CAAE,MAAOQ,GAEP,OADAR,GAAQI,KDjDmB,gDCiDUI,GAC1B,IACb,GCxDIC,GAAkBA,CAACD,EAAUE,KACjC,IAAIC,EAAaH,EAMjB,OALKrH,EAAcqH,GAGhBG,EAAqBC,QAAU,GAAGF,MAAUF,EAAII,UAFjDD,EAAa,IAAIvH,MAAM,GAAGsH,MAAUH,GAAyBC,MAIxDG,CAAU,ECsEbE,GAAeA,CACnBjB,EACAD,EACAmB,EACAC,GAAQ,EACRC,IAEA,IAAIC,SAAQ,CAACC,EAASC,KACCC,SAASC,eAAe1B,IAE3CwB,EAAO,IAAI/H,MH9FoBuG,IACnC,yBAAyBA,kFG6FJ2B,CAA4B3B,KAG/C,IACE,IAAI4B,EAhDYC,KAEpB,MAAMC,EAAeL,SAASM,qBAAqB,QACnD,GAAID,EAAavO,OAAS,EAExB,YADAuO,EAAa,IAAIE,aAAaH,EAAkBC,EAAa,IAAIG,YAKnE,MAAMC,EAAiBT,SAASM,qBAAqB,UACrD,GAAIG,EAAe3O,OAAS,GAAK2O,EAAe,IAAIC,WAElD,YADAD,EAAe,IAAIC,WAAWH,aAAaH,EAAkBK,EAAe,IAK9E,MAAME,EAAcX,SAASY,cAAc,QAC3CD,EAAYE,YAAYT,GAExB,MAAMU,EAAcd,SAASM,qBAAqB,QAAQ,GAC1DQ,GAAaP,aAAaI,EAAaG,EAAYN,WAAW,EAyC1DO,CA7FsBC,EAC1BxC,EACAD,EACAoB,GAAQ,EACRsB,EAAiE,KACjEC,EAA+B,KAC/BtB,EAA0C,CAAE,KAE5C,MAAMuB,EAAgBnB,SAASY,cAAc,UAc7C,OAbAO,EAAclO,KAAO,kBACrBkO,EAAcF,OAASA,EACvBE,EAAcD,QAAUA,EACxBC,EAAcC,IAAM5C,EACpB2C,EAAc5C,GAAKA,EACnB4C,EAAcxB,MAAQA,EAEtB9M,OAAOqG,KAAK0G,GAAiBhH,SAAQyI,IACnCF,EAAcG,aAAaD,EAAezB,EAAgByB,GAAyB,IAGrFF,EAAcG,aAAa,cCxCO,aD0C3BH,CAAa,EAuEHH,CAAoBxC,EAAKD,EAAIoB,GAX3BsB,KACZjH,WAA6BuH,aAAapB,GAC3CL,EAAQvB,EAAG,IAGG2C,KACblH,WAA6BuH,aAAapB,GAC3CJ,EAAO,IAAI/H,MAAMsG,GAAkBC,EAAIC,IAAM,GAImBoB,IAGlEO,EAAanG,WAA6BwH,YAAW,KACnDzB,EAAO,IAAI/H,MH7GeyJ,EAAClD,EAAYC,EAAakB,IAC1D,gBAAgBA,0DAAgEnB,gBAAiBC,MG4G1EiD,CAA0BlD,EAAIC,EAAKkB,IAAU,GAC7DA,EACL,CAAE,MAAON,GACPW,EAAOV,GAAgBD,EAAKd,GAAkBC,EAAIC,IACpD,KE9GJ,MAAMkD,GAGJC,iBAAkB,EAGlBnK,WAAAA,CACEoK,EACAhD,EACAc,ECpBoC,KDsBpC1N,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK0N,QAAUA,EACf1N,KAAK2P,gBAAkBpK,QAAQvF,KAAK4P,cACpC5P,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKA+P,UAAAA,CAAWC,GACT,MAAMxD,IAAEA,EAAGD,GAAEA,EAAEmB,QAAEA,EAAOC,MAAEA,EAAKjF,SAAEA,EAAQkF,gBAAEA,GAAoBoC,EACzDC,GAAmB3K,EAAWoD,GAEpC+E,GAAajB,EAAKD,EAAImB,GAAW1N,KAAK0N,QAASC,EAAOC,GACnDsC,MAAM3D,IACA0D,GACHvH,EAAS6D,EACX,IAED4D,OAAM/C,IACLpN,KAAK6P,QAAQzC,GACR6C,GACHvH,GACF,GAEN,CAKAmH,OAAAA,CAAQO,GACN,IAAIpQ,KAAK2P,gBAGP,MAAMS,EAFNpQ,KAAK4P,cAAcC,QAAQO,EX5CL,oBWgD1B,EEzDF,IAAMlM,GAAemM,OAAAC,IAAW,kBAsChC,SAASC,KACR,GAAIC,GAAa,EAChBA,SADD,CAQA,IAHA,IAAItM,EACAqM,GAAW,OAEU,IAAlBE,IAA6B,CACnC,IAAI/L,EAA6B+L,GAKjC,IAJAA,QAAgB,EAEhBC,cAEOhM,GAAsB,CAC5B,IAAMiM,EAA2BjM,EAAOiM,EAIxC,GAHAjM,EAAOiM,OAAA,EACPjM,EAAOgM,IAAU,IAlDH,EAoDRhM,EAAOgM,IAAsBlQ,GAAiBkE,GACnD,IACCA,EAAOlE,GACN,CAAA,MAAOkE,GACH6L,IACJrM,EAAQQ,EACR6L,GAAA,EAED,CAEF7L,EAASiM,CACT,CACD,CAID,GAHAD,GAAiB,EACjBF,KAEID,EACH,MAAMrM,CAnCN,CAmDF,CAAA,SAASQ,GAASR,GACjB,GAAIsM,GAAa,EAChB,OAAOtM,IA1DRsM,KA6DA,IACC,OAAOtM,GACP,CAAA,QACAqM,IACA,CACF,CAGA,IAAII,UAoBAF,QAAoC,EACpCD,GAAa,EACbE,GAAiB,EAIjBE,GAAgB,EAEpB,SAASnJ,GAAcvD,GACtB,QAAoB,IAAhByM,GAAJ,CAIA,IAAIJ,EAAOrM,EAAOxC,EAClB,QAAA,IAAI6O,GAAsBA,EAAKA,IAAYI,GAmC1C,OAtBAJ,EAAO,CACNrM,EAAU,EACV2M,EAAS3M,EACTD,EAAa0M,GAAYH,EACzB9O,OAAA,EACA6O,EAASI,GACTlJ,OAAA,EACAlG,SACAmD,EAAe6L,QAGhB,IAAII,GAAYH,IACfG,GAAYH,EAAS9O,EAAc6O,GAEpCI,GAAYH,EAAWD,EACvBrM,EAAOxC,EAAQ6O,EA9JA,GAkKXI,GAAYD,GACfxM,EAAO2M,EAAWN,GAEZA,EACG,IAAmB,IAAnBA,EAAKrM,EAiCf,OA/BAqM,EAAKrM,EAAW,OAehB,IAAIqM,EAAK7O,IACR6O,EAAK7O,EAAYuC,EAAcsM,EAAKtM,WAEhCsM,EAAKtM,IACRsM,EAAKtM,EAAYvC,EAAc6O,EAAK7O,GAGrC6O,EAAKtM,EAAc0M,GAAYH,EAC/BD,EAAK7O,OAAA,EAELiP,GAAYH,EAAU9O,EAAc6O,EACpCI,GAAYH,EAAWD,GAKjBA,CAxEP,CA2EF,CAgEA,SAASO,GAAqB5M,GAC7BlE,KAAK4Q,EAAS1M,EACdlE,KAAKkE,EAAW,EAChBlE,KAAK0B,OAAA,EACL1B,KAAKuQ,OAAA,CAGNO,CA+GA,SAASC,GAAU7M,GAClB,OAAO,IAAI4M,GAAO5M,EAGnB,CAAA,SAAS1D,GAAiB0D,GAIzB,IACC,IAAIqM,EAAOrM,EAAOsM,OACT,IAATD,EACAA,EAAOA,EAAK7O,EAKZ,GACC6O,EAAKM,EAAQ3M,IAAaqM,EAAKrM,IAC9BqM,EAAKM,EAAQJ,KACdF,EAAKM,EAAQ3M,IAAaqM,EAAKrM,EAE/B,OAAO,EAKT,QAGD,CAAA,SAASzE,GAAeyE,GAavB,IACC,IAAIqM,EAAOrM,EAAOsM,OACT,IAATD,EACAA,EAAOA,EAAK7O,EACX,CACD,IAAMgD,EAAe6L,EAAKM,EAAQnP,EAOlC,QANqB,IAAjBgD,IACH6L,EAAK7L,EAAgBA,GAEtB6L,EAAKM,EAAQnP,EAAQ6O,EACrBA,EAAKrM,GAAY,OAEQ,IAArBqM,EAAK7O,EAA2B,CACnCwC,EAAOsM,EAAWD,EAClB,KACA,CACD,CACF,CAEA,SAAS9L,GAAeP,GASvB,IARA,IAAIqM,EAAOrM,EAAOsM,EACd9L,OAAA,OAOY,IAAT6L,GAAoB,CAC1B,IAAMI,EAAOJ,EAAKtM,GAUK,IAAnBsM,EAAKrM,GACRqM,EAAKM,EAAQG,EAAaT,QAEb,IAATI,IACHA,EAAKjP,EAAc6O,EAAK7O,QAEzB,IAAI6O,EAAK7O,IACR6O,EAAK7O,EAAYuC,EAAc0M,IAahCjM,EAAO6L,EAGRA,EAAKM,EAAQnP,EAAQ6O,EAAK7L,WACtB6L,EAAK7L,IACR6L,EAAK7L,OAAA,GAGN6L,EAAOI,EAGRzM,EAAOsM,EAAW9L,CACnB,CAcA,SAASuM,GAAyB/M,GACjC4M,GAAO9P,KAAKhB,UAAA,GAEZA,KAAKuB,EAAM2C,EACXlE,KAAKwQ,OAAA,EACLxQ,KAAKkR,EAAiBN,GAAgB,EACtC5Q,KAAK0Q,EAxgBW,CAygBjB,CAkJA,SAASS,GAAcjN,GACtB,IAAMQ,EAAUR,EAAO4M,EAGvB,GAFA5M,EAAO4M,OAAA,EAEgB,mBAAZpM,EAAwB,CAjoBnC8L,KAqoBC,IAAM9O,EAAciP,GACpBA,QAAc,EACd,IACCjM,GACC,CAAA,MAAO6L,GAIR,MAHArM,EAAOwM,IAAU,EACjBxM,EAAOwM,GAxqBO,EAyqBdQ,GAAchN,GACRqM,CAENI,CAAAA,QAAAA,GAAcjP,EACd6O,IAED,CAAA,CACF,CAEA,SAASW,GAAchN,GACtB,IACC,IAAIqM,EAAOrM,EAAOsM,OACT,IAATD,EACAA,EAAOA,EAAK7O,EAEZ6O,EAAKM,EAAQG,EAAaT,GAE3BrM,EAAO3C,OAAM,EACb2C,EAAOsM,OAAW,EAElBW,GAAcjN,EACf,CAEA,SAASD,GAAwBC,GAChC,GAAIyM,KAAgB3Q,KACnB,MAAU,IAAAgG,MAAM,uBAEjBvB,GAAezE,MACf2Q,GAAczM,EAEdlE,KAAK0Q,IAAU,EAvsBC,EAwsBZ1Q,KAAK0Q,GACRQ,GAAclR,MAEfuQ,IAkBD,CAAA,SAASpQ,GAAqB+D,GAC7BlE,KAAKuB,EAAM2C,EACXlE,KAAK8Q,OAAA,EACL9Q,KAAKwQ,OAAA,EACLxQ,KAAK2Q,OAAqB,EAC1B3Q,KAAK0Q,EAhuBW,EAiuBjB,CA6DA,SAASU,GAAOlN,GACf,IAAMqM,EAAS,IAAIpQ,GAAO+D,GAC1B,IACCqM,EAAO/P,GACN,CAAA,MAAO0D,GAER,MADAqM,EAAOQ,IACD7M,EAIP,OAAOqM,EAAOQ,EAASjB,KAAKS,EAC7B,CAxhBAO,GAAOhQ,UAAUuQ,MAAQnN,GAEzB4M,GAAOhQ,UAAU2P,EAAW,WAC3B,OACD,CAAA,EAEAK,GAAOhQ,UAAU+P,EAAa,SAAU3M,GACnClE,KAAKuQ,IAAarM,QAA6B,IAArBA,EAAKuD,IAClCvD,EAAK3C,EAAcvB,KAAKuQ,OACF,IAAlBvQ,KAAKuQ,IACRvQ,KAAKuQ,EAAS9I,EAAcvD,GAE7BlE,KAAKuQ,EAAWrM,EAElB,EAEA4M,GAAOhQ,UAAUkQ,EAAe,SAAU9M,GAEzC,QAAA,IAAIlE,KAAKuQ,EAAwB,CAChC,IAAMA,EAAOrM,EAAKuD,EACZ/C,EAAOR,EAAK3C,OACL,IAATgP,IACHA,EAAKhP,EAAcmD,EACnBR,EAAKuD,OAAc,QAEpB,IAAI/C,IACHA,EAAK+C,EAAc8I,EACnBrM,EAAK3C,OAAA,GAEF2C,IAASlE,KAAKuQ,IACjBvQ,KAAKuQ,EAAW7L,EAGnB,CAAA,EAEAoM,GAAOhQ,UAAUwQ,UAAY,SAAUpN,GAAE,IAAAqM,EACxCvQ,KAAO,OAAAoR,IAAO,WACb,IAAM1M,EAAQ6L,EAAKtO,MAEbP,EAAciP,GACpBA,QAAA,EACA,IACCzM,EAAGQ,EAGH,CAFA,QACAiM,GAAcjP,EAEhB,GACD,EAEAoP,GAAOhQ,UAAUoC,QAAU,WAC1B,OAAOlD,KAAKiC,KACb,EAEA6O,GAAOhQ,UAAUM,SAAW,WAC3B,YAAYa,MAAQ,EACrB,EAEA6O,GAAOhQ,UAAUyQ,OAAS,WACzB,OAAWvR,KAACiC,KAGb6O,EAAAA,GAAOhQ,UAAU0Q,KAAO,WACvB,IAAMtN,EAAcyM,GACpBA,QAAA,EACA,IACC,OAAO3Q,KAAKiC,KACZ,CAAA,QACA0O,GAAczM,CACd,CACF,EAEArD,OAAO4Q,eAAeX,GAAOhQ,UAAW,QAAS,CAChD6B,IAAG,WACF,IAAMuB,EAAOuD,GAAczH,MAI3B,YAHA,IAAIkE,IACHA,EAAKA,EAAWlE,KAAKkE,QAEV0M,CACb,EACA/N,IAAA,SAAkBqB,GACjB,GAAIA,IAAUlE,KAAK4Q,EAAQ,CAC1B,GAAIF,GAAiB,IACpB,MAAM,IAAI1K,MAAM,kBAGjBhG,KAAK4Q,EAAS1M,EACdlE,KAAKkE,IACL0M,KA7UFJ,KAgVE,IACC,IACC,IAAI9L,EAAO1E,KAAKuQ,OACP,IAAT7L,EACAA,EAAOA,EAAKnD,EAEZmD,EAAK6L,EAAQmB,GAEd,CAAA,QACAnB,IACA,CACD,CACF,KAiJDU,GAASnQ,UAAY,IAAIgQ,IAENL,EAAW,WAG7B,GAFAzQ,KAAK0Q,IAAU,EAhhBA,EAkhBX1Q,KAAK0Q,EACR,OAAA,EAMD,GAphBgB,KAohBI,GAAf1Q,KAAK0Q,GACT,OAAO,EAIR,GAFA1Q,KAAK0Q,IAAU,EAEX1Q,KAAKkR,IAAmBN,GAC3B,OAAO,EAOR,GALA5Q,KAAKkR,EAAiBN,GAItB5Q,KAAK0Q,GAriBU,EAsiBX1Q,KAAKkE,EAAW,IAAM1D,GAAiBR,MAE1C,OADAA,KAAK0Q,IAAU,GACf,EAGD,IAAMxM,EAAcyM,GACpB,IACClR,GAAeO,MACf2Q,GAAc3Q,KACd,IAAMuQ,EAAQvQ,KAAKuB,KA3iBH,GA6iBfvB,KAAK0Q,GACL1Q,KAAK4Q,IAAWL,GACE,IAAlBvQ,KAAKkE,KAELlE,KAAK4Q,EAASL,EACdvQ,KAAK0Q,IAAU,GACf1Q,KAAKkE,IAMN,CAJC,MAAOA,GACRlE,KAAK4Q,EAAS1M,EACdlE,KAAK0Q,GAvjBW,GAwjBhB1Q,KAAKkE,GAENyM,CAGA,OAHAA,GAAczM,EACdO,GAAezE,MACfA,KAAK0Q,IAAU,GACf,CAGDO,EAAAA,GAASnQ,UAAU+P,EAAa,SAAU3M,GACzC,QAAA,IAAIlE,KAAKuQ,EAAwB,CAChCvQ,KAAK0Q,GAAU,GAIf,IACC,IAAIH,EAAOvQ,KAAKwQ,WAChBD,EACAA,EAAOA,EAAK7O,EAEZ6O,EAAKM,EAAQA,EAAWN,EAEzB,CACDO,GAAOhQ,UAAU+P,EAAW7P,KAAKhB,KAAMkE,EACxC,EAEA+M,GAASnQ,UAAUkQ,EAAe,SAAU9M,GAE3C,QAAsB,IAAlBlE,KAAKuQ,IACRO,GAAOhQ,UAAUkQ,EAAahQ,KAAKhB,KAAMkE,QAInB,IAAlBlE,KAAKuQ,GAAwB,CAChCvQ,KAAK0Q,IAAU,GAEf,IACC,IAAIH,EAAOvQ,KAAKwQ,OAAA,IAChBD,EACAA,EAAOA,EAAK7O,EAEZ6O,EAAKM,EAAQG,EAAaT,EAG5B,CACF,EAEAU,GAASnQ,UAAU4Q,EAAU,WAC5B,KA1mBgB,EA0mBV1R,KAAK0Q,GAAoB,CAC9B1Q,KAAK0Q,GAAU,EAEf,IACC,IAAIxM,EAAOlE,KAAKuQ,WAChBrM,EACAA,EAAOA,EAAK3C,EAEZ2C,EAAKqM,EAAQmB,GAEd,CACF,EAEA7Q,OAAO4Q,eAAeR,GAASnQ,UAAW,QAAS,CAClD6B,IAAG,WACF,GA1nBc,EA0nBV3C,KAAK0Q,EACR,MAAM,IAAI1K,MAAM,kBAEjB,IAAM9B,EAAOuD,GAAczH,MAK3B,GAJAA,KAAKyQ,SACQ,IAATvM,IACHA,EAAKA,EAAWlE,KAAKkE,GA5nBN,GA8nBZlE,KAAK0Q,EACR,MAAWE,KAAAA,EAEZ,OAAYA,KAAAA,KAmGdzQ,GAAOW,UAAUN,EAAY,WAC5B,IAAM0D,EAASlE,KAAK6Q,IACpB,IACC,GAxuBe,EAwuBX7Q,KAAK0Q,EAAmB,OAC5B,QAAiB,IAAb1Q,KAAKuB,EAAmB,OAE5B,IAAMgP,EAAUvQ,KAAKuB,IACE,mBAAZgP,IACVvQ,KAAK8Q,EAAWP,EAIjB,CAFA,QACArM,GAEF,CAAA,EAEA/D,GAAOW,UAAU+P,EAAS,WACzB,GAxvBe,EAwvBX7Q,KAAK0Q,EACR,MAAU,IAAA1K,MAAM,kBAEjBhG,KAAK0Q,GA3vBU,EA4vBf1Q,KAAK0Q,IAAU,EACfS,GAAcnR,MACdP,GAAeO,MA9tBfwQ,KAiuBA,IAAMtM,EAAcyM,GAEpB,OADAA,GAAc3Q,KACPiE,GAAU6L,KAAK9P,KAAMkE,EAG7B/D,EAAAA,GAAOW,UAAU4Q,EAAU,WArwBV,EAswBV1R,KAAK0Q,IACV1Q,KAAK0Q,GAvwBU,EAwwBf1Q,KAAK2Q,EAAqBF,GAC1BA,GAAgBzQ,KAElB,EAEAG,GAAOW,UAAUiQ,EAAW,WAC3B/Q,KAAK0Q,GA5wBW,EAHD,EAixBT1Q,KAAK0Q,GACVQ,GAAclR,KAiBhB,ECnyBA,MAAM2R,GAGJnM,WAAAA,GACExF,KAAK4R,MAAQ,EACf,CAEAC,OAAAA,CAAQC,GACN9R,KAAK4R,MAAM7N,KAAK+N,EAClB,CAEAC,OAAAA,GACE,OAA0B,IAAtB/R,KAAK4R,MAAM9R,OACN,KAEEE,KAAC4R,MAAMzF,OACpB,CAEA6F,OAAAA,GACE,OAA6B,SAAjBJ,MAAM9R,MACpB,CAEAmS,IAAAA,GACE,OAAWjS,KAAC4R,MAAM9R,MACpB,CAEAoS,KAAAA,GACElS,KAAK4R,MAAQ,EACf,EC7BF,MAAMO,GAA0C,CAC9CC,IAAK,EACLC,KAAM,EACNC,MAAO,EACPC,KAAM,EACNC,MAAO,EACPC,KAAM,GAGFC,GAAoB,QAmG1B,MAAMC,GAAgB,IA3FtB,MAKEnN,WAAAA,CAAYoN,EAAwBF,GAAmBG,EAAQ,GAAIC,EAAcC,SAC/E/S,KAAK4S,YAAcT,GAAcS,GACjC5S,KAAK6S,MAAQA,EACb7S,KAAK8S,YAAcA,CACrB,CAEAE,GAAAA,IAAOjI,GACL/K,KAAKiT,UAAU,MAAOlI,EACxB,CAEAmI,IAAAA,IAAQnI,GACN/K,KAAKiT,UAAU,OAAQlI,EACzB,CAEAoI,KAAAA,IAASpI,GACP/K,KAAKiT,UAAU,QAASlI,EAC1B,CAEAiC,IAAAA,IAAQjC,GACN/K,KAAKiT,UAAU,OAAQlI,EACzB,CAEAqF,KAAAA,IAASrF,GACP/K,KAAKiT,UAAU,QAASlI,EAC1B,CAEAkI,SAAAA,CAAUG,EAAqBrI,GACzB/K,KAAK4S,aAAeT,GAAciB,IACpCpT,KAAK8S,YACHM,EAAUC,oBACLrT,KAAKsT,cAAcvI,GAE9B,CAEAwI,QAAAA,CAASC,GACPxT,KAAK6S,MAAQW,GAAYxT,KAAK6S,KAChC,CAIAY,cAAAA,CAAeC,GACb1T,KAAK4S,YAAcT,GAAcuB,GAC7B/N,EAAY3F,KAAK4S,eACnB5S,KAAK4S,YAAcT,GAAcO,IAErC,CAKAY,aAAAA,CAAcvI,GACZ,GAAI/H,MAAMsD,QAAQyE,IAASA,EAAKjL,OAAS,EAAG,CAE1C,IAAI6T,EAAM,YAGN3T,KAAK6S,QACPc,EAAM,GAAGA,OAAS3T,KAAK6S,SAOzBc,EAAM,GAAGA,QAHWlO,EAASsF,EAAK,IAAMA,EAAK,GAAG6I,OAAS,KAKzD,MAAMC,EAAgB,CACpBF,EA9EqB,sDACP,wBAyFhB,OANKlO,EAASsF,EAAK,KACjB8I,EAAc9P,KAAKgH,EAAK,IAI1B8I,EAAc9P,QAAQgH,EAAK1J,MAAM,IAC1BwS,CACT,CAEA,OAAO9I,CACT,GClGW+I,GAA0B,CACrC,eACA,gBACA,gBACA,iBACA,QAGWC,GAAuB,gBCH9BC,GAAiC,yDAmEjCC,GAAoBA,CAACC,EAAgBzM,EAA8B+E,IACvE,GAAG0H,sCAA2CzM,EAAIA,EAAExG,KAAO,gBAAgBuL,KAoHvE2H,GAA8B,sCCxM9BC,GAAc,kBACdC,GAAkB,UAClBC,GAAc,IAAInR,OACtB,uQCAIoR,GAA0C,SAC1CC,GAAmB,6BACnBC,GAAuB,KACvBC,GAAoB,GAAGF,MAAoBC,MAAwBF,MAAcH,KACjFO,GAAmB,GAAGH,MAAoBC,MAAwBF,MAAcF,KAChFO,GAAwB,8BCNxBC,GAAmC,UACnCC,GAAqC,KACrCC,GAAsC,MAE/BC,GAA6D,CACxEC,SAAU,yBACVC,MAAO,sBACPC,OAAQ,wBAGGC,GAAqE,CAChFP,CAACA,IAAmC,WAGzBQ,GAAuE,CAClFP,CAACA,IAAqC,oBACtCQ,OAAQ,2BAGGC,GAAsE,CACjFR,CAACA,IAAsC,WACvCS,OAAQ,eCmBJC,GAAqCC,GAAOtR,EA/BV,CACtCsP,SAAU,QACViC,UAAWf,GACXgB,iBAAiB,EACjBC,SAAU,CACRC,WAAW,EACXpI,QAASpD,IAEXyL,eAAgB,MAChBC,oBAAoB,EACpBC,aAAc,CAAEC,KAAK,GACrBC,WAAW,EACXC,mBAAoB,CAAA,EACpBC,yBAA0B,CAAA,EAC1BC,aAAc,CAAA,EACdC,yBAAyB,EACzBC,eAAgB,OAChBC,QAAS,GACTC,qCAAqC,EACrCC,iCAAiC,EACjCC,6BlB3BkD,IkB4BlDC,QAAS,CACPC,WAAY,CACVC,QAASjC,IAEXkC,SAAS,GAEXC,uBAAwB,CAAE,EAC1BC,sBAAsB,KCpClBC,GAA4B,CAChCpO,OAAQ,aACRqO,WAAY,WACZC,YAAa,kBACbjO,QAAS,cACTkO,YAAa,iBACbC,gBAAiB,wBACjBC,uBAAwB,gCACxBC,YAAa,aACbC,UAAW,iBAGPC,GAA8B,CAClC5O,OAAQ,GACRqO,WAAY,CAAA,EACZC,YAAa,GACbjO,QAAS,GACTkO,YAAa,GACbC,gBAAiB,GACjBC,uBAAwB,GACxBC,YAAa,CAAA,EACbC,UAAW,MClBPE,GAA2C,CAC/C9B,WAAW,EACXpI,QAASpD,IAGLuN,GAA6B,CACjC9O,OAAQ2M,GAAOiC,GAA4B5O,QAC3CqO,WAAY1B,GAAOiC,GAA4BP,YAC/CC,YAAa3B,GAAOiC,GAA4BN,aAChDjO,QAASsM,GAAOiC,GAA4BvO,SAC5CkO,YAAa5B,GAAOiC,GAA4BL,aAChDC,gBAAiB7B,GAAOiC,GAA4BJ,iBACpDC,uBAAwB9B,GAAOiC,GAA4BH,wBAC3DC,YAAa/B,GAAOiC,GAA4BF,aAChDC,UAAWhC,GAAOiC,GAA4BD,YCjB1CI,GAAuC,CAC3CC,SAAUrC,IAAO,GACjBmB,QAAS,CACPmB,wBAAyBtC,IAAO,GAChCuC,yBAA0BvC,IAAO,GACjCwC,0BAA2BxC,IAAO,IAEpCyC,kBAAmBzC,IAAO,GAC1B0C,YAAa1C,IAAO,GACpB2C,gBAAiB3C,IAAO,GACxB4C,kBAAmB5C,IAAO,GAC1B6C,OAAQ7C,IAAO,GACf8C,YAAa9C,IAAO,ICZhB+C,GAAiC,CACrCC,wBAAyBhD,IAAO,GAChCiD,0BAA2BjD,IAAO,GAClCkD,iCAAkClD,QAAOvU,GACzC0X,6BAA8BnD,IAAO,ICJjCoD,GAAuCpD,QAAOvU,GCC9C4X,GAAiC,CACrCC,mBAAoBtD,QAAOvU,GAC3B8X,oBAAqBvD,GAAOhB,IAC5BwE,eAAgBxD,GAAOf,IACvBwE,gBAAiBzD,QAAOvU,GACxBiY,OAAQ1D,QAAOvU,GACfkY,YAAa3D,IAAO,GACpBhC,SAAUgC,GAAO,SACjB4D,OAAQ5D,IAAO,GACf6D,eAAgB7D,GAAO,IACvB8D,SAAU9D,QAAOvU,GACjBsY,aAAc/D,QAAOvU,ICZjBuY,GAA+B,CACnCC,QAASjE,IAAO,GAChB2D,YAAa3D,IAAO,GACpB3K,KAAM2K,GAAO,CAAA,GACbkE,+BAAgClE,QAAOvU,GACvC0Y,WAAYnE,GAAO,CAAEiE,SAAS,IAC9BG,YAAapE,GAAO,CAAA,GACpBqE,mBAAoBrE,GAAO,OAC3BsE,SAAUtE,QAAOvU,GACjB8Y,SAAUvE,QAAOvU,ICTb+Y,GAA6B,CACjCC,QAASzE,GAAO,GAChB0E,QAAS1E,GAAO,GAChB2E,KAAM3E,GAAO,GACb4E,OAAQ5E,GAAO,GACf6E,UAAW7E,GAAO,ICJd8E,GAA6B,CACjCC,IAAK/E,GAAO,CACVnN,KAAMwB,EACN2Q,U7BLkB,4B6BMlB3D,QAAS/M,GACT2Q,Y7BNgB,Q6BQlB3R,OAAQ0M,GAAO,MACfkF,QAASlF,GAAO,CACdnN,KAAMwB,EACNgN,QAAS/M,GACT6Q,eAAiB7S,WAA6B8S,uBAEhDC,UAAWrF,GAAO,IAClBsF,OAAQtF,GAAO,MACfuF,QAASvF,GAAO,MAChBwF,GAAIxF,GAAO,CACTnN,KAAM,GACNwO,QAAS,KAEXoE,OAAQzF,GAAO,MACf0F,OAAQ1F,GAAO,CACb2F,QAAS,EACTC,MAAO,EACPC,OAAQ,EACRC,WAAY,EACZC,YAAa,IAEf,QAAS/F,QAAOvU,GAChBua,SAAUhG,QAAOvU,IC9Bbwa,GAAmD,CACvDC,uBAAwBlG,GAAO,IAC/BmG,mBAAoBnG,GAAO,IAC3BoG,qBAAsBpG,GAAO,CAAE,GAC/BqG,mBAAoBrG,GAAO,IAC3BE,gBAAiBF,IAAO,GACxBsG,wBAAyBtG,GAAO,IAChCuG,wBAAyBvG,IAAO,GAChCwG,mBAAoBxG,GAAO,CAAE,ICRzByG,GAAqC,CACzCC,mBAAoB1G,GAAO,IAC3B2G,oBAAqB3G,GAAO,KCFxB4G,GAA6B,CACjCC,MAAO7G,IAAO,GACd8G,cAAe9G,GAAO,IACtB+G,cAAe/G,GAAO,IACtBgH,wBAAyBhH,GAAO,IAChCiH,cAAejH,GAAO,IACtBkH,mBAAoBlH,GAAO,ICNvBmH,GAA6B,CACjCC,qBAAsBpH,QAAOvU,GAC7B6V,QAAStB,IAAO,GAChBzU,KAAMyU,QAAOvU,GACb4b,OAAQrH,QAAOvU,GACf6b,QAAStH,GAAO,CAAE,GAClBuH,uBAAwBvH,IAAO,ICN3BwH,GAA6C,CACjDC,2BAA4BzH,IAAO,GACnC0H,eAAgB1H,QAAOvU,ICDnBkc,GAA6C,CACjDC,sBAAuB5H,QAA+BvU,GACtDoc,gBAAiB7H,IAAO,IC8BpB8H,GAA0B,IAC3BpZ,EAnBwC,CAC3CqZ,aAAc3F,GACd4F,SAAUhE,GACVxM,QAASsN,GACTmD,YAAaxB,GACbyB,UAAW7E,GACX8E,YAAapI,GACbqI,QAAS5D,GACT6D,mBAAoBpC,GACpBlF,QAAS6F,GACT0B,UAAWvF,GACXwF,QAASpG,GACTzU,OAAQ0V,GACRjC,QAASgG,GACTqB,cAAehB,GACfiB,gBAAiBd,MCoKnB,MAAMe,GAAsB,IA3K5B,MACE3H,QAA6B,GAC7B4H,OAA0C,CAAA,EAC1CC,MAA2C,CAAA,EAC3CtO,OAA6B,CAAEuO,QAAQ,GAGvC/Y,WAAAA,CAAYiD,EAA8B,CAAE,EAAEmE,GAC5C5M,KAAKgQ,OAAS,CACZuO,QAAQ,KACL9V,GAGLzI,KAAK4M,OAASA,CAChB,CAEA4R,QAAAA,CAASC,EAAyBjB,GAChC,IAAKiB,EAAOlW,KAAM,CAChB,MAAMmW,ErBJV,GqBImD/U,IrBJtC0C,4BqBKT,GAAIrM,KAAKgQ,OAAOuO,OACd,MAAM,IAAIvY,MAAM0Y,GAEhB1e,KAAK4M,QAAQwD,MAAMsO,EAAcD,EAErC,CAEA,GAAIze,KAAKqe,OAAOI,EAAOlW,MAAO,CAC5B,MAAMmW,ErBXwBC,EAACzR,EAAiB0R,IACpD,GAAG1R,IAAUb,aAAgCuS,qBqBUpBD,CAA4BhV,EAAe8U,EAAOlW,MACvE,GAAIvI,KAAKgQ,OAAOuO,OACd,MAAM,IAAIvY,MAAM0Y,GAEhB1e,KAAK4M,QAAQwD,MAAMsO,EAEvB,CAEA1e,KAAKse,MAAQ,CAAA,EACbte,KAAKyW,QAAUzW,KAAKyW,QAAQpV,QAC5B,IAAIwd,EAAM7e,KAAKyW,QAAQ3W,OAEvBE,KAAKyW,QAAQ7P,SAAQ,CAACkY,EAA6BjY,KAC7CiY,EAAWC,MAAMjS,SAAS2R,EAAOlW,QACnCsW,EAAMG,KAAKC,IAAIJ,EAAKhY,GACtB,IAGF7G,KAAKyW,QAAQyI,OAAOL,EAAK,EAAGJ,GAE5Bze,KAAKqe,OAAOI,EAAOlW,MAAQkW,EAEvBnZ,EAAWmZ,EAAOU,aACpBV,EAAOU,WAAW3B,EAEtB,CAEA4B,UAAAA,CAAW7W,GACT,MAAMkW,EAASze,KAAKqe,OAAO9V,GAE3B,IAAKkW,EAAQ,CACX,MAAMC,ErBtCV,GqBsCgD/U,IrBtCnC0C,aqBsCkD9D,gBAC3D,GAAIvI,KAAKgQ,OAAOuO,OACd,MAAU,IAAAvY,MAAM0Y,GAEhB1e,KAAK4M,QAAQwD,MAAMsO,EAEvB,CAEA,MAAM7X,EAAQ7G,KAAKyW,QAAQ4I,QAAQZ,GAEnC,IAAe,IAAX5X,EAAc,CAChB,MAAM6X,ErB/CoBY,EAACpS,EAAiB0R,IAChD,GAAG1R,IAAUb,aAAgCuS,4IqB8CpBU,CAAwB3V,EAAepB,GAC5D,GAAIvI,KAAKgQ,OAAOuO,OACd,MAAM,IAAIvY,MAAM0Y,GAEhB1e,KAAK4M,QAAQwD,MAAMsO,EAEvB,CAEA1e,KAAKse,MAAQ,CAAA,SACFte,KAACqe,OAAO9V,GACnBvI,KAAKyW,QAAUzW,KAAKyW,QAAQpV,QAC5BrB,KAAKyW,QAAQyI,OAAOrY,EAAO,EAC7B,CAEA0Y,SAAAA,CAAUhX,GACR,OAAOvI,KAAKqe,OAAO9V,EACrB,CAEAiX,UAAAA,CAAWC,GACT,MAAMC,EAAgBD,GAAY,IAclC,OAZKzf,KAAKse,MAAMoB,KACd1f,KAAKse,MAAMoB,GAAiB1f,KAAKyW,QAAQkJ,QAAOlB,IAC9C,GAAIA,EAAOM,MAAMa,MAAKC,IAAe7f,KAAKqe,OAAOwB,KAAc,CAE7D,MAAMC,EAAerB,EAAOM,KAAKY,QAAOE,IAAe7f,KAAKqe,OAAOwB,KAEnE,OADA7f,KAAK4M,QAAQwD,MrBtEG2P,EAAC7S,EAAiB0R,EAAoBkB,IAC9D,GAAG5S,IAAUb,aAAgCuS,4DAAqEkB,mBqBqEvFC,CAAkBpW,EAAe8U,EAAOlW,KAAMuX,KAC1D,CACT,CACA,MAAyB,MAAlBJ,GzCpHQM,EAACpf,EAA0BuE,IAChDI,QAAQU,EAAerF,EAAKuE,IyCmHgB6a,CAAevB,EAAQiB,EAAc,KAIxE1f,KAAKse,MAAMoB,EACpB,CAIAO,iBAAAA,CAAkBvX,GAChBA,EAAS1I,KAAKyW,SACdzW,KAAKse,MAAQ,CAAE,CACjB,CAEA4B,MAAAA,CAAgBT,EAAmBU,GAAgB,KAASC,GAC1D,IAAIC,EAAqBZ,EAEzB,IAAKY,EACH,MAAM,IAAIra,MrB1HuB,wEqB6HnC,MAAMsa,EAASD,EAAmBrV,WAAW,KACvCuT,EAASve,KAAKgQ,OAAOuO,QAAU8B,EAAmBE,SAAS,KAKjE,GAFAF,EAAqBA,EAAmBG,QAAQ,WAAY,KAEvDH,EACH,MAAM,IAAIra,MrBnIuB,wEqBsInC,MAAMya,EAA0BJ,EAAmBja,MAAM,KACzDqa,EAAwB1T,MAExB,MAAM2T,EAAmBD,EAAwBzc,KAAK,KAKtD,OAJwBmc,EACpBngB,KAAKwf,WAAWa,GAChB,CAACrgB,KAAKwf,WAAWa,GAAoB,KAElBle,KAAIsc,IACzB,MAAMkC,EAAS1a,EAAewY,EAAQ4B,GAEtC,IAAK/a,EAAWqb,IAAWL,EACzB,OAAOK,EAGT,IACE,OAAOA,EAAO5gB,MAAMkG,EAAewY,EAAQiC,GAAmBN,EAChE,CAAE,MAAOhT,GAEP,GAAImR,EACF,MAAMnR,EAENpN,KAAK4M,QAAQwD,MrB3HSwQ,EAC9B1T,EACAuS,EACAb,IAEA,GAAG1R,IAAUb,2BAA8CoT,iCAAwCb,MqBuHzFgC,CAAwBjX,EAAe0W,EAAoB5B,EAAOlW,MAClE6E,EAGN,CAEA,OAAW,IAAA,GAEf,CAEAyT,YAAAA,CAAsBpB,KAAsBW,GAC1C,OAAOpgB,KAAKkgB,OAAOT,GAAU,KAAUW,GAAM,EAC/C,CAEAU,cAAAA,CAAwBrB,KAAsBW,GAC5C,OAAOpgB,KAAKkgB,OAAOT,GAAU,KAASW,EACxC,GAG2C,CAAE7B,QAAQ,GAAQ5L,ICrMzDoO,GAAgC,qBAChCC,GAAgC,CAACD,ICwMvC,MAAME,GAAsB,IA/K5B,MAOEzb,WAAAA,CAAYoH,EAAkBsU,GAC5BlhB,KAAK4M,OAASA,EACd5M,KAAKkhB,aAAeA,EACpBlhB,KAAKmhB,YAAc,IAAIxP,GACvB3R,KAAKohB,cACP,CAEAA,YAAAA,GACE,IAA2D,IAAvD5D,GAAMQ,UAAUnF,6BAA6B5W,MAC/C,KAAOjC,KAAKmhB,YAAYlP,OAAS,GACRjS,KAAKmhB,YAAYpP,SAO9C,CAEAsP,oBAAAA,GACM,qBAAuBrZ,YACxBA,WAA6BsZ,iBAAiB,SAAUzY,IACvD7I,KAAK6P,QAAQhH,OAAO1H,OAAWA,OAAWA,EAAW,qBAAqB,IAG3E6G,WAA6BsZ,iBAC5B,sBACCzY,IACC7I,KAAK6P,QAAQhH,OAAO1H,OAAWA,OAAWA,EAAW,4BAA4B,KAIrFnB,KAAK4M,QAAQuG,MAAM,2CAEvB,CAEAoO,IAAAA,CAAKC,GACH,GAAKxhB,KAAKkhB,aAIV,IACE,MAAMzB,EAAW,sBACXgC,EAAsBzhB,KAAKkhB,aAAaL,aAC5CpB,EACAjC,GACAxd,KAAKkhB,aACLM,EACAxhB,KAAK4M,QAEH6U,aAA+B5T,SACjC4T,EACGvR,MAAMwR,IACL1hB,KAAK2hB,mBAAqBD,CAAM,IAEjCvR,OAAM/C,IACLpN,KAAK4M,QAAQwD,MvBvDvB,GuBuDiE1G,IvBvDpD2C,qDuBuDoEe,EAAI,GAGnF,CAAE,MAAOA,GACPpN,KAAK6P,QAAQzC,EAAK1D,EACpB,CACF,CAEAmG,OAAAA,CACEO,EACAlD,EAAU,GACV0U,EAAgB,GAChBC,GAAoB,EACpBC,EAAY,WAGZ,IAAIpD,EChGctO,KACpB,IAAIsO,EAEJ,IACE,GAAIjZ,EAAS2K,GACXsO,EAAetO,OACV,GAAIA,aAAiBpK,MAC1B0Y,EAAetO,EAAM5C,gBACZ4C,aAAiB2R,WAC1BrD,EAAetO,EAAM5C,aAGd4C,GAAAA,aAAiB4R,MAAO,CAC/B,MAAMC,EAAc7R,EAAM8R,OAE1B,GAAID,GAAyC,WAA1BA,EAAYE,UAC7B,MAAO,GAGT,GACEF,GAAaG,UC7BD,cD8BXH,EAAYG,QAAQC,QACoB,SAAvCJ,EAAYG,QAAQE,gBAEtB,MAAO,GAET5D,EAAe,kDAAkDuD,GAAa7S,eAAe6S,GAAa1V,KAC5G,MACEmS,EAAgBtO,EAAc5C,QACzB4C,EAAc5C,QACfL,GAAyBiD,EAEjC,CAAE,MAAO3I,GACPiX,EAAe,kBAAmBjX,EAAY+F,SAChD,CAEA,OAAOkR,CAAY,ED4DE6D,CAAanS,GAGhC,IAAKsO,EACH,OAQFA,EACE,GAAGxR,IAAUb,KAAwBuV,KAAiBlD,I1ChHA8B,QAAQ,SAAU,K0CmH1E,IAAIgC,EAAkB,IAAIxc,MAAM0Y,GAMhC,GALI3Y,EAAcqK,KAChBoS,EAAkB3hB,OAAOiC,OAAOsN,EAAO,CACrC5C,QAAS,CAAEvL,MAAOyc,MAGJ,YAAdoD,EAAyB,CAI3B,GAFA9hB,KAAKyiB,YAAYD,IAEbxiB,KAAK4M,OAOP,MAAM4V,EAJN,GAFAxiB,KAAK4M,OAAOwD,MAAMsO,GAEdmD,EACF,MAAMW,CAKZ,CAIEhF,GAAMQ,UAAUtF,wBAAwBzW,OACvCub,GAAMQ,UAAUnF,6BAA6B5W,KAQlD,CAQAygB,eAAAA,CAAgBC,GACd,GAAI3iB,KAAKkhB,aACP,IACElhB,KAAKkhB,aAAaL,aAChB,4BACA7gB,KAAKkhB,aACLlhB,KAAK2hB,mBACLgB,EACA3iB,KAAK4M,OAET,CAAE,MAAOQ,GACPpN,KAAK6P,QAAQzC,EAAK1D,EAAe,4BACnC,CAEJ,CAOA+Y,WAAAA,CAAYrS,GACV,GAAIpQ,KAAKkhB,cClIkB9Q,KACzBA,EAAM5C,UACAwT,GAA8BpB,MAAKnY,GAAK2I,EAAM5C,QAAQV,SAASrF,KDgI9Cmb,CAAsBxS,GAC7C,IACEpQ,KAAKkhB,aAAaL,aAChB,wBACA7gB,KAAKkhB,aACLlhB,KAAK2hB,mBACLvR,EACAoN,GACAxd,KAAK4M,OAET,CAAE,MAAOQ,GAEPpN,KAAK4M,QAAQwD,MvB9JnB,GuB8J8C1G,IvB9JjC2C,gCuB8JiDe,EAC1D,CAEJ,GAG2CuF,GAAeyL,IGjMtDyE,GAAyBC,GAC7Bvd,QACwC,UAAtCud,EAAY9S,OAAO+S,iBACyB,IAA1CD,EAAY9S,OAAOgT,qBACiB,IAApCF,EAAY9S,OAAOiT,cAenBC,GAA2BC,GAC/BA,EAAaxD,OAAOkD,IClBhBO,GAAgC,CACpC,cACA,UACA,uBACA,yBACA,2BACA,iBACA,sBACA,eACA,sBACA,yBACA,yBACA,oBACA,0BACA,kBACA,qzCCjBF,MA0CMC,GACJC,IAEA,MAAMC,EAAgB,CAAA,EAWtB,OATAD,EAAkB1c,SAAQgY,IACxB,GAAIwE,GAAgBtW,SAAS8R,GAAa,CACxC,MAAM4E,EAhDV5E,KAEA,OAAQA,GACN,IAAK,cACH,MAAO,IAAM6E,GAAA,+BAAA,iBAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACf,IAAK,UACH,MAAO,IAAMD,mEACf,IAAK,uBACH,MAAO,IAAMA,GAAA,+BAAA,0BAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACf,IAAK,yBACH,MAAO,IAAMD,kFACf,IAAK,2BACH,MAAO,IAAMA,GAA+D,+BAAA,8BAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KAC9E,IAAK,iBACH,MAAO,IAAMD,GAAqD,+BAAA,oBAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACpE,IAAK,sBACH,MAAO,IAAMD,GAAA,+BAAA,yBAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACf,IAAK,eACH,MAAO,IAAMD,wEACf,IAAK,sBACH,MAAO,IAAMA,GAA0D,+BAAA,yBAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACzE,IAAK,yBACH,MAAO,IAAMD,GAAA,+BAAA,4BAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACf,IAAK,yBACH,MAAO,IAAMD,kFACf,IAAK,oBACH,MAAO,IAAMA,GAAwD,+BAAA,uBAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACvE,IAAK,0BACH,MAAO,IAAMD,GAA8D,+BAAA,6BAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KAC7E,IAAK,kBACH,MAAO,IAAMD,GAAA,+BAAA,qBAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACf,IAAK,WACH,MAAO,IAAMD,GAAA,+BAAA,cAAAvT,MAAAwT,GAAAC,GAAAD,GAAA,KACf,QACE,OACJ,EAa2BE,CAAyBhF,GAC5C4E,IACFD,EAAc3E,GAAc4E,EAEhC,KAGKD,CAAa,ECxCtBM,GAAAP,GAAAD,KAAAC,IAAA,CAAA,EAAAQ,GAAA,CAAA,ECWA,MAAMC,GAKJve,WAAAA,CAAYwe,EAAuBpU,EAA8BhD,GAC/D5M,KAAKgkB,OAASA,EAEdhkB,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKAuhB,IAAAA,GACE/D,GAAMI,UAAUxE,OAAOnX,MAAQ,iBAI7ByI,GAAiB,iBAAkB8S,GAAMI,UAAU1E,eAAejX,OAEpEjC,KAAKikB,mBACLjkB,KAAKkkB,uBACLlkB,KAAKmkB,wBACLnkB,KAAKokB,eACP,CAMAA,aAAAA,GACEC,IAAO,MAE0C,IAA7C7G,GAAM/G,QAAQkG,cAAc1a,MAAMnC,QAClC0d,GAAM/G,QAAQ+F,cAAcva,MAAMnC,OAAS0d,GAAM/G,QAAQgG,cAAcxa,MAAMnC,SAC3E0d,GAAM/G,QAAQmG,mBAAmB3a,QAGnCqiB,IAAM,KACJ9G,GAAM/G,QAAQ8F,MAAMta,OAAQ,EAG5Bub,GAAMI,UAAUxE,OAAOnX,MAAQ,cAAc,GAEjD,GAEJ,CAMAsiB,6BAAAA,GAEE,IAAI7H,EAA0Bc,GAAM/G,QAAQiG,wBAAwBza,MAEpE,IAAKya,EACH,MAAO,GAGT,MAAM8H,EAAwC,CAC5C,CACEC,oBAAqBA,IAAM5e,EAAU2X,GAAMW,gBAAgBb,sBAAsBrb,OACjFyiB,uBAAwB,wCACxBC,iBAAkBnH,GAAMW,gBAAgBb,sBAAsBrb,MAC9D2iB,iBAAkB/jB,OAAOgkB,OAAOtP,IAChCuP,yBAAyB,GAE3B,CACEL,oBAAqBA,IACnB5e,EAAU2X,GAAMQ,UAAUpF,iCAAiC3W,OAC7DyiB,uBAAwB,6BACxBC,iBAAkBnH,GAAMQ,UAAUpF,iCAAiC3W,MACnE8iB,YAAa,CAAC,kBACdH,iBAAkB/jB,OAAOgkB,OAAOzP,KAElC,CACEqP,oBAAqBA,IACnBvB,GAAwB1F,GAAMO,mBAAmBnC,uBAAuB3Z,OAAOnC,OAAS,EAC1F4kB,uBAAwB,uDACxBE,iBAAkB,CAAC,yBAA0B,2BAE/C,CACEH,oBAAqBA,IACnBvB,GAAwB1F,GAAMO,mBAAmBnC,uBAAuB3Z,OAAO2d,MAC7EkD,GAAeA,EAAYkC,sCAE/BN,uBACE,uEACFE,iBAAkB,CAAC,6BAErB,CACEH,oBAAqBA,IAAM5e,EAAU2X,GAAME,SAAS9D,+BAA+B3X,OACnFyiB,uBAAwB,gCACxBC,iBAAkBnH,GAAME,SAAS9D,+BAA+B3X,MAChE2iB,iBAAkB/jB,OAAOgkB,OAAO7P,KAElC,CACEyP,oBAAqBA,IAAM5e,EAAU2X,GAAM3G,QAAQiG,qBAAqB7a,OACxEyiB,uBAAwB,gCACxBC,iBAAkBnH,GAAM3G,QAAQiG,qBAAqB7a,MACrD2iB,iBAAkB/jB,OAAOgkB,OAAOxP,KAElC,CACEoP,oBAAqBA,IAAMjH,GAAM3G,QAAQG,QAAQ/U,MACjDyiB,uBAAwB,+BACxBE,iBAAkB,CAAC,qBA+BvB,OA1BAJ,EAAsB5d,SAAQqe,IACxBA,EAAMR,uBACR/H,EAA0BA,EAAwBiD,OAChDsF,EAAMN,iBACF/F,KAEIA,IAAeqG,EAAMN,kBACrBM,EAAML,iBAAiB9X,SAAS8R,IAEpCA,GAAc/Y,EAAU+Y,IAG9B5e,KAAKklB,kBAAkBD,EAbD,MAa2BvI,IAEjDA,EAA0BA,EAAwBiD,YAC1Bxe,IAAtB8jB,EAAMF,YACFnG,KAEKqG,EAAMF,YAA6BjY,SAAS8R,IAC7CqG,EAAML,iBAAiB9X,SAAS8R,IAEpCA,IAAeqG,EAAML,iBAAiB9X,SAAS8R,GAEvD,IAGK,IAAK/d,OAAOqG,KDvKvB,CAAA,MCuK2EwV,EACzE,CAEQwI,iBAAAA,CACND,EACAC,EACAxI,GAEA,MAAMoI,EAA0BG,EAAMH,yBAA2BI,EACjE,IAAIC,EAEFA,EADEF,EAAMN,iBACa,IAAKM,EAAMF,aAAe,GAAKE,EAAMN,kBAErC,IAAIM,EAAML,kBAGjC,MAAMQ,EAAiBD,EAAmBxF,QACxCf,IAAelC,EAAwB5P,SAAS8R,KAE9CwG,EAAetlB,OAAS,IACtBglB,GACFpI,EAAwB3Y,QAAQqhB,GAGlCplB,KAAK4M,QAAQI,K9BiCyBqY,EAC1CnY,EACAuX,EACAW,EACAN,KAEA,MAAMQ,EAA2C,IAA1BF,EAAetlB,OAChCylB,EAAgBD,EAClB,KAAKF,EAAe,iBACpB,MAAMA,EAAephB,KAAK,yBACxBwhB,EAAc,GAAGtY,IAAUb,KAAwBoY,SAA2Bc,4BACpF,OAAIT,EACK,GAAGU,SAAmBF,EAAiB,aAAe,gDAExD,GAAGE,gEAA0EF,EAAiB,KAAO,0CAA0C,E8B9ChJD,CACE7b,EACAyb,EAAMP,uBACNU,EACAN,IAIR,CAKAb,gBAAAA,GACE,MAAMwB,EAAgBzlB,KAAKukB,gCAErBmB,EAAmB,IAAI7kB,OAAOqG,KAAK4c,OAAsBV,IACzDzG,EAA8B,GAC9BF,EAA0B,GAEhCgJ,EAAc7e,SAAQgY,IAChB8G,EAAiB5Y,SAAS8R,GAC5BjC,EAAc5Y,KAAK6a,GAEnBnC,EAAc1Y,KAAK6a,EACrB,IAGEnC,EAAc3c,OAAS,GACzBE,KAAK6P,QACH,IAAI7J,MACF,wCAAwCyW,EAAczY,KACpD,4BACuBnD,OAAOqG,KDjO1C,CAAA,GCiOyElD,KAC7D,8BACyBwZ,GAAM/G,QAAQiG,wBAAwBza,MAAM+B,KAAK,SAKlFsgB,IAAM,KACJ9G,GAAM/G,QAAQmG,mBAAmB3a,MAAQwjB,EAAc3lB,OACvD0d,GAAM/G,QAAQkG,cAAc1a,MAAQ0a,EACpCa,GAAM/G,QAAQgG,cAAcxa,MAAQwa,CAAa,GAErD,CAKAyH,oBAAAA,GACErjB,OAAOgkB,OAAOf,IAAkBld,SAAQ+e,IAEpCrgB,EAAWqgB,IACXnI,GAAM/G,QAAQkG,cAAc1a,MAAM6K,SAAS6Y,IAAcpd,OAEzDvI,KAAKwe,SAAS,CAACmH,KACjB,GAEJ,CAKAxB,qBAAAA,GACE,MAAMyB,GDjPVtC,ECkPM9F,GAAM/G,QAAQkG,cAAc1a,MDlPlC,IAAA4hB,GAAAP,KAAAA,MCqPIzV,QAAQgY,IACNhlB,OAAOqG,KAAK0e,GAAmBzjB,KAAIwL,gBAC3BiY,EAAkBE,KACrB5V,MAAM6V,GAA4B/lB,KAAKwe,SAAS,CAACuH,EAAmBC,cACpE7V,OAAM/C,IAELoQ,GAAM/G,QAAQgG,cAAcxa,MAAQ,IAC/Bub,GAAM/G,QAAQgG,cAAcxa,MAC/B6jB,GAEF9lB,KAAK6P,QAAQzC,EAAK0Y,EAAgB,GAClC,KAEN3V,OAAM/C,IACNpN,KAAK6P,QAAQzC,EAAI,GAErB,CAKA0T,cAAAA,CAAwBrB,KAAsBW,GAC5C,IACE,YAAY4D,OAAOlD,eAAerB,KAAaW,EACjD,CAAE,MAAO3Y,GAEP,OADAzH,KAAK6P,QAAQpI,EAAGgY,GACT,EACT,CACF,CAKAoB,YAAAA,CAAsBpB,KAAsBW,GAC1C,IACE,YAAY4D,OAAOnD,aAAapB,KAAaW,EAC/C,CAAE,MAAO3Y,GAEP,OADAzH,KAAK6P,QAAQpI,EAAGgY,GACL,IACb,CACF,CAKAjB,QAAAA,CAAS/H,GACPA,EAAQ7P,SAAQ6X,IACd,IACEze,KAAKgkB,OAAOxF,SAASC,EAAQjB,GAC/B,CAAE,MAAO/V,GACP+V,GAAM/G,QAAQgG,cAAcxa,MAAQ,IAAIub,GAAM/G,QAAQgG,cAAcxa,MAAOwc,EAAOlW,MAClFvI,KAAK6P,QAAQpI,EACf,IAEJ,CAGAwe,sBAAAA,GACEplB,OAAOgkB,OAAOf,IAAkBld,SAAQ+e,IACtC,IACE3lB,KAAKgkB,OAAO5E,WAAWuG,IAAcpd,KACvC,CAAE,MAAOd,GACPzH,KAAK6P,QAAQpI,EACf,IAEJ,CAKAoI,OAAAA,CAAQO,EAAgBwR,GACtB,IAAI5hB,KAAK4P,aAGP,MAAMQ,EAFNpQ,KAAK4P,aAAaC,QAAQO,EAAO5G,EAAiBoY,EAItD,ECnVF,MAAMsE,GAAqBA,CACzBC,EACAtW,KAEA,IACE,OAAOtI,KAAK6e,MAAMD,GAAgB,GACpC,CAAE,MAAO/Y,GACP,MAAMgD,EAAQ/C,GAAgBD,EAAK,iCACnC,IAAI9H,EAAWuK,GAGb,MAAMO,EAFNP,EAAQO,EAIZ,CAEgB,ECFZiW,GAA2D,CAC/DC,QAAS,CACPC,OAAQ,mBACR,eAAgB,kCAElB5F,OAAQ,OAMJ6F,GAA0BA,CAC9Bha,EACA/D,EACAge,KAEA,MAAMC,EAAqC5f,EACzCuf,GACA5d,GAAW,CAAA,GAWb,OARIge,IACFC,EAAeJ,QAAUxf,EAAe4f,EAAeJ,QAAS,CAC9DK,cAAeF,KAInBC,EAAela,IAAMA,EAEdka,CAAc,EAQjBE,GAAaA,CACjBne,EACAiF,E9C1D6B,I8C2D7Bd,IAEA,IAAIiB,SAAQ,CAACC,EAASC,KACpB,IAAIpF,EACJ,IAA4B,IAAxBF,EAAQoe,YACVle,EAAUF,EAAQsC,UAGlB,GADApC,EAAUwE,GAAyB1E,EAAQsC,MAAM,EAAO,GAAI6B,GACxDlH,EAAOiD,GAOT,YANAoF,EAAO,CACLqC,MAAO,IAAIpK,MhCnDU,2CgCoDrB7E,iBACAsH,YAON,MAAMqe,EAAM,IAAIC,eAgBVC,EAAYvf,IAChBsG,EAAO,CACLqC,MAAO,IAAIpK,MAAMiO,GAAkB8M,GAA+BtZ,EAAGgB,EAAQ+D,MAC7Esa,MACAre,WACA,EAGJqe,EAAIG,UAAYD,EAChBF,EAAI5X,QAAU8X,EAEdF,EAAI7X,OAAS,KAzBMxH,IhCHrByM,EACAkF,EACA8N,EACA1a,EgC0BQsa,EAAI1N,QAAU,KAAO0N,EAAI1N,OAAS,IACpCtL,EAAQ,CACNqZ,SAAUL,EAAIX,aACdW,MACAre,YA7BJsF,EAAO,CACLqC,MAAO,IAAIpK,OhCLjBkO,EgCOU6M,GhCNV3H,EgCOU0N,EAAI1N,OhCNd8N,EgCOUJ,EAAII,WhCNd1a,EgCOU/D,EAAQ+D,IhCNP,GAAG0H,kBAAuBkF,MAAW8N,cAAuB1a,OgCSjEsa,MACAre,WAuBF,EAGFqe,EAAIM,KAAK3e,EAAQkY,OAAQlY,EAAQ+D,MACD,IAA5B/D,EAAQ4e,kBACVP,EAAIO,iBAAkB,GAIxBP,EAAIpZ,QAAUA,EAEd7M,OAAOqG,KAAKuB,EAAQ6d,SAAS1f,SAAQ0gB,IAC/B7e,EAAQ6d,QAAQgB,IAClBR,EAAIS,iBAAiBD,EAAY7e,EAAQ6d,QAAQgB,GACnD,IAGF,IACER,EAAIU,KAAK7e,EACX,CAAE,MAAOyE,GACPW,EAAO,CACLqC,MAAO/C,GAAgBD,GhCjDP8G,EgCiD2B6M,GhCjDXvU,EgCiD0C/D,EAAQ+D,IhCjD1B,GAAG0H,cAAmB1H,MgCkD9Esa,MACAre,WAEJ,ChCrDmBgf,IAACvT,EAAgB1H,CgCqDpC,ICtHJ,MAAMkb,GAIJ/X,iBAAkB,EAElBnK,WAAAA,CAAYoK,EAA8BhD,GACxC5M,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK2P,gBAAkBpK,QAAQvF,KAAK4P,cACpC5P,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKA,aAAM2nB,CACJ3X,GAEA,MAAMxD,IAAEA,EAAG/D,QAAEA,EAAOiF,QAAEA,EAAOka,cAAEA,GAAkB5X,EAEjD,IACE,MAAMjF,QAAa6b,GACjBJ,GAAwBha,EAAK/D,EAASzI,KAAKymB,iBAC3C/Y,EACA1N,KAAK4M,QAEP,MAAO,CACL7B,KAAM6c,EAAgB7c,EAAKoc,SAAWjB,GAAsBnb,EAAKoc,SAAUnnB,KAAK6P,SAChFgY,QAAS9c,EAEb,CAAE,MAAO+c,GAEP,OADA9nB,KAAK6P,QAASiY,EAA2B1X,OAAS0X,GAC3C,CAAE/c,UAAM5J,EAAW0mB,QAASC,EACrC,CACF,CAKAC,YAAAA,CAAsB/X,GACpB,MAAMtH,SAAEA,EAAQ8D,IAAEA,EAAG/D,QAAEA,EAAOiF,QAAEA,EAAOka,cAAEA,GAAkB5X,EACrDC,GAAmB3K,EAAWoD,GAEpCke,GAAWJ,GAAwBha,EAAK/D,EAASzI,KAAKymB,iBAAkB/Y,EAAS1N,KAAK4M,QACnFsD,MAAMnF,IACAkF,GACHvH,EACEkf,EAAgB7c,EAAKoc,SAAWjB,GAAsBnb,EAAKoc,SAAUnnB,KAAK6P,SAC1E9E,EAEJ,IAEDoF,OAAOpF,IACN/K,KAAK6P,QAAQ9E,EAAKqF,OAASrF,GACtBkF,GACHvH,OAASvH,EAAW4J,EACtB,GAEN,CAKA8E,OAAAA,CAAQO,GACN,IAAIpQ,KAAK2P,gBAGP,MAAMS,EAFNpQ,KAAK4P,cAAcC,QAAQO,ElD3Eb,akD+ElB,CAKA4X,aAAAA,CAAc/lB,EAAegmB,GAAS,GACpC,MAAMC,EAAUD,EAAShmB,EAAQyF,EAAS,GAAGzF,MAC7CjC,KAAKymB,gBAAkB,SAASyB,GAClC,CAKAC,eAAAA,GACEnoB,KAAKymB,qBAAkBtlB,CACzB,EAGF,MAAMinB,GAAoB,IAAIV,GAAWzG,GAAqBtO,IC3GxD0V,GAA8B,gBAC9BC,GAA6B,eAC7BC,GAA+B,iBAC/BC,GAA8B,gBAC9BC,GAA0B,OCI1BC,GAA2B,qBAC3BC,GAAuB,2BAEvBC,GAA4B,6BAE5BC,GAAsC,CAC1C,SACA,aACA,cACA,UACA,cACA,kBACA,yBACA,cACA,aCEWC,GAAwD,CACnET,CAACA,IAAiBK,GAClBJ,CAACA,IAAgBK,GACjBH,CAACA,IDjB8B,qBCkB/BD,CAACA,IAAkBK,ICdfG,GAAmBA,KAAgBnjB,EAAkBoC,WAAWghB,UAAUC,eCR1EC,GAAiE,CAGrEC,IAAKA,KAAO7jB,EAAW0C,WAAWmhB,OAAS7jB,EAAW0C,WAAW2D,iBACjEkC,QAASA,KAAOvI,EAAW0C,WAAW6F,SACtC,eAAgBub,KAAO9jB,EAAW0C,WAAWxG,OAAO6nB,OACpD,mBAAoBC,KAAOhkB,EAAW0C,WAAWxG,OAAOC,WACxD,aAAc8nB,KAAOjkB,EAAW0C,WAAWhF,MAAM6E,MACjD,uBAAwB2hB,KAAOlkB,EAAW0C,WAAWhF,MAAMlC,UAAU2oB,MACrE,2BAA4BC,KAAOpkB,EAAW0C,WAAWhF,MAAMlC,UAAUgM,UACzE,4BAA6B6c,KAAOrkB,EAAW0C,WAAWF,OAAOhH,UAAUyf,UAC3E,8BAA+BqJ,KAAOtkB,EAAW0C,WAAWF,OAAOhH,UAAUkK,YAC7E,4BAA6B6e,KAAOvkB,EAAW0C,WAAWF,OAAOhH,UAAUgM,UAC3E,8BAA+Bgd,KAAOxkB,EAAW0C,WAAWF,OAAOhH,UAAUipB,YAC7E,uBAAwBC,KAAO1kB,EAAW0C,WAAWF,OAAOC,eAC5D,iBAAkBkiB,KAAO3kB,EAAW0C,WAAWnH,OAAOmc,SACtD,gBAAiBkN,KAAO5kB,EAAW0C,WAAWnH,OAAOgkB,QACrD,gBAAiBsF,KAAO7kB,EAAW0C,WAAWnH,OAAOupB,QACrD,qBAAsBC,KAAO/kB,EAAW0C,WAAWnH,OAAOypB,aAC1D,4BAA6BC,KAzBJC,MACzB,MAAMC,EAAcziB,WAAWgG,SAASY,cAAc,OAEtD,OADA6b,EAAYnb,aAAa,WAAY,OAC9Bmb,EAAYrI,SAAqC,MAA3BqI,EAAYrI,QAAQsI,EAAkB,EAsB/BF,GAGpCriB,YAAaA,KAAO7C,EAAW0C,WAAWG,eAAiB7C,EAAW0C,WAAW2iB,aACjFC,sBAAuBA,KACpBtlB,EAAW0C,WAAW4iB,yBAA2BtlB,EAAW0C,WAAW6iB,sBAC1EC,YAAaA,KAAOxlB,EAAW0C,WAAW8iB,aAC1C,uBAAwBC,KAAOzlB,EAAW0C,WAAWghB,UAAUgC,YAE/DC,YAAaA,KAAO3lB,EAAW0C,WAAWkjB,YAC1CC,IAAKA,KAAO7lB,EAAW0C,WAAWmjB,MCnC9BC,GAAmBA,KACvB,IAAIC,EAA4B,CAC9BhQ,QAAS,EACTC,MAAO,EACPC,OAAQ,EACRC,WAAY,EACZC,YAAa,GAWf,OARA4P,EAAgB,CACd/P,MAAOtT,WAAWoT,OAAOE,MACzBC,OAAQvT,WAAWoT,OAAOG,OAC1BF,QAASrT,WAAWsjB,iBACpB9P,WAAYxT,WAAWwT,WACvBC,YAAazT,WAAWyT,aAGnB4P,CAAa,ECFhBE,GAA0B9jB,IAC9B,MAEM+jB,EAFgB,CAAC,qBAAsB,8BAEF1e,SAASrF,EAAEc,OADhC,CAAC,GAAI,MACkDuE,SAASrF,EAAEgkB,MAExF,OAAOhkB,aAAaikB,cAAgBF,CAAoB,EAKpDG,GAAqBA,CACzB1qB,EAAoBqnB,GACpBsD,EACAhf,KAEA,IAAIiK,EACAgV,EAEJ,IACE,OAAQ5qB,GACN,KAAKunB,GACH,OAAO,EACT,KAAKH,GACHxR,EAAU+U,EACVC,ELvCoB,qBKwCpB,MACF,KAAKvD,GACHzR,EAAU+U,GAAmB5jB,WAAW8jB,aACxCD,EL1C2B,iBK2C3B,MACF,KAAKtD,GACH1R,EAAU+U,GAAmB5jB,WAAW+jB,eACxCF,EL7C6B,iBK8C7B,MACF,QACE,OAAO,EAGX,QAAKhV,IAILA,EAAQmV,QAAQH,EAAU,UACtBhV,EAAQoV,QAAQJ,KAClBhV,EAAQqV,WAAWL,IACR,GAGf,CAAE,MAAOze,GACP,MAAM+e,ExCJR,GwCIwD9iB,IxCJ3CgD,UwCIiEpL,sBAC5E,IAAI6mB,EAAS,cAKb,OAJIyD,GAAuBne,KACzB0a,EAAS,QAEXlb,GAAQI,KAAK,GAAGmf,IAAYrE,KAAW1a,IAC3B,CACd,GChEIhF,GAASA,CAACnG,EAAY2K,KAC1B,IACE,OAAOwf,mBAAmBnqB,EAC5B,CAAE,MAAOmL,GAEP,YADAR,GAAQwD,MzC2MuB,oCyC3MWhD,EAE5C,GAMIif,GAAUpqB,IACd,IACE,OAAOqqB,mBAAmBrqB,EAC5B,CAAE,MAAOmL,GAEP,MACF,GA0EIyY,GAAMA,IApEG0G,KACb,MAAM3rB,EAA2B,GAC3B4rB,EAAQD,EAAInmB,MAAM,WACxB,IAAIqmB,EAEJ,OAAKD,EAAM,IAKXA,EAAM5lB,SAAQ8lB,IACZD,EAAOC,EAAStmB,MAAM,KACtB,MAAMuE,EAAU8hB,EAAK,GAAKJ,GAAOI,EAAK,SAAMtrB,EAExCwJ,IACF/J,EAAI+J,GAAW8hB,EAAK,GAAKJ,GAAOI,EAAK,SAAMtrB,EAC7C,IAGKP,GAbEA,CAaC,EAmDHwlB,CADmBpe,WAAWgG,SAAS+O,QAc1CA,GAAS,SACbxU,EACAtG,EACAwG,EACAmE,GAEA,OAAQ/M,UAAUC,QAChB,KAAK,EACL,OACA,KAAM,EACJ,MApEM+C,EACV0F,EACAtG,EACA0qB,EACA/f,KAEA,MAAMnE,EAAyB,IAAKkkB,IAAmB,CAAE,EACzD,IAAIC,EAAe,GAAGxkB,GAAOG,EAAMqE,MAAWxE,GAAOnG,EAAO2K,KAExDlH,EAAOzD,KACTwG,EAAQokB,QAAU,GAGhBpkB,EAAQokB,SACVpkB,EAAQqkB,QAAU,IAAI7pB,MAAM,IAAIA,KAASwF,EAAQokB,SAG/CpkB,EAAQtD,OACVynB,GAAgB,UAAUnkB,EAAQtD,QAGhCsD,EAAQskB,SACVH,GAAgB,YAAYnkB,EAAQskB,UAGlCtkB,EAAQqkB,UACVF,GAAgB,aAAankB,EAAQqkB,QAAQE,iBAG3CvkB,EAAQwkB,WACVL,GAAgB,cAAcnkB,EAAQwkB,YAGpCxkB,EAAQykB,SACVN,GAAgB,YAGlB5kB,WAAWgG,SAAS+O,OAAS6P,CAAY,EA+B9B/pB,CAAI0F,EAAMtG,EAAOwG,EAASmE,GACnC,OACE,OAAIrE,EAlBGA,IAA0Bsd,KAActd,GAmBtC5F,CAAI4F,GAENsd,KACT,QACE,OAAOA,KAEb,ECzHMsH,GAAc3gB,IAElB,MAAM4gB,EACsB,mBAAnBplB,WAAWmhB,IAdKkE,KACzB,MAAM5oB,EAAIuJ,SAASY,cAAc,KAEjC,OADAnK,EAAE4oB,KAAOA,EACF5oB,EAAE6oB,QAAQ,EAWwBC,CAAkB/gB,GAAO,IAAI2c,IAAI3c,GAAK8gB,SACzEE,EAAQJ,GAAMhnB,MAAM,MAAQ,GAC5BqnB,EAAOD,EAAMA,EAAM1tB,OAAS,GAC5B4tB,EAAmB,GAGzB,GAAqB,IAAjBF,EAAM1tB,QAAgB2tB,GAAQA,IAASE,SAASF,EAAM,IAAIrsB,WAC5D,OAAOssB,EAIT,GAAIF,EAAM1tB,QAAU,EAElB,OAAI0tB,EAAM,KAAyC,IAAnCA,EAAM,GAAGnO,QAAQ,aACxB,CAAC,aAEHqO,EAIT,IAAK,IAAIxpB,EAAIspB,EAAM1tB,OAAS,EAAGoE,GAAK,EAAGA,GAAK,EAC1CwpB,EAAO3pB,KAAKypB,EAAMnsB,MAAM6C,GAAGF,KAAK,MAGlC,OAAO0pB,CAAM,EChCTE,GAA0BA,KAC9B,MAAMC,EAAY,ID0CJrhB,KACd,MAAMkhB,EAASP,GAAW3gB,GAI1B,IAAK,IAAItI,EAAI,EAAGA,EAAIwpB,EAAO5tB,OAAQoE,GAAK,EAAG,CACzC,MAAM6oB,EAASW,EAAOxpB,GAChB4pB,EPtD4B,UOuD5BC,EAAO,CACXhB,OAAQ,IAAoC,IAAjCA,EAAO1N,QAAQ,aAAsB,GAAK,MAAM0N,KAO7D,GAHAhQ,GAAO+Q,EAAO,EAAGC,GAGbhR,GAAO+Q,GAGT,OADA/Q,GAAO+Q,EAAO,KAAMC,GACbhB,CAEX,CAEA,MAAO,EAAE,ECjEaA,CAAO/kB,WAAW4D,SAASyhB,QAEjD,MAAO,CACLR,OzDZ8B,QyDa9B1nB,KAAM,IACN4nB,OAASc,GAA2B,MAAdA,EAAgCA,OAAZ1sB,EAC1C8rB,SAAU,MACVtT,SAAS,EACV,ECLH,MAAMqU,GACJC,uBAAkD,KAGlDC,oBAAqB,EACrBC,WAAY,EACZruB,OAAS,EAET0F,WAAAA,CAAYiD,EAA0C,CAAA,EAAImE,GACxD,GAAIohB,GAAcI,gBAEhB,OAAOJ,GAAcI,gBAGvBpuB,KAAKyI,QAAUmlB,KACf5tB,KAAK4M,OAASA,EACd5M,KAAKquB,UAAU5lB,GAEfulB,GAAcI,gBAAkBpuB,IAClC,CAEAquB,SAAAA,CAAU5lB,GAOR,OANAzI,KAAKyI,QAAU3B,EAAe9G,KAAKyI,SAAW,CAAE,EAAEA,GAC9CA,EAAQ6lB,8BACHtuB,KAAKyI,QAAQskB,OAEtB/sB,KAAKkuB,mBAAqBvC,GAAmBtD,GAAgBroB,KAAMA,KAAK4M,QACxE5M,KAAKmuB,UAAY5oB,QAAQvF,KAAKyI,QAAQkR,SAAW3Z,KAAKkuB,oBAC/CluB,KAAKyI,OACd,CAEAujB,OAAAA,CAAQppB,EAAaX,GAGnB,OAFA8a,GAAOna,EAAKX,EAAOjC,KAAKyI,QAASzI,KAAK4M,QACtC5M,KAAKF,OAASe,OAAOqG,KAAK6V,MAAUjd,QAC7B,CACT,CAGAmsB,OAAAA,CAAQrpB,GACN,MAAMX,EAAQ8a,GAAOna,GACrB,OAAO+C,EAAY1D,GAAS,KAAOA,CACrC,CAEAiqB,UAAAA,CAAWtpB,GACT,MAAMgC,EAAS5E,KAAKgsB,QAAQppB,EAAK,MAEjC,OADA5C,KAAKF,OAASe,OAAOqG,KAAK6V,MAAUjd,OAC7B8E,CACT,CAGAsN,KAAAA,GAIE,CAGFtP,GAAAA,CAAIiE,GAEF,OADgB7G,KAAKkH,OACNL,IAAU,IAC3B,CAGAK,IAAAA,GACE,OAAOrG,OAAOqG,KAAK6V,KACrB,ECVF,MAAMwR,GAAyB,IAvD/B,MAGEJ,WAAY,EACZruB,OAAS,EACTiL,KAA4B,CAAE,EAE9BvF,WAAAA,CAAYiD,EAAmCmE,GAC7C5M,KAAKyI,QFQgE,CACvEkR,SAAS,GERP3Z,KAAK4M,OAASA,EACd5M,KAAKquB,UAAU5lB,GAAW,CAAA,EAC5B,CAEA4lB,SAAAA,CAAU5lB,GAGR,OAFAzI,KAAKyI,QAAU3B,EAAe9G,KAAKyI,QAASA,GAC5CzI,KAAKmuB,UAAY5oB,QAAQvF,KAAKyI,QAAQkR,SAC/B3Z,KAAKyI,OACd,CAEAujB,OAAAA,CAAQppB,EAAaX,GAGnB,OAFAjC,KAAK+K,KAAKnI,GAAOX,EACjBjC,KAAKF,OAASe,OAAOqG,KAAKlH,KAAK+K,MAAMjL,OAC9BmC,CACT,CAEAgqB,OAAAA,CAAQrpB,GACN,OAAIA,UAAYmI,KACP/K,KAAK+K,KAAKnI,GAER,IACb,CAEAspB,UAAAA,CAAWtpB,GAKT,OAJIA,KAAO5C,KAAK+K,aACP/K,KAAK+K,KAAKnI,GAEnB5C,KAAKF,OAASe,OAAOqG,KAAKlH,KAAK+K,MAAMjL,OAC9B,IACT,CAEAoS,KAAAA,GACElS,KAAK+K,KAAO,CAAA,EACZ/K,KAAKF,OAAS,CAChB,CAEA8C,GAAAA,CAAIiE,GAEF,OADgB7G,KAAKkH,OACNL,IAAU,IAC3B,CAEAK,IAAAA,GACE,OAAOrG,OAAOqG,KAAKlH,KAAK+K,KAC1B,GAGiD,CAAA,EAAI4H,6QCzD2B6b,QAGxE,WAER,SAASC,EAAO7tB,GAEd,OADAA,EAAM2G,KAAKC,UAAU5G,KAChB,gBAAgByE,KAAKzE,EAI3B,CACD,SAAS4G,EAAUtG,GACjB,YAAeC,IAARD,GAAoC,mBAARA,EAAqBA,EAAM,GAAKqG,KAAKC,UAAUtG,EACnF,CACD,SAASwtB,EAAYzsB,GACnB,GAAqB,iBAAVA,EAGX,IACE,OAAOsF,KAAK6e,MAAMnkB,EACnB,CAAC,MAAOwF,GACP,OAAOxF,CACR,CACF,CACD,SAASqD,EAAWrD,GAClB,MAAmC,sBAA5B,CAAE,EAACb,SAASJ,KAAKiB,EACzB,CACD,SAASqE,EAAQrE,GACf,MAAiD,mBAA1CpB,OAAOC,UAAUM,SAASJ,KAAKiB,EACvC,CAGD,SAAS0sB,EAAc9X,GACrB,IAAI+X,EAAO,eACTC,EAAS,MACX,IAEOhY,IACHA,EAAUiY,OAAOhD,cAEnBjV,EAAQmV,QAAQ4C,EAAMC,GACtBhY,EAAQqV,WAAW0C,EACpB,CAAC,MAAOnnB,GACP,IAAIsnB,EAAkB,CACtBA,MAAwB,CAAE,EAC1BA,QAA0B,SAAUxiB,EAAIrL,GACtC,OAAO6tB,EAAgBC,MAAMziB,GAAMzE,OAAO5G,EAClD,EACM6tB,QAA0B,SAAUxiB,GAClC,OAAOwiB,EAAgBC,MAAMjuB,eAAewL,GAAMwiB,EAAgBC,MAAMziB,QAAMpL,CACtF,EACM4tB,WAA6B,SAAUxiB,GACrC,cAAcwiB,EAAgBC,MAAMziB,EAC5C,EACMwiB,MAAwB,WACtB,OAAOA,EAAgBC,MAAQ,EACvC,GACMnY,EAAUkY,CAChB,CAAK,QACKlY,EAAQoV,QAAQ2C,KAAUC,GAAQhY,EAAQqV,WAAW0C,EAC1D,CACD,OAAO/X,CACR,CAGD,IAAIA,EAAU8X,IACd,SAASM,IACP,KAAMjvB,gBAAgBivB,GACpB,OAAW,IAAAA,CAEd,CACDA,EAAMnuB,UAAY,CAChB+B,IAAK,SAAaD,EAAK1B,GACrB,GAAI0B,IAAQ6rB,EAAO7rB,GACjBiU,EAAQmV,QAAQppB,EAAK4E,EAAUtG,SACtButB,GAAAA,EAAO7rB,GAChB,IAAK,IAAInD,KAAKmD,EAAK5C,KAAK6C,IAAIpD,EAAGmD,EAAInD,IAErC,OAAWO,IACZ,EACD2C,IAAK,SAAaC,GAEhB,QAAYzB,IAARyB,EAAmB,CACrB,IAAIssB,EAAM,CAAE,EAIZ,OAHAlvB,KAAK4G,SAAQ,SAAUhE,EAAK1B,GAC1B,OAAOguB,EAAItsB,GAAO1B,CAC5B,IACeguB,CACR,CACD,GAAsB,MAAlBtsB,EAAIb,OAAO,GACb,YAAYotB,IAAIvsB,EAAIwsB,OAAO,IAE7B,IAAIhP,EAAOvgB,UACX,GAAIugB,EAAKtgB,OAAS,EAAG,CAEnB,IADA,IAAIuvB,EAAK,CAAE,EACFnrB,EAAI,EAAGorB,EAAMlP,EAAKtgB,OAAQoE,EAAIorB,EAAKprB,IAAK,CAC/C,IAAIjC,EAAQysB,EAAY7X,EAAQoV,QAAQ7L,EAAKlc,KACzClE,KAAKmvB,IAAI/O,EAAKlc,MAChBmrB,EAAGjP,EAAKlc,IAAMjC,EAEjB,CACD,OAAOotB,CACR,CACD,OAAOX,EAAY7X,EAAQoV,QAAQrpB,GACpC,EACDsP,MAAO,WAEL,OADA2E,EAAQ3E,QACDlS,IACR,EACDuvB,OAAQ,SAAgB3sB,GACtB,IAAI1B,EAAMlB,KAAK2C,IAAIC,GAEnB,OADAiU,EAAQqV,WAAWtpB,GACZ1B,CACR,EACDiuB,IAAK,SAAavsB,GAChB,MAAO,CAAA,EAAG7B,eAAeC,KAAKhB,KAAK2C,MAAOC,EAC3C,EACDsE,KAAM,WACJ,IAAI6J,EAAI,GAIR,OAHA/Q,KAAK4G,SAAQ,SAAUjC,GACrBoM,EAAEhN,KAAKY,EACf,IACaoM,CACR,EACDnK,QAAS,SAAiB8B,GACxB,IAAK,IAAIxE,EAAI,EAAGorB,EAAMzY,EAAQ/W,OAAQoE,EAAIorB,EAAKprB,IAAK,CAClD,IAAItB,EAAMiU,EAAQjU,IAAIsB,GACtBwE,EAAS9F,EAAK5C,KAAK2C,IAAIC,GACxB,CACD,OAAO5C,IACR,EACD6L,OAAQ,SAAgB0gB,GAGtB,IAFA,IAAIiD,EAAMxvB,KAAKkH,OACbmoB,EAAK,CAAE,EACAnrB,EAAI,EAAGorB,EAAME,EAAI1vB,OAAQoE,EAAIorB,EAAKprB,IACrCsrB,EAAItrB,GAAGmb,QAAQkN,IAAQ,IAAG8C,EAAGG,EAAItrB,IAAMlE,KAAK2C,IAAI6sB,EAAItrB,KAE1D,OAAOmrB,CACR,EACDC,IAAK,WACH,OAAOzY,EAAQ/W,MAChB,GAEH,IAAI2vB,EAAS,KACb,SAASC,EAAM9sB,EAAKmI,GAClB,IAAI4kB,EAAO9vB,UACPwvB,EAAK,KAET,GADKI,IAAQA,EAASR,KACF,IAAhBU,EAAK7vB,OAAc,OAAO2vB,EAAO9sB,MACrC,GAAoB,IAAhBgtB,EAAK7vB,OAAc,CACrB,GAAmB,iBAAR8C,EAAkB,OAAO6sB,EAAO9sB,IAAIC,GAC/C,GAAI6rB,EAAO7rB,GAAM,OAAO6sB,EAAO5sB,IAAID,EACpC,CACD,GAAoB,IAAhB+sB,EAAK7vB,QAA+B,iBAAR8C,EAAkB,CAChD,IAAKmI,EAAM,OAAO0kB,EAAOF,OAAO3sB,GAChC,GAAImI,GAAwB,iBAATA,EAAmB,OAAO0kB,EAAO5sB,IAAID,EAAKmI,GACzDA,GAAQzF,EAAWyF,KACrBskB,EAAK,KACLA,EAAKtkB,EAAKnI,EAAK6sB,EAAO9sB,IAAIC,IAC1B8sB,EAAM7sB,IAAID,EAAKysB,GAElB,CACD,GAAoB,IAAhBM,EAAK7vB,QAAgBwG,EAAQ1D,IAAQ0C,EAAWyF,GAClD,IAAK,IAAI7G,EAAI,EAAGorB,EAAM1sB,EAAI9C,OAAQoE,EAAIorB,EAAKprB,IACzCmrB,EAAKtkB,EAAKnI,EAAIsB,GAAIurB,EAAO9sB,IAAIC,EAAIsB,KACjCwrB,EAAM7sB,IAAID,EAAIsB,GAAImrB,GAGtB,OAAOK,CACR,CACD,IAAK,IAAIjwB,KAAKwvB,EAAMnuB,UAAW4uB,EAAMjwB,GAAKwvB,EAAMnuB,UAAUrB,GAE1D,OAAOiwB,CAET,CA/KkFE,2BC2DlF,MAAMC,GAAsB,IApD5B,MAGE3B,oBAAqB,EACrBC,WAAY,EACZruB,OAAS,EAET0F,WAAAA,CAAYiD,EAAgC,CAAA,EAAImE,GAC9C5M,KAAKyI,QJL6BqnB,CACpCnW,SAAS,GIKP3Z,KAAK4M,OAASA,EACd5M,KAAKquB,UAAU5lB,EACjB,CAEA4lB,SAAAA,CAAU5lB,GAIR,OAHAzI,KAAKyI,QAAU3B,EAAe9G,KAAKyI,QAASA,GAC5CzI,KAAKkuB,mBAAqBvC,GAAmBrD,GAAetoB,KAAMA,KAAK4M,QACvE5M,KAAKmuB,UAAY5oB,QAAQvF,KAAKyI,QAAQkR,SAAW3Z,KAAKkuB,oBAC/CluB,KAAKyI,OACd,CAEAujB,OAAAA,CAAQppB,EAAaX,GACnBytB,GAAM7sB,IAAID,EAAKX,GACfjC,KAAKF,OAAS4vB,GAAMJ,KACtB,CAGArD,OAAAA,CAAQrpB,GACN,MAAMX,EAAQytB,GAAM/sB,IAAIC,GACxB,OAAO+C,EAAY1D,GAAS,KAAOA,CACrC,CAEAiqB,UAAAA,CAAWtpB,GACT8sB,GAAMH,OAAO3sB,GACb5C,KAAKF,OAAS4vB,GAAMJ,KACtB,CAEApd,KAAAA,GACEwd,GAAMxd,QACNlS,KAAKF,OAAS,CAChB,CAEA8C,GAAAA,CAAIiE,GAEF,OADgB7G,KAAKkH,OACNL,IAAU,IAC3B,CAGAK,IAAAA,GACE,OAAOwoB,GAAMxoB,MACf,GAG2C,CAAE,EAAEyL,ICGjD,MAAMod,GAAwB,IAzD9B,MAGE7B,oBAAqB,EACrBC,WAAY,EACZruB,OAAS,EACT4vB,MAAQ1nB,WAAW+jB,eAEnBvmB,WAAAA,CAAYiD,EAAkC,CAAE,EAAEmE,GAChD5M,KAAKyI,QLA+BunB,CACtCrW,SAAS,GKAP3Z,KAAK4M,OAASA,EACd5M,KAAKquB,UAAU5lB,EACjB,CAEA4lB,SAAAA,CAAU5lB,GAIR,OAHAzI,KAAKyI,QAAU3B,EAAe9G,KAAKyI,QAASA,GAC5CzI,KAAKkuB,mBAAqBvC,GAAmBpD,GAAiBvoB,KAAMA,KAAK4M,QACzE5M,KAAKmuB,UAAY5oB,QAAQvF,KAAKyI,QAAQkR,SAAW3Z,KAAKkuB,oBAC3CluB,KAACyI,OACd,CAEAujB,OAAAA,CAAQppB,EAAaX,GACnBjC,KAAK0vB,MAAM1D,QAAQppB,EAAKX,GACxBjC,KAAKF,OAASE,KAAK0vB,MAAM5vB,MAC3B,CAEAmsB,OAAAA,CAAQrpB,GACN,MAAMX,EAAQjC,KAAK0vB,MAAMzD,QAAQrpB,GACjC,OAAO+C,EAAY1D,GAAS,KAAOA,CACrC,CAEAiqB,UAAAA,CAAWtpB,GACT5C,KAAK0vB,MAAMxD,WAAWtpB,GACtB5C,KAAKF,OAASE,KAAK0vB,MAAM5vB,MAC3B,CAEAoS,KAAAA,GACElS,KAAK0vB,MAAMxd,QACXlS,KAAKF,OAAS,CAChB,CAEA8C,GAAAA,CAAIiE,GACF,OAAO7G,KAAK0vB,MAAM9sB,IAAIiE,EACxB,CAEAK,IAAAA,GACE,MAAMA,EAAiB,GACvB,IAAK,IAAIhD,EAAI,EAAGA,EAAIlE,KAAK0vB,MAAM5vB,OAAQoE,GAAK,EAAG,CAC7C,MAAMtB,EAAM5C,KAAK0vB,MAAM9sB,IAAIsB,GACf,OAARtB,GACFsE,EAAKnD,KAAKnB,EAEd,CACA,OAAOsE,CACT,GAG+C,CAAE,EAAEyL,IC/C/Csd,GAAoBhvB,IACxB,OAAQA,GACN,KAAKqnB,GACH,OAAOuH,GACT,KAAKtH,GACH,OAAOwH,GACT,KAAKvH,GACH,OAAO+F,GACT,KAAKlG,GACH,OAAW,IAAA2F,GAAc,GAAIrb,IAC/B,QACE,OAAO4b,GACX,EA0CI2B,GAA0BA,CAC9BC,EAAuD,GACvDC,EAAqD,CAAA,EACrDC,EAA2D,GAC3DC,EAAyD,CAAE,KAzBxB7nB,MAfCA,KACpC,MAAM0nB,EAAuB,IAAInC,GAAc,CAAA,EAAIrb,IAAe0b,UAAU5lB,GAC5E+U,GAAM3G,QAAQkG,OAAO9a,MAAQ,CAC3B4qB,OAAQsD,EAAqBtD,OAC7B1nB,KAAMgrB,EAAqBhrB,KAC3B4nB,OAAQoD,EAAqBpD,OAC7BE,SAAUkD,EAAqBlD,SAC/BH,QAASqD,EAAqBrD,QAC9BI,OAAQiD,EAAqBjD,OAC9B,EAiCDqD,CAA6BJ,GA3BM1nB,EA4BP2nB,EA3B5BP,GAAoBxB,UAAU5lB,GAMQA,KACtC8lB,GAAuBF,UAAU5lB,EAAQ,EAqBzC+nB,CAA+BH,GAfM5nB,KACrCsnB,GAAsB1B,UAAU5lB,EAAQ,EAexCgoB,CAA8BH,EAAsB,EClEtD,MAAMrB,GAUJtf,iBAAkB,EAIlBnK,WAAAA,CAAYwK,EAAsBgU,EAAmB0M,GACnD1wB,KAAKuM,GAAKyD,EAAOzD,GACjBvM,KAAKuI,KAAOyH,EAAOzH,KACnBvI,KAAK2wB,YAAc3gB,EAAO2gB,cAAe,EACzC3wB,KAAK4wB,UAAY5gB,EAAO4gB,WAAa,CAAA,EACrC5wB,KAAKgkB,OAASA,GAAUiM,GAAiB3H,IACzCtoB,KAAK6wB,gBAAyD,IAAvChwB,OAAOqG,KAAKlH,KAAK4wB,WAAW9wB,OACnDE,KAAK8wB,cAAgB9gB,EAAO8gB,cAC5B9wB,KAAK+wB,eAAiB/wB,KAAKgkB,OAC3BhkB,KAAK4P,aAAeI,EAAOJ,cAAgBqR,GAC3CjhB,KAAK2P,gBAAkBpK,QAAQvF,KAAK4P,cACpC5P,KAAK4M,OAASoD,EAAOpD,QAAU+F,GAC/B3S,KAAK0wB,eAAiBA,CACxB,CAKAM,cAAAA,CAAepuB,GACb,MAAM2F,KAAEA,EAAIgE,GAAEA,EAAEqkB,UAAEA,EAASC,gBAAEA,EAAeC,cAAEA,GAAkB9wB,KAEhE,GAAI6wB,EACF,OAAOC,EAAgBluB,EAAM,CAAC2F,EAAMgE,EAAI3J,GAAKoB,KAAK,KAIpD,IAAIitB,EAOJ,OANApwB,OAAOgkB,OAAO+L,GAAWhqB,SAAQsqB,IAC3BA,IAAiBtuB,IACnBquB,EAAcH,EAAgBluB,EAAM,CAAC2F,EAAMgE,EAAI3J,GAAKoB,KAAK,KAC3D,IAGKitB,CACT,CAKAE,8BAAAA,GACE,MAAM5oB,KAAEA,EAAIgE,GAAEA,EAAEqkB,UAAEA,EAASE,cAAEA,GAAkB9wB,KACzC+uB,EAAkBkB,GAAiBzH,IAKzC3nB,OAAOqG,KAAK0pB,GAAWhqB,SAAQhE,IAC7B,MAAMX,EAAQjC,KAAK2C,IAAIiuB,EAAUhuB,IAC3BwuB,EAAWN,EAAgBluB,EAAM,CAAC2F,EAAMgE,EAAI3J,GAAKoB,KAAK,KAE5D+qB,EAAgB/C,QAAQoF,EAAUnvB,GAGlCjC,KAAKuvB,OAAO3sB,EAAI,IAGlB5C,KAAKgkB,OAAS+K,CAChB,CAKAlsB,GAAAA,CAAID,EAAaX,GACf,MAAMmvB,EAAWpxB,KAAKgxB,eAAepuB,GAErC,GAAKwuB,EAIL,IAEEpxB,KAAKgkB,OAAOgI,QACVoF,EACApxB,KAAKqxB,QAAQlkB,GAAyBlL,GAAO,EAAO,GAAIjC,KAAK4M,SAEjE,CAAE,MAAOQ,GACHme,GAAuBne,IACzBpN,KAAK4M,QAAQI,KlDsEnB,GkDtEuD,SAAShN,KAAKuM,OlDsExDF,mHkDpEPrM,KAAKmxB,iCAELnxB,KAAK6C,IAAID,EAAKX,IAEdjC,KAAK6P,QAAQxC,GAAgBD,ElDhCNxK,IAC7B,iCAAiCA,gBkD+BO0uB,CAAsB1uB,IAE5D,CACF,CAKAD,GAAAA,CAAaC,GACX,MAAMwuB,EAAWpxB,KAAKgxB,eAAepuB,GAErC,IACE,IAAKwuB,EACH,OAAO,KAGT,MAAM7E,EAAMvsB,KAAKuxB,QAAQvxB,KAAKgkB,OAAOiI,QAAQmF,IAE7C,OAAIxrB,EAAkB2mB,GACT,KAINhlB,KAAK6e,MAAMmG,EACpB,CAAE,MAAOnf,GAEP,OADApN,KAAK6P,QAAQ,IAAI7J,MAAM,GlDtDGpD,IAC9B,yCAAyCA,kBkDqDX4uB,CAAuB5uB,OAAUwK,EAAcI,YAC9D,IACb,CACF,CAKA+hB,MAAAA,CAAO3sB,GACL,MAAMwuB,EAAWpxB,KAAKgxB,eAAepuB,GAEjCwuB,GACFpxB,KAAKgkB,OAAOkI,WAAWkF,EAE3B,CAKAK,iBAAAA,GACE,OAAWzxB,KAAC+wB,cACd,CAKAQ,OAAAA,CAAQtvB,GACN,OAAI2D,EAAkB3D,QAIfjC,KAAK0xB,OAAOzvB,EAAiB,UACtC,CAKAovB,OAAAA,CAAQpvB,GACN,OAAOjC,KAAK0xB,OAAOzvB,EAAO,UAC5B,CAKAyvB,MAAAA,CAAOzvB,EAAsB0vB,GAC3B,MAAMC,GACH5xB,KAAK2wB,cAAgB1uB,GAA0B,iBAAVA,GAAsC,KrE3LpEA,IAA0BA,EAAMue,QAAQ,cAAe,IqE2LH5M,CAAK3R,GAEnE,GAAI2vB,EACF,OAAO3vB,EAGT,MAAMoe,EAAqB,WAAWsR,IAChCE,EAAiB7xB,KAAK0wB,eACxB1wB,KAAK0wB,eAAe7P,aAAqBR,EAAoBpe,GAC7DA,EAEJ,YAAiC,IAAnB4vB,EAAiC5vB,EAAQ4vB,GAAkB,EAC3E,CAKAhiB,OAAAA,CAAQO,GACN,IAAIpQ,KAAK2P,gBAGP,MAAMS,EAFNpQ,KAAK4P,cAAcC,QAAQO,EAAO,SAASpQ,KAAKuM,KAIpD,EC/KF,MAAMulB,GACJC,OAAiC,CAAA,EACjCC,eAAgB,EAIhBriB,iBAAkB,EAElBnK,WAAAA,CAAYkrB,EAAkC9gB,EAA8BhD,GAC1E5M,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK2P,gBAAkBpK,QAAQvF,KAAK4P,cACpC5P,KAAK0wB,eAAiBA,EACtB1wB,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKAuhB,IAAAA,GACE,GAAIvhB,KAAKgyB,cACP,OAGF,MAAMnU,EAAcL,GAAMK,YAAY5b,MAChC+N,EAA8B,CAClCmgB,qBAAsB,CACpBlD,SAAUpP,EAAY9H,eACtBmX,OAAQrP,EAAYoU,aACpBlF,OAAQlP,EAAYqU,gBACpB5D,sBAAuBzQ,EAAYyQ,sBACnC3U,SAAS,GAEXyW,oBAAqB,CAAEzW,SAAS,GAChC0W,uBAAwB,CAAE1W,SAAS,GACnC2W,sBAAuB,CAAE3W,SAAS,IAGpCuW,GACE/oB,EACEL,EAAekJ,EAAOmgB,sBAAwB,CAAA,EAAI3S,GAAM3G,QAAQkG,QAAQ9a,OAAS,KAEnFkF,EAAsB6I,EAAOogB,qBAC7BjpB,EAAsB6I,EAAOqgB,wBAC7BlpB,EAAsB6I,EAAOsgB,wBAG/BtwB,KAAKmyB,uBACLnyB,KAAKgyB,eAAgB,CACvB,CAKAG,oBAAAA,GACEnyB,KAAKoyB,yBAOgB,CAAC5J,GAAgBF,GAAeD,GAAgBE,IAExD3hB,SAAQyrB,IACfpC,GAAiBoC,IAAclE,WACjCnuB,KAAKsyB,SAAS,CACZ/lB,GAAIuc,GAA8BuJ,GAClC9pB,KAAMugB,GAA8BuJ,GACpC1B,aAAa,EACbG,eAAe,EACf7vB,KAAMoxB,GAEV,GAEJ,CAEAD,sBAAAA,GACE,IAAIG,EAAoB/U,GAAM3G,QAAQ5V,KAAKgB,MACvCuwB,EAAiBhV,GAAMK,YAAY5b,MAAM4U,SAASmG,QAGtD,MAAMyV,EAAyBjV,GAAME,SAAS5D,YAAY7X,MAAM4U,SAC5DhR,EAAU4sB,GAAwBxxB,OAAS4E,EAAU4sB,GAAwBzV,YAC/EuV,EAAoBE,GAAwBxxB,KAC5CuxB,EAAiBC,GAAwBzV,SAG3C,IAAIC,GAAyB,EACzByV,EAAiB,CAAE,EACvB7J,GAAkBjiB,SAAQ+rB,IACxB,MAAM/vB,EAAM+vB,EACNC,EAAaD,EACbE,EAAwBL,IAAiB5vB,IAAM3B,KAE/C6xB,ECjIqCC,EAC/CvV,EACAmV,KAEA,IAAIK,EACJ,GAAIxV,EAAME,SAAS7D,WAAW5X,MAAM0X,QAClC,OAAQ6D,EAAME,SAAS7D,WAAW5X,MAAM4U,SAASoc,UAC/C,IAAK,OACHD,EAAwBvK,GACxB,MACF,IAAK,UACgB,gBAAfkK,IACFK,EAAwBvK,IAE1B,MACF,IAAK,cACgB,gBAAfkK,IACFK,EAAwBvK,IAOhC,OAAOuK,CAAqB,EDyGMD,CAAyCvV,GAAOmV,GAGxEN,EACJS,GAAyBD,GAAyBN,GAAqBxe,GAEnEmf,EAAmBlzB,KAAKmzB,+BAA+Bd,EAAaM,GAEtEO,IAAqBzK,KACvBxL,GAAyB,GAG3ByV,EAAiB,IACZA,EACHC,CAACA,GAAa,CACZ1xB,KAAMiyB,EACNtwB,IAAKuU,GAA0Byb,IAElC,IAGHtO,IAAM,KACJ9G,GAAM3G,QAAQ5V,KAAKgB,MAAQswB,EAC3B/U,GAAM3G,QAAQmG,QAAQ/a,MAAQywB,EAC9BlV,GAAM3G,QAAQoG,uBAAuBhb,MAAQgb,CAAsB,GAEvE,CAEQkW,8BAAAA,CAA+Bd,EAA0BM,GAC/D,IAAIO,EAAmBb,EACvB,OAAQA,GACN,KAAK/J,GACE2H,GAAiB3H,KAAgB6F,YACpC+E,EAAmB1K,IAErB,MACF,KAAKD,GACE0H,GAAiB1H,KAAkB4F,YACtC+E,EAAmB1K,IAErB,MACF,KAAKA,GACL,KAAKC,GACH,MAEF,QAGIyK,EADEjD,GAAiB5H,KAAiB8F,UACjB9F,GACV4H,GAAiB3H,KAAgB6F,UACvB7F,GACV2H,GAAiB1H,KAAkB4F,UACzB5F,GAEAC,GAWzB,OANI0K,IAAqBb,GACvBryB,KAAK4M,QAAQI,KnDRiBomB,EAClClmB,EACAmmB,EACAC,EACAJ,IAEA,GAAGhmB,IAAUb,uBAA0CinB,kCAAoDD,+CAAmDH,2BmDGxJE,CAA4BxpB,EAAe+oB,EAAYN,EAAaa,IAIjEA,CACT,CAKAZ,QAAAA,CAASiB,GACP,MAAMC,EAAgBvD,GAAiBsD,EAAYtyB,MAEnD,OADAjB,KAAK+xB,OAAOwB,EAAYhnB,IAAM,IAAI0iB,GAAMsE,EAAaC,EAAexzB,KAAK0wB,gBAClE1wB,KAAK+xB,OAAOwB,EAAYhnB,GACjC,CAKAknB,QAAAA,CAASlnB,GACP,OAAWvM,KAAC+xB,OAAOxlB,EACrB,CAKAsD,OAAAA,CAAQO,GACN,IAAIpQ,KAAK2P,gBAGP,MAAMS,EAFNpQ,KAAK4P,cAAcC,QAAQO,EAAOxG,EAItC,EEhOF,MAOM8pB,GAAclnB,IAClB,IAAK/G,EAAS+G,GACZ,SAGF,IAOE,OAJIlH,EAAW0C,WAAWmhB,MAExB,IAAIA,IAAI3c,GAEH8H,GAAYjP,KAAKmH,EAC1B,CAAE,MAAO/E,GACP,OAAO,CACT,GCDIksB,GAAmBA,CAACna,EAAmBC,KAZnBD,KACxB,IAAK/T,EAAS+T,IAAoD,IAAtCA,EAAoB5F,OAAO9T,OACrD,MAAM,IAAIkG,MtDqDsBwT,IAClC,kBAAkBA,0GsDtDAoa,CAA2Bpa,GAC7C,EAUAqa,CAAiBra,GAPWC,KAC5B,IAAKia,GAAWja,GACd,MAAU,IAAAzT,MtDkD2ByT,IACvC,uBAAuBA,+GsDnDLqa,CAAgCra,GAClD,EAKAsa,CAAqBta,EAAa,EAkC9Bua,GAAqBC,GAGlB,GAxBgBznB,KAEvB,MAAM0nB,EAAS,IAAI/K,IAAI3c,IAGjB4gB,KAAEA,EAAI+G,SAAEA,GAAaD,EAGrB1G,EAAkBJ,EAAKhnB,MAAM,KACnC,IAAIynB,EASJ,OALEA,EAFEL,EAAM1tB,OAAS,EAEL,GAAG0tB,EAAMA,EAAM1tB,OAAS,MAAM0tB,EAAMA,EAAM1tB,OAAS,KAGnDstB,EAEP,GAAG+G,MAAatG,GAAW,EAItBuG,CAAgBtF,OAAOljB,SAASyhB,SAClB4G,EAASjpB,WAAW,KAAOipB,EAAS/oB,UAAU,GAAK+oB,ICtDzEI,GAAyB7nB,GAC7BA,GAAK+T,SAAS,KAAO8T,GAAsB7nB,EAAItB,UAAU,EAAGsB,EAAI1M,OAAS,IAAM0M,EAE3E8nB,GAAa9nB,IACjB,IAEE,OADe,IAAI2c,IAAI3c,GACT4gB,IAChB,CAAE,MAAOhd,GACP,OAAO,IACT,GAQImkB,GAAsBC,GAA6BF,GAAUE,IAAa,GAO1EC,GAAwBjoB,IAC5B,MAAM5H,EAAwB,CAAE,EAChC,IACE,MAAMsvB,EAAS,IAAI/K,IAAI3c,GACjBkoB,EAAa,OACnBR,EAAOS,aAAa/tB,SAAQ,CAAC3E,EAAO2yB,KAClC,GAAIA,EAAO5pB,WAAW0pB,GAAa,CACjC,IAAIG,EAAWD,EAAO1pB,UAAUwpB,EAAW50B,QAE1B,aAAb+0B,IACFA,EAAW,QAEbjwB,EAAOiwB,GAAY5yB,CACrB,IAEJ,CAAE,MAAOmO,GACP,CAEF,OAAOxL,CAAM,ECzCTkwB,GAAiB,KAkBjBC,GAAgCA,CACpCC,EACApoB,KAGA,IAAIooB,GAD2B,CAAC,KAAM,MACeloB,SAASkoB,GAU9D,OAAOA,EATLpoB,GAAQI,KxD8GV,GwD5GM1D,IxD4GO+C,kCwD3GP2oB,4GACAF,4BAKsB,EAWxBG,GAAsBA,CAC1BC,EACAC,EACAH,EACApoB,KAGA,GAAIsoB,GAAcr0B,OAAOqG,KAAKguB,GAAYp1B,OAAS,EAAG,CACpD,MAGMs1B,EAhDqBC,KAE7B,GAAIryB,MAAMsD,QAAQ+uB,IAASA,EAAKv1B,OAAS,EAAG,CAC1C,MAAMc,EAAMy0B,EAAK5L,MAAK6L,IAAyB,IAAjBA,EAAKtP,UACnC,GAAIplB,GAAO8yB,GAAW9yB,EAAI4L,KACxB,OAAO5L,EAAI4L,GAEf,CACU,EAwCW+oB,CAFmBL,EADvBH,GAA8BC,EAAuBpoB,IAAWkoB,KACnBI,EAAWJ,KAGvE,GAAIM,EACF,OAAOA,CAEX,CAEA,GAAID,EACF,OAAOA,CAIO,ECzEZK,GAAwD,OACxDC,GAAyD,YCHzDC,GAA8B,CAClCxf,KAAK,GCsFDyf,GAAuB1zB,GAC3BgF,EAAiBhF,IAAUe,MAAMsD,QAAQrE,GAQrC2zB,GAAwBA,CAC5BC,EACAjpB,KAEA,IAAIoN,SAAEA,GAAuD6b,EAC7D,MAAMC,EAA2B9b,EAAWhF,GAA+BgF,QAAY7Y,E3D7E/C40B,IAGxCC,E2DmFA,OARIhc,IAAa8b,IACflpB,GAAQwD,O3D5EV4lB,E2D6EgEhhB,G3D3EhE,G2D2EsC1L,I3D3EzB+C,0B2D2EyC2N,wF3D3EiHnZ,OAAOqG,KAC5K8uB,S2D8EAhc,OAAW7Y,GAEN,CAAE6Y,WAAU8b,2BAA0B,EASzCG,GAA2BA,CAC/BJ,EACAjpB,KAEA,IAAIkpB,EAIA9b,EAHAkc,EAA8B,GAC9BC,EAA6B,GAC7B9c,GAAc,EAGdM,GAA6C,IAAnCkc,GAAuBlc,QACjC1S,EAA2C4uB,IAA0Blc,MAEpEK,WAAU8b,4BAA6BF,GAAsBC,EAAuBjpB,IAEnF+oB,GAAoBE,EAAsBK,qBAC5CA,EAAoBL,EAAsBK,kBAC1C7c,GAAc,GAGZsc,GAAoBE,EAAsBM,oBAC5CA,EAAmBN,EAAsBM,iBACzC9c,GAAc,IAIlB,MAAM+c,EAAe,CACnBF,oBACAC,oBAMF,OAFAxc,EAAUA,GAAWpU,QAAQuwB,GAEtB,CACL9b,WACA8b,2BACAzc,cACAM,UACAyc,eACD,ECvGGC,GAAYA,KAChB,MAAMC,EAAUtoB,SAASM,qBAAqB,UACxCioB,EAAmB,2BAGzB,IAAK,MAAMC,KAAUF,EAAS,CAC5B,MAAMlnB,EAAMonB,EAAOC,aAAa,OAChC,GAAIrnB,GAAOmnB,EAAiBlxB,KAAK+J,GAC/B,OAAOA,CAEX,CAEgB,EAQZsnB,GAAuBA,CAACC,EAA2B/pB,KC1ExBgqB,M7DgH/BC,EACAC,E4DlCA,GAJAtZ,GAAMQ,UAAUtF,wBAAwBzW,QC3ET20B,ED4ELD,EAAIvzB,OAAO4M,QC3Ec,IAAnD4mB,GAAcG,iBAAiBC,QAAQrd,SCEnCmV,OAAemI,QAAWnI,OAAemI,OAAOC,SAAYpI,OAAemI,OAAOC,QAAQ3qB,IF0E9FiR,GAAMQ,UAAUrF,0BAA0B1W,MCvET20B,KACmB,IAApDA,GAAcG,iBAAiBjZ,SAASnE,QDsEUhB,CAA0Bge,EAAIvzB,OAAO4M,QAEnFwN,GAAMQ,UAAUtF,wBAAwBzW,MAAO,CACjD,MAAMk1B,EC7EuCP,IAC/CA,GAAcG,iBAAiBC,QAAQhd,SD4ERod,CAAwCT,EAAIvzB,OAAO4M,QAG1EqnB,EAA6BF,EAC/B/hB,GAAuC+hB,QACvCh2B,EAECwE,EAAYwxB,IAA0BE,GAEzCzqB,GAAQI,M5DuBZ6pB,E4DnBQzhB,G5DoBR0hB,E4DnBQjiB,G5DqBR,G4DxBQvL,I5DwBK+C,mC4DvBL8qB,iF5DuByKt2B,OAAOqG,KACtL2vB,8BAC2BC,6B4DlB3BtZ,GAAMQ,UAAUpF,iCAAiC3W,MAC/Co1B,GACAjiB,GAAuCP,GAC3C,GAGIyiB,GAAqC1qB,IACzC,MAAMsK,qBACJA,EAAoBqgB,oBACpBA,EACA1gB,QAAS2gB,GACPha,GAAMK,YAAY5b,MACtB,IAAIowB,EAAcmF,GAAqBv2B,KACnC4E,EAAUwsB,KN9EYA,IACH,iBAAhBA,GAA4Bve,GAAwBhH,SAASulB,GM6ErCoF,CAAmBpF,KAChDzlB,GAAQI,K5DX4B0qB,EACtCxqB,EACAmlB,EACAsF,IAEA,GAAGzqB,IAAUb,uBAA0CgmB,6EAAuFve,0BAA+C6jB,2B4DOzLD,CAAgCpuB,EAAgB+oB,EAAate,KAE/Dse,EAActe,IAGhB,IAAI6jB,EAA2BJ,GAAqB1gB,YAAYC,QAChE,MAAM+F,EACJ8a,GAA4BviB,GAAyCuiB,G5DFlBC,IAGrDC,EACAC,G4DAKpyB,EAAYiyB,IAA6BjyB,EAAYmX,IAExDlQ,GAAQI,M5DHV8qB,E4DOMziB,G5DNN0iB,E4DOMjjB,G5DLN,G4DEMxL,I5DFO+C,qC4DGPurB,gF5DH8K/2B,OAAOqG,KACzL4wB,6BAC0BC,6B4DM1BH,EAA2B9iB,IAClBnP,EAAYiyB,KACrBA,EAA2B9iB,IAI7B,MAAMkjB,EAA2BR,GAAqBxgB,QAChDihB,EACHD,GACDJ,IAA6B9iB,IAEE,IAA7BkjB,GAAqCC,IAAsBD,GAC7DprB,GAAQI,K5DhBoCkrB,EAC9ChrB,EACA0qB,EACAG,IAEA,GAAG7qB,IAAUb,qGAAwHurB,yBAAgDG,4G4DYjLG,CACE5uB,EACAsuB,EACA9iB,KAKNwP,IAAM,KACJ9G,GAAM3G,QAAQ5V,KAAKgB,MAAQowB,EAC3B,IAAI8F,EAAgBX,GAAqBza,QAAU,CAAE,EAErD,GAAI7F,EAAsB,CACxBsG,GAAMU,cAAcf,2BAA2Blb,MAAQiV,EACvD,MAAMkG,EAAiB4W,GACrBuD,GzDzI8B,cyD2IhC,GAAI7D,GAAWtW,GAAiB,CAC9BI,GAAMU,cAAcd,eAAenb,MAAQoyB,GAAsBjX,GAEjDkX,GAAUxF,OAAOljB,SAASyhB,QAClBiH,GAAUlX,KAKhC+a,EAAgB,IACXA,EACHlL,SAAU,OACVC,QAAQ,GAGd,MACE1P,GAAMU,cAAcf,2BAA2Blb,OAAQ,CAE3D,CAEAub,GAAM3G,QAAQkG,OAAO9a,MAAQk2B,EAE7B3a,GAAM3G,QAAQiG,qBAAqB7a,MACjCoT,GAAyCuiB,GAE3Cpa,GAAM3G,QAAQG,QAAQ/U,MAAQg2B,CAAiB,GAC/C,EAGEG,GAAsCxrB,IAC1C,MAAMoN,SAAEA,EAAQ8b,yBAAEA,EAAwBzc,YAAEA,EAAWM,QAAEA,EAAOyc,aAAEA,GAChEH,GAAyBzY,GAAMK,YAAY5b,MAAMo2B,kBAAmBzrB,GAGhE0rB,EAAiB9a,GAAMK,YAAY5b,MAAM4X,WAE/C,IAAI0e,EACFD,GAAgBzhB,SAASoc,UAAYuC,G5DgBQgD,IAC/CtrB,EACAurB,E4DhBI5yB,EAAU0yB,KADY,CAAC,OAAQ,UAAW,eACOzrB,SAASyrB,KAC5DA,EAAkB/C,GAElB5oB,GAAQI,M5DYVE,E4DVM5D,E5DWNmvB,E4DVMH,GAAgBzhB,SAASoc,S5Da/B,GAAG/lB,IAAUb,uCAA0DosB,qI4DZjEjD,+BAKN,IAAIkD,EACFJ,GAAgBK,QAAQC,UAAYnD,GAElC5vB,EAAU6yB,KADQ,CAAC,YAAa,UACgB5rB,SAAS4rB,KAC3DA,EAAqBjD,GAErB7oB,GAAQI,K5DGyC6rB,EACnD3rB,EACA4rB,EACAC,IAEA,GAAG7rB,IAAUb,2CAA8DysB,mHAAsIC,2B4DP7MF,CACEvvB,EACAgvB,GAAgBK,QAAQC,SACxBnD,MAKNnR,IAAM,KACJ9G,GAAME,SAAS9D,+BAA+B3X,MAAQ6zB,EACtDtY,GAAME,SAASrE,YAAYpX,MAAQoX,EACnCmE,GAAME,SAAS/D,QAAQ1X,MAAQ0X,EAC/B6D,GAAME,SAAS3S,KAAK9I,MAAQm0B,EAC5B5Y,GAAME,SAAS1D,SAAS/X,MAAQ+X,EAEhCwD,GAAME,SAAS7D,WAAW5X,MAAQ,CAIhC0X,SACkD,IAAhD6D,GAAMK,YAAY5b,MAAM4X,YAAYF,UACpB,IAAhBN,IACY,IAAZM,EACF9C,QAAS,CACPoc,SAAUsF,GAEZI,OAAQ,CACNC,SAAUF,GAEb,GACD,EAmCEM,GAA6CpsB,IACjD,GAAI4Q,GAAMW,gBAAgBZ,gBAAgBtb,MAAO,CAC/C,MAAMg3B,EAA2C,WACjD,IAAI3b,EAAoC2b,EAEpCzb,GAAMK,YAAY5b,MAAMkU,YACtBqH,GAAMC,aAAatF,kBAAkBlW,MACvCqb,EAAwB,eAExBA,EAAwB2b,EAExBrsB,GAAQI,K5D9Id,G4D8IkD1D,I5D9IrC+C,mG4DkJXiY,IAAM,KACJ9G,GAAMW,gBAAgBb,sBAAsBrb,MAAQqb,CAAqB,GAE7E,GAGI4b,GAAqBA,CACzBvjB,EACA6D,EACAjD,EACA3J,KAEA,MAAMusB,EAAkB,IAAIxtB,gBAAgB,CAC1C1H,E5EzTgB,M4E0ThB2M,EAAG5G,GACHovB,MAAO7kB,GACPiF,WACAjD,wBAAyBA,EAAwBnV,aAGnD,IAAIi4B,EAASzkB,GACT+f,EAAewE,EACfG,EAAW,iBACXz1B,EAAO,GACX,GAAI6vB,GAAW/d,GAAY,CACzB,MAAM4jB,EAAoB,IAAIpQ,IAAIxT,GAC5B0e,GAAsBkF,EAAkBD,UAAqB/Y,SAAS,mBAC1EgZ,EAAkBD,SAAW,GAC3BjF,GAAsBkF,EAAkBD,2BAG5CC,EAAkBD,SAAkCC,EAAkBD,SP3Ud9Y,QAAQ,UAAW,KO6U3E2Y,EAAgBvyB,SAAQ,CAAC3E,EAAOW,KACkB,OAA5C22B,EAAkB5E,aAAahyB,IAAIC,IACrC22B,EAAkB5E,aAAa9xB,IAAID,EAAKX,EAC1C,IAGFo3B,EAASE,EAAkBF,OAC3BC,EAAWC,EAAkBD,SAC7B3E,EAAe4E,EAAkB5E,aACjC9wB,EAAO01B,EAAkB11B,IAC3B,MACE+I,GAAQI,K5DzIuBwsB,EAACtsB,EAAiByI,IACnD,GAAGzI,IAAUb,qCAAwDsJ,8D4DwItD6jB,CAA2BlwB,EAAgBqM,IAG1D,MAAO,GAAG0jB,IAASC,KAAY3E,IAAe9wB,GAAM,EG3TtD,MAAM41B,GAIJ9pB,iBAAkB,EAElBnK,WAAAA,CAAYk0B,EAAyB9pB,EAA8BhD,GACjE5M,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK05B,WAAaA,EAClB15B,KAAK2P,gBAAkBpK,QAAQvF,KAAK4P,cAEpC5P,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,MACjCA,KAAK25B,cAAgB35B,KAAK25B,cAAc7pB,KAAK9P,KAC/C,CAEAokB,aAAAA,GACEC,IAAO,KACLrkB,KAAK4M,QAAQ6G,eAAe+J,GAAMI,UAAUlK,SAASzR,MAAM,GAE/D,CAMAsf,IAAAA,GACEvhB,KAAKokB,gBAELuP,GAAiBnW,GAAMI,UAAUpE,SAASvX,MAAOub,GAAMI,UAAUnE,aAAaxX,OAE9E,MAAMsU,EAA0BiH,GAAMK,YAAY5b,MAAMsU,wBAGlDqjB,ECpDqBC,EAC7BC,EACAvjB,EACAwjB,KAEA,IAAI9gB,EAAsB,GAG1B,GAAI8gB,EAA2B,CAG7B,GAFA9gB,EAAsBob,GAAsB0F,IAEvC9gB,IAAwBya,GAAWza,GACtC,MAAM,IAAIjT,MhEhBgB,6EgEmB5B,OAAOiT,CACT,CAGA,MAAM+gB,EAAS3D,KAUf,OATApd,EAAsB+gB,EAClBA,EAAO5zB,MAAM,KAAK/E,MAAM,GAAI,GAAG44B,OAAO7lB,IAAapQ,KAAK,KACxD0Q,GAGA6B,IACF0C,EAAsBA,EAAoBuH,QAAQ/L,GAAsBqlB,IAGnE7gB,CAAmB,EDuBL4gB,CACjB7vB,GACAuM,EACAiH,GAAMK,YAAY5b,MAAMi4B,gBAGpBhhB,ECrBiBihB,KACzB,IAAIjhB,EAAiB,GAGrB,GAAIihB,EAAsB,CAGxB,GAFAjhB,EAAiBmb,GAAsB8F,IAElCjhB,IAAmBwa,GAAWxa,GACjC,MAAM,IAAIlT,MhEhDmB,wEgEmD/B,OAAOkT,CACT,CAGA,MAAM8gB,EAAS3D,KAKf,OAJAnd,EAAiB8gB,EACbA,EAAO5zB,MAAM,KAAK/E,MAAM,GAAI,GAAG44B,OAAO5lB,IAAiBrQ,KAAK,KAC5D2Q,GAEGuE,CAAc,EDCIkhB,CAAkB5c,GAAMK,YAAY5b,MAAMo4B,mBAEjE/C,GAAkCt3B,KAAK4M,QACvCwrB,GAAmCp4B,KAAK4M,QACxCosB,GAA0Ch5B,KAAK4M,QAE/C,MAAM8G,SAAEA,EAAQiC,UAAEA,GAAc6H,GAAMK,YAAY5b,MAGlDqiB,IAAM,KACJ9G,GAAMI,UAAU3E,oBAAoBhX,MAAQ23B,EAC5Cpc,GAAMI,UAAU1E,eAAejX,MAAQiX,EAEnCxF,IACF8J,GAAMI,UAAUlK,SAASzR,MAAQyR,GAGnC8J,GAAMI,UAAUzE,gBAAgBlX,MAAQi3B,GACtCvjB,EACA6H,GAAMI,UAAUpE,SAASvX,MACzBsU,EACAvW,KAAK4M,OACN,IAGH5M,KAAKs6B,WACP,CAKAzqB,OAAAA,CAAQO,EAAgBwR,EAAwBC,GAC9C,IAAI7hB,KAAK2P,gBAGP,MAAMS,EAFNpQ,KAAK4P,cAAcC,QAAQO,EAAO9G,EAAgBsY,EAAeC,EAIrE,CAMA8X,aAAAA,CAAcxS,EAA0CU,GAGtD,IAAKV,EAEH,YADAnnB,KAAK6P,S/D1DwBiY,E+D0DUD,GAASzX,M/DzDpD,8CAA8C0X,MADbA,M+D8D/B,IAAI6O,EACJ,IAEIA,EADElxB,EAAS0hB,GACL5f,KAAK6e,MAAMe,GAEXA,CAEV,CAAE,MAAO/Z,GAEP,YADApN,KAAK6P,QAAQzC,EAAK4G,IAAgC,EAEpD,CAEA,IT7GyB2iB,IAC3BpwB,EAA0BowB,IAC1BpwB,EAA0BowB,EAAIvzB,UAC7BwC,EAAkB+wB,EAAIvzB,OAAOmJ,KAC9BhG,EAA0BowB,EAAIvzB,OAAO4M,SACrChN,MAAMsD,QAAQqwB,EAAIvzB,OAAO+f,cSwGlBoX,CAAoB5D,GAEvB,YADA32B,KAAK6P,QAAQ,IAAI7J,MAAMgO,SAAiC7S,GAAW,GAKrE,IAA2B,IAAvBw1B,EAAIvzB,OAAOuW,QAEb,YADA3Z,KAAK4M,QAAQwD,M/DhIW,qF+DqI1BsmB,GAAqBC,EAAK32B,KAAK4M,QAG/B,MAAM6M,EAAewb,GACnB0B,EAAIvzB,OAAO8xB,WACX1X,GAAMI,UAAUnE,aAAaxX,MAC7Bub,GAAMK,YAAY5b,MAAMu4B,gBACxBx6B,KAAK4M,QAGP,IAAK6M,EAEH,YADAzZ,KAAK6P,QAAQ,IAAI7J,M/DlJM,mJ+DkJuB7E,GAAW,GAG3D,MAAM4c,EACJ4Y,EAAIvzB,OAAO+f,aAAarjB,OAAS,EE5JLqjB,KAChC,MAAMpF,EAAoC,GAiB1C,OAhBAoF,EAAavc,SAASkc,IAChBA,EAAYnJ,UAAYmJ,EAAY2X,SACtC1c,EAAmBha,KAAK,CACtBwI,GAAIuW,EAAYvW,GAChBmuB,YAAa5X,EAAY6X,sBAAsBD,YAC/C1qB,OAAQ8S,EAAY9S,OACpBgV,oCACElC,EAAYkC,sCAAuC,EACrD4V,oCACE9X,EAAY8X,sCAAuC,EACrDC,eAAgB,GAAG/X,EAAY6X,sBAAsBD,YAAY3Q,WAAW,IAAK,UAC/EjH,EAAYvW,MAGlB,IAEKwR,CAAkB,EF0IgB+c,CAAyBnE,EAAIvzB,OAAO+f,cAAgB,GAG3FmB,IAAM,KAEJ9G,GAAMpa,OAAOnB,MAAQ,CACnB+N,OAAQ2mB,EAAIvzB,OAAO4M,OACnBzD,GAAIoqB,EAAIvzB,OAAOmJ,GACfwuB,YAAapE,EAAIvzB,OAAO23B,aAI1Bvd,GAAMO,mBAAmBnC,uBAAuB3Z,MAAQ8b,EAGxDP,GAAM/G,QAAQiG,wBAAwBza,MAAQub,GAAMK,YAAY5b,MAAMwU,SAAW,GHiF1DukB,KAC3B,IAGIC,EAHAlhB,EACFyD,GAAME,SAAS3D,mBAAmB9X,MAGhCsE,EAA0By0B,EAAKE,6BAC7B1d,GAAME,SAAS1D,SAAS/X,QAC1B8X,EACEihB,EAAKE,0BAA0BC,UAAU1R,MACvCxlB,GAAKA,EAAE+V,WAAawD,GAAME,SAAS1D,SAAS/X,SAC3C8X,oBAAsByD,GAAME,SAAS3D,mBAAmB9X,OAG/Dg5B,EAAcD,EAAKE,2BAIiB,WAAlC1d,GAAME,SAAS1D,SAAS/X,QAC1B8X,OAAqB5Y,GAGvBmjB,IAAM,KACJ9G,GAAME,SAASzD,SAAShY,MAAQmC,EAAM62B,GACtCzd,GAAME,SAAS3D,mBAAmB9X,MAAQ8X,CAAkB,GAC5D,EGvGEqhB,CAAoBzE,GAIpBnZ,GAAMI,UAAU5E,mBAAmB/W,MAAQoyB,GAAsB5a,GACjE+D,GAAMI,UAAUxE,OAAOnX,MAAQ,YAAY,GAE/C,CAOAq4B,SAAAA,GACE,MAAMe,EAAmB7d,GAAMK,YAAY5b,MAAMq5B,gBACjD,GAAID,EAAkB,CACpB,IAAK/1B,EAAW+1B,GACd,MAAM,IAAIr1B,M/D5LiB,8H+D+L7B,MAAM2wB,EAAM0E,IAER1E,aAAe9oB,QACjB8oB,EACGzmB,MAAKqrB,GAAQv7B,KAAK25B,cAAc4B,KAChCprB,OAAM/C,IACLpN,KAAK6P,QAAQzC,EAAK,eAAe,IAGrCpN,KAAK25B,cAAchD,EAEvB,MAEE32B,KAAK05B,WAAW3R,aAAa,CAC3Bvb,IAAKgR,GAAMI,UAAUzE,gBAAgBlX,MACrCwG,QAAS,CACP6d,QAAS,CACP,oBAAgBnlB,IAGpBuH,SAAU1I,KAAK25B,eAGrB,EG7NF,MCGM6B,GAAcA,IAAcxtB,UAAUwmB,UAAY,UAwDlDiH,GAA2BA,KAC/B,MAAMC,EAnDgBC,MACtB,MAAMC,EAAO5tB,SAASM,qBAAqB,QAC3C,IAAIotB,EAAe,GAEnB,IAAK,IAAIx3B,EAAI,EAAG03B,EAAK13B,GAAIA,GAAK,EAAG,CAC/B,MAAM23B,EAAMD,EAAK13B,GACjB,GAAgC,cAA5B23B,EAAIpF,aAAa,SAA2BiF,EAAc,CAC5DA,EAAeG,EAAIpF,aAAa,SAAW,GAC3C,KACF,CACF,CAEA,OAAOiF,CAAY,EAuCEC,GACrB,IAAIx2B,EAAO6C,WAAW4D,SAAS0tB,SAC/B,MAAQjM,KAAMyO,GAAW9zB,WAAW4D,SACpC,IAAImwB,EAAUD,EACd,MAAMjwB,OAAEA,GAAW7D,WAAW4D,SAG9B,GAAI8vB,EACF,IACE,MAAMxH,EAAS,IAAI/K,IAAIuS,GAGrBK,EADoB,KAAlB7H,EAAOroB,OACC6vB,EAAe7vB,EAEf6vB,EAGZv2B,EAAO+uB,EAAOoF,QAChB,CAAE,MAAOlsB,GACP,CAIJ,MAAMZ,EZ9BmBA,KACzB,IAAIwvB,EAAiBxvB,EACrB,IACE,MAAM0nB,EAAS,IAAI/K,IAAI3c,GACvBwvB,EAAiB9H,EAAOmF,OAASnF,EAAOoF,SAAWpF,EAAOroB,MAC5D,CAAE,MAAOuE,GACP,CAEF,OAAO4rB,CAAc,EYsBTC,CAAkBF,IACxBG,MAAEA,GAAUluB,SACZwmB,EAAWgH,KACjB,MAAO,CACLr2B,OACAqvB,WACA2H,iBAAkB5H,GAAmBC,GACrC3oB,SACAqwB,QACA1vB,MACA4vB,QAASN,EACV,EChGGO,GACF,0EAAsDx7B,OAAOqG,KAC3DgiB,IACAllB,KAAK,SAKLs4B,GAAqB,sBC0B3B,MAAMC,GAKJ/2B,WAAAA,CAAYoK,EAA8BhD,GACxC5M,KAAK4M,OAASA,EACd5M,KAAK4P,aAAeA,EACpB5P,KAAKwhB,kBAAoB,IAAI9R,GAAkB1P,KAAK4P,aAAc5P,KAAK4M,QACvE5M,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,MACjCA,KAAKw8B,QAAUx8B,KAAKw8B,QAAQ1sB,KAAK9P,KACnC,CAEAuhB,IAAAA,GACE,IACEvhB,KAAKy8B,6BACLz8B,KAAK08B,uBACP,CAAE,MAAOtvB,GACPpN,KAAK6P,QAAQzC,EACf,CACF,CAMAuvB,yBAAAA,GACErY,IAAM,KAEJ9G,GAAMC,aAAa5G,QAAQoB,yBAAyBhW,MAAQ0pB,GAC1DtD,GACA4H,GAAiB5H,IACjBroB,KAAK4M,QAEP4Q,GAAMC,aAAa5G,QAAQmB,wBAAwB/V,MAAQ0pB,GACzDrD,QACAnnB,EACAnB,KAAK4M,QAEP4Q,GAAMC,aAAa5G,QAAQqB,0BAA0BjW,MAAQ0pB,GAC3DpD,QACApnB,EACAnB,KAAK4M,QAIP4Q,GAAMC,aAAatF,kBAAkBlW,OhChExC2D,EAAkBoC,WAAWghB,UAAUgC,aACxC1lB,EAAW0C,WAAWghB,UAAUgC,YgCgE5BxN,GAAMC,aAAapF,gBAAgBpW,MAAQ8mB,KAC3CvL,GAAMC,aAAanF,kBAAkBrW,OhCxExC2D,EAAkBoC,WAAW0pB,SAAWpsB,EAAW0C,WAAW0pB,OAAOkL,iBgCyElEpf,GAAMC,aAAalF,OAAOtW,MhChEFsD,QAAQyC,WAAWghB,UAAUjO,UAAU8hB,MAAM,qBgCiErErf,GAAMC,aAAa1F,SAAS9V,MAAQ+F,WAAWghB,UAAU8T,OAGzDtf,GAAMtQ,QAAQ6N,UAAU9Y,MF7DT86B,MACnB,GAAIp3B,EAAYqC,WAAWghB,WACzB,OAAW,KAGb,IAAIjO,UAAEA,GAAc/S,WAAWghB,UAC/B,MAAMgU,MAAEA,GAAUh1B,WAAWghB,UAI7B,GAAIgU,GAASn8B,OAAOkC,eAAei6B,GAAOC,QAAS,CAGjD,MAAMC,EAAaniB,EAAU8hB,MAAM,uBAE/BK,IACFniB,EAAY,GAAGA,WAAmBmiB,EAAW,KAEjD,CAEA,OAAOniB,CAAS,EEyCoBgiB,GAChCvf,GAAMtQ,QAAQiO,OAAOlZ,MFtCrB0D,EAAYqC,WAAWghB,WACd,KAGNhhB,WAAWghB,UAAUmU,UAAan1B,WAAWghB,UAAkBoU,gBEmClE5f,GAAMtQ,QAAQkO,OAAOnZ,MAAQmpB,KAC7B5N,GAAMtQ,QAAQwO,SAASzZ,MHxFTo7B,MAClB,MAAM3hB,GAAW,IAAIzY,MAAO7B,WAAWy7B,MAAM,mBAC7C,OAAOnhB,GAAYA,EAAS,GAAKA,EAAS,GAAK,IAAI,EGsFhB2hB,GAE3BtU,MC/FqBuU,EAAC50B,EAAyC60B,EAAQ,UACjE,SAAVA,GACF70B,OAASvH,GAEG,YAAVo8B,GACF70B,EAASsgB,UAAUC,eAEP,SAAVsU,GACFvU,UAAUC,eACNuU,qBAAqB,CACrB,eACA,UACA,SACA,SACA,QACA,WACA,kBACA,gBACA,kBACA,UAEDttB,MAAMutB,IACL/0B,EAAS+0B,EAAG,IAEbttB,OAAM,KACLzH,GAAU,GAEhB,EDqEM40B,EAAwBI,IACtBlgB,GAAMtQ,QAAQ,SAASjL,MAAQy7B,CAAI,GAClClgB,GAAMK,YAAY5b,MAAMuU,eAC7B,IAIF6N,IAAO,MAEyC,IAA5C7G,GAAMK,YAAY5b,MAAM07B,sBACkBx8B,IAA1Cqc,GAAMI,UAAUzE,gBAAgBlX,OErGf27B,EAAChuB,EAA8BhD,KAOtD,MAAMixB,EAAU,IAAI1U,IAAI3L,GAAMI,UAAUzE,gBAAgBlX,OAClDuK,EAAM,GAAGqxB,EAAQxE,SAASwE,EAAQvE,mBAElCI,EAAa,IAAIhS,GAAW9X,EAAchD,GAChD8sB,EAAW1R,cAAcxK,GAAMI,UAAUpE,SAASvX,OAElDy3B,EAAW3R,aAAa,CACtBvb,MACA/D,QAAS,CAEPkY,OAAQ,OACR2F,QAAS,CACP,oBAAgBnlB,IAGpBymB,eAAe,EACflf,SAAUA,CAAC9D,EAAQijB,KAGjBrK,GAAMC,aAAajF,YAAYvW,WACVd,IAAnB0mB,GAASzX,OAAuByX,GAASf,KAAKgX,cAAgBtxB,CAAG,GAErE,EF0EIoxB,CAAiB59B,KAAK4P,aAAc5P,KAAK4M,OAC3C,GAEJ,CAKA6vB,0BAAAA,GACEjf,GAAMC,aAAarF,YAAYnW,M/B7EV87B,MACvB,MAAMC,EAA2Bn9B,OAAOqG,KAAKgiB,IAC7C,IAAI+U,GAAgB,EAGpB,IAAK,IAAI/5B,EAAI,EAAGA,EAAI85B,EAAyBl+B,OAAQoE,IAAK,CACxD,MAAMg6B,EACJhV,GAAgC8U,EAAyB95B,IAE3D,GAAIoB,EAAW44B,IAAwBA,IAAuB,CAC5DD,GAAgB,EAChB,KACF,CACF,CAEA,OAAOA,CAAa,E+B8DqBF,GACvC,MAAMI,EAAoB3gB,GAAMK,YAAY5b,MAAMm8B,YAClD,IAAIC,EAAchC,GACdv2B,EAAoBq4B,KAClBzK,GAAWyK,GACbE,EAAcF,EAEdn+B,KAAK4M,QAAQI,KrE6HgBsxB,EACnCpxB,EACAixB,IAEA,GAAGjxB,IAAUb,gCAAmD8xB,gEqEjIxCG,CAA6Bj1B,EAAsB80B,KASzE,GAJE3gB,GAAMK,YAAY5b,MAAM+T,oBACxBwH,GAAMC,aAAarF,YAAYnW,OAC/ByxB,GAAW2K,GAEW,CACtB,MAAME,EAA2BF,IAAgB7gB,GAAMK,YAAY5b,MAAMm8B,YACzE,GAAIG,EAA0B,CAG5B,MAAMC,EAAuB,uBAAuBhhB,GAAMI,UAAUpE,SAASvX,QAEvEw8B,EAAmBA,KACvBz+B,KAAKw8B,iBAGGx0B,WAAmBw2B,EAAqB,EAGjDx2B,WAAmBw2B,GAAwBC,EAE5CJ,EAAc,GAAGA,cAAwBG,GAC3C,CAEAx+B,KAAKwhB,kBAAkBzR,WAAW,CAChCvD,IAAK6xB,EACL9xB,GAAI+vB,GACJ3uB,OAAO,EACPD,QDnJsB,ICoJtBhF,SAAWg2B,IACJA,EAEOH,GACVv+B,KAAKw8B,UAFLx8B,KAAK6P,QAAQ,IAAI7J,MrEsDM24B,EAACD,EAAkBlyB,IACpD,+CAA+CkyB,eAAsBlyB,KqEvDpCmyB,CAA2BrC,GAAoB+B,IAGxE,GAGN,MACEr+B,KAAKw8B,SAET,CAKAE,qBAAAA,GACE10B,WAAWsZ,iBAAiB,WAAW,KACrC9D,GAAMC,aAAa1F,SAAS9V,OAAQ,CAAK,IAG3C+F,WAAWsZ,iBAAiB,UAAU,KACpC9D,GAAMC,aAAa1F,SAAS9V,OAAQ,CAAI,IAG1C+F,WAAWsZ,iBACT,SlF9IN,SAAkBsd,EAAyBC,EAAcC,EDpC5B,KCqC3B,IAAIC,EAEJ,MAAO,IAAI3e,KACRpY,WAA6BuH,aAAawvB,GAE3CA,EAAa/2B,WAA6BwH,YAAW,KACnDovB,EAAK7+B,MAAM8+B,EAASze,EAAK,GACxB0e,EAAM,CAEb,CkFqIME,EAAS,KACPxhB,GAAMtQ,QAAQkO,OAAOnZ,MAAQmpB,IAAkB,GAC9CprB,MAEP,CAMAw8B,OAAAA,GACEx8B,KAAK28B,4BACLnf,GAAMI,UAAUxE,OAAOnX,MAAQ,0BACjC,CAMA4N,OAAAA,CAAQO,GACN,IAAIpQ,KAAK4P,aAGP,MAAMQ,EAFNpQ,KAAK4P,aAAaC,QAAQO,EAAO/G,EAIrC,EG7MF,IAFA,IAA8B41B,GAAfC,GAAI,GAAIC,GAAI,EAEpBA,GAAM,IAAKA,KACjBD,GAAIC,KAAQA,GAAM,KAAK/9B,SAAS,IAAI8J,UAAU,YAG/Bk0B,KCRT,IAAgB9P,IDSjB2P,IAAYE,GAAM,GAPf,QCFc7P,EDEd,KAQP2P,GCTMvN,OAAOkL,gBAAgB,IAAI1R,WAAWoE,IDU5C6P,GAAM,GAIP,IADA,IAASE,EAALn7B,EAAE,EAAQo7B,EAAI,GACXp7B,EAAI,GAAIA,IACdm7B,EAAMJ,GAAOE,GAAMj7B,GACTo7B,GAAH,GAAHp7B,EAAag7B,GAAU,GAANG,EAAW,IACpB,GAAHn7B,EAAag7B,GAAU,GAANG,EAAW,KACzBH,GAAIG,GAER,EAAJn7B,GAASA,EAAI,GAAKA,EAAI,KAAIo7B,GAAO,KAItC,OADAH,IAAO,GACAG,CACR,CEzBA,IADA,IAAqBL,GAAjBE,GAAI,IAAKD,GAAI,GACVC,MAAOD,GAAIC,KAAQA,GAAM,KAAK/9B,SAAS,IAAI8J,UAAU,GCC5D,MCEMq0B,GAAeA,KDDlB35B,EAAkBoC,WAAW0pB,SAAWpsB,EAAW0C,WAAW0pB,OAAOkL,iBCG7D4C,gBFFV,IAASC,EAALv7B,EAAE,EAAQo7B,EAAI,GAElB,IAAKL,IAAYE,GAAM,GAAM,IAAM,CAElC,IADAF,GAASj8B,MAAMkB,EAAE,KACVA,KAAK+6B,GAAO/6B,GAAK,IAAM8a,KAAK0gB,SAAW,EAC9Cx7B,EAAIi7B,GAAM,CACX,CAEA,KAAOj7B,EAAI,GAAIA,IACdu7B,EAAMR,GAAOE,GAAMj7B,GACTo7B,GAAH,GAAHp7B,EAAag7B,GAAU,GAANO,EAAW,IACpB,GAAHv7B,EAAag7B,GAAU,GAANO,EAAW,KACzBP,GAAIO,GAER,EAAJv7B,GAASA,EAAI,GAAKA,EAAI,KAAIo7B,GAAO,KAItC,OADAH,KACOG,CACR,CEdSK,GCNHC,GAAqB,CAAC,eAAgB,cAAe,qBAIrDC,GAA4B,CAAC,UAAW,oBAAqB,YAAa,QAAS,UAGnFC,GAAoB,CACxB,KACA,eACA,UACA,UACA,YACA,cACA,qBACA,QACA,aACA,UACA,aACA,qBACA,oBACA,WACA,eCpBIC,GAAYN,GAAqC,iBAARA,IAAqBj+B,OAAO6nB,MAAMoW,GAe3EO,GAAqBP,GAAaM,GAASN,IAAQA,GAAO,GAAKj+B,OAAOC,UAAUg+B,GCChFQ,GAAqBC,IACzB,MAAMC,EAAYl9B,KAAKm9B,MACvB,OAAO76B,SAAS26B,GAAaC,EAAYD,EAAU,EAe/CG,GAAyBA,CAACC,EAAoB1zB,KAClD,SACG0zB,GACAN,GAAkBM,KD5BDC,ECEQ,GDFed,EC6BJa,ED7BoBb,EAAIr+B,WAAWtB,QAAUygC,MC+BlF3zB,GAAQI,K/EoIuBwzB,EACjCtzB,EACAozB,EACAG,IAEA,GAAGvzB,IAAUb,8BAAiDi0B,kEAA0EG,mE+ExIpID,CAA2B/2B,EAAsB62B,EA9BzB,MAgCd,GDlCKI,IAACH,EAAuBd,CCoChC,EA0BPkB,GAAgCA,CAACp0B,EAAaK,KAI3C,CACLL,GAJwB8zB,GAAuB9zB,EAAIK,GAChDL,EA/CiCtJ,KAAKm9B,MAmDzCQ,kBAAcz/B,EACd0/B,aAAa,IAIXC,GAAoCzO,GACxC9sB,QACE8sB,IAAgBhK,IACdgK,IAAgB/J,IAChB+J,IAAgB9J,IAChB8J,IAAgB7J,IAOhBuY,GAAsBA,IAAcxB,KC5DpCyB,GAA4BC,IAEhC,MAAMC,EAAezF,KAEf0F,EAA0B,CAAE,EAUlC,OATAtgC,OAAOqG,KAAKg6B,GAAct6B,SAAShE,IACjCu+B,EAAav+B,GAAOq+B,IAAYr+B,IAAQs+B,EAAat+B,EAAI,IAG3Du+B,EAAaC,iBACXH,GAAWG,kBAAoB5jB,GAAMS,QAAQ1G,gBAAgBtV,MAE/Dk/B,EAAaE,yBACXJ,GAAWI,0BAA4B7jB,GAAMS,QAAQzG,uBAAuBvV,MACvEk/B,CAAY,EA2CfG,GAAmCA,CACvC1gC,EACA2gC,EACA30B,KAEIrG,EAA0B3F,IAC5BC,OAAOqG,KAAKtG,GAAegG,SAAQ46B,KAE/B1B,GAAkBhzB,SAAS00B,IAC3B1B,GAAkBhzB,SAAS00B,EAASnuB,iBAEpCzG,GAAQI,KhFuCiBy0B,EAC/Bv0B,EACAs0B,EACAD,EACAG,IAEA,GAAGx0B,IAAUb,UAA6Bm1B,8BAAqCD,gHAA4HG,MgF5CnMD,CAAyBl4B,EAAei4B,EAAUD,EAAezB,IAErE,GAEJ,EA0FI6B,GAAiBA,CAACC,EAA0Bn5B,KAE5ClC,EAA0BkC,KArEIo5B,EAACD,EAA0Bn5B,KACzDA,EAAQ4O,aAAe5R,EAASgD,EAAQ4O,eAE1CuqB,EAAYvqB,YAAc5O,EAAQ4O,aAGhC9Q,EAA2CkC,EAAQwN,gBAErD2rB,EAAY3rB,aAAexN,EAAQwN,cAGjCxN,EAAQq5B,mBAAqBr8B,EAASgD,EAAQq5B,qBAEhDF,EAAYE,kBAAoBr5B,EAAQq5B,kBAC1C,EAwDED,CAA4BD,EAAan5B,GAEzCm5B,EAAY10B,QAjDS60B,EACvBC,EACAv5B,EACAmE,KAEA,IAAIM,EAAU80B,EAsBd,OArBAnhC,OAAOqG,KAAKuB,GAAS7B,SAAQhE,IAC3B,IAAKg9B,GAAmB9yB,SAASlK,KAASi9B,GAA0B/yB,SAASlK,GAC3E,GAAY,YAARA,EACFsK,EAAUpG,EAAeoG,EAAS,CAChCtK,CAACA,GAAM6F,EAAQ7F,cAEP+C,EAAY8C,EAAQ7F,KAAS2D,EAA0BkC,EAAQ7F,IAAO,CAChF,MAAMq/B,EAAmC,CAAE,EAC3CphC,OAAOqG,KAAKuB,EAAQ7F,IAA6BgE,SAAQa,IAClDo4B,GAA0B/yB,SAASrF,KACtCw6B,EAAYx6B,GAAMgB,EAAQ7F,GAA6B6E,GACzD,IAEFyF,EAAUpG,EAAeoG,EAAS,IAC7B+0B,GAEP,CAGF,IAEK/0B,CAAO,EAsBU60B,CAAiBH,EAAY10B,QAASzE,GAC9D,EAQIy5B,GAA8BhmB,IAClC,IAAIimB,EAWJ,OATEA,EA1BF3kB,GAAMK,YAAY5b,MAAMyU,sCACvBnQ,EAA0BiX,GAAME,SAAS5D,YAAY7X,OAAOgU,eAC3D1P,EAA0BiX,GAAMO,mBAAmBjC,qBAAqB7Z,QAwBtDmC,EAChBoZ,GAAME,SAAS5D,YAAY7X,OAAOgU,cAChCuH,GAAMO,mBAAmBjC,qBAAqB7Z,OAEzCsE,EAA0B2V,GACjBA,EAEAwZ,GAEbyM,CAAe,EAWlBC,GAAmBA,CACvBR,EACAn5B,EACAw4B,EACAr0B,KAEA,MAAMy1B,EAAkB,CACtBC,QHrPY,MGsPZp1B,QAAS,CACPlE,OAAQ5E,EAAMoZ,GAAMS,QAAQ7G,WAAWnV,OACvCq+B,UAAW9iB,GAAMS,QAAQxG,YAAYxV,MAAMsK,SAAMpL,EACjDy/B,aAAcpjB,GAAMS,QAAQxG,YAAYxV,MAAM2+B,mBAAgBz/B,KAE1Dqc,GAAME,SAAS/D,QAAQ1X,OAAS,CAClCo2B,kBAAmB,CACjBlC,iBAAkB/xB,EAAMoZ,GAAME,SAAS3S,KAAK9I,MAAMk0B,kBAClDD,kBAAmB9xB,EAAMoZ,GAAME,SAAS3S,KAAK9I,MAAMi0B,mBACnDlc,SAAUwD,GAAME,SAAS1D,SAAS/X,MAClC8X,mBAAoByD,GAAME,SAAS3D,mBAAmB9X,QAG1D,QAASub,GAAMtQ,QAAQ,SAASjL,MAChCwY,IAAK+C,GAAMtQ,QAAQuN,IAAIxY,MACvB2Y,QAAS4C,GAAMtQ,QAAQ0N,QAAQ3Y,MAC/B8Y,UAAWyC,GAAMtQ,QAAQ6N,UAAU9Y,MACnCiZ,GAAIsC,GAAMtQ,QAAQgO,GAAGjZ,MACrBkZ,OAAQqC,GAAMtQ,QAAQiO,OAAOlZ,MAC7BmZ,OAAQoC,GAAMtQ,QAAQkO,OAAOnZ,MAC7BsgC,SAAU9N,GAAqBzsB,WAAW4D,SAASyhB,MACnDmV,KAAMxB,GAAyBC,GAC/BvlB,SAAU8B,GAAMtQ,QAAQwO,SAASzZ,OAEnC6/B,mBCzQkB,IAAI7+B,MAAOw/B,cD0Q7BxsB,aAAcyf,GACdgN,UAAWnD,KACXx2B,OAAQ64B,EAAY74B,QAAUyU,GAAMS,QAAQlV,OAAO9G,OAIlD6+B,GAAiCtjB,GAAM3G,QAAQmG,QAAQ/a,MAAMoV,aAAapW,MAM3EohC,EAAgBhrB,YAAcmG,GAAMS,QAAQ5G,YAAYpV,MAHxDogC,EAAgBhrB,YAAc0pB,KAO5BvjB,GAAM3G,QAAQoG,uBAAuBhb,QACtCogC,EAAgBn1B,QAA0B+P,wBAAyB,GAG7C,aAArB2kB,EAAY3gC,OACbohC,EAAgBn1B,QAA0BlE,OACzCwU,GAAM3G,QAAQmG,QAAQ/a,MAAMmV,YAAYnW,OAASwnB,GAC7CrkB,EAAMoZ,GAAMS,QAAQ7G,WAAWnV,OAC9B2/B,EAAY10B,QAA0BlE,QAGtB,UAArB44B,EAAY3gC,QACV2gC,EAAYx4B,SAAWoU,GAAMS,QAAQ7U,QAAQnH,SAC/CogC,EAAgBj5B,QAAUw4B,EAAYx4B,SAAWoU,GAAMS,QAAQ7U,QAAQnH,QAGrE2/B,EAAY54B,QAAUwU,GAAMS,QAAQ3G,YAAYrV,SAClDogC,EAAgBr5B,OACdwU,GAAM3G,QAAQmG,QAAQ/a,MAAMqV,aAAarW,OAASwnB,GAC9CrkB,EAAMoZ,GAAMS,QAAQ3G,YAAYrV,OAChC2/B,EAAY54B,SAItB,MAAM25B,EAAiB77B,EAAe86B,EAAaS,GAiBnD,YAd6BlhC,IAAzBwhC,EAAe95B,QACjB85B,EAAe95B,MAAQ,WAGS1H,IAA9BwhC,EAAen6B,aACjBm6B,EAAen6B,WAAa,MAG9Bm5B,GAAegB,EAAgBl6B,GA3MAm6B,EAAChB,EAA0Bh1B,KAE1D,MAAMpE,WAAEA,EAAUQ,OAAEA,EAAMkE,QAAEA,GAAY00B,GAChC54B,OAAQ65B,GAAqB31B,EAErCo0B,GAAiC94B,EAAY,aAAcoE,GAC3D00B,GAAiCt4B,EAAQ,SAAU4D,GACnD00B,GAAiCuB,EAAkB,iBAAkBj2B,EAAO,EAqM5Eg2B,CAAyBD,EAAgB/1B,GAGzC+1B,EAAe1sB,aAAeisB,GAA2BS,EAAe1sB,cAEjE0sB,CAAc,EEjUvB,MAAMG,GAGJt9B,WAAAA,CAAYoH,GACV5M,KAAK4M,OAASA,CAChB,CASAm2B,iBAAAA,CACEz6B,EACAC,EACAC,EACAC,GAEA,IAAIu6B,EAAQx6B,GAAc,CAAE,EAE5Bw6B,EAAMz6B,KAAOA,EACby6B,EAAM16B,SAAWA,EAEjB06B,EF4B6BC,EAC/Bz6B,EACAC,KAEA,MAAMy6B,EAAqBz6B,GAAwB+5B,MAAsB,CAAA,EACnEvB,EAAYz4B,EAGZ04B,EAAezF,KAkBrB,OAhBA56B,OAAOqG,KAAKg6B,GAAct6B,SAAShE,IAC7B+C,EAAYs7B,EAAUr+B,MACxBq+B,EAAUr+B,GAAOsgC,EAAiBtgC,IAAQs+B,EAAat+B,GACzD,IAGE+C,EAAYs7B,EAAUG,oBACxBH,EAAUG,iBACR8B,EAAiB9B,kBAAoB5jB,GAAMS,QAAQ1G,gBAAgBtV,OAGnE0D,EAAYs7B,EAAUI,4BACxBJ,EAAUI,yBACR6B,EAAiB7B,0BAA4B7jB,GAAMS,QAAQzG,uBAAuBvV,OAG/Eg/B,CAAS,EEtDNgC,CAAyBD,EAAOv6B,GASxC,OAAO25B,GAPiC,CACtC55B,WAAYw6B,EACZz6B,OACAD,WACArH,KAAM,QAG2BwH,EAASu6B,EAAOhjC,KAAK4M,OAC1D,CAQAu2B,kBAAAA,CACEt6B,EACAL,EACAC,GAQA,OAAO25B,GANkC,CACvC55B,aACAK,QACA5H,KAAM,SAG4BwH,OAAStH,EAAWnB,KAAK4M,OAC/D,CAQAw2B,qBAAAA,CACEr6B,EACAC,EACAP,GAUA,OAAO25B,GARqC,CAC1Cr5B,SACA9H,KAAM,WACNiM,QAAS,CACPlE,WAImCP,OAAStH,EAAWnB,KAAK4M,OAClE,CAQAy2B,kBAAAA,CACEn6B,EACArB,EACAY,GAEA,MAKM66B,EAAgBlB,GALmB,CACvCmB,WAAY17B,EACZ5G,KAAM,SAG2CwH,OAAStH,EAAWnB,KAAK4M,QAG5E,OADA02B,EAAcv6B,OAASG,GAAMo6B,EAAcv6B,OACpCu6B,CACT,CAQAE,kBAAAA,CACEp6B,EACAJ,EACAP,GAEA,MAAMg7B,EAAmC,CACvCxiC,KAAM,SAWR,OARImI,IACFq6B,EAAWr6B,QAAUA,GAGnBJ,IACFy6B,EAAWz6B,OAASA,GAGfo5B,GAAiBqB,EAAYh7B,OAAStH,EAAWnB,KAAK4M,OAC/D,CAOA9J,MAAAA,CAAO+F,GACL,IAAI66B,EACJ,OAAQ76B,EAAM5H,MACZ,IAAK,OACHyiC,EAAW1jC,KAAK+iC,kBACdl6B,EAAMP,SACNO,EAAMN,KACNM,EAAML,WACNK,EAAMJ,SAER,MACF,IAAK,QACHi7B,EAAW1jC,KAAKmjC,mBAAmBt6B,EAAMN,KAAgBM,EAAML,WAAYK,EAAMJ,SACjF,MACF,IAAK,WACHi7B,EAAW1jC,KAAKojC,sBAAsBv6B,EAAME,OAAQF,EAAMG,OAAQH,EAAMJ,SACxE,MACF,IAAK,QACHi7B,EAAW1jC,KAAKqjC,mBAAmBx6B,EAAMK,GAAwBL,EAAMhB,KAAMgB,EAAMJ,SACnF,MACF,IAAK,QACHi7B,EAAW1jC,KAAKwjC,mBAAmB36B,EAAMO,QAASP,EAAMG,OAAQH,EAAMJ,SAM1E,OAAOi7B,CACT,EC1JF,MAAMC,GAcJn+B,WAAAA,CACEo+B,EACAC,EACAj0B,EACAhD,GAEA5M,KAAK4jC,gBAAkBA,EACvB5jC,KAAK6jC,mBAAqBA,EAC1B7jC,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK8jC,aAAe,IAAIhB,GAAmB9iC,KAAK4M,QAChD5M,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKAuhB,IAAAA,GACEvhB,KAAK4jC,gBAAgBriB,MACvB,CAEAwiB,MAAAA,GACE/jC,KAAK4jC,gBAAgBG,QACvB,CAMAC,QAAAA,CAASn7B,GACP7I,KAAK6jC,mBAAmBI,iBACxB,MAAMrC,EAAc5hC,KAAK8jC,aAAahhC,OAAO+F,GACzC+4B,EACF5hC,KAAK4jC,gBAAgB/xB,QAAQ+vB,EAAa/4B,EAAMH,UAEhD1I,KAAK6P,QAAQ,IAAI7J,MnF3Ce,wCmF6CpC,CAMA6J,OAAAA,CAAQO,EAAgBwR,EAAwBC,GAC9C,IAAI7hB,KAAK4P,aAGP,MAAMQ,EAFNpQ,KAAK4P,aAAaC,QAAQO,EAAO7G,EAAeqY,EAAeC,EAInE,ECJF,MAAMqiB,GAOJ1+B,WAAAA,CACEoK,EACAhD,EACA8jB,EACAyT,EACAzK,GAEA15B,KAAKmkC,aAAeA,EACpBnkC,KAAK0wB,eAAiBA,EACtB1wB,KAAK4M,OAASA,EACd5M,KAAK4P,aAAeA,EACpB5P,KAAK05B,WAAaA,EAClB15B,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKAuhB,IAAAA,GACEvhB,KAAKokC,yBAGLpkC,KAAKqkC,iBACP,CAEAD,sBAAAA,GACEpkC,KAAKskC,yBACLtkC,KAAKukC,iCAGLvkC,KAAKwkC,UAAUxkC,KAAKykC,aACpBzkC,KAAK0kC,cAAc1kC,KAAK2kC,iBACxB3kC,KAAK4kC,WAAW5kC,KAAK6kC,cACrB7kC,KAAK8kC,eAAe9kC,KAAK+kC,kBACzB,MAAMC,8BAAEA,EAA6BC,mBAAEA,GAAuBznB,GAAMK,YAAY5b,MAChF,IAAIijC,EAEFp/B,EAAoBk/B,IACqB,iBAAlCA,IAEPE,EAAsBllC,KAAKmlC,mCAAmCH,IAEhEhlC,KAAKolC,eAAeF,GAAuBllC,KAAKqlC,eAAeJ,IAC/DjlC,KAAKslC,aAAatlC,KAAKulC,gBACvBvlC,KAAKwlC,yBACLxlC,KAAKylC,0BACP,CAEAA,wBAAAA,GACE,IAAIhuB,EAAczX,KAAK0lC,iBACvB,GAAI1lC,KAAK2lC,oCAAoC,eAAgB,CAC3D,MAAMC,EAAgC5lC,KAAK6lC,mCACrCC,EAAqBruB,GAAeG,GAC1CH,EAAc,IACTquB,KACAF,EACH9vB,UACE8vB,EAA8B9vB,YAAgD,IAAnCgwB,EAAmBjF,YAEpE,CAEArjB,GAAMS,QAAQxG,YAAYxV,MAAQjC,KAAK2lC,oCAAoC,eACtEluB,EACDE,GAA4BF,YAG5B+F,GAAMS,QAAQxG,YAAYxV,MAAM6T,WAClC9V,KAAK+lC,yBAAyBvoB,GAAMS,QAAQxG,YAAYxV,MAE5D,CAEAujC,sBAAAA,GACE,MAAMQ,EAA2BhmC,KAAKimC,qBAChCC,EAAkClmC,KAAKmmC,4BAE7C,GAAIH,GAA4BE,EAC9BlmC,KAAKomC,mBAAmBJ,GACxBhmC,KAAKqmC,0BAA0BH,OAC1B,CACL,MAAM3uB,EAAkByuB,GAA4BxK,KACpDx7B,KAAKomC,mBAAmB7uB,GACxBvX,KAAKqmC,0BAA0B9R,GAAmBhd,GACpD,CACF,CAEAouB,mCAAAA,CAAoCW,GAClC,OAAOxF,GACLtjB,GAAM3G,QAAQmG,QAAQ/a,MAAMqkC,IAAYrlC,KAE5C,CAEAsjC,8BAAAA,GACE,MAAMvnB,EAAUQ,GAAM3G,QAAQmG,QAAQ/a,MAChCskC,EAA2B,CAACle,GAAgBC,GAAeC,IACjE1nB,OAAOqG,KAAK8V,GAASpW,SAAQysB,IAC3B,MAAMzwB,EAAMywB,EACNmT,EAAiBxpB,EAAQpa,IAAM3B,KAC/BwlC,EAAWzmC,KAAKmkC,cAAc1Q,SAClC3K,GAA8B0d,IAE5BC,GACFF,EAAyB3/B,SAAQiQ,IAC/B,MAAM6Y,EAAQ1vB,KAAKmkC,cAAc1Q,SAC/B3K,GAA8BjS,IAEhC,GAAI6Y,GAAS7Y,IAAY2vB,EAAgB,CACvC,MAAMvkC,EAAQytB,EAAM/sB,IAAIwU,GAA0BvU,IzGjInBX,IACzC6D,EAAoB7D,IAAoB,KAAVA,EyGiIhBykC,CAAkCzkC,IACpCwkC,EAAS5jC,IAAIsU,GAA0BvU,GAAMX,GAG/CytB,EAAMH,OAAOpY,GAA0BvU,GACzC,IAEJ,GAEJ,CAEA0hC,sBAAAA,GACE,IAAK9mB,GAAM3G,QAAQG,QAAQ/U,MACzB,OAGF,MAMM8vB,EAAmB,GANI,CAC3BrJ,GACAC,GACAC,IAImBhiB,SAAQ+/B,IAC3B,MAAMjX,EAAQ1vB,KAAKmkC,cAAc1Q,SAASkT,GACtCjX,GACFqC,EAAOhuB,KAAK2rB,EACd,IAGF7uB,OAAOqG,KAAKiQ,IAA2BvQ,SAAQgsB,IAC7C,MAAMgU,EAAezvB,GAA0Byb,GAC/Cb,EAAOnrB,SAAQ8oB,IACb,MAAMmX,EAAc7mC,KAAK0wB,gBAAgB7P,aACvC,kBACA+lB,EACAlX,EAAM1L,OACNhkB,KAAK4P,aACL5P,KAAK4M,QAMFhH,EAAkBihC,IACrBnX,EAAM7sB,IAAI+jC,EAAcC,EAC1B,GACA,GAEN,CAEAhB,gCAAAA,GACE,IASIn4B,EATAoI,GAA4D,IAAhD0H,GAAMK,YAAY5b,MAAM4T,UAAUC,UAGlD,IAAKA,EACH,MAAO,CACLA,aAKJ,MAAMgxB,EAA2BtpB,GAAMK,YAAY5b,MAAM4T,UAAUnI,QAyBnE,OAxBKsyB,GAAkB8G,GAUrBp5B,EAAUo5B,GATV9mC,KAAK4M,QAAQI,KpF3FgB+5B,EACjC75B,EACAQ,EACAs5B,IAEA,GAAG95B,IAAUb,gCAAmDqB,8CAAoDs5B,6BoFuF9GD,CACEt9B,EACAq9B,EACAx8B,KAGJoD,EAAUpD,IAKI,IAAZoD,IACF1N,KAAK4M,QAAQI,KpFhGjB,GoFgG2CvD,IpFhG9B4C,6LoFiGTyJ,GAAY,GAIVpI,EAAU,GAAKA,ElGzQQ,KkG0QzB1N,KAAK4M,QAAQI,KpFpGqBi6B,EACtC/5B,EACAQ,EACAw5B,IAEA,GAAGh6B,IAAUb,+BAAkDqB,gDAAsDw5B,oGoFgG/GD,CAAgCx9B,EAAsBiE,ElG3Q/B,MkG8QpB,CAAEA,UAASoI,YACpB,CAMAjG,OAAAA,CAAQO,EAAgBwR,GACtB,IAAI5hB,KAAK4P,aAGP,MAAMQ,EAFNpQ,KAAK4P,aAAaC,QAAQO,EAAO3G,EAAsBmY,EAI3D,CAQAulB,sBAAAA,CAAuBC,EAA0B1X,GAC/C,MAAM2X,EAA6C,GAYnD,OAXAD,EAAWxgC,SAAQa,IACjB,MAAM6/B,EAAiB5X,GAAO2B,QAC5BlkB,GAAyB1F,EAAExF,OAAO,EAAO,GAAIjC,KAAK4M,SAEhD9G,EAAoBwhC,IACtBD,EAAoBtjC,KAAK,CACvBwE,KAAMd,EAAEc,KACRtG,MAAOqlC,GAEX,IAEKD,CACT,CAOAE,sBAAAA,CACEF,EACA3+B,GAEA1I,KAAK05B,YAAY3R,aAAa,CAC5Bvb,IAAKgR,GAAMU,cAAcd,eAAenb,MACxCwG,QAAS,CACPkY,OAAQ,OACR5V,KAAMoC,GAAyB,CAC7Bq6B,QAAS,aACTzM,YAAavd,GAAMpa,OAAOnB,OAAO84B,YACjChwB,KAAM,CACJtC,QAAS,CACPg/B,OAAQjqB,GAAM3G,QAAQkG,OAAO9a,OAAO4qB,OACpC1nB,KAAMqY,GAAM3G,QAAQkG,OAAO9a,OAAOkD,KAClC4nB,OAAQvP,GAAM3G,QAAQkG,OAAO9a,OAAO8qB,OACpC2a,SAAUlqB,GAAM3G,QAAQkG,OAAO9a,OAAOgrB,SACtCC,OAAQ1P,GAAM3G,QAAQkG,OAAO9a,OAAOirB,OACpCJ,QAAStP,GAAM3G,QAAQkG,OAAO9a,OAAO6qB,SAEvC6a,QAASN,KAGbxgB,aAAa,EACbQ,iBAAiB,GAEnBO,eAAe,EACflf,YAEJ,CAOAk/B,mBAAAA,CAAoBR,EAA0BS,EAAuBnY,GACnE,IAEE,MAAM2X,EAAsBrnC,KAAKmnC,uBAAuBC,EAAY1X,GAChE2X,EAAoBvnC,OAAS,GAE/BE,KAAKunC,uBAAuBF,GAAqB,CAAC1Q,EAAK9O,KpFpQvBzO,MoFqQD,MAAzByO,GAASf,KAAK1N,OAChBguB,EAAWxgC,SAAQkhC,IACjB,MAAMC,EAAcrY,GAAO/sB,IAAImlC,EAAKv/B,MAC9By/B,EAAS76B,GAAyB26B,EAAK7lC,OAAO,EAAO,IACrDgmC,EAAQ96B,GAAyB46B,GAAa,EAAO,IACvDE,IAAUD,IACZhoC,KAAK4M,QAAQuG,MAAM,mCAAoC60B,GACvDhoC,KAAK4M,QAAQuG,MAAM,oCAAqC80B,GACxDjoC,KAAK4M,QAAQwD,MpF1Q3B,gCoF0QyE03B,EAAKv/B,oEAC5Ds/B,GACFA,EAAGC,EAAKv/B,KAAMu/B,EAAK7lC,OAEvB,KAGFjC,KAAK4M,QAAQwD,OpFpRegJ,EoFoRsByO,GAASf,KAAK1N,OpFnR1E,oCAAoCA,qFoFoR1BguB,EAAWxgC,SAAQkhC,IACbD,GACFA,EAAGC,EAAKv/B,KAAMu/B,EAAK7lC,MACrB,IAEJ,GAGN,CAAE,MAAOwF,GACPzH,KAAK6P,QAAQpI,EpF1RoC,oGoF2RjD2/B,EAAWxgC,SAAQkhC,IACbD,GACFA,EAAGC,EAAKv/B,KAAMu/B,EAAK7lC,MACrB,GAEJ,CACF,CAOAimC,kBAAAA,CACEvV,EACA1wB,GAEA,MAAM+a,EAAUQ,GAAM3G,QAAQmG,QAAQ/a,MAChCowB,EAAcrV,EAAQ2V,IAAa1xB,KACzC,GAAI6/B,GAAiCzO,GAAc,CACjD,MAAMoU,EAAWzmC,KAAKmkC,cAAc1Q,SAClC3K,GAA8BuJ,IAE1BzvB,EAAMoa,EAAQ2V,IAAa/vB,IAC7BX,IAAUwD,EAASxD,IAAUgF,EAAiBhF,IAI9Cub,GAAMU,cAAcf,2BAA2Blb,OAC/CowB,IAAgBhK,GAEhBroB,KAAK4nC,oBACH,CAAC,CAAEr/B,KAAM3F,EAAKX,WACd,CAACkmC,EAAYJ,KACXtB,GAAU5jC,IAAIslC,EAAYJ,EAAY,GAExCtB,GAGFA,GAAU5jC,IAAID,EAAKX,GAGrBwkC,GAAUlX,OAAO3sB,EAErB,CACF,CAKAyhC,eAAAA,GAEExb,GAAkBjiB,SAAQ+rB,IACxBtO,IAAO,KACLrkB,KAAKkoC,mBAAmBvV,EAAYnV,GAAMS,QAAQ0U,GAAY1wB,MAAM,GACpE,GAEN,CAUAmjC,cAAAA,CAAe/tB,EAAsB+wB,GACnC,IAAIC,EAA8ChxB,EAClD,GAAIrX,KAAK2lC,oCAAoC,eAAgB,CAC3D,IAAK0C,GAAoBD,EAAsB,CAC7C,MAAME,EAAsBtoC,KAAK0wB,gBAAgB7P,aAC/C,sCACAunB,GAEFC,EAAmBC,CACrB,CACAD,EAAmBA,GAAoBtH,IACzC,MACEsH,EAAmB1wB,GAA4BN,YAGjDmG,GAAMS,QAAQ5G,YAAYpV,MAAQomC,CACpC,CAOAhD,cAAAA,CAAe58B,GACb,MAAMoO,EAAuB2G,GAAM3G,QAAQmG,QAAQ/a,MAAMoV,aAAapW,KAEtE,GAAI6/B,GAAiCjqB,GAAU,CAC7C,IAAI0xB,EAAuBvoC,KAAKwoC,cAAc,eAC9C,IAAKD,GAAwB9/B,EAAS,CAEpC,MAAMggC,EAA0BzoC,KAAK0wB,gBAAgB7P,aACnD,yBACAoP,GACAxnB,GAEF8/B,EAAuBE,CACzB,CACAjrB,GAAMS,QAAQ5G,YAAYpV,MAAQsmC,GAAwBxH,IAC5D,CACA,OAAOvjB,GAAMS,QAAQ5G,YAAYpV,KACnC,CAEAumC,aAAAA,CAAc7V,GACZ,MAAM3V,EAAUQ,GAAM3G,QAAQmG,QAAQ/a,MAChCowB,EAAcrV,EAAQ2V,IAAa1xB,KACzC,GAAI6/B,GAAiCzO,GAAc,CACjD,MAAM3C,EAAQ1vB,KAAKmkC,cAAc1Q,SAC/B3K,GAA8BuJ,IAE1BO,EAAa5V,EAAQ2V,IAAa/vB,IACxC,OAAO8sB,GAAO/sB,IAAIiwB,IAAe,IACnC,CACA,OAAO,IACT,CAEAuS,kCAAAA,CAAmCviC,GACjC,MAAM4wB,EAAgBvD,GAAiB5H,IACvC,OAAImL,GAAerF,UACVqF,EAAcvH,QAAQrpB,IAAQ,KAEhC,IACT,CAMA6hC,SAAAA,GACE,OAAOzkC,KAAKwoC,cAAc,SAC5B,CAMA7D,aAAAA,GACE,OAAO3kC,KAAKwoC,cAAc,aAC5B,CAMA3D,UAAAA,GACE,OAAO7kC,KAAKwoC,cAAc,UAC5B,CAMAzD,cAAAA,GACE,OAAO/kC,KAAKwoC,cAAc,cAC5B,CAMAvC,kBAAAA,GACE,OAAOjmC,KAAKwoC,cAAc,kBAC5B,CAMArC,yBAAAA,GACE,OAAOnmC,KAAKwoC,cAAc,yBAC5B,CAMA9C,cAAAA,GACE,OAAO1lC,KAAKwoC,cAAc,cAC5B,CAMAjD,YAAAA,GACE,OAAOvlC,KAAKwoC,cAAc,YAC5B,CAMAE,YAAAA,GACE,MAAMjxB,EAAczX,KAAK0lC,kBAAoB/tB,GAA4BF,YACzE,OACGA,EAAY3B,YAAcmqB,GAAkBxoB,EAAYyoB,YACzDzoB,EAAYopB,YAELppB,EAAYlL,IAAM,KAEhB,IACb,CAMA03B,cAAAA,GACE,IAAIxsB,EAAczX,KAAK0lC,kBAAoB/tB,GAA4BF,aACnEA,EAAY3B,WAAa2B,EAAYopB,eACnCppB,EAAY3B,YACd9V,KAAK+lC,yBAAyBtuB,GAC9BA,EAAc+F,GAAMS,QAAQxG,YAAYxV,YAUTd,IAA7BsW,EAAYmpB,aACdnpB,EAAc,IACTA,EACHmpB,cAAc,GAEPnpB,EAAYmpB,eACrBnpB,EAAc,IACTA,EACHmpB,cAAc,KAMpBpjB,GAAMS,QAAQxG,YAAYxV,MAAQwV,EAEG,kBAAjC+F,GAAMI,UAAUxE,OAAOnX,OAGzBjC,KAAKkoC,mBAAmB,cAAezwB,EAE3C,CAQAkxB,KAAAA,CAAMC,EAA4BC,GAChC,MAAM5qB,QAAEA,GAAYT,IACdqjB,YAAEA,EAAW/qB,UAAEA,GAAcmI,EAAQxG,YAAYxV,MAEvDqiB,IAAM,KACJrG,EAAQlV,OAAO9G,MAAQ0V,GAA4B5O,OACnDkV,EAAQ7G,WAAWnV,MAAQ0V,GAA4BP,WACvD6G,EAAQ7U,QAAQnH,MAAQ0V,GAA4BvO,QACpD6U,EAAQ3G,YAAYrV,MAAQ0V,GAA4BL,YACxD2G,EAAQvG,UAAUzV,MAAQ0V,GAA4BD,UAElDkxB,GAEF5oC,KAAKolC,iBAGHyD,IAIA/yB,GACFmI,EAAQxG,YAAYxV,MAAQ0V,GAA4BF,YACxDzX,KAAK+lC,yBAAyB9nB,EAAQxG,YAAYxV,QACzC4+B,GACT7gC,KAAK8oC,8BACP,GAEJ,CAMAtE,SAAAA,CAAUz7B,GACRyU,GAAMS,QAAQlV,OAAO9G,MACnBjC,KAAK2lC,oCAAoC,WAAa58B,EAClDA,EACA4O,GAA4B5O,MACpC,CAMA27B,aAAAA,CAAc17B,GACZwU,GAAMS,QAAQ7G,WAAWnV,MACvBjC,KAAK2lC,oCAAoC,eAAiB38B,EACtDlC,EACE0W,GAAMS,QAAQ7G,WAAWnV,OAAS0V,GAA4BP,WAC9DpO,GAEF2O,GAA4BP,UACpC,CAMAwtB,UAAAA,CAAWx7B,GACToU,GAAMS,QAAQ7U,QAAQnH,MACpBjC,KAAK2lC,oCAAoC,YAAcv8B,EACnDA,EACAuO,GAA4BvO,OACpC,CAMA07B,cAAAA,CAAe97B,GACbwU,GAAMS,QAAQ3G,YAAYrV,MACxBjC,KAAK2lC,oCAAoC,gBAAkB38B,EACvDlC,EACE0W,GAAMS,QAAQ3G,YAAYrV,OAAS0V,GAA4BL,YAC/DtO,GAEF2O,GAA4BL,WACpC,CAMA8uB,kBAAAA,CAAmB5R,GACjBhX,GAAMS,QAAQ1G,gBAAgBtV,MAC5BjC,KAAK2lC,oCAAoC,oBAAsBnR,EAC3DA,EACA7c,GAA4BJ,eACpC,CAMA8uB,yBAAAA,CAA0B0C,GACxBvrB,GAAMS,QAAQzG,uBAAuBvV,MACnCjC,KAAK2lC,oCAAoC,2BAA6BoD,EAClEA,EACApxB,GAA4BH,sBACpC,CAKAuuB,wBAAAA,CAAyBtuB,GACvB,GAAIwoB,GAAkBxoB,EAAYyoB,WAChC1iB,GAAMS,QAAQxG,YAAYxV,MLlrBK+mC,KACnC,MAAM7I,EAAYl9B,KAAKm9B,MACjB1yB,EAAkBs7B,GAAkB1+B,GAC1C,MAAO,CACLiC,GAAI4zB,EACJD,UAAWC,EAAYzyB,EACvBA,UACAkzB,kBAAcz/B,EACd2U,WAAW,EACZ,EKyqBqCmzB,CAA4BxxB,EAAY/J,aACrE,CACL,MAAMyyB,EAAYl9B,KAAKm9B,MACjB1yB,EAAU+J,EAAY/J,QAC5B8P,GAAMS,QAAQxG,YAAYxV,MAAQ6E,EAAe2Q,EAAa,CAC5DyoB,UAAWC,EAAYzyB,GAE3B,CACF,CAOAw7B,KAAAA,CAAM38B,GACJiR,GAAMS,QAAQxG,YAAYxV,MAAQ0+B,GAA8Bp0B,EAAIvM,KAAK4M,OAC3E,CAKAk8B,2BAAAA,GACE9oC,KAAKkpC,MAAMjmC,KAAKm9B,MAClB,CAKA+I,GAAAA,GACE3rB,GAAMS,QAAQxG,YAAYxV,MAAQ0V,GAA4BF,WAChE,CAMA6tB,YAAAA,CAAa8D,GACX5rB,GAAMS,QAAQvG,UAAUzV,MACtBjC,KAAK2lC,oCAAoC,cAAgByD,EACrDA,EACAzxB,GAA4BD,SACpC,EChxBF,MAAM2xB,GAA2C,CAC/C,cACA,UACA,uBACA,yBACA,2BACA,iBACA,sBACA,eACA,sBACA,yBACA,yBACA,oBACA,0BACA,kBACA,YCpBIC,GAAoC,uBACpCC,GAAsC,0BC8BtCC,GAAgBA,CAAC3gC,EAAoB2U,KACzC,MAAMisB,EAAarlC,EAAMyE,GAGnB6gC,EAAkB7gC,EAAMoN,cAAgByf,GACxCiU,EAAyBnsB,EAAMO,mBAAmB7B,mBAAmBja,MACrE2nC,EAxBgCC,EACtCH,EACAC,IAEA9oC,OAAOqG,KAAKwiC,GACT/pB,QAAOmqB,IAA0C,IAA9BJ,EAAgBI,KAAuBH,EAAuBG,KACjFC,QAAO,CAACnpC,EAAsBgC,KAC7B,MAAM0E,EAASlD,EAAMxD,GAErB,OADA0G,EAAO1E,GAAO8mC,EAAgB9mC,GACvB0E,CAAM,GACZ,CAAA,GAcsBuiC,CACzBH,EACAC,GAIF,OADAF,EAAWxzB,aAAenP,EAAe6iC,EAAwBC,GAC1DH,CAAU,ECVnB,MAAMO,GAiBJxkC,WAAAA,CACEkrB,EACAyT,EACAv0B,EACAhD,GAEA5M,KAAK0wB,eAAiBA,EACtB1wB,KAAK4P,aAAeA,EACpB5P,KAAK4M,OAASA,EACd5M,KAAK05B,WAAa,IAAIhS,GAAW9X,EAAchD,GAC/C5M,KAAKmkC,aAAeA,EACpBnkC,KAAK6P,QAAU7P,KAAK6P,QAAQC,KAAK9P,KACnC,CAKAuhB,IAAAA,GACE,IACEvhB,KAAKiqC,qBAAuBjqC,KAAK0wB,eAAe7P,aAC9C,GAAGyoB,UACH9rB,GACAxd,KAAK05B,WACL15B,KAAKmkC,aACLnkC,KAAK4P,aACL5P,KAAK4M,OAET,CAAE,MAAOnF,GACPzH,KAAK6P,QAAQpI,ExF8HuB,uCwF7HtC,CAEA,IACEzH,KAAKkqC,eAAiBlqC,KAAK0wB,eAAe7P,aACxC,sBACArD,GACAxd,KAAK0wB,eACL1wB,KAAK05B,WACL15B,KAAKmkC,aACLnkC,KAAK4P,aACL5P,KAAK4M,OAET,CAAE,MAAOnF,GACPzH,KAAK6P,QAAQpI,ExFiHiB,uDwFhHhC,CAEA,IACEzH,KAAKmqC,wBAA0BnqC,KAAK0wB,eAAe7P,aACjD,GAAG0oB,UACH/rB,GACAxd,KAAK0wB,eACL1wB,KAAKmkC,aACLnkC,KAAKkqC,eACLlqC,KAAK4P,aACL5P,KAAK4M,OAET,CAAE,MAAOnF,GACPzH,KAAK6P,QAAQpI,ExFiGyB,qDwFhGxC,CAGA4c,IAAO,MAC0D,IAA3D7G,GAAMO,mBAAmB9B,wBAAwBha,QACnDjC,KAAKmqC,yBAAyBjB,QAC9BlpC,KAAKkqC,gBAAgBhB,QACvB,IAGF,MAAMkB,EDvE+B5sB,IACvCA,EAAME,SAAS7D,WAAW5X,MAAM0X,SACqB,WAArD6D,EAAME,SAAS7D,WAAW5X,MAAM02B,QAAQC,WACe,YAAtDpb,EAAME,SAAS7D,WAAW5X,MAAM4U,SAASoc,UACc,SAAtDzV,EAAME,SAAS7D,WAAW5X,MAAM4U,SAASoc,UCmEPoX,CAAgC7sB,IAMlE,IAAIuhB,EACJ1a,IAAO,KACL,MAAMimB,GACwD,IAA5D9sB,GAAMK,YAAY5b,MAAM0U,kCACmC,IAA3D6G,GAAMO,mBAAmB9B,wBAAwBha,OAO5B,IALCub,GAAMO,mBAAmBlC,mBAAmB5Z,MAAM2d,MACvE2qB,IAAsBC,O9DnHE1nB,E8DmHsBynB,E9DlHrDhlC,QACwC,WAAtCud,EAAY9S,OAAO+S,iBACyB,IAA1CD,EAAY9S,OAAOgT,oBAHQF,K8DoH3B,MAGyD,IAAzBwnB,GAC7BF,IACoD,IAArDpqC,KAAKiqC,sBAAsBQ,wBAE1BziC,WAA6BuH,aAAawvB,GAC3C/+B,KAAKiqC,sBAAsBf,QAC7B,KAI8D,IAA5D1rB,GAAMK,YAAY5b,MAAM0U,kCAC1BooB,EAAa/2B,WAA6BwH,YAAW,MACM,IAArDxP,KAAKiqC,sBAAsBQ,uBAC7BzqC,KAAKiqC,sBAAsBf,OAC7B,GACC1rB,GAAMK,YAAY5b,MAAM2U,8BAE/B,CAEAmtB,MAAAA,IAC2D,IAArD/jC,KAAKiqC,sBAAsBQ,wBACzBjtB,GAAME,SAAS5D,YAAY7X,MAAMyoC,0BACnC1qC,KAAKiqC,sBAAsB/3B,QAC3BlS,KAAKmqC,yBAAyBj4B,SAGhClS,KAAKiqC,sBAAsBf,QAE/B,CAOAr3B,OAAAA,CAAQhJ,EAAoBH,GAC1B,IAAIiiC,EACJ,IACEA,EAAWnB,GAAc3gC,EAAO2U,IAChCxd,KAAK0wB,eAAe7P,aAClB,GAAGyoB,aACH9rB,GACAxd,KAAKiqC,qBACLU,EACA3qC,KAAK4P,aACL5P,KAAK4M,OAET,CAAE,MAAOnF,GACPzH,KAAK6P,QAAQpI,ExF0BoB,sCwFzBnC,CAEA,IACE,MAAMmjC,EAAUxmC,EAAMyE,GACtB7I,KAAK0wB,eAAe7P,aAClB,GAAG0oB,aACH/rB,GACAxd,KAAKmqC,wBACLS,EACA5qC,KAAK4P,aACL5P,KAAK4M,OAET,CAAE,MAAOnF,GACPzH,KAAK6P,QAAQpI,ExFWsB,oDwFVrC,CAGA,IAGEiB,IAAWiiC,EACb,CAAE,MAAOv6B,GACPpQ,KAAK6P,QAAQO,ExFHe,iCwFI9B,CACF,CAQAP,OAAAA,CAAQO,EAAgBwR,EAAwBC,GAC9C,IAAI7hB,KAAK4P,aAGP,MAAMQ,EAFNpQ,KAAK4P,aAAaC,QAAQO,EzG/MP,kByG+MgCwR,EAAeC,EAItE,EC7NF,MAAMgpB,GAAoBhiC,IACxB,MAAMiiC,EAAc,IAAIhgB,YAAYjiB,EAAO,CACzCkiC,OAAQ,CAAE9+B,kBAAoBjE,WAA6BgjC,iBAC3DC,SAAS,EACTC,YAAY,EACZC,UAAU,IAGXnjC,WAA6BgG,SAASo9B,cAAcN,EAAY,EC8DnE,MAAMO,GAmBJ7lC,WAAAA,GACExF,KAAKsrC,cAAgB,IAAI35B,GACzB3R,KAAKqZ,aAAc,EACnBrZ,KAAK4P,aAAeqR,GACpBjhB,KAAK4M,OAAS+F,GACd3S,KAAKwhB,kBAAoB,IAAI9R,GAAkB1P,KAAK4P,aAAc5P,KAAK4M,QACvE5M,KAAKurC,oBAAsB,IAAIhP,GAAoBv8B,KAAK4P,aAAc5P,KAAK4M,QAC3E5M,KAAK05B,WAAatR,EACpB,CAKAojB,IAAAA,CACEhyB,EACAC,EACAoE,EAAoC,CAAE,GAEtC,GAAIL,GAAMI,UAAUxE,OAAOnX,MACzB,OAGF,IAAIwpC,EAAqBrnC,EAAMqV,GAC3BiyB,EAAoBtnC,EAAMyZ,GAG1BxX,EAAmBoT,KACrBiyB,EAAoBjyB,EACpBgyB,OAAqBtqC,GAIlBuD,IAAC,KACJ8Y,GAAMI,UAAUpE,SAASvX,MAAQuX,EACjCgE,GAAMI,UAAUnE,aAAaxX,MAAQwpC,EACrCjuB,GAAMK,YAAY5b,MC7GK0pC,EAC3BC,EACA/tB,KAGA,MAAMguB,EAAqBznC,EAAMyZ,GAuGjC,OArGKpY,EAASomC,EAAmB3Z,yBACxB2Z,EAAmB3Z,gBAGC,CAAC,SAAU,MAAO,QACrBplB,SAAS++B,EAAmB91B,wBAC7C81B,EAAmB91B,eAG5B81B,EAAmB5Z,cAAmD,IAApC4Z,EAAmB5Z,aAE7B,CAAC,OAAQ,UAAW,QACvBnlB,SAAS++B,EAAmBr1B,wBACxCq1B,EAAmBr1B,eAGvBjQ,EAA0BslC,EAAmB51B,sBACzC41B,EAAmB51B,aAG5B41B,EAAmBp1B,QAAUo1B,EAAmBp1B,SAAW4yB,GAE3DwC,EAAmBn1B,qCAC0C,IAA3Dm1B,EAAmBn1B,oCAErBm1B,EAAmBl1B,iCACsC,IAAvDk1B,EAAmBl1B,gCAErBk1B,EAAmBlO,iBAAyD,IAAvCkO,EAAmBlO,gBAExDkO,EAAmB30B,sBAAmE,IAA5C20B,EAAmB30B,qBAG3D20B,EAAmBtU,qBAC+B,iBAA3CsU,EAAmBtU,4BAEnBsU,EAAmBtU,oBAGvBhxB,EAA0BslC,EAAmB50B,gCACzC40B,EAAmB50B,uBAGvBpR,EAAUgmC,EAAmBj2B,iBAGhCi2B,EAAmBj2B,iBAAyD,IAAvCi2B,EAAmBj2B,uBAFjDi2B,EAAmBj2B,gBAKvBrP,EAA0BslC,EAAmBh1B,UAGhDg1B,EAAmBh1B,QAAUzP,EAA6BykC,EAAmBh1B,SAC5Eg1B,EAAmBh1B,QAAwBG,SACF,IAAxC60B,EAAmBh1B,SAASG,gBAJvB60B,EAAmBh1B,QAOvBtQ,EAA0BslC,EAAmBz1B,oBAGhDy1B,EAAmBz1B,mBAAqBhP,EACtCykC,EAAmBz1B,2BAHdy1B,EAAmBz1B,mBAOvB7P,EAA0BslC,EAAmBx1B,0BAGhDw1B,EAAmBx1B,yBAA2BjP,EAC5CykC,EAAmBx1B,iCAHdw1B,EAAmBx1B,yBAOvB9P,EAA0BslC,EAAmBv1B,cAGhDu1B,EAAmBv1B,aAAelP,EAA6BykC,EAAmBv1B,qBAF3Eu1B,EAAmBv1B,aAK5Bu1B,EAAmBt1B,yBAAyE,IAA/Cs1B,EAAmBt1B,wBAE3DwpB,GAAS8L,EAAmBj1B,sCACxBi1B,EAAmBj1B,6BAGvBrQ,EAA0BslC,EAAmBh1B,SAASkG,QAGxD8uB,EAAmBh1B,QAAwBkG,OAAS3V,EACnDykC,EAAmBh1B,SAASkG,eAHvB8uB,EAAmBh1B,SAASkG,OAOhCxW,EAA0BslC,EAAmBhyB,YAGhDgyB,EAAmBhyB,WAAazS,EAA6BykC,EAAmBhyB,mBAFzEgyB,EAAmBhyB,WAKW/S,EAAe8kC,EAAsBC,EAEpD,EDCMF,CAAqBnuB,GAAMK,YAAY5b,MAAOypC,GACxEluB,GAAMI,UAAUxE,OAAOnX,MAAQ,SAAS,IAItCub,GAAMK,YAAY5b,MAAMyR,UAC1B1T,KAAK4M,QAAQ6G,eAAe+J,GAAMK,YAAY5b,MAAMyR,UAItDhJ,GAAiB,QAAS8S,GAAOhE,GAKjCxZ,KAAK8rC,gBACP,CAMAA,cAAAA,GACEznB,IAAO,KACL,IACE,OAAQ7G,GAAMI,UAAUxE,OAAOnX,OAC7B,IAAK,UACHjC,KAAK+rC,YACL,MACF,IAAK,2BACH/rC,KAAKgsC,6BACL,MACF,IAAK,aACHhsC,KAAKisC,eACL,MACF,IAAK,iBAWL,IAAK,sBAQL,IAAK,gBACL,QACE,MAnBF,IAAK,eACHjsC,KAAKksC,iBACL,MACF,IAAK,cACHlsC,KAAKmsC,gBACL,MACF,IAAK,SACHnsC,KAAKosC,WACL,MAGF,IAAK,oBACHpsC,KAAKqsC,sBACL,MACF,IAAK,QACHrsC,KAAKw8B,UAMX,CAAE,MAAOpvB,GACP,MAAME,EAAQ,yBACdtN,KAAK4P,aAAaC,QAAQxC,GAAgBD,EAAKE,GAAQxD,EACzD,IAEJ,CAEAkiC,0BAAAA,GAEE7gC,GAA4BnL,MAC5BA,KAAKssC,0BACLtsC,KAAKusC,YACP,CAEAH,QAAAA,GACEpsC,KAAKwsC,yBAE2C,IAA5ChvB,GAAME,SAAS7D,WAAW5X,MAAM0X,QAClC6D,GAAMI,UAAUxE,OAAOnX,MAAQ,QAE/BjC,KAAKysC,kBAET,CAKAV,SAAAA,GACE/rC,KAAKurC,oBAAoBhqB,MAC3B,CAKAxV,0BAAAA,CAA2B2gC,GACrB1pC,MAAMsD,QAAQomC,IAChBA,EAAe9lC,SAAQ+lC,GAAiB3sC,KAAKsrC,cAAcz5B,QAAQzN,EAAMuoC,KAE7E,CAKAC,0BAAAA,GACE,KAAO5sC,KAAKsrC,cAAcr5B,OAAS,GAAG,CACpC,MAAM46B,EAAiB7sC,KAAKsrC,cAAcv5B,UAEtC86B,GACF7gC,GAA4B,IAAI6gC,GAAiB7sC,KAErD,CACF,CAEAssC,uBAAAA,GACEtsC,KAAK0wB,eAAiB,IAAI3M,GAAe3F,GAAqBpe,KAAK4P,aAAc5P,KAAK4M,QACtF5M,KAAKmkC,aAAe,IAAIrS,GAAa9xB,KAAK0wB,eAAgB1wB,KAAK4P,aAAc5P,KAAK4M,QAClF5M,KAAK8sC,cAAgB,IAAIrT,GAAcz5B,KAAK05B,WAAY15B,KAAK4P,aAAc5P,KAAK4M,QAChF5M,KAAK6jC,mBAAqB,IAAIK,GAC5BlkC,KAAK4P,aACL5P,KAAK4M,OACL5M,KAAK0wB,eACL1wB,KAAKmkC,aACLnkC,KAAK05B,YAEP15B,KAAK4jC,gBAAkB,IAAIoG,GACzBhqC,KAAK0wB,eACL1wB,KAAKmkC,aACLnkC,KAAK4P,aACL5P,KAAK4M,QAEP5M,KAAK+sC,aAAe,IAAIpJ,GACtB3jC,KAAK4jC,gBACL5jC,KAAK6jC,mBACL7jC,KAAK4P,aACL5P,KAAK4M,OAET,CAKA2/B,UAAAA,GACM/uB,GAAMI,UAAUpE,SAASvX,OAC3BjC,KAAK05B,WAAW1R,cAAcxK,GAAMI,UAAUpE,SAASvX,OAGzDjC,KAAK8sC,eAAevrB,MACtB,CAKA2qB,cAAAA,GACElsC,KAAK4P,aAAa2R,KAAKvhB,KAAKwhB,mBAE5BxhB,KAAKmkC,cAAc5iB,OACnBvhB,KAAK6jC,oBAAoBtiB,OAGrB/D,GAAME,SAAS/D,QAAQ1X,QAAUub,GAAME,SAASrE,YAAYpX,QAC9DjC,KAAK0wB,gBAAgB7P,aAAa,sBAAuBrD,GAAOxd,KAAK4M,SAErB,IAA5C4Q,GAAME,SAAS7D,WAAW5X,MAAM0X,SAClC3Z,KAAK0wB,gBAAgB7P,aACnB,oCACArD,GACAxd,KAAKmkC,aACLnkC,KAAK4M,SAMX5M,KAAK+sC,cAAcxrB,OAGnB/D,GAAMI,UAAUxE,OAAOnX,MAAQ,aACjC,CAKAgqC,YAAAA,GACEjsC,KAAK0wB,gBAAgBnP,MAGvB,CAKA4qB,aAAAA,GAEEnsC,KAAK4sC,6BAKDtnC,EAAWkY,GAAMK,YAAY5b,MAAMmqC,WACrC5uB,GAAMK,YAAY5b,MAAMmqC,SAAUpkC,WAA6BgjC,iBAIjE1mB,IAAM,KACJ9G,GAAMI,UAAUtE,OAAOrX,OAAQ,EAC/Bub,GAAMI,UAAUxE,OAAOnX,MAAQ,QAAQ,IAGzCjC,KAAKqZ,aAAc,EAGnBwxB,GAAiB,kBACnB,CAMArO,OAAAA,GACEhf,GAAMI,UAAUxE,OAAOnX,MAAQ,gBAC/Bub,GAAMG,YAAYtB,oBAAoBpa,MAAM2E,SAAS8B,IACnD,IACEA,GACF,CAAE,MAAO0E,GACPpN,KAAK4P,aAAaC,QAAQzC,EAAKtD,EAAgBqK,GACjD,KAIF02B,GAAiB,YACnB,CAKA2B,qBAAAA,GAIE,IAAIE,EAAiBlvB,GAAMG,YAAYvB,mBAAmBna,MAC1D,KAAOyqC,EAAe5sC,OAAS,GAAG,CAChC,MAAM6sC,EAAgBD,EAAevgC,QAGrC,GAFAqR,GAAMG,YAAYvB,mBAAmBna,MAAQyqC,EAEzCC,EAAe,CACjB,MAAMzgC,EAAaygC,EAAc,GAC7BrnC,EAAYtF,KAAakM,KAE1BlM,KAAakM,MAAeygC,EAActrC,MAAM,IAAI,EAEzD,CAEAqrC,EAAiBlvB,GAAMG,YAAYvB,mBAAmBna,KACxD,CACF,CAKAwqC,gBAAAA,GACE,GAAIjvB,GAAMO,mBAAmB9B,wBAAwBha,MACnD,OAIFjC,KAAK0wB,gBAAgB7P,aACnB,2CACArD,GACAxd,KAAK0wB,eACL1wB,KAAK4P,aACL5P,KAAK4M,QAGP,MAAMogC,EAA0BxvB,GAAMO,mBAAmBlC,mBAAmB5Z,MAAMnC,OAClD,IAA5BktC,GAMJxvB,GAAMI,UAAUxE,OAAOnX,MAAQ,sBAC/BjC,KAAK0wB,gBAAgB7P,aACnB,0BACArD,GACAxd,KAAKwhB,kBACLxhB,KAAK4P,aACL5P,KAAK4M,QAIPyX,IAAO,MAEyB,IAA5B2oB,GACAxvB,GAAMO,mBAAmB/B,wBAAwB/Z,MAAMnC,OACrD0d,GAAMO,mBAAmBhC,mBAAmB9Z,MAAMnC,SAClDktC,IAGF1oB,IAAM,KACJ9G,GAAMI,UAAUxE,OAAOnX,MAAQ,oBAC/Bub,GAAMO,mBAAmB9B,wBAAwBha,OAAQ,CAAI,GAEjE,KA3BAub,GAAMI,UAAUxE,OAAOnX,MAAQ,mBA6BnC,CAMAoqC,mBAAAA,GAIuC,UAAjC7uB,GAAMI,UAAUxE,OAAOnX,QACzBub,GAAMI,UAAUxE,OAAOnX,MAAQ,QAEnC,CAIAsa,KAAAA,CAAM7T,EAAuBukC,GAAuB,GAClD,MAAMhsC,EAAO,QAEb,GAAKuc,GAAMI,UAAUtE,OAAOrX,MAU5B,GAFAjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,gBAEpCqE,EAAWoD,GAUhB,GAAqC,kBAAjC8U,GAAMI,UAAUxE,OAAOnX,MACzB,IACEyG,GACF,CAAE,MAAO0E,GACPpN,KAAK4P,aAAaC,QAAQzC,EAAKtD,EAAgBqK,GACjD,MAEAqJ,GAAMG,YAAYtB,oBAAoBpa,MAAQ,IACzCub,GAAMG,YAAYtB,oBAAoBpa,MACzCyG,QAlBF1I,KAAK4M,OAAOwD,M1F/XhB,GjBlEgB,aiBkEH/D,0C0FqXTmR,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAMyH,GA6Bb,CAEA85B,IAAAA,CAAK75B,EAA0BskC,GAAuB,GACpD,MAAMhsC,EAAO,OAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,WACzCuc,GAAMM,QAAQvD,UAAUtY,OAAS,EAEjCjC,KAAK+sC,cAAc/I,SAAS,CAC1B/iC,KAAM,OACNqH,SAAUK,EAAQL,SAClBC,KAAMI,EAAQJ,KACdC,WAAYG,EAAQH,WACpBC,QAASE,EAAQF,QACjBC,SAAUC,EAAQD,YAQuB,IAAzC8U,GAAMC,aAAajF,YAAYvW,OAC/B0G,EAAQL,WAAa2B,IAErBjK,KAAKwiC,KACHn6B,EACE4B,G1G3fgB,wB0G6fhB,CAGE9E,K1G/fc,e0GigBhBqY,GAAMK,YAAY5b,MAAMgV,0BApC5BuG,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAM0H,GAsCb,CAEAukC,KAAAA,CAAMvkC,EAA2BskC,GAAuB,GACtD,MAAMhsC,EAAO,QAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,WACzCuc,GAAMM,QAAQvD,UAAUtY,OAAS,EAEjCjC,KAAK+sC,cAAc/I,SAAS,CAC1B/iC,OACAsH,KAAMI,EAAQJ,WAAQpH,EACtBqH,WAAYG,EAAQH,WACpBC,QAASE,EAAQF,QACjBC,SAAUC,EAAQD,YAflB8U,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAM0H,GAeb,CAEAwkC,QAAAA,CAASxkC,EAA8BskC,GAAuB,GAC5D,MAAMhsC,EAAO,WAEb,IAAKuc,GAAMI,UAAUtE,OAAOrX,MAK1B,YAJAub,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAM0H,KAKX3I,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,WACzCuc,GAAMM,QAAQvD,UAAUtY,OAAS,EAENsD,QACzBoD,EAAQI,QAAUyU,GAAMS,QAAQlV,OAAO9G,OAAS0G,EAAQI,SAAWyU,GAAMS,QAAQlV,OAAO9G,QAIxFjC,KAAK2oC,QAIFjjC,EAAOiD,EAAQI,SAClB/I,KAAK6jC,oBAAoBW,UAAU77B,EAAQI,QAE7C/I,KAAK6jC,oBAAoBa,cAAc/7B,EAAQK,QAE/ChJ,KAAK+sC,cAAc/I,SAAS,CAC1B/iC,OACA8H,OAAQJ,EAAQI,OAChBC,OAAQL,EAAQK,OAChBP,QAASE,EAAQF,QACjBC,SAAUC,EAAQD,UAEtB,CAEA0kC,KAAAA,CAAMzkC,EAA2BskC,GAAuB,GACtD,MAAMhsC,EAAO,QAEb,IAAKuc,GAAMI,UAAUtE,OAAOrX,MAK1B,YAJAub,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAM0H,KAKX3I,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,WACzCuc,GAAMM,QAAQvD,UAAUtY,OAAS,EAEjC,MAAMshC,EACJ56B,EAAQd,MACR7H,KAAK6jC,oBAAoBY,aACzBzkC,KAAK6jC,oBAAoBwB,iBAE3BrlC,KAAK+sC,cAAc/I,SAAS,CAC1B/iC,OACAiI,GAAIP,EAAQO,GACZrB,KAAM07B,EACN96B,QAASE,EAAQF,QACjBC,SAAUC,EAAQD,UAEtB,CAEAuc,KAAAA,CAAMtc,EAA2BskC,GAAuB,GACtD,MAAMhsC,EAAO,QAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,WACzCuc,GAAMM,QAAQvD,UAAUtY,OAAS,EAG5ByD,EAAOiD,EAAQS,UAClBpJ,KAAK6jC,oBAAoBe,WAAWj8B,EAAQS,SAG9CpJ,KAAK6jC,oBAAoBiB,eAAen8B,EAAQK,QAEhDhJ,KAAK+sC,cAAc/I,SAAS,CAC1B/iC,OACAmI,QAAST,EAAQS,QACjBJ,OAAQL,EAAQK,OAChBP,QAASE,EAAQF,QACjBC,SAAUC,EAAQD,YAtBlB8U,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAM0H,GAsBb,CAEAggC,KAAAA,CAAMC,EAA4BqE,GAAuB,GACvD,MAAMhsC,EAAO,QAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAChB,OAAOzhB,mCAAsC2nC,KAE/C5oC,KAAK6jC,oBAAoB8E,MAAMC,IAV7BprB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAM2nC,GASb,CAEAvD,cAAAA,CAAe58B,GACb,OAAWzI,KAAC6jC,oBAAoBwB,eAAe58B,EACjD,CAEA28B,cAAAA,CACE/tB,EACA+wB,EACA6E,GAAuB,GAEvB,MAAMhsC,EAAO,iBAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,gBACzCjB,KAAK6jC,oBAAoBuB,eAAe/tB,EAAa+wB,IARnD5qB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAMoW,EAAa+wB,GAO1B,CAGA3D,SAAAA,GACE,OAAOjnB,GAAMS,QAAQlV,OAAO9G,KAC9B,CAGA0iC,aAAAA,GACE,OAAOnnB,GAAMS,QAAQ7G,WAAWnV,KAClC,CAGA4iC,UAAAA,GACE,OAAOrnB,GAAMS,QAAQ7U,QAAQnH,KAC/B,CAGA8iC,cAAAA,GACE,OAAOvnB,GAAMS,QAAQ3G,YAAYrV,KACnC,CAEAorC,YAAAA,CAAa/M,EAAoB2M,GAAuB,GACtD,MAAMhsC,EAAO,eAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,gBACzCjB,KAAK6jC,oBAAoBqF,MAAM5I,IAR7B9iB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,EAAMq/B,GAOb,CAEAgN,UAAAA,CAAWL,GAAuB,GAChC,MAAMhsC,EAAO,aAERuc,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,OAAOzhB,gBACzCjB,KAAK6jC,oBAAoBsF,OARvB3rB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAChB,GAOP,CAGAynC,YAAAA,GACE,MAAMpI,EAAYtgC,KAAK6jC,oBAAoB6E,eAC3C,OAAOpI,GAAa,IACtB,CAEAiN,OAAAA,CAAQ9kC,EAA0BwkC,GAAuB,GAGlDzvB,GAAMI,UAAUtE,OAAOrX,OAQ5BjC,KAAK4P,aAAa8S,gBAAgB,0BAElC4B,IAAM,KACJ9G,GAAME,SAAS7D,WAAW5X,MAAQ,IAAKub,GAAME,SAAS7D,WAAW5X,MAAO0X,SAAS,GACjF6D,GAAME,SAAS5D,YAAY7X,M/B7rBGwG,KAClC,MAAM+kC,EAA+B,CACnCC,eAAe,EACfC,cAAc,EACdhD,yBAAyB,GAE3B,GAAInkC,EAA0BkC,GAAU,CACtC,MAAMklC,EAAgBvpC,EAAMqE,GAE5B+kC,EAAa32B,QAAU82B,EAAc92B,QACjChR,EAAU8nC,EAAc13B,gBAC1Bu3B,EAAav3B,aAAe1P,EAA0BonC,EAAc13B,cAChE03B,EAAc13B,aACdyf,IAEN8X,EAAa9C,yBAAoE,IAA1CiD,EAAcjD,wBACrD8C,EAAaC,eAAgD,IAAhCE,EAAcF,cAC3CD,EAAaE,cAA8C,IAA/BC,EAAcD,aACtCzmC,EAAiB0mC,EAActV,qBAEjCmV,EAAanV,kBAAoBvxB,EAAe6mC,EAActV,kBAAmB,CAC/E1e,QAAS6D,GAAME,SAAS/D,QAAQ1X,QAGtC,CACA,OAAOurC,CAAY,E+BoqBoBI,CAA2BnlC,GAE9D,MAAM4Q,YAAEA,EAAW+c,aAAEA,GAAiBH,GACpCzY,GAAME,SAAS5D,YAAY7X,MAAMo2B,kBACjCr4B,KAAK4M,QAGP4Q,GAAME,SAASrE,YAAYpX,MAAQoX,GAAemE,GAAME,SAASrE,YAAYpX,MAC7Eub,GAAME,SAAS3S,KAAK9I,MAAQm0B,CAAY,IAItC5Y,GAAME,SAAS/D,QAAQ1X,QAAUub,GAAME,SAASrE,YAAYpX,OAC9DjC,KAAK0wB,gBAAgB7P,aACnB,oCACArD,GACAxd,KAAKmkC,aACLnkC,KAAK4M,QAKT5M,KAAKmkC,cAAc/R,yBAGnBpyB,KAAK6jC,oBAAoBO,yBAGzBpkC,KAAK+sC,cAAchJ,SAEnB/jC,KAAKysC,mBAELzsC,KAAK6tC,mBAAmBZ,IA3CtBzvB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CALS,UAKFwG,GA0Cb,CAEAolC,kBAAAA,CAAmBZ,GAGjB,GAAIzvB,GAAME,SAAS5D,YAAY7X,MAAMyrC,aAAc,CACjD,MAAMI,EAAellC,E1GhxBM,kC0GixBvBqkC,EACFzvB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAC,QAAS6rC,IAGZ9tC,KAAKktC,MAAMY,EAEf,CAEA,GAAItwB,GAAME,SAAS5D,YAAY7X,MAAMwrC,cAAe,CAClD,MAAMM,EAAc1lC,IAChB4kC,EACFzvB,GAAMG,YAAYvB,mBAAmBna,MAAQ,IACxCub,GAAMG,YAAYvB,mBAAmBna,MACxC,CAAC,OAAQ8rC,IAGX/tC,KAAKwiC,KAAKuL,EAEd,CACF,CAEAzI,YAAAA,CAAa8D,GACXppC,KAAK6jC,oBAAoByB,aAAa8D,EACxC,EE1wBF,MAAM4E,GACJ/f,uBAAoD,KACpDggB,mBAAiD,CAAE,EACnDC,oBAAsB,GACtBthC,OAAS+F,GAGTnN,WAAAA,GACE,GAAIwoC,GAAgB5f,gBAGlB,OAAO4f,GAAgB5f,gBAGzBnN,GAAoBI,uBAEpBrhB,KAAKmuC,sBAAwBnuC,KAAKmuC,sBAAsBr+B,KAAK9P,MAC7DA,KAAKouC,qBAAuBpuC,KAAKouC,qBAAqBt+B,KAAK9P,MAC3DA,KAAKwrC,KAAOxrC,KAAKwrC,KAAK17B,KAAK9P,MAC3BA,KAAKuc,MAAQvc,KAAKuc,MAAMzM,KAAK9P,MAC7BA,KAAKquC,yBAA2BruC,KAAKquC,yBAAyBv+B,KAAK9P,MACnEA,KAAKwiC,KAAOxiC,KAAKwiC,KAAK1yB,KAAK9P,MAC3BA,KAAKktC,MAAQltC,KAAKktC,MAAMp9B,KAAK9P,MAC7BA,KAAKmtC,SAAWntC,KAAKmtC,SAASr9B,KAAK9P,MACnCA,KAAKotC,MAAQptC,KAAKotC,MAAMt9B,KAAK9P,MAC7BA,KAAKilB,MAAQjlB,KAAKilB,MAAMnV,KAAK9P,MAC7BA,KAAK2oC,MAAQ3oC,KAAK2oC,MAAM74B,KAAK9P,MAC7BA,KAAKqlC,eAAiBrlC,KAAKqlC,eAAev1B,KAAK9P,MAC/CA,KAAKolC,eAAiBplC,KAAKolC,eAAet1B,KAAK9P,MAC/CA,KAAKykC,UAAYzkC,KAAKykC,UAAU30B,KAAK9P,MACrCA,KAAK2kC,cAAgB3kC,KAAK2kC,cAAc70B,KAAK9P,MAC7CA,KAAK6kC,WAAa7kC,KAAK6kC,WAAW/0B,KAAK9P,MACvCA,KAAK+kC,eAAiB/kC,KAAK+kC,eAAej1B,KAAK9P,MAC/CA,KAAKqtC,aAAertC,KAAKqtC,aAAav9B,KAAK9P,MAC3CA,KAAKstC,WAAattC,KAAKstC,WAAWx9B,KAAK9P,MACvCA,KAAK0oC,aAAe1oC,KAAK0oC,aAAa54B,KAAK9P,MAC3CA,KAAKslC,aAAetlC,KAAKslC,aAAax1B,KAAK9P,MAC3CA,KAAKutC,QAAUvtC,KAAKutC,QAAQz9B,KAAK9P,MAEjCguC,GAAgB5f,gBAAkBpuB,KAGlCA,KAAKquC,2BAIJrmC,WAA6BgjC,gBAAkBhrC,IAClD,CAOAmuC,qBAAAA,CAAsB30B,GAChBA,IACFxZ,KAAKkuC,oBAAsB10B,EAE/B,CAKA40B,oBAAAA,CAAqB50B,GACnB,MAAM80B,EAAa90B,GAAYxZ,KAAKkuC,oBAQpC,OANgC3oC,QAAQvF,KAAKiuC,mBAAmBK,MAG9DtuC,KAAKiuC,mBAAmBK,GAAc,IAAIjD,IAGrCrrC,KAAKiuC,mBAAmBK,EACjC,CAKA9C,IAAAA,CAAKhyB,EAAkBC,EAAsBoE,GACtCpY,EAAS+T,GAKVxZ,KAAKiuC,mBAAmBz0B,KAI5BxZ,KAAKmuC,sBAAsB30B,GAC3BxZ,KAAKiuC,mBAAmBz0B,GAAY,IAAI6xB,GACxCrrC,KAAKouC,qBAAqB50B,GAAUgyB,KAAKhyB,EAAUC,EAAcoE,IAV/D7d,KAAK4M,OAAOwD,M5FwEmBm+B,EAACrhC,EAAiBsM,IACrD,GAAGtM,IAAUb,oBAAuCmN,gF4FzE9B+0B,CAA6B1kC,EAAQ2P,GAW3D,CAMA60B,wBAAAA,GACE,MAAMhjC,EAAuBrI,MAAMsD,QAAS0B,WAA6BgjC,iBACnEhjC,WAA6BgjC,gBAC9B,GxGhCmC3/B,KAC1C,MAAMmjC,EAAoB,UACpBC,EAAgBpjC,EAAqBsU,QACzCgtB,GAAiBA,EAAc,KAAO6B,IAGlCE,EAAmBrjC,EAAqBsU,QAC5CgtB,GAAiBA,EAAc,KAAO6B,IAKxCnjC,EAAqB6T,OACnB,EACA7T,EAAqBvL,UAClB2uC,KACAC,EACJ,EwGkBCC,CAAmCtjC,GAKnC,MAAMujC,ExGpEqBvjC,KAE7B,IAAIujC,EAAgC,GAKhC1qC,EAAI,EACR,KAAOA,EAAImH,EAAqBvL,QAAQ,CACtC,GACEuL,EAAqBnH,IATF,SAUlBmH,EAAqBnH,GAA0B,GAChD,CACA0qC,EAAYxqC,EAAMiH,EAAqBnH,IACvCmH,EAAqB6T,OAAOhb,EAAG,GAC/B,KACF,CACAA,GAAK,CACP,CAEA,OAAO0qC,CAAS,EwGgDwBC,CAAsBxjC,GAG5DX,GAAiBR,GAAuB9F,EAAMiH,IAG1CujC,EAAU9uC,OAAS,IAErB8uC,EAAUziC,QAGVnM,KAAKwrC,KAAKzrC,MAAM,KAAM6uC,GAE1B,CAKAryB,KAAAA,CAAM7T,GACJ1I,KAAKouC,uBAAuB7xB,MAAM7T,EACpC,CAKA85B,IAAAA,CACEl6B,EACAC,EACAC,EACAC,EACAC,GAEA1I,KAAKouC,uBAAuB5L,KAC1Bn6B,EAA2BC,EAAUC,EAAMC,EAAYC,EAASC,GAEpE,CAKAwkC,KAAAA,CACErkC,EACAL,EACAC,EACAC,GAEA1I,KAAKouC,uBAAuBlB,MAC1BtkC,EAA4BC,EAAOL,EAAYC,EAASC,GAE5D,CAKAykC,QAAAA,CACEpkC,EACAC,EACAP,EACAC,GAEA1I,KAAKouC,uBAAuBjB,SAC1BrkC,EAA+BC,EAAQC,EAAQP,EAASC,GAE5D,CAKA0kC,KAAAA,CACElkC,EACArB,EACAY,EACAC,GAEA1I,KAAKouC,uBAAuBhB,MAAMnkC,EAA4BC,EAAIrB,EAAMY,EAASC,GACnF,CAKAuc,KAAAA,CACE7b,EACAJ,EACAP,EACAC,GAEyB,IAArB7I,UAAUC,OAKdE,KAAKouC,uBAAuBnpB,MAC1B9b,EAA4BC,EAASJ,EAAQP,EAASC,IALtD1I,KAAK4M,OAAOwD,M5FvChB,G4FuC6CvG,I5FvChCwC,kE4F8Cb,CAEAs8B,KAAAA,CAAMC,GACJ5oC,KAAKouC,uBAAuBzF,MAAMC,EACpC,CAEAvD,cAAAA,CAAe58B,GACb,OAAOzI,KAAKouC,uBAAuB/I,eAAe58B,EACpD,CAEA28B,cAAAA,CAAe/tB,EAAsB+wB,GACnCpoC,KAAKouC,uBAAuBhJ,eAAe/tB,EAAa+wB,EAC1D,CAEA3D,SAAAA,GACE,OAAOzkC,KAAKouC,uBAAuB3J,WACrC,CAEAE,aAAAA,GACE,OAAW3kC,KAACouC,uBAAuBzJ,eACrC,CAEAE,UAAAA,GACE,OAAW7kC,KAACouC,uBAAuBvJ,YACrC,CAEAE,cAAAA,GACE,OAAO/kC,KAAKouC,uBAAuBrJ,gBACrC,CAEAsI,YAAAA,CAAa/M,GACX,OAAWtgC,KAACouC,uBAAuBf,aAAa/M,EAClD,CAEAgN,UAAAA,GACE,OAAWttC,KAACouC,uBAAuBd,YACrC,CAEA5E,YAAAA,GACE,OAAW1oC,KAACouC,uBAAuB1F,cACrC,CAEApD,YAAAA,CAAa8D,GACX,OAAWppC,KAACouC,uBAAuB9I,aAAa8D,EAClD,CAEAmE,OAAAA,CAAQ9kC,GACN,OAAOzI,KAAKouC,uBAAuBb,QAAQ9kC,EAC7C,EC/RF,MAAM0lC,sBACJA,GAAqBC,qBACrBA,GAAoB5C,KACpBA,GAAIjvB,MACJA,GAAKimB,KACLA,GAAI0K,MACJA,GAAKC,SACLA,GAAQC,MACRA,GAAKnoB,MACLA,GAAK0jB,MACLA,GAAKtD,eACLA,GAAcD,eACdA,GAAcX,UACdA,GAASE,cACTA,GAAaE,WACbA,GAAUE,eACVA,GAAcsI,aACdA,GAAYC,WACZA,GAAU5E,aACVA,GAAY6E,QACZA,GAAOjI,aACPA,IACE,IAAI0I", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 37, 87, 113, 114, 115]}