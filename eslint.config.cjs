const eslintPluginVue = require("eslint-plugin-vue");
const eslint = require("@eslint/js");
const eslintConfigPrettier = require("eslint-config-prettier");
const typescriptEslint = require("typescript-eslint");
const globals = require("globals");
const {includeIgnoreFile} = require("@eslint/compat");
const {globalIgnores} = require("eslint/config");
const path = require("path");

module.exports = typescriptEslint.config(
    includeIgnoreFile(path.join(__dirname, ".gitignore")),
    globalIgnores([
        "**/*.min.js",
        "public/vendor/",
        "public/openvidu/",
        "public/project/",
        "public/rsa/",
        "public/swagger/",
    ]),
    eslint.configs.recommended,
    ...typescriptEslint.configs.recommended,
    ...eslintPluginVue.configs["flat/vue2-essential"],
    {
        files: ["**/*.{ts,vue,js}"],
        languageOptions: {
            ecmaVersion: "latest",
            sourceType: "module",
            globals: globals.browser,
            parserOptions: {
                parser: typescriptEslint.parser,
            },
        },
        rules: {
            "no-undef": "off",
            "vue/no-mutating-props": "off",
            "no-useless-escape": "off",
        },
    },
    {
        files: ["*.{ts,vue,js,cjs}"],
        languageOptions: {
            globals: globals.node,
        },
        rules: {
            '@typescript-eslint/no-require-imports': 'off',
        }
    },
    eslintConfigPrettier,
);
