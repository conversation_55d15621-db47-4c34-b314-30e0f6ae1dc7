<template>
    <form
        :class="formClass"
        @submit.prevent="handleSubmit"
        ref="form$"
    >

        <FormLanguageSelector
            v-if="multilingual"
            @changeLanguage="setLanguage"
        />

        <FormWizard
            v-if="hasWizard"
            :steps="wizard"
            :elements$="elements$"
            @submit="handleSubmit"
            ref="wizard$"
        />

        <FormErrors
            v-if="formErrors && errors.length"
            :errors="errors"
        />

        <FormTabs
            v-if="hasTabs"
            :tabs="tabs"
            :elements$="elements$"
            ref="tabs$"
        />

        <FormElements
            :schema="schema"
            :wizard$="wizard$"
            :tabs$="tabs$"
            ref="elements$"
        />

        <FormWizardControls
            v-if="hasWizard && wizardControls"
            :wizard$="wizard$"
        />

        <FormButtons
            v-if="!hasWizard"
            :buttons="buttons"
        />
    </form>
</template>

<script lang="ts">
import Vue from 'vue';
import Laraform from "@laraform/laraform/src/components/Laraform.vue";

export default {
    mixins: [Laraform],
}
</script>
