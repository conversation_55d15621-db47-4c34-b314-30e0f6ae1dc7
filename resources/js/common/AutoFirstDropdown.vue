<script lang="ts">
import Vue from 'vue'
import {BDropdown, BDropdownItem} from "bootstrap-vue";

export default Vue.extend({
    name: "AutoFirstDropdown",
    components: {
        BDropdown,
        BDropdownItem,
    },
    props: {
        name: {
            type: String,
            required: true,
        },
        value: {
            type: null as () => number | string | null,
            required: true,
        },
        options: {
            type: Array,
            required: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        variant: {
            type: String,
            default: 'white',
        },
        dropdownAttrs: {
            type: Object,
            default: () => ({}),
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.checkValueAndOptions(this.options);
        });
    },
    computed: {
        selectedOption(): BSelectOption | null {
            const selectedOption = this.options.find((option: BSelectOption) => option.value === this.value);
            return selectedOption || null;
        }
    },
    methods: {
        select(newValue: number | string | null) {
            this.$emit('input', newValue);
        },
        checkValueAndOptions(options: BSelectOption[]) {
            const newOptionValues = options.map((op: BSelectOption) => op.value);
            if (newOptionValues.length && (!this.value || !newOptionValues.includes(this.value))) {
                this.select(newOptionValues[0]);
            }
        }
    },
    watch: {
        options(newOptions: BSelectOption[]) {
            this.checkValueAndOptions(newOptions);
        }
    }
})
</script>

<template>
    <b-dropdown
        :class="[`field-${name}`, dropdownAttrs?.class || '']"
        :variant="variant"
        :disabled="disabled"
        no-caret
        v-bind="dropdownAttrs"
    >
        <template v-slot:button-content>
            <template v-if="selectedOption">
                <span class="one-line-ellipsis">
                    {{ selectedOption.text }}
                </span>
                <i class="tdi td-chevron-down ml-1"></i>
            </template>
        </template>
        <div class="max-h-30vh overflow-auto">
            <b-dropdown-item
                v-for="option in options"
                :key="option.value"
                @click="select(option.value)"
                link-class="d-flex align-items-baseline"
            >
                {{ option.text }}
                <i
                    class="tdi td-check ml-auto"
                    v-if="value === option.value"
                ></i>
            </b-dropdown-item>
        </div>
    </b-dropdown>
<!--    <b-form-select-->
<!--        v-bind:value="value"-->
<!--        v-on:input="select"-->
<!--        :options="options"-->
<!--        :disabled="disabled"-->
<!--    ></b-form-select>-->
</template>

<style scoped>

</style>
