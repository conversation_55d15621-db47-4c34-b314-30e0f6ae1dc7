<template>
    <component :is="theme.components.BaseElementLayout"
               v-if="!embed"
               :el$="el$"
    >
        <template slot="field">
            <slot name="prefix"></slot>

            <!-- Upload button -->
            <a
                v-if="uploader == 'button' && displayUploader"
                href=""
                :class="theme.classes.uploaderButton"
                @click.prevent="handleClick"
            >{{ __('elements.file.uploadButton') }}</a>

            <!-- Drag n drop area -->
            <component :is="theme.components.DragAndDrop"
                       v-if="uploader == 'drop' && displayUploader"
                       :class="theme.classes.uploaderDragndrop"
                       :draggingClass="theme.classes.uploaderDragndropOver"
                       :title="__('elements.file.dndTitle')"
                       :description="__('elements.file.dndDescription')"
                       @click="handleClick"
                       @drop="handleFileDropped"
            />

            <!-- Actual input field -->
            <input
                type="file"
                :disabled="disabled"
                @change="handleFileSelected"
                ref="input"
            />

            <!-- File preview -->
            <component :is="theme.components.FilePreview"
                       v-if="file"
                       :el$="el$"
                       @remove="handleRemove"
            >
                <slot slot="remove" name="remove"></slot>
            </component>

            <slot name="suffix"></slot>
        </template>

        <slot slot="label" name="label" :el$="el$"></slot>
        <slot slot="before" name="before" :el$="el$"></slot>
        <slot slot="between" name="between" :el$="el$"></slot>
        <slot slot="error" name="error" :el$="el$"></slot>
        <slot slot="after" name="after" :el$="el$"></slot>
    </component>

    <!-- Standalone file preview -->
    <component :is="theme.components.FilePreview"
               v-else
               :el$="el$"
               @remove="handleRemove"
    >
        <slot slot="remove" name="remove"></slot>
    </component>
</template>

<script>
import FileElement from '@laraform/laraform/src/components/elements/FileElement.vue';
import {first, isNil, isPlainObject, isString, last, split} from "lodash";
import {reportToSentry} from "../config";
import {defineComponent} from "vue";

export default defineComponent({
    mixins: [FileElement],
    methods: {
        /**
         * Inits file if the element has any.
         *
         * @private
         * @returns {void}
         */
        $_initFile() {
            if (!this.value) {
                return
            }

            const value = this.value;
            if (value instanceof Blob) {
                // value is a JS File
                this.set(value)
            } else if (isString(value)) {
                // value is a file name
                this.downloadFileAndSetLaraformData(this.baseUrl + '/' + value);
            } else if (isPlainObject(value)) {
                // value is serialized model (App\Models\File)
                this.downloadFileAndSetLaraformData(value.url);
            } else {
                reportToSentry(`Unexpected value type in FileElement: ${typeof value}`)
            }
        },
        downloadFileAndSetLaraformData(url) {
            // fetch the actual file
            fetch(url, {mode: 'no-cors'})
                .then(res => res.blob())
                .then((blob) => {
                    if(
                        ((isString(this.value) || isNil(this.value)) && !url.includes(this.value))
                        || (isPlainObject(this.value) && !url.includes(this.value.url))
                    ){
                        /**
                         * There was a race condition here:
                         * 1. the element is initialized, the default value is set and download started
                         * 2. the element is set to null due to loading data
                         * 3. this.file is sent when download completes
                         *
                         * This checks if the completed callback is for the current value.
                         */
                        return;
                    }

                    // creating JS File object
                    const jsFile = new File([blob], this.computeDisplayName(url))

                    // adding properties to JS File object
                    jsFile.originalName = this.originalName
                    jsFile.url = url.replace('/' + jsFile.name, '');

                    // creating a Laraform File from JS File object
                    // which will be set as this.file
                    this.set(jsFile)
                });
        },
        computeDisplayName(url) {
            const lastPathComponent = last(split(url, '/'));
            const nameWithoutParams = first(split(lastPathComponent, '?'));

            return nameWithoutParams || url;
        },
    },
    computed: {
        baseUrl() {
            return this.schema.url || this.url || '';
        }
    }
})
</script>
