<template>
    <component
        :is="theme.components.BaseElementLayout"
        :el$="el$"
    >
        <template slot="field">
            <slot name="prefix"></slot>
            <div
                class="row"
                v-if="showSlotConfig"
            >
                <div class="col-12">
                    <b-alert
                        show
                        class="d-flex mb-0 h-100 align-items-baseline"
                        :variant="enoughSlots ? 'success' : 'warning'"
                    >
                        <i
                            :class="
                                classNames('fas mr-2', {
                                    'fa-exclamation-triangle': !enoughSlots,
                                    'fa-check-circle': enoughSlots,
                                })
                            "
                        ></i>
                        <i18n
                            path="You have {candidateCount} and {slotCount}"
                            tag="div"
                        >
                            <template v-slot:candidateCount>
                                <span class="font-weight-medium">{{
                                    $tc("1 candidate|{count} candidates", candidatesCount)
                                }}</span>
                            </template>
                            <template v-slot:slotCount>
                                <span class="font-weight-medium">{{
                                    $tc("1 slot|{count} slots", participantCount)
                                }}</span>
                            </template>
                        </i18n>
                    </b-alert>
                </div>
            </div>
            <div class="d-flex my-2">
                <div class="d-flex text-sm">
                    <div
                        v-for="user in users"
                        class="d-flex align-items-center mr-3"
                    >
                        <avatar
                            :user="user"
                            avatar-class="mr-2"
                        ></avatar>
                        {{ user.name }}
                    </div>
                </div>
                <div class="text-muted text-sm ml-auto align-self-end">
                    {{ $t("All times are {timezone}", {timezone: timezone}) }}
                </div>
            </div>
            <td-vue-cal
                :time-from="timeFromAsMinutes"
                :time-to="timeToAsMinutes"
                :hide-weekends="hideWeekends"
                ref="vuecal"
                :snap-to-time="snap"
                :editable-events="editableEventsConfig"
                :drag-to-create-event="false"
                :events="events"
                @event-resizing="handleEventResizing($event)"
                @event-duration-change="handleEventDurationChange($event)"
                @event-change="handleEventChange($event)"
                @event-delete="handleEventChange($event)"
                @cell-dblclick="() => false"
                :on-event-dblclick="(event, e) => e.stopPropagation()"
                :disable-views="['years', 'year', 'month', 'day']"
                hide-view-selector
                :time-cell-height="64"
                style="height: 49vh"
                :min-date="minDate"
                :max-date="maxDate"
                :on-event-create="handleEventCreate"
                :show-all-day-events="showAllDayEvents && 'short'"
                :special-hours="specialHours"
                :disable-days="disableDays"
                @cell-click="handleCellClick($event)"
                @view-change="handleViewChange($event)"
                class="time-scheduler teamdash-calendar vuecal--full-height-delete"
                :style="{
                    '--drag-shadow-top': dragShadowTop,
                    '--drag-shadow-height': dragShadowHeight,
                }"
                :cell-click-hold="false"
                :locale="locale"
                watch-real-time
                :class="{'highlight-available-times': schema.highlight_available_times}"
            >
                <template #title="{title, view}">
                    <span>
                        {{ title }}
                    </span>
                    <div class="ml-auto">
                        <b-dropdown
                            variant="white"
                            size="sm"
                            :text="$t('Additional options')"
                            right
                            v-if="showSlotConfig"
                        >
                            <div style="min-width: 250px">
                                <div class="form-group">
                                    <label
                                        for=""
                                        class="control-label pt-0 pb-1"
                                    >
                                        {{ $t("Calendar time") }}
                                    </label>
                                    <div class="d-flex gap-2">
                                        <select
                                            name=""
                                            v-bind:value="calendarTimeStart"
                                            v-on:input="calendarTimeStart = parseInt($event.target.value, 10)"
                                            class="form-control form-control-sm custom-select-sm available-time-input"
                                            id=""
                                        >
                                            <option
                                                :key="n"
                                                v-for="n in calendarTimeStartOptions"
                                                :value="n"
                                            >
                                                {{ n.toString().length === 1 ? `0${n}` : n }}:00
                                            </option>
                                        </select>
                                        <select
                                            name=""
                                            v-bind:value="calendarTimeEnd"
                                            v-on:input="calendarTimeEnd = parseInt($event.target.value, 10)"
                                            class="form-control form-control-sm custom-select-sm available-time-input"
                                            id=""
                                        >
                                            <option
                                                :key="n"
                                                v-for="n in calendarTimeEndOptions"
                                                :value="n"
                                            >
                                                {{ n.toString().length === 1 ? `0${n}` : n }}:00
                                            </option>
                                        </select>
                                        <b-button
                                            variant="white"
                                            class="py-0"
                                            v-b-tooltip.bottom.hover="
                                                hideWeekends ? $t('Show weekends') : $t('Hide weekends')
                                            "
                                            @click="hideWeekends = !hideWeekends"
                                        >
                                            <i
                                                :class="
                                                    classNames('fas', {
                                                        'fa-calendar-check': hideWeekends,
                                                        'fa-calendar-times': !hideWeekends,
                                                    })
                                                "
                                            />
                                        </b-button>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label
                                        for=""
                                        class="control-label pt-0 pb-1"
                                    >
                                        {{ $t("Limit interviews to start on ...") }}
                                    </label>
                                    <select
                                        name=""
                                        v-model="snap"
                                        class="form-control form-control-sm custom-select-sm"
                                        id=""
                                    >
                                        <option :value="60">{{ $t("full hour (:00)") }}</option>
                                        <option :value="15">{{ $t("15 minutes (:00, :15, :30, :45)") }}</option>
                                        <option :value="10">{{ $t("10 minutes (:00, :10, :20...)") }}</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label
                                        for=""
                                        class="control-label pt-0 pb-1"
                                    >
                                        {{ $t("Interview length (in minutes)") }}
                                    </label>
                                    <input
                                        class="form-control form-control-sm"
                                        type="number"
                                        min="10"
                                        step="5"
                                        v-model="defaultLength"
                                    />
                                </div>
                            </div>
                        </b-dropdown>
                    </div>
                </template>
                <template #event="{event, view}">
                    <time-scheduler-event
                        :event="event"
                        :view="view"
                        :show-slot-config="showSlotConfig"
                        @set-popover-open="isPopoverOpen = $event"
                        @delete="deleteEvent(event)"
                        @change="handleEventChange()"
                    />
                </template>
            </td-vue-cal>
            <slot name="suffix"></slot>
        </template>

        <slot
            slot="label"
            name="label"
            :el$="el$"
        ></slot>
        <slot
            slot="info"
            name="info"
            :el$="el$"
        ></slot>
        <slot
            slot="before"
            name="before"
            :el$="el$"
        ></slot>
        <slot
            slot="between"
            name="between"
            :el$="el$"
        ></slot>
        <slot
            slot="error"
            name="error"
            :el$="el$"
        ></slot>
        <slot
            slot="after"
            name="after"
            :el$="el$"
        ></slot>
    </component>
</template>

<script lang="ts">
// @ts-nocheck

import BaseElement from "@laraform/laraform/src/mixins/BaseElement";
import BaseValidation from "@laraform/laraform/src/mixins/BaseValidation";
import Condition from "@laraform/laraform/src/utils/condition";
import Vue from "vue";
import "vue-cal/dist/vuecal.css";
import axios from "axios";
import {setMoment} from "../config";

import Lockr from "lockr";
import {DateTime, Interval} from "luxon";
import classNames from "classnames";
import {BAlert, BButton, BButtonGroup, BCollapse, BDropdown, BDropdownForm, BPopover, VBTooltip} from "bootstrap-vue";
import {debounce, isNil, range} from "lodash";
import Avatar from "../Avatar.vue";
import TimeSchedulerEvent from "./TimeSchedulerEvent.vue";
import {getTimezone} from "../datetime";
import {TimezoneDate} from "../TimezoneDate";
import TdVueCal from "../TdVueCal.vue";

setMoment();

export default Vue.extend({
    name: "TimeSchedulerElement",
    mixins: [BaseElement, BaseValidation],
    components: {
        TdVueCal,
        TimeSchedulerEvent,
        Avatar,
        BAlert,
        BButton,
        BButtonGroup,
        BCollapse,
        BDropdown,
        BDropdownForm,
        BPopover,
    },
    directives: {
        VBTooltip,
    },
    data() {
        return {
            isMounted: false,
            users: [],
            events: [] as CalendarEvent[],
            presetEvents: [] as CalendarEvent[],
            snap: this.schema.slot_snap ?? Lockr.get("scheduler_snap", 15),
            defaultLength: this.schema.slot_length ?? Lockr.get("scheduler_default_length", 45),
            timezone: getTimezone(),
            minDate: this.schema.min_date ?? DateTime.now().toFormat("yyyy-MM-dd"),
            maxDate: this.schema.max_date ?? null,
            calendarTimeStart: Lockr.get("calendar_time_start", 7),
            calendarTimeEnd: Lockr.get("calendar_time_end", 20),
            hideWeekends: Lockr.get("hide_weekends", true),
            dragShadowTop: null as null | string,
            dragShadowHeight: null as null | string,
            isPopoverOpen: false,
            isResizing: false,
            locale: {
                weekDays: [
                    this.$i18n.t("Monday").toString(),
                    this.$i18n.t("Tuesday"),
                    this.$i18n.t("Wednesday"),
                    this.$i18n.t("Thursday"),
                    this.$i18n.t("Friday"),
                    this.$i18n.t("Saturday"),
                    this.$i18n.t("Sunday"),
                ],
                months: [
                    this.$i18n.t("January").toString(),
                    this.$i18n.t("February"),
                    this.$i18n.t("March"),
                    this.$i18n.t("April"),
                    this.$i18n.t("May"),
                    this.$i18n.t("June"),
                    this.$i18n.t("July"),
                    this.$i18n.t("August"),
                    this.$i18n.t("September"),
                    this.$i18n.t("October"),
                    this.$i18n.t("November"),
                    this.$i18n.t("December"),
                ],
                years: this.$i18n.t("Years"),
                year: this.$i18n.t("Year"),
                month: this.$i18n.t("Month"),
                week: this.$i18n.t("Week"),
                day: this.$i18n.t("Day"),
                today: this.$i18n.t("Today"),
                noEvent: this.$i18n.t("No Event"),
                allDay: this.$i18n.t("All day"),
                deleteEvent: this.$i18n.t("Delete"),
                createEvent: this.$i18n.t("Create an event"),
                dateFormat: "dddd D MMMM YYYY",
            },
            highlight_available_times: this.schema.highlight_available_times ?? false,
        };
    },
    created() {
        this.stopResizing = debounce(this.stopResizing, 1000);
    },
    mounted() {
        Vue.nextTick(() => {
            this.handleEventChange();
            if (this.form$) {
                if (this.form$.el$("users")) {
                    this.form$.el$("users").on("change", () => {
                        this.loadEvents();
                    });
                }
                this.loadEvents();
            }
            if (this.$refs.vuecal) {
                this.$refs.vuecal.now = new TimezoneDate(new Date().toISOString(), getTimezone());
                if (this.schema.min_date) {
                    this.$refs.vuecal.updateSelectedDate(this.schema.min_date);
                }
            }
        });
        this.isMounted = true;
    },
    methods: {
        classNames,
        deleteEvent(event: any) {
            if (confirm(this.$t("Are you sure you want to delete this event slot?").toString())) {
                this.$refs.vuecal.utils.event.deleteAnEvent(event);
                this.handleEventChange();
            }
        },
        stopResizing() {
            this.isResizing = false;
        },
        handleCellClick(e: Date): void {
            if (!e || this.isPopoverOpen || this.isResizing) {
                return;
            }
            if (this.isSingleSlot && this.memory.length >= 1) {
                return;
            }
            let getRoundedDate = (minutes: number, d = new Date()) => {
                let ms = 1000 * 60 * minutes; // convert minutes to ms
                let roundedDate = new Date(Math.round(d.getTime() / ms) * ms);
                return roundedDate;
            };
            let rounded = new TimezoneDate(getRoundedDate(this.snap, e), getTimezone());

            // Disallow creating past events
            if (rounded < new Date()) {
                return;
            }

            const newEventStartISO = rounded.toISOString();
            const newEventDurationISO = `PT${this.defaultLength}M`;
            const newEventInterval = Interval.fromISO(`${newEventStartISO}/${newEventDurationISO}`);

            const isOutOfBounds = (interval: Interval): boolean => {
                const minAllowed = DateTime.fromISO(this.minDate).plus({minutes: this.timeFromAsMinutes});
                const maxAllowed = DateTime.fromISO(this.maxDate).plus({minutes: this.timeToAsMinutes});

                const eventStartAsMinutes = interval.start.hour * 60 + interval.start.minute;
                const eventEndAsMinutes = (interval.end.hour || 24) * 60 + interval.end.minute;

                return (
                    interval.start < minAllowed ||
                    interval.end > maxAllowed ||
                    eventStartAsMinutes < this.timeFromAsMinutes ||
                    eventEndAsMinutes > this.timeToAsMinutes
                );
            };

            // Disallow creating events that would exceed bounds
            if (isOutOfBounds(newEventInterval)) {
                return;
            }

            let overlapCheck = true;
            if (!this.allowOverlap) {
                // Check that there is no overlap with existing events
                overlapCheck = this.blockedIntervals
                    .map(presetEvent => !presetEvent.overlaps(newEventInterval))
                    .every(x => !!x);
            }

            if (overlapCheck) {
                this.$refs.vuecal.createEvent(rounded, this.defaultLength, {
                    class: "vuecal__event--interview-slot vuecal__event--interview-slot--new",
                    tz: getTimezone(),
                    participants: this.isSingleSlot ? this.candidatesCount : 1,
                    editable: !this.isSingleSlot,
                    resizable: true,
                });
                this.$nextTick(() => {
                    this.validate();
                });
            }
        },
        handleEventCreate(event) {
            event.start = new TimezoneDate(event.start, getTimezone());
            event.end = new TimezoneDate(event.end.toISOString(), getTimezone());
            return event;
        },
        handleEventChange(event) {
            if (event?.event && event?.originalEvent) {
                const start = new TimezoneDate(event.event.start, getTimezone());
                const end = new TimezoneDate(event.event.end, getTimezone());

                // Event should not be dragged into the past.
                const pastCheck = !(start < new Date());

                // Event should not be dragged too early or too late.
                const earlyCheck = !(start.getHours() * 60 + start.getMinutes() < this.timeFromAsMinutes);
                const endHours = end.getHours() === 0 ? 24 : end.getHours();
                const lateCheck = !(endHours * 60 + end.getMinutes() > this.timeToAsMinutes);

                // Event should not overlap with others if the schema doesn't allow it.
                // This is used when the candidate can select their own timeslot.
                let overlapCheck = true;
                if (!this.allowOverlap) {
                    const eventInterval = Interval.fromDateTimes(start, end);
                    overlapCheck = this.blockedIntervals
                        .map(presetEvent => !presetEvent.overlaps(eventInterval))
                        .every(x => !!x);
                }

                const movedEvent = this.$refs.vuecal.mutableEvents.find(e => e._eid === event.event._eid);

                // If the event overlaps with another event after dragging to a new position, or would be in the past,
                // throw it back to where it came from.
                if (!overlapCheck || !pastCheck || !earlyCheck || !lateCheck) {
                    const jumpBackStart = DateTime.fromISO(event.originalEvent.start);
                    const jumpBackEnd = DateTime.fromISO(event.originalEvent.end);
                    movedEvent.start = jumpBackStart.toJSDate();
                    movedEvent.startTimeMinutes = jumpBackStart.hour * 60 + jumpBackStart.minute;
                    movedEvent.end = jumpBackEnd.toJSDate();
                    movedEvent.endTimeMinutes = jumpBackEnd.hour * 60 + jumpBackEnd.minute;
                }

                const movedEventTimes = this.getTimezoneDatesForEvent(movedEvent);

                movedEvent.start = movedEventTimes.start;
                movedEvent.startTimeMinutes = movedEventTimes.startTimeMinutes;
                movedEvent.end = movedEventTimes.end;
                movedEvent.endTimeMinutes = movedEventTimes.endTimeMinutes;
            }

            this.$nextTick(() => {
                this.update(
                    JSON.parse(
                        JSON.stringify(
                            this.$refs.vuecal.mutableEvents
                                .filter(e => !e.model_id)
                                .map(event => ({
                                    ...event,
                                    start: new TimezoneDate(event.start, getTimezone()),
                                    end: new TimezoneDate(event.end, getTimezone()),
                                }))
                        )
                    )
                );
                this.handleChange();
                this.ensureEventsHaveDragListeners();
            });
        },
        handleViewChange(event: any) {
            this.loadEvents();
        },
        async loadEvents() {
            const startDate = DateTime.fromJSDate(this.$refs.vuecal.view.startDate)
                .setZone(this.timezone)
                .toFormat("yyyy-MM-dd HH:mm");

            const endDate = DateTime.fromJSDate(this.$refs.vuecal.view.endDate)
                .setZone(this.timezone)
                .toFormat("yyyy-MM-dd HH:mm");

            const user_ids = this.form$?.el$("users")?.value ?? this.form$?.el$("users_to_add")?.value ?? null;

            const res = await axios.get(this.schema.timeSlotUrl, {
                params: {
                    tz: this.timezone,
                    user_ids: user_ids,
                    start_date: startDate,
                    end_date: endDate,
                },
            });

            const presetEvents = res.data.timeSlots.filter(e => e.event_set_id !== this.form$.filtered.id);

            this.users = res.data.users;
            this.events = [
                ...this.$refs.vuecal.mutableEvents
                    .filter(e => !e.is_static)
                    .map(event => ({
                        ...event,
                        start: new TimezoneDate(event.start, getTimezone()),
                        end: new TimezoneDate(event.end, getTimezone()),
                    })),
                ...presetEvents.map(event => ({
                    ...event,
                    start: new TimezoneDate(event.start, getTimezone()),
                    end: new TimezoneDate(event.end, getTimezone()),
                })),
            ];
            this.presetEvents = presetEvents;
        },
        ensureEventsHaveDragListeners() {
            [...this.$el.querySelectorAll(".vuecal__event")].map(el => {
                if (!el.hasDragListener) {
                    el.addEventListener("drag", this.onDrag);
                    el.addEventListener("dragstart", (e: DragEvent) => {
                        el.cursorGrabAt = e.offsetY;
                    });
                    el.addEventListener("dragend", () => {
                        el.cursorGrabAt = null;
                        this.dragShadowTop = null;
                        this.dragShadowHeight = null;
                    });
                }
            });
        },
        onDrag(dragEvent: DragEvent): void {
            const {timeStep, timeCellHeight, timeFrom, utils} = this.$refs.vuecal;

            const getEventStart = e => {
                let {y} = utils.cell.getPosition(e);
                y -= e.target.cursorGrabAt * 1;
                return Math.round((y * timeStep) / parseInt(timeCellHeight) + timeFrom);
            };

            let startTimeMinutes = Math.max(getEventStart(dragEvent), 0);

            const plusHalfSnapTime = startTimeMinutes + this.$refs.vuecal.snapToTime / 2;
            startTimeMinutes = plusHalfSnapTime - (plusHalfSnapTime % this.$refs.vuecal.snapToTime);

            let vueEvent = dragEvent.target.__vue__.event;

            const ratio = parseInt(timeCellHeight) / timeStep;
            this.dragShadowTop = Math.round((startTimeMinutes - timeFrom) * ratio) + "px";
            this.dragShadowHeight = Math.round((vueEvent.endTimeMinutes - vueEvent.startTimeMinutes) * ratio) + "px";
        },
        handleEventResizing(event: any) {
            this.isResizing = true;
            this.stopResizing();
        },
        handleEventDurationChange(event: any) {
            this.stopResizing();
        },
        getTimezoneDatesForEvent(event: any) {
            const start = new TimezoneDate(event.start, getTimezone());
            const end = new TimezoneDate(event.end, getTimezone());

            return {
                start,
                startTimeMinutes: start.getHours() * 60 + start.getMinutes(),
                end,
                endTimeMinutes: end.getHours() * 60 + end.getMinutes(),
            };
        },
    },
    computed: {
        isSingleSlot(): boolean {
            if (typeof this.schema.singleSlot === "boolean") {
                return this.schema.singleSlot;
            }
            // this.schema.singleSlot can be an array of conditions similar to the main "conditions" property
            if (Array.isArray(this.schema.singleSlot)) {
                return !_.some(this.schema.singleSlot, condition => {
                    return !Condition.check(condition, this.path || null, this.form$);
                });
            }
            return false;
        },
        calendarTimeStartOptions(): number[] {
            return Array.from({length: this.calendarTimeEnd}, (v, k) => k);
        },
        calendarTimeEndOptions(): number[] {
            return Array.from({length: 24 - this.calendarTimeStart}, (v, k) => this.calendarTimeStart + k + 1);
        },
        localizedAvailableTimeStart(): DateTime | null {
            if (!this.schema.available_time_start) {
                return null;
            }
            return DateTime.fromISO(this.schema.available_time_start, {
                zone: this.schema.available_time_zone,
            }).toLocal();
        },
        localizedAvailableTimeEnd(): DateTime | null {
            if (!this.schema.available_time_end) {
                return null;
            }
            return DateTime.fromISO(this.schema.available_time_end, {zone: this.schema.available_time_zone}).toLocal();
        },
        timeFromAsMinutes() {
            if (this.schema.available_time_start) {
                if (this.localizedAvailableTimeStart.hour > this.localizedAvailableTimeEnd.hour) {
                    return 0;
                }
                const [hour, minute] = this.localizedAvailableTimeStart
                    .toFormat("HH:mm")
                    .split(":")
                    .map(segment => parseInt(segment, 10));
                return hour * 60 + minute;
            }
            if (!isNil(this.calendarTimeStart)) {
                return this.calendarTimeStart * 60;
            }
            return 7 * 60;
        },
        timeToAsMinutes() {
            if (this.schema.available_time_end) {
                if (this.localizedAvailableTimeEnd.hour < this.localizedAvailableTimeStart.hour) {
                    return 1440;
                }
                const [hour, minute] = this.localizedAvailableTimeEnd
                    .toFormat("HH:mm")
                    .split(":")
                    .map(segment => parseInt(segment, 10));
                return hour * 60 + minute;
            }
            if (!isNil(this.calendarTimeEnd)) {
                return this.calendarTimeEnd * 60;
            }
            return 20 * 60;
        },
        editableEventsConfig() {
            const resize = !this.schema.slot_length ?? true;
            return {
                title: false,
                drag: true,
                resize,
                delete: false,
                create: true,
            };
        },
        allowOverlap() {
            return this.schema.allow_overlap ?? true;
        },
        showSlotConfig() {
            return this.schema.show_slot_config ?? true;
        },
        candidatesCount(): number {
            let candidatesCount = 0;

            if (this.form$.el$("candidates")) {
                candidatesCount = this.form$.el$("candidates").value.length as number;
            }

            if (this.form$.el$("candidates_to_add")) {
                candidatesCount += this.form$.el$("candidates_to_add").value.length;
            }

            return candidatesCount;
        },
        participantCount(): number {
            const participantCount = (this.value || [])
                .filter((ce: CalendarEvent) => {
                    return new Date(ce.start) > new Date();
                })
                .reduce((carry: number, ce: CalendarEvent) => {
                    if (ce.participants) {
                        return carry + ce.participants;
                    }
                    return carry;
                }, 0) as number;
            return participantCount;
        },
        enoughSlots(): boolean {
            return this.participantCount >= this.candidatesCount;
        },
        blockedIntervals() {
            if (!this.isMounted) {
                return {};
            }
            const calendarView = this.$refs.vuecal.view;
            const start = DateTime.fromJSDate(calendarView.startDate);
            const end = DateTime.fromJSDate(calendarView.endDate);
            const availablePeriodInterval = Interval.fromDateTimes(start, end);

            // These are events that are already in the calendar.
            const presetEventsIntervals = this.presetEvents.map(event => {
                const start = DateTime.fromISO(event.start.replace(" ", "T"));
                const end = DateTime.fromISO(event.end.replace(" ", "T"));
                return Interval.fromDateTimes(start, end);
            });

            // These are time blocks that are unavailable due to calendar differences.
            const blockedHoursIntervals = availablePeriodInterval.splitBy({day: 1}).flatMap((dayInterval: Interval) => {
                const daySpecialHours = this.specialHours?.[dayInterval.s.weekday] ?? [];

                return daySpecialHours.map(specialHour => {
                    const start = dayInterval.start.set({
                        hour: Math.floor(specialHour.from / 60),
                        minute: specialHour.from % 60,
                    });
                    const end = dayInterval.start.set({
                        hour: Math.floor(specialHour.to / 60),
                        minute: specialHour.to % 60,
                    });
                    return Interval.fromDateTimes(start, end);
                });
            });

            return [...presetEventsIntervals, ...blockedHoursIntervals];
        },
        specialHours() {
            if (!this.isMounted) {
                return {};
            }
            const calendarView = this.$refs.vuecal.view;
            const today = new TimezoneDate(new Date().toISOString(), getTimezone());

            const specialHours = {};

            // Mark the part of today that has already passed as disabled.
            // It is not possible to create slots in the past anyways.
            if (calendarView.startDate <= today && today <= calendarView.endDate) {
                specialHours[today.getDay()] = [
                    ...(specialHours?.[today.getDay()] ?? []),
                    {
                        from: 0,
                        to: today.getHours() * 60 + today.getMinutes(),
                        class: "past-time--disabled",
                    },
                ];
            }

            if (!!this.localizedAvailableTimeStart && !!this.localizedAvailableTimeEnd) {
                // If the available time range happens to be overnight,
                // so that the start time is actually later than the end time (e.g. range is from 23:00 - 7:00),
                // we show the entire calendar and block out the time between 7:00 - 23:00 instead.
                if (this.localizedAvailableTimeEnd.hour < this.localizedAvailableTimeStart.hour) {
                    range(1, 7).forEach(day => {
                        specialHours[day] = [
                            ...(specialHours?.[day] ?? []),
                            {
                                from: this.localizedAvailableTimeEnd.hour * 60 + this.localizedAvailableTimeEnd.minute,
                                to:
                                    this.localizedAvailableTimeStart.hour * 60 +
                                    this.localizedAvailableTimeStart.minute,
                                class: "past-time--disabled",
                            },
                        ];
                    });
                }
            }

            return specialHours;
        },
        disableDays() {
            if (!this.schema.available_days) {
                return [];
            }
            const now = DateTime.now();
            // Calculate days in the available period that are not on included weekdays
            const start = DateTime.fromISO(this.minDate).startOf("day");
            const end = DateTime.fromISO(this.maxDate).endOf("day");
            const availablePeriodInterval = Interval.fromDateTimes(start, end);
            const availableDays = this.schema.available_days.split(",").map(day => parseInt(day, 10));

            const disabledDays = availablePeriodInterval
                .splitBy({day: 1})
                .map((dayInterval: Interval) => {
                    // If the day has already ended, or the weekday is not in available days, let's disable the day.
                    if (dayInterval.end < now || !availableDays.includes(dayInterval.s.weekday - 1)) {
                        return dayInterval.s.toISODate();
                    }
                    return null;
                })
                .filter(date => !!date);

            return disabledDays;
        },
        showAllDayEvents(): boolean {
            return this.schema.show_all_day_events ?? true;
        },
    },
    watch: {
        value(v: CalendarEvent[], o) {
            if (o === undefined && v) {
                this.events = v;
            }
        },
        candidatesCount(v: number) {
            if (this.isSingleSlot && this.value && this.value.length) {
                const event = this.value[0];
                if (event) {
                    const mutableEvent = this.$refs.vuecal.mutableEvents.find(e => e._eid === event._eid);
                    mutableEvent.participants = v;
                    this.handleEventChange();
                }
            }
        },
        snap(v) {
            Lockr.set(`scheduler_snap`, parseInt(v));
        },
        defaultLength(v) {
            Lockr.set(`scheduler_default_length`, parseInt(v));
        },
        calendarTimeStart(v) {
            Lockr.set("calendar_time_start", v);
        },
        calendarTimeEnd(v) {
            Lockr.set("calendar_time_end", v);
        },
        hideWeekends(v) {
            Lockr.set("hide_weekends", v);
        },
    },
});
</script>

<style scoped lang="scss">
.available-time-input {
    width: 35%;
}
</style>
