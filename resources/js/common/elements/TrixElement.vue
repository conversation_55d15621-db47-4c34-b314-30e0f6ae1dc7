<template>
    <component
        :is="theme.components.BaseElementLayout"
        :el$="el$"
    >
        <template slot="field">
            <slot name="prefix"></slot>
            <Trix
                v-model="model"
                :placeholder="placeholder"
                :id="`${el$.path}-${id}`"
                :accept="accept"
                :accept-mimes="acceptMimes"
                :endpoint="'/comments/handleAttachment'"
                :disabled="disabled"
                :add-link-to-attachments="addLinkToAttachments"
                :class="{
                    [theme.classes.trixDisabled]: disabled,
                    'has-email-signature': !!schema.signature || !!schema.signature_user_id_path,
                    'no-attachments': schema.no_attachments,
                    'not-sortable': true,
                }"
                @change="handleChange"
                @alert="handleAlert"
                @trix-attachment-remove="pullMentionsFromTrix"
                @trix-attachment-add="pullMentionsFromTrix"
                @trix-initialize="pullMentionsFromTrix"
                ref="trix$"
            />
            <p
                v-if="containsMentionAll"
                :id="mentionAllDisclaimerId"
                v-text="
                    $tc(
                        'No users will be notified.|1 user will be notified.|{count} users will be notified.',
                        this.mentionedUsers.length
                    )
                "
                v-b-tooltip.v-light.rightbottom.hover.window="mentionedUsers.map(user => user.name).join(', ')"
                style="width: fit-content"
                class="mb-0 mt-1 text-muted"
            />
            <div
                v-if="activeSignature"
                class="signature"
                v-html="activeSignature"
            ></div>
            <writing-assistant-popover
                :trix-el="$refs.trix$.$el"
                v-if="trixLoaded"
                :value="filtered"
                @data="load($event)"
                :merge-tag-info="schema.after"
                :context="schema.writing_assistant ? schema.writing_assistant.context : undefined"
                :specific-prompts="schema.writing_assistant ? schema.writing_assistant.specific_prompts || [] : []"
                :additional-ids="additionalIds"
            ></writing-assistant-popover>
            <slot name="suffix"></slot>
        </template>
        <template slot="description"></template>
        <slot
            slot="label"
            name="label"
            :el$="el$"
        ></slot>
        <slot
            slot="before"
            name="before"
            :el$="el$"
        ></slot>
        <slot
            slot="between"
            name="between"
            :el$="el$"
        ></slot>
        <slot
            slot="error"
            name="error"
            :el$="el$"
        ></slot>
        <slot
            slot="after"
            name="after"
            :el$="el$"
        >
            <merge-tag-description
                :after-class="theme.classes.fieldAfter"
                @tagclick="insertTagAtCursor($event)"
                :description="el$.after"
                v-if="el$.after"
            ></merge-tag-description>
            <small
                v-if="shouldWarnBlankPositionDisplayName"
                :class="theme.classes.fieldAfter"
                class="form-text text-danger"
            >
                {{ $t("This project doesn't have a position display name set.") }}
                {{ $t("{mergeTag} will be blank.", {mergeTag: "[position_display_name]"}) }}
            </small>
        </slot>
    </component>
</template>

<script>
import TrixElement from "@laraform/laraform/src/components/elements/TrixElement.vue";

// import Trix from '@laraform/laraform/src/components/wrappers/Trix.vue'
// TODO: Consider the necessity of this, seeing as we will soon be using a vendored version of Laraform
import Trix from "../wrappers/Trix.vue";
import MergeTagDescription from "./components/MergeTagDescription.vue";
import {default as TrixLib} from "trix";
import Tribute from "tributejs";
import "tributejs/dist/tribute.css";
import {chain, debounce, isEqual} from "lodash";
import Lockr from "lockr";
import Vue from "vue";
import axios from "axios";
import {BDropdownItem, BPopover, BSpinner, VBTooltip} from "bootstrap-vue";
import WritingAssistantPopover from "./partials/WritingAssistantPopover.vue";
import {getProjectFromContext, makeId, readFile} from "../util";
import {shouldWarnBlankPositionDisplayName} from "../mergeTags";

const debounceAutosave = debounce((key, value) => {
    Lockr.set(key, value);
}, 1000);

Vue.directive("b-tooltip", VBTooltip);

export default {
    mixins: [TrixElement],
    components: {
        WritingAssistantPopover,
        Trix,
        MergeTagDescription,
        BPopover,
        BSpinner,
        BDropdownItem,
    },
    data() {
        return {
            activeSignature: this.schema.signature,
            tribute: null,
            fieldTarget: null,
            trixLoaded: false,
            mentionAllDisclaimerId: makeId(),
            isPublic: true,
            mentionedUsers: [],
            containsMentionAll: false,
        };
    },
    mounted() {
        if (this.schema.mentions) {
            this.addMentions();
        }
        this.$nextTick(() => {
            this.trixLoaded = true;
        });
        if (this.schema.no_attachments) {
            document.addEventListener("trix-file-accept", function (event) {
                event.preventDefault();
            });
        }
        if (this.schema.auto_save_key) {
            this.loadAutoSave();
        }

        if (this.schema.signature_user_id_path) {
            Vue.nextTick(() => {
                if (this.form$) {
                    const userId = this.form$.el$(this.schema.signature_user_id_path).value;
                    if (userId) {
                        this.updateSignature(userId);
                    }

                    const explicitWatchFields = this.schema.signature_watch_fields || [];

                    const watchFields = [...explicitWatchFields, this.schema.signature_user_id_path];

                    watchFields.forEach(field => {
                        this.$watch(
                            () => this.form$.el$(field).value,
                            async val => {
                                let userId = val;
                                if (field !== this.schema.signature_user_id_path) {
                                    userId = this.form$.el$(this.schema.signature_user_id_path).value;
                                }

                                if (userId) {
                                    const signatureData = {};

                                    await Promise.all(
                                        watchFields.map(async field => {
                                            const fieldVal = this.form$.el$(field).value;

                                            if (fieldVal instanceof Blob) {
                                                signatureData[field] = await readFile(fieldVal);
                                            } else {
                                                signatureData[field] = fieldVal;
                                            }
                                        })
                                    );

                                    this.updateSignature(userId, signatureData);
                                }
                            }
                        );
                    });
                }
            });
        }

        if (this.schema.autofill_url && Array.isArray(this.schema.autofill_watch)) {
            Vue.nextTick(() => {
                if (this.schema.autofill_on_load) {
                    this.autofill();
                }
                this.schema.autofill_watch.forEach(watchField => {
                    if (this.form$ && this.form$.el$(watchField)) {
                        this.$watch(
                            () => this.form$.el$(watchField).value,
                            async val => {
                                if (val) {
                                    this.autofill();
                                }
                            }
                        );
                    }
                });
            });
        }

        this.$watch(
            "form$.data.is_public",
            newValue => {
                this.isPublic = newValue;
                this.pushMentionRemovalsToTrix();
                this.pushMentionAllUpdatesToTrix();
                this.pullMentionsFromTrix();
            },
            {immediate: true}
        );

        // Submit on ctrl+enter
        if (this.schema.submit_on_ctrl_enter) {
            this.$refs.trix$.$el.querySelector("trix-editor").addEventListener("keydown", e => {
                if (e.key === "Enter" && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    this.form$.submit();
                }
            });
        }

        // Autofocus
        if (this.schema.autofocus) {
            this.$nextTick(() => {
                this.$refs.trix$.$el.querySelector("trix-editor").focus();
            });
        }
    },
    methods: {
        handleChange(value) {
            if (this.fire("change") === false) {
                return;
            }
            if (this.form$.$_shouldValidateOn("change")) {
                this.validate();
            }
            if (this.schema.auto_save_key) {
                debounceAutosave(this.schema.auto_save_key, value);
            }
        },
        loadAutoSave() {
            const autosaveValue = Lockr.get(this.schema.auto_save_key);
            if (autosaveValue) {
                this.trix$.update(autosaveValue);
                this.pushMentionAllUpdatesToTrix();
                this.pullMentionsFromTrix();
            }
        },
        insertTagAtCursor(tag) {
            this.$refs.trix$.$refs.trix$.editor.insertString(tag);
        },
        update(value) {
            this.trix$.update(value);
            if (!this.value && value) {
                // I'm not entirely sure why this happens
                this.value = value;
            }
            this.pushMentionAllUpdatesToTrix();
            this.pullMentionsFromTrix();
        },
        addMentions() {
            const _pasteHtml = (html, startPos, endPos) => {
                let position = this.fieldTarget.editor.getPosition();
                console.log(html, startPos, endPos, position);
                this.fieldTarget.editor.setSelectedRange([
                    position - this.tribute.currentMentionTextSnapshot?.length - 1,
                    position,
                ]);
                this.fieldTarget.editor.deleteInDirection("backward");
            };

            this.fieldTarget = this.$refs.trix$.$refs.trix$;

            const mentionAllOption = {
                all: true,
                name: this.$t("all").toString(),
            };

            this.tribute = new Tribute({
                allowSpaces: true,
                lookup: "name",
                values: (text, cb) =>
                    cb(this.project ? [mentionAllOption, ...this.mentionableUsers] : this.mentionableUsers),
                selectClass: "tribute-selected",
                menuItemTemplate: function (item) {
                    return '<a class="dropdown-item">' + item.string + "</a>";
                },
            });

            this.tribute.attach(this.fieldTarget);

            this.tribute.range.pasteHtml = _pasteHtml.bind(this);

            this.fieldTarget.addEventListener("tribute-replaced", e => {
                console.log(e);
                let mention = e.detail.item.original;

                if (mention.all) {
                    this.containsMentionAll = true;
                }

                let attachment = new TrixLib.Attachment({
                    content: "&#64;" + mention.name,
                    contentType: "text/mention",
                    ...(mention.all
                        ? {all: true, user_ids: this.mentionableProjectUsers.map(user => user.id)}
                        : {all: false, user_id: mention.id}),
                });
                this.fieldTarget.editor.insertAttachment(attachment);
                this.fieldTarget.editor.insertString(" ");

                this.pullMentionsFromTrix();
            });
        },
        async updateSignature(userId, data = {}) {
            const response = await axios.post(`/users/${userId}/signature`, data);
            this.activeSignature = response.data.signature;
        },
        async autofill() {
            if (this.available && this.schema.autofill_url && Array.isArray(this.schema.autofill_watch)) {
                let autoFillUrl = this.schema.autofill_url;
                this.schema.autofill_watch.forEach(watchField => {
                    if (!this.form$.el$(watchField)) {
                        return;
                    }
                    const watchFieldValue = this.form$.el$(watchField).value;
                    if (!watchFieldValue) {
                        return;
                    }
                    if (this.schema.autofill_skip?.[watchField]?.includes(watchFieldValue)) {
                        return;
                    }
                    const placeholder = `---${watchField}---`;
                    if (autoFillUrl.includes(placeholder)) {
                        autoFillUrl = autoFillUrl.replace(placeholder, watchFieldValue);
                    }
                });

                if (autoFillUrl.includes("---")) {
                    return;
                }
                this.update(this.$t("Loading..."));
                const response = await axios.get(autoFillUrl);
                this.update(response.data);
            }
        },
        checkEditorContainsMentionAll() {
            return this.getTrixAttachments().some(attachment => attachment?.attributes?.values?.all);
        },
        getEditor() {
            return this.$refs.trix$.$refs.trix$.editor;
        },
        getTrixAttachments() {
            return this.getEditor()?.composition?.attachments ?? [];
        },
        pushMentionRemovalsToTrix() {
            const mentionableUserIds = this.mentionableUsers.map(user => user.id);

            // Remove direct-user mentions of users who are not currently mentionable
            this.getTrixAttachments()
                .filter(attachment => attachment?.attributes?.values?.user_id)
                .filter(attachment => !mentionableUserIds.includes(attachment.attributes.values.user_id))
                .forEach(attachment => this.getEditor().composition.removeAttachment(attachment));
        },
        pushMentionAllUpdatesToTrix() {
            // Required because "user_ids" of existing @all-attachments can go stale after context changes
            this.getTrixAttachments()
                .filter(attachment => attachment?.attributes?.values?.all)
                .forEach(attachment =>
                    attachment.setAttributes({
                        user_ids: this.mentionableProjectUsers.map(user => user.id),
                    })
                );
        },
        hasAccess(user) {
            const isIncludedInSchema = this.potentiallyMentionableUsers.map(u => u.id).includes(user.id);
            const passesLimitedUserCheck =
                user.role !== "limited" ||
                this.isPublic ||
                (this.schema.for_type === "ProjectLog" && window.settings?.show_all_project_logs_to_limited);
            return isIncludedInSchema && passesLimitedUserCheck;
        },
        isNotCurrentUser(user) {
            return user.id !== window.settings?.user?.id;
        },
        getCurrentlyMentionedUsers() {
            return chain(this.getTrixAttachments())
                .filter(attachment => attachment?.attributes?.values?.all || attachment?.attributes?.values?.user_id)
                .flatMap(attachment =>
                    attachment.attributes.values.all
                        ? this.mentionableProjectUsers
                        : this.mentionableUsers.filter(user => user.id === attachment.attributes.values.user_id)
                )
                .uniqBy("id")
                .sortBy("name")
                .value();
        },
        pullMentionsFromTrix() {
            this.containsMentionAll = this.checkEditorContainsMentionAll();
            this.mentionedUsers = this.getCurrentlyMentionedUsers();
        },
    },
    computed: {
        addLinkToAttachments() {
            return this.schema?.add_link_to_attachments ?? true;
        },
        additionalIds() {
            let ids = JSON.parse(
                JSON.stringify(this.schema.writing_assistant ? this.schema.writing_assistant.ids || {} : {})
            );

            if (this.form$.el$("candidates") && !ids.candidate_id) {
                let candidateIds = this.form$.el$("candidates").value;

                if (candidateIds.length === 1) {
                    ids["candidate_id"] = candidateIds[0];
                }
            }

            return ids;
        },
        shouldWarnBlankPositionDisplayName() {
            return shouldWarnBlankPositionDisplayName(this.model, this.project);
        },
        potentiallyMentionableUsers() {
            return this.schema.potentially_mentionable_users ?? [];
        },
        mentionableUsers() {
            return this.potentiallyMentionableUsers.filter(this.hasAccess).filter(this.isNotCurrentUser);
        },
        mentionableProjectUsers() {
            return [this.project.manager, ...this.project.users].filter(this.hasAccess).filter(this.isNotCurrentUser);
        },
        project() {
            return getProjectFromContext(this);
        },
    },
    watch: {
        "project.users": function (newValue, oldValue) {
            const newIds = new Set((newValue ?? []).map(user => user.id));
            const oldIds = new Set((oldValue ?? []).map(user => user.id));
            if (!isEqual(newIds, oldIds)) {
                this.pushMentionAllUpdatesToTrix();
                this.pullMentionsFromTrix();
            }
        },
        "project.project_manager_id": function (newValue, oldValue) {
            if (newValue !== oldValue) {
                this.pushMentionAllUpdatesToTrix();
                this.pullMentionsFromTrix();
            }
        },
    },
};
</script>
