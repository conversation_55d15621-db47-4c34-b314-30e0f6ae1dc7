<template>
    <component :is="theme.components.BaseElementLayout"
               :el$="el$"
    >
        <template slot="field">
            <slot name="prefix"></slot>

            <component :is="theme.components.ElementLabelFloating"
                       v-if="floating"
                       :visible="!empty"
            >{{ floating }}
            </component>

            <multiselect
                :value="selectedItem"
                v-bind="options"
                :id="id"
                :name="name.toString()"
                :options="selectOptions"
                :placeholder="placeholder"
                :disabled="disabled"
                :multiple="multiple"
                :searchable="search"
                label="label"
                track-by="label"
                :show-labels="false"
                :loading="loading"
                :optionsLimit="limit"
                @input="updateValue"
                @select="handleSelect"
                @remove="handleRemove"
                @search-change="handleSearchChange"
                @open="handleOpen"
                @close="handleClose"
                ref="select$"
            >
                <slot name="beforeList" slot="beforeList" :el$="el$">
                    <component v-if="slots.beforeList" :is="slots.beforeList" :el$="el$"/>
                </slot>
                <slot name="afterList" slot="afterList" :el$="el$">
                    <component v-if="slots.afterList" :is="slots.afterList" :el$="el$"/>
                </slot>
                <slot name="singleLabel" slot="singleLabel" :el$="el$" :option="selectedOption">
                    <component v-if="slots.singleLabel" :is="slots.singleLabel" :el$="el$" :option="selectedOption"/>
                </slot>
                <slot name="noResult" slot="noResult" :el$="el$">
                    <component v-if="slots.noResult" :is="slots.noResult" :el$="el$"/>
                </slot>
                <slot name="noOptions" slot="noOptions" :el$="el$">
                    <component v-if="slots.noOptions" :is="slots.noOptions" :el$="el$"/>
                </slot>
                <template #option="{option}">
                    <p class="select-with-description-label mb-0 text-wrap" v-text="option.label"/>
                    <p v-if="option.description"
                       class="select-with-description-description mb-0 text-wrap"
                       v-text="option.description"/>
                </template>
                <slot name="selection" slot="selection" slot-scope="{ values, search, remove }" :el$="el$"
                      :values="values" :search="search" :remove="remove">
                    <component v-if="slots.selection" :is="slots.selection" :el$="el$" :values="values" :search="search"
                               :remove="remove">
                        <slot name="tag" slot="tag" slot-scope="{ option, search, remove }" :el$="el$" :option="option"
                              :search="search" :remove="remove">
                            <component v-if="slots.tag && selectOptions.length > 0" :is="slots.tag" :el$="el$"
                                       :option="option" :search="search" :remove="remove"/>
                        </slot>
                    </component>
                </slot>
            </multiselect>

            <slot name="suffix"></slot>
        </template>

        <slot slot="label" name="label" :el$="el$"></slot>
        <slot slot="before" name="before" :el$="el$"></slot>
        <slot slot="between" name="between" :el$="el$"></slot>
        <slot slot="error" name="error" :el$="el$"></slot>
        <slot slot="after" name="after" :el$="el$"></slot>
    </component>
</template>

<script>
import SelectElement from '@laraform/laraform/src/components/elements/SelectElement.vue'
import Multiselect from '@teamdash/vue-multiselect/src'

export default {
    name: 'SelectWithDescriptionElement',
    mixins: [SelectElement],
    components: {
        Multiselect,
    },
    created() {
        if (!this.schema.items) {
            throw new Error('Property `items` must be defined. To init with empty items, use {} or [].')
        }

        if (this.slots.noResult === null) {
            this.slots.noResult = this.theme.components.MultiselectSlotNoResult
        }

        if (this.slots.noOptions === null) {
            this.slots.noOptions = this.theme.components.MultiselectSlotNoOptions
        }

        this.$_copy(['items', 'search', 'limit', 'trackBy'])
    },
    computed: {
        selectOptions() {
            if (this.value && !this.items.find(item => item.value === this.value)) {
                return [{
                    label: 'Original value',
                    description: 'You don\'t have access to it',
                    value: this.value
                }, ...this.items];
            }

            return this.items;
        },
        selectedItem() {
            return this.selectOptions.find(option => option.value === this.model);
        }
    },
    methods: {
        updateValue(item) {
            this.model = item.value;
            this.handleInput(item.value);
        },
    }
}
</script>

<style lang="scss">
@use '/resources/sass/facelift/config/variables';
.select-with-description-label {
    font-size: 0.875rem;
}
.select-with-description-description {
    font-size: 0.75rem;
    color: variables.$gray-500;
}
</style>
