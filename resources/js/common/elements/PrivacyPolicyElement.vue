<script lang="ts">
import {defineComponent} from 'vue'
import StaticElement from '@laraform/laraform/src/components/elements/StaticElement.vue'

export default defineComponent({
    name: "PrivacyPolicyElement",
    mixins: [StaticElement],
    computed: {
        privacyPolicyLink() {
            // this.schema.privacy_policy_url is filled when showing the form publicly
            // See app/Http/Controllers/Tenant/Pub/FormController.php
            //
            // window.settings.privacy_policy_url is filled when showing the form in the admin panel
            // This ensures that the privacy policy link is always up-to-date and not stored on the form itself.
            const privacyPolicyUrl = this.schema.privacy_policy_url ?? window.settings?.privacy_policy_url;

            if (!privacyPolicyUrl) {
                return '';
            }

            return `<a href="${privacyPolicyUrl}" target="_blank">${this.el$.label}</a>`;
        }
    }
})
</script>

<template>
    <component :is="theme.components.BaseElementLayout"
               v-if="wrap"
               :el$="el$"
    >
        <template slot="field">
            <div
                v-if="isHtml"
                v-html="content"
            ></div>

            <component
                v-else
                :is="content"
                :el$="el$"
            />
        </template>

        <slot slot="label" name="label" :el$="el$">
            <component :is="theme.components.ElementLabel" :for="el$.id" v-html="privacyPolicyLink"/>
        </slot>
        <slot slot="before" name="before" :el$="el$"></slot>
        <slot slot="between" name="between" :el$="el$"></slot>
        <slot slot="error" name="error" :el$="el$"></slot>
        <slot slot="after" name="after" :el$="el$"></slot>
    </component>

    <div
        v-else-if="isHtml"
        v-html="content"
    ></div>

    <component
        v-else
        :is="content"
        :el$="el$"
    />
</template>

<style scoped>

</style>
