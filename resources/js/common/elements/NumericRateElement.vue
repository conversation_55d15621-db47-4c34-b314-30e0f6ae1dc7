<template>
    <component
        :is="theme.components.BaseElementLayout"
        :el$="el$"
    >
        <template slot="field">
            <slot name="prefix"></slot>

            <div :class="wrapperClass">
                <div class="btn-group d-flex">
                    <strong
                        v-for="i in range"
                        :key="'rating' + i"
                        class="btn btn-white justify-content-center flex-grow-1 px-0"
                        :class="{'active': el$.model === i}"
                        style="max-width: 4ch; min-width: 2ch"
                        @click="el$.model = i"
                        v-text="i"
                    />
                </div>
            </div>

            <slot name="suffix"></slot>
        </template>

        <slot slot="label" name="label" :el$="el$"></slot>
        <slot slot="info" name="info" :el$="el$"></slot>
        <slot slot="before" name="before" :el$="el$"></slot>
        <slot slot="between" name="between" :el$="el$"></slot>
        <slot slot="error" name="error" :el$="el$"></slot>
        <slot slot="after" name="after" :el$="el$"></slot>
    </component>
</template>

<script>
import BaseElement from '@laraform/laraform/src/mixins/BaseElement'
import BaseValidation from '@laraform/laraform/src/mixins/BaseValidation'

export default {
    name: 'NumericRateElement',
    mixins: [BaseElement, BaseValidation],
    created() {
        this.$_copy(['wrapperClass'])
    },
    computed: {
        min() {
            return Number(this.schema.range[0] ?? 0);
        },
        max() {
            return Number(this.schema.range[1] ?? 10);
        },
        range() {
            const length = this.max - this.min;
            return Array.from({length: length + 1}, (v, k) => k + this.min);
        }
    }
}
</script>
