<template>
    <img :src="resized" alt="">
</template>

<script lang="ts">
    import Vue from 'vue';
    import {getResizedImageUrl} from "./util";

    export default Vue.extend({
        name: "ResizedImg",
        props: {
            src: {
                required: true,
                type: String as () => string
            },
            width: {
                required: true,
                type: Number as () => number,
            }
        },
        computed: {
            resized(): string {
                return getResizedImageUrl(this.src, this.width);
            }
        },
    })
</script>
