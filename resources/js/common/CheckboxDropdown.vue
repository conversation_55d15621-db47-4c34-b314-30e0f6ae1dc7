<template>
    <b-dropdown
        ref="dropdown"
        :class="dropdownClass"
        :toggle-class="toggleClass"
        :menu-class="menuClass"
        :variant="variant"
        :size="size"
        @shown="handleShown"
        :right="right"
        :boundary="boundary"
        no-caret
        lazy
        :disabled="disabled"
    >
        <template v-slot:button-content>
            <small v-if="icon" class="mr-1">
                <i :class="[iconBase, icon]"></i>
            </small>
            <slot name="labelText" :value="value">
                {{ labelText }}
            </slot>
            <slot name="after-label"></slot>
            <small class="ml-2">
                <i class="fas fa-chevron-down"></i>
            </small>
        </template>
        <b-dropdown-header>
            <slot name="top"></slot>
            <div v-if="search" class="mb-2">
                <input
                    type="text"
                    ref="searchInput"
                    v-model="query"
                    class="form-control form-control-sm"
                    :placeholder="inputPlaceholder"
                >
            </div>
        </b-dropdown-header>
        <div style="min-width: 10rem;">
            <b-dropdown-divider
                class="mx-n2 my-1"
                v-if="search || $slots.top"
            ></b-dropdown-divider>
            <div class="d-flex align-items-center">
                <div v-if="showClearButton">
                    <button class="btn btn-xs btn-white" @click="clearValues">
                        {{ $t('Unselect all') }}
                    </button>
                </div>
                <slot name="extra"></slot>
            </div>
            <b-dropdown-divider
                class="mx-n2 my-1"
                v-if="showClearButton || $slots.extra"
            ></b-dropdown-divider>
            <overlay-scrollbars style="max-height: 20rem;">
                <div
                    class="dropdown-item"
                    v-for="o in filteredOptions"
                    :key="`${_uid}_${o.value}`"
                >
                    <styled-checkbox
                        :value="getValue(o)"
                        :key="`${_uid}_${o.value}`"
                        @input="updateValue($event, o)"
                        :no-wrap="true"
                        :disabled="o.disabled === true"
                        :class="{'text-muted': o.disabled === true}"
                    >
                        <slot name="option" :option="o">
                            {{ o.label }}
                        </slot>
                    </styled-checkbox>
                </div>
            </overlay-scrollbars>
            <div
                v-if="creatable && query"
                class="btn btn-white btn-xs"
                @click="handleAdd"
            >
                <i class="fas fa-plus mr-2"></i>
                <i18n path="Add {query}" tag="span">
                    <template v-slot:query>
                        <strong>{{ query }}</strong>
                    </template>
                </i18n>
            </div>
            <slot name="after"></slot>
        </div>
    </b-dropdown>
</template>

<script lang="ts">
import {BButton, BDropdown, BDropdownDivider, BDropdownHeader, BDropdownItem} from 'bootstrap-vue';
import StyledCheckbox from "../common/StyledCheckbox.vue";
import Vue from 'vue';
import {OverlayScrollbarsComponent} from "overlayscrollbars-vue";
import i18n from "./i18n";
import {PropType} from "vue/types/options";
import {debounce} from "lodash";
import axios from "axios";

const asyncSearchFn = debounce(async (vm) => {
    if (!vm.asyncEndpoint) {
        return;
    }
    const res = await axios.get(vm.asyncEndpoint, {
        params: {
            query: vm.query,
            include: vm.value,
        },
    })
    vm.managedOptions = res.data.options;
}, 50);

export default Vue.extend({
    components: {
        BButton,
        BDropdownDivider,
        BDropdownItem,
        BDropdown,
        BDropdownHeader,
        StyledCheckbox,
        'overlay-scrollbars': OverlayScrollbarsComponent,
    },
    mounted() {
        if (this.openOnMount) {
            this.$nextTick(() => {
                // @ts-ignore
                this.$refs.dropdown.show();
            });
        }
    },
    created() {
        if (this.value && this.asyncEndpoint) {
            // if there are values already selected and the component is async, we
            // need to fetch the options to display selected choices
            this.doAsyncSearch();
        }
    },
    props: {
        openOnMount: {
            type: Boolean as () => boolean,
            default: false,
        },
        dropdownClass: {
            type: String as () => string,
            default: '',
        },
        menuClass: {
            type: String as () => string,
            default: '',
        },
        toggleClass: {
            type: String as () => string,
            default: '',
        },
        variant: {
            type: String as () => string,
            default: 'white',
        },
        size: {
            type: String as () => string,
            default: 'sm',
        },
        options: {
            type: Array as () => BMultiSelectOption[],
        },
        value: {
            type: Array as () => (number | string)[],
        },
        search: {
            type: Boolean as () => boolean,
            default: false,
        },
        text: {
            type: String as () => string,
        },
        icon: {
            type: String as () => string | null,
            default: null,
        },
        right: {
            type: Boolean as () => boolean,
            default: false,
        },
        creatable: {
            type: Boolean as () => boolean,
            default: false,
        },
        showCount: {
            type: Boolean as () => boolean,
            default: true,
        },
        showClearButton: {
            type: Boolean as () => boolean,
            default: true,
        },
        boundary: {
            type: String as () => string,
            default: 'viewport',
        },
        asyncEndpoint: {
            type: String as PropType<string | null>
        },
        disabled: {
            type: Boolean as () => boolean,
            default: false,
        }
    },
    data() {
        return {
            query: '' as string,
            managedOptions: [] as BMultiSelectOption[]
        }
    },
    computed: {
        filteredOptions(): BMultiSelectOption[] {
            if (this.asyncEndpoint) {
                return this.managedOptions;
            }

            // @ts-ignore
            const q = this.query as string;
            if (!q) {
                return this.options;
            } else {
                return this.options.filter(v => {
                    return v.label.toLowerCase().includes(q.toLowerCase());
                });
            }
        },
        labelText() {
            if (this.value.length === 0 || !this.showCount) {
                return this.text;
            } else {
                return `${this.text} (${this.value.length})`;
            }
        },
        inputPlaceholder() {
            if (this.creatable) {
                return i18n.t('Type to search or add...');
            } else {
                return i18n.t('Type to search...');
            }
        },
        iconBase() {
            if (!this.icon) {
                return null;
            }
            return this.icon.startsWith('fa-') ? 'fas' : 'tdi';
        }
    },
    methods: {
        handleShown() {
            if (this.search) {
                let el = this.$refs.searchInput;
                if (el) {
                    // @ts-ignore
                    el.focus();
                }
            }
        },
        updateValue(value: boolean, option: BMultiSelectOption): void {
            let valueCopy = [...this.value];
            let idx = this.value.indexOf(option.value);
            if (value && idx === -1) {
                valueCopy.push(option.value);
            } else if (!value && idx > -1) {
                valueCopy = valueCopy.filter(v => v !== option.value);
            }
            this.$emit('input', valueCopy);
        },
        getValue(o: BMultiSelectOption): boolean {
            return this.value.includes(o.value);
        },
        clearValues() {
            this.$emit('input', []);
        },
        handleAdd() {
            this.$emit('add', this.query);
            this.query = "";
        },
        doAsyncSearch() {
            return asyncSearchFn(this);
        }
    },
    watch: {
        query(v) {
            console.log('query', v);
            if (!this.asyncEndpoint) {
                return;
            }
            this.doAsyncSearch();
        }
    }
})
</script>
