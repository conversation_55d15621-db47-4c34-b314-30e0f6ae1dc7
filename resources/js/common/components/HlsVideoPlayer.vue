<script lang="ts">
import {defineComponent} from 'vue'
import Hls from "hls.js";
import {PropType} from "vue/types/options";
import {VideoHTMLAttributes} from "vue/types/jsx";

export default defineComponent({
    name: "HlsVideoPlayer",
    props: {
        hlsSrc: {
            type: String,
        },
        fallbackSrc: {
            type: String,
            required: false,
        },
        hlsOnly: {
            type: Boolean,
            required: false,
            default: false,
        },
        videoAttributes: {
            type: Object as PropType<VideoHTMLAttributes>,
            default: () => {return {}},
        },
        shouldEmitTimeEvents: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            waitingForHls: false,
            videoElErrorListener: null as null | EventListener,
            videoElTimeUpdateListener: null as null | EventListener,
        };
    },
    mounted() {
        this.reinitializePlayer();
    },
    methods: {
        reinitializePlayer() {
            const videoElement = this.getVideoElement();
            if (this.videoElErrorListener) {
                videoElement.removeEventListener("error", this.videoElErrorListener);
            }
            if (this.videoElTimeUpdateListener) {
                videoElement.removeEventListener("timeupdate", this.videoElTimeUpdateListener);
            }

            if (Hls.isSupported() && this.hlsSrc) {
                console.log('Using hls.js');
                const hls = new Hls();
                hls.loadSource(this.hlsSrc);
                hls.attachMedia(videoElement);
                hls.on(Hls.Events.ERROR, (e) => {
                    console.log('Caught error event from hls.js');
                    hls.detachMedia();
                    if (this.fallbackSrc && !this.hlsOnly) {
                        console.log('replacing src with fallback src');
                        videoElement.src = this.fallbackSrc;
                    } else if (this.hlsOnly) {
                        // we can poll until the hls is ready
                        setTimeout(() => {
                            this.reinitializePlayer();
                        }, 1000);
                    }
                });
            } else if (videoElement.canPlayType('application/vnd.apple.mpegurl') && this.hlsSrc) {
                console.log('Using native hls support');

                this.videoElErrorListener = () => {
                    console.log('handling video error event');
                    if (this.fallbackSrc) {
                        console.log('Falling back to fallback src after native hls error');
                        videoElement.src = this.fallbackSrc;
                    }
                };
                videoElement.addEventListener('error', this.videoElErrorListener);
                videoElement.src = this.hlsSrc;
            } else if (this.fallbackSrc) {
                console.log('Fallback url is only option, so doing default video.');
                videoElement.src = this.fallbackSrc;
            }

            if (this.shouldEmitTimeEvents) {
                this.videoElTimeUpdateListener = () => {
                    this.$emit('timeupdate', videoElement.currentTime);
                };
                videoElement.addEventListener('timeupdate', this.videoElTimeUpdateListener);
            }
        },
        getVideoElement(): HTMLMediaElement {
            return this.$el as HTMLMediaElement;
        },
    },
    watch: {
        hlsSrc() {
            this.reinitializePlayer();
        },
        fallbackSrc() {
            this.reinitializePlayer();
        },
    },
});
</script>

<template>
    <video
        controls
        v-bind="videoAttributes"
        playsinline
    ></video>
</template>

<style scoped></style>
