<template>
    <div>
        <a class="html-preview-trigger" :id="`html-preview-trigger-${componentId}`">
            <slot></slot>
        </a>
        <b-popover
            :target="`html-preview-trigger-${componentId}`"
            triggers="hover"
            :custom-class="'popover--wide'"
            placement="right"
            style="max-width: 100%;"
            boundary="window"
            :delay="{show: 50, hide: 200}"
            @show="previewLoaded()"
        >
            <div
                class="overflow-auto pr-3"
                style="width: 70ch;
                height: 70vh;"
                v-html="htmlValue"
                v-if="htmlValue"/>
            <div
                class="overflow-auto pr-3 d-flex align-items-center justify-content-center"
                style="width: 70ch;
                height: 70vh;"
                v-else
            >
                <b-spinner/>
            </div>
        </b-popover>
    </div>
</template>

<script lang="ts">
import {defineComponent} from 'vue';
import {BPopover, BSpinner} from "bootstrap-vue";
import {makeId} from "../util";
import {PropType} from "vue/types/options";
import axios from "axios";

export default defineComponent({
    name: 'HtmlPreview',
    components: {
        BPopover,
        BSpinner,
    },
    props: {
        summaryId: {
            type: Number as PropType<number>,
            required: true,
        },
    },
    data() {
        return {
            componentId: makeId(12),
            htmlValue: null as null | string,
        }
    },
    methods: {
        previewLoaded(): void {
            console.log('preview loaded');
            if (!this.htmlValue) {
                this.loadSummaryHtml();
            }

            this.$emit('preview-loaded');
        },
        async loadSummaryHtml() {
            this.htmlValue = (await axios.get('/candidate-summaries/' + this.summaryId)).data.anonymized_cv_html;
        }
    }
})
</script>
