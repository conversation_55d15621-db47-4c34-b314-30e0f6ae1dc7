<script lang="ts">
import Vue, {defineComponent} from 'vue'
import Distance from "../Distance.vue";
import {BListGroupItem, VBTooltip} from "bootstrap-vue";
import LocationResults from "./LocationResults.vue";
import {directive as onClickaway} from "vue-clickaway";
import PlaceConfidence from "./PlaceConfidence.vue";

Vue.directive('b-tooltip', VBTooltip);

export default defineComponent({
    name: "Place",
    components: {BListGroupItem, PlaceConfidence, LocationResults, Distance},
    data() {
        return {
            showAlternatives: false,
        };
    },
    directives: {
        onClickaway: onClickaway,
    },
    props: {
        place: {
            type: Object as () => Place,
            required: true,
        },
        withLabel: {
            type: Boolean,
            default: true,
        },
        withDistances: {
            type: Array as () => Location[] | null,
            default: null,
        },
        withCache: {
            type: Array as () => Place[] | null,
            default: null,
        },
        small: {
            type: Boolean,
            default: false,
        },
    },
    methods: {
        getDistanceFromLabel(place_id: string): string {
            if (this.withDistances) {
                const location = this.withDistances.find((location: Location) => `${location.place_id}` === `${place_id}`);
                if (!location) {
                    return '';
                }
                const locationName = location.name;
                const locationAddress = location.place?.label ?? '';
                return this.$t('Distance from {locationName} ({locationAddress})', {locationName, locationAddress}).toString();
            }
            return '';
        },
        hideAlternatives() {
            this.showAlternatives = false;
        },
    },
    computed: {
        shouldShowDistances(): boolean {
            return !!this.place.distances && (!!this.withDistances || (this.place.distances[0].place_id === "0"));
        },
        tooltipText(): string {
            const alternatives = this.$t('Click to see alternatives.').toString();

            if (!this.place.confidence) {
                return alternatives;
            }

            let parts = [];
            if (this.place.confidence < 0.5) {
                parts = [
                    this.$t('This location was detected automatically with low confidence based on data provided in the CV.').toString(),
                ];
            } else if (this.place.confidence < 0.8) {
                parts = [
                    this.$t('This location was detected automatically with medium confidence based on data provided in the CV.').toString(),
                ];
            } else {
                parts = [
                    this.$t('This location was detected automatically with high confidence based on data provided in the CV.').toString(),
                ];
            }

            if (this.withCache && this.withCache.length > 1) {
                parts.push(alternatives);
            }
            return parts.join(' ');
        },
    }
})
</script>

<template>
    <div>
        <span v-if="withLabel">{{ place.label }}</span>
        <div class="autocomplete-wrapper d-inline-block ml-1" v-on-clickaway="hideAlternatives">
            <place-confidence
                :place="place"
                :small="small"
                @click="showAlternatives = !showAlternatives"
                v-b-tooltip.bottom.hover="tooltipText"
            />
            <div
                class="autocomplete-results"
                v-if="showAlternatives"
                style="min-width: 300px;"
            >
                <location-results
                    v-if="withCache"
                    :selected="place"
                    :results="withCache"
                    @select="$emit('update-place', $event)"
                >
                    <template #prepend>
                        <b-list-group-item class="text-xs bg-lighter text-body" disabled>
                            <div class="font-weight-normal">{{ $t('Original location:')}}</div>
                            <div>{{ place.original_query }}</div>
                        </b-list-group-item>
                    </template>
                </location-results>
            </div>
        </div>
        <span v-if="shouldShowDistances">
            (<template
                v-for="(distance, index) in place.distances"
            >
                <distance
                    :key="distance.place_id"
                    :distance="distance.distance"
                    v-b-tooltip.bottom.hover="getDistanceFromLabel(distance.place_id)"
                /><span v-if="index != (place.distances.length - 1)">, </span>
            </template>)
        </span>
    </div>
</template>

