<script lang="ts">
import {defineComponent} from 'vue'
import CandidateTag from "./CandidateTag.vue";
import axios from "axios";

export default defineComponent({
    name: "LandingTag",
    mixins: [CandidateTag],
    methods: {
        async updateColor() {
            await axios.put(`/landing-tags/${this.tag.id}/updateColor`, {
                light_color: this.tag.light_color,
            });
            this.$emit('update', this.tag);
        },
    },
})
</script>
