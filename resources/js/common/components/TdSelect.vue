<script lang="ts">
import {defineComponent} from 'vue'
import CheckboxDropdown from "../CheckboxDropdown.vue";
import {PropType} from "vue/types/options";

export default defineComponent({
    name: "TdSelect",
    mixins: [CheckboxDropdown],
    props: {
        value: {
            type: Number as PropType<number | null>,
        },
        showCount: {
            default: false,
        },
        showClearButton: {
            default: true,
        },
    },
    methods: {
        updateValue(value: boolean, option: BMultiSelectOption): void {
            if(!value){
                this.$emit('input', null);
            }else{
                this.$emit('input', option.value);
            }
            this.$refs.dropdown.hide();
        },
        getValue(o: BMultiSelectOption): boolean {
            return this.value === o.value;
        },
        clearValues() {
            this.$emit('input', null);
        },
    },
    computed: {
        labelText(){
            return this.value ? this.options.find(o => o.value === this.value)?.label : this.text;
        }
    }
})
</script>
