<template>
    <b-nav-item
        :id="`side-menu-item-${icon}`"
        @click.prevent="
            $emit('click');
            $inertia.visit(link);
        "
        :href="link"
    >
        <div
            class="btn btn-white btn-circle flex-shrink-0"
            :class="{'bg-light': link && $page.url == link}"
        >
            <i
                class="tdi"
                :class="icon"
            ></i>
            <div
                v-if="count && !$parent.open"
                class="badge badge-light rounded-circle"
            >
                {{ count }}
            </div>
        </div>
        <span class="ml-2 d-flex align-items-center gap-2">
            {{ text }}
            <span
                v-if="count && $parent.open"
                class="badge badge-light badge-pill"
                >{{ count }}</span
            >
        </span>
        <b-tooltip
            ref="tooltip"
            :disabled="$parent.open"
            :target="`side-menu-item-${icon}`"
            placement="right"
            triggers="hover"
        >
            {{ text }}
        </b-tooltip>
    </b-nav-item>
</template>

<script lang="ts">
import {BNavItem, BTooltip} from "bootstrap-vue";
import {defineComponent} from "vue";
import {Link} from "@inertiajs/vue2";

export default defineComponent({
    name: "side-menu-item",
    components: {
        BNavItem,
        BTooltip,
        Link,
    },
    watch: {
        "$parent.open"(v: boolean) {
            if (v) {
                // @ts-ignore
                this.$refs.tooltip.$emit("close");
            }
        },
    },
    computed: {
        to(): string | null {
            return this.link ?? null;
        },
    },
    props: {
        link: {
            type: String,
            required: false,
        },
        icon: {
            type: String,
            required: true,
        },
        text: {
            type: String,
            required: true,
        },
        count: {
            type: Number,
            required: false,
        },
    },
});
</script>
