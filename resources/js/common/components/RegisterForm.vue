<template>
    <div>
        <p>
            To save and publish your job ad, we need your contact details. Once you're authenticated,
            we'll set up the application form and you'll be able to access your candidates.
        </p>
        <p>
            Don't worry, this is free (as in free beer).
        </p>
        <b-form @submit.prevent="submit">
            <b-form-group label="Name">
                <b-form-input id="name" v-model="form.name" :state="valid('name')"></b-form-input>
                <b-form-invalid-feedback :state="valid('name')">
                    {{ errors.name ? errors.name.join(';') : null }}
                </b-form-invalid-feedback>
            </b-form-group>
            <b-form-group
                label="E-mail"
            >
                <b-form-input id="email" :state="valid('email')" v-model="form.email" type="email"></b-form-input>
                <b-form-invalid-feedback :state="valid('email')">
                    {{ errors.email ? errors.email.join(';') : null }}
                </b-form-invalid-feedback>
            </b-form-group>
            <b-form-group
                label="Password"
            >
                <b-form-input
                    :state="valid('password')"
                    v-model="form.password" type="password"></b-form-input>
                <b-form-invalid-feedback :state="valid('password')">
                    {{ errors.password ? errors.password.join(';') : null }}
                </b-form-invalid-feedback>
            </b-form-group>
            <b-form-group label="Instance name" description="You can login later on this web address.">
                <b-input-group append=".teamdash.com">
                    <b-form-input v-model="form.instance_name" :state="valid('instance_name')"
                                  placeholder="yourcompany"></b-form-input>
                </b-input-group>
                <b-form-invalid-feedback :state="valid('instance_name')">
                    {{ errors.instance_name ? errors.instance_name.join(';') : null }}
                </b-form-invalid-feedback>
            </b-form-group>
            <div class="text-center pt-2">
                <b-button type="submit" variant="primary" :disabled="saving">
                    <b-spinner small v-if="saving" class="mr-2"></b-spinner>
                    {{ saving ? 'Setting things up...' : 'Save' }}
                </b-button>
            </div>
        </b-form>
    </div>
</template>
<script lang="ts">
import Vue from 'vue';
import {PropType} from "vue/types/options";
import {BButton, BForm, BFormGroup, BFormInput, BFormInvalidFeedback, BInputGroup, BSpinner} from "bootstrap-vue";
import axios from "axios";

export default Vue.extend({
    components: {
        BForm,
        BFormGroup,
        BFormInput,
        BInputGroup,
        BButton,
        BFormInvalidFeedback,
        BSpinner,
    },
    props: {
        toCache: {
            type: Object as PropType<any>,
            default: null,
        }
    },
    data() {
        return {
            form: {
                email: '',
                name: '',
                instance_name: '',
                password: '',
                phone: 'job ad',
                volume: 'job ad',
            },
            errors: {} as { [key: string]: string[] },
            saving: false,
        }
    },
    methods: {
        valid(field: string): boolean | null {
            if (!this.errors[field]) {
                return null;
            } else {
                return false;
            }
        },
        submit() {
            this.saving = true;
            axios.post('/start-v2', {
                ...this.form,
                ...this.toCache,
                ...JSON.parse(localStorage.getItem('google_id') ?? '{}'),
            }).then(({data}) => {
                this.saving = false;
                window.analytics.track('Registered instance without email verification', {}, () => {
                    window.location.href = data.redirect_url;
                });
            }).catch(({response}) => {
                this.saving = false;
                console.log(response);
                if (response.status == 422) {
                    this.errors = response.data;
                }
            });
        }
    }
});
</script>
