<template>
    <table class="table table-td mb-0">
        <tbody>
            <tr :key="action.id" v-for="action in actions">
                <td>
                    <div class="d-flex">
                        <i class="mr-2" :class="ACTION_ICONS[action.action_type]"></i>
                        <div class="d-flex flex-column">
                            <span class="mb-1">
                                {{ action.name }}
                                <template v-if="showProject">
                                    <span>({{ action.stage?.project?.position_name }})</span>
                                </template>
                            </span>
                            <span class="font-weight-normal">{{ getActionSummary(action) }}</span>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center justify-content-end gap-2" v-if="showTableActions">
                        <a class="btn btn-sm btn-white" @click="startEditAction(action)">
                            {{ $t('Edit') }}
                        </a>
                        <a class="btn btn-sm btn-white-danger" @click="deleteAction(action)">
                            {{ $t('Delete') }}
                        </a>
                    </div>
                </td>
            </tr>
        </tbody>
        <form-modal
            v-model="showEditActionModal"
            :form-name="editActionFormName"
            :title="$t('Edit action {actionName}', {actionName: editAction?.name ?? ''})"
            :to-load="prepareActionPreFill(editAction)"
            :params="{project_id: editAction?.stage.project_id, action_id: editAction?.id}"
            @response="$emit('response', $event)"
            @success="handleActionChanged"
            size="lg"
        ></form-modal>
    </table>
</template>
<script lang="ts">
import {PropType} from "vue/types/options";
import {getSummaryForAction} from "../../actions/utils";
import {ACTION_ICONS} from "../../constants/action_types";
import FormModal from "../../forms/FormModal.vue";
import axios from "axios";
import {reject} from "lodash";
import {confirmDelete} from "../util";

export default {
    name: 'ActionsTable',
    components: {FormModal},
    props: {
        actions: {
            type: Array as PropType<Action[]>,
            required: true
        },
        showProject: {
            type: Boolean,
            default: false
        },
        showTableActions: {
            type: Boolean,
            default: true
        },
    },
    data() {
        return {
            editAction: null as Action | null,
        }
    },
    methods: {
        getActionSummary(action: Action): string {
            return getSummaryForAction(action, this.project);
        },
        handleActionChanged(res: { payload: { action: Action } }) {
            const updatedAction = res.payload.action;
            const index = this.actions.findIndex(action => action.id === updatedAction.id);
            this.actions.splice(index, 1, updatedAction);
        },
        async startEditAction(action: Action) {
            this.editAction = (await axios.get(`/actions/${action.id}/getActionFormDataToLoad`)).data;
        },
        async deleteAction(action: Action): Promise<void> {
            const confirmed = await confirmDelete(
                this,
                [this.$t('Are you sure you want to delete action {actionName}?', {actionName: action.name}).toString()],
            )
            if (!confirmed) {
                return;
            }

            try {
                await axios.delete(`/actions/${action.id}`);

                const remainingActions = reject(this.actions, (a: Action) => a.id === action.id);
                this.$emit('actions-updated', remainingActions);
            } catch (e) {
                this.$bvToast.toast(this.$t('There was an error removing this stage action.').toString(), {
                    title: this.$t('An error occurred').toString(),
                    variant: 'danger',
                    solid: true
                });
            }
        },
        prepareActionPreFill(action: null | Action): null | Action {
            if (action === null || action.trigger !== 'delayed_after_form_submission') {
                return action;
            }

            return {
                ...action,
                form_type: action.form_type ?? 'all',
            };
        },
    },
    computed: {
        ACTION_ICONS() {
            return ACTION_ICONS
        },
        showEditActionModal: {
            get(): boolean {
                return !!this.editAction;
            },
            set(val: boolean) {
                if (!val) {
                    this.editAction = null;
                }
            }
        },
        editActionFormName(): string {
            if (!this.editAction) {
                return '';
            }

            return this.editAction.form_name.split('\\').slice(-1)[0];
        },
    }
}
</script>
