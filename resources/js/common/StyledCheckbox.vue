<template>
    <div
        class="checkbox-wrapper-4"
        :class="{
            'checkbox-wrapper-4--lg': size === 'lg',
            'checkbox-wrapper-4--sm': size === 'sm',
            'checkbox-wrapper-4--no-label': !$scopedSlots.default
        }"
    >
        <input
            :name="name"
            v-model="internalValue"
            class="styled-checkbox"
            :id="id || `ss-cb-item-${_uid}`"
            type="checkbox"
            :disabled="disabled"
            v-if="trueValue !== undefined && falseValue !== undefined"
            :true-value="trueValue"
            :false-value="falseValue"
            @change="$emit('change', $event)"
        >
        <input
            v-else
            :name="name"
            v-model="internalValue"
            :value="inputValue"
            class="styled-checkbox"
            :id="id || `ss-cb-item-${_uid}`"
            type="checkbox"
            :disabled="disabled"
            @change="$emit('change', $event)"
        >
        <label
            :for="id || `ss-cb-item-${_uid}`"
            class="form-check-label"
            :class="labelClass"
            :style="{'white-space': noWrap ? 'nowrap' : 'initial'}"
        >
            <span class="checkbox non-fl-hide" :class="checkboxClass">
                <svg :width="svgWidth" :height="svgHeight">
                <use xlink:href="#check-4"></use>
                </svg>
            </span>
            <span class="form-check-label--text">
                <slot></slot>
            </span>
            <svg class="inline-svg non-fl-hide">
                <symbol id="check-4" viewBox="0 0 12 10">
                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                </symbol>
            </svg>
        </label>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';

export default Vue.extend({
    props: {
        name: {
            type: String as () => string,
        },
        id: {
            type: String as () => string,
        },
        value: {
            type: true as unknown as () => any,
        },
        noWrap: {
            type: Boolean as () => boolean,
            default: false,
        },
        size: {
            type: String as () => string,
            default: 'md',
        },
        labelClass: {
            type: String as () => string,
            default: '',
        },
        checkboxClass: {
            type: String as () => string,
            default: '',
        },
        inputValue: {
            type: null as unknown as () => any,
        },
        disabled: {
            type: Boolean as () => boolean,
            default: false,
        },
        trueValue: {
            type: null as unknown as () => any,
        },
        falseValue: {
            type: null as unknown as () => any,
        },
    },
    computed: {
        internalValue: {
            get(): any {
                return this.value;
            },
            set(v: any) {
                this.$emit('input', v);
            }
        },
        svgWidth(): string {
            switch (this.size) {
                case 'lg':
                    return '20px';
                case 'sm':
                    return '11px';
                default:
                    return '12px';
            }
        },
        svgHeight(): string {
            switch (this.size) {
                case 'lg':
                    return '14px';
                case 'sm':
                    return '9px';
                default:
                    return '10px';
            }
        },
    }
});
</script>
