<template>
    <div class="duration-picker">
        <b-button-group
            size="sm"
            class="flex-shrink-0"
        >
            <b-button
                :class="['flex-nowrap', {active: value && value.period_type === period.value}]"
                variant="white"
                v-for="period in filteredPeriods"
                :key="period.value"
                @click="updatePeriodType(period.value)"
            >
                {{ period.label }}
            </b-button>
            <b-dropdown
                :toggle-class="['flex-nowrap', {active: value && value.period_type === 'custom'}]"
                :text="displayString"
                variant="white"
                no-flip
                size="sm"
            >
                <div class="p-2">
                    <div class="d-flex">
                        <div class="mr-1">
                            <div class="mb-1">
                                {{ $t("From") }}
                            </div>
                            <calendar
                                class="start-calendar"
                                :value="calendarStartDate"
                                value-as-date
                                @selected="updatePeriodStart"
                                :max="calendarEndDate"
                                :date-info-fn="calendarStyler"
                                start-weekday="1"
                            ></calendar>
                        </div>
                        <div class="ml-1">
                            <div class="mb-1">
                                {{ $t("Until") }}
                            </div>
                            <calendar
                                class="end-calendar"
                                :value="calendarEndDate"
                                value-as-date
                                @selected="updatePeriodEnd"
                                :min="calendarStartDate"
                                :date-info-fn="calendarStyler"
                                start-weekday="1"
                            ></calendar>
                        </div>
                    </div>
                </div>
            </b-dropdown>
        </b-button-group>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import {BButton, BButtonGroup, BDropdown} from "bootstrap-vue";
import {DateTime} from "luxon";
import {PropType} from "vue/types/options";
import classNames from "classnames";
import Calendar from "./Calendar.vue";
import {formatDateTime} from "./datetime";

export default Vue.extend({
    name: "DurationPicker",
    components: {BButtonGroup, BButton, BDropdown, Calendar},
    props: {
        value: {
            type: Object as PropType<DurationPickerValue | null>,
        },
        showPeriods: {
            type: Array as PropType<string[]>,
            default: () => ["last_week", "last_month", "last_3_months", "last_6_months", "last_12_months"],
        },
        defaultPeriod: {
            type: String as PropType<string>,
            default: "last_12_months",
        },
        allowReset: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
    },
    mounted() {
        if (!this.value) {
            this.updatePeriodType(this.defaultPeriod);
        }
    },
    methods: {
        getDatesFromPeriod(period: string): [DateTime | null, DateTime | null] {
            const currentDate = DateTime.now();
            let startDate;
            let endDate;
            switch (period) {
                case "this_week":
                    startDate = currentDate.startOf("day");
                    endDate = currentDate.endOf("day").plus({weeks: 1});
                    break;
                case "tomorrow":
                    startDate = currentDate.startOf("day").plus({days: 1});
                    endDate = currentDate.endOf("day").plus({days: 1});
                    break;
                case "today":
                    startDate = currentDate.startOf("day");
                    endDate = currentDate.endOf("day");
                    break;
                case "last_week":
                    startDate = currentDate.startOf("week").minus({weeks: 1});
                    endDate = startDate.endOf("week");
                    break;
                case "last_month":
                    startDate = currentDate.minus({months: 1});
                    endDate = currentDate;
                    break;
                case "last_3_months":
                    startDate = currentDate.minus({months: 3});
                    endDate = currentDate;
                    break;
                case "last_6_months":
                    startDate = currentDate.minus({months: 6});
                    endDate = currentDate;
                    break;
                case "last_12_months":
                    startDate = currentDate.minus({months: 12});
                    endDate = currentDate;
                    break;
                default:
                    return [null, null];
            }

            return [startDate.startOf("day"), endDate.endOf("day")];
        },
        updatePeriodType(period: string): void {
            if (this.allowReset && this.value && this.value.period_type === period) {
                this.$emit("input", {
                    ...this.value,
                    period_type: "none",
                    period: [null, null],
                });
                return;
            }

            const [startDate, endDate] = this.getDatesFromPeriod(period);

            this.$emit("input", {
                ...this.value,
                period_type: period,
                period: [
                    startDate ? startDate.startOf("day").toISO() : null,
                    endDate ? endDate.endOf("day").toISO() : null,
                ],
            });
        },
        updatePeriodStart(ymd: string, date: Date): void {
            this.$emit("input", {
                ...this.value,
                period_type: "custom",
                period: [DateTime.fromJSDate(date).startOf("day").toISO(), this.calendarEndDate],
            });
        },
        updatePeriodEnd(ymd: string, date: Date): void {
            this.$emit("input", {
                ...this.value,
                period_type: "custom",
                period: [this.calendarStartDate, DateTime.fromJSDate(date).endOf("day").toISO()],
            });
        },
        calendarStyler(ymd: string, date: Date): string {
            return classNames({
                "period-start": this.startDateDT && DateTime.fromJSDate(date).hasSame(this.startDateDT, "day"),
                "period-end": this.endDateDT && DateTime.fromJSDate(date).hasSame(this.endDateDT, "day"),
                "table-info":
                    this.startDateDT &&
                    this.endDateDT &&
                    date >= this.startDateDT.toJSDate() &&
                    date <= this.endDateDT.toJSDate(),
            });
        },
    },
    computed: {
        calendarStartDate(): string | null {
            if (!this.value) {
                return null;
            }
            if (this.value?.period_type === "custom" && this.value?.period?.[0]) {
                return this.value?.period?.[0];
            } else {
                const [startDate] = this.getDatesFromPeriod(this.value?.period_type);
                return startDate ? startDate.toISO() : null;
            }
        },
        calendarEndDate(): string | null {
            if (!this.value) {
                return null;
            }
            if (this.value?.period_type === "custom" && this.value?.period?.[1]) {
                return this.value?.period?.[1];
            } else {
                const [_, endDate] = this.getDatesFromPeriod(this.value?.period_type);
                return endDate ? endDate.toISO() : null;
            }
        },
        displayString(): string {
            if (!this.calendarStartDate && !this.calendarEndDate) {
                return this.$t("durationpicker.custom").toString();
            }
            const startDate = this.calendarStartDate ? formatDateTime(this.calendarStartDate) : "...";
            const endDate = this.calendarEndDate ? formatDateTime(this.calendarEndDate) : "...";
            return `${startDate} – ${endDate}`;
        },
        startDateDT(): DateTime | null {
            return this.calendarStartDate ? DateTime.fromISO(this.calendarStartDate) : null;
        },
        endDateDT(): DateTime | null {
            return this.calendarEndDate ? DateTime.fromISO(this.calendarEndDate) : null;
        },
        availablePeriods(): FormListItem[] {
            return [
                {label: this.$t("12M").toString(), value: "last_12_months"},
                {label: this.$t("6M").toString(), value: "last_6_months"},
                {label: this.$t("90D").toString(), value: "last_3_months"},
                {label: this.$t("30D").toString(), value: "last_month"},
                {label: this.$t("Previous week").toString(), value: "last_week"},
                // future
                {label: this.$t("Today").toString(), value: "today"},
                {label: this.$t("Tomorrow").toString(), value: "tomorrow"},
                {label: this.$t("This week").toString(), value: "this_week"},
            ];
        },
        filteredPeriods(): FormListItem[] {
            return this.availablePeriods?.filter(available => this.showPeriods.includes(available.value)) ?? [];
        },
    },
});
</script>

<style scoped lang="scss">
.btn-group::v-deep {
    .btn-white-default {
        border: none;
        z-index: 0;

        &:hover,
        &:focus,
        &:active,
        &.active,
        &[aria-expanded="true"] {
            background-color: color-mix(in srgb, var(--light), black 5%);
        }

        &:not(:disabled):not(.disabled):active,
        &:not(:disabled):not(.disabled).active,
        &:not(:disabled):not(.disabled)[aria-expanded="true"] {
            box-shadow: 0px 1px 4px RGBA(0, 0, 0, 0.25);
        }
    }
}

.b-calendar::v-deep {
    .b-calendar-grid-body {
        .col.period-start {
            border-top-left-radius: 50%;
            border-bottom-left-radius: 50%;
        }

        .col.period-end {
            border-top-right-radius: 50%;
            border-bottom-right-radius: 50%;
        }
    }
}
</style>
