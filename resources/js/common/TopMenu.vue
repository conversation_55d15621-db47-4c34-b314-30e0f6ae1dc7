<script lang="ts">
import {defineComponent} from "vue";
import {Link} from "@inertiajs/vue2";
import {ROLE_ADMIN, ROLE_LIMITED} from "../constants/user_roles";
import {BDropdown, BDropdownItem, BSpinner} from "bootstrap-vue";
import {TranslateResult} from "vue-i18n";
import {FeatureControl, FeatureControls} from "../@types/rl-settings";
import UpgradeBadge from "./UpgradeBadge.vue";

interface TopMenuDropdownItem {
    label: string | TranslateResult;
    route: string;
    inertia?: boolean;
    control: keyof FeatureControls;
}

export default defineComponent({
    name: "TopMenu",
    components: {
        UpgradeBadge,
        BSpinner,
        Link,
        BDropdown,
        BDropdownItem,
    },
    methods: {
        checkPermission(control: keyof FeatureControl | string) {
            // if contains .
            if (control.includes(".")) {
                let [parent, child] = control.split(".") as [keyof FeatureControls, keyof FeatureControl];
                return this.$rlSettings.controls[parent][child];
            } else {
                return this.$rlSettings.controls[control as keyof FeatureControls].view;
            }
        },
        getClasses(item: TopMenuDropdownItem) {
            if (item.control === undefined) {
                return {};
            }
            const disabled = !this.checkPermission(item.control);
            return {
                disabled: disabled,
                "text-muted": disabled || this.showUpgradeBadge(item),
                "pointer-events-none": disabled && !this.showUpgradeBadge(item),
            };
        },
        showUpgradeBadge(item: TopMenuDropdownItem) {
            if (this.$rlSettings.user.role === ROLE_LIMITED) {
                return false;
            }
            return item.control && !this.checkPermission(item.control);
        },
        getLink(item: TopMenuDropdownItem) {
            let featureHelpUrl = null;

            switch (item.control) {
                case "videoInterviews":
                    featureHelpUrl = "https://support.teamdash.com/en/articles/6193251-asynchronous-video-interviews";
                    break;
                case "requisitions":
                    featureHelpUrl = "https://support.teamdash.com/en/articles/8275471-job-requisitions";
                    break;
                case "scorecards":
                    featureHelpUrl = "https://support.teamdash.com/en/articles/7850420-scorecards";
                    break;
                default:
                    featureHelpUrl = "https://support.teamdash.com";
                    break;
            }

            if (featureHelpUrl && this.showUpgradeBadge(item)) {
                return featureHelpUrl;
            }

            return this.route(item.route);
        },
    },
    computed: {
        ROLE_ADMIN() {
            return ROLE_ADMIN;
        },
        topMenuItems() {
            return [
                {
                    label: this.$t("Projects"),
                    route: "projects.index",
                    matchActive: "projects.*",
                    inertia: true,
                },
                ...(this.$rlSettings.user.role === ROLE_LIMITED
                    ? [
                          {
                              label: this.$t("Requisitions"),
                              route: "requisitions.index",
                              matchActive: "requisitions.*",
                              inertia: false,
                              control: "requisitions",
                          },
                      ]
                    : []),
                {
                    label: this.$t("Job ads"),
                    route: "landings.index",
                    matchActive: "landings.*",
                    inertia: true,
                    control: "landings",
                },
                {
                    label: this.$t("Talent Pool"),
                    route: "candidates.index",
                    matchActive: "candidates.*",
                    inertia: false,
                    control: "candidates.update",
                },
                ...(this.$rlSettings.organization_type === "agency"
                    ? [
                          {
                              // clients
                              label: this.$t("Clients"),
                              route: "clients.index",
                              matchActive: "clients.*",
                              inertia: false,
                              control: "clients",
                          },
                      ]
                    : []),
                {
                    // calendar
                    label: this.$t("Calendar"),
                    route: "interviews.index",
                    matchActive: "interviews.*",
                    inertia: true,
                    control: "interviews",
                },
                ...(this.$page.props.latestProject && !this.route().current("projects.show")
                    ? [
                          {
                              label: this.$page.props.latestProject.position_name,
                              url: this.route("projects.show", {project: this.$page.props.latestProject.id}),
                              matchActive: "projects.show",
                              inertia: true,
                          },
                      ]
                    : []),
                ...(this.$rlSettings.is_superadmin
                    ? [
                          {
                              label: this.$t("Admin"),
                              route: "admin.website.index",
                              matchActive: "admin.*",
                              inertia: true,
                          },
                      ]
                    : []),
            ];
        },
        topMenuDropdownItems(): TopMenuDropdownItem[] {
            return [
                {
                    label: this.$t("Video interviews"),
                    route: "video-interviews.index",
                    control: "videoInterviews",
                    inertia: true,
                },
                {
                    label: this.$t("Forms"),
                    route: "forms.index",
                    control: "forms",
                    inertia: true,
                },
                {
                    label: this.$t("Message templates"),
                    route: "templates.index",
                    control: "templates",
                },
                {
                    label: this.$t("Published job ads"),
                    route: "structured-jobs.index",
                    control: "landings",
                },
                {
                    label: this.$t("Job requisitions"),
                    route: "requisitions.index",
                    control: "requisitions",
                },
                {
                    label: this.$t("Integrations"),
                    route: "integrations.index",
                    control: "integrations",
                },
                {
                    label: this.$t("Scorecards"),
                    route: "scorecards.index",
                    control: "scorecards",
                },
                {
                    label: this.$t("Messages"),
                    route: "messages.index",
                    control: "messages",
                },
            ];
        },
    },
});
</script>

<template>
    <nav class="navbar navbar-expand-md">
        <div :class="$page.props.navbar_container_class || 'container'">
            <div class="navbar-brand">
                <Link :href="route('projects.index')">
                    <img
                        style="width: 140px; height: auto"
                        src="/img/teamdash_logo_black.svg"
                        alt="Teamdash Logo"
                    />
                </Link>
            </div>
            <div class="collapse navbar-collapse">
                <ul class="navbar-nav mr-auto">
                    <li
                        class="nav-item"
                        v-for="item in topMenuItems"
                        :key="item.label"
                        :class="{
                            active: route().current(item.matchActive),
                        }"
                    >
                        <component
                            class="nav-link"
                            :class="getClasses(item)"
                            :is="item.inertia ? 'Link' : 'a'"
                            :href="item.url ? item.url : route(item.route)"
                            >{{ item.label }}
                        </component>
                    </li>
                    <b-dropdown
                        no-caret
                        variant="link"
                        menu-class="nav-item"
                        toggle-class="px-0"
                    >
                        <template #button-content>
                            <img
                                src="/img/icon-more.svg"
                                alt=""
                            />
                        </template>
                        <li
                            v-for="item in topMenuDropdownItems"
                            :key="item.label.toString()"
                        >
                            <component
                                class="dropdown-item"
                                :is="item.inertia ? 'Link' : 'a'"
                                :href="getLink(item)"
                                :class="getClasses(item)"
                                :target="showUpgradeBadge(item) ? '_blank' : null"
                            >
                                {{ item.label }}
                                <upgrade-badge
                                    :text="''"
                                    tag="span"
                                    v-if="showUpgradeBadge(item)"
                                    badge-bottom-margin=""
                                ></upgrade-badge>
                            </component>
                        </li>
                    </b-dropdown>
                </ul>
                <div class="navbar-nav align-items-center gap-1">
                    <Link
                        class="btn btn-circle btn-dark"
                        :href="route('users.create')"
                        :title="$t('Add user').toString()"
                        v-if="$rlSettings.user.role === ROLE_ADMIN"
                    >
                        <i
                            class="tdi td-add-user"
                            :alt="$t('Add user').toString()"
                        ></i>
                    </Link>
                    <Link
                        :title="$rlSettings.user.name"
                        :href="route('users.edit', {user: $rlSettings.user.id})"
                        class="btn btn-white btn-circle p-0 avatar"
                    >
                        <div
                            class="avatar-image"
                            :style="{backgroundImage: `url('${$rlSettings.user.avatar_url}')`}"
                        ></div>
                    </Link>
                    <Link
                        :title="$t('Organization settings').toString()"
                        :href="route('organization.settings')"
                        class="btn btn-white btn-circle"
                        v-if="$rlSettings.user.role === ROLE_ADMIN"
                    >
                        <i
                            class="tdi td-settings"
                            :alt="$t('Settings').toString()"
                        ></i>
                    </Link>
                </div>
            </div>
        </div>
    </nav>
</template>
