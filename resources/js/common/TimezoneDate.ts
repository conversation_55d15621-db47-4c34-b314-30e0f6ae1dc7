// Import Luxon's DateTime class
import {DateTime, IANAZone} from "luxon";

export class TimezoneDate extends Date {
    private _timezone: string;

    constructor();
    constructor(value: number | string | Date, timezone?: string);
    constructor(
        year: number,
        month: number,
        date?: number,
        hours?: number,
        minutes?: number,
        seconds?: number,
        ms?: number,
        timezone?: string
    );
    constructor(...args: any[]) {
        // Extract timezone if provided
        const timezone = typeof args[args.length - 1] === "string" ? args.pop() : "UTC";

        if (args.length === 1 && args[0] instanceof Date && !(args[0] instanceof TimezoneDate)) {
            const date = args[0];
            // Adjust the date to the specified timezone
            const adjustedDate = TimezoneDate.adjustDateToTimezone(date, timezone);
            super(adjustedDate.getTime());
        } else {
            super(...(args as [any]));
        }

        this._timezone = timezone;
    }

    /**
     * Sets the timezone using an IANA timezone name.
     * @param timezone - The timezone to set (e.g., 'Europe/Lisbon', 'America/Los_Angeles')
     */
    public setTimezone(timezone: string): void {
        if (!IANAZone.isValidZone(timezone)) {
            throw new Error(`Invalid timezone: ${timezone}`);
        }
        this._timezone = timezone;
    }

    /**
     * Gets the currently set timezone.
     * @returns The currently set timezone.
     */
    public getTimezone(): string {
        return this._timezone;
    }

    /**
     * Helper method to get the DateTime object in the specified timezone.
     * @returns A Luxon DateTime object adjusted to the specified timezone.
     */
    private _getDateTimeInZone(): DateTime {
        const dateTime = DateTime.fromJSDate(this).setZone(this._timezone);
        if (!dateTime.isValid) {
            throw new Error("Invalid DateTime object.");
        }
        return dateTime;
    }

    // Override time-related methods to return values in the specified timezone

    /**
     * Gets the hours (0-23) in the specified timezone.
     * @returns The hour in the specified timezone.
     */
    public getHours(): number {
        return this._getDateTimeInZone().hour;
    }

    /**
     * Sets the hours (0-23) in the specified timezone.
     */
    public setHours(hours: number, min?: number, sec?: number, ms?: number): number {
        const dateTime = this._getDateTimeInZone().set({hour: hours, minute: min, second: sec, millisecond: ms});
        if (!dateTime.isValid) {
            throw new Error("Invalid DateTime object.");
        }
        this.setTime(dateTime.toJSDate().getTime());
        return this.getTime();
    }

    /**
     * Gets the minutes (0-59) in the specified timezone.
     * @returns The minutes in the specified timezone.
     */
    public getMinutes(): number {
        return this._getDateTimeInZone().minute;
    }

    /**
     * Gets the seconds (0-59) in the specified timezone.
     * @returns The seconds in the specified timezone.
     */
    public getSeconds(): number {
        return this._getDateTimeInZone().second;
    }

    /**
     * Gets the milliseconds (0-999) in the specified timezone.
     * @returns The milliseconds in the specified timezone.
     */
    public getMilliseconds(): number {
        return this._getDateTimeInZone().millisecond;
    }

    /**
     * Gets the full year (e.g., 2023) in the specified timezone.
     * @returns The full year in the specified timezone.
     */
    public getFullYear(): number {
        return this._getDateTimeInZone().year;
    }

    /**
     * Gets the month (0-11) in the specified timezone.
     * @returns The month in the specified timezone.
     */
    public getMonth(): number {
        return this._getDateTimeInZone().month - 1; // Adjust because JavaScript months are 0-indexed
    }

    /**
     * Gets the date of the month (1-31) in the specified timezone.
     * @returns The date in the specified timezone.
     */
    public getDate(): number {
        return this._getDateTimeInZone().day;
    }

    /**
     * Gets the day of the week (0-6) in the specified timezone.
     * @returns The day of the week in the specified timezone.
     */
    public getDay(): number {
        const weekday = this._getDateTimeInZone().weekday; // 1 (Monday) to 7 (Sunday)
        return weekday % 7; // Map 7 (Sunday) to 0
    }

    /**
     * Gets the time value in milliseconds since January 1, 1970, 00:00:00 UTC, in the specified timezone.
     * @returns The time value in milliseconds in the specified timezone.
     */
    public getTimeInZone(): number {
        return this._getDateTimeInZone().toMillis();
    }

    /**
     * Adjusts the date to the selected timezone.
     * @param {Date} originalDate - The Date object received from Vue-cal.
     * @param {string} selectedTimezone - The IANA timezone string (e.g., 'America/Los_Angeles').
     * @returns {Date} - A new Date object representing the same local time in the selected timezone.
     */
    private static adjustDateToTimezone(originalDate: Date, selectedTimezone: string): Date {
        // Extract the local components from originalDate
        const localYear = originalDate.getFullYear();
        const localMonth = originalDate.getMonth() + 1; // JavaScript months are 0-based; Luxon uses 1-based months
        const localDay = originalDate.getDate();
        const localHour = originalDate.getHours();
        const localMinute = originalDate.getMinutes();
        const localSecond = originalDate.getSeconds();
        const localMillisecond = originalDate.getMilliseconds();

        // Create a Luxon DateTime in the selected timezone with the same components
        const dtInSelectedZone = DateTime.fromObject(
            {
                year: localYear,
                month: localMonth,
                day: localDay,
                hour: localHour,
                minute: localMinute,
                second: localSecond,
                millisecond: localMillisecond,
            },
            {zone: selectedTimezone}
        );

        // Check if the DateTime object is valid
        if (!dtInSelectedZone.isValid) {
            throw new Error("Invalid DateTime object.");
        }

        // Convert the DateTime back to a JavaScript Date object
        return dtInSelectedZone.toJSDate();
    }

    // Additional overridden methods if needed
    // You can follow the same pattern to override more methods
}
