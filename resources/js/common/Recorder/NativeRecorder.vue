<template>
    <div>
        <div>
            <div v-if="open">
                <div v-if="!isMobileSafari">
                    <video :id="`recorder-${_uid}`"
                           ref="videoEl"
                           autoplay muted
                           style="width: 100%;"
                           :class="{
                               'flipped-video': !videoBlob,
                           }"
                           v-show="!videoBlob"
                           playsinline
                    ></video>
                    <video
                        ref="playbackVideo"
                        controls
                        playsinline
                        style="width: 100%;"
                        v-show="videoBlob"
                    ></video>
                </div>
                <slot name="postvideo" v-if="!isMobileSafari"></slot>
                <div v-else>
                    <div class="mb-2">
                        <label :for="`video-file-input-`+_uid"
                            class="btn text-center d-block"
                               :class="[videoBlob ? 'btn-outline-secondary' : 'btn-dark']"
                        >
                            <template v-if="!videoBlob">
                                {{$t('Record response')}}
                            </template>
                            <template v-else>
                                {{$t('Change response')}}
                            </template>
                        </label>
                        <input type="file" accept="video/*" capture
                               ref="videoInput"
                               :id="`video-file-input-`+_uid"
                               style="visibility: hidden; height: 1px; position: absolute;"
                               :class="{'record-video-file-input--ok': videoBlob}"
                               class="record-video-file-input"
                               @change="handleVideoInputChange($event)">
                    </div>
                    <div class="d-flex justify-content-center">
                        <button class="btn btn-success btn-sm" v-if="videoBlob && !uploading" :disabled="uploading"
                                @click.prevent="startUpload()"
                        >
                            <b-spinner small v-if="uploading"></b-spinner>
                            {{ $t('Confirm') }}
                        </button>
                    </div>
                </div>
                <div class="d-flex justify-content-around mt-2" v-if="!isMobileSafari && !hideControls">
                    <button class="btn btn-white-danger btn-sm" v-if="!videoBlob && !recording"
                            @click.prevent="startRecord();"
                    >
                        <i class="fas fa-circle text-sm mr-2"></i>
                        {{ $t('Record') }}
                    </button>
                    <button class="btn btn-white-danger btn-sm" v-if="!videoBlob && recording"
                            @click.prevent="stopRecord(); "
                    >
                        <i class="fas fa-circle text-sm mr-2"></i>
                        {{ $t('recorder.stop') }}
                    </button>
                    <button v-if="videoBlob" class="btn btn-white-warning btn-sm"
                            v-b-tooltip="$t('record_again')"
                            @click.prevent="retry()"
                    >
                        <i class="fas fa-undo text-sm"></i>
                    </button>
                    <button class="btn btn-success btn-sm" v-if="videoBlob && !uploading" :disabled="uploading"
                            @click.prevent="startUpload()"
                    >
                        <b-spinner small v-if="uploading"></b-spinner>
                        {{ $t('Confirm') }}
                    </button>
                </div>
                <div v-if="videoBlob && !value" class="alert alert-danger mt-2">
                    {{ $t('warning_not_sent') }}
                </div>
                <div v-if="uploading" class="mt-2">
                    <b-progress :value="uploadPercentage"
                                :max="100" animated></b-progress>
                </div>
            </div>
            <div v-else class="row">
                <div class="col-4"></div>
                <div class="col-8">
                    <button class="btn btn-success btn-sm" @click.prevent="$emit('input', null)" v-if="value">
                        {{ $t('Record new video') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import Vue from 'vue';

import {BProgress, BSpinner, ToastPlugin, VBTooltip} from "bootstrap-vue";
import axios from 'axios';
import {isMobileSafari, reportToSentry} from "../config";

Vue.use(ToastPlugin);
Vue.directive('b-tooltip', VBTooltip);

interface HTMLMediaElementWithCaptureStream extends HTMLMediaElement {
    captureStream(): MediaStream;
}

export default Vue.extend({
    components: {
        BSpinner,
        BProgress,
    },
    props: {
        value: {
            type: Number as () => number | null,
            required: false,
        },
        open: {
            type: Boolean as () => boolean,
        },
        autoload: {
            default: true
        },
        hideControls: {
            default: false,
            type: Boolean as () => boolean,
        },
    },
    data() {
        return {
            videoBlob: null as null | Blob,
            videoBuffer: [] as Blob[],
            uploading: false,
            uid: null as string | number | null,
            uploadPercentage: 0,
            recording: false,
            retriesLeft: 3,
            recorder: null as null | MediaRecorder,
        };
    },
    mounted() {
        if (this.open) {
            this.startPlayer();
        }
    },
    beforeDestroy() {
        this.destroyPlayer();
    },
    methods: {
        startUpload() {
            this.uploading = true;
            this.$emit('uploadstart', true);
            this.uploadPercentage = 0;

            axios.post<Video>(this.route('videos.initialize')).then(res => {
                console.log(res);
                let url = this.route('videos.upload', {video: res.data.token});
                this.uid = res.data.id;
                let data = new FormData;
                if (this.videoBlob) {
                    if (this.isMobileSafari) {
                        // @ts-ignore
                        data.append('file', this.videoBlob);
                    } else {
                        data.append('file', new File([this.videoBlob], 'video.mkv'));
                    }
                }
                var instance = axios.create();
                delete instance.defaults.headers.common['X-Requested-With'];
                return instance.post(url, data, {
                    onUploadProgress: (progressEvent) => {
                        this.uploadPercentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        this.$emit('progress', this.uploadPercentage);
                    }
                });
            }).then((res) => {
                console.log(res);
                this.$emit('input', this.uid);
                this.uploading = false;
                this.$emit('close');
            }).catch((err) => {
                if (this.retriesLeft > 0) {
                    setTimeout(() => {
                        this.startUpload();
                    }, 500);
                    this.retriesLeft--;
                } else {
                    this.$bvToast.toast(`Sometimes technology breaks in unexpected ways. This does not usually happen and we are very sorry. Please refresh this page and try again.`, {
                        title: 'Your response was not uploaded.',
                        variant: 'danger',
                        solid: true,
                        noAutoHide: true,
                    });
                    this.uploading = false;
                    this.downloadRecordedVideo();
                    this.$emit('close');
                    reportToSentry(err);
                }

            });
        },
        async startPlayer() {
            if (this.isMobileSafari) {
                return false;
            }
            let stream;
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: true,
                });
            } catch (e) {
                // @ts-ignore
                this.$emit('error', e.message);
                return;
            }

            const videoEl = this.getVideoEl();

            if(!videoEl){
                return;
            }

            videoEl.controls = false;
            videoEl.autoplay = true;
            videoEl.muted = true;
            videoEl.srcObject = stream;

            videoEl.captureStream = videoEl.captureStream
                // @ts-ignore
                || videoEl.mozCaptureStream // firefox
                || (() => videoEl.srcObject) // safari; see https://stackoverflow.com/a/73480671/12640886
            ;
        },
        startRecord() {
            const videoEl = this.getVideoEl();

            // get video bitrate from video_bitrate url parameter and fall back to 1500000 if it's not set
            const videoBitrate = new URLSearchParams(window.location.search).get('video_bitrate') || '1500000';
            const mimeType = new URLSearchParams(window.location.search).get('video_mime') || undefined;

            this.recorder = new MediaRecorder(videoEl.captureStream(), {
                videoBitsPerSecond: parseInt(videoBitrate),
                mimeType: mimeType,
            });

            this.videoBuffer = [];

            this.recorder.ondataavailable = (event) => {
                if(event.data.size === 0){
                    return;
                }
                this.videoBuffer.push(event.data);
            };

            this.recorder.onstop = () => {
                this.videoBlob = new Blob([...this.videoBuffer],
                    {
                        type: "video/mp4", // the actual video format is whaterver the browser prefers
                    });
                this.$nextTick(() => {
                    this.beginShowingTheRecordedVideo();
                })
            }

            this.recorder.onerror = (errEvent) => {
                console.error(errEvent);
                console.log('recorder', this.recorder);
                const types = [
                    "video/webm",
                    "audio/webm",
                    "video/webm;codecs=vp8",
                    "video/webm;codecs=daala",
                    "video/webm;codecs=h264",
                    "audio/webm;codecs=opus",
                    "video/mp4",
                ];

                for (const type of types) {
                    console.log(
                        `Is ${type} supported? ${
                            MediaRecorder.isTypeSupported(type) ? "Maybe!" : "Nope :("
                        }`,
                    );
                }

                this.$emit('error', errEvent.error.message);
            }

            this.recorder.start(2000);
            this.$emit('start');
            this.recording = true;
        },
        stopRecord() {
            if (!this.recorder) {
                throw new Error("Tried to stop recording, but recorder doesnt exist");
            }
            this.recorder.stop(); // this raises dataavailable and stop events
            this.recording = false;
            this.$emit('stop');
        },
        beginShowingTheRecordedVideo() {
            if (!this.videoBlob) {
                // this should not happen, of course
                throw new Error('No video blob');
            }
            console.log(this.videoBlob);

            // @ts-ignore
            this.$refs.playbackVideo.src = URL.createObjectURL(this.videoBlob);
        },
        async retry() {
            this.videoBlob = null;
            await this.startPlayer();
            this.$emit('retry');
        },
        destroyPlayer() {
            console.log('destroying player');
            if(!this.getVideoEl()){
                return;
            }

            this.getVideoEl().pause();
            // @ts-ignore
            this.getVideoEl().srcObject?.getTracks().forEach((track) => {
                track.stop();
            });
            this.getVideoEl().srcObject = null;
            this.getVideoEl().src = '';
            this.getVideoEl().load();
            console.log('destroyed player');

            return;
        },
        downloadRecordedVideo() {
            if (!this.videoBlob) {
                throw new Error('No video blob');
            }
            const url = URL.createObjectURL(this.videoBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'video.mp4';
            a.click();
            URL.revokeObjectURL(url);
        },
        handleVideoInputChange(e: any) {
            if (e.target.files.length) {
                this.videoBlob = e.target.files[0] as Blob;
            } else {
                this.videoBlob = null;
            }
        },
        getVideoEl(): HTMLMediaElementWithCaptureStream {
            return this.$refs.videoEl as HTMLMediaElementWithCaptureStream;
        },
    },
    watch: {
        open(v) {
            if (v) {
                Vue.nextTick(() => {
                    this.startPlayer();
                })
            } else {
                try {
                    this.destroyPlayer();
                } catch (e) {
                    console.error(e);
                }
            }
        }
    },
    computed: {
        isMobileSafari(): boolean {
            return isMobileSafari();
        }
    }
});

</script>
