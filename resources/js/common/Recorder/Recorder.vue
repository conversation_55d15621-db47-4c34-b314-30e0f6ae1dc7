<template>
    <div>
        <div v-if="isOldEdgeBrowser" class="mb-2">
            {{ $t('You are using an outdated version of MS Edge. All features might not work as intended.') }}
            <i18n path="Please update {updateLink} or contact your system administrator." tag="span">
                <template #updateLink>
                    <a href="https://www.microsoft.com/en-us/edge" target="_blank">{{ $t('here') }}</a>
                </template>
            </i18n>
        </div>
        <div v-if="error" class="bg-lighter py-2 px-3 rounded-lg text-sm">
            <p class="text-danger font-weight-medium">{{ $t('An error occurred when starting the camera.') }}</p>
            <pre class="text-wrap">{{error}}</pre>
            <p>{{ $t('Troubleshooting:') }}</p>
            <ul>
                <li>{{ $t('Close any other programs which might be using the webcam.') }}</li>
                <li>{{
                        $t('Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to "Allow".')
                    }}</li>
                <li>{{ $t('Refresh this page.') }}</li>
            </ul>
        </div>
        <div v-if="showFileUpload && !value" class="mt-2">
            <div class="mb-2 d-flex">
                <a
                    href="#"
                    @click.prevent="inputType = 'file'"
                    v-if="inputType === 'camera'"
                    class="btn btn-sm btn-white"
                >
                    {{ $t('Or upload video') }}
                </a>
                <a
                    href="#"
                    @click.prevent="inputType = 'camera'"
                    v-if="inputType === 'file'"
                    class="btn btn-sm btn-white"
                >
                    {{ $t('Record video') }}
                </a>
            </div>
            <video-upload
                v-if="inputType === 'file'"
                @input="$emit('input', $event)"
            ></video-upload>
        </div>
        <div v-show="!value && inputType === 'camera'">
            <native-recorder
                :value="value"
                :open="open"
                @input="$emit('input', $event)"
                @progress="$emit('progress', $event)"
                @uploadstart="$emit('uploadstart', $event)"
                @close="$emit('close')"
                @error="error = $event"
                v-if="!error"
                ref="recorder"
                @start="handleRecordStart"
                @retry="$emit('retry')"
            />
        </div>
        <div v-if="value && !showCurrentVideo">
            <div>{{$t('recorder.ok')}} {{value}} <a @click.prevent="changeVideo()" href="#">{{$t('Change video')}}</a></div>
        </div>
        <div v-else-if="value && showCurrentVideo">
            <div class="text-center" v-if="loadingCurrentVideo">
                <b-spinner small></b-spinner>
            </div>
            <div v-if="currentVideoData && currentVideoData.file && currentVideoData.hls_playlist">
                <!-- Using hls-only here for maximum compatibility when we have to show the current video after uploading. -->
                <hls-video-player
                    class="w-100"
                    :hls-src="currentVideoData.hls_playlist.url"
                    :fallback-src="currentVideoData.file.url"
                    hls-only
                ></hls-video-player>
                <div class="mt-1 d-flex">
                    <a
                        href="#"
                        @click.prevent="changeVideo()"
                        class="btn btn-sm btn-white"
                    >{{ $t('Change video') }}</a>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import {defineComponent} from 'vue';
    import NativeRecorder from "./NativeRecorder.vue";
    import {isMobileSafari} from "../config";
    import {BSpinner} from "bootstrap-vue";
    import axios from "axios";
    import VideoUpload from "./VideoUpload.vue";
import HlsVideoPlayer from "../components/HlsVideoPlayer.vue";

    export default defineComponent({
        components: {
            NativeRecorder,
            BSpinner,
            VideoUpload,
            HlsVideoPlayer,
        },
        data() {
            return {
                error: null as null | Error,
                currentVideoData: null as null | Video,
                loadingCurrentVideo: false as boolean,
                inputType: 'camera' as 'camera' | 'file',
            }
        },
        props: {
            value: {
                type: Number as () => number | null,
                required: false,
            },
            open: {
                type: Boolean as () => boolean,
            },
            showFileUpload: {
                type: Boolean as () => boolean,
                default: false,
            },
            showCurrentVideo: {
                type: Boolean as () => boolean,
                default : false,
            },
        },
        methods: {
            loadCurrentVideoData(videoId: number){
                this.loadingCurrentVideo = true;
                axios.get(`/videos/${videoId}`).then((res) => {
                    this.currentVideoData = res.data;
                    this.loadingCurrentVideo = false;
                }).catch(() => {
                    this.loadingCurrentVideo = false;
                });
            },
            handleRecordStart() {
                this.$emit('start');
            },
            changeVideo(){
                this.$emit('input', null);
                this.$nextTick(() => {
                    this.$emit('pleaseopen');
                });
            }
        },
        computed: {
            isOldEdgeBrowser(): boolean {
                var browser = (function (agent) {
                    switch (true) {
                        case agent.indexOf("edge") > -1:
                            return "MS Edge (EdgeHtml)";
                        case agent.indexOf("edg") > -1:
                            return "MS Edge Chromium";
                        case agent.indexOf("trident") > -1:
                            return "Internet Explorer";
                        case agent.indexOf("firefox") > -1:
                            return "firefox";
                        case agent.indexOf("safari") > -1:
                            return "safari";
                        default:
                            return "other";
                    }
                })(window.navigator.userAgent.toLowerCase());
                return browser === 'MS Edge (EdgeHtml)';
            },
        },
        watch: {
            value(v: null | number){
                if(v){
                    this.loadCurrentVideoData(v);
                }else{
                    this.currentVideoData = null;
                }
            }
        }
    });
</script>
