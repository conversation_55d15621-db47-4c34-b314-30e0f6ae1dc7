<template>
    <b-dropdown
        variant="white-default"
        :toggle-class="toggleClass"
        right
    >
        <template v-slot:button-content>
            <img
                :style="{width: imgWidth}"
                :alt="getLocaleLang().code"
                :class="imgClasses"
                :src="`/img/flags/${getLocaleLang().flag}.svg`"
            />
        </template>
        <b-dropdown-item
            v-for="l in langs"
            :key="l.code"
            no-caret
            @click="lang = l.code"
        >
            <img
                :style="{width: imgWidth}"
                :src="`/img/flags/${l.flag}.svg`"
                class="mr-2"
                :alt="l.code"
                :class="imgClasses"
            />
            {{ l.label }}
        </b-dropdown-item>
    </b-dropdown>
</template>
<script lang="ts">
import Vue, {defineComponent} from "vue";
import {BDropdown, BDropdownItem} from "bootstrap-vue";

import Lockr from "lockr";
import {loadLanguageAsync} from "./i18n";
import Cookies from "js-cookie";
import {PropType} from "vue/types/options";
import {Settings} from "luxon";

interface Lang {
    code: string;
    flag: string;
    label: string;
}

export const languageStore = Vue.observable({
    currentLanguage: Lockr.get("public_lang", "en"),
});

export default defineComponent({
    name: "LangPicker",
    components: {
        BDropdown,
        BDropdownItem,
    },
    emits: {
        /* eslint-disable @typescript-eslint/no-unused-vars */
        change: (lang: string) => true,
    },
    props: {
        imgWidth: {
            type: String as PropType<string>,
            default: "16px",
        },
        imgClasses: {
            type: [String, Array, Object] as PropType<VueClassBinding>,
        },
        toggleClass: {
            type: [String, Array, Object] as PropType<VueClassBinding>,
        },
    },
    async mounted() {
        let setLang = Lockr.get("public_lang") as string | null;
        if (!setLang) {
            let navLang = navigator.language.split("-")[0];
            if (navLang === "et") {
                this.lang = "et";
            } else if (navLang === "lt") {
                this.lang = "lt";
            } else {
                this.lang = "en";
            }
        } else {
            await loadLanguageAsync(setLang);
        }
    },
    data() {
        return {
            langs: [
                {code: "et", flag: "estonia", label: "Estonian"},
                {code: "en", flag: "united-kingdom", label: "English"},
                {code: "lt", flag: "lithuania", label: "Lithuanian"},
                {code: "lv", flag: "latvia", label: "Latvian"},
            ] as const,
        };
    },
    computed: {
        lang: {
            async set(code: string) {
                Lockr.set("public_lang", code);
                this.saveUserLangCookie(code);
                await loadLanguageAsync(code);
                window.language = code;
                languageStore.currentLanguage = code;
                this.setLuxonLocale(code);
                this.$emit("change", code);
            },
            get(): string {
                const code = Lockr.get("public_lang", "en");
                this.saveUserLangCookie(code);
                languageStore.currentLanguage = code;
                this.setLuxonLocale(code);
                return code;
            },
        },
        luxonSettings() {
            return Settings;
        },
    },
    methods: {
        getLocaleLang(): Lang {
            return this.langs.find(l => l.code === this.$i18n.locale) ?? this.langs[0];
        },
        saveUserLangCookie(lang: string): void {
            Cookies.set("user_lang", lang, {expires: 10, path: "/"});
        },
        setLuxonLocale(lang: string): void {
            Settings.defaultLocale = lang;
        },
    },
});
</script>
