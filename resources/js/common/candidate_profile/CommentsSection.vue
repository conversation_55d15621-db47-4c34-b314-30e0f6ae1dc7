<template>
    <div :class="wrapperClass">
        <div :class="[menuClass, {'bg-lighter': showCommentForm}]">
            <div
                class="card mb-3 w-100"
                v-if="showCommentForm"
            >
                <div class="card-body">
                    <standalone-form
                        ref="commentForm"
                        :form-name="'CommentForm'"
                        :to-load="{
                            candidate_id: candidate.id,
                            project_id: project ? project.id : null,
                            video_interview_invite_id: videoInvite ? videoInvite.id : null,
                            rating: null,
                        }"
                        :params="{
                            candidate_id: candidate.id,
                            project_id: this.project ? this.project.id : null,
                        }"
                        :labelSave="editingComment ? $t('Update comment') : $t('Post')"
                        @response="handleCommentResponse"
                        @cancel="handleCommentCancel"
                        show-cancel
                    ></standalone-form>
                </div>
            </div>
            <div v-if="!showCommentForm">
                <b-button
                    @click="showCommentForm = true"
                    variant="white"
                    size="sm"
                >
                    <i class="tdi td-plus mr-2"></i>
                    {{ $t("Add comment") }}
                </b-button>
            </div>
            <div v-if="project && !videoInvite && !showCommentForm">
                <styled-checkbox
                    :true-value="false"
                    :false-value="true"
                    v-model="showAllComments"
                    id="profile_comments_all"
                >
                    {{ $t("Only show comments from current project") }}
                </styled-checkbox>
            </div>
        </div>
        <div :class="contentClass">
            <single-comment
                avatar-class="mt-2"
                v-for="comment in comments.data"
                :comment="comment"
                :project="project"
                :candidate="candidate"
                :video-invite="videoInvite"
                :key="`candidate_comment_${candidate.id}_${comment.id}`"
                @delete="deleteComment(comment)"
                @public="makePublic(comment)"
                @private="makePrivate(comment)"
                @reload="
                    loadComments();
                    recalculateCandidateCardScore(candidate, project);
                "
                class="mb-3"
            >
            </single-comment>
            <div
                v-if="loading"
                v-for="i in loading"
            >
                <div class="d-flex mb-4">
                    <div class="mr-2">
                        <b-skeleton type="avatar"></b-skeleton>
                    </div>
                    <div class="flex-grow-1">
                        <b-skeleton
                            width="30%"
                            height="1rem"
                        ></b-skeleton>
                        <b-skeleton
                            width="13%"
                            height="0.75rem"
                        ></b-skeleton>
                    </div>
                </div>
                <div class="mb-4">
                    <b-skeleton width="75%"></b-skeleton>
                    <b-skeleton width="55%"></b-skeleton>
                </div>
                <hr />
            </div>
            <div
                class="text-center"
                v-if="comments.current_page !== comments.last_page"
            >
                <button
                    class="btn btn-white btn-sm"
                    @click="loadComments(comments.current_page + 1)"
                >
                    {{ $t("Load more") }}
                </button>
            </div>
            <empty-state
                v-if="!loading && !comments.data.length"
                :title="$t('No comments yet')"
                :description="$t('Add a comment to start the conversation.')"
            ></empty-state>
        </div>
    </div>
</template>
<script lang="ts">
import Vue from "vue";
import {BAlert, BButton, BCollapse, BSkeleton} from "bootstrap-vue";
import axios, {AxiosResponse} from "axios";
import SingleComment from "../SingleComment.vue";
import StandaloneForm from "../../forms/StandaloneForm.vue";

import Lockr from "lockr";
import {PropType} from "vue/types/options";
import StyledCheckbox from "../StyledCheckbox.vue";
import {computeNewAverage, resolveSometimesStringNumber} from "../util";
import {recalculateCandidateCardScore} from "../../project/util";
import EmptyState from "../EmptyState.vue";

export default Vue.extend({
    name: "CommentsSection",
    components: {
        EmptyState,
        StyledCheckbox,
        BAlert,
        BButton,
        BSkeleton,
        SingleComment,
        BCollapse,
        StandaloneForm,
    },
    props: {
        candidate: {
            type: Object as PropType<Candidate>,
            required: true,
        },
        project: {
            type: Object as PropType<Project | null>,
        },
        videoInvite: {
            type: Object as PropType<VideoInterviewInvite | null>,
            default: null,
        },
        total: {
            type: Number as PropType<number>,
        },
        wrapperClass: {
            type: String as PropType<string>,
            default: "",
        },
        menuClass: {
            type: String as PropType<string>,
            default: "",
        },
        contentClass: {
            type: String as PropType<string>,
            default: "",
        },
    },
    data() {
        return {
            commentForm: null,
            addingComment: false,
            deletingComment: null as number | null | undefined,
            editingComment: false,
            showAllComments: Lockr.get("showAllComments", false),
            showCommentForm: false,
            comments: {
                data: [],
                total: 0,
                current_page: 0,
                from: 0,
                last_page: 0,
                last_page_url: "",
                next_page_url: "",
                path: "",
                per_page: 0,
                prev_page_url: "",
                to: 0,
            } as PaginatedResults<Comment>,
            loading: 0,
        };
    },
    mounted() {
        this.loadComments();
        if (!this.videoInvite) {
            analytics.track("Viewed candidate comments tab");
        }
    },
    methods: {
        loadComments(page = 1) {
            if (page === 1) {
                this.loading = isNaN(this.total) ? 3 : this.total;
                console.log(`Loading ${this.total} comments`);
            } else {
                this.loading = this.comments.per_page;
            }
            const params = {
                page: page,
                project_id: !this.showAllComments && !this.videoInvite && this.project ? this.project.id : null,
                video_invite_id: this.videoInvite ? this.videoInvite.id : null,
            };
            axios
                .get(`/candidates/${this.candidate.id}/comments`, {
                    params: params,
                })
                .then((res: AxiosResponse<PaginatedResults<Comment>>) => {
                    if (page === 1) {
                        this.comments = res.data;
                    } else {
                        this.comments.data = this.comments.data.concat(res.data.data);
                        this.comments.current_page = res.data.current_page;
                    }
                    this.loading = 0;
                    if (this.addingComment) {
                        let form: any = this.$refs.commentForm;
                        form.reset();
                        form.update({
                            candidate_id: this.candidate.id,
                            project_id: this.project ? this.project.id : null,
                            video_interview_invite_id: this.videoInvite ? this.videoInvite.id : null,
                            rating: null,
                        });
                        this.addingComment = false;
                    }
                });
        },
        recalculateCandidateCardScore,
        handleCommentResponse(res: any) {
            window.analytics.track("Added candidate comment");

            const form: any = this.getForm();
            this.editingComment = false;
            this.showCommentForm = false;
            if (this.project) {
                recalculateCandidateCardScore(this.candidate, this.project);
            }
            this.loadComments();
        },
        handleCommentCancel() {
            if (this.editingComment) {
                const form: any = this.getForm();
                form.load({candidate_id: this.candidate.id});
                this.editingComment = false;
            }
            this.showCommentForm = false;
        },
        deleteComment(comment: Comment) {
            const confirmQuestion = `${this.$t("Are you sure you want to delete this comment?")} ${this.$t("This action cannot be undone.")}`;
            this.$bvModal
                .msgBoxConfirm(confirmQuestion, {
                    title: this.$t("Delete comment"),
                    okVariant: "danger",
                    cancelVariant: "white",
                    titleTag: "h3",
                    okTitle: this.$t("Delete"),
                    cancelTitle: this.$t("Cancel"),
                    hideHeaderClose: false,
                    centered: true,
                    buttonSize: "sm",
                    bodyClass: "pt-2",
                })
                .then((value: boolean) => {
                    if (value) {
                        this.deletingComment = comment.id;
                        axios
                            .delete(`/comments/${comment.id}`)
                            .then(() => {
                                return this.loadComments();
                            })
                            .then(() => {
                                this.deletingComment = null;
                            });
                    }
                });
        },
        edit(comment: Comment) {
            const form: any = this.getForm();
            form.load(comment);
            this.editingComment = true;
            this.showCommentForm = true;
        },
        makePublic(comment: Comment) {
            axios.post(`/comments/${comment.id}/makePublic`).then(() => {
                comment.is_public = true;
            });
        },
        makePrivate(comment: Comment) {
            axios.post(`/comments/${comment.id}/makePrivate`).then(() => {
                comment.is_public = false;
            });
        },
        getForm() {
            const commentForm: any = this.$refs.commentForm;
            return commentForm.$refs.form;
        },
    },
    watch: {
        showAllComments(v) {
            Lockr.set("showAllComments", v);
            this.comments.data = [];
            this.loadComments();
        },
    },
});
</script>
