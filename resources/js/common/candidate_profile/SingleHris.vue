<script lang="ts">
import {defineComponent} from "vue";
import axios from "axios";
import {<PERSON>pin<PERSON>} from "bootstrap-vue";
import FormModal from "../../forms/FormModal.vue";
import {PropType} from "vue/types/options";
import {HRISInfo} from "../../@types/hris";
import FormSubmission from "../components/FormSubmission.vue";
import FormSubmissionCard from "../components/FormSubmissionCard.vue";

export default defineComponent({
    name: "SingleHRIS",
    components: {FormSubmissionCard, FormSubmission, FormModal, BSpinner},
    props: {
        hris: {
            type: Object as PropType<HRISInfo>,
            required: true,
        },
        candidate: {
            type: Object as PropType<Candidate>,
            required: true,
        },
        project: {
            type: Object as PropType<Project>,
            required: false,
        },
    },
    data() {
        return {
            loading: false,
            showForm: false,
        };
    },
    methods: {
        async sendToHris() {
            if (this.hasForm) {
                this.showForm = true;
                return;
            }

            this.loading = true;

            try {
                await axios.post(`/candidates/${this.candidate.id}/sendToHris/${this.hris.connector.integration.id}`, {
                    project_id: this.project?.id,
                });
                await this.afterSend();
            } finally {
                this.loading = false;
            }
        },
        async afterSend() {
            this.$emit("update");

            analytics.track("Candidate sent to HRIS", {
                hris_type: this.hris.connector.integration.name,
            });
        },
        async removeLink() {
            await axios.delete(`/candidates/${this.candidate.id}/removeLink/${this.hris.connector.integration.id}`);

            this.$emit("update");
        },
    },
    computed: {
        hasForm(): boolean {
            return this.hris.connector.form !== null;
        },
    },
});
</script>

<template>
    <section class="candidate-profile__section">
        <h2>{{ hris.connector.integration.name }}</h2>
        <div v-if="hris.candidate_hris_data.status === 'exists_in_hris'">
            {{ $t("Candidate exists in {hrisName}", {hrisName: hris.connector.integration.name}) }}
            <a
                :href="hris.candidate_hris_data.remoteUrl"
                target="_blank"
                v-if="hris.candidate_hris_data.remoteUrl"
            >
                <i class="fas fa-external-link-alt"></i>
            </a>
            <button
                class="btn btn-white-danger btn-sm mt-2"
                :disabled="loading"
                @click="removeLink()"
            >
                {{ $t("Remove link") }}
            </button>
        </div>
        <div v-else-if="hris.candidate_hris_data.status === 'sent_to_hris'">
            {{ $t("Candidate sent to {hrisName}", {hrisName: hris.connector.integration.name}) }}
        </div>
        <div v-else>
            <div>
                <p
                    class="text-danger"
                    v-if="hris.connector.preboarding_form && !hris.connector.candidate_preboarding_submission"
                >
                    {{ $t("The candidate has not yet submitted the preboarding information.") }}
                </p>
                <p
                    class="text-success"
                    v-else-if="hris.connector.preboarding_form && hris.connector.candidate_preboarding_submission"
                >
                    {{ $t("The candidate has submitted preboarding information.") }}
                </p>
                <div class="mb-2 text-sm">
                    {{ $t("Candidate not in {hrisName}", {hrisName: hris.connector.integration.name}) }}
                </div>
                <div class="d-flex">
                    <button
                        class="btn btn-white-primary btn-sm mr-2"
                        :disabled="loading"
                        @click="sendToHris()"
                    >
                        <b-spinner
                            small
                            v-if="loading"
                            class="mr-2"
                        ></b-spinner>
                        {{ $t("Send to {hrisName}", {hrisName: hris.connector.integration.name}) }}
                        <template v-if="hasForm"> ({{ $t("fill handover form") }}) </template>
                    </button>
                </div>
            </div>
            <form-modal
                v-if="hasForm"
                :to-load="{candidate_id: candidate.id}"
                :label-save="$t('Send')"
                v-model="showForm"
                :form-to-load="hris.connector.form"
                :endpoint="`/candidates/${candidate.id}/sendToHris/${hris.connector.integration.id}?project_id=${project?.id}`"
                @response="afterSend()"
                :title="$t('Send to {hrisName}', {hrisName: hris.connector.integration.name})"
            >
                <template #after-form>
                    <template v-if="hris.connector.candidate_preboarding_submission !== null">
                        <div class="text-sm">
                            <p>
                                {{
                                    $t(
                                        "The candidate has submitted the following preboarding information. It will be sent to {hrisName} together with the handover form.",
                                        {hrisName: hris.connector.integration.name}
                                    )
                                }}
                            </p>
                            <form-submission-card
                                :submission="hris.connector.candidate_preboarding_submission"
                            ></form-submission-card>
                        </div>
                    </template>
                </template>
            </form-modal>
        </div>
    </section>
</template>
