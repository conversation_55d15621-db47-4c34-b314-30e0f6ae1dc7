<script lang="ts">
import {defineComponent} from 'vue'

export default defineComponent({
    name: "SettingsPageCardLayout",
    props: {
        settingsBodyClasses: {
            type: String,
            required: false
        },
        pageTitle: {
            type: String,
            required: false
        }
    },
    computed: {
        hasSettingsHeader(): boolean {
            return this.$slots.settingsHeader !== undefined || this.$scopedSlots.settingsHeader !== undefined
        },
        title(): string|undefined {
            return this.pageTitle ?? this.$page.props.title;
        }
    }
})
</script>

<template>
    <div class="card">
        <div
            v-if="title || hasSettingsHeader"
            class="card-header d-flex align-items-center justify-content-between"
        >
            <h2 v-if="title" class="mb-0">
                {{ title }}
            </h2>
            <slot name="settingsHeader"></slot>
        </div>
        <slot name="settingsTabs"></slot>
        <div :class="['card-body', settingsBodyClasses]">
            <slot></slot>
        </div>
    </div>
</template>
