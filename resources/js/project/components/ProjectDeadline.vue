<script lang="ts">
import {defineComponent} from "vue";
import i18n from "../../common/i18n";
import Lockr from "lockr";
import {getDaysText} from "../util";
import {formatDateTime} from "../../common/datetime";

export default defineComponent({
    name: "ProjectDeadline",
    i18n,
    data() {
        return {
            deadlinePreference: "due_in",
        };
    },
    props: {
        textClass: {
            type: String,
            required: false,
            default: "text-sm text-gray-500",
        },
        project: {
            type: Object as () => Project,
            required: true,
        },
        showNps: {
            type: Boolean,
            default: true,
        },
    },
    created() {
        this.deadlinePreference = Lockr.get(`project_deadline_preference`, "due_in");
    },
    computed: {
        dateText(): string {
            const bits = [];

            if (this.project.start_date) {
                bits.push(this.$t("Started: {date}", {date: formatDateTime(this.project.start_date)}));
            }
            if (this.project.deadline_at) {
                bits.push(this.$t("Deadline: {date}", {date: formatDateTime(this.project.deadline_at)}));
            } else {
                bits.push(this.$t("Deadline not set"));
            }
            return bits.join("<br/>");
        },
        deadlineText(): string {
            return getDaysText(this.project, this.deadlinePreference);
        },
    },
    methods: {
        handlePreferenceChange() {
            if (this.deadlinePreference === "due_in") {
                this.deadlinePreference = "date";
            } else {
                this.deadlinePreference = "due_in";
            }
        },
    },
    watch: {
        deadlinePreference(v) {
            Lockr.set("project_deadline_preference", v);
        },
    },
});
</script>

<template>
    <span
        :class="textClass"
        @click="handlePreferenceChange"
        v-b-tooltip="{title: dateText, hover: true, placement: 'bottom', customClass: 'text-left', html: true}"
    >
        <template v-if="!project.is_perpetual">
            <span>{{ deadlineText }}</span>
        </template>
        <template v-if="project.nps_score !== undefined && project.nps_score !== null && showNps">
            <span v-if="!project.is_perpetual"> • </span>cNPS: {{ project.nps_score }}
        </template>
        <span v-if="project.is_perpetual && project.nps_score === undefined">&nbsp;</span>
    </span>
</template>
