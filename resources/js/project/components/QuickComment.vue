<template>
    <div class="d-flex flex-wrap gap-2 align-items-center">
        <div
            v-if="candidate.quick_comments[0]"
            v-html="candidate.quick_comments[0].content"
            class="ml-2"
            @click="editing = editable"
        ></div>
        <a
            href="#"
            class="btn btn-xs btn-white"
            v-if="editable"
            :id="`candidate_comment_${candidate.id}`"
            @click.prevent="editing = true;"
        >
            <i class="tdi td-edit td-lg"></i>
        </a>
        <b-popover
            :show.sync="editing"
            :target="`candidate_comment_${candidate.id}`"
            triggers="click blur"
            placement="bottomright"
        >
            <div class="d-flex">
                <a href="#" class="btn btn-xs btn-white" @click.prevent="editing = false;">
                    <i class="tdi td-arrow-left td-sm"></i>
                </a>
            </div>
            <input
                type="text"
                class="form-control-sm form-control flex-grow-1 my-2"
                ref="editField"
                v-model="commentText"
                @keyup.esc="editing = false"
                @keyup.enter="save()"
            >
            <div v-if="editing" class="d-flex">
                <a href="#" class="btn btn-xs btn-white" @click.prevent="save();">
                    <i class="tdi td-check td-sm" v-if="!loading"></i>
                    <b-spinner small v-else></b-spinner>
                </a>
            </div>
        </b-popover>
    </div>
</template>
<script lang="ts">
import Vue from 'vue';
import axios, {AxiosResponse} from 'axios';
import {BPopover, BSpinner} from "bootstrap-vue";

export default Vue.extend({
    components: {
        BSpinner,
        BPopover,
    },
    props: {
        candidate: {
            type: Object as () => Candidate,
        },
        project: {
            type: Object as () => Project,
        },
    },
    data() {
        return {
            user: window.user,
            editing: false,
            commentText: null as string | null,
            loading: false,
        }
    },
    mounted() {
        if (this.candidate.quick_comments[0]) {
            this.commentText = this.candidate.quick_comments[0].text_content;
        }
    },
    methods: {
        save() {
            this.loading = true;
            if (this.firstComment) {
                axios.put(`/comments/${this.firstComment.id}`, {
                    content: `<div>${this.commentText}</div>`
                }).then((res: AxiosResponse<Comment>) => {
                    if (this.firstComment) {
                        this.firstComment.content = res.data.content;
                    }
                    this.loading = false;
                    this.editing = false;
                });
            } else {
                axios.post('/comments', {
                    content: `<div>${this.commentText}</div>`,
                    project_id: this.project.id,
                    candidate_id: this.candidate.id,
                    is_quick: true,
                }).then((res: AxiosResponse<Comment>) => {
                    this.candidate.quick_comments.push(res.data);
                    this.loading = false;
                    this.editing = false;
                });
            }
        }
    },
    watch: {
        editing(v: boolean) {
            if (v) {
                this.$nextTick(() => {
                    let editField = this.$refs.editField;
                    if (editField) {
                        // @ts-ignore
                        editField.focus();
                    }
                })
            }
        }
    },
    computed: {
        firstComment(): null | Comment {
            if (this.candidate.quick_comments[0]) {
                return this.candidate.quick_comments[0];
            }
            return null;
        },
        editable(): boolean {
            if (this.candidate.quick_comments[0]) {
                if (this.candidate.quick_comments[0].user_id === this.user.id) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return true;
            }
        }
    },

})
</script>
