<template>
    <div class="d-flex flex-column flex-grow-1 overflow-hidden">
        <template v-if="!project.is_template">
            <div
                class="d-flex align-items-center mb-2 justify-content-between flex-wrap gap-2 mr-3"
                ref="buttonContainer"
            >
                <div
                    class="d-flex align-items-center gap-2"
                    ref="searchAndSortButtons"
                >
                    <b-input-group
                        size="sm"
                        :style="{width: '343px'}"
                    >
                        <template #prepend>
                            <b-input-group-text>
                                <i class="fas fa-search"></i>
                            </b-input-group-text>
                        </template>
                        <b-form-input
                            :placeholder="$t('Find candidates')"
                            v-model="query"
                            debounce="50"
                        ></b-form-input>
                        <template #append>
                            <b-input-group-text v-if="loadingAdvanced">
                                <b-spinner small></b-spinner>
                            </b-input-group-text>
                            <b-button
                                class="px-2"
                                variant="white"
                                size="sm"
                                @click="query = ''"
                                v-b-tooltip.bottom.hover="$t('Clear search')"
                            >
                                <i class="tdi td-close td-sm"></i>
                            </b-button>
                        </template>
                    </b-input-group>
                    <div class="d-flex align-items-center gap-1">
                        <button
                            class="btn btn-white btn-circle btn-sm d-none d-md-inline-block"
                            :class="{'text-danger': advancedFiltersApplied}"
                            v-b-tooltip.bottom.hover="$t('Show advanced filters')"
                            @click.stop.prevent="showAdvancedSearch = !showAdvancedSearch"
                        >
                            <i class="tdi td-filter-alt"></i>
                        </button>
                        <b-dropdown
                            toggle-class="btn-sm btn-white btn-circle"
                            v-b-tooltip.bottom.hover="$t('Sort candidates')"
                            boundary="window"
                            no-caret
                        >
                            <template v-slot:button-content>
                                <i class="tdi td-sort-arrow-down"></i>
                            </template>
                            <b-dropdown-group
                                id="sort-by-items"
                                :header="$t('Sort by:')"
                                header-classes="px-2 py-1"
                            >
                                <b-dropdown-item
                                    link-class="d-flex align-items-baseline"
                                    @click="getCandidateSimilarities()"
                                    :disabled="!$rlSettings.features.enable_ai"
                                >
                                    <span class="mr-auto">
                                        {{ $t("Similarity to selected") }}
                                        <span
                                            class="ml-2"
                                            v-b-tooltip="
                                                $t(
                                                    'Use AI to compare candidates. To avoid bias, you should select multiple candidates at a time.'
                                                )
                                            "
                                            >✨</span
                                        >
                                    </span>
                                    <i
                                        class="tdi td-check ml-2"
                                        v-if="sortCandidatesBy === 'similarity_score'"
                                    ></i>
                                </b-dropdown-item>
                                <b-dropdown-item
                                    v-for="option in sortOptions"
                                    :key="option.value"
                                    @click="sortWithOrder = option.value"
                                    link-class="d-flex align-items-baseline"
                                >
                                    <span class="mr-auto">
                                        {{ option.text }}
                                    </span>
                                    <i
                                        class="tdi td-check ml-2"
                                        v-if="sortWithOrder === option.value"
                                    ></i>
                                </b-dropdown-item>
                            </b-dropdown-group>
                        </b-dropdown>
                        <button
                            class="btn btn-white btn-circle btn-sm d-none d-md-inline-block"
                            :class="{active: showDatabase}"
                            v-b-tooltip.bottom.hover="showDatabase ? $t('Hide talent pool') : $t('Show talent pool')"
                            @click.stop.prevent="showDatabase = !showDatabase"
                            v-if="$rlSettings.user.role !== 'limited'"
                        >
                            <i class="tdi td-users-group"></i>
                        </button>
                        <b-dropdown
                            toggle-class="btn-sm btn-white btn-circle"
                            v-b-tooltip.bottom.hover="$t('Other')"
                            no-caret
                        >
                            <template v-slot:button-content>
                                <i class="tdi td-command"></i>
                            </template>
                            <b-dropdown-item
                                icon="td-paper-download"
                                :href="`/projects/${project.id}/toXlsx`"
                                >{{ $t("Export candidates") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                icon="td-paper-upload"
                                @click="showXlsxImportModal = true"
                                >{{ $t("Import candidates") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                @click="layout = layout === 'layout_table' ? 'layout_columns' : 'layout_table'"
                            >
                                <span v-if="layout === 'layout_table'">{{ $t("Switch to columns view") }}</span>
                                <span v-else>{{ $t("Switch to table view") }}</span>
                            </b-dropdown-item>
                            <b-dropdown-item
                                v-if="!project.current_user_is_subscribed"
                                @click="setNotifications(true)"
                                v-b-tooltip.hover.right="
                                    $t(
                                        'E-mail notifications are currently turned off. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn on.'
                                    )
                                "
                            >
                                {{ $t("Turn on e-mail notifications") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                v-else
                                v-b-tooltip.hover.right="
                                    $t(
                                        'Notifications are currently turned on. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn off.'
                                    )
                                "
                                @click="setNotifications(false)"
                            >
                                {{ $t("Mute e-mail notifications") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                @click="showStageCategory = !showStageCategory"
                                v-if="$rlSettings.user.role !== 'limited'"
                            >
                                {{ showStageCategory ? $t("Hide stage categories") : $t("Show stage categories") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                @click="cloningProjectOptions = {project, asTemplate: false}"
                                v-can.projects.create
                            >
                                {{ $t("Clone") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                v-if="!project.is_template"
                                @click="cloningProjectOptions = {project, asTemplate: true}"
                                v-can.project-templates.update
                            >
                                {{ $t("Create template from project") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                @click="convertToTemplate"
                                v-can.project-templates.create
                                v-if="project.is_empty && !project.is_template"
                            >
                                {{ $t("Convert to template") }}
                            </b-dropdown-item>
                        </b-dropdown>
                    </div>
                </div>
                <div
                    class="d-flex flex-wrap gap-1"
                    ref="actionButtons"
                >
                    <b-button-group
                        variant="white"
                        v-if="layout !== 'layout_table'"
                    >
                        <button
                            class="btn btn-sm btn-white"
                            @click.prevent="moveSelectedToNextStage()"
                            :disabled="readOnly || selected.length === 0"
                            v-b-tooltip.hover="collapseActionButtonTexts ? $t('Move to next stage') : null"
                        >
                            <i class="tdi td-right-square"></i>
                            <span
                                class="ml-2"
                                v-show="!collapseActionButtonTexts"
                            >
                                {{ $t("Move to next stage") }}
                            </span>
                        </button>
                        <b-dropdown
                            right
                            variant="white"
                            size="sm"
                            toggle-class="dropdown-toggle-split"
                            :disabled="readOnly || selected.length === 0"
                            @click.prevent="moveSelectedToNextStage()"
                            v-if="layout !== 'layout_table'"
                        >
                            <b-dropdown-item
                                v-for="s in project.stages"
                                :key="s.id"
                                @click.prevent="moveSelectedToNextStage(s)"
                            >
                                {{ s.name }}
                            </b-dropdown-item>
                        </b-dropdown>
                    </b-button-group>
                    <b-button-group variant="white">
                        <button
                            :disabled="disableScheduleInterviews"
                            @click="showEventSetModal(selected)"
                            class="btn btn-sm btn-white"
                            v-b-tooltip.hover="collapseActionButtonTexts ? $t('Schedule interviews') : null"
                        >
                            <i class="tdi td-calendar"></i>
                            <span
                                class="d-none d-sm-inline ml-2"
                                v-if="!collapseActionButtonTexts"
                            >
                                {{ $t("Schedule interviews") }}
                            </span>
                            <span
                                class="text-danger"
                                v-if="selected.length !== 0 && selected.length !== selected.filter(c => c.email).length"
                            >
                                <i
                                    class="tdi td-alert-circle ml-2"
                                    v-b-tooltip.bottom.hover="
                                        $t(
                                            'Your selection contains candidates without email addresses. You can only create pre-scheduled interviews for this selection.'
                                        )
                                    "
                                ></i>
                            </span>
                        </button>
                        <b-dropdown
                            right
                            variant="white"
                            size="sm"
                            :disabled="readOnly"
                            toggle-class="dropdown-toggle-split"
                        >
                            <b-dropdown-item
                                @click="showVideoInterviewInviteModal = true"
                                v-if="$rlSettings.features.video_interviews"
                                :disabled="
                                    readOnly ||
                                    !$rlSettings.controls.videoInterviews.create ||
                                    selected.length === 0 ||
                                    (selected.length !== 0 && selected.length !== selected.filter(c => c.email).length)
                                "
                            >
                                <i class="tdi td-video mr-2"></i>
                                {{ $t("Send video interview") }}
                            </b-dropdown-item>
                            <b-dropdown-item
                                v-else
                                href="mailto:<EMAIL>?subject=Video interviews upgrade&body=Hi, RecruitLab team!%0d%0a%0d%0aI would like to use RecruitLab's Video Interview Tool to cut my time-to-hire by at least 30% and offer a better experience for my candidates.%0d%0a%0d%0aPlease send me the pricing info.%0d%0a%0d%0aWith best wishes"
                                :disabled="
                                    readOnly ||
                                    selected.length === 0 ||
                                    (selected.length !== 0 && selected.length !== selected.filter(c => c.email).length)
                                "
                            >
                                <i class="tdi td-video mr-2"></i>
                                {{ $t("Send video interview") }}
                                <span class="badge badge-info ml-2">{{ $t("upgrade") }}</span>
                            </b-dropdown-item>
                            <b-dropdown-item
                                @click="showVideoCallModal = true"
                                :disabled="
                                    selected.length === 0 ||
                                    (selected.length !== 0 && selected.length !== selected.filter(c => c.email).length)
                                "
                            >
                                <i class="tdi td-call mr-2"></i>
                                {{ $t("Instant video call") }}
                            </b-dropdown-item>
                        </b-dropdown>
                    </b-button-group>
                    <send-message
                        v-if="layout !== 'layout_table'"
                        :candidates="selected"
                        :disabled="readOnly"
                        :show-button-text="!collapseActionButtonTexts"
                        @message-sent="handleMessageSent"
                        @sms-sent="handleSMSSent"
                        @video-message-sent="handleVideoMessageSent"
                    ></send-message>
                    <b-dropdown
                        right
                        variant="white"
                        size="sm"
                        :disabled="selected.length === 0"
                        v-if="layout !== 'layout_table' && $rlSettings.user.role !== 'limited'"
                        v-b-tooltip.hover="collapseActionButtonTexts ? $t('More') : null"
                    >
                        <template #button-content>
                            <i class="tdi td-menu"></i>
                            <span
                                class="d-none d-sm-inline ml-2"
                                v-if="!collapseActionButtonTexts"
                            >
                                {{ $t("More") }}
                            </span>
                        </template>
                        <b-dropdown-item
                            @click="() => showCreateTaskModal(selected)"
                            :disabled="readOnly || !$rlSettings.controls.tasks.create || selected.length === 0"
                        >
                            <i class="tdi td-check-circle"></i>
                            {{ $t("Create tasks") }}
                        </b-dropdown-item>
                        <b-dropdown-item
                            @click="showBulkCommentModal = true"
                            :disabled="!$rlSettings.controls.comments.create || selected.length === 0"
                        >
                            <i class="tdi td-chat"></i>
                            {{ $t("Add bulk comments") }}
                        </b-dropdown-item>
                        <b-dropdown-item
                            @click="showBulkTagModal = true"
                            :disabled="!$rlSettings.controls.tags.create || selected.length === 0"
                        >
                            <i class="tdi td-add-tag"></i>
                            {{ $t("Add bulk tags") }}
                        </b-dropdown-item>
                        <b-dropdown-item @click="candidatesToScreen = [...selected]"
                            >Screen candidates with AI</b-dropdown-item
                        >
                        <b-dropdown-item
                            @click="showCopyToOtherProjectModal = true"
                            :disabled="selected.length === 0 || $rlSettings.user.role === 'limited'"
                        >
                            <i class="tdi td-plus"></i>
                            {{ $t("Copy to another project") }}
                        </b-dropdown-item>
                        <b-dropdown-item
                            @click="bulkAnonymize()"
                            :disabled="selected.length === 0 || $rlSettings.user.role === 'limited'"
                        >
                            <span class="text-danger">
                                <i class="fas fa-broom"></i>
                                <template v-if="$rlSettings.enable_permanent_delete">
                                    {{ $t("Delete permanently") }}
                                </template>
                                <template v-else>
                                    {{ $t("Anonymize") }}
                                </template>
                            </span>
                        </b-dropdown-item>
                        <b-dropdown-item
                            @click="showPresentModal(selected)"
                            :disabled="readOnly || !$rlSettings.controls.projects.update || selected.length === 0"
                        >
                            <i class="tdi td-share-alt"></i>
                            {{ $t("Share") }}
                        </b-dropdown-item>
                    </b-dropdown>
                </div>
            </div>
            <b-collapse :visible="showAdvancedSearch">
                <div class="d-flex align-items-center gap-2 mb-2 border rounded-xl">
                    <checkbox-dropdown
                        variant="transparent"
                        size=""
                        toggle-class="font-weight-normal"
                        v-model="advancedFilters.hasCommentsByUserIds"
                        :options="commentUserOptions"
                        :search="true"
                        :text="$t('Comments')"
                    />
                    <b-dropdown
                        variant="transparent"
                        toggle-class="font-weight-normal"
                        no-caret
                    >
                        <template v-slot:button-content>
                            {{ $t("Project entry date") }}
                            <small class="ml-2">
                                <i class="fas fa-chevron-down"></i>
                            </small>
                        </template>
                        <div v-if="false">
                            <b-dropdown-item>{{ $t("MTD") }}</b-dropdown-item>
                            <b-dropdown-item>{{ $t("Previous month") }}</b-dropdown-item>
                            <b-dropdown-item>{{ $t("YTD") }}</b-dropdown-item>
                            <b-dropdown-item>{{ $t("Last 30 days") }}</b-dropdown-item>
                            <b-dropdown-item>{{ $t("Last 60 days") }}</b-dropdown-item>
                            <b-dropdown-item>{{ $t("Last 90 days") }}</b-dropdown-item>
                            <b-dropdown-item>{{ $t("Custom range") }}</b-dropdown-item>
                        </div>

                        <div class="p-2">
                            <div class="d-flex">
                                <div class="mr-1">
                                    <div class="mb-1">{{ $t("Candidates added to the project after") }}:</div>
                                    <calendar
                                        v-model="advancedFilters.submissionAfter"
                                        :max="new Date().toISOString().split('T')[0]"
                                    ></calendar>
                                    <div
                                        class="d-flex mt-2"
                                        v-if="advancedFilters.submissionAfter"
                                    >
                                        <a
                                            class="btn btn-sm btn-white"
                                            href="#"
                                            @click.prevent.stop="advancedFilters.submissionAfter = null"
                                        >
                                            {{ $t("Clear") }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </b-dropdown>
                    <div>
                        <styled-checkbox
                            id="has-new-activities"
                            class="ml-2"
                            v-model="advancedFilters.hasNewActivities"
                        >
                            {{ $t("Show candidates with new activities") }}
                        </styled-checkbox>
                    </div>
                    <div v-if="$rlSettings.mark_underage_candidates">
                        <styled-checkbox
                            id="show-only-underage"
                            class="ml-2"
                            v-model="advancedFilters.showOnlyUnderage"
                        >
                            {{ $t("Candidates under min age ({age})", {age: $rlSettings.underage_age_under}) }}
                        </styled-checkbox>
                    </div>
                </div>
            </b-collapse>
        </template>
        <div>
            <overlay-scrollbars
                ref="mainScroll"
                class="project-columns__container flex-grow-1"
                id="project-columns-container"
                v-if="project.is_template || layout === 'layout_columns'"
            >
                <draggable
                    group="stages"
                    force-fallback="false"
                    :fallback-on-body="true"
                    :remove-clone-on-hide="false"
                    :invertSwap="true"
                    class="project-columns__inner"
                    handle=".fa-grip-vertical"
                    v-model="computedStages"
                    :options="{disabled: readOnly}"
                    ref="projectColumns"
                    @start="handleDragStart"
                    @end="handleDragEnd"
                >
                    <!-- NB! Not rendering the #header slot at all will break reordering stages -->
                    <template slot="header">
                        <div
                            class="project-columns__column project-columns__column--database"
                            v-if="canUserUpdate && !project.is_template && showDatabase"
                        >
                            <div class="project-columns__column-head text-sm">
                                <div class="d-flex align-items-center">
                                    <div class="project-columns__column-heading">
                                        {{ $t("Talent Pool") }}
                                        <span class="badge badge-pill badge-light font-weight-medium ml-2">
                                            {{
                                                $t("{dataLength} of {dataTotal}", {
                                                    dataLength: database.data.length,
                                                    dataTotal: database.total,
                                                })
                                            }}
                                        </span>
                                    </div>
                                    <div class="mr-0 ml-auto">
                                        <button
                                            class="btn btn-sm pr-1 py-0"
                                            @click.prevent="showDatabase = false"
                                        >
                                            <template v-if="!databaseLoading">
                                                <i
                                                    class="fas fa-angle-double-left"
                                                    v-b-tooltip.bottom.hover="$t('Hide talent pool')"
                                                ></i>
                                            </template>
                                            <b-spinner
                                                v-else
                                                small
                                            ></b-spinner>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="project-columns__column-addon">
                                <button
                                    class="btn btn-white btn-sm w-100 text-left"
                                    @click="showCandidateForm = true"
                                >
                                    <i class="tdi td-plus mr-2"></i>
                                    {{ $t("Add candidate") }}
                                </button>
                            </div>
                            <draggable
                                group="main"
                                force-fallback="false"
                                :fallback-on-body="true"
                                :remove-clone-on-hide="false"
                                :invertSwap="true"
                                :list="databaseContents"
                                @change="handleTransition($event, null)"
                                :fallback-tolerance="5"
                                draggable=".project-columns__item"
                                :options="{disabled: readOnly}"
                                class="project-columns__column-body"
                            >
                                <candidate-card
                                    class="project-columns__item"
                                    v-for="candidate in databaseContents"
                                    :key="`item-${candidate.id}`"
                                    :candidate="candidate"
                                    :project="project"
                                    v-model="selected"
                                    :query="query"
                                    :id="`candidate_card_${candidate.id}`"
                                    @open="openCandidateModal"
                                    :show-drag-handle="isMobile"
                                />
                                <div
                                    slot="footer"
                                    class="d-flex justify-content-center mt-2"
                                >
                                    <button
                                        class="btn btn-sm btn-white"
                                        :disabled="dbLoadingMore"
                                        @click="loadMoreDatabase()"
                                        v-if="database.current_page !== database.last_page"
                                    >
                                        <b-spinner
                                            small
                                            v-if="dbLoadingMore"
                                            class="mr-2"
                                        ></b-spinner>
                                        {{ $t("Load more") }}
                                    </button>
                                </div>
                            </draggable>
                        </div>
                    </template>

                    <project-board-column
                        v-for="(stage, stageIndex) in project.stages"
                        :key="`stage_column_${stage.id}`"
                        :project="project"
                        :stage="stage"
                        :stage-index="stageIndex"
                        :advanced-filters-applied="advancedFiltersApplied"
                        :filtered-items="getFilteredItems(stage.candidates)"
                        :is-mobile="isMobile"
                        :query="query"
                        :read-only="readOnly"
                        :show-c-nps-tooltip="showCNpsTooltip"
                        :selected="selected"
                        :show-category="showStageCategory"
                        :boardRef="$refs.projectColumns"
                        @move="
                            moveCandidateToStage(
                                $event.candidate,
                                $event.stage,
                                project.stages.find(s => s.id === $event.toStageId)
                            )
                        "
                        @transition="handleTransition($event, stage)"
                        @ensure-summaries="ensureSummaries()"
                        @open-candidate-modal="openCandidateModal"
                        @show-cnps-help="showCNpsHelp()"
                        @hide-cnps-tooltip="hideCNpsTooltip()"
                        @select="selected = $event"
                        @select-all="selectAll(getFilteredItems(stage.candidates))"
                        @retry-summary="retrySummary"
                        @show-create-task-modal="cnd => showCreateTaskModal([cnd])"
                    />
                    <div
                        style="min-width: 260px"
                        class="mr-5"
                        v-if="canUserUpdate"
                    >
                        <div
                            class="bg-lighter rounded-xl position-sticky"
                            style="top: 0rem"
                        >
                            <div class="project-columns__column-head text-sm">
                                <div class="project-columns__column-heading">
                                    <span>{{ $t("Add a new stage") }}</span>
                                </div>
                            </div>
                            <div class="p-2">
                                <div class="input-group input-group-sm">
                                    <input
                                        type="text"
                                        v-model="newStageName"
                                        @keyup.enter="addStage"
                                        :disabled="readOnly"
                                        class="form-control"
                                        :placeholder="$t('New stage name')"
                                        :aria-label="$t('New stage name')"
                                    />
                                </div>
                                <div class="input-group mt-2">
                                    <select
                                        name="category"
                                        v-model="newStageCategory"
                                        :disabled="readOnly"
                                        class="form-control form-control-sm custom-select custom-select-sm font-weight-normal"
                                    >
                                        <option
                                            v-for="category in stageCategories"
                                            :key="category.custom_stage_category_id || category.category_id"
                                            :value="category.custom_stage_category_id || category.category_id"
                                        >
                                            {{ category.name }}
                                        </option>
                                    </select>
                                </div>
                                <button
                                    class="btn btn-white btn-sm mt-2 mb-1"
                                    :disabled="addingStage || readOnly"
                                    @click="addStage"
                                    type="button"
                                >
                                    <b-spinner
                                        v-if="addingStage"
                                        small
                                    ></b-spinner>
                                    <i class="tdi td-plus td-sm mr-1"></i>
                                    {{ $t("Add stage") }}
                                </button>
                            </div>
                        </div>
                    </div>
                </draggable>
            </overlay-scrollbars>
            <div
                v-else
                id="project-table-view"
                class="flex-grow-1"
            >
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <div class="text-sm text-bold">
                            <template v-if="selected.length">
                                {{
                                    $t("{selected} of {total} candidates selected", {
                                        selected: selected.length,
                                        total: getTableItems(project).length,
                                    })
                                }}
                            </template>
                            <template v-else>
                                {{ $tc("1 candidate|{count} candidates", getTableItems(project).length) }}
                            </template>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-td table-top">
                            <thead>
                                <tr>
                                    <th>
                                        <styled-checkbox
                                            id="select-all"
                                            size="lg"
                                            v-model="tableSelectAllCheckbox"
                                        ></styled-checkbox>
                                    </th>
                                    <th>{{ $t("Name") }}</th>
                                    <th>{{ $t("Stage") }}</th>
                                    <th>{{ $t("Age") }}</th>
                                    <th>{{ $t("Added") }}</th>
                                    <th style="width: 20%">
                                        {{ $t("Comment") }}
                                    </th>
                                    <th>{{ $t("Rating") }}</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="c in getTableItems(project)">
                                    <td>
                                        <styled-checkbox
                                            :id="`t-cb-item-${c.id}`"
                                            size="lg"
                                            :input-value="c"
                                            v-model="selected"
                                        ></styled-checkbox>
                                    </td>
                                    <td>
                                        <div>
                                            <a
                                                class="font-weight-medium"
                                                href="#"
                                                @click.prevent="openCandidateModal(c)"
                                                >{{ c.name }}
                                            </a>
                                            <div class="mt-1">
                                                <inline-tags
                                                    :candidate="c"
                                                    tag-class="badge-xxs"
                                                ></inline-tags>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span
                                            class="badge badge-pill text-center text-sm"
                                            :style="{backgroundColor: stageCategoryColors[c.stage.category]}"
                                        >
                                            {{ c.stage.name }}
                                        </span>
                                    </td>
                                    <td>{{ c.age }}</td>
                                    <td>{{ c.first_activity_date }}</td>
                                    <td>
                                        <quick-comment
                                            :candidate="c"
                                            :project="project"
                                        ></quick-comment>
                                    </td>
                                    <td>
                                        <quick-rate
                                            :candidate="c"
                                            :project="project"
                                        ></quick-rate>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center justify-content-end gap-1">
                                            <a
                                                :href="c.last_cv.url"
                                                target="_blank"
                                                class="btn btn-xs btn-white"
                                                v-b-tooltip.bottom.hover="$t('Open CV')"
                                                v-if="c.last_cv"
                                            >
                                                <i class="tdi td-paper-download td-lg"></i>
                                            </a>
                                            <send-message
                                                :candidates="[c]"
                                                :disabled="readOnly"
                                                button-size="xs"
                                                :show-button-text="false"
                                                :show-missing-email="false"
                                                :button-icon-class="['td-lg', {'text-warning': c.messages_count > 0}]"
                                                @message-sent="handleMessageSent"
                                                @sms-sent="handleSMSSent"
                                                @video-message-sent="handleVideoMessageSent"
                                            ></send-message>
                                            <a
                                                href="#"
                                                class="btn btn-xs btn-white"
                                                :class="{'text-warning': c.comments_count > 0}"
                                                @click.prevent="openCandidateModal(c, 'commentsTab')"
                                                v-b-tooltip.bottom.hover="$t('View comments')"
                                            >
                                                <i class="tdi td-chat td-lg"></i>
                                            </a>
                                            <a
                                                href=""
                                                class="btn btn-xs btn-white"
                                                @click.prevent="showEventSetModal([c])"
                                                v-if="!c.accepted_invites_count"
                                                v-b-tooltip.bottom.hover="$t('Schedule interview')"
                                            >
                                                <i
                                                    class="tdi td-calendar td-lg"
                                                    :class="{'text-warning': c.pending_invites_count > 0}"
                                                ></i>
                                            </a>
                                            <a
                                                href=""
                                                @click.prevent="openCandidateModal(c, 'invitesTab')"
                                                v-else
                                                class="btn btn-xs btn-white"
                                                v-b-tooltip.bottom.hover="$t('View invites')"
                                            >
                                                <i class="tdi td-calendar td-lg text-success"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <template v-if="!project.is_template">
            <b-modal
                ref="candidateModal"
                scrollable
                @ok="openCandidate = null"
                @cancel="openCandidate = null"
                @hidden="openCandidate = null"
                @close="openCandidate = null"
                modal-class="fixed-height-modal candidate-profile-modal"
                body-class="d-flex flex-column p-0"
                no-close-on-backdrop
                hide-header
                hide-footer
            >
                <candidate-profile
                    :candidate="openCandidate"
                    v-if="openCandidate"
                    ref="openProfile"
                    :input-project="project"
                    :key="`profile-${openCandidate.id}`"
                    :pii-mode="modalPiiMode"
                    :tab-to-open="modalTabToOpen"
                    @anonymized="handleAnonymized"
                    @loaded="modalTabToOpen = null"
                    @scorecardupdated="candidateScorecardUpdated"
                    @close="openCandidate = null"
                    @addtoproject="addToProject"
                    @share="showPresentModal($event)"
                    @retry-summary="retrySummary"
                >
                    <template #top-right>
                        <candidate-stage-prev-next-buttons
                            v-if="project.stages[getCurrentStageIdxForCandidate(openCandidate)]"
                            @change="openCandidateModal($event)"
                            :project="project"
                            :candidate-review-state="candidateReviewState"
                            :current-stage="project.stages[getCurrentStageIdxForCandidate(openCandidate)]"
                            @stage="moveAndCommit(openCandidate, $event)"
                        ></candidate-stage-prev-next-buttons>
                    </template>
                </candidate-profile>
            </b-modal>
            <form-modal
                :title="$t('Add candidate')"
                v-model="showCandidateForm"
                form-name="CandidateForm"
                ref="candidateForm"
                :params="{project_id: project.id}"
                @loaded="candidateFormLoaded"
                @response="candidateFormResponse"
                @form-fail="candidateFormFailOrError"
                @form-error="candidateFormFailOrError"
            >
                <div slot="custom-modal-footer">
                    <div class="d-flex align-items-center gap-2">
                        <b-button
                            size="sm"
                            variant="white"
                            @click="showCandidateForm = false"
                        >
                            {{ $t("Cancel") }}
                        </b-button>
                        <b-button
                            size="sm"
                            variant="white-success"
                            :disabled="!!savingCandidate"
                            @click="saveCandidate()"
                        >
                            <template v-if="savingCandidate === 'regular'">
                                {{ $t("Saving...") }}
                                <b-spinner
                                    class="ml-2"
                                    small
                                ></b-spinner>
                            </template>
                            <template v-else>
                                {{ $t("Save") }}
                            </template>
                        </b-button>
                        <b-button
                            size="sm"
                            variant="success"
                            :disabled="!!savingCandidate"
                            @click.prevent="saveCandidate('add')"
                        >
                            <template v-if="savingCandidate === 'add'">
                                {{ $t("Saving...") }}
                                <b-spinner
                                    class="ml-2"
                                    small
                                />
                            </template>
                            <template v-else>
                                {{ $t("Save candidate and add to current project") }}
                            </template>
                        </b-button>
                    </div>
                </div>
            </form-modal>
            <presentation-modal
                :candidates="candidatesToPresent"
                :project="project"
                @sent="selected = []"
                @close="candidatesToPresent = []"
            ></presentation-modal>
            <event-set-modal
                :candidates="candidatesToInterview"
                :project="project"
                @close="candidatesToInterview = []"
                @sent="
                    candidatesToInterview.map(c => c.pending_invites_count++);
                    $refs.openProfile ? $refs.openProfile.initialize() : () => {};
                    selected = [];
                "
            ></event-set-modal>
            <screener-modal
                :candidates="candidatesToScreen"
                :project="project"
                @close="candidatesToScreen = []"
            ></screener-modal>
            <form-modal
                :title="$t('Create a task')"
                v-model="showCreateTaskModalState"
                form-name="TasksForm"
                :to-load="{candidate_ids: showCreateTaskModalTargetCandidateIds.map(c => c.id), project_id: project.id}"
                @response="handleTasksCreated($event)"
            >
            </form-modal>
            <form-modal
                v-model="showBulkCommentModal"
                form-name="BulkCommentForm"
                :title="$t('Add bulk comments')"
                :params="{project_id: project.id}"
                :to-load="{candidate_ids: selected.map(c => c.id), project_id: project.id, rating: null}"
                @response="handleCommentsCreated($event)"
            >
            </form-modal>
            <form-modal
                v-model="showBulkTagModal"
                form-name="BulkTagForm"
                :title="$t('Add bulk tags')"
                :to-load="{candidate_ids: selected.map(c => c.id), project_id: project.id}"
                @response="handleTagsCreated($event)"
            >
            </form-modal>
            <form-modal
                :title="$t('Import candidates')"
                :form-name="`XlsxImportForm?project_id=${project.id}`"
                v-model="showXlsxImportModal"
                :to-load="{}"
                @response="handleXlsxImportDone"
            ></form-modal>
            <form-modal
                :title="$t('Send video interview invites')"
                form-name="VideoInterviewSendForm"
                v-model="showVideoInterviewInviteModal"
                :to-load="{candidates: selected.map(c => c.id)}"
                @success="
                    selected.map(c => c.pending_video_invites_count++);
                    selected = [];
                    track('Sent video interview');
                "
                :options-to-load="{
                    candidates: selected.map(c => {
                        return {label: c.name, value: c.id};
                    }),
                }"
                :label-save="$t('Send video interview invites')"
                :label-success="$t('Video interviews sent!')"
            ></form-modal>
            <form-modal
                :title="$t('Create video call')"
                form-name="VideoCallForm"
                v-model="showVideoCallModal"
                :to-load="{candidates: selected.map(c => c.id), project_id: project.id}"
                :options-to-load="{
                    candidates: selected.map(c => {
                        return {label: c.name, value: c.id};
                    }),
                }"
                @response="handleVideoCallCreated($event)"
                @success="track('Created quick video call')"
                :label-save="$t('Create call')"
                :label-success="$t('Video call created!')"
            ></form-modal>
            <form-modal
                :form-name="`CandidateDropoutForm`"
                v-model="dropoutApplications.length"
                :to-load="{
                    dropout_applications: dropoutApplications,
                }"
                size="lg"
                :title="$t('Add dropout reason')"
                :label-save="$t('Add dropout reason')"
                :label-success="$t('Dropout reason added.')"
                :label-cancel="$t('Skip')"
                @success="handleDropoutReasonAddSuccess"
                @cancel="handleDropoutReasonCancel"
            >
                <template v-slot:custom-modal-footer="slotProps">
                    <div class="mr-auto">
                        <b-button
                            v-if="$rlSettings.user.role === 'admin'"
                            variant="white"
                            @click="slotProps.send({disable: true})"
                            v-b-tooltip.bottom="
                                $t(
                                    'This will apply to all users. It can later be turned back on in Organization settings.'
                                )
                            "
                        >
                            {{ $t("Switch off asking for dropout reasons") }}
                        </b-button>
                    </div>
                    <b-button
                        variant="white"
                        @click="slotProps.cancel()"
                        class="mr-2"
                    >
                        {{ slotProps.labelCancel }}
                    </b-button>
                    <b-button
                        variant="primary"
                        :disabled="slotProps.sending"
                        @click="slotProps.send()"
                    >
                        <template
                            v-if="slotProps.sending"
                            class="ml-2"
                        >
                            {{ slotProps.labelSaving }}
                            <b-spinner small></b-spinner>
                        </template>
                        <template v-else>
                            {{ slotProps.labelSave }}
                        </template>
                    </b-button>
                </template>
            </form-modal>
            <b-modal
                no-close-on-backdrop
                v-model="showCopyToOtherProjectModal"
                hide-footer
                size="lg"
                header-class="align-items-center border-bottom"
            >
                <template #modal-header="{close}">
                    <h2 class="mb-0">{{ $t("Copy to another project") }}</h2>
                    <b-button
                        size="sm"
                        variant="white"
                        class="btn-circle"
                        @click="close()"
                    >
                        <i class="tdi td-close"></i>
                    </b-button>
                </template>
                <div>
                    <stage-selector
                        v-model="addToStageId"
                        :exclude-project-id="this.project.id"
                    ></stage-selector>
                    <button
                        class="btn btn-white-success ml-auto mt-5"
                        :disabled="!addToStageId"
                        @click="addToStage()"
                    >
                        <b-spinner
                            small
                            class="mr-1"
                            v-if="addingToStage"
                        ></b-spinner>
                        {{ $t("Copy to project") }}
                    </button>
                </div>
            </b-modal>
            <form-modal
                form-name="ProjectCloneForm"
                v-model="showProjectCloneForm"
                :title="projectCloneFormTitle"
                :to-load="projectCloneFormPrefill"
                :params="projectCloneFormParams"
                :label-save="projectCloneFormSaveLabel"
                :label-success="projectCloneFormSuccessLabel"
                @response="redirectTo(`/projects/${$event.payload.updates.id}`)"
                @loaded="projectCloneFormLoaded"
                ref="projectCloneFormModal"
            />
            <b-toast
                variant="info"
                v-model="remoteUpdated"
                no-close-button
                no-auto-hide
            >
                <template #toast-title>
                    {{ $t("Project updated") }}
                </template>
                <div class="d-flex justify-content-between align-items-center">
                    <p class="mb-0">{{ $t("Click to see the changes.") }}</p>
                    <button
                        class="btn btn-sm btn-white"
                        @click="
                            $inertia.reload();
                            remoteUpdated = false;
                        "
                    >
                        {{ $t("Reload") }}
                    </button>
                </div>
            </b-toast>
        </template>
    </div>
</template>

<script lang="ts">
import Vue, {defineComponent} from "vue";

import {
    BButton,
    BButtonGroup,
    BCollapse,
    BDropdown,
    BDropdownGroup,
    BDropdownItem,
    BFormInput,
    BInputGroup,
    BInputGroupText,
    BModal,
    BSpinner,
    BToast,
    ModalPlugin,
    ToastPlugin,
    VBModal,
    VBTooltip,
} from "bootstrap-vue";
import draggable from "vuedraggable";
import {debounce, isNil} from "lodash";
import Laraform, {LaraformForm} from "@laraform/laraform/src";
import PresentationModal from "../../forms/PresentationModal.vue";
import EventSetModal from "../../forms/EventSetModal.vue";
import FormModal from "../../forms/FormModal.vue";
import CandidateCard from "../components/CandidateCard.vue";
import CandidateProfile from "../../common/CandidateProfile.vue";
import {colors, getRatingAsEmoji, initSentry, setMoment} from "../../common/config";
import {OverlayScrollbarsComponent} from "overlayscrollbars-vue";
import {
    candidateAverageScore,
    convertProjectToTemplate,
    doSort,
    getProjectCloneFormParams,
    getProjectCloneFormPrefill,
    getProjectCloneFormSaveLabel,
    getProjectCloneFormSuccessLabel,
    getProjectCloneFormTitle,
    handleProjectCloneFormLoaded,
    isProjectCloneFormVisible,
    SortOrder,
    SortType,
} from "../util";
import {setUpLaraform} from "../../common/LF_config";
import QuickComment from "../components/QuickComment.vue";
import QuickRate from "../components/QuickRate.vue";
import Lockr from "lockr";
import CheckboxDropdown from "../../common/CheckboxDropdown.vue";
import StyledCheckbox from "../../common/StyledCheckbox.vue";
import Calendar from "../../common/Calendar.vue";
import InlineTags from "../../common/InlineTags.vue";
import {STAGE_CATEGORY_COLORS} from "../../common/stageCategories";
import {ACTION_ICONS} from "../../constants/action_types";
import i18n from "../../common/i18n";
import axios, {AxiosError, AxiosResponse} from "axios";
import ProjectBoardColumn from "./ProjectBoardColumn.vue";
import SendMessage from "../../common/SendMessage.vue";
import StageSelector from "../../common/elements/components/StageSelector.vue";
import {redirectTo, reload, showCopyCandidateToast} from "../../common/util";
import {addListener, removeListener} from "resize-detector";
import BulkAnonymizes from "../../candidate/components/BulkAnonymizes.vue";
import CandidateStagePrevNextButtons from "../../common/components/CandidateStagePrevNextButtons.vue";
import {LoadedShowProject} from "../util";
import ScreenerModal from "./ScreenerModal.vue";

setMoment();
setUpLaraform(Laraform);

initSentry();
Vue.use(Laraform);

Vue.use(ToastPlugin);
Vue.use(ModalPlugin);
Vue.directive("b-tooltip", VBTooltip);

const LAYOUT_COLUMNS = "layout_columns";
const LAYOUT_TABLE = "layout_table";

function displayFatalError(vm: any, message: string) {
    vm.$bvModal
        .msgBoxOk(message, {
            size: "md",
            buttonSize: "md",
            okVariant: "danger",
            headerClass: "p-2 border-bottom-0",
            footerClass: "p-2 border-top-0",
            centered: true,
        })
        .finally(() => {
            window.location.reload();
        });
}

export default defineComponent({
    name: "ProjectBoard",
    i18n,
    components: {
        ScreenerModal,
        BToast,
        CandidateStagePrevNextButtons,
        SendMessage,
        ProjectBoardColumn,
        BModal,
        BCollapse,
        Calendar,
        draggable,
        BFormInput,
        BInputGroup,
        BInputGroupText,
        BDropdown,
        BButtonGroup,
        BDropdownItem,
        BDropdownGroup,
        BSpinner,
        BButton,
        CandidateProfile,
        PresentationModal,
        EventSetModal,
        FormModal,
        CandidateCard,
        "overlay-scrollbars": OverlayScrollbarsComponent,
        QuickComment,
        QuickRate,
        CheckboxDropdown,
        StyledCheckbox,
        InlineTags,
        StageSelector,
    },
    mixins: [BulkAnonymizes],
    props: {
        project: {
            type: Object as () => LoadedShowProject,
            required: true,
        },
        users: {
            type: Array as () => User[],
            required: true,
        },
    },
    directives: {
        "b-modal": VBModal,
    },
    data() {
        const sortCandidatesBy = Lockr.get(`project_${this.project.id}_candidate_sort`, "unseen_activity_count");
        const sortCandidatesOrder = Lockr.get(`project_${this.project.id}_candidate_sort_order`, "desc");

        const firstCategory = window.stageCategories?.[0];
        let defaultNewStageCategory;
        if (firstCategory) {
            defaultNewStageCategory = this.$rlSettings.use_custom_stage_categories
                ? firstCategory.custom_stage_category_id
                : firstCategory.category_id;
        } else {
            defaultNewStageCategory = null;
        }

        return {
            query: "" as string,
            stageCategories: window.stageCategories as any,
            selected: [] as Candidate[],
            drag: false as boolean,
            transitionQueue: [] as StageTransition[],
            database: {
                data: [],
                total: 0,
                current_page: 0,
                from: 0,
                last_page: 0,
                last_page_url: "",
                next_page_url: "",
                path: "",
                per_page: 0,
                prev_page_url: "",
                to: 0,
            } as PaginatedResults<Candidate>,
            databaseLoading: false as boolean,
            showEventModal: false,
            sendingEvent: false,
            openCandidate: null as Candidate | null,
            showDatabase: Lockr.get("showDatabase", false),
            showCNpsTooltip: Lockr.get("showCNpsTooltip", true),
            showCandidateForm: false,
            savingCandidate: null as null | string,
            candidatesToPresent: [] as Candidate[],
            candidatesToInterview: [] as Candidate[],
            candidatesToScreen: [] as Candidate[],
            processingCandidates: [] as Candidate[],
            uploadsWithoutEmail: [] as Candidate[],
            recentlyAdded: [] as Candidate[],
            modalTabToOpen: null as null | string,
            showAdvancedSearch: false,
            advancedFilters: {
                hasCommentsByUserIds: [],
                submissionAfter: null as string | null,
                hasNewActivities: false,
                showOnlyUnderage: false,
            },
            advancedLoading: false,
            passAdvancedFilters: [] as number[],
            loadingAdvanced: false,
            showCreateTaskModalState: false,
            showCreateTaskModalTargetCandidateIds: [] as Candidate[],
            showBulkCommentModal: false,
            showBulkTagModal: false,
            showToolbox: false,
            moving: false,
            moveStartEvent: null as MouseEvent | null,
            scrollMaxX: null as null | number,
            scrollMaxY: null as null | number,
            colors: colors,
            layout: Lockr.get(`project_view_layout${this.project.id}_collapsed`, LAYOUT_COLUMNS),
            hoverEndTimeout: null as null | number,
            droppedFiles: [] as DroppedFile[],
            stagedStage: null as null | Stage,
            showShortcuts: false as boolean,
            showXlsxImportModal: false as boolean,
            showSmsModal: false as boolean,
            showMessageModal: false,
            dbLoadingMore: false,
            showJobAdModal: false,
            showVideoInterviewInviteModal: false,
            showVideoMessageModal: false,
            showVideoCallModal: false,
            showAddUsersModal: false,
            showStructuredJobAdForm: false,
            dropoutApplications: [],
            isMobile: window.innerWidth < 600,
            showPublishInfoModal: false,
            addingStage: false,
            showNpsResultsModal: false,
            sortCandidatesBy: sortCandidatesBy as SortType,
            sortCandidatesOrder: sortCandidatesOrder as SortOrder,
            showStatsModal: false,
            statsModalAvailable: true,
            summaryTimeoutId: null as null | number,
            newStageName: "",
            newStageCategory: defaultNewStageCategory,
            showStageCategory: Lockr.get("project_view_show_stage_category") ?? false,
            addToStageId: null,
            addingToStage: false,
            showCopyToOtherProjectModal: false,
            actionButtonWidth: null as null | number,
            collapseActionButtonTexts: false,
            cloningProjectOptions: null as {project: Project; asTemplate: boolean} | null,
            candidateReviewState: {
                latestStageIndex: null as null | number,
                latestCandidateIndex: null as null | number,
            },
            remoteUpdated: false,
        };
    },
    created() {
        console.log("Subscribing to", this.updatesChannelName, this.$echo);
        this.$echo.private(this.updatesChannelName).listen("ProjectBoardUpdated", (e: any) => {
            console.log("Received", e);
            this.remoteUpdated = true;
        });

        TD.$on("candidate-tags-updated", (candidate: Candidate) => {
            if (candidate.pivot?.stage_id) {
                this.refreshStage(candidate.pivot?.stage_id, this);
            }
        });

        TD.$on("candidate-dropout-reason-updated", (candidate: Candidate) => {
            if (candidate.pivot?.stage_id) {
                this.refreshStage(candidate.pivot?.stage_id, this);
            }
        });

        TD.$on("action-triggered-stage", (stageId: Number) => {
            this.refreshStage(stageId, this);
        });
    },
    beforeUnmount() {
        TD.$off("candidate-tags-updated");
        TD.$off("candidate-dropout-reason-updated");
    },
    mounted() {
        window.context = {
            project_id: this.project.id,
        };
        this.$nextTick(() => {
            this.filterDatabase("", this);
            this.ensureSummaries();
        });

        window.addEventListener("resize", () => {
            const someDiv = document.getElementById("project-columns-container");
            if (someDiv) {
                const distanceToTop = someDiv.getBoundingClientRect().top;
                someDiv.style.height = window.innerHeight - distanceToTop + "px";
            }
        });

        const someDiv = document.getElementById("project-columns-container");
        if (someDiv) {
            const distanceToTop = someDiv.getBoundingClientRect().top;
            someDiv.style.height = window.innerHeight - distanceToTop + "px";
        }

        window.addEventListener("beforeunload", e => {
            if (!this.loadingAdvanced && this.transitionQueue.length === 0) {
                return undefined;
            }
            const confirmationMessage = "If you leave before saving, your changes will be lost.";
            (e || window.event).returnValue = confirmationMessage; //Gecko + IE
            return confirmationMessage; //Gecko + Webkit, Safari, Chrome etc.
        });

        const candidateId = new URL(window.location.href).searchParams.get("candidate_id");
        if (candidateId) {
            type StageCandidatePair = [Stage, Candidate];
            const pair: StageCandidatePair | undefined = this.project.stages
                .flatMap((stage: Stage): StageCandidatePair[] => stage.candidates.map(candidate => [stage, candidate]))
                .find(([stage, candidate]: StageCandidatePair) => candidate?.id === parseInt(candidateId));

            if (pair) {
                const [stage, candidate] = pair;
                const tabRef = new URL(window.location.href).searchParams.get("tab_ref");
                this.openCandidateModal(candidate, tabRef, stage.summary_mode);
                history.pushState(null, "", location.href.split("?")[0]);
            }
        }

        const actionButtonRef = this.$refs.actionButtons as HTMLElement | null;

        // Action buttons are not there for templates.
        if (actionButtonRef) {
            this.actionButtonWidth = actionButtonRef.offsetWidth;
            this.handleActionButtonFit();
            addListener(actionButtonRef, this.handleActionButtonFit);
        }

        this.addScrollEventDispatcherToOverlayScrollbars();
    },
    beforeDestroy() {
        const buttonContainer = this.$refs.buttonContainer as HTMLElement | null;
        if (buttonContainer) {
            removeListener(buttonContainer);
        }
        if (this.summaryTimeoutId) {
            clearTimeout(this.summaryTimeoutId);
        }
        window.context = {};
        console.log("Leaving", this.updatesChannelName);
        this.$echo.leave(this.updatesChannelName);
    },
    methods: {
        isNil,
        redirectTo,
        getEmoji: getRatingAsEmoji,
        hideCNpsTooltip() {
            this.showCNpsTooltip = false;
            window.analytics.track("Closed cNPS stage tooltip");
            Lockr.set("showCNpsTooltip", false);
        },
        addScrollEventDispatcherToOverlayScrollbars() {
            if (!this.$refs.mainScroll) {
                return;
            }

            this.$refs.mainScroll.osInstance().options({
                callbacks: {
                    onScrollStart: () => {
                        // emit custom event to window
                        window.dispatchEvent(new CustomEvent("board-scroll-start"));
                    },
                },
            });
        },
        showCNpsHelp() {
            // @ts-ignore
            this.$refs.help.show();
            window.analytics.track("Opened cNPS help", {trigger: "project_stage_tip"});
        },
        setNotifications(subscribed: boolean) {
            this.project.current_user_is_subscribed = subscribed;
            axios.post(`/projects/${this.project.id}/toggleSubscription`, {
                subscribed: subscribed,
            });
        },
        applyAdvancedFilters() {
            this.loadingAdvanced = true;
            axios.post(`/projects/${this.project.id}/advancedSearch`, this.advancedFilters).then(res => {
                this.passAdvancedFilters = res.data;
                this.loadingAdvanced = false;
            });
        },
        toggleDatabase() {
            this.showDatabase = !this.showDatabase;
        },
        getFilteredItems(cs: Candidate[]) {
            const q = this.query.toLowerCase();
            return cs.filter(candidate => {
                let passAdvanced = false;
                if (this.advancedFiltersApplied) {
                    passAdvanced = this.passAdvancedFilters.includes(candidate.id || 0);
                } else {
                    passAdvanced = true;
                }

                const nameMatches = (candidate.name || "").toLowerCase().includes(q);
                const emailMatches = (candidate.email || "").toLowerCase().includes(q);
                const phoneMatches = (candidate.phone_e164 || "").toLowerCase().includes(q);
                const tagMatches =
                    (
                        candidate.tags?.filter(t => {
                            return t.name.toLowerCase().includes(q);
                        }) || []
                    ).length > 0;
                const employerMatches: boolean =
                    [candidate.last_employment].filter(e => {
                        if (e === null) {
                            return false;
                        }
                        return (
                            e.employer_name?.toLowerCase().includes(q) || e.position_title?.toLowerCase().includes(q)
                        );
                    }).length > 0;
                const educationMatches: boolean =
                    candidate.educations.filter(e => {
                        return e.institution?.toLowerCase().includes(q) || e.programme?.toLowerCase().includes(q);
                    }).length > 0;
                const isNew = this.recentlyAdded.includes(candidate);
                return (
                    (nameMatches ||
                        isNew ||
                        tagMatches ||
                        phoneMatches ||
                        employerMatches ||
                        educationMatches ||
                        emailMatches) &&
                    passAdvanced
                );
            });
        },
        getTableItems(project: Project): Candidate[] {
            return this.getFilteredItems(
                project.stages
                    .map((s: Stage) => {
                        return s.candidates.map((c: Candidate) => ({
                            ...c,
                            stage: s,
                        }));
                    })
                    .flat()
            ).sort((a, b) => {
                return ("" + b.first_activity_datetime).localeCompare(a.first_activity_datetime + "");
            });
        },
        moveAndCommit(candidate: Candidate, toStage: Stage) {
            if (this.candidateReviewState.latestCandidateIndex !== null) {
                this.candidateReviewState.latestCandidateIndex--;
            }
            this.moveCandidateToStageAndQueueTransitions(candidate, toStage);
            this.commit(this.transitionQueue, this);
        },
        doTableStageChange(c: Candidate, evt: Event): void {
            const target = evt.target as any;
            if (!target) {
                return;
            }
            const nextStage = this.project.stages.find(s => s.id == target.value || 0);
            if (nextStage) {
                this.moveCandidateToStageAndQueueTransitions(c, nextStage);
                this.commit(this.transitionQueue, this);
            }
        },
        selectAll(candidates: Candidate[]) {
            const selectedIds = this.selected.map(c => c.id);

            if (candidates.filter(c => selectedIds.includes(c.id)).length > 0) {
                candidates.forEach(c => {
                    const idx = this.selected.findIndex(sc => sc.id === c.id);
                    if (idx !== -1) {
                        this.selected.splice(idx, 1);
                    }
                });
            } else {
                candidates.forEach(c => {
                    this.selected.push(c);
                });
            }
        },
        showPresentModal(items: Candidate[]) {
            this.candidatesToPresent = [...items];
        },
        displayMessageModal(items: Candidate[]) {
            this.selected = [...items];
            this.showMessageModal = true;
        },
        showEventSetModal(items: Candidate[]) {
            this.candidatesToInterview = [...items];
        },
        showCreateTaskModal(targetCandidates: Candidate[]): void {
            this.showCreateTaskModalState = true;
            this.showCreateTaskModalTargetCandidateIds = targetCandidates;
        },
        moveSelectedToNextStage(nextStageArg?: Stage) {
            if (this.readOnly) {
                return;
            }

            for (const c of this.selected) {
                const currentStageIdx: number = this.project.stages.findIndex((s: Stage): boolean => {
                    return s.candidates.includes(c);
                });
                const nextStage: Stage = nextStageArg || this.project.stages[currentStageIdx + 1];
                this.moveCandidateToStageAndQueueTransitions(c, nextStage);
            }
            console.log("queue", this.transitionQueue);
            this.commit(this.transitionQueue, this);
            this.selected = [];
        },
        getCurrentStageIdxForCandidate(c: Candidate): number {
            return this.project.stages.findIndex((s: Stage): boolean => {
                return s.candidates.includes(c);
            });
        },
        moveCandidateToStageAndQueueTransitions(c: Candidate, nextStage: Stage) {
            const currentStageIdx: number = this.getCurrentStageIdxForCandidate(c);
            if (currentStageIdx !== -1 && nextStage) {
                const currentStage = this.project.stages[currentStageIdx];
                this.moveCandidateToStage(c, currentStage, nextStage || this.project.stages[currentStageIdx + 1]);
                this.transitionQueue.push({
                    stage: {id: currentStage.id},
                    change: {
                        removed: {
                            element: {
                                id: c.id,
                            },
                            oldIndex: 0,
                        },
                    },
                });
                this.transitionQueue.push({
                    stage: {id: nextStage.id, category: nextStage.category},
                    change: {
                        added: {
                            element: {
                                id: c.id,
                                pivot: {
                                    id: c.pivot?.id,
                                },
                            },
                            newIndex: 0,
                        },
                    },
                });
            }
        },
        moveCandidateToStage(candidate: Candidate, from: Stage, to: Stage): void {
            from.candidates.splice(from.candidates.indexOf(candidate), 1);
            to.candidates.unshift(candidate);
            if (!candidate.pivot) {
                candidate.pivot = {
                    stage_id: to.id ?? 0,
                    candidate_id: candidate.id ?? 0,
                    sort_order: 0,
                };
            } else {
                candidate.pivot.stage_id = to.id ?? 0;
            }
        },
        handleOk(evt: Event) {
            evt.preventDefault();

            (this.$refs["cform"] as LaraformForm).submit();
        },
        handleVideoCallCreated(res: any) {
            if (res.payload.redirect_url) {
                window.location = res.payload.redirect_url;
            } else {
                window.location.assign(`/video-call/${res.payload.updates.id}/redirect`);
            }
        },
        handleMessageSent() {
            this.selected.forEach((c: Candidate) => {
                if (c.messages_count !== undefined) {
                    c.messages_count++;
                }
            });

            if (this.$refs.openProfile) {
                // @ts-ignore
                this.$refs.openProfile.initialize();
            }

            this.selected = [];
        },
        handleSMSSent() {
            this.selected.forEach((c: Candidate) => {
                if (c.sms_count !== undefined) {
                    c.sms_count++;
                }
            });

            this.selected = [];
            this.track("Sent SMS");
        },
        handleVideoMessageSent() {
            this.selected.forEach((c: Candidate) => {
                if (c.messages_count !== undefined) {
                    c.messages_count++;
                }
            });

            this.selected = [];
            this.track("Sent video message");
        },
        loadMoreDatabase() {
            this.dbLoadingMore = true;
            axios
                .get("/candidates", {
                    params: {
                        search: this.query,
                        project_id: this.project.id,
                        search_from_name: 1,
                        search_from_position: 1,
                        search_from_employer: 1,
                        search_from_files: 1,
                        search_from_tags: 1,
                        page: this.database.current_page + 1,
                    },
                })
                .then((res: AxiosResponse<PaginatedResults<Candidate>>) => {
                    this.database.data = this.database.data.concat(res.data.data);
                    this.database.current_page = res.data.current_page;
                    this.dbLoadingMore = false;
                });
        },
        filterDatabase: debounce((v: string, vm) => {
            if (vm.$rlSettings.user.role === "limited") {
                return;
            }
            vm.databaseLoading = true;
            vm.dbPage = 1;
            axios
                .get("/candidates", {
                    params: {
                        search: v,
                        project_id: vm.project.id,
                        search_from_name: 1,
                        search_from_position: 1,
                        search_from_employer: 1,
                        search_from_files: 1,
                        search_from_tags: 1,
                    },
                })
                .then((res: AxiosResponse<PaginatedResults<Candidate>>) => {
                    vm.database = res.data;
                    vm.databaseLoading = false;
                });
        }, 300),
        commit: debounce((queue: StageTransition[], vm: any, callback: (vm: any) => void = (vm: any) => {}) => {
            try {
                window.onbeforeunload = () => {
                    return vm.$t("If you leave before saving, your changes will be lost.").toString();
                };

                const items: StageTransition[] = JSON.parse(JSON.stringify(queue));
                vm.transitionQueue = [];
                vm.loadingAdvanced = true;

                const affectedStageIds = vm.sortCandidatesBy === "custom" ? items.map(i => i.stage?.id) : [];

                const sortOrders = vm.project.stages
                    .filter((stage: Stage) => affectedStageIds.includes(stage.id))
                    .map((stage: Stage) => {
                        return {
                            stage_id: stage.id,
                            candidate_order: stage.candidates.map((c: Candidate) => c.id),
                        };
                    });

                const transitions = items
                    .filter(item => !!item.change.added)
                    .map(item => {
                        return {
                            candidate_id: item.change.added?.element.id,
                            application_id: item.change.added?.element.pivot?.id,
                            to_stage_id: item.stage?.id,
                        };
                    });

                axios
                    .post(`/projects/${vm.project.id}/commitStageTransitions`, {
                        transitions,
                        stage_sort_orders: sortOrders,
                    })
                    .then((res: AxiosResponse<{moved_applications: Application[]; updated_stages: Stage[]}>) => {
                        window.analytics.track("Moved candidates between stages");
                        vm.loadingAdvanced = false;

                        if (res.data.moved_applications) {
                            const movedCandidateIds = res.data.moved_applications.map((a: any) => a.candidate_id);
                            const movedCandidates = vm.project.stages
                                .flatMap((s: Stage) => s.candidates)
                                .filter((c: Candidate) => movedCandidateIds.includes(c.id ?? 0));
                            movedCandidates.forEach((c: Candidate) => {
                                c.pivot = res.data.moved_applications.find((a: any) => a.candidate_id === c.id);
                                if (c.last_stage_entry_activity) {
                                    c.last_stage_entry_activity.days_in_stage = 0;
                                }
                            });

                            if (vm.$rlSettings.ask_for_dropout_reason) {
                                const dropoutStageIds = vm.project.stages
                                    .filter((s: Stage) => s.category === 50)
                                    .map((s: Stage) => s.id);

                                const applicationsMovedToDropout = res.data.moved_applications.filter((a: any) =>
                                    dropoutStageIds.includes(a.stage_id)
                                );

                                if (applicationsMovedToDropout.length > 0) {
                                    vm.dropoutApplications = applicationsMovedToDropout.map((a: Application) => a.id);
                                }
                            }
                            // count all candidates in stages where category is submissions
                            vm.project.applications_pending_screening_count = vm.project.stages
                                .filter((s: Stage) => s.category === 10)
                                .reduce((acc: number, s: Stage) => acc + s.candidates.length, 0);
                        }

                        if (res.data.updated_stages) {
                            res.data.updated_stages.forEach((stage: Stage) => {
                                vm.applyStageUpdate(stage);
                            });
                        }

                        vm.ensureSummaries();
                        callback(vm);
                    })
                    .catch((err: AxiosError) => {
                        if (err.response && err.response.status === 403) {
                            vm.$bvToast.toast(
                                vm
                                    .$t(
                                        "You dont have permission to move candidates in project. Your changes will not be saved."
                                    )
                                    .toString(),
                                {
                                    variant: "danger",
                                    solid: true,
                                }
                            );
                        } else {
                            let message;
                            if (err.response && err.response.status === 419) {
                                message = vm
                                    .$t(
                                        "Your session has expired and your last change was not saved. Sorry. We will refresh the page for you."
                                    )
                                    .toString();
                            } else {
                                message = vm
                                    .$t("There was an error committing stage transitions. Please refresh the page.")
                                    .toString();
                            }

                            displayFatalError(vm, message);
                        }

                        vm.loadingAdvanced = false;
                        callback(vm);
                    })
                    .finally(() => {
                        window.onbeforeunload = null;
                    });
            } catch (e) {
                displayFatalError(
                    vm,
                    vm.$t("There was an error committing stage transitions. Please refresh the page.").toString()
                );
                window.onbeforeunload = null;
                console.error(e);
                vm.loadingAdvanced = false;
                return;
            }
        }, 100),
        handleTransition(change: DraggableChange<Candidate>, stage?: Stage) {
            this.transitionQueue.push({
                change: change,
                stage: {id: stage?.id ?? null, category: stage?.category ?? null},
            });
            if (!change.removed?.element && change.added?.element) {
                const candidate = change.added.element;
                this.recentlyAdded.push(candidate);

                if (candidate.rating_avg === undefined) {
                    this.$set(candidate, "rating_avg", null);
                }
                if (candidate.rating_count === undefined) {
                    this.$set(candidate, "rating_count", 0);
                }
            }
            this.commit(this.transitionQueue, this);
        },
        saveCandidate(type = "regular") {
            const modal: any = this.$refs["candidateForm"];
            const form = modal.$refs.form as any;
            modal.send();
            this.savingCandidate = type;

            if (form.invalid) {
                this.savingCandidate = null;
                return;
            }
        },
        candidateFormLoaded(event: any) {
            const modal: any = this.$refs["candidateForm"];
            const form = modal.$refs.form as any;
            this.$nextTick(() => {
                form.el$("email").on("change", (...args: any) => {
                    if (form.el$("email").value) {
                        form.el$("email").value = form.el$("email").value.replace(/\s+/, "");
                    }
                });
            });
        },
        candidateFormResponse(res: any) {
            const modal: any = this.$refs["candidateForm"];
            const form = modal.$refs.form as any;
            if (this.savingCandidate === "regular") {
                this.filterDatabase("", this);
                this.savingCandidate = null;
            } else if (this.savingCandidate === "add") {
                // add to stage
                console.log("adding just-added candidate to stage");
                const candidate = res.payload;
                this.transitionQueue.push({
                    stage: {id: this.project.stages[0].id},
                    change: {
                        added: {
                            element: candidate,
                            newIndex: 0,
                        },
                    },
                });
                this.$emit("reload");
                this.commit(this.transitionQueue, this, vm => {
                    vm.$emit("reload");
                });
            }
            this.savingCandidate = null;
            this.showCandidateForm = false;
            analytics.track("Added candidate manually");
        },
        candidateFormFailOrError() {
            this.savingCandidate = null;
        },
        addToProject(candidate: Candidate) {
            this.database.data.splice(this.database.data.indexOf(candidate), 1);
            this.project.stages[0].candidates.unshift(candidate);
            this.transitionQueue.push({
                stage: {id: this.project.stages[0].id},
                change: {
                    added: {
                        element: candidate,
                        newIndex: 0,
                    },
                },
            });
            this.commit(this.transitionQueue, this);
            this.openCandidate = null;
        },
        handleAnonymized() {
            window.location.reload();
        },
        openCandidateModal(c: Candidate, tabRef: string | null = null) {
            this.modalTabToOpen = tabRef;
            this.openCandidate = c;
            const currentStageIdxForCandidate = this.getCurrentStageIdxForCandidate(c);
            if (currentStageIdxForCandidate !== -1) {
                this.candidateReviewState.latestStageIndex = currentStageIdxForCandidate;
                this.candidateReviewState.latestCandidateIndex =
                    this.project.stages[currentStageIdxForCandidate].candidates.indexOf(c);
            } else {
                this.candidateReviewState.latestStageIndex = null;
                this.candidateReviewState.latestCandidateIndex = null;
            }
        },
        handleTasksCreated(response: any) {
            response.tasks.map((task: Task) => {
                this.project.stages.map(s => {
                    const c = s.candidates.find(c => c.id == task.candidate_id);
                    if (c) {
                        c.incomplete_tasks.push(task);
                    }
                });
            });
            window.analytics.track("Created candidate task");
            this.selected = [];
        },
        handleCommentsCreated(response: any) {
            response.comments.map((comment: Comment) => {
                this.project.stages.map(s => {
                    const c = s.candidates.find(c => c.id == comment.candidate_id) as Candidate | undefined;
                    if (c && c.comments_count !== undefined) {
                        c.comments_count++;
                    }
                });
            });
            this.selected = [];
        },
        handleTagsCreated(response: any) {
            this.project.stages.map(s => {
                const candidates = s.candidates.filter(c => response.candidate_ids.includes(c.id ?? 0));
                candidates.map(c => {
                    console.log("c", c);
                    if (c.tags) {
                        const candidateTagIds = c.tags.map(ct => ct.id);
                        const newTags = response.tags.filter((nt: Tag) => {
                            return !candidateTagIds.includes(nt.id);
                        });
                        c.tags.push(...newTags);
                        c.tags.sort((a, b) => (a.name < b.name ? -1 : 1));
                    } else {
                        c.tags = response.tags;
                    }
                    console.log("c2", c);
                });
            });
            window.analytics.track("Candidate bulk tagged");
            this.selected = [];
        },
        handleActionCreated(response: any) {
            const action: Action = response.payload.action;
            const theStage = this.project.stages.find(s => s.id == action.stage_id);
            if (theStage) {
                theStage.actions.push(action);
            }
            this.selected = [];
        },
        async ensureSummaries(): Promise<void> {
            const candidatesWhoNeedSummaries = this.project.stages
                .filter(stage => stage.summary_mode !== "off")
                .flatMap(stage => stage.candidates)
                .filter(candidate => !candidate.summary && candidate.last_cv);

            if (candidatesWhoNeedSummaries.length === 0) {
                return;
            }

            const response: AxiosResponse<CandidateSummary[]> = await axios.post(
                `/projects/${this.project.id}/ensureSummaries`,
                {candidate_ids: candidatesWhoNeedSummaries.map(c => c.id)}
            );

            for (const summary of response.data) {
                const candidate = candidatesWhoNeedSummaries.find(c => c.id === summary.candidate_id);
                if (candidate) {
                    this.$set(candidate, "summary", summary);
                }
            }

            this.summaryTimeoutId = setTimeout(this.ensureSummaries, 2000);
        },
        handleXlsxImportDone() {
            window.analytics.track("Imported from xlsx", {}, () => {
                window.location.reload();
            });
        },
        async updateStageOrder() {
            const newOrders = this.project.stages.map((stage, index) => {
                stage.sort_order = index + 1;
                return {
                    stage_id: stage.id,
                    new_sort_order: index + 1,
                };
            });
            await axios.post(`/projects/updateStageOrder`, {
                new_orders: newOrders,
            });
            window.analytics.track("Reordered stages by dragging");
        },
        handleStructuredJob(res: any) {
            window.analytics.track("Published job ad from project view", {}, () => {
                if (res.payload.redirect_url) {
                    window.location.href = res.payload.redirect_url;
                } else {
                    window.location.href = "/structured-jobs";
                }
            });
        },
        handleDropoutReasonAddSuccess(res: any) {
            const candidates = this.project.stages
                .flatMap(s => s.candidates)
                .filter(c => this.dropoutApplications.map(d => d.candidate_id).includes(c.id));
            candidates.forEach(c => TD.$emit("candidate-dropout-reason-updated", c));

            this.dropoutApplications = [];
            const disabledDropoutReasons = res.payload?.disabled;
            if (disabledDropoutReasons) {
                this.$rlSettings.ask_for_dropout_reason = false;
                window.analytics.track("Turned off asking for dropout reasons.");
            } else {
                window.analytics.track("Added dropout reason to candidates");
            }
        },
        handleDropoutReasonCancel() {
            this.dropoutApplications = [];
            window.analytics.track("Skipped adding a dropout reason to candidates");
        },
        addStage() {
            if (!this.newStageName || this.readOnly) {
                return;
            }

            const selectedCategory = this.stageCategories.find((c: any) => {
                if (this.$rlSettings.use_custom_stage_categories) {
                    return c.custom_stage_category_id === this.newStageCategory;
                }
                return c.category_id === this.newStageCategory;
            });

            this.addingStage = true;
            axios
                .put(`/projects/${this.project.id}/stages`, {
                    stage_name: this.newStageName,
                    category: selectedCategory?.category_id,
                    custom_stage_category_id: selectedCategory?.custom_stage_category_id,
                })
                .then(({data}) => {
                    this.project.stages.push(data.model);
                    this.newStageName = "";
                    window.analytics.track("Added stage from project view");
                })
                .finally(() => {
                    this.addingStage = false;
                });
        },
        async getCandidateSimilarities() {
            if (this.selected.length) {
                const ids = this.selected.map(c => c.id).join(",");
                const similarities: AxiosResponse<{
                    [key: number]: number;
                }> = await axios.get(`/projects/${this.project.id}/getCandidateSimilarities?candidate_ids=${ids}`);
                this.project.stages.map(s => {
                    s.candidates.map(c => {
                        if (!c.id) return;
                        c.similarity_score = similarities.data[c.id] || 0;
                    });
                });
            }
            if (this.sortCandidatesBy === "similarity_score") {
                this.project.stages.forEach((s: Stage) =>
                    doSort(s.candidates, "similarity_score", this.sortCandidatesOrder)
                );
            } else {
                this.sortWithOrder = "similarity_score.desc";
            }
        },
        clearCandidateSimilarities(): void {
            this.project.stages.map(s => {
                s.candidates.map(c => {
                    c.similarity_score = null;
                });
            });
        },
        averageScore: candidateAverageScore,
        candidateScorecardUpdated(fullCandidate: Candidate) {
            if (this.openCandidate) {
                this.openCandidate.questions_responses = fullCandidate.questions_responses;
            }
            this.$emit("reload");
        },
        track: window.analytics.track,
        addToStage() {
            if (this.$rlSettings.user.role === "limited") {
                return;
            }

            window.analytics.track("Copied candidate(s) to another project", {
                selectedCandidatesCount: this.selected.length,
            });

            this.addingToStage = true;
            const data: {candidate_ids: number[]} = {
                candidate_ids: this.selected.map(candidate => candidate.id).filter((id): id is number => id !== null),
            };
            axios
                .post(`/stages/${this.addToStageId}/addCandidatesToStage`, data)
                .then((response: AxiosResponse<CopyCandidatesResult>) => {
                    showCopyCandidateToast(this, response.data);
                })
                .finally(() => {
                    this.addingToStage = false;
                    this.selected = [];
                    this.showCopyToOtherProjectModal = false;
                });
        },
        refreshStage: async function (stageId: number, vm: any) {
            if (!stageId || isNaN(stageId)) return;

            const {data: stage} = await axios.get(`/stages/${stageId}/get`);
            vm.applyStageUpdate(stage);
        },
        applyStageUpdate(newStage: Stage) {
            const existingStageIndex = this.project.stages.findIndex(s => s.id === newStage.id);
            if (existingStageIndex === -1) return;
            this.$set(this.project.stages[existingStageIndex], "actions", newStage.actions);
        },
        handleActionButtonFit() {
            const buttonContainer = this.$refs.buttonContainer as HTMLElement | null;
            const searchAndSortButtons = this.$refs.searchAndSortButtons as HTMLElement | null;

            if (!buttonContainer || !searchAndSortButtons || !this.actionButtonWidth) {
                return;
            }

            const buttonContainerWidth = buttonContainer.offsetWidth;
            const searchWidth = searchAndSortButtons.offsetWidth;

            this.collapseActionButtonTexts = buttonContainerWidth < searchWidth + this.actionButtonWidth;

            window.dispatchEvent(new Event("resize"));
        },
        bulkAnonymize(): void {
            this.confirmAnonymize(this.selected.length)
                .then(res => {
                    if (res) {
                        const ids = this.selected.map(c => c.id);
                        return axios.post("/candidates/anonymizeByIds", {
                            candidate_ids: ids,
                        });
                    }
                })
                .then(() => {
                    this.handleAnonymized();
                });
        },
        handleDragStart(event: Event) {
            document.body.classList.add("noselect");
        },
        handleDragEnd(event: Event) {
            document.body.classList.remove("noselect");
        },
        retrySummary(candidate: Candidate) {
            axios.post(`/candidates/${candidate.id}/retrySummary`).then(() => {
                this.$set(candidate, "summary", null);
                this.ensureSummaries();
            });
        },
        convertToTemplate(): void {
            convertProjectToTemplate(this.project)
                .then(reload)
                .catch(() =>
                    this.$bvToast.toast(this.$t("Project couldn't be converted to a template.").toString(), {
                        variant: "danger",
                        solid: true,
                    })
                );
        },
        projectCloneFormLoaded(): void {
            handleProjectCloneFormLoaded(this.$refs.projectCloneFormModal as InstanceType<typeof FormModal>);
        },
    },
    watch: {
        sortCandidatesBy(v: SortType, o: SortType) {
            if (v !== "similarity_score") {
                Lockr.set(`project_${this.project.id}_candidate_sort`, v);
            }
            if (o === "similarity_score" && v !== "similarity_score") {
                this.selected = [];
                this.clearCandidateSimilarities();
            }
            this.project.stages.forEach((s: Stage) => doSort(s.candidates, v, this.sortCandidatesOrder));
        },
        sortCandidatesOrder(v: SortOrder) {
            Lockr.set(`project_${this.project.id}_candidate_sort_order`, v);
            this.project.stages.forEach((s: Stage) => doSort(s.candidates, this.sortCandidatesBy, v));
        },
        layout(v) {
            if (v === LAYOUT_COLUMNS) {
                this.$nextTick(() => {
                    const someDiv = document.getElementById("project-columns-container");
                    if (someDiv) {
                        const distanceToTop = someDiv.getBoundingClientRect().top;
                        someDiv.style.height = window.innerHeight - distanceToTop + "px";
                    }
                });
                this.addScrollEventDispatcherToOverlayScrollbars();
            }
            Lockr.set(`project_view_layout${this.project.id}_collapsed`, v);
            window.analytics.track("Project view layout changed", {layout: v});
        },
        openCandidate(v: Candidate | null, old: Candidate | null) {
            if (v) {
                //@ts-ignore
                this.$refs.candidateModal.show();
            } else {
                //@ts-ignore
                this.$refs.candidateModal.hide();
                if (this.selected.length === 1 && this.selected.find(c => c.id === old?.id)) {
                    this.selected = [];
                }
            }
            window.context = {
                project_id: this.project.id,
                candidate_id: this.openCandidate ? this.openCandidate.id : undefined,
            };
        },
        query(v) {
            this.recentlyAdded = [];
            this.filterDatabase(v, this);
        },
        showDatabase(v) {
            Lockr.set("showDatabase", v);
        },
        advancedFilters: {
            handler() {
                this.applyAdvancedFilters();
            },
            deep: true,
        },
        selected(v: Candidate[], o: Candidate[]) {
            if (v.length && v.length !== o.length && this.sortCandidatesBy === "similarity_score") {
                this.getCandidateSimilarities();
            } else if (v.length === 0 && v.length !== o.length && this.sortCandidatesBy === "similarity_score") {
                this.clearCandidateSimilarities();
            }
        },
        showStageCategory(v) {
            Lockr.set("project_view_show_stage_category", v);
        },
    },
    computed: {
        stageCategoryColors(): {[key: number]: string} {
            return STAGE_CATEGORY_COLORS;
        },
        advancedFiltersApplied(): boolean {
            return !!(
                this.advancedFilters.hasCommentsByUserIds.length ||
                this.advancedFilters.submissionAfter ||
                this.advancedFilters.hasNewActivities ||
                this.advancedFilters.showOnlyUnderage
            );
        },
        sortWithOrder: {
            get(): string {
                return this.sortCandidatesBy + "." + this.sortCandidatesOrder;
            },
            set(v: string) {
                const parts = v.split(".");
                this.sortCandidatesBy = parts[0] as SortType;
                this.sortCandidatesOrder = parts[1] as SortOrder;
                window.analytics.track("Sorted candidates", {sort_with_order: v});
            },
        },
        computedStages: {
            get(): Stage[] {
                return [...this.project.stages];
            },
            set(stages: Stage[]): void {
                console.log(stages);
                this.project.stages = stages;
                this.updateStageOrder();
            },
        },
        readOnly(): boolean {
            return this.project.status === 3 || this.project.status === 4;
        },
        commentUserOptions(): BMultiSelectOption[] {
            return this.users.map((u: User) => ({
                label: u.name,
                value: u.id!,
            }));
        },
        ACTION_ICONS(): {[key: string]: string} {
            return ACTION_ICONS;
        },
        disableScheduleInterviews(): boolean {
            return this.readOnly || !this.$rlSettings.controls.interviews.create || this.selected.length === 0;
        },
        sortOptions(): {text: string; value: string}[] {
            return [
                {text: this.$i18n.t("Unseen notifications first").toString(), value: "unseen_activity_count.desc"},
                {text: this.$i18n.t("Longer in stage first").toString(), value: "time_in_stage.desc"},
                {text: this.$i18n.t("Less time in stage first").toString(), value: "time_in_stage.asc"},
                {text: this.$i18n.t("Newer applications first").toString(), value: "apply_date.desc"},
                {text: this.$i18n.t("Older applications first").toString(), value: "apply_date.asc"},
                {text: this.$i18n.t("Higher scorecard score first").toString(), value: "scorecard_score.desc"},
                {text: this.$i18n.t("Lower scorecard score first").toString(), value: "scorecard_score.asc"},
                {text: this.$i18n.t("Higher rated first").toString(), value: "rating.desc"},
                {text: this.$i18n.t("Lower rated first").toString(), value: "rating.asc"},
                {text: this.$i18n.t("References submitted first").toString(), value: "submitted_references.desc"},
                {text: this.$i18n.t("References pending first").toString(), value: "pending_references.desc"},
                {text: this.$i18n.t("Alphabetic").toString(), value: "alphabetic.asc"},
                {text: this.$i18n.t("Sort manually").toString(), value: "custom.desc"},
            ];
        },
        databaseContents(): Candidate[] {
            const candidateIds = this.project.stages.flatMap(s => s.candidates).map(c => c.id);
            const selectedIds = this.selected.map(c => c.id);
            return [
                // First show selected candidates, who have not been added to the project.
                // This ensures that if a user searches for a candidate and selects them,
                // then they won't get lost somewhere in the bottom of the list.
                ...this.selected.filter(c => !candidateIds.includes(c.id)),

                // Then show all non-selected candidates, who have not been added to the project.
                ...this.database.data
                    .filter(c1 => !selectedIds.includes(c1.id))
                    .filter(c2 => !candidateIds.includes(c2.id)),
            ];
        },
        canUserUpdate(): boolean {
            return this.project.is_template
                ? this.$rlSettings.controls.projectTemplates.update
                : this.$rlSettings.controls.projects.update;
        },
        tableSelectAllCheckbox: {
            get(): boolean {
                return this.selected.length === this.getTableItems(this.project).length && this.selected.length !== 0;
            },
            set(v: boolean) {
                if (v) {
                    this.selected = [...this.getTableItems(this.project)];
                } else {
                    this.selected = [];
                }
            },
        },
        showProjectCloneForm: {
            get(): boolean {
                return isProjectCloneFormVisible(this.cloningProjectOptions);
            },
            set(v: boolean) {
                if (!v) {
                    this.cloningProjectOptions = null;
                }
            },
        },
        projectCloneFormTitle(): string | null {
            return getProjectCloneFormTitle(this.cloningProjectOptions);
        },
        projectCloneFormSaveLabel(): string | null {
            return getProjectCloneFormSaveLabel(this.cloningProjectOptions);
        },
        projectCloneFormSuccessLabel(): string | null {
            return getProjectCloneFormSuccessLabel(this.cloningProjectOptions);
        },
        projectCloneFormPrefill(): Record<string, unknown> {
            return getProjectCloneFormPrefill(this.cloningProjectOptions);
        },
        projectCloneFormParams(): Record<string, unknown> {
            return getProjectCloneFormParams(this.cloningProjectOptions);
        },
        modalPiiMode(): SummaryMode | null {
            if (!this.openCandidate) {
                return null;
            }
            const currentStageIdxForCandidate = this.getCurrentStageIdxForCandidate(this.openCandidate);

            if (currentStageIdxForCandidate === -1) {
                return null;
            }

            return this.project.stages[currentStageIdxForCandidate].summary_mode;
        },
        updatesChannelName(): string {
            return `${this.$rlSettings.instance_uuid}.project.${this.project.id}`;
        },
    },
});
</script>
