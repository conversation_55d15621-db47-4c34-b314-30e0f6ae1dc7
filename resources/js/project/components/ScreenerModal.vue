<script lang="ts">
import {defineComponent} from "vue";
import TdModal from "../../common/components/TdModal.vue";
import {PropType} from "vue/types/options";
import {Project} from "@playwright/test";
import axios from "axios";

export default defineComponent({
    name: "ScreenerModal",
    components: {TdModal},
    props: {
        candidates: {
            type: Array as PropType<Candidate[]>,
            required: true,
        },
        project: {
            type: Object as PropType<Project>,
            required: true,
        },
    },
    data() {
        return {
            criteria: [],
            responses: [],
        };
    },
    computed: {
        showModal() {
            return this.candidates.length > 0;
        },
    },
    methods: {
        generateCriteria() {
            axios
                .post(
                    this.route("projects.generateScreenerQuestions", {
                        project: this.project.id,
                    })
                )
                .then(({data}) => {
                    this.criteria = data;
                })
                .catch(error => {});
        },
        runScreener(candidate) {
            axios
                .post(
                    this.route("applications.runTempScreener", {
                        application: candidate.pivot.id,
                    }),
                    {
                        criteria: this.criteria,
                    }
                )
                .then(({data}) => {
                    this.responses.push({
                        candidate_id: candidate.id,
                        response: data,
                    });
                })
                .catch(error => {
                    // Handle error
                });
        },
    },
});
</script>

<template>
    <td-modal
        @close="$emit('close')"
        :value="showModal"
        size="lg"
    >
        <button
            class="btn btn-primary"
            @click="generateCriteria"
        >
            Generate criteria
        </button>
        <div v-for="(c, idx) in criteria">
            <input
                type="text"
                :value="c"
                @input="criteria[idx] = $event.target.value"
            />
        </div>
        <button @click="criteria.push('')">+</button>
        <table>
            <thead>
                <tr>
                    <th>Candidate</th>
                    <th>Run</th>
                    <td v-for="c in criteria">{{ c }}</td>
                </tr>
            </thead>
            <tbody>
                <tr
                    v-for="candidate in candidates"
                    :key="candidate.id"
                >
                    <td>{{ candidate.name }}</td>
                    <td><button @click="runScreener(candidate)">Run</button></td>
                    <td v-for="c in criteria">
                        <div v-if="responses.length > 0">
                            <div
                                v-for="response in responses"
                                :key="response.candidate_id"
                            >
                                <div v-if="response.candidate_id === candidate.id">
                                    {{ response.response.criteria_matches.find(k => k.criterion === c)?.match }}
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </td-modal>
</template>
