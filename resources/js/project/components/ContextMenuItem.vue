<template>
    <div>
        <div @mouseover="handleMouseOverButton"
             @mouseleave="handleMouseLeaveButton"
             ref="button"
        >
            <b-dropdown-item @click="handleClick(item, $event)"
                             :disabled="item.disabled"
                             link-class="d-flex align-items-baseline"
            >
                <span class="mr-1">{{ item.label }}</span>
                <i v-if="isTree(item)" class="ml-auto tdi td-chevron-right"/>
                <i v-else-if="item.iconClass" :class="item.iconClass" class="ml-auto"/>
            </b-dropdown-item>
        </div>
        <portal
            v-if="isTree(item) && !item.disabled && subMenuPosition && (isHoveringButton || isHoveringSubmenu || isHoveringRecursiveSubmenu)">
            <div :style="getSubMenuStyle(subMenuPosition)"
                 class="position-fixed dropdown-menu dropdown-menu-right show border-0"
                 ref="submenu"
                 @mouseover="handleMouseOverSubmenu"
                 @mouseleave="handleMouseLeaveSubmenu"
            >
                <context-menu-item v-for="subItem in item.subItems"
                                   :key="subItem.label"
                                   :item="subItem"
                                   :depth="depth + 1"
                                   @leaf-clicked="$emit('leaf-clicked', $event)"
                                   @mouseover-submenu="handleMouseOverRecursiveSubmenu"
                                   @mouseleave-submenu="handleMouseLeaveRecursiveSubmenu"
                />
            </div>
        </portal>
    </div>
</template>

<script lang="ts">
import {defineComponent} from 'vue';
import {PropType} from "vue/types/options";
import {Position} from './ContextMenu.vue'
import {Portal} from "@linusborg/vue-simple-portal"
import {BDropdownItem} from "bootstrap-vue";
import {StyleValue} from "vue/types/jsx";
import {LocaleMessage} from 'vue-i18n';

const submenuHidingDelayMs = 100;

interface BaseItem {
    label: string|LocaleMessage,
    disabled?: boolean
}

interface LeafItem extends BaseItem {
    iconClass?: VueClassBinding,
    onClick: (event: MouseEvent) => void,
}

interface TreeItem extends BaseItem {
    subItems: MenuItem[]
}

export type MenuItem = LeafItem | TreeItem


export default defineComponent({
    name: 'ContextMenuItem',
    components: {
        Portal,
        BDropdownItem,
    },
    emits: {
        'leaf-clicked': (event: MouseEvent) => true,
        'mouseover-submenu': () => true,
        'mouseleave-submenu': () => true,
    },
    data() {
        return {
            subMenuPosition: null as Position | null,
            isReadyToShow: false as boolean,
            isHoveringButton: false as boolean,
            isHoveringSubmenu: false as boolean,
            isHoveringRecursiveSubmenu: false as boolean,
            unsetHoveringButtonTimeout: null as number | null,
            unsetHoveringSubmenuTimeout: null as number | null,
            unsetHoveringRecursiveSubmenuTimeout: null as number | null,
        }
    },
    props: {
        item: {
            type: Object as PropType<MenuItem>,
            required: true
        },
        depth: {
            type: Number as PropType<number>,
            default: 1
        }
    },
    methods: {
        getSubMenuStyle(position: Position): StyleValue {
            return {
                ...position,
                visibility: this.isReadyToShow ? 'visible' : 'hidden'
            }
        },
        handleClick(item: MenuItem, event: MouseEvent): void {
            if (this.isLeaf(item)) {
                item.onClick(event);
                this.$emit('leaf-clicked', event);
            }
        },
        isLeaf(item: MenuItem): item is LeafItem {
            return 'onClick' in item;
        },
        isTree(item: MenuItem): item is TreeItem {
            return 'subItems' in item;
        },
        handleMouseOverButton(): void {
            this.isHoveringButton = true;
            if (this.unsetHoveringButtonTimeout) {
                window.clearTimeout(this.unsetHoveringButtonTimeout)
                this.unsetHoveringButtonTimeout = null;
            }
            this.revealSubMenuNearButton();
        },
        handleMouseLeaveButton(): void {
            // Unset after a short delay, giving the user time to trigger the hover event in the submenu
            this.unsetHoveringButtonTimeout = window.setTimeout(() => {
                this.isHoveringButton = false;

                if (!this.isHoveringSubmenu && !this.isHoveringRecursiveSubmenu) {
                    this.hideSubmenu();
                }
            }, submenuHidingDelayMs)
        },
        handleMouseOverSubmenu(): void {
            this.isHoveringSubmenu = true;
            if (this.unsetHoveringSubmenuTimeout) {
                window.clearTimeout(this.unsetHoveringSubmenuTimeout)
                this.unsetHoveringSubmenuTimeout = null;
            }
            this.$emit('mouseover-submenu')
        },
        handleMouseLeaveSubmenu(): void {
            // Unset after a short delay, giving the user time to trigger the hover event in a deeper submenu
            this.unsetHoveringSubmenuTimeout = window.setTimeout(() => {
                this.isHoveringSubmenu = false;
                this.$emit('mouseleave-submenu')
            }, submenuHidingDelayMs)
        },
        handleMouseOverRecursiveSubmenu(): void {
            this.isHoveringRecursiveSubmenu = true;
            if (this.unsetHoveringRecursiveSubmenuTimeout) {
                window.clearTimeout(this.unsetHoveringRecursiveSubmenuTimeout)
                this.unsetHoveringRecursiveSubmenuTimeout = null;
            }
            this.$emit('mouseover-submenu')
        },
        handleMouseLeaveRecursiveSubmenu(): void {
            // Unset after a short delay, giving the user time to trigger the hover event in a deeper submenu
            this.unsetHoveringRecursiveSubmenuTimeout = window.setTimeout(() => {
                this.$emit('mouseleave-submenu')
                this.isHoveringRecursiveSubmenu = false;
            }, submenuHidingDelayMs)
        },
        revealSubMenuNearButton(previousAttemptsCount = 0): void {
            const buttonElement = this.$refs.button;

            // If the button element has still not been rendered, then try again on next tick (up to 5 times)
            if (!(buttonElement instanceof HTMLElement)) {
                if (previousAttemptsCount < 5) {
                    this.$nextTick(() => this.revealSubMenuNearButton(previousAttemptsCount + 1))
                }
                return;
            }

            // The first render will position the submenu in its default left-bottom position (but invisible to user).
            const buttonBoundingRectangle = buttonElement.getBoundingClientRect();
            this.subMenuPosition = {
                left: `${buttonBoundingRectangle.right}px`,
                top: `${buttonBoundingRectangle.top}px`,
                right: 'unset',
                bottom: 'unset',
            }

            const fixPositionIfClipping = (previousAttemptsCount = 0) => {
                // If the submenu element has still not been rendered, then try again on next tick (up to 5 times)
                const submenuElement = this.$refs.submenu;
                if (!(submenuElement instanceof HTMLElement)) {
                    if (previousAttemptsCount < 5) {
                        this.$nextTick(() => fixPositionIfClipping(previousAttemptsCount + 1))
                    }
                    return;
                }

                const buttonBoundingRectangle = buttonElement.getBoundingClientRect();
                const submenuBoundingRectangle = submenuElement.getBoundingClientRect();
                const clipsIntoRightEdge = buttonBoundingRectangle.right + submenuBoundingRectangle.width > document.documentElement.clientWidth;
                const clipsIntoBottomEdge = buttonBoundingRectangle.bottom + submenuBoundingRectangle.height > document.documentElement.clientHeight;

                const horizontalPosition = clipsIntoRightEdge
                    ? {right: `${document.documentElement.clientWidth - buttonBoundingRectangle.left}px`, left: 'unset'}
                    : {left: `${buttonBoundingRectangle.right}px`, right: 'unset'};

                const verticalPosition = clipsIntoBottomEdge
                    ? {
                        bottom: `${document.documentElement.clientHeight - buttonBoundingRectangle.bottom}px`,
                        top: 'unset'
                    }
                    : {top: `${buttonBoundingRectangle.top}px`, bottom: 'unset'}

                this.subMenuPosition = {
                    ...horizontalPosition,
                    ...verticalPosition,
                }
                this.isReadyToShow = true;
            };

            // After the first render, we check the submenu's bounding rectangle.
            // If it clips into the viewport, we give it an alternative position.
            this.$nextTick(fixPositionIfClipping);

        },
        hideSubmenu(): void {
            this.subMenuPosition = null;
        },
    },
})
</script>

