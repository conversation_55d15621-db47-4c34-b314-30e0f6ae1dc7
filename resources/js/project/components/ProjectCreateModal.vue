<script lang="ts">
import {defineComponent} from "vue";
import TdModal from "../../common/components/TdModal.vue";
import axios from "axios";
import StandaloneForm from "../../forms/StandaloneForm.vue";
import ProjectCreateModalTemplateItem from "./ProjectCreateModalTemplateItem.vue";
import ProjectForm, {
    buildProjectPrefillFromTemplate,
    buildProjectPrefillFromRequisition,
} from "../../forms/ProjectForm.vue";
import {PropType} from "vue/types/options";

export default defineComponent({
    name: "ProjectCreateModal",
    components: {
        ProjectCreateModalTemplateItem,
        StandaloneForm,
        ProjectForm,
        TdModal,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        initialSelectedTemplate: {
            type: Number,
            default: 0,
        },
        requisition: {
            type: Object as PropType<LoadedRequisition>,
        },
        disableTemplateCreation: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            templates: [] as Project[],
            selectedTemplate: this.initialSelectedTemplate ?? (0 as number | null),
        };
    },
    computed: {
        ProjectForm() {
            return ProjectForm;
        },
        show: {
            get(): boolean {
                return this.value;
            },
            set(value: boolean) {
                this.$emit("input", value);
            },
        },
        selectedTemplateItem(): Project | null {
            if (this.selectedTemplate === 0) {
                return null;
            }
            return this.templates.find(a => a.id === this.selectedTemplate) || null;
        },
        formPrefillData(): Partial<Project> | null {
            const templatePrefill = this.selectedTemplateItem
                ? this.buildProjectPrefillFromTemplate(this.selectedTemplateItem, this.$rlSettings)
                : {};
            const requisitionPrefill = this.requisition
                ? this.buildProjectPrefillFromRequisition(this.requisition)
                : {};

            return {
                ...templatePrefill,
                ...requisitionPrefill,
            };
        },
        selectedTemplateTitle(): string {
            if (this.selectedTemplate === 0) {
                return this.$t("New project").toString();
            }
            return this.templates.find(a => a.id === this.selectedTemplate)?.position_name || "";
        },
        formName(): string {
            if (this.selectedTemplate === 0) {
                return "ProjectFormGrouped";
            }
            return "Project.ProjectFormGroupedFromTemplate";
        },
    },
    methods: {
        buildProjectPrefillFromTemplate,
        buildProjectPrefillFromRequisition,
        async fetchTemplates() {
            const {data} = await axios.get("/projects/api/templates");
            this.templates = data;
        },
        async createProjectFromTemplate(template: Project) {
            const {data} = await axios.post("/projects/clone", {
                data: {
                    project_id: template.id,
                    clone_stage_visibility: "true",
                    clone_automatic_actions: "true",
                },
            });

            const newProjectId = data.payload.updates.id;
            this.goToUrl(`/projects/${newProjectId}`);
        },
        async createProjectFromScratch() {
            const {data} = await axios.post("/projects/create_v2");
            const newProjectId = data.id;
            this.goToUrl(`/projects/${newProjectId}`);
        },
        projectCreated(data: any) {
            if (data?.payload?.updates?.id) {
                this.goToUrl(`/projects/${data.payload.updates.id}`);
            }
        },
        goToUrl(url: string) {
            if (this.$inertia) {
                this.$inertia.visit(url);
            } else {
                window.location.href = url;
            }
        },
    },
    mounted() {
        this.fetchTemplates();
    },
    watch: {
        value(value: boolean) {
            if (!value) {
                this.selectedTemplate = null;
            }
        },
        show(value: boolean) {
            if (value) {
                window.analytics.track("Project create modal opened");
            }
        },
        initialSelectedTemplate(value: number) {
            this.selectedTemplate = value;
        },
    },
});
</script>

<template>
    <td-modal
        size="lg"
        v-model="show"
        :title="$t('Create a new project')"
        body-class="p-0 overflow-hidden h-100"
        content-class="overflow-auto h-100"
        @close="show = false"
        hide-footer
        modal-class="fixed-height-modal"
        dialog-class="modal-dialog-clamp clamp-1600"
        no-close-on-esc
        no-close-on-backdrop
    >
        <div class="d-flex h-100">
            <div
                class="py-3 px-4 border-right flex-shrink-0 bg-lighter overflow-auto"
                style="width: 25%"
            >
                <div class="d-flex flex-column gap-2 mb-3">
                    <project-create-modal-template-item
                        :template="{
                            id: 0,
                            position_name: $t('New project from scratch'),
                            stages: $rlSettings.default_stages,
                            updated_at: null,
                        }"
                        :selected="selectedTemplate === 0"
                        @select="selectedTemplate = 0"
                    />
                    <hr class="w-90 my-25" />
                    <project-create-modal-template-item
                        v-for="template in templates"
                        :key="template.id"
                        :template="template"
                        :selected="selectedTemplate === template.id"
                        @select="selectedTemplate = template.id"
                    />
                    <div
                        v-if="!disableTemplateCreation"
                        class="d-flex mt-2"
                    >
                        <button
                            class="btn btn-white btn-sm"
                            v-if="$listeners.hasOwnProperty('on-new-template')"
                            @click="$emit('on-new-template')"
                        >
                            <i class="tdi td-plus mr-2"></i>
                            {{ $t("Create a new template") }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex-grow-1 d-flex flex-column">
                <p
                    class="m-3"
                    v-if="selectedTemplate === null"
                >
                    {{ $t("Select an option from the left to start your new project.") }}
                </p>
                <template v-else>
                    <template>
                        <div class="flex-grow-1 h-100 overflow-y-scroll bg-lightest">
                            <standalone-form
                                :key="selectedTemplate"
                                form-class="h-100 d-flex flex-column justify-content-between"
                                form-buttons-class="justify-content-end py-3 px-4 border-top bg-white"
                                :form-name="formName"
                                :form-component="ProjectForm"
                                :form-props="{
                                    class: 'overflow-auto flex-grow-1',
                                    tabClasses: 'h-100 overflow-hidden d-flex flex-column',
                                    tabNavClasses: 'px-4 bg-white mb-0',
                                    tabContentClasses: 'py-3 px-4 overflow-auto h-100',
                                    selectedTemplateItem,
                                }"
                                :wrap-in-row="false"
                                :to-load="formPrefillData"
                                :label-save="$t('Create project')"
                                :label-success="$t('Project created!')"
                                @response="$emit('response', $event)"
                                @success="projectCreated"
                            ></standalone-form>
                        </div>
                    </template>
                </template>
            </div>
        </div>
    </td-modal>
</template>
