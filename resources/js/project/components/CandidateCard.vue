<template>
    <div
        ref="card"
        class="project-columns__item card card--interactive flex-row gap-1"
        :class="{'project-columns__item--email-missing': !candidate.email}"
        @contextmenu.prevent="showMenuNearMouse"
        @mouseover="isHovering = true"
        @mouseleave="isHovering = false"
    >
        <div class="d-flex flex-column flex-grow-1 project-columns__item__data">
            <div class="d-flex position-relative text-break">
                <div>
                    <span @click="openCandidate()"
                          class="clickable text-dark font-weight-bold"
                          :class="{'text-bold': isNew}"
                    >
                        <text-highlight :queries="[query]">
                            {{ getCandidateDisplayName(candidate, hidePII) }}
                        </text-highlight>
                    </span>
                    <span v-if="showDragHandle" class="candidate-drag-handle p-2">
                        <i class="fas fa-grip-horizontal"></i>
                    </span>
                    <score
                        v-if="candidate.similarity_score !== null && candidate.similarity_score !== undefined"
                        v-b-tooltip.hover="candidate.similarity_score === 0 ? $t('The candidate might not have a CV attached or the CV might be unreadable or too long.') : null"
                        :score="(candidate.similarity_score * 100).toFixed(1) + '%'"
                        :id="`candidate_card_similarity_score_${candidate.id}`"
                        class="ml-1 text-xs"
                        scale="5"
                    ></score>
                    <template v-for="scorecard in project.scorecards">
                        <scorecard-summary
                            :key="`csc-${scorecard.id}`"
                            badge-class="ml-1 text-xs"
                            v-if="showScorecardAvgScore(candidate, project, $rlSettings.user, scorecard)"
                            :candidate="candidate"
                            :scorecard="scorecard"
                            :user="$rlSettings.user"
                            @click="openCandidate('scorecardTab')"
                            :id="`candidate_card_score_${scorecard.id}_${candidate.id}`"
                            with-popover
                        ></scorecard-summary>
                    </template>
                </div>
                <b-button v-if="enableContextMenu"
                          @click="toggleMenuNearClickedElement"
                          variant="light"
                          :class="$style['context-menu-button']"
                          :style="{ opacity: isHovering ? 1 : 0}"
                          class="ml-auto"
                          size="sm"
                >
                    <img style="width: 0.8rem; transform: rotate(90deg);" src="/img/icon-more.svg" alt="Context menu">
                </b-button>
            </div>
            <div
                v-if="candidate.new_activity_summaries && candidate.new_activity_summaries.length > 0"
                class="notification-dot"
            ></div>
            <lazy-component>
                <template v-if="diverse">
                    <div v-if="candidate.summary" class="mt-1">
                        <div v-if="!candidate.summary.is_failed">
                            <p class="text-xs mb-2">
                                {{ candidate.summary.summary_json?.one_sentence_summary }}
                            </p>
                            <p
                                v-if="!candidate.summary.summary_json?.is_relevant_for_position"
                                class="text-warning text-sm mb-2"
                            >
                                {{ $t('Possibly irrelevant candidate') }}
                            </p>
                            <div class="d-flex mb-2 text-sm gap-2">
                                <a
                                    href="#"
                                    class="btn btn-xs btn-white"
                                    v-b-tooltip.bottom="$t('Regenerate summary')"
                                    @click="$emit('retry-summary', candidate)"
                                >
                                    <i class="tdi td-refresh text-xxs"></i>
                                </a>
                                <a
                                    href="#"
                                    class="btn btn-xs btn-white"
                                    :id="`diverse-info-${candidate.id}`"
                                >
                                    <i class="tdi td-info-circle text-xxs mr-1"></i>
                                    {{ $t('More info') }}
                                </a>
                            </div>
                            <b-popover
                                :target="`diverse-info-${candidate.id}`" triggers="hover"
                                :custom-class="'candidate-summary popover--wide bg-lighter box-shadow'"
                                placement="bottomright" style="max-width: 100%;"
                                boundary="window"
                            >
                                <p
                                    style="max-width: 60ch;"
                                    class="summary-wrapper text-dark-medium"
                                    v-html="candidate.summary.summary_json?.five_sentence_summary_html"
                                ></p>
                                <p
                                    class="mb-0 text-dark-medium"
                                    style="max-width: 60ch;"
                                >{{ candidate.summary.summary_json?.fit_for_position_assessment }}</p>
                            </b-popover>
                        </div>
                        <div v-else>
                            <span v-b-tooltip="candidate.summary.fail_reason" class="text-danger text-sm">
                                <template v-if="candidate.summary.fail_reason === 'no_cv_contents'">
                                    {{ $t('Failed to parse the candidate\'s CV.') }}
                                </template>
                                <template v-else>
                                    {{ $t('Summarization failed') }}
                                </template>
                            </span>
                            <button type="button"
                                    class="btn btn-xs btn-white d-inline-block"
                                    v-b-tooltip.bottom="$t('Regenerate summary')"
                                    @click="$emit('retry-summary', candidate)"
                            >
                                <i class="tdi td-refresh text-xxs text-danger"/>
                            </button>
                        </div>
                    </div>
                    <p class="text-xxs lh-1 mt-1 mb-0" v-else>
                        <template v-if="candidate.last_cv">
                            {{
                                $t("We are currently processing this candidate's profile. Please check again in a few minutes.")
                            }}
                        </template>
                        <template v-else>
                            {{ $t("Candidate can't be analyzed as they have no CV.") }}
                        </template>
                    </p>
                </template>
                <div
                    class="project-columns__item-body clickable"
                    v-if="!diverse && !loading"
                    @click="openCandidate()"
                >
                    <div class="text-danger" v-if="!candidate.email">
                        {{ $t('Missing email!') }}
                    </div>
                    <div
                        class="text-danger"
                        v-if="selected && !candidate.phone_e164 && $rlSettings.sms_enabled"
                    >
                        {{ $t('Missing or invalid phone!') }}
                    </div>
                    <div
                        class="text-warning"
                        v-if="candidate.unresolved_duplicate_links_count && $rlSettings.controls.candidates.update"
                    >
                        {{ $t('Possible duplicates found') }}
                    </div>
                    <candidate-body
                        :candidate="candidate"
                        :project="project"
                    ></candidate-body>
                </div>
                <div v-if="loading">
                    <div class="text-sm">
                        {{ $t('Reading candidate profile...') }}
                    </div>
                </div>
                <div class="project-columns__item-tasks mt-1">
                    <div v-for="t in candidate.incomplete_tasks"
                         :key="`task_${t.id}`"
                         class="d-flex gap-2">
                        <styled-checkbox
                            v-model="t.done"
                            size="sm"
                            @change="setTaskDoneState(t, $event)"
                            label-class="d-flex align-items-start"
                            checkbox-class="mt-xxs"
                        >
                            <span :class="{'text-strike': t.done}">{{ t.title }}</span>
                            <span v-if="t.assignee_id == $rlSettings.user.id" class="text-success"> ({{ $t('you') }})</span>
                            <i
                                class="tdi td-info-circle text-muted ml-1"
                                v-if="parseInt(t.project_id) !== project.id"
                                v-b-tooltip.bottom="$t('Task in project {project}', {project: t.project?.position_name})"
                            ></i>
                        </styled-checkbox>
                    </div>
                </div>
                <div class="d-flex" v-if="summaryMode === 'off' || summaryMode === 'with_summaries'">
                    <div class="text-sm d-flex align-items-center" v-if="summaryMode!=='only_summary'">

                    </div>
                </div>
            </lazy-component>
        </div>
        <lazy-component
            root-margin="10px 10px 10px 210px"
            :style="{minWidth: 'auto!important'}"
            class="d-flex flex-column gap-1 align-items-center"
            :threshold="[0,0.2,0.4,1]"
        >
            <template #placeholder>
                <div style="min-height: 110px; min-width: 1.5rem;"></div>
            </template>
            <i class="tdi td-load-spinner fa-spin text-xl" v-if="loading"></i>
            <styled-checkbox v-if="!loading" v-model="selected" size="lg" :disabled="readOnly" label-class="d-block"/>
            <div
                :id="ratingEmojiId"
                class="emoji"
                v-if="!isNil(candidate.rating_avg)"
            >{{ getRatingAsEmoji(resolveSometimesStringNumber(candidate.rating_avg)) }}
            </div>
            <b-popover v-if="project && !isNil(candidate.rating_avg)"
                       :target="ratingEmojiId"
                       triggers="hover"
                       placement="rightbottom"
                       custom-class="overflow-auto max-height-15 pr-2"
            >
                <candidate-ratings-list
                    :project="project"
                    :candidate="candidate"
                    style="width: 12rem;"
                ></candidate-ratings-list>
            </b-popover>
            <small
                class="text-xxs font-weight-medium"
                v-if="candidate.last_stage_entry_activity"
                :style="{color: daysColor}"
                v-b-tooltip.right.o5="$tc('1 day in stage | {count} days in stage', candidate.last_stage_entry_activity.days_in_stage)"
            >
                {{ candidate.last_stage_entry_activity.days_in_stage }}{{ $t('daysInStageShort') }}
            </small>
            <div class="d-flex flex-column flex-grow-1 align-items-center gap-1">
                <i
                    class="tdi td-lg td-smartphone clickable"
                    v-if="candidate.sms_count !==undefined && candidate.sms_count !== 0 && $rlSettings.sms_enabled"
                    v-b-tooltip.right="`${$tc('1 SMS sent | {count} SMS sent', candidate.sms_count)} | ${$tc('{count} received', candidate.received_sms_count)}`"
                    :class="{
                       'text-warning': candidate.sms_count && !candidate.received_sms_count,
                       'text-success': candidate.received_sms_count
                   }"
                    @click="openCandidate('smsTab')"
                ></i>
                <i
                    class="tdi td-lg td-message clickable"
                    v-if="candidate.messages_count !== undefined && $rlSettings.cc_indicator_messages"
                    v-b-tooltip.right.o5="messageIndicatorTooltip"
                    :class="messageIndicatorClass"
                    @click="openCandidate('messagesTab')"
                ></i>
                <i
                    class="tdi td-lg td-chat clickable"
                    :class="[candidate.comments_count ? 'text-warning' : 'text-muted']"
                    v-if="candidate.comments_count !== undefined && $rlSettings.cc_indicator_comments"
                    v-b-tooltip.right="$tc('1 comment | {count} comments', candidate.comments_count)"
                    @click="openCandidate('commentsTab')"
                ></i>
                <i
                    class="tdi td-lg td-calendar clickable"
                    :class="{'text-success': candidate.accepted_invites_count, 'text-warning': candidate.pending_invites_count && !candidate.accepted_invites_count}"
                    v-if="$rlSettings.cc_indicator_invites && candidate.pending_invites_count !== undefined && (candidate.pending_invites_count || candidate.accepted_invites_count)"
                    v-b-tooltip.right="`${$tc('1 pending | {count} pending', candidate.pending_invites_count)} | ${$tc('1 accepted | {count} accepted', candidate.accepted_invites_count)}`"
                    @click="openCandidate('invitesTab')"
                ></i>
                <i
                    class="tdi td-lg td-video clickable"
                    :class="{'text-success': candidate.answered_video_invites_count, 'text-warning': candidate.pending_video_invites_count && !candidate.answered_video_invites_count}"
                    v-if="$rlSettings.cc_indicator_video_invites && candidate.pending_video_invites_count !== undefined && (candidate.pending_video_invites_count || candidate.answered_video_invites_count)"
                    v-b-tooltip.right="`${$tc('1 pending | {count} pending', candidate.pending_video_invites_count)} | ${$tc('1 answered | {count} answered', candidate.answered_video_invites_count)}`"
                    @click="openCandidate('invitesTab')"
                ></i>
                <i
                    class="tdi td-lg td-check-double clickable"
                    :class="{'text-success': (candidate.submitted_references && !candidate.pending_references), 'text-warning': candidate.pending_references}"
                    v-if="$rlSettings.features.references && $rlSettings.cc_indicator_references && candidate.references_count !== undefined && (candidate.references_count || candidate.pending_references || candidate.submitted_references)"
                    v-b-tooltip.right="{customClass: 'text-left', html: true, title: `${$tc('1 referee | {count} referees', candidate.references_count)}<br>${$tc('1 pending | {count} pending', candidate.pending_references)} | ${$tc('1 submitted | {count} submitted', candidate.submitted_references)}`}"
                    @click="openCandidate('referencesTab')"
                ></i>
                <hover-file-preview
                    v-if="isFullFilePreviewVisible"
                    :url="candidate.last_cv.url"
                    wrapper-class="mt-auto"
                    :file-id="candidate.last_cv.id"
                    @preview-loaded="trackQuickView"
                >
                    <i class="tdi td-paper td-lg text-muted"></i>
                </hover-file-preview>
                <candidate-html-summary-preview v-else-if="isAnonymizedFilePreviewVisible"
                              :summary-id="candidate.summary.id"
                              @preview-loaded="trackQuickView"
                >
                    <i class="tdi td-paper td-lg text-muted"></i>
                </candidate-html-summary-preview>
            </div>
            <candidate-card-context-menu v-if="enableContextMenu"
                                         :event="menuTriggerEvent"
                                         :positioning-strategy="menuPositioningStrategy"
                                         :candidate="candidate"
                                         :project="project"
                                         :hide-pii="hidePII"
                                         @comment="recalculateCandidateCardScore(candidate, project)"
                                         @create-task="$emit('create-task', candidate)"
                                         @closed="resetMenu"
            />
        </lazy-component>
    </div>
</template>
<script lang="ts">
import {defineComponent, PropType} from 'vue';
import TextHighlight from "vue-text-highlight";
import {getRatingAsEmoji, rgbToHex} from "../../common/config";
import {createColorMap, linearScale, RGBColor} from "@colormap/core";
import {plasma} from "@colormap/presets";

import axios from "axios";
import CandidateBody from "../../common/components/CandidateBody.vue";
import {getCandidateDisplayName, makeId, resolveSometimesStringNumber, showConfetti} from "../../common/util";
import HoverFilePreview from "../../common/components/HoverFilePreview.vue";
import {BButton, BPopover, BProgress, BSpinner, BTooltip} from "bootstrap-vue";
import {recalculateCandidateCardScore, showScorecardAvgScore} from "../util";
import StyledCheckbox from "../../common/StyledCheckbox.vue";
import ScorecardSummary from "../../common/ScorecardSummary.vue";
import LazyComponent from "v-lazy-component/vue2";
import Score from "../../common/Score.vue";
import CandidateHtmlSummaryPreview from "../../common/components/CandidateHtmlSummaryPreview.vue";
import {isEmpty, isNil} from "lodash";
import CandidateCardContextMenu from "./CandidateCardContextMenu.vue";
import {PositioningStrategy} from "./ContextMenu.vue";
import CandidateRatingsList from "./CandidateRatingsList.vue";
import {getCandidateRatings} from "../../candidate/util";
import {LoadedCandidate, LoadedShowProject} from "../util";

const plasmaColorMap = createColorMap(plasma, linearScale([0, 17], [0, 1]));

export default defineComponent({
    name: 'CandidateCard',
    components: {
        CandidateCardContextMenu,
        CandidateHtmlSummaryPreview,
        Score,
        ScorecardSummary,
        TextHighlight,
        CandidateBody,
        HoverFilePreview,
        BPopover,
        BButton,
        StyledCheckbox,
        LazyComponent,
        CandidateRatingsList
    },
    emits: {
        open: (candidate: Candidate, tab: string | null, piiMode: SummaryMode | null) => true,
        'retry-summary': (candidate: Candidate) => true,
        input: (selectedCandidates: Candidate[]) => true,
        'create-task': (candidate: Candidate) => true,
    },
    props: {
        candidate: {
            type: Object as PropType<LoadedCandidate>,
            required: true
        },
        project: {
            type: Object as PropType<LoadedShowProject>,
            required: true,
        },
        query: {
            type: String as PropType<string | null>
        },
        value: {
            type: Array as PropType<Candidate[]>
        },
        showDragHandle: {
            type: Boolean as PropType<boolean>,
            default: false
        },
        readOnly: {
            type: Boolean as PropType<boolean>,
            default: false
        },
        summaryMode: {
            type: String as PropType<SummaryMode | null>,
            default: 'off',
        },
        fairEvaluations: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        loading: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        enableContextMenu: {
            type: Boolean as PropType<boolean>,
            default: false
        }
    },
    data() {
        return {
            menuTriggerEvent: null as MouseEvent | null,
            menuPositioningStrategy: null as PositioningStrategy | null,
            isHovering: false as boolean,
            ratingEmojiId: makeId()
        }
    },
    methods: {
        isEmpty,
        getCandidateRatings,
        isNil,
        resolveSometimesStringNumber,
        getRatingAsEmoji,
        getCandidateDisplayName,
        trackQuickView() {
            analytics.track('Opened quick cv preview');
        },
        setTaskDoneState(task: Task, event: Event) {
            // @ts-ignore
            if (event.target.checked) {
                window.analytics.track('Marked task as complete', {
                    view: 'candidate_card',
                });
                this.ck();
            }
            axios.put(`/tasks/${task.id}/setDone`, {
                // @ts-ignore
                done: event.target.checked,
            });
        },
        enc: encodeURIComponent,
        ck: showConfetti,
        openCandidate(tab: string | null = null) {
            this.$emit('open', this.candidate, tab, this.summaryMode);
        },
        showScorecardAvgScore,
        recalculateCandidateCardScore,
        showMenuNearMouse(event: MouseEvent): void {
            this.menuTriggerEvent = event;
            this.menuPositioningStrategy = 'NEAR_MOUSE';
        },
        toggleMenuNearClickedElement(event: MouseEvent): void {
            if (this.menuTriggerEvent && this.menuPositioningStrategy === 'NEAR_CLICKED_ELEMENT') {
                this.menuTriggerEvent = null;
                this.menuPositioningStrategy = null;
            } else {
                this.menuTriggerEvent = event;
                this.menuPositioningStrategy = 'NEAR_CLICKED_ELEMENT';
            }
        },
        resetMenu(): void {
            this.menuTriggerEvent = null;
            this.menuPositioningStrategy = null;
        },
    },
    computed: {
        selected: {
            get(): boolean {
                return this.value.includes(this.candidate);
            },
            set(v: boolean) {
                let arrCopy = [...this.value];
                if (v) {
                    arrCopy.push(this.candidate);
                    this.$emit('input', arrCopy);
                    console.log(arrCopy);
                } else {
                    const idx = this.value.indexOf(this.candidate);
                    arrCopy.splice(idx, 1);
                    this.$emit('input', arrCopy);
                    console.log(arrCopy);
                }
            }
        },
        isNew(): boolean {
            return (this.candidate.new_activity_summaries || []).filter(nas => {
                return [
                    'added_to_stage', 'added_to_stage_from_external', 'added_to_stage_from_form',
                    'added_to_stage_from_api', 'dragged_from_mailbox',
                ].includes(nas.activity_type.name ?? '')
            }).length > 0;
        },
        messageIndicatorClass(): string {
            if (this.candidate.clicked_messages_count) {
                return 'text-purple-light';
            } else if (this.candidate.opened_messages_count) {
                return 'text-success';
            } else if (this.candidate.bounced_messages_count) {
                return 'text-danger';
            } else if (this.candidate.sent_messages_count) {
                return 'text-warning';
            } else if (this.candidate.messages_count) {
                return 'text-warning';
            } else {
                return 'text-muted';
            }
        },
        messageIndicatorTooltip(): string {
            let bits = [
                this.$tc('1 message | {count} messages', this.candidate.messages_count),
            ];

            if (this.candidate.clicked_messages_count) {
                bits.push(this.$tc('Latest clicked.'));
            } else if (this.candidate.opened_messages_count) {
                bits.push(this.$tc('Latest opened.'));
            } else if (this.candidate.bounced_messages_count) {
                bits.push(this.$tc('Latest bounced.'));
            } else if (this.candidate.sent_messages_count) {
                bits.push(this.$tc('Latest sent.'));
            }

            return `${bits.join(". ")}`;
        },
        daysColor(): string | null {
            if (this.candidate.last_stage_entry_activity) {
                let days = this.candidate.last_stage_entry_activity.days_in_stage;
                if (days > 14) {
                    days = 14;
                }
                let res = plasmaColorMap(days);
                let cRes: RGBColor = [0, 0, 0];
                cRes[0] = Math.round(res[0] * 255);
                cRes[1] = Math.round(res[1] * 255);
                cRes[2] = Math.round(res[2] * 255);
                return rgbToHex(...cRes);
            }
            return null;
        },
        diverse(): boolean {
            return this.summaryMode !== 'off';
        },
        isFullFilePreviewVisible(): boolean {
            return !!this.candidate.last_cv
                && !this.hidePII
                && (
                    !this.$rlSettings.hide_files_from_limited_users || this.$rlSettings.user.role !== 'limited'
                );
        },
        isAnonymizedFilePreviewVisible(): boolean {
            return !this.isFullFilePreviewVisible
                && this.summaryMode === 'only_summary_with_anonymized_cv'
                && !!this.candidate.summary?.has_anonymized_cv;
        },
        hidePII(): boolean {
            return this.summaryMode === 'only_summary'
                || this.summaryMode === 'only_summary_with_anonymized_cv';
        },
    }
})
</script>

<style lang="scss" scoped module>
.context-menu-button {
    padding: 0 0.1rem;
    transition: opacity 0.1s ease-in-out;
    height: 24px;
}
</style>
