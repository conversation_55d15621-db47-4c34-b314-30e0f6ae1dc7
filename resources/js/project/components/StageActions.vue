<script lang="ts">
import {defineComponent, PropType} from "vue";
import {ACTION_ICONS} from "../../constants/action_types";
import ActionTrigger from "../../common/ActionTrigger.vue";
import {<PERSON><PERSON><PERSON>on, BCollapse} from "bootstrap-vue";
import Lockr from "lockr";

export default defineComponent({
    name: "StageActions",
    components: {
        ActionTrigger,
        BButton,
        BCollapse,
    },
    data() {
        return {
            open: false,
        };
    },
    created() {
        this.open = Lockr.get(`stage-actions-open-${this.stage.id}`, false);
    },
    computed: {
        ACTION_ICONS() {
            return ACTION_ICONS;
        },
    },
    props: {
        stage: {
            type: Object as PropType<Stage>,
            required: true,
        },
        project: {
            type: Object as PropType<Project>,
            required: true,
        },
    },
    watch: {
        open: {
            handler(val: boolean) {
                Lockr.set(`stage-actions-open-${this.stage.id}`, val);
            },
        },
    },
});
</script>

<template>
    <div>
        <b-button
            v-if="stage.actions.length > 1"
            @click="open = !open"
            variant="white"
            size="sm"
            class="w-100 text-left d-flex align-items-center"
        >
            <span class="mr-2">
                {{ $t("Actions") }}
            </span>
            <div class="d-flex flex-grow-1 align-items-center justify-content-end gap-2 pr-2">
                <div
                    class="text-xxs gap-1 d-flex align-items-center"
                    :class="{'text-muted': a.pending_count === 0}"
                    v-for="a in stage.actions"
                    :key="`action-${a.id}`"
                    v-b-tooltip.bottom.hover="a.name"
                >
                    <i :class="ACTION_ICONS[a.action_type]"></i>
                    <span>{{ a.pending_count }}</span>
                </div>
            </div>
            <i
                class="tdi td-chevron-down ml-auto"
                :class="{'td-rotate-180': open}"
            ></i>
        </b-button>
        <component
            :is="stage.actions.length === 1 ? 'div' : 'b-collapse'"
            :id="`actions-${stage.id}`"
            :visible="open"
        >
            <action-trigger
                class="mt-1"
                v-for="a in stage.actions"
                :action="a"
                :stage="stage"
                :key="`action-trigger-${a.id}`"
                :disabled="project.is_template"
                @move="$emit('move', $event)"
            ></action-trigger>
        </component>
    </div>
</template>
