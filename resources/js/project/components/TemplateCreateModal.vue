<script lang="ts">
import {defineComponent} from "vue";
import TdModal from "../../common/components/TdModal.vue";
import axios from "axios";
import StandaloneForm from "../../forms/StandaloneForm.vue";
import ProjectForm from "../../forms/ProjectForm.vue";

export default defineComponent({
    name: "TemplateCreateModal",
    components: {
        StandaloneForm,
        ProjectForm,
        TdModal,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            templates: [] as Project[],
            selectedTemplate: 0 as number | null,
        };
    },
    computed: {
        ProjectForm() {
            return ProjectForm;
        },
        show: {
            get(): boolean {
                return this.value;
            },
            set(value: boolean) {
                this.$emit("input", value);
            },
        },
    },
    methods: {
        async fetchTemplates() {
            const {data} = await axios.get("/projects/api/templates");
            this.templates = data;
        },
        async createProjectFromTemplate(template: Project) {
            const {data} = await axios.post("/projects/clone", {
                data: {
                    project_id: template.id,
                    clone_stage_visibility: "true",
                    clone_automatic_actions: "true",
                },
            });

            const newProjectId = data.payload.updates.id;
            window.location.href = `/projects/${newProjectId}`;
        },
        async createProjectFromScratch() {
            const {data} = await axios.post("/projects/create_v2");
            const newProjectId = data.id;
            window.location.href = `/projects/${newProjectId}`;
        },
        projectCreated(data: any) {
            if (data?.payload?.updates?.id) {
                window.location.href = `/projects/${data.payload.updates.id}`;
            }
        },
    },
    mounted() {
        this.fetchTemplates();
    },
    watch: {
        value(value: boolean) {
            if (!value) {
                this.selectedTemplate = null;
            }
        },
        show(value: boolean) {
            if (value) {
                window.analytics.track("Template create modal opened");
            }
        },
    },
});
</script>

<template>
    <td-modal
        size="lg"
        v-model="show"
        :title="$t('Create a new project template')"
        body-class="p-0 overflow-hidden h-100"
        content-class="overflow-auto h-100"
        @close="show = false"
        hide-footer
        modal-class="fixed-height-modal"
        dialog-class="modal-dialog-clamp clamp-1600"
        no-close-on-esc
        no-close-on-backdrop
    >
        <div class="d-flex h-100">
            <div class="flex-grow-1 d-flex flex-column">
                <div class="flex-grow-1 h-100 overflow-y-scroll bg-lightest">
                    <standalone-form
                        :key="selectedTemplate"
                        form-class="h-100 d-flex flex-column justify-content-between"
                        form-buttons-class="justify-content-end py-3 px-4 border-top bg-white"
                        form-name="Project.TemplateForm"
                        :form-component="ProjectForm"
                        :form-props="{
                            class: 'overflow-auto flex-grow-1',
                            tabClasses: 'h-100 overflow-hidden d-flex flex-column',
                            tabNavClasses: 'px-4 bg-white mb-0',
                            tabContentClasses: 'py-3 px-4 overflow-auto',
                        }"
                        :to-load="{
                            is_template: true,
                        }"
                        :wrap-in-row="false"
                        :label-save="$t('Create template')"
                        :label-success="$t('Template created!')"
                        @response="$emit('response', $event)"
                        @success="projectCreated"
                    ></standalone-form>
                </div>
            </div>
        </div>
    </td-modal>
</template>
