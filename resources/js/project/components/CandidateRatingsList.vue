<script lang="ts">
import Vue, {defineComponent} from "vue";
import {PropType} from "vue/types/options";
import {resolveSometimesStringNumber} from "../../common/util";
import {BSpinner, VBTooltipPlugin} from "bootstrap-vue";
import {getRatingAsEmoji} from "../../common/config";
import {formatPercentage, getCandidateRatings, RatingComment} from "../../candidate/util";
import {isEmpty} from "lodash";
import axios, {AxiosResponse} from "axios";
import {formatDateTime} from "../../common/datetime";
import TdRelativeTime from "../../common/library/TdRelativeTime.vue";

Vue.use(VBTooltipPlugin);

export default defineComponent({
    name: "CandidateRatingsList",
    components: {
        TdRelativeTime,
        BSpinner,
    },
    props: {
        candidate: {
            type: Object as PropType<Candidate>,
            required: true,
        },
        project: {
            type: Object as PropType<Project>,
            required: true,
        },
    },
    methods: {
        isEmpty,
        resolveSometimesStringNumber,
        getRatingAsEmoji,
        formatDateTime,
        getTooltipOptions(comment: Comment): Record<string, unknown> {
            return {
                title: comment.content,
                boundary: "viewport",
                placement: "auto",
                html: true,
                customClass: "max-height-20 overflow-auto",
                trigger: "hover",
                variant: "lighter",
            };
        },
    },
    mounted(): void {
        if (this.candidate.comments) {
            return;
        }

        const params = {no_pagination: true};
        axios.get(`/candidates/${this.candidate.id}/comments`, {params}).then((response: AxiosResponse<Comment[]>) => {
            this.$set(this.candidate, "comments", response.data);
        });
    },
    computed: {
        averageRatingFormatted(): string | null {
            return formatPercentage(this.candidate.rating_avg);
        },
        ratings(): RatingComment[] | null {
            return getCandidateRatings(this.candidate, this.project);
        },
    },
});
</script>

<template>
    <div>
        <strong
            v-if="averageRatingFormatted"
            class="d-block mb-1"
        >
            {{ `${$t("Average rating")}: ${averageRatingFormatted}` }}
        </strong>
        <template v-if="candidate.comments">
            <p
                v-if="isEmpty(ratings)"
                class="text-muted"
            >
                {{ $t("No ratings") }}
            </p>
            <template v-else>
                <div
                    v-for="comment in ratings"
                    :key="comment.id"
                    class="d-flex align-items-start justify-content-between mb-2"
                >
                    <div
                        class="d-flex align-items-center d-inline-block"
                        v-b-tooltip="getTooltipOptions(comment)"
                    >
                        <span
                            class="big-emoji position-relative"
                            style="top: 1px"
                        >
                            {{ getRatingAsEmoji(resolveSometimesStringNumber(comment.rating)) }}
                        </span>
                        <small class="ml-2 mr-4 text-muted cursor-default">{{ `${comment.rating}%` }}</small>
                    </div>
                    <div class="d-flex flex-column align-items-end mt-1 d-inline-block">
                        <span class="d-block">{{ comment.user.name }}</span>
                        <small
                            v-b-tooltip.bottom.hover="formatDateTime(comment.updated_at, 'short-time')"
                            class="text-xxs d-block text-muted ml-auto"
                        >
                            <td-relative-time :timestamp="comment.updated_at" />
                        </small>
                    </div>
                </div>
            </template>
        </template>
        <div
            v-else
            class="d-flex my-3 justify-content-center align-items-center"
        >
            <b-spinner variant="secondary" />
        </div>
    </div>
</template>
