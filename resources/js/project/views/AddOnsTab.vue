<script lang="ts">
import {defineComponent} from "vue";
import AddOnListItem from "./AddOnListItem.vue";

export default defineComponent({
    name: "AddOnsTab",
    components: {AddOnListItem},
    mounted() {
        window.analytics.track("Add-ons tab viewed");
    },
    computed: {
        items() {
            return [
                {
                    title: this.$t("Automations"),
                    description: this.$t(
                        "Automate repetitive tasks and communications to save time and improve the candidate experience."
                    ),
                    status: "enabled",
                    documentation: "https://support.teamdash.com/en/articles/8929701-automatic-stage-actions",
                    category: "recruitment",
                },
                {
                    title: this.$t("Scorecards"),
                    description: this.$t(
                        "Collaborate with hiring managers and assess candidates in a fair and equitable way."
                    ),
                    status: this.$rlSettings.features.scorecards ? "enabled" : "disabled",
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/articles/7850420-scorecards",
                },
                {
                    title: this.$t("Reference Checks"),
                    description: this.$t(
                        "Gather references from candidates and automate outreach to previous employers."
                    ),
                    status: this.$rlSettings.features.references ? "enabled" : "disabled",
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/articles/9166853-automated-reference-checks",
                },
                {
                    title: this.$t("Asynchronous Video Interviews"),
                    description: this.$t(
                        "Meet your candidates without scheduling a call. Candidates answer your questions on their own time."
                    ),
                    status: this.$rlSettings.features.video_interviews ? "enabled" : "disabled",
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/articles/6193251-asynchronous-video-interviews",
                },
                {
                    title: this.$t("AI Assistance"),
                    description: this.$t("Candidate CV summaries, similarity search, writing assistance, and more."),
                    status: this.$rlSettings.features.enable_ai ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/collections/10751537-ai-powered-tools",
                },
                {
                    title: this.$t("Locations"),
                    description: this.$t(
                        "Candidates hate long commutes. If you're hiring for on-site positions, you can make better hiring decisions with location data."
                    ),
                    status: "enabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/articles/9245858-distance-search",
                },
                {
                    title: this.$t("Recruitment Performance"),
                    description: this.$t(
                        "Get insights into your recruitment process. See how long it takes to fill a position, where candidates are dropping off, and more."
                    ),
                    status: "enabled",
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/articles/8191703-recruitment-performance",
                },
                {
                    title: this.$t("Requisitions"),
                    description: this.$t(
                        "Let your managers request new hires and let management approve or reject the requests. All steps are customisable."
                    ),
                    status: this.$rlSettings.features.requisitions ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "recruitment",
                    documentation: "https://support.teamdash.com/en/articles/8275471-job-requisitions",
                },
                {
                    title: this.$t("Team-Based Access Controls"),
                    description: this.$t(
                        "Control who can see and edit what. Create teams and assign them to projects, job ads, and candidates."
                    ),
                    status: this.$rlSettings.features.teams ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "recruitment",
                },
                {
                    title: this.$t("Career Pages"),
                    description: this.$t(
                        "Create on-brand customisable career sites. Recruiters can publish all jobs with a single click."
                    ),
                    status: this.$rlSettings.features.career_pages ? "enabled" : "disabled",
                    category: "marketing",
                },
                {
                    title: this.$t("Custom Domain"),
                    description: this.$t(
                        "Host your job ads, career sites, and scheduler invites on your own domain. Your branding, our tech."
                    ),
                    cta: this.$t("Get a Quote"),
                    status:
                        window.location.host.includes(".teamdash.") || window.location.host.includes(".recruitlab.")
                            ? "disabled"
                            : "enabled",
                    category: "marketing",
                },
                {
                    title: this.$t("Two-Factor Authentication"),
                    description: this.$t(
                        "Keep your accounts secure with two-factor authentication. Requires a mobile phone number."
                    ),
                    status: "enabled",
                    category: "security",
                },
                {
                    title: this.$t("Single Sign-On"),
                    description: this.$t(
                        "Use your company's identity provider to log in to Teamdash. Requires a company email address."
                    ),
                    status: this.$rlSettings.features.sso ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "security",
                },
                {
                    title: this.$t("Private job ads"),
                    description: this.$t("Make your job ads accessible only from your company's internal network."),
                    status: this.$rlSettings.features.job_ads_ip_limit ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "security",
                },
                {
                    title: this.$t("Audit Logging"),
                    description: this.$t(
                        "Records all candidate data accesses and modifications. The logs are retained according to your internal policies."
                    ),
                    status: this.$rlSettings.features.audit_log ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "security",
                },
                {
                    title: this.$t("Automated User Provisioning (SCIM)"),
                    description: this.$t("Keep your Teamdash user list in sync with your company's identity provider."),
                    status: this.$rlSettings.features.scim ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "security",
                    documentation:
                        "https://intercom.help/teamdash/en/articles/9244756-automatic-user-provisioning-with-scim-and-ms-entra",
                },
                {
                    title: this.$t("Self-Managed Backups"),
                    description: this.$t(
                        "Have your IT-team configure daily backups of all your candidate data. Requires an NDA."
                    ),
                    status: this.$rlSettings.features.dump_database ? "enabled" : "disabled",
                    cta: this.$t("Schedule a Consultation"),
                    category: "security",
                },
            ];
        },
    },
});
</script>

<template>
    <div class="pt-4 pb-4">
        <h1 class="mb-3">{{ $t("Recruitment") }}</h1>
        <div class="addons-grid">
            <add-on-list-item
                :key="item.title"
                v-for="item in items.filter(i => i.category === 'recruitment')"
                :item="item"
            />
        </div>
        <h1 class="mt-5 mb-3">{{ $t("Marketing") }}</h1>
        <div class="addons-grid">
            <add-on-list-item
                :key="item.title"
                v-for="item in items.filter(i => i.category === 'marketing')"
                :item="item"
            />
        </div>
        <h1 class="mt-5 mb-3">{{ $t("Security & compliance") }}</h1>
        <div class="addons-grid">
            <add-on-list-item
                :key="item.title"
                v-for="item in items.filter(i => i.category === 'security')"
                :item="item"
            />
        </div>
    </div>
</template>

<style scoped>
.addons-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}
</style>
