<template>
    <div>
        <div class="row" v-if="projects.total === 0">
            <div class="col mt-6 pt-6">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="tdi td-prime hazey hazey-lg"></i>
                    <div class="ml-6">
                        <h1 class="font-weight-normal text-dark-light mb-3">{{ $t('No projects found') }}</h1>
                        <p v-if="hasProjectsAtAll">
                            {{ $t('Feel free to tweak filters and double-check you have access to the project.') }}</p>
                        <div class="d-flex align-items-center gap-2" v-if="$rlSettings.user.role !== 'limited'">
                            <button type="button" class="btn btn-sm btn-primary" @click="$emit('create-project')">
                                <i class="tdi td-plus mr-2"></i>
                                {{ $t('Create project') }}
                            </button>
                            <a href="/landings/v2/create" class="btn btn-sm btn-outline-primary">
                                <i class="tdi td-plus mr-2"></i>
                                {{ $t('Create job ad') }}
                            </a>
                        </div>
                        <div class="d-flex align-items-center gap-2" v-else>
                            <a
                                class="btn btn-sm btn-primary"
                                v-can.requisitions.create
                                @click="$emit('create-requisition')"
                            >
                                <i class="tdi td-plus mr-2"></i>
                                {{ $t('Create requisition') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <b-overlay :show="false" rounded="sm" class="row" v-if="projects.data.length > 0">
            <project-card
                class="col-12 col-sm-4 col-lg-3 p-3"
                v-for="p in projects.data"
                @pinned="$emit('change', 1)"
                @finished="$emit('change', 1)"
                @clone="cloningProjectOptions = { project: $event, asTemplate: false }"
                @clone-as-template="cloningProjectOptions = { project: $event, asTemplate: true }"
                :stage-summaries="stageSummaries"
                :key="`project_${p.id}`"
                :project="p"
            ></project-card>
        </b-overlay>
        <div class="row" v-if="projects.data.length > 0">
            <div class="col-12 text-center">
                <div class="mt-1 d-flex justify-content-center">
                    <laravel-vue-pagination
                        :data="projects"
                        :limit="15"
                        @pagination-change-page="$emit('change', $event)"
                    ></laravel-vue-pagination>
                </div>
            </div>
        </div>
        <form-modal
            form-name="ProjectCloneForm"
            v-model="showProjectCloneForm"
            :title="projectCloneFormTitle"
            :to-load="projectCloneFormPrefill"
            :params="projectCloneFormParams"
            :label-save="projectCloneFormSaveLabel"
            :label-success="projectCloneFormSuccessLabel"
            @response="redirectTo(`/projects/${$event.payload.updates.id}`)"
            @loaded="projectCloneFormLoaded"
            ref="projectCloneFormModal"
        />
    </div>
</template>

<script lang="ts">
import {defineComponent} from 'vue';
import LaravelVuePagination from 'laravel-vue-pagination';

import ProjectCard from "../components/ProjectCard.vue";
import {BOverlay} from "bootstrap-vue";
import {
    getProjectCloneFormParams,
    getProjectCloneFormPrefill,
    getProjectCloneFormSaveLabel,
    getProjectCloneFormSuccessLabel,
    getProjectCloneFormTitle,
    handleProjectCloneFormLoaded,
    isProjectCloneFormVisible
} from "../util";
import FormModal from "../../forms/FormModal.vue";
import {PropType} from "vue/types/options";
import {ProjectWithRequisition} from "../index";

export default defineComponent({
    components: {
        BOverlay,
        FormModal,
        ProjectCard,
        LaravelVuePagination,
    },
    emits: {
        change: (page: number) => true
    },
    props: {
        projects: {
            type: Object as PropType<PaginatedResults<ProjectWithRequisition>>,
            required: true,
        },
        stageSummaries: {
            type: Array as PropType<StageSummary[]>,
            required: true,
        },
    },
    data() {
        return {
            cloningProjectOptions: null as { project: Project, asTemplate: boolean } | null,
            hasProjectsAtAll: window.has_projects_at_all as boolean,
        };
    },
    methods: {
        projectCloneFormLoaded(): void {
            handleProjectCloneFormLoaded(this.$refs.projectCloneFormModal as InstanceType<typeof FormModal>)
        },
        redirectTo(path: string) {
            window.location.href = path;
        },
    },
    computed: {
        showProjectCloneForm: {
            get(): boolean {
                return isProjectCloneFormVisible(this.cloningProjectOptions);
            },
            set(v: boolean) {
                if (!v) {
                    this.cloningProjectOptions = null;
                }
            },
        },
        projectCloneFormTitle(): string | null {
            return getProjectCloneFormTitle(this.cloningProjectOptions);
        },
        projectCloneFormSaveLabel(): string | null {
            return getProjectCloneFormSaveLabel(this.cloningProjectOptions);
        },
        projectCloneFormSuccessLabel(): string | null {
            return getProjectCloneFormSuccessLabel(this.cloningProjectOptions);
        },
        projectCloneFormPrefill(): Record<string, unknown> {
            return getProjectCloneFormPrefill(this.cloningProjectOptions);
        },
        projectCloneFormParams(): Record<string, unknown> {
            return getProjectCloneFormParams(this.cloningProjectOptions);
        }
    },
})


</script>
