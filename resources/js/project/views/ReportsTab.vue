<template>
    <div class="pt-4">
        <div class="d-flex justify-content-between mb-4">
            <duration-picker v-model="filters.duration"></duration-picker>
            <groupby-picker
                v-if="filters.duration"
                v-model="filters.duration.group_by"
            ></groupby-picker>
        </div>
        <div class="d-flex flex-column gap-4 mb-5">
            <uncategorized-stages
                v-if="!allStagesCategorized"
                :message="
                    $t('To show you project funnel statistics, all project stages need to be categorized.').toString()
                "
                @ok="allStagesCategorized = true"
            ></uncategorized-stages>
            <template v-else>
                <projects-funnel
                    :filters="filters"
                    class="card-translucent"
                ></projects-funnel>
                <projects-report
                    :filters="filters"
                    name="PipelineHealthReport"
                    :description="
                        $t('Shows how candidates are divided across stage categories at specific moments in time')
                    "
                    :title="$t('Pipeline overview')"
                ></projects-report>
            </template>
            <projects-report
                :filters="filters"
                name="TimeToHireReport"
                :title="$t('Finished projects + time to fill')"
            ></projects-report>
            <projects-report
                :filters="filters"
                name="SubmissionsReport"
                :title="$t('Candidate submission sources')"
            ></projects-report>
            <projects-report
                :filters="{
                    ...filters,
                    report_category: reachReportFilters.category,
                    unique: reachReportFilters.unique,
                }"
                name="ReachReport"
                :title="$t('Candidate reach')"
            >
                <template #title>
                    <i18n
                        path="Candidates who reached {stage} stage"
                        class="d-flex align-items-baseline gap-2"
                    >
                        <template #stage>
                            <td-single-select
                                size="md"
                                v-model="reachReportFilters.category"
                                :options="[
                                    ...Object.entries(STAGE_CATEGORIES).map(([value, label]) => ({value, label})),
                                ]"
                            ></td-single-select>
                        </template>
                    </i18n>
                </template>
                <template #filters>
                    <div class="d-flex align-items-center gap-2">
                        <styled-checkbox
                            no-wrap
                            v-model="reachReportFilters.unique"
                        >
                            {{ $t("Unique candidates") }}
                        </styled-checkbox>
                    </div>
                </template>
            </projects-report>
            <projects-report
                :filters="filters"
                name="HiresReport"
                :title="$t('Where do hires come from?')"
            ></projects-report>
            <projects-report
                :filters="filters"
                :reports="[
                    {report: 'DropoutReport', button: $t('By time')},
                    {report: 'DropoutReportByStage', button: $t('By stage')},
                ]"
                :title="$t('Why do candidates drop out?')"
            ></projects-report>
            <projects-report
                enabled
                :filters="filters"
                :reports="[
                    {report: 'CnpsReport', button: $t('Total')},
                    {report: 'CnpsGroupedReport', button: $t('By Project Manager')},
                    {report: 'CnpsTable', button: $t('All responses'), el: CNPSTable},
                ]"
                name="CnpsGroupedReport"
                title="cNPS"
            >
                <template v-slot:upgrade>
                    <b-alert
                        variant="warning"
                        show
                        class="text-sm mb-0"
                    >
                        {{ $t("Please contact your account manager to get started with cNPS reporting.") }}
                    </b-alert>
                </template>
            </projects-report>
            <projects-report
                :filters="filters"
                name="MessagesReport"
                :title="$t('Email message conversions')"
            ></projects-report>
            <projects-report
                :filters="{
                    ...filters,
                    user_role: activityReportFilters.role,
                }"
                name="ActivityReport"
                :chartAvailable="false"
                :use-logged-in-user-filters="false"
                :title="$t('Activity report')"
            >
                <template #filters>
                    <b-dropdown
                        variant="white"
                        size="sm"
                        no-caret
                    >
                        <template v-slot:button-content>
                            <i class="tdi td-user mr-2"></i>
                            {{ USER_ROLES[activityReportFilters.role] || $t("All") }}
                            <small class="ml-2">
                                <i class="fas fa-chevron-down"></i>
                            </small>
                        </template>
                        <b-dropdown-item
                            link-class="d-flex"
                            @click="activityReportFilters.role = null"
                        >
                            {{ $t("All") }}
                            <i
                                class="tdi td-check ml-auto"
                                v-if="activityReportFilters.role === null"
                            ></i>
                        </b-dropdown-item>
                        <b-dropdown-item
                            v-for="(role, key) in USER_ROLES"
                            link-class="d-flex"
                            @click="activityReportFilters.role = key"
                        >
                            {{ role }}
                            <i
                                class="tdi td-check ml-auto"
                                v-if="activityReportFilters.role === key"
                            ></i>
                        </b-dropdown-item>
                    </b-dropdown>
                </template>
            </projects-report>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from "vue";
import {PropType} from "vue/types/options";

import ProjectsReport from "../components/statistics/ProjectsReport.vue";
import DurationPicker from "../../common/DurationPicker.vue";
import GroupbyPicker from "../../common/GroupbyPicker.vue";
import CNPSTable from "../components/statistics/CNPSTable.vue";
import {BAlert, BDropdown, BDropdownItem} from "bootstrap-vue";
import ProjectsFunnel from "../components/statistics/ProjectsFunnel.vue";
import UncategorizedStages from "../components/statistics/UncategorizedStages.vue";
import {USER_ROLES} from "../../constants/user_roles";
import {CATEGORY_INTERVIEWS, STAGE_CATEGORIES} from "../../common/stageCategories";
import StyledCheckbox from "../../common/StyledCheckbox.vue";
import TdSingleSelect from "../../common/library/TdSingleSelect.vue";

export default Vue.extend({
    computed: {
        USER_ROLES() {
            return USER_ROLES;
        },
        CNPSTable() {
            return CNPSTable;
        },
        STAGE_CATEGORIES() {
            return STAGE_CATEGORIES;
        },
        CATEGORY_INTERVIEWS() {
            return CATEGORY_INTERVIEWS;
        },
    },
    components: {
        TdSingleSelect,
        StyledCheckbox,
        BDropdownItem,
        BDropdown,
        UncategorizedStages,
        ProjectsFunnel,
        DurationPicker,
        GroupbyPicker,
        ProjectsReport,
        CNPSTable,
        BAlert,
    },
    props: {
        filters: {
            type: Object as PropType<ProjectFilters>,
            required: true,
        },
    },
    data() {
        return {
            observer: null as IntersectionObserver | null,
            allStagesCategorized: false,
            activityReportFilters: {
                role: null,
            },
            reachReportFilters: {
                category: CATEGORY_INTERVIEWS,
                unique: false,
            },
        };
    },
    mounted() {
        this.observer = new IntersectionObserver(
            ([e]) => e.target.toggleAttribute("data-stuck", e.intersectionRatio < 1),
            {threshold: [1]}
        );
        const topSection = document.querySelector(".top-section");
        if (topSection) {
            topSection.classList.add("top-section--sticky");
            this.observer.observe(topSection);
        }
        window.analytics.track("Opened recruitment performance");
    },
    beforeDestroy() {
        const topSection = document.querySelector(".top-section");
        if (this.observer && topSection) {
            this.observer.unobserve(topSection);
            topSection.classList.remove("top-section--sticky");
            topSection.removeAttribute("data-stuck");
        }
    },
});
</script>
