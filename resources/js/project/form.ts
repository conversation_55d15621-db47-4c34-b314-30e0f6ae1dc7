import Vue from 'vue';
import Laraform from '@laraform/laraform/src';
import axios from 'axios';
import BaseForm from "../forms/BaseForm.vue";
import {ToastPlugin} from "bootstrap-vue";
import {initSentry} from "../common/config";
import {setUpLaraform} from "../common/LF_config";
import {enforceProjectManagerUsersFormItems} from "../project/util";
import i18n from "../common/i18n";
import ProjectForm from "../forms/ProjectForm.vue";
import JobProfileForm from "../forms/JobProfileForm.vue";

setUpLaraform(Laraform);

Vue.use(Laraform);
Vue.use(ToastPlugin);

declare global {
    interface Window {
        form_app: Vue,
        model: Model,
    }
}

initSentry();
window.form_app = new Vue({
    el: '#content',
    i18n,
    mounted() {
        const vm = this;
        console.log('form app mounted');
        this.$nextTick(() => {
            console.log('ok');
            const form: any = this.$refs.bform;
            const originalRemoveFn = form.el$('stages').remove;
            form.el$('stages').remove = function (index: number) {
                const id = this.data.stages[index].id;
                if(!id){
                    return originalRemoveFn(index);
                }
                const candidatesCount = window.model.stages.find((s: Stage) => s.id === id).all_candidates_count;
                if (candidatesCount > 0) {
                    this.$bvToast.toast(this.$t('There are candidates, actions or statistics associated with this stage.').toString(), {
                        title: this.$t('Could not remove stage').toString(),
                        variant: 'danger',
                        solid: true
                    });
                    return;
                }
                return originalRemoveFn(index);
            };
            form.el$('status').on('change', (...args:any) => {
                // @ts-ignore
                if(form.el$('status').value === 3 || form.el$('status').value === 4){
                    form.el$('end_date').value = (new Date).toISOString().substr(0,10);
                }
            });

            enforceProjectManagerUsersFormItems(form);
        })
    },
    methods: {
    },
    components: {
        BaseForm,
        ProjectForm,
        JobProfileForm,
    }
});
