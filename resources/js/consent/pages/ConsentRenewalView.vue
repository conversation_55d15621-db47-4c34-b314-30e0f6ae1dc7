<script lang="ts">
import {defineComponent} from "vue";
import <PERSON><PERSON>icker from "../../common/LangPicker.vue";
import {Head} from "@inertiajs/vue2";
import {TranslationsLoadedEvent} from "../../common/i18n";

export default defineComponent({
    name: "ConsentRenewalView",
    components: {Head, LangPicker},
    props: {
        renewal: {
            type: Object as () => ConsentRenewal,
            required: true,
        },
        renewal_data: {
            type: Object as () => {
                org_name: string;
                recruiter_email: string;
                privacy_policy_url: string;
                recruiter_name: string;
                date_until: string;
                fillable: boolean;
            },
            required: true,
        },
        consent_subtypes: {
            type: Array as () => ConsentSubtype[],
            required: true,
        },
        consent_subtype_general: {
            type: Object as () => ConsentSubtype,
            required: true,
        },
    },
    data() {
        return {
            consent: "i_consent" as "i_consent" | "i_dont_consent",
            generalConsent: true as boolean,
            selectedSubtypes: [] as number[],
            selectedLanguage: null as string | null,
        };
    },
    created() {
        window.addEventListener("translations-loaded", (event: Event) => {
            const customEvent = event as TranslationsLoadedEvent;
            this.selectedLanguage = customEvent?.detail?.lang ?? null;
        });
    },
    computed: {
        mustSelectSubtypes(): boolean {
            return this.consent === "i_consent" && this.selectedSubtypes.length === 0 && !this.generalConsent;
        },
    },
    methods: {
        submit() {
            this.$inertia.post(this.route("consent.update", {token: this.renewal.token}), {
                consent: this.consent,
                consent_subtype_ids: this.selectedSubtypes,
                generalConsent: this.generalConsent,
            });
        },
        getSubtypeRenewalText(subtype: ConsentSubtype) {
            if (this.selectedLanguage === "en") {
                return subtype.renewal_text;
            }
            return (
                subtype.renewal_text_translations?.find(t => t.language === this.selectedLanguage)?.renewal_text ||
                subtype.renewal_text
            );
        },
    },
    watch: {
        allSelected(value) {
            if (value) {
                this.selectedSubtypes = this.consent_subtypes.map(subtype => subtype.id!);
            }
        },
        generalConsent(value) {
            if (value) {
                this.selectedSubtypes = [];
            }
        },
        selectedSubtypes(value) {
            if (value.length !== 0) {
                this.generalConsent = false;
            }
        },
    },
});
</script>

<template>
    <div class="mt-5">
        <Head :title="$t('Consent renewal')" />
        <div class="row">
            <div class="col-sm-6 offset-sm-3">
                <div class="mb-2 d-flex">
                    <lang-picker class="ml-auto mr-0"></lang-picker>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6 offset-sm-3">
                <div
                    class="card"
                    v-if="!renewal_data.answered && renewal_data.expired"
                >
                    <div class="card-body">
                        <p class="mb-0">{{ $t("This consent request has expired.") }}</p>
                    </div>
                </div>
                <div
                    class="card"
                    v-else-if="renewal_data.fillable"
                >
                    <div class="card-body text-sm">
                        <p>{{ $t("intro", {name: renewal_data.recruiter_name, org: renewal_data.org_name}) }}</p>
                        <p class="text-medium">{{ $t("data_use") }}</p>
                        <p v-html="$t('policy_review', {url: renewal_data.privacy_policy_url})"></p>
                        <form @submit.prevent="submit">
                            <div>
                                <div class="d-flex align-items-start gap-25 mb-2">
                                    <input
                                        type="radio"
                                        id="opt1"
                                        name="consent"
                                        checked
                                        value="i_consent"
                                        v-model="consent"
                                        class="mt-1"
                                    />
                                    <label for="opt1">
                                        {{
                                            $t("i_consent", {
                                                org: renewal_data.org_name,
                                                date: renewal_data.date_until,
                                            })
                                        }}
                                    </label>
                                </div>
                                <div v-if="consent === 'i_consent' && consent_subtypes.length > 0">
                                    <div class="text-small ml-4">
                                        <div class="d-flex align-items-start gap-2">
                                            <input
                                                type="radio"
                                                id="general_consent"
                                                name="general_consent"
                                                checked
                                                :value="true"
                                                v-model="generalConsent"
                                                class="mt-1"
                                            />
                                            <label for="general_consent">
                                                {{ getSubtypeRenewalText(consent_subtype_general) }}
                                            </label>
                                        </div>
                                        <div class="d-flex align-items-start gap-2">
                                            <input
                                                type="radio"
                                                id="specific_consent"
                                                name="specific_consent"
                                                checked
                                                :value="false"
                                                v-model="generalConsent"
                                                class="mt-1"
                                            />
                                            <label for="specific_consent">
                                                {{ $t("I want to limit my consent") }}
                                            </label>
                                        </div>
                                        <ul class="list-unstyled ml-4">
                                            <li
                                                v-for="subtype in consent_subtypes"
                                                class="d-flex align-items-start gap-2"
                                            >
                                                <input
                                                    type="checkbox"
                                                    :id="subtype.id"
                                                    name="consent_subtype_ids"
                                                    :value="subtype.id"
                                                    v-model="selectedSubtypes"
                                                    class="mt-1"
                                                />
                                                <label :for="subtype.id">
                                                    {{ getSubtypeRenewalText(subtype) }}
                                                </label>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-start gap-25">
                                <input
                                    type="radio"
                                    id="opt2"
                                    name="consent"
                                    value="i_dont_consent"
                                    v-model="consent"
                                    class="mt-1"
                                />
                                <label for="opt2">
                                    {{ $t("i_dont_consent") }}
                                </label>
                            </div>
                            <div class="text-sm mt-4">
                                <div
                                    class="text-danger mb-3"
                                    v-if="mustSelectSubtypes"
                                >
                                    <i class="tdi td-info-circle td-lg"></i>
                                    {{ $t("Please select at least one consent type or give a general consent.") }}
                                </div>
                                <div>
                                    <button
                                        class="btn btn-primary"
                                        :disabled="mustSelectSubtypes"
                                    >
                                        {{ $t("submit") }}
                                    </button>
                                </div>
                            </div>
                        </form>
                        <p class="text-small text-black-50 mt-4">
                            <small>
                                {{
                                    $t("You can revoke this consent any time by contacting {org} at {email}.", {
                                        org: renewal_data.org_name,
                                        email: renewal_data.recruiter_email,
                                    })
                                }}
                            </small>
                        </p>
                    </div>
                </div>
                <div
                    class="alert alert-success"
                    v-else
                >
                    {{ $t("Thank you! We have saved your preferences!") }}
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
