<script lang="ts">
import Vue, {PropType} from "vue";
import {DateTime} from "luxon";
import {i18n} from "../common/i18n";
import TdDateRange from "../common/library/TdDateRange.vue";

export default Vue.extend({
    components: {TdDateRange},
    i18n,
    name: "InterviewAlert",
    props: {
        eventSet: {
            type: Object as PropType<EventSet>,
            required: true,
        },
    },
    computed: {
        start() {
            return this.eventSet.available_time_start && this.eventSet.available_time_zone
                ? DateTime.fromISO(this.eventSet.available_time_start, {zone: this.eventSet.available_time_zone})
                      .toLocal()
                      .toFormat("HH:mm")
                : "";
        },
        end() {
            return this.eventSet.available_time_end && this.eventSet.available_time_zone
                ? DateTime.fromISO(this.eventSet.available_time_end, {zone: this.eventSet.available_time_zone})
                      .toLocal()
                      .toFormat("HH:mm")
                : "";
        },
    },
});
</script>

<template>
    <div class="alert alert-light text-dark-medium text-sm mb-0 d-flex align-items-baseline">
        <i class="tdi td-info-circle mr-2"></i>
        <div class="d-flex flex-column">
            <div class="mb-1">
                <strong class="font-weight-medium">{{ $t("Available period") }}: </strong>
                <td-date-range
                    :start="eventSet.available_period_start"
                    :end="eventSet.available_period_end"
                ></td-date-range>
                {{ $t("between {start} and {end}", {start, end}) }}
            </div>
            <div>
                <strong class="font-weight-medium">{{ $t("Interview length") }}: </strong
                >{{ eventSet.interview_length }} {{ $t("minutes") }}
            </div>
        </div>
    </div>
</template>

<style scoped></style>
