<template>
    <component is="BaseBlockLayout" v-on="$listeners" :editable="editable" :landing="landing" :spec="spec"
               :class="[
            `page-block__content--bg-${spec.bgVariation}`,
            `page-block__content--img-${spec.imagePlacement}`
            ]"
    >
        <template slot="left-settings">
            <block-header-dropdown :spec="spec">
                <template slot="content">
                    <div>
                        <div class="d-flex flex-column justify-content-between mb-2 mx-0 bg-gray-light rounded-lg p-2">
                            <label>Background</label>
                            <div class="d-flex mt-2 col-gap align-items-baseline">
                                <label :for="`${spec._id}_lighter`">
                                    <input type="radio"
                                           :id="`${spec._id}_lighter`"
                                           v-model="spec.bgVariation" value="lighter">
                                    Lighter
                                </label>
                                <label :for="`${spec._id}_darker`">
                                    <input type="radio"
                                           :id="`${spec._id}_darker`"
                                           v-model="spec.bgVariation" value="darker">
                                    Darker
                                </label>
                            </div>
                        </div>
                        <div class="d-flex flex-column justify-content-between mb-2 mx-0 bg-gray-light rounded-lg p-2">
                            <label>Image position</label>
                            <div class="d-flex mt-2 col-gap align-items-baseline">
                                <label :for="`${spec._id}_left`">
                                    <input type="radio"
                                           :id="`${spec._id}_left`"
                                           v-model="spec.imagePlacement" value="left">
                                    Left
                                </label>
                                <label :for="`${spec._id}_right`">
                                    <input type="radio" v-model="spec.imagePlacement" value="right"
                                           :id="`${spec._id}_right`"
                                    >
                                    Right
                                </label>
                            </div>
                        </div>
                    </div>
                </template>
            </block-header-dropdown>
        </template>
        <template slot="content">
            <div class="page-block__content">
                <div class="page-block__left position-relative">
                    <img
                        class="img-fluid"
                        v-if="spec.imageUrl"
                        :src="getResizedImageUrl(spec.imageUrl, 800)" alt="Job ad image">
                    <pic-browser
                        class="position-absolute"
                        style="right: 24px; top: 8px;"
                        v-model="spec.imageUrl"
                        btn-class="btn-white-default btn--round"
                        v-if="editable"
                    >
                        <template v-slot:button-content>
                            <i class="fas fa-image text-gray"></i>
                        </template>
                    </pic-browser>
                </div>
                <div class="page-block__right">
                    <editable
                        :editable="editable"
                        v-model="spec.content"
                        with-bullet-list
                        :bullet-color="page.theme.brandColor"
                    ></editable>
                </div>
            </div>
        </template>
    </component>
</template>
<script lang="ts">
import Vue from 'vue';
import Editable3 from "../components/Editable3.vue";
import {PropType} from "vue/types/options";
import {LandingHalfImageTextBlock, LandingPage} from "../LandingTypes";
import BaseBlockLayout from "./BaseBlockLayout.vue";
import BlockHeaderDropdown from "../components/BlockHeaderDropdown.vue";
import PicBrowser from "../../landing_page_builder/components/PicBrowser.vue";
import {getResizedImageUrl, makeId} from "../../common/util";

export default Vue.extend({
    components: {
        editable: Editable3,
        BaseBlockLayout,
        BlockHeaderDropdown,
        PicBrowser,
    },
    data() {
        return {}
    },
    props: {
        spec: {
            type: Object as PropType<LandingHalfImageTextBlock>,
            required: true,
        },
        editable: {
            type: Boolean as () => boolean,
        },
        page: {
            type: Object as PropType<LandingPage>,
        },
        landing: {
            type: Object as PropType<Landing>,
        },
    },
    methods: {
        getResizedImageUrl,
        makeId
    },
});
</script>
