import '@tiptap/extension-text-style'

import { Extension } from '@tiptap/core'

export type TextShadowOptions = {
    types: string[],
}

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        textShadow: {
            /**
             * Set the text color
             */
            setTextShadow: (color: string) => ReturnType,
            /**
             * Unset the text color
             */
            unsetTextShadow: () => ReturnType,
        }
    }
}

export const TextShadow = Extension.create<TextShadowOptions>({
    name: 'textShadow',

    addOptions() {
        return {
            types: ['textStyle'],
        }
    },

    addGlobalAttributes() {
        return [
            {
                types: this.options.types,
                attributes: {
                    textShadow: {
                        default: null,
                        parseHTML: element => {
                            return element.style.textShadow?.replace(/['"]+/g, '');
                        },
                        renderHTML: attributes => {
                            if (!attributes.textShadow) {
                                return {}
                            }

                            return {
                                style: `text-shadow: ${attributes.textShadow}`,
                            }
                        },
                    },
                },
            },
        ]
    },

    addCommands() {
        return {
            setTextShadow: textShadow => ({ chain }) => {
                return chain()
                    .setMark('textStyle', { textShadow })
                    .run()
            },
            unsetTextShadow: () => ({ chain }) => {
                return chain()
                    .setMark('textStyle', { textShadow: null })
                    .removeEmptyTextStyle()
                    .run()
            },
        }
    },
})
