<template>
    <div id="app" class="show-page">
        <page
            :page="page"
            :editing="editing"
            :status="status"
            :landing="landing"
            :class="{'is-mobile': isMobile}"
            :blocks-class="{'saving-image': isForImage, 'saving-preview': isForPreview, 'external-application': isForExternalApplications}"
            :for-image="isForImage"
            :for-preview="isForPreview"
        ></page>
    </div>
</template>
<script lang="ts">
import Vue from 'vue';
import Page from "./Page.vue";


import {migrateIfNeeded} from "../migrations";
import {runningInBrowser, getContext} from "../../common/util";

export default Vue.extend({
    components: {
        Page,
    },
    data() {
        const isForImage = getContext().is_for_image ?? false;
        const isForPreview = getContext().is_for_preview ?? false;
        const pageData = isForImage ? getContext().landing.data : getContext().landing.published_data;

        return {
            editing: false,
            page: migrateIfNeeded(pageData),
            status: getContext().landing.status as string,
            landing: getContext().landing as Landing,
            isForImage: isForImage,
            isForPreview: isForPreview,
            isForExternalApplications: getContext().is_for_external_applications ?? false,
            isMobile: runningInBrowser() ? (window.document.documentElement as HTMLElement).offsetWidth < 722 : false,
        }
    },
    props: {},
    methods: {},
});
</script>
