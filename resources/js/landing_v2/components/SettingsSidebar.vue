<template>
    <b-sidebar
        :visible="value"
        @change="$emit('input', $event)"
        shadow
        right
        backdrop
    >
        <div class="p-3">
            <h3>{{ $t("Review") }}</h3>
            <div>
                <div
                    class="alert alert-success"
                    v-if="landing.status === 'active' && landing.permalink"
                >
                    {{ $t("Latest version published") }}:
                    <a
                        :href="landing.permalink"
                        target="_blank"
                        >{{ $t("here") }}</a
                    >
                </div>
                <b-form-group
                    :label="$t('Project')"
                    label-size="sm"
                >
                    <multilevel-select
                        data-testid="project-stage-select"
                        :options="projectsOptions"
                        :placeholders="[$t('Choose project').toString()]"
                        v-model="projectStageValue"
                    ></multilevel-select>
                    <span
                        class="text-warning"
                        v-if="
                            !landing.stage_id &&
                            !page.blocks.find(b => b.type === 'LandingCareersBlock') &&
                            page.blocks.find(b => ['LandingHeroFormBlock', 'LandingFormBlock'].includes(b.type))
                        "
                    >
                        {{ $t("Missing related project.") }}
                    </span>
                </b-form-group>
                <div
                    class="form-group"
                    v-if="$rlSettings.enable_internal_landings"
                >
                    <div class="d-flex text-sm mb-1">
                        <label
                            class="switch ml-0"
                            for="is_internal"
                        >
                            <input
                                id="is_internal"
                                class="switch__el"
                                type="checkbox"
                                v-model="landing.is_internal"
                            />
                            <span class="switch__holder">
                                <span class="switch__button"></span>
                            </span>
                            <span class="switch__text">
                                <span class="d-none d-md-flex">{{ $t("Internal job ad") }}</span>
                                <small class="d-md-none"><i class="fas fa-lg fa-eye"></i></small>
                            </span>
                        </label>
                    </div>
                    <div class="text-smaller text-muted">
                        {{ $t("The job ad can be accessed only by logged in users and from internal network.") }}
                    </div>
                </div>
                <b-form-group
                    label-size="sm"
                    :label="$t('Descriptive page name')"
                    :description="$t('This is used in internal menus. Candidates can not see this.')"
                >
                    <b-form-input
                        v-model="page.meta.name"
                        data-testid="landing-name"
                    ></b-form-input>
                </b-form-group>
                <b-form-group
                    label-size="sm"
                    :description="$t('Candidates can see this on social media sharing previews.')"
                    :label="$t('Page meta title')"
                >
                    <b-form-input v-model="page.meta.title"></b-form-input>
                </b-form-group>
                <b-form-group
                    label-size="sm"
                    :label="$t('Page meta description')"
                    :description="$t('Displayed when sharing page in social media.')"
                >
                    <b-form-textarea v-model="page.meta.description"></b-form-textarea>
                </b-form-group>
                <div>
                    <div class="mb-2">
                        {{ $t("Social media sharing image") }}
                    </div>
                    <div class="mb-2">
                        <img
                            class="img-fluid"
                            :src="page.meta.imageUrl"
                            v-if="page.meta.imageUrl"
                            alt="og:image preview"
                        />
                        <span
                            class="text-warning"
                            v-else
                        >
                            {{ $t("Missing social media preview image. Please generate or upload it.") }}
                        </span>
                    </div>
                    <div class="d-flex">
                        <button
                            @click="setPreview()"
                            class="btn btn-sm btn-primary mr-2"
                            :disabled="generatingPreview"
                        >
                            <b-spinner
                                small
                                class="mr-2"
                                v-if="generatingPreview"
                            ></b-spinner>
                            {{ $t("Generate preview") }}
                        </button>
                        <pic-browser
                            v-model="page.meta.imageUrl"
                            :label="$t('Choose image')"
                            btn-class="btn-sm"
                        ></pic-browser>
                    </div>
                </div>
            </div>
            <div class="text-center mt-4">
                <b-button
                    @click="save()"
                    variant="success"
                    class="mr-0 ml-auto"
                    :disabled="saving"
                >
                    <b-spinner
                        small
                        class="mr-2"
                        v-if="saving"
                    ></b-spinner>
                    {{ $t("Publish") }}
                </b-button>
            </div>

            <div
                class="clickable mt-4 mb-2"
                @click="showAdvanced = !showAdvanced"
            >
                <span>{{ $t("Advanced options") }}</span>
                <small>
                    <i
                        v-if="showAdvanced"
                        class="tdi td-chevron-down"
                    />
                    <i
                        v-else
                        class="tdi td-chevron-right"
                    />
                </small>
            </div>
            <b-collapse
                id="collapse-advanced"
                accordion="advanced-options"
                v-model="showAdvanced"
            >
                <div>
                    <div class="mb-3">
                        <b-button
                            size="sm"
                            variant="outline-primary"
                            @click="exportPageToClipboard"
                            class="mb-2"
                        >
                            {{ $t("Export page to clipboard") }}
                        </b-button>
                        <div
                            v-if="exportSuccess"
                            class="text-success small mb-2"
                        >
                            {{ $t("Page data copied to clipboard!") }}
                        </div>
                    </div>
                    <div>
                        <label for="import-textarea">{{ $t("Import page data") }}</label>
                        <b-form-textarea
                            id="import-textarea"
                            v-model="importData"
                            placeholder="Paste exported page data here"
                            rows="3"
                            max-rows="6"
                        ></b-form-textarea>
                        <b-button
                            size="sm"
                            variant="outline-primary"
                            @click="importPage"
                            class="mt-2"
                            :disabled="!importData"
                        >
                            {{ $t("Import") }}
                        </b-button>
                        <div
                            v-if="importError"
                            class="text-danger small mt-2"
                        >
                            {{ importError }}
                        </div>
                    </div>
                </div>
            </b-collapse>
        </div>
    </b-sidebar>
</template>
<script lang="ts">
import Vue, {PropType} from "vue";
import {BButton, BFormGroup, BFormInput, BFormTextarea, BSidebar, BSpinner, BCollapse} from "bootstrap-vue";
import {LandingBlockType, LandingPage} from "../LandingTypes";
import axios from "axios";
import html2canvas from "html2canvas";
import {getContext, getResizedImageUrl, showErrorToast} from "../../common/util";
import PicBrowser from "../../landing_page_builder/components/PicBrowser.vue";
import MultilevelSelect, {SelectedValues} from "../../common/components/MultilevelSelect.vue";
import i18n from "../../common/i18n";

export default Vue.extend({
    i18n,
    components: {
        BSidebar,
        BButton,
        BFormGroup,
        BFormInput,
        BFormTextarea,
        BSpinner,
        PicBrowser,
        MultilevelSelect,
        BCollapse,
    },
    data() {
        return {
            generatingPreview: false,
            projectsOptions: getContext().projects_options as BMultiSelectOption[],
            projectId: getContext().project_id as number | null,
            previewURL: this.page.meta.imageUrl,
            importData: "",
            importError: "",
            exportSuccess: false,
            showAdvanced: false,
        };
    },
    props: {
        value: {
            type: Boolean,
        },
        page: {
            type: Object as PropType<LandingPage>,
            required: true,
        },
        landing: {
            type: Object as PropType<Landing>,
            required: true,
        },
        saving: {
            type: Boolean as PropType<boolean>,
        },
    },
    methods: {
        save() {
            this.$emit("publish");
        },
        exportPageToClipboard() {
            try {
                const pageData = JSON.stringify(this.page);
                // Use this UTF-8 safe version instead of btoa
                const base64Data = this.utf8ToBase64(pageData);
                navigator.clipboard.writeText(base64Data);
                this.exportSuccess = true;
                setTimeout(() => {
                    this.exportSuccess = false;
                }, 3000);
            } catch (error) {
                console.error("Error exporting page data:", error);
                showErrorToast(this, this.$t("Failed to export page data").toString());
            }
        },

        // Helper function to safely encode UTF-8 strings to base64
        utf8ToBase64(str) {
            return btoa(
                encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
                    return String.fromCharCode(parseInt(p1, 16));
                })
            );
        },

        // Helper function to safely decode base64 to UTF-8 strings
        base64ToUtf8(str) {
            return decodeURIComponent(
                Array.prototype.map
                    .call(atob(str), c => {
                        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
                    })
                    .join("")
            );
        },
        importPage() {
            try {
                this.importError = "";
                if (!this.importData) {
                    this.importError = this.$t("Please paste exported page data").toString();
                    return;
                }

                // Decode base64 and parse JSON using our UTF-8 safe method
                const jsonData = this.base64ToUtf8(this.importData.trim());
                const pageData = JSON.parse(jsonData);

                // Validate the imported data has the expected structure
                if (!pageData.meta || !pageData.blocks || !pageData.theme || !pageData.settings) {
                    this.importError = this.$t("Invalid page data format").toString();
                    return;
                }

                // Update the page with the imported data
                this.$emit("update:page", pageData);
                this.importData = "";

                // Show success message
                showErrorToast(this, this.$t("Page data imported successfully").toString(), {
                    variant: "success",
                    autoHideDelay: 3000,
                    title: this.$t("Success").toString(),
                });
            } catch (error) {
                console.error("Error importing page data:", error);
                this.importError = this.$t("Failed to import page data. Make sure the format is correct.").toString();
            }
        },
        async setPreview() {
            this.generatingPreview = true;
            const previewUrl = await axios.get(`/landings/${this.landing.id}/getPreviewUrl?force=1`);
            this.previewURL = previewUrl.data;
            if (!previewUrl.data) {
                showErrorToast(
                    this,
                    "Failed to generate preview. Please check that you have included a hero block in the landing page.",
                    {
                        autoHideDelay: 5000,
                    }
                );
            } else {
                this.page.meta.imageUrl = previewUrl.data;
            }
            this.generatingPreview = false;
        },
        getResizedImageUrl: getResizedImageUrl,
        createPreview(): Promise<string | null> {
            return new Promise((resolve, reject) => {
                const heroBlock =
                    this.page.blocks.find(b => b.type === LandingBlockType.LandingHeroBlock) ||
                    this.page.blocks.find(b => b.type === LandingBlockType.LandingHeroFormBlock);
                if (!heroBlock) {
                    return resolve(null);
                }
                const heroEl = document.getElementById(heroBlock._id);
                if (!heroEl) {
                    return resolve(null);
                }
                heroEl.style.width = "1000px";
                heroEl.style.height = "524px";
                heroEl.classList.add("saving-image");
                this.$emit("set-editing", false);
                const vm = this;
                this.$nextTick(() => {
                    html2canvas(heroEl, {allowTaint: false, useCORS: true}).then(function (canvas) {
                        document.body.appendChild(canvas);
                        resolve(canvas.toDataURL("image/png"));
                        heroEl.style.width = "";
                        heroEl.style.height = "";
                        heroEl.classList.remove("saving-image");
                        vm.$emit("set-editing", true);
                        document.body.removeChild(canvas);
                    });
                });
            });
        },
    },
    computed: {
        projectStageValue: {
            get(): SelectedValues {
                let stageId = this.landing.stage_id;
                if (stageId) {
                    let projectOption = this.projectsOptions.find(project =>
                        (project.children ?? []).find(stage => stage.value === stageId)
                    );
                    if (projectOption) {
                        return [projectOption.value, stageId];
                    }
                } else if (this.projectId) {
                    let projectOption = this.projectsOptions.find(proj => proj.value === this.projectId);
                    if (projectOption && projectOption.children) {
                        this.landing.stage_id = projectOption.children[0].value;
                    }
                    return [this.projectId, this.landing.stage_id];
                }
                return [null, null];
            },
            set(values: SelectedValues) {
                if (!values[0]) {
                    this.landing.stage_id = null;
                } else if (values[0] && !values[1]) {
                    let projectOption = this.projectsOptions.find(proj => proj.value === values[0]);
                    if (projectOption && projectOption.children) {
                        this.landing.stage_id = projectOption.children[0].value;
                    }
                } else {
                    this.landing.stage_id = values[1];
                }
            },
        },
    },
});
</script>
