<template>
    <div class="blocks-container apply-font"
         :class="{
            'is-mobile': isMobile,
            'editing': editing,
            'has-body-font': !!this.page.theme.bodyFont,
            ...blocksClass,
        }"
         :style="variables"
    >
        <landing-font-manager
            :theme="page.theme"
        ></landing-font-manager>
        <div
            v-if="(status === 'archived')"
            class="alert alert-danger text-center"
        >Current Job ad is archived and no longer accepting applications.
        </div>
        <component
            v-for="(b, i) in blocks"
            :key="b._id"
            @duplicate="duplicate(b)"
            @remove="remove(b)"
            @up="moveUp(b)"
            :editable="editing"
            :focus="i === page.blocks.findIndex(b => b.type === 'HeroBlock')"
            @down="moveDown(b)"
            :theme="page.theme"
            @append="append($event, b)"
            :class="b.type"
            :page="page"
            :landing="landing"
            :is="b.type"
            :spec="b"
            :id="b._id"
            v-observe-visibility="{
  callback: visibilityChanged(b),
  once: true,
  throttle: 300,
  intersection: {
    threshold: 0.7,
  }
}"
        ></component>
        <landing-null-block
            v-if="editing"
            :spec="{_id: 'null', type: 'LandingNullBlock'}"
            :editable="true"
            :landing="landing"
            class="LandingNullBlock"
            @append="append($event, null)"
        ></landing-null-block>
        <div
            class="text-center p-2 page-footer"
            :title="`Schema version ${page.version}`"
        >
            <a
                :href="`https://www.teamdash.com/applicant-tracking-system/?utm_source=client&utm_campaign=job_footer&utm_content=${instanceName}`"
                target="_blank"
            >
                Applicant tracking system
            </a>
            by
            <a
                :href="`https://www.teamdash.com/?utm_source=client&utm_campaign=job_footer&utm_content=${instanceName}`"
                target="_blank"
            >
                Teamdash
            </a>
        </div>
    </div>
</template>

<script lang="ts">
/* eslint vue/no-mutating-props: 0 */
import Vue, {PropType} from 'vue';
import {LandingBlock, LandingPage, LandingBlockType, LandingPageTheme} from "../LandingTypes";
import {copyBlockWithNewId, getNewDefaultBlock,} from "../data";
import LandingImageBlock from '../blocks/LandingImageBlock.vue';
import LandingHeroBlock from '../blocks/LandingHeroBlock.vue';
import LandingImageTextBlock from "../blocks/LandingImageTextBlock.vue";
import LandingCountdownBlock from "../blocks/LandingCountdownBlock.vue";
import LandingBgImageTextBlock from "../blocks/LandingBgImageTextBlock.vue";
import LandingQuotesBlock from "../blocks/LandingQuotesBlock.vue";
import LandingVideoBlock from "../blocks/LandingVideoBlock.vue";
import LandingFormBlock from "../blocks/LandingFormBlock.vue";
import LandingContactBlock from "../blocks/LandingContactBlock.vue";
import LandingLogoBlock from "../blocks/LandingLogoBlock.vue";
import LandingNullBlock from "../blocks/LandingNullBlock.vue";
import LandingTextBlock from "../blocks/LandingTextBlock.vue";
import LandingHalfImageTextBlock from "../blocks/LandingHalfImageTextBlock.vue";
import LandingHalfVideoTextBlock from "../blocks/LandingHalfVideoTextBlock.vue";
import LandingImageArgumentsBlock from "../blocks/LandingImageArgumentsBlock.vue";
import LandingImageArgumentsWithTitleBlock from "../blocks/LandingImageArgumentsWithTitleBlock.vue";
import LandingCareersBlock from "../blocks/LandingCareersBlock.vue";
import LandingSocialMediaBlock from "../blocks/LandingSocialMediaBlock.vue";
import LandingHeroFormBlock from "../blocks/LandingHeroFormBlock.vue";
import VueObserveVisibility from 'vue-observe-visibility'
import {getContext, runningInBrowser} from "../../common/util";
import {colorMix, isValidHexColor} from "../util";
import LandingFontManager from "./LandingFontManager.vue";

Vue.use(VueObserveVisibility);

export default Vue.extend({
    components: {
        LandingFontManager,
        LandingHeroBlock,
        LandingHeroFormBlock,
        LandingImageBlock,
        LandingImageTextBlock,
        LandingCountdownBlock,
        LandingBgImageTextBlock,
        LandingQuotesBlock,
        LandingVideoBlock,
        LandingFormBlock,
        LandingContactBlock,
        LandingLogoBlock,
        LandingTextBlock,
        LandingNullBlock,
        LandingCareersBlock,
        LandingHalfImageTextBlock,
        LandingHalfVideoTextBlock,
        LandingImageArgumentsBlock,
        LandingImageArgumentsWithTitleBlock,
        LandingSocialMediaBlock,
    },
    data() {
        return {
            isMobile: false,
            instanceName: getContext().instance_name,
        };
    },
    props: {
        blocksClass: {
            type: Object as PropType<Record<string, boolean>>,
        },
        page: {
            type: Object as PropType<LandingPage>,
        },
        editing: {
            type: Boolean as PropType<boolean>,
        },
        status: {
            type: String as PropType<string>,
        },
        landing: {
            type: Object as PropType<Landing>,
        },
        themePreview: {
            type: Object as PropType<Partial<LandingPageTheme>>,
            default: () => {
                return {
                    font: null,
                    bodyFont: null,
                };
            }
        },
        forImage: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        forPreview: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
    },
    mounted() {
        this.trackPageview();
        this.trackSubmissions();
    },
    methods: {
        visibilityChanged(block: LandingBlock) {
            if (!runningInBrowser()) {
                return;
            }
            if (!window.is_landing) {
                return () => {
                };
            }
            if (this.forPreview || this.forImage) {
                return () => {
                };
            }
            return (visible: boolean, entry: IntersectionObserverEntry) => {
                if (visible && window.rudderanalytics) {
                    console.log(`Viewed: ${block.type} ${block._id}`);
                    window.rudderanalytics.track('Viewed client landing block', {
                        blockId: block._id,
                        blockType: block.type,
                        // @ts-ignore
                        instance: this.instanceName,
                        landingId: window.landing.url_token ?? window.landing.id,
                    })
                }
            }
        },
        trackPageview() {
            if (!runningInBrowser()) {
                return;
            }
            if (!window.is_landing) {
                return;
            }
            if (this.forPreview || this.forImage) {
                return;
            }
            if (window.rudderanalytics) {
                window.rudderanalytics.page({
                    instance: this.instanceName,
                    page_id: window.landing.url_token ?? window.landing.id,
                })
            }
        },
        trackSubmissions() {
            if (!runningInBrowser()) {
                return;
            }
            if (!window.is_landing) {
                return () => {
                };
            }

            const vm = this;

            window.addEventListener('message', function (event) {
                if (!window.rudderanalytics) {
                    return;
                }

                const isString = (str: string): boolean => {
                    return Object.prototype.toString.call(str) === "[object String]"
                }

                const isEmail = (email: string): boolean => {
                    const validRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
                    return !!email.match(validRegex);
                }

                if (event.data.action === 'submission') {
                    event.data.form_data;
                    let values = Object.values(event.data.form_data) as string[];
                    let email = values.filter(isString).filter(isEmail)[0] as string | undefined;
                    window.rudderanalytics.track('Form submitted', {
                        email: email,
                        instance: vm.instanceName,
                        landingId: window.landing.url_token ?? window.landing.id,
                    })
                }
            });
        },
        setIsMobile() {
            if ((this.$el as HTMLElement).offsetWidth > 738) {
                this.isMobile = true
            } else {
                this.isMobile = false;
            }
        },
        duplicate(block: LandingBlock) {
            const idx = this.page.blocks.findIndex(bs => bs === block);

            this.page.blocks.splice(idx, 0, copyBlockWithNewId(block));
        },
        remove(block: LandingBlock) {
            const idx = this.page.blocks.findIndex(bs => bs === block);
            this.page.blocks.splice(idx, 1);
        },
        append(block: LandingBlock, after: LandingBlock) {
            const idx = this.page.blocks.findIndex(bs => bs === after);
            this.page.blocks.splice(idx + 1, 0, block);
            return block;
        },
        moveUp(block: LandingBlock) {
            const idx = this.page.blocks.findIndex(bs => bs === block);
            if (idx === 0) {
                return;
            }
            const rows = [this.page.blocks[idx - 1], this.page.blocks[idx]];
            this.page.blocks.splice(idx - 1, 2, rows[1], rows[0]);
        },
        moveDown(block: LandingBlock) {
            const idx = this.page.blocks.findIndex(bs => bs === block);
            if (idx === this.page.blocks.length - 1) {
                return;
            }
            const rows = [this.page.blocks[idx], this.page.blocks[idx + 1]];
            this.page.blocks.splice(idx, 2, rows[1], rows[0]);
        },
    },
    computed: {
        blocks(): LandingBlock[] {
            return this.page.blocks.filter((b) => {
                return (
                    (
                        this.status === 'archived' && b.type !== 'LandingFormBlock'
                    ) ||
                    this.status !== 'archived'
                )
            });
        },
        variables(): any {
            return {
                '--landing-primary': this.page.theme.brandColor,
                '--landing-primary--darker': isValidHexColor(this.page.theme.brandColor) ? colorMix(this.page.theme.brandColor, "#000000", 3) : null,
                '--landing-background': this.page.theme.backgroundColor,
                '--landing-background--darker': isValidHexColor(this.page.theme.backgroundColor) ? colorMix(this.page.theme.backgroundColor, '#000000', 3) : null,
                '--landing-heading-font': this.themePreview.font || this.page.theme.font,
                '--landing-font': this.themePreview.bodyFont || this.page.theme.bodyFont,
                '--landing-heading-color': this.page.theme.headingColor,
                '--landing-text-color': this.page.theme.textColor,
            }
        },
    }
})
</script>
