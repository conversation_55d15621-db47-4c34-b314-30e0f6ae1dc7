import Vue from "vue";
import Page from "./components/Page.vue";
import {
    <PERSON><PERSON>utton,
    BCollapse,
    BDropdown,
    BDropdownForm,
    BDropdownText,
    BFormInput,
    BModal,
    BNavbar,
    BNavbarToggle,
    BOverlay,
    BSidebar,
    BSpinner,
    BTab,
    BTabs,
    BTooltip,
    ToastPlugin,
    VBPopover,
} from "bootstrap-vue";
import {fontsApiKey, initSentry} from "../common/config";
import FontPicker from "font-picker-vue";
import TdFontPicker from "../common/components/FontPicker.vue";
import {LandingBlockType, LandingPage} from "./LandingTypes";
import {pageTemplate, basicTemplate} from "./data";
import TemplatePicker from "./components/TemplatePicker.vue";
import SettingsSidebar from "./components/SettingsSidebar.vue";
import axios from "axios";
import {debounce} from "lodash";
import {setUpLaraform} from "../common/LF_config";
import Laraform from "@laraform/laraform/src";
import RegisterForm from "../common/components/RegisterForm.vue";
import {migrateIfNeeded} from "./migrations";
import {slugify} from "../common/slugify";
import {getContext, setStyle} from "../common/util";
import MovableDialog from "../common/components/MovableDialog.vue";
import {PrismEditor} from "vue-prism-editor";
import "vue-prism-editor/dist/prismeditor.min.css";
import {highlight, languages} from "prismjs/components/prism-core";
import "prismjs/components/prism-css";
import "prismjs/themes/prism.css";
import html2canvas from "html2canvas";
import {calculateContrast, calculateLuminance, isValidHexColor} from "./util";
import i18n from "../common/i18n"; // import syntax highlighting styles
import {goBackInHistoryOrTo} from "../common/util";
import LandingColorPicker from "./components/LandingColorPicker.vue";
import LandingTheme from "./components/LandingTheme.vue";
import LandingHistory from "./components/LandingHistory.vue";

declare global {
    interface Window {
        page: BlockPage;
        access: BlockPageAccess | undefined;
        full: boolean;
        is_simple: true | undefined;
    }
}

initSentry();
setUpLaraform(Laraform);
Vue.use(Laraform);

Vue.directive("b-popover", VBPopover);

Vue.use(ToastPlugin);

const debounceSaveDraft = debounce((vm: any) => {
    vm.draftStatus = "saving";
    vm.saveDraft();
}, 2000);

const debounceSaveStyle = debounce((v: string) => {
    setStyle(v);
}, 500);

window.app = new Vue({
    i18n,
    el: "#app",
    components: {
        BButton,
        BOverlay,
        BTooltip,
        BCollapse,
        BNavbar,
        BNavbarToggle,
        FontPicker,
        TdFontPicker,
        BSpinner,
        BDropdown,
        BDropdownText,
        BDropdownForm,
        BFormInput,
        Page,
        TemplatePicker,
        SettingsSidebar,
        BModal,
        RegisterForm,
        MovableDialog,
        PrismEditor,
        LandingColorPicker,
        LandingTheme,
        LandingHistory,
        BTabs,
        BTab,
    },
    mounted() {
        if (window.full) {
            this.setIsMobile();
        }
        setStyle(this.page.theme.customCss);
    },
    data() {
        let page: LandingPage =
            (window?.landing?.data as unknown as LandingPage | null) ||
            (window.is_simple ? basicTemplate : pageTemplate);
        if (window.is_simple) {
            window.analytics.track("Opened public landing page tool");
        }
        if (window.full) {
            page = window?.landing?.published_data;
        }
        return {
            editing: !window.full,
            showFullScreen: window.full,
            isMobile: false,
            fontsApiKey: fontsApiKey,
            page: migrateIfNeeded(page),
            landing: window.landing || {
                id: null,
                type: "v3",
                data: null,
                status: "draft",
                stage_id: null,
                slug: "",
                image_url: null,
                public_token: null,
                private_token: null,
                is_internal: false,
            },
            loading: false,
            dirty: false,
            saving: false,
            activeTemplate: null,
            showTemplatePicker: false,
            showThemeEditor: false,
            showPublishSidebar: false,
            draftStatus: null as "dirty" | "saved" | "saving" | null,
            showPublishedModal: false,
            showPublishLanding: false,
            user: getContext().user,
            showRegisterModal: false,
            downloadingAsImage: getContext().is_for_image ?? false,
            downloadFormat: "image/png" as "image/png" | "image/jpeg",
            downloadWidth: 800,
            downloadPreference: "smallfile",
            themePreview: {
                bodyFont: null,
                font: null,
            },
            showHistory: false,
            historyPreviewLandingData: null,
        };
    },
    computed: {
        downloadImageURL(): string | null {
            if (!this.landing.permalink) {
                return null;
            }

            const imageGenerationParams = {
                format: this.downloadFormat.split("/")[1],
                width: `${this.downloadWidth}`,
                optimize: this.downloadPreference === "quality" ? "false" : "true",
            };
            const params = new URLSearchParams(imageGenerationParams);
            return `/landings/${this.landing.id}/downloadAsImage?${params.toString()}`;
        },
        narrowBuilder(): boolean {
            return this.showThemeEditor || this.showTemplatePicker || this.showHistory;
        },
    },
    methods: {
        getRelativeLuminance(hex1: string, hex2: string) {
            if (!hex1 || !hex2 || !isValidHexColor(hex1) || !isValidHexColor(hex2)) {
                return 0;
            }
            const lum1 = calculateLuminance(hex1);
            const lum2 = calculateLuminance(hex2);
            const bits = [lum1, lum2];

            return (Math.max(...bits) + 0.05) / (Math.min(...bits) + 0.05);
        },
        setIsMobile() {
            this.isMobile = (this.$el as HTMLElement).offsetWidth < 722;
        },
        saveDraft() {
            if (!this.landing.id) {
                return axios
                    .post(`/landings`, {
                        ...this.landing,
                        slug: slugify(this.page.meta.title, true),
                        data: this.page,
                    })
                    .then(({data}) => {
                        this.landing.id = data.updates.id;
                        this.landing.permalink = data.updates.permalink;
                        history.replaceState(null, "", `/landings/${data.updates.id}/edit`);
                        this.showOk();
                    });
            } else {
                return axios
                    .put(`/landings/${this.landing.id}`, {
                        ...this.landing,
                        slug: slugify(this.page.meta.title, true),
                        data: this.page,
                    })
                    .then(() => {
                        this.showOk();
                    });
            }
        },
        highlighter(code: string): void {
            return highlight(code, languages.css); // languages.<insert language> to return html with markup
        },
        publish() {
            this.landing.status = "active";
            this.saving = true;
            if (!this.landing.id) {
                this.saveDraft().then(() => {
                    this.doPublish();
                });
            } else {
                this.doPublish();
            }
        },
        doPublish() {
            return axios
                .put(`/landings/${this.landing.id}?publish=1`, {
                    ...this.landing,
                    slug: slugify(this.page.meta.title, true),
                    data: this.page,
                })
                .then(res => {
                    window.analytics.track("Published landing");
                    this.showOk(true);
                    this.landing.permalink = res.data.updates.permalink;
                    if (res.data.should_publish) {
                        this.showPublishLanding = true;
                    } else {
                        this.showPublishedModal = true;
                    }
                });
        },
        handleStructuredJob(res: any) {
            if (res.payload.redirect_url) {
                window.location.href = res.payload.redirect_url;
            } else {
                window.location.href = "/structured-jobs";
            }
        },
        showOk(modal = false) {
            this.saving = false;
            this.draftStatus = "saved";
            if (modal) {
                this.$bvToast.toast(this.$t("Page saved!").toString(), {
                    variant: "success",
                    autoHideDelay: 2000,
                    solid: true,
                });
            }
        },
        chooseCurrentTemplate() {
            if (this.activeTemplate) {
                this.page = JSON.parse(JSON.stringify(this.activeTemplate));
                analytics.track("Chose template from landing page tool");
            }
            this.activeTemplate = null;
            this.showTemplatePicker = false;
        },
        downloadAsImage__old() {
            function downloadURI(uri: string, name: string) {
                const link = document.createElement("a");
                console.log("created link");
                link.download = name;
                link.href = uri;
                document.body.appendChild(link);
                console.log("appended link to body");
                link.click();
                console.log("clicked link");
                document.body.removeChild(link);
            }

            this.downloadingAsImage = true;

            this.createFullImage(parseInt(this.downloadWidth + ""), this.downloadFormat).then(dataUri => {
                console.log("createFullImage resolved successfully");
                if (dataUri) {
                    console.log("data length: " + dataUri.length);
                    if (this.downloadFormat === "image/jpeg") {
                        downloadURI(dataUri, "download.jpg");
                    } else {
                        downloadURI(dataUri, "download.png");
                    }
                }
                window.analytics.track("Landing exported to image");
                this.downloadingAsImage = false;
            });
        },
        createFullImage(width: number, mime: "image/jpeg" | "image/png"): Promise<string | null> {
            console.log("creating full image");
            return new Promise((resolve, reject) => {
                const pageEl = document.querySelector(".blocks-container") as HTMLElement;
                if (!pageEl) {
                    console.error("no page element");
                    return resolve(null);
                }
                pageEl.style.width = width + "px";
                this.editing = false;
                const vm = this;
                setTimeout(() => {
                    console.log("started html2canvas");
                    html2canvas(pageEl, {allowTaint: false, useCORS: true, scale: 2})
                        .then(function (canvas) {
                            console.log("html2canvas complete");
                            document.body.appendChild(canvas);
                            const dataURL = canvas.toDataURL(mime);
                            // resolve(dataURL);
                            console.log("first dataUrl OK");
                            pageEl.style.width = "";
                            vm.editing = true;
                            if (true) {
                                console.log("starting resize");
                                const c2 = document.createElement("canvas");
                                c2.setAttribute("width", canvas.width / window.devicePixelRatio + "");
                                c2.setAttribute("height", canvas.height / window.devicePixelRatio + "");
                                document.body.appendChild(c2);
                                const ctx = c2.getContext("2d");
                                if (ctx) {
                                    ctx.drawImage(
                                        canvas,
                                        0,
                                        0,
                                        canvas.width,
                                        canvas.height,
                                        0,
                                        0,
                                        canvas.width / window.devicePixelRatio,
                                        canvas.height / window.devicePixelRatio
                                    );
                                    resolve(c2.toDataURL(mime));
                                    console.log("second dataUrl OK");
                                    document.body.removeChild(c2);
                                } else {
                                    console.log("could not get 2nd ctx");
                                }
                            } else {
                                resolve(dataURL);
                            }
                            document.body.removeChild(canvas);
                        })
                        .catch(err => {
                            console.error(err);
                        });
                }, 200);
            });
        },
        redirectToPublishForm() {
            window.location.href = "/structured-jobs/create?publish_landing_id=" + this.landing.id;
        },
        goBackInHistoryOrTo,
        toggleThemeEditor() {
            if (this.showThemeEditor) {
                this.showThemeEditor = false;
            } else {
                this.showThemeEditor = true;
                this.showTemplatePicker = false;
            }
        },
        toggleTemplatePicker() {
            if (this.showTemplatePicker) {
                this.showTemplatePicker = false;
            } else {
                this.showTemplatePicker = true;
                this.showThemeEditor = false;
                this.activeTemplate = null;
            }
        },
        toggleHistory() {
            this.showHistory = !this.showHistory;
        },
    },
    watch: {
        page: {
            deep: true,
            handler() {
                if (!this.dirty) {
                    this.dirty = true;
                }
                this.draftStatus = "dirty";
                if (this.user) {
                    debounceSaveDraft(this);
                }
            },
        },
        "page.theme.customCss": {
            deep: true,
            handler(v) {
                debounceSaveStyle(v);
            },
        },
        "page.theme.backgroundColor": {
            handler(v) {
                if (!isValidHexColor(v)) {
                    return v;
                }
                if (["#333333", "#ffffff"].includes(this.page.theme.headingColor)) {
                    this.page.theme.headingColor = calculateContrast(v) > 186 ? "#333333" : "#ffffff";
                }
                if (["#666666", "#ffffff"].includes(this.page.theme.textColor)) {
                    this.page.theme.textColor = calculateContrast(v) > 186 ? "#666666" : "#ffffff";
                }
            },
        },
        isMobile(v) {
            this.$nextTick(() => {
                window.dispatchEvent(new Event("resize"));
            });
        },
        editing(v) {
            if (!v) {
                this.$nextTick(() => {
                    window.dispatchEvent(new Event("resize"));
                });
            }
        },
        showRegisterModal(v) {
            if (v) {
                window.analytics.track("Clicked public landing page tool save button");
            }
        },
    },
    directives: {
        VBPopover,
    },
});
