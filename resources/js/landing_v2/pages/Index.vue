<script lang="ts">
import {defineComponent} from "vue";
import {PropType} from "vue/types/options";
import SettingsPageCardLayout from "../../settings/components/SettingsPageCardLayout.vue";
import SettingsPageLayout from "../../settings/components/SettingsPageLayout.vue";
import CheckboxDropdown from "../../common/CheckboxDropdown.vue";
import StyledCheckbox from "../../common/StyledCheckbox.vue";
import {BDropdown, BDropdownItem, BDropdownText, BFormSelect, BSpinner} from "bootstrap-vue";
import EmptyState from "../../common/EmptyState.vue";
import LaravelVuePagination from "laravel-vue-pagination";
import {DateTime} from "luxon";
import InlineLandingTags from "../../common/InlineLandingTags.vue";
import LandingStats from "../components/stats/LandingStats.vue";
import InlineEditable from "../../common/InlineEditable.vue";
import LandingTableRow from "../../common/LandingTableRow.vue";
import axios from "axios";

export default defineComponent({
    name: "Index.vue",
    components: {
        EmptyState,
        StyledCheckbox,
        CheckboxDropdown,
        SettingsPageCardLayout,
        BFormSelect,
        LaravelVuePagination,
        LandingTableRow,
    },
    layout: SettingsPageLayout,
    props: {
        landings: {
            type: Object as PropType<PaginatedResults<Landing>>,
            required: true,
        },
        landingTemplate: {
            type: String as PropType<string>,
            required: true,
        },
        landingTagsOptions: {
            type: Array as PropType<BMultiSelectOption[]>,
            required: true,
        },
        teamsOptions: {
            type: Array as PropType<BMultiSelectOption[]>,
            required: true,
        },
        filters: {
            type: Object as PropType<{
                name: string | null;
                statuses: string[];
                tag_ids: number[];
                team_ids: number[];
                teams_with_descendants: boolean;
                activeExports: string;
                landingType: string;
            }>,
            required: true,
        },
        landingTags: {
            type: Array as PropType<LandingTag[]>,
            required: true,
        },
    },
    data() {
        return {
            loadingStats: false,
        };
    },
    mounted() {
        this.loadStats();
    },
    methods: {
        tagAdded(landingTag: LandingTag) {
            this.$inertia.reload({only: ["landingTags"]});
        },
        async loadStats() {
            this.loadingStats = true;
            const response = await axios.get(this.route("landings.stats"), {
                params: {
                    landing_ids: this.landings.data.map(landing => landing.id),
                },
            });
            this.landings.data.map(landing => {
                const stats = response.data[landing.id];
                if (stats) {
                    this.$set(landing, "impressions", stats.impressions);
                    this.$set(landing, "submissions", stats.submissions);
                }
            });
            this.loadingStats = false;
        },
        search(page: number, preserveScroll = true) {
            this.$inertia.visit(
                this.route("landings.index", {
                    page: page,
                    ...this.filters,
                }),
                {
                    preserveState: true,
                    preserveScroll: preserveScroll,
                    only: ["landings"],
                    onSuccess: () => {
                        this.loadStats();
                    },
                }
            );
        },
    },
    computed: {
        statusOptions(): BMultiSelectOption[] {
            return [
                {value: "active", label: this.$t("Active").toString()},
                {value: "draft", label: this.$t("Draft").toString()},
                {value: "archived", label: this.$t("Archived").toString()},
            ];
        },
        exportsOptions(): BSelectOption[] {
            return [
                {value: "EXISTS", text: this.$t("With active exports").toString()},
                {value: "NOT_EXISTS", text: this.$t("Without active exports").toString()},
                {value: "ANY", text: this.$t("Any").toString()},
            ];
        },
        landingTypeOptions(): BSelectOption[] {
            return [
                {value: "LANDING", text: this.$t("Landing page").toString()},
                {value: "EXPORTABLE", text: this.$t("Downloadable image").toString()},
                {value: "ANY", text: this.$t("Any type").toString()},
            ];
        },
    },
    watch: {
        filters: {
            handler: function () {
                this.search(1);
            },
            deep: true,
        },
    },
});
</script>

<template>
    <settings-page-card-layout>
        <template #settingsHeader>
            <div class="d-flex gap-2">
                <a
                    :href="route('landings.create')"
                    class="btn btn-info btn-sm"
                    v-if="landingTemplate !== 'default'"
                >
                    <i class="tdi td-plus mr-2"></i>
                    {{ $t("Create legacy page") }}
                </a>
                <a
                    :href="route('landings.v2.create')"
                    class="btn btn-primary btn-sm"
                >
                    <i class="tdi td-plus mr-2"></i>
                    {{ $t("Create landing page") }}
                </a>
            </div>
        </template>
        <template #settingsTabs>
            <ul class="nav nav-tabs card-tabs">
                <li class="nav-item">
                    <Link
                        :href="route('landings.index')"
                        class="nav-link active"
                    >
                        {{ $t("Landing pages") }}
                    </Link>
                </li>
                <li class="nav-item">
                    <a
                        :href="route('structured-jobs.index')"
                        class="nav-link"
                    >
                        {{ $t("Other exports") }}
                    </a>
                </li>
            </ul>
            <div class="card-header d-flex gap-2 align-items-center">
                <div>
                    <input
                        type="text"
                        v-model="filters.name"
                        class="form-control form-control-sm"
                        :placeholder="$t('Search...')"
                    />
                </div>
                <div>
                    <checkbox-dropdown
                        v-model="filters.statuses"
                        variant="white"
                        :options="statusOptions"
                        :text="$t('Status')"
                    >
                    </checkbox-dropdown>
                </div>
                <div>
                    <checkbox-dropdown
                        v-model="filters.tag_ids"
                        variant="white"
                        :options="landingTagsOptions"
                        :text="$t('Tags')"
                        search
                    >
                    </checkbox-dropdown>
                </div>
                <div v-if="$rlSettings.features.teams">
                    <checkbox-dropdown
                        v-if="teamsOptions.length > 0"
                        v-model="filters.team_ids"
                        variant="white"
                        :options="teamsOptions"
                        :search="true"
                        :text="$t('Teams')"
                        icon="fa-users"
                    >
                        <div slot="top">
                            <styled-checkbox
                                class="mb-2"
                                no-wrap
                                v-model="filters.teams_with_descendants"
                            >
                                {{ $t("With descendant teams") }}
                            </styled-checkbox>
                        </div>
                    </checkbox-dropdown>
                </div>
                <div>
                    <b-form-select
                        size="sm"
                        v-model="filters.activeExports"
                        :options="exportsOptions"
                    ></b-form-select>
                </div>
                <div>
                    <b-form-select
                        size="sm"
                        v-model="filters.landingType"
                        :options="landingTypeOptions"
                    ></b-form-select>
                </div>
            </div>
        </template>
        <empty-state
            v-if="landings.total === 0"
            :title="$t('No landings found').toString()"
        ></empty-state>

        <template v-else>
            <div class="mt-1 mb-1">
                {{ $tc("Found 1 landing.|Found {count} landings.", landings.total) }}
            </div>
            <table class="table table-td table-top">
                <thead>
                    <tr>
                        <th style="width: 50%">
                            {{ $t("Title") }}
                        </th>
                        <th>{{ $t("Status") }}</th>
                        <th>{{ $t("Project") }}</th>
                        <th style="min-width: 150px">{{ $t("Statistics") }}</th>
                        <th style="width: 192px"></th>
                    </tr>
                </thead>
                <tbody>
                    <landing-table-row
                        v-for="landing in landings.data"
                        :key="landing.id"
                        :landing="landing"
                        :landing-tags="landingTags"
                        :loading-stats="loadingStats"
                        @structured-job-published="search(landings.current_page)"
                        @updated="search(landings.current_page)"
                        @tag-added="tagAdded"
                    >
                    </landing-table-row>
                </tbody>
            </table>
            <laravel-vue-pagination
                :data="landings"
                show-disabled
                :limit="5"
                @pagination-change-page="search($event, false)"
                align="center"
            ></laravel-vue-pagination>
        </template>
    </settings-page-card-layout>
</template>
