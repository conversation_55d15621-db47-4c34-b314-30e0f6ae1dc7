<script lang="ts">
import {defineComponent} from "vue";
import {Head as InertiaHead} from "@inertiajs/vue2";
import {PropType} from "vue/types/options";
import {TranslationsLoadedEvent} from "../../common/i18n";
import LangPicker from "../../common/LangPicker.vue";
import {setUpIntercom, toastError} from "../../common/util";
import {chain} from "lodash";
import VideoInterviewIntro, {InterviewProgressStatus} from "../components/VideoInterviewIntro.vue";
import VideoInterviewOutro from "../components/VideoInterviewOutro.vue";
import {show} from "@intercom/messenger-js-sdk";
import InteractiveVideoQuestion from "../components/InteractiveVideoQuestion.vue";
import VideoInterviewNavigationMenu from "../components/VideoInterviewNavigationMenu.vue";
import {B<PERSON>utton, BCard} from "bootstrap-vue";
import {reportToSentry} from "../../common/config";
import {findActiveResponse, findConfirmedResponse, LoadedInvite, LoadedQuestion, LoadedResponse} from "../util";

// prettier-ignore
type ViewState =
    | {status: "intro"}
    | {status: "viewing-question", questionId: LoadedQuestion["id"], isRecording: boolean}
    | {status: "outro"};
type ViewingQuestionState = Extract<ViewState, {status: "viewing-question"}>;

export interface QuestionNavigation {
    question: LoadedQuestion;
    isNavigable: boolean;
    isCurrent: boolean;
}

export default defineComponent({
    name: "PublicVideoInterviewView",
    components: {
        BButton,
        BCard,
        VideoInterviewNavigationMenu,
        InteractiveVideoQuestion,
        VideoInterviewOutro,
        VideoInterviewIntro,
        InertiaHead,
        LangPicker,
    },
    props: {
        initialInvite: {
            type: Object as PropType<LoadedInvite>,
            required: true,
        },
    },
    data() {
        return {
            invite: JSON.parse(JSON.stringify(this.initialInvite)) as LoadedInvite,
            state: {status: "intro"} as ViewState,
            isMobileSidebarVisible: false as boolean,
        };
    },
    computed: {
        interviewProgressStatus(): InterviewProgressStatus {
            switch (true) {
                case this.invite.interview.questions.every(
                    question => this.findConfirmedResponse(question) || this.isQuestionAttemptsExhausted(question)
                ):
                    return "all-questions-confirmed-or-exhausted";
                case this.invite.interview.questions.flatMap(question => question.responses).length > 0:
                    return "some-responses";
                default:
                    return "no-responses";
            }
        },
        isMediaRecorderSupported(): boolean {
            return !!navigator.mediaDevices && !!navigator.mediaDevices.getUserMedia;
        },
        priorityUnconfirmedQuestion(): LoadedQuestion | null {
            return (
                chain(this.invite.interview.questions)
                    .filter(question => !this.findConfirmedResponse(question))
                    .minBy([
                        (question: LoadedQuestion) => (findActiveResponse(question.responses) ? 0 : 1),
                        (question: LoadedQuestion) => question.sort_order,
                    ])
                    .value() ?? null
            );
        },
        navigationItems(): QuestionNavigation[] {
            return this.invite.interview.questions.map(question => ({
                question,
                isNavigable: this.canNavigateTo(question),
                isCurrent: this.state.status === "viewing-question" && this.state.questionId === question.id,
            }));
        },
    },
    mounted() {
        window.addEventListener("translations-loaded", this.setUpIntercom);
        this.setUpIntercom();
    },
    beforeDestroy() {
        window.removeEventListener("translations-loaded", this.setUpIntercom as EventListener);
    },
    methods: {
        getQuestion(viewState: ViewingQuestionState): LoadedQuestion {
            return this.invite.interview.questions.find(question => question.id === viewState.questionId)!;
        },
        findConfirmedResponse(question: LoadedQuestion): LoadedResponse | undefined {
            return findConfirmedResponse(question.responses);
        },
        isQuestionAttemptsExhausted(question: LoadedQuestion): boolean {
            return this.invite.interview.enforce_limits
                && !!question.max_attempts
                && question.max_attempts <= question.responses.length;
        },
        showIntercom(): void {
            show();
        },
        setUpIntercom(event?: TranslationsLoadedEvent): void {
            setUpIntercom({
                hide_default_launcher: true,
                name: this.invite.candidate.name + " (Candidate)",
                email: this.invite.candidate.email,
                ...(event ? {language_override: event.detail.lang} : {}),
            });
        },
        start(): void {
            const firstQuestionToShow = this.priorityUnconfirmedQuestion ?? this.invite.interview.questions[0];

            if (!firstQuestionToShow) {
                toastError.call(this, this.$t("There was an error with the interview. Please contact support."));
                reportToSentry("Interview has no questions!?", {
                    invite: {...this.invite},
                });
                return;
            }

            this.state = {
                status: "viewing-question",
                questionId: firstQuestionToShow.id,
                isRecording: false,
            };
        },
        handleResponseConfirmed(state: ViewingQuestionState, updatedResponse: LoadedResponse): void {
            console.log("updating response", updatedResponse, this.state);

            const question = this.getQuestion(state);
            const otherResponses = question.responses.filter(r => r.id !== updatedResponse.id);
            question.responses = [...otherResponses, updatedResponse];

            this.state = {
                ...state,
                isRecording: false,
            };
        },
        triggerNextQuestion() {
            console.log("next question:", this.priorityUnconfirmedQuestion);
            this.state = this.priorityUnconfirmedQuestion
                ? {status: "viewing-question", questionId: this.priorityUnconfirmedQuestion.id, isRecording: false}
                : {status: "outro"};
        },
        navigateTo(question: LoadedQuestion): void {
            console.log("navigating to question", question, this.state);

            if (this.canNavigateTo(question)) {
                this.state = {status: "viewing-question", questionId: question.id, isRecording: false};
                this.isMobileSidebarVisible = false;
            } else {
                throw new Error(
                    "Allowing answers in any order is not enabled, but user tried to navigate to a question that is not the current one."
                );
            }
        },
        handleRecordingStart(state: ViewingQuestionState, newResponse: LoadedResponse): void {
            console.log("handling recording start", state, newResponse);

            const question = this.invite.interview.questions.find(q => q.id === state.questionId)!;
            question.responses = [...question.responses, newResponse];

            this.state = {...state, isRecording: true};
        },
        handleRecordingStop(state: ViewingQuestionState): void {
            console.log("unlocking navigation", state);
            this.state = {...state, isRecording: false};
        },
        canNavigateTo(question: LoadedQuestion): boolean {
            const responseNotInProgress = this.state.status !== "viewing-question" || !this.state.isRecording;
            const isAnswerable =
                !!this.findConfirmedResponse(question) ||
                question.id === this.priorityUnconfirmedQuestion?.id ||
                !this.invite.interview.require_answers_in_order;

            return responseNotInProgress && isAnswerable;
        },
    },
});
</script>

<template>
    <div class="d-flex flex-column m-1 my-md-2 mx-md-6 align-items-center">
        <inertia-head :title="$t('Asynchronous video interview')" />
        <div class="d-flex mb-1 mb-md-2 align-items-center flex-grow-1 w-100">
            <b-button
                v-if="state.status !== 'intro'"
                @click="isMobileSidebarVisible = true"
                size="sm"
                variant="white"
                class="d-md-none py-2 mr-auto"
            >
                <i class="tdi td-menu mx-1 text-dark-light" />
            </b-button>

            <b-button
                @click="showIntercom()"
                size="sm"
                variant="light"
                class="py-2 ml-auto border-primary bg-white border-light text-dark-light"
            >
                {{ $t("Tech support") }}
            </b-button>
            <lang-picker
                :i18n="$i18n"
                img-width="1.5rem"
                img-classes="rounded-circle border"
                toggle-class="border-0"
            />
        </div>
        <template v-if="isMediaRecorderSupported">
            <video-interview-intro
                v-if="state.status === 'intro'"
                :interview="invite.interview"
                :candidate="invite.candidate"
                :status="interviewProgressStatus"
                @start="start()"
                style="width: fit-content"
            />
            <div
                v-if="state.status === 'outro' || state.status === 'viewing-question'"
                class="d-flex flex-column flex-md-row flex-nowrap w-100 justify-content-center"
            >
                <video-interview-navigation-menu
                    :items="navigationItems"
                    :enforce-limits="invite.interview.enforce_limits"
                    :is-sidebar-open="isMobileSidebarVisible"
                    @navigate-to="navigateTo($event)"
                    @close-sidebar="isMobileSidebarVisible = false"
                    class="mr-3 flex-grow-1 flex-shrink-1"
                    style="max-width: min(30ch, 30vw)"
                />
                <video-interview-outro
                    v-if="state.status === 'outro'"
                    class="flex-grow-1"
                    style="max-width: 768px"
                />
                <interactive-video-question
                    v-if="state.status === 'viewing-question'"
                    :question="getQuestion(state)"
                    :token="invite.token"
                    :enforce-limits="invite.interview.enforce_limits"
                    :has-next-question="interviewProgressStatus !== 'all-questions-confirmed-or-exhausted'"
                    @recording-start="handleRecordingStart(state, $event)"
                    @recording-stop="handleRecordingStop(state)"
                    @response-confirmed="handleResponseConfirmed(state, $event)"
                    @next="triggerNextQuestion()"
                    class="flex-grow-1"
                    style="max-width: 768px"
                />
            </div>
        </template>
        <b-card v-else>
            <p v-text="$t('Unfortunately, your browser is not supported.')" />
            <i18n path="Please {updateYourBrowserHere} or use another browser.">
                <template #updateYourBrowserHere>
                    <a
                        href="https://browser-update.org/update-browser.html"
                        target="_blank"
                        v-text="$t('update your browser here')"
                    />
                </template>
            </i18n>
        </b-card>
    </div>
</template>

<style lang="scss">
@use "/resources/sass/facelift/config/variables";

body {
    background-color: variables.$shark-grey;
}

.tech-support-button {
    &:hover,
    &:active,
    &:focus {
        color: white !important;
        background-color: black !important;
        border-color: black !important;
    }
}
</style>
