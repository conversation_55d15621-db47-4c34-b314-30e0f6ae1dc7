<script lang="ts">
import {defineComponent} from "vue";
import {PropType} from "vue/types/options";
import {retryPromise, toastError} from "../../common/util";
import HlsVideoPlayer from "../../common/components/HlsVideoPlayer.vue";
import {BAlert, BButton, BCard, BCollapse, BOverlay, BSpinner} from "bootstrap-vue";
import axios, {AxiosRequestConfig, AxiosResponse} from "axios";
import RecordableVideo from "./RecordableVideo.vue";
import {reportToSentry} from "../../common/config";
import VideoTroubleshootingParagraph from "./VideoTroubleshootingParagraph.vue";
import {
    findActiveResponse,
    formatWordSeconds,
    LoadedQuestion,
    LoadedResponse,
    LoadedResponseWithFile,
    VideoData,
} from "../util";
import {isNumber} from "lodash";
import CountdownRecordableVideo, {CountdownData} from "./CountdownRecordableVideo.vue";
import CountupRecordableVideo from "./CountupRecordableVideo.vue";
import QuestionLimitsInfo from "./QuestionLimitsInfo.vue";
import {languageStore} from "../../common/LangPicker.vue";

type HasResponseId = {responseId: LoadedResponse["id"]};
type HasResponseWithVideo = {response: LoadedResponseWithFile};
type HasStatus<T> = {status: T};
type StepByStatus<T extends Step["status"]> = Extract<Step, {status: T}>;
type Step =
    | HasStatus<"preparing">
    | HasStatus<"ready-to-record">
    | HasStatus<"waiting-recording-start">
    | (HasStatus<"recording"> & HasResponseId & {chunksReceived: number})
    | (HasStatus<"combining-chunks"> & HasResponseId & {chunksReceived: number})
    | (HasStatus<"deliberating"> & HasResponseWithVideo)
    | (HasStatus<"confirming"> & HasResponseWithVideo)
    | (HasStatus<"confirmed"> & HasResponseWithVideo);

type MediaStreamState =
    | (HasStatus<"acquired"> & {mediaStream: MediaStream})
    | (HasStatus<"waiting"> & {elapsedSeconds: number})
    | HasStatus<"idle">;

type RecorderInstance = InstanceType<typeof RecordableVideo | typeof CountdownRecordableVideo>;

function createNewQuestionStep(question: LoadedQuestion): StepByStatus<"preparing" | "confirmed" | "deliberating"> {
    const activeResponse = findActiveResponse(question.responses);
    const hasVideo = (response: LoadedResponse): response is LoadedResponseWithFile => !!response.video?.file;

    if (!activeResponse || !hasVideo(activeResponse)) {
        return {status: "preparing"};
    } else if (activeResponse.confirmed_at) {
        return {
            status: "confirmed",
            response: activeResponse,
        };
    } else {
        return {
            status: "deliberating",
            response: activeResponse,
        };
    }
}

export default defineComponent({
    name: "InteractiveVideoQuestion",
    components: {
        QuestionLimitsInfo,
        CountupRecordableVideo,
        CountdownRecordableVideo,
        BCollapse,
        VideoTroubleshootingParagraph,
        HlsVideoPlayer,
        BSpinner,
        BCard,
        BButton,
        BAlert,
        BOverlay,
    },
    emits: {
        /* eslint-disable @typescript-eslint/no-unused-vars*/
        "recording-start": (newResponse: LoadedResponse) => true,
        "recording-stop": () => true,
        "response-confirmed": (updatedResponse: LoadedResponse) => true,
        next: () => true,
    },
    props: {
        token: {
            type: String as PropType<string>,
            required: true,
        },
        question: {
            type: Object as PropType<LoadedQuestion>,
            required: true,
        },
        enforceLimits: {
            type: Boolean as PropType<boolean>,
            required: true,
        },
        hasNextQuestion: {
            type: Boolean as PropType<boolean>,
            required: true,
        },
    },
    data() {
        const step = createNewQuestionStep(this.question);
        const mediaStreamState =
            step.status === "deliberating" ? {status: "waiting", elapsedSeconds: 0} : {status: "idle"};

        return {
            step: step as Step,
            mediaStreamState: mediaStreamState as MediaStreamState,
            fatalError: false as boolean,
            isVideoQuestionVisible: true as boolean,
            chunkTransitPromises: {} as Record<string, undefined | Promise<void>>,
        };
    },
    mounted() {
        this.resetDataForNewQuestion(this.question);
    },
    computed: {
        showAutoSubmitWarning(): boolean {
            if (!this.question.max_attempts || !this.enforceLimits) {
                return false;
            }

            const isWaitingBeforeLastAttempt =
                this.question.max_attempts - this.question.responses.length === 1 &&
                this.is(this.step, "ready-to-record", "waiting-recording-start");
            const isLastAttemptInProgress =
                this.question.max_attempts - this.question.responses.length <= 0 &&
                this.is(this.step, "recording", "combining-chunks");

            return isLastAttemptInProgress || isWaitingBeforeLastAttempt;
        },
        isAfterLastAttempt(): boolean {
            return (
                this.enforceLimits &&
                !!this.question.max_attempts &&
                this.question.max_attempts - this.question.responses.length <= 0 &&
                this.isNot(this.step, "recording")
            );
        },
        maxTimeStr(): string | null {
            return this.question.max_response_duration
                ? formatWordSeconds(this.question.max_response_duration, languageStore.currentLanguage)
                : null;
        },
        softLimitExceeded(): boolean {
            if (!this.question.max_attempts || this.enforceLimits || !!this.activeResponse?.confirmed_at) {
                return false;
            }

            const isDuringAttempt = this.is(this.step, "deliberating", "recording", "combining-chunks");
            const attemptDiff = this.question.max_attempts - this.question.responses.length;

            return attemptDiff < 0 || (!isDuringAttempt && attemptDiff === 0);
        },
        isLoading(): boolean {
            return this.is(this.step, "combining-chunks", "confirming") || this.mediaStreamState.status === "waiting";
        },
        activeResponse(): LoadedResponse | undefined {
            return findActiveResponse(this.question.responses);
        },
        isQuestionVisible: {
            get(): boolean {
                return this.question.type === "text" || this.isVideoQuestionVisible;
            },
            set(val: boolean): void {
                this.isVideoQuestionVisible = this.question.type === "text" || val;
            },
        },
    },
    methods: {
        isNumber,
        is<T extends Step["status"]>(step: Step, ...statuses: T[]): step is StepByStatus<T> {
            return statuses.includes(step.status as T);
        },
        isNot<T extends Step["status"]>(step: Step, ...statuses: T[]): step is Exclude<Step, {status: T}> {
            return !statuses.includes(step.status as T);
        },
        handleChunk(data: Partial<CountdownData> & VideoData): void {
            console.log("received chunk", data);
            if (this.isNot(this.step, "recording", "combining-chunks")) {
                reportToSentry("handleChunk() called in wrong state", {
                    actualState: this.step.status,
                    videoData: data,
                });
                return;
            }

            const chunkNumber = this.step.chunksReceived + 1;
            if (this.is(this.step, "recording", "combining-chunks")) {
                this.step.chunksReceived = chunkNumber;
            }

            const formData = this.buildChunkFormData(data, this.step);
            const newPromise = this.uploadVideoChunk(formData, this.step);
            this.chunkTransitPromises = {
                ...this.chunkTransitPromises,
                [chunkNumber]: newPromise,
            };

            newPromise
                .catch(error => {
                    this.fatalError = true;
                    toastError.call(this, this.$t("The video recording failed unexpectedly. Please refresh the page."));
                    reportToSentry(error, {
                        devMsg: "Couldn't upload chunk due to error.",
                    });
                    return Promise.reject(error);
                })
                .finally(() => {
                    this.chunkTransitPromises = {
                        ...this.chunkTransitPromises,
                        [chunkNumber]: undefined,
                    };
                });
        },
        confirmRecording(step: Extract<Step, HasResponseWithVideo>): Promise<unknown> {
            console.log("confirming recording");
            this.step = {
                status: "confirming",
                response: step.response,
            };

            const confirmRecording = () =>
                axios.post<LoadedResponseWithFile>(
                    this.route("asyncVideoInterviews.confirmResponse", {
                        invite: this.token,
                        response: step.response,
                    })
                );

            return retryPromise(confirmRecording, 3)
                .then(response => {
                    console.log("received response from server after having saved response", response.data);
                    this.isQuestionVisible = false;
                    this.step = {
                        status: "confirmed",
                        response: response.data,
                    };
                    console.log("emitting response-confirmed", response.data);
                    this.$emit("response-confirmed", response.data);
                })
                .catch(error => {
                    this.fatalError = true;
                    toastError.call(this, this.$t("The video recording failed unexpectedly. Please refresh the page."));
                    reportToSentry(error, {
                        devMsg: "Couldn't confirm response due to error.",
                    });
                    return Promise.reject(error);
                });
        },
        handleRecorderError(value: Event & {error?: Error}): void {
            this.fatalError = true;

            reportToSentry(value.error instanceof Error ? value.error : "Failed recording video.", {
                devMsg: "Error in recorder in VideoInterviewQuestionInProgress.",
                value,
            });
        },
        startRecording(): void {
            this.step = {status: "waiting-recording-start"};

            const initializeResponse = () =>
                axios.post<LoadedResponse>(
                    this.route("asyncVideoInterviews.initializeResponse", {
                        invite: this.token,
                        question: this.question.id,
                    })
                );

            console.log("starting recording initalization process");

            this.ensureMediaStream()
                .then(() => retryPromise(initializeResponse, 3))
                .then(response => {
                    console.log("received response from server after incremenatation request");

                    const recorder = this.$refs.recorder as undefined | RecorderInstance;
                    if (!recorder) {
                        return Promise.reject("Recorder not found.");
                    }

                    this.$emit("recording-start", response.data);
                    this.step = {
                        status: "recording",
                        chunksReceived: 0,
                        responseId: response.data.id,
                    };

                    recorder.record();
                    console.log("successfully started recording");
                })
                .catch(error => {
                    this.fatalError = true;
                    toastError.call(this, this.$t("Failed to start recording. Please refresh the page."));
                    reportToSentry(error, {
                        devMsg: "Can't start recording due to error.",
                    });
                });
        },
        stopRecording(step: StepByStatus<"recording" | "combining-chunks">): void {
            const recorder = this.$refs.recorder as undefined | RecorderInstance;

            this.step = {
                status: "combining-chunks",
                responseId: step.responseId,
                chunksReceived: step.chunksReceived,
            };

            if (recorder) {
                recorder.stop();
            } else {
                reportToSentry("Recorder $ref was empty!");
            }
        },
        gotoReadyToRecord(): void {
            this.step = {status: "ready-to-record"};
            this.isQuestionVisible = false;
            this.ensureMediaStream();
        },
        ensureMediaStream(): Promise<unknown> {
            return this.mediaStreamState.status === "acquired" ? Promise.resolve() : this.acquireMediaStream();
        },
        acquireMediaStream(): Promise<unknown> {
            this.mediaStreamState = {status: "waiting", elapsedSeconds: 0};

            const intervalId = window.setInterval(() => {
                if (this.mediaStreamState.status === "waiting") {
                    this.mediaStreamState.elapsedSeconds += 1;
                } else {
                    window.clearInterval(intervalId);
                }
            }, 1000);

            return navigator.mediaDevices
                .getUserMedia({video: true, audio: true})
                .then(mediaStream => {
                    console.log("acquired media stream", mediaStream);
                    if (this.is(this.step, "ready-to-record")) {
                        this.isQuestionVisible = false;
                    }
                    this.mediaStreamState = {status: "acquired", mediaStream};
                    return mediaStream;
                })
                .catch(error => {
                    this.fatalError = true;
                    toastError.call(this, this.$t("Failed to start recording. Please refresh the page."));
                    reportToSentry(error, {
                        devMsg: "Error accessing media devices.",
                    });
                    return Promise.reject(error);
                });
        },
        waitForFinalChunks(): Promise<void> {
            const pipeline: () => Promise<void> = () => {
                const promises: Array<Promise<string>> = Object.entries(this.chunkTransitPromises)
                    .filter((entry): entry is [string, Promise<void>] => entry[1] instanceof Promise)
                    .map(([chunkNumber, promise]) => promise.then(() => chunkNumber));

                return Promise.all(promises)
                    .then(chunkNumbers =>
                        chunkNumbers.forEach(chunkOrder => {
                            this.chunkTransitPromises = {
                                ...this.chunkTransitPromises,
                                [chunkOrder]: undefined,
                            };
                        })
                    )
                    .then(() => {
                        if (Object.values(this.chunkTransitPromises).some(promise => promise instanceof Promise)) {
                            return pipeline();
                        }
                    });
            };

            return pipeline();
        },
        handleFinishedRecording(data?: CountdownData): void {
            console.log("finishing recording", data, {...this.step});

            if (this.isNot(this.step, "recording", "combining-chunks")) {
                reportToSentry("promise callback in handleFinishedRecording() called in wrong state", {
                    actualState: {...this.step},
                    videoData: data,
                });
                return;
            }

            const responseId = this.step.responseId;
            this.step = {
                status: "combining-chunks",
                responseId,
                chunksReceived: this.step.chunksReceived,
            };

            if (data?.ranOutOfTime) {
                this.$bvToast.toast(this.$t("Recording stopped automatically after time ran out.").toString(), {
                    autoHideDelay: 2000,
                    variant: "warning",
                });
            }

            const mergeChunks = () =>
                axios.post<LoadedResponseWithFile>(
                    this.route("asyncVideoInterviews.mergeChunks", {
                        invite: this.token,
                        response: responseId,
                    }),
                    {ran_out_of_time: !!data?.ranOutOfTime}
                );

            this.waitForFinalChunks()
                .then(() => {
                    if (this.isNot(this.step, "recording", "combining-chunks") || this.step.chunksReceived <= 0) {
                        return Promise.reject("No chunks received or already in wrong state");
                    }
                })
                .then(() => retryPromise(mergeChunks, 5))
                .then(axiosResponse => {
                    console.log("received response from server after merging chunks", {
                        ...this.step,
                    });
                    this.step = {status: "deliberating", response: axiosResponse.data};
                    if (
                        this.enforceLimits &&
                        isNumber(this.question.max_attempts) &&
                        this.question.max_attempts - this.question.responses.length === 0
                    ) {
                        console.log("this was the final attempt, so triggering confirmation of recording");
                        return this.confirmRecording(this.step);
                    }
                })
                .catch(error => {
                    toastError.call(this, this.$t("Failed to upload recording. Please refresh the page."));
                    reportToSentry("Error while uploading video", {error, step: this.step});
                    this.fatalError = true;
                })
                .finally(() => {
                    this.$emit("recording-stop");
                });
        },
        buildChunkFormData(
            {blob, extension}: VideoData,
            {responseId, chunksReceived}: StepByStatus<"recording" | "combining-chunks">
        ): FormData {
            const file = new File([blob], `response_${responseId}_part_${chunksReceived}`);
            const formData = new FormData();
            formData.append("chunk", file);
            if (extension) {
                formData.append("extension", extension);
            }
            formData.append("order", chunksReceived.toString());

            return formData;
        },
        uploadVideoChunk(
            data: FormData,
            {responseId}: StepByStatus<"recording" | "combining-chunks">
        ): Promise<AxiosResponse<void>> {
            const url = this.route("asyncVideoInterviews.uploadChunk", {invite: this.token, response: responseId});
            const axiosConfig: AxiosRequestConfig<FormData> = {
                transformRequest: [
                    (data, headers) => {
                        delete headers["X-Requested-With"];
                        return data;
                    },
                ],
            };

            return retryPromise(() => axios.post<void>(url, data, axiosConfig), 5);
        },
        resetDataForNewQuestion(question: LoadedQuestion): void {
            this.step = createNewQuestionStep(question);
            this.fatalError = false;
            this.isQuestionVisible = this.is(this.step, "preparing");
            this.chunkTransitPromises = {};

            if (this.is(this.step, "deliberating")) {
                this.ensureMediaStream();
            } else {
                this.mediaStreamState = {status: "idle"};
            }
        },
    },
    watch: {
        question(incoming: LoadedQuestion, outgoing: LoadedQuestion): void {
            if (incoming.id !== outgoing.id) {
                this.resetDataForNewQuestion(incoming);
            }
        },
    },
});
</script>

<template>
    <div>
        <b-overlay
            :show="fatalError"
            no-wrap
            class="position-fixed"
        >
            <template #overlay>
                <div class="d-flex flex-column align-items-center">
                    <video-troubleshooting-paragraph
                        :title="$t('Oops! Something went wrong.').toString()"
                        variant="error"
                    />
                    <b-spinner />
                </div>
            </template>
        </b-overlay>
        <b-overlay
            :show="mediaStreamState.status === 'waiting' && mediaStreamState.elapsedSeconds > 5"
            no-wrap
            class="position-fixed"
        >
            <template #overlay>
                <div class="d-flex flex-column align-items-center">
                    <video-troubleshooting-paragraph
                        :title="$t('Please allow access to your camera and microphone.').toString()"
                        variant="warning"
                    />
                    <b-spinner />
                </div>
            </template>
        </b-overlay>
        <b-card body-class="d-flex flex-column align-items-start p-md-4 w-100 gap-2">
            <h1
                class="text-dark-light h2"
                v-text="$t('Question {no}', {no: question.sort_order})"
            />
            <question-limits-info
                :question="question"
                :enforce-limits="enforceLimits"
                :in-progress="is(step, 'deliberating', 'recording', 'combining-chunks')"
                class="mb-2 w-100"
            />
            <b-alert
                :show="softLimitExceeded"
                :class="$style['alert-spacing']"
                variant="warning"
            >
                {{ $t("You have reached the recommended attempt limit, but you can still submit responses.") }}
            </b-alert>
            <b-button
                variant="white"
                v-if="
                    question.type === 'video' &&
                    (mediaStreamState.status === 'acquired' || is(step, 'confirmed', 'deliberating'))
                "
                @click="isQuestionVisible = !isQuestionVisible"
                class="mb-2 align-self-center"
            >
                <i
                    class="tdi"
                    :class="isQuestionVisible ? 'td-chevron-down' : 'td-chevron-right'"
                    style="font-size: 1.2em"
                />
                <span v-text="isQuestionVisible ? $t('Hide question') : $t('Show question')" />
            </b-button>
            <b-collapse
                v-model="isQuestionVisible"
                class="w-100"
            >
                <div class="d-flex flex-column align-items-start mt-2">
                    <div
                        v-if="question.type === 'text'"
                        :class="$style['text-question']"
                        class="text-question-html text-md mb-2"
                        v-html="question.contents"
                    />
                    <hls-video-player
                        v-else
                        class="w-100 mb-3 rounded-lg"
                        :hls-src="question.video.hls_playlist?.url"
                        :fallback-src="question.video.file.url"
                    />
                </div>
            </b-collapse>

            <div class="w-100">
                <hls-video-player
                    v-if="is(step, 'deliberating', 'confirming', 'confirmed')"
                    class="w-100 mb-2 rounded-lg"
                    :hls-src="step.response.video.hls_playlist?.url"
                    :fallback-src="step.response.video.file.url"
                />
                <template v-else-if="isNot(step, 'preparing') && mediaStreamState.status === 'acquired'">
                    <countdown-recordable-video
                        v-if="question.max_response_duration"
                        ref="recorder"
                        video-class="w-100 mb-2 rounded-lg"
                        :media-stream="mediaStreamState.mediaStream"
                        :enforce-time-limit="enforceLimits"
                        @stop="handleFinishedRecording($event)"
                        @chunk="handleChunk($event)"
                        @error="handleRecorderError($event)"
                        :initial-seconds="question.max_response_duration"
                        class="w-100 mb-2"
                    />
                    <countup-recordable-video
                        v-else
                        ref="recorder"
                        video-class="w-100 mb-2 rounded-lg"
                        :media-stream="mediaStreamState.mediaStream"
                        @stop="handleFinishedRecording($event)"
                        @chunk="handleChunk($event)"
                        @error="handleRecorderError($event)"
                        class="w-100 mb-2"
                    />
                </template>
            </div>

            <div class="d-flex flex-column align-items-start w-100 mb-2">
                <b-alert
                    :show="
                        is(step, 'confirming', 'waiting-recording-start', 'combining-chunks') ||
                        mediaStreamState.status === 'waiting'
                    "
                    variant="light"
                    class="d-flex flex-row flex-nowrap align-items-center bg-lighter text-dark-light"
                    :class="$style['alert-spacing']"
                >
                    <p
                        v-if="is(step, 'confirming', 'combining-chunks')"
                        class="mb-0"
                        v-text="$t('Your response is uploading. Please wait until it is finished.')"
                    />
                    <p
                        v-if="is(step, 'waiting-recording-start')"
                        class="mb-0"
                        v-text="$t('Waiting for recording to start.')"
                    />
                    <p
                        v-if="mediaStreamState.status === 'waiting'"
                        class="mb-0"
                        v-text="$t('Waiting for camera and microphone access.')"
                    />
                    <div>
                        <b-spinner
                            small
                            class="ml-3"
                        />
                    </div>
                </b-alert>
                <b-alert
                    :show="isAfterLastAttempt && !activeResponse?.confirmed_at"
                    variant="light"
                    :class="$style['alert-spacing']"
                >
                    <div class="d-flex align-items-center">
                        <i
                            class="fas fa-exclamation-triangle mr-3"
                            style="font-size: 1.2em"
                        />
                        <span
                            v-text="
                                $t('You have exhausted your attempt limit. You can no longer answer this question.')
                            "
                        />
                        <button
                            v-if="activeResponse"
                            class="btn btn-primary ml-auto text-nowrap"
                            @click="confirmRecording(step)"
                        >
                            {{ $t("Confirm current response") }}
                        </button>
                    </div>
                </b-alert>
                <b-alert
                    :show="is(step, 'confirmed')"
                    variant="success"
                    :class="$style['alert-spacing']"
                >
                    <div class="d-flex align-items-center">
                        <i
                            class="tdi td-check mr-3"
                            style="font-size: 1.5em"
                        />
                        <div>
                            <p
                                class="mb-0"
                                v-text="$t('answer_saved')"
                            />
                            <p class="mb-0">
                                <span
                                    v-if="hasNextQuestion"
                                    v-text="$t('You can now proceed to the next question.')"
                                />
                                <span
                                    v-else
                                    v-text="$t('You have successfully finished the interview.')"
                                />
                            </p>
                        </div>
                    </div>
                </b-alert>
                <b-alert
                    :show="is(step, 'deliberating') && !isAfterLastAttempt"
                    :class="$style['alert-spacing']"
                    variant="light"
                    class="bg-lighter text-dark-light"
                >
                    <div class="d-flex align-items-center">
                        <i
                            class="tdi td-lg td-alert-circle"
                            :class="$style['icon-align']"
                        />
                        <span v-text="$t('warning_not_sent')" />
                    </div>
                </b-alert>
            </div>

            <b-button
                v-if="is(step, 'confirmed')"
                @click="$emit('next')"
                variant="success"
                :class="$style['button-positioning']"
            >
                <span
                    v-if="hasNextQuestion"
                    v-text="$t('Next question')"
                />
                <span
                    v-else
                    v-text="$t('Finish interview')"
                />
            </b-button>
            <b-button
                v-if="is(step, 'preparing')"
                variant="success"
                @click="gotoReadyToRecord()"
                :class="$style['button-positioning']"
            >
                {{ $t("Ready to answer") }}
            </b-button>

            <template v-if="!isAfterLastAttempt">
                <b-button
                    v-if="is(step, 'recording')"
                    variant="success"
                    class="align-self-center"
                    @click="stopRecording(step)"
                >
                    {{ $t("Stop recording") }}
                </b-button>
                <div
                    v-if="is(step, 'deliberating', 'confirming')"
                    class="d-flex flex-row justify-content-end gap-2 w-100"
                >
                    <b-button
                        @click="gotoReadyToRecord()"
                        variant="white"
                        :disabled="is(step, 'confirming')"
                    >
                        {{ $t("Try again") }}
                    </b-button>
                    <b-button
                        @click="confirmRecording(step)"
                        variant="success"
                        :disabled="is(step, 'confirming')"
                        >{{ $t("Submit answer") }}
                    </b-button>
                </div>
                <template
                    v-if="
                        mediaStreamState.status === 'acquired' && is(step, 'ready-to-record', 'waiting-recording-start')
                    "
                >
                    <b-button
                        @click="startRecording()"
                        variant="success"
                        :disabled="is(step, 'waiting-recording-start')"
                        class="align-self-center"
                    >
                        {{ $t("Start recording") }}
                    </b-button>
                </template>
            </template>
        </b-card>
    </div>
</template>

<style lang="scss" module>
@use "/resources/sass/facelift/config/variables";
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins";

.alert-spacing {
    margin-bottom: 0.5rem;
    width: 100%;
}

.icon-align {
    margin-top: 0.15rem;
    margin-right: 0.4rem;
}

.button-positioning {
    align-self: end;
}

.text-question {
    //border-left: 4px solid variables.$dark-light;
    //padding-left: 1ch;
}
</style>

<style lang="scss">
.text-question-html {
    img {
        max-width: 100%;
        max-height: min-content;
    }

    pre {
        white-space: break-spaces;
    }
}
</style>
