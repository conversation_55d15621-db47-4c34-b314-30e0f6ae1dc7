<script lang="ts">
import {defineComponent} from "vue";
import {PropType} from "vue/types/options";
import {formatClockSeconds, VideoData} from "../util";
import RecordableVideo from "./RecordableVideo.vue";
import {<PERSON><PERSON><PERSON>, BBadge} from "bootstrap-vue";
import {isNumber} from "lodash";

export interface CountdownData {
    ranOutOfTime: boolean;
}

export default defineComponent({
    name: "CountdownRecordableVideo",
    components: {
        RecordableVideo,
        BBadge,
        BAlert,
    },
    emits: {
        /* eslint-disable @typescript-eslint/no-unused-vars*/
        stop: (data: CountdownData) => true,
        start: () => true,
        chunk: (data: VideoData & CountdownData) => true,
        error: (event: Event) => true,
    },
    props: {
        mediaStream: {
            type: MediaStream as PropType<MediaStream>,
            required: true,
        },
        initialSeconds: {
            type: Number as PropType<number>,
            required: true,
        },
        videoClass: {
            type: [String, Array, Object] as PropType<undefined | VueClassBinding>,
        },
        enforceTimeLimit: {
            type: Boolean as PropType<boolean>,
        },
    },
    data() {
        return {
            secondsRemaining: this.initialSeconds,
            countdownIntervalId: undefined as undefined | number,
        };
    },
    computed: {
        countdownData(): CountdownData {
            return {
                ranOutOfTime: this.secondsRemaining <= 0,
            };
        },
        recorder(): undefined | InstanceType<typeof RecordableVideo> {
            return this.$refs.recorder as undefined | InstanceType<typeof RecordableVideo>;
        },
        timeStr(): string {
            const sign = this.secondsRemaining < 0 ? '-' : '';
            const value = formatClockSeconds(Math.abs(this.secondsRemaining));
            return `${sign}${value}`;
        },
        remainingTimeAttrs(): {
            variant: 'white' | 'danger' | 'warning';
            class: VueClassBinding;
        } {
            if (this.secondsRemaining > 10) {
                return {
                    variant: 'white',
                    class: 'text-black',
                };
            }

            if (!this.enforceTimeLimit && this.secondsRemaining > 0) {
                return {
                    variant: 'warning',
                    class: 'text-white',
                };
            }

            return {
                variant: 'danger',
                class: 'text-white',
            };
        },
    },
    methods: {
        isNumber,
        buildData(videoData: VideoData): CountdownData & VideoData {
            return {
                ...videoData,
                ...this.countdownData,
            };
        },
        record(): void {
            if (this.recorder) {
                this.recorder.record();
            }
        },
        stop(): void {
            this.clearCountdown();

            if (this.recorder) {
                this.recorder.stop();
            }
        },
        handleStart(): void {
            this.startCountdown();
            this.$emit("start");
        },
        handleStop(): void {
            this.clearCountdown();
            this.$emit("stop", this.countdownData);
        },
        handleChunk(data: VideoData): void {
            this.$emit("chunk", this.buildData(data));
        },
        handleError(event: Event): void {
            this.stop();
            this.$emit("error", event);
        },
        startCountdown(): void {
            this.secondsRemaining = this.initialSeconds;
            this.countdownIntervalId = window.setInterval(() => {
                this.secondsRemaining -= 1;

                if (this.enforceTimeLimit && this.secondsRemaining <= 0) {
                    this.stop();
                }
            }, 1000);
        },
        clearCountdown(): void {
            window.clearInterval(this.countdownIntervalId);
            this.countdownIntervalId = undefined;
        },
    },
    beforeDestroy() {
        this.clearCountdown();
    },
});
</script>

<template>
    <div class="d-flex flex-column align-items-center">
        <recordable-video
            ref="recorder"
            :media-stream="mediaStream"
            @start="handleStart()"
            :video-class="videoClass"
            @error="$emit('error', $event)"
            @stop="handleStop()"
            @chunk="handleChunk($event)"
        />
        <b-badge
            v-if="countdownIntervalId"
            :variant="remainingTimeAttrs.variant"
            class="m-1 mb-2"
            :active-class="remainingTimeAttrs.class"
            style="font-size: 1.5rem; width: 7ch;"
        >
            {{ timeStr }}
        </b-badge>
        <b-alert
            :show="secondsRemaining < 0"
            variant="danger"
        >
            {{ $t('Time’s up! Try to finish soon.') }}
        </b-alert>
    </div>
</template>

<style scoped></style>
