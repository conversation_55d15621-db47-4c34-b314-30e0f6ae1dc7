export default {
    elements: {
        date: {
            format: 'Y-m-d',
            dataFormat: 'YYYY-MM-DD',
        },
        time: {
            format: 'H:i',
            dataFormat: 'HH:mm',
            secondsFormat: 'H:i:S',
            secondsDataFormat: 'HH:mm:ss',
        },
        datetime: {
            format: 'Y-m-d H:i',
            dataFormat: 'YYYY-MM-DD HH:mm',
            secondsFormat: 'Y-m-d H:i:S',
            secondsDataFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        address: {
            addressLabel: 'Aadress',
            address2Label: 'Aadress 2',
            zipLabel: 'Zip',
            cityLabel: 'Linn',
            stateLabel: 'Maakond',
            countryLabel: 'Riik'
        },
        list: {
            add: '+ Lisa',
            remove: '&times;',
        },
        multiselect: {
            selection: ':options valitud',
            maxElements: 'Maksimaalne arv valitud.',
            noResult: 'Valikuid ei leitud.',
            noOptions: 'Valikud puuduvad.',
            createLabel: 'Uue valiku lisamiseks vajuta Enter',
        },
        file: {
            defaultName: 'file',
            uploadButton: 'Lae fail üles',
            dndTitle: 'Lae fail üles',
            dndDescription: 'Tiri fail siia või klõpsa, et üles laadida',
        },
        image: {
            defaultName: 'image',
            uploadButton: 'Lae pilt üles',
            dndTitle: 'Lae pilt üles',
            dndDescription: 'Tiri pilt siia või klõpsa, et üles laadida',
        },
        multifile: {
            uploadButton: 'Lae failid üles',
            dndTitle: 'Lae failid üles',
            dndDescription: 'Tiri failid siia või klõpsa, et üles laadida',
        },
        gallery: {
            uploadButton: 'Lae pildid üles',
            dndTitle: 'Lae pildid üles',
            dndDescription: 'Tiri pildid siia või klõpsa, et üles laadida',
        },
    },
    wizard: {
        finish: 'Lõpeta',
        next: 'Edasi',
        previous: 'Tagasi',
    },
    trix: {
        acceptedMimes: 'Aktsepteeritud failitüübid on: :mimes',
        acceptedExtensions: 'Aktsepteeritud laiendused on: :extensions'
    },
    messages: {
        accepted: ':attribute tuleb aktsepteerida.',
        active_url: ':attribute peab olema kehtiv URL.',
        after: ':attribute peab olema kuupäev pärast :date.',
        after_or_equal: ':attribute peab olema kuupäev pärast :date või sellega võrdne.',
        alpha: ':attribute võib sisaldada ainult tähti.',
        alpha_dash: ':attribute võib sisaldada ainult tähti, numbreid, sidekriipse ja alakriipse.',
        alpha_num: ':attribute võib sisaldada ainult tähti ja numbreid.',
        array: ':attribute peab olema nimekiri.',
        before: ':attribute peab olema kuupäev enne :date.',
        before_or_equal: ':attribute peab olema kuupäev, mis on enne :date või sellega võrdne.',
        between: {
            numeric: ':attribute peab olema vahemikus :min kuni :max.',
            file: ':attribute peab olema vahemikus :min kuni :max kilobaiti.',
            string: ':attribute pikkus peab jääma :min ja :max tähemärgi vahele.',
            array: 'Välja :attribute üksuste arv peab olema vahemikus :min kuni :max.'
        },
        boolean: ':attribute peab olema tõene või väär.',
        confirmed: 'Välja :attribute kinnitus ei ühti.',
        date: ':attribute ei ole kehtiv kuupäev.',
        date_format: ':attribute ei vasta vormingule :format.',
        date_equals: ':attribute peab olema võrdne väärtusega :date.',
        different: ':attribute ja :other peavad olema erinevad.',
        digits: ':attribute peab olema :digits numbri pikkune.',
        digits_between: ':attribute pikkus peab jääma :min ja :max numbri vahele.',
        dimensions: 'Väljal :attribute on sobimatud pildi mõõtmed.',
        distinct: 'Väljal :attribute on duplikaatväärtus.',
        email: ':attribute peab olema kehtiv e-posti aadress.',
        exists: 'Valitud :attribute on kehtetu.',
        file: 'Väljal :attribute peab olema fail.',
        filled: 'Väljal :attribute peab olema väärtus.',
        gt: {
            numeric: ':attribute peab olema suurem kui :value.',
            file: ':attribute peab olema suurem kui :value kilobaiti.',
            string: ':attribute peab olema pikem kui :value tähemärki.',
            array: 'Väljal :attribute peab olema rohkem kui :value üksust.'
        },
        gte: {
            numeric: ':attribute peab olema suurem kui :value või sellega võrdne.',
            file: ':attribute peab olema suurem kui :value kilobaiti või sellega võrdne.',
            string: ':attribute peab olema vähemalt :value tähemärki.',
            array: 'Väljal :attribute peab olema :value või rohkem üksust.'
        },
        image: ':attribute peab olema pilt.',
        in: 'Valitud :attribute on kehtetu.',
        in_array: 'Välja :attribute ei eksisteeri väljal :other.',
        integer: ':attribute peab olema täisarv.',
        ip: ':attribute peab olema kehtiv IP-aadress.',
        ipv4: ':attribute peab olema kehtiv IPv4-aadress.',
        ipv6: ':attribute peab olema kehtiv IPv6-aadress.',
        json: ':attribute peab olema kehtiv JSON-string.',
        lt: {
            numeric: ':attribute peab olema väiksem kui :value.',
            file: ':attribute peab olema väiksem kui :value kilobaiti.',
            string: ':attribute peab olema lühem kui :value tähemärki.',
            array: 'Väljal :attribute ei tohi olla rohkem kui :value üksust.'
        },
        lte: {
            numeric: ':attribute peab olema väiksem kui :value või sellega võrdne.',
            file: ':attribute peab olema väiksem kui :value kilobaiti või sellega võrdne.',
            string: ':attribute ei tohi olla pikem kui :value tähemärki.',
            array: 'Väljal :attribute ei tohi olla rohkem kui :value üksust.'
        },
        max: {
            numeric: ':attribute ei tohi olla suurem kui :max.',
            file: ':attribute ei tohi olla suurem kui :max kilobaiti.',
            string: ':attribute ei tohi olla pikem kui :max tähemärki.',
            array: 'Väljal :attribute ei tohi olla rohkem kui :max üksust.'
        },
        mimes: ':attribute peab olema faili tüüpi: :values.',
        mimetypes: ':attribute peab olema faili tüüpi: :values.',
        min: {
            numeric: ':attribute ei tohi olla väiksem kui :min.',
            file: ':attribute peab olema vähemalt :min kilobaiti.',
            string: ':attribute peab olema vähemalt :min tähemärgi pikkune.',
            array: 'Väljal :attribute peab olema vähemalt :min üksust.'
        },
        not_in: 'Valitud :attribute on kehtetu.',
        not_regex: ':attribute vorming on kehtetu.',
        numeric: ':attribute peab olema arv.',
        present: ':attribute peab olema täidetud.',
        regex: ':attribute vorming on kehtetu.',
        required: ':attribute on kohustuslik.',
        required_if: ':attribute on kohustuslik, kui :other on :value.',
        required_unless: ':attribute on kohustuslik, välja arvatud juhul, kui :other on jaotises :values.',
        required_with: ':attribute on kohustuslik, kui :values on olemas.',
        required_with_all: ':attribute on kohustuslik, kui :values on olemas.',
        required_without: ':attribute on kohustuslik, kui :values on puudu.',
        required_without_all: ':attribute on kohustuslik, kui ükski :values ei ole täidetud.',
        same: ':attribute ja :other peavad ühtima.',
        size: {
            numeric: ':attribute peab olema :size.',
            file: ':attribute peab olema :size kilobaiti.',
            string: ':attribute peab olema :size tähemärki.',
            array: 'Väli :attribute peab sisaldama :size üksust.'
        },
        string: ':attribute peab olema tekst.',
        timezone: ':attribute peab olema kehtiv ajavöönd.',
        unique: ':attribute on juba võetud.',
        uploaded: ':attribute üleslaadimine ebaõnnestus.',
        url: ':attribute vorming on kehtetu.',
        uuid: ':attribute peab olema kehtiv UUID.',
        remote: ':attribute on kehtetu.'
    }
}
