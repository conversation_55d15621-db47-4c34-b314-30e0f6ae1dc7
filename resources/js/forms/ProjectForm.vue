<script lang="ts">
import {defineComponent} from "vue";
import Laraform from "../themes/teamdash_facelift/components/Laraform.vue";
import {BTab, BTabs} from "bootstrap-vue";
import {ACTION_ICONS} from "../constants/action_types";
import {getSummaryForAction} from "../actions/utils";
import StageCategoryPill from "../common/components/StageCategoryPill.vue";
import {PropType} from "vue/types/options";
import {chain} from "lodash";
import {DateTime} from "luxon";
import {AllSettings} from "../@types/rl-settings";

export type PrefillableProjectData = Partial<
    Overwrite<
        Project,
        {
            users: Pick<User, "id">[];
            scorecards: Pick<Scorecard, "id">[];
            locations: Pick<Location, "id">[];
            requisition: LoadedRequisition;
        }
    >
>;

// Check \App\Forms\ProjectFormGrouped to see the form schema.
// Don't put undefined as a value, Lara<PERSON> will convert it as string "unknown" 🤢
type ProjectFormData = Partial<{
    id: NeverUndefined<Project["id"]>;
    is_template: NonNullable<Project["is_template"]>;
    requisition_id: NeverUndefined<Requisition["id"]>;
    template_id: NeverUndefined<Project["template_id"]>;
    meta_1: Partial<{
        position_name: NeverUndefined<Project["position_name"]>;
        display_name: NeverUndefined<Project["display_name"]>;
        description: NeverUndefined<Project["description"]>;
    }>;
    meta_2: Partial<{
        status_group: Partial<{
            status: NeverUndefined<Project["status"]>;
            project_failure_reason_id: NeverUndefined<ProjectFailureReason["id"]>;
        }>;
        project_dates: Partial<{
            start_date: NeverUndefined<Project["start_date"]>;
            deadline_at: NeverUndefined<Project["deadline_at"]>;
        }>;
        end_date: NeverUndefined<Project["end_date"]>;
        warranty_until: NeverUndefined<Project["warranty_until"]>;
        is_perpetual: NonNullable<Project["is_perpetual"]>;
        perpetual_gdpr_consent_length: NeverUndefined<Project["perpetual_gdpr_consent_length"]>;
    }>;
    member_group: Partial<{
        team_id: NeverUndefined<Project["team_id"]>;
        project_manager_id: NeverUndefined<Project["project_manager_id"]>;
        users: Array<NeverUndefined<User[""]>>;
        accessible_only_members: NonNullable<Project["accessible_only_members"]>;
    }>;
    client_group: Partial<{
        crm_organization_id: NeverUndefined<Project["crm_organization_id"]>;
        crm_office_id: NeverUndefined<Project["crm_office_id"]>;
    }>;
    meta_3: Partial<{
        scorecards: Array<NeverUndefined<Scorecard["id"]>>;
        locations: Array<NeverUndefined<Location["id"]>>;
    }>;
    custom_fields: NeverUndefined<Project["custom_fields"]>;
    stages: NeverUndefined<Project["stages"]>;
}>;

// The fields are divided into groups to support the more complex front-end layout for better UX.
export const buildPrefillDataFromExistingProject = (project: Project): ProjectFormData => ({
    id: project.id,
    is_template: project.is_template,
    template_id: project.template_id,
    meta_1: {
        position_name: project.position_name,
        display_name: project.display_name,
        description: project.description,
    },
    meta_2: {
        status_group: {
            status: project.status,
            project_failure_reason_id: project.project_failure_reason_id,
        },
        project_dates: {
            start_date: project.start_date,
            deadline_at: project.deadline_at,
        },
        end_date: project.end_date,
        warranty_until: project.warranty_until,
        is_perpetual: project.is_perpetual,
        perpetual_gdpr_consent_length: project.perpetual_gdpr_consent_length,
    },
    member_group: {
        team_id: project.team_id,
        project_manager_id: project.project_manager_id,
        users: project.users?.map(user => user.id) ?? [],
        accessible_only_members: !!project.accessible_only_members,
    },
    client_group: {
        crm_organization_id: project.crm_organization_id,
        crm_office_id: project.crm_office_id,
    },
    meta_3: {
        scorecards: project.scorecards?.map(scorecard => scorecard.id) ?? [],
        locations: project.locations?.map(location => location.id) ?? [],
    },
    custom_fields: project.custom_fields,
    stages: project.stages,
});

// The fields are divided into groups to support the more complex front-end layout for better UX.
export const buildProjectPrefillFromTemplate = (template: Project, settings: AllSettings): ProjectFormData => {
    // when the template has ping_user actions, we need to add the users to the project
    const requiredUserIds = template.stages
        .flatMap(stage => stage.actions)
        .filter(action => action.action_type === "ping_user")
        .flatMap(action => action.data.user_ids ?? [])
        .map(userId => parseInt(userId + ""));

    return {
        is_template: false,
        template_id: template.id,
        meta_1: {
            position_name: template.position_name,
            display_name: template.display_name,
            description: template.description,
        },
        meta_2: {
            status_group: {
                status: template.status,
            },
            project_dates: {
                start_date: DateTime.now().toISODate(),
                deadline_at: template.deadline_at,
            },
            end_date: template.end_date,
            warranty_until: template.warranty_until,
            is_perpetual: template.is_perpetual,
            perpetual_gdpr_consent_length: template.perpetual_gdpr_consent_length,
        },
        member_group: {
            team_id: template.team_id,
            project_manager_id: settings?.user?.id ?? template.project_manager_id,
            users: requiredUserIds,
            accessible_only_members: settings.projects_private_by_default,
        },
        client_group: {
            crm_organization_id: template.crm_organization_id,
            crm_office_id: template.crm_office_id,
        },
        meta_3: {
            scorecards: template.scorecards?.map(scorecard => scorecard.id) ?? [],
            locations: template.locations?.map(location => location.id) ?? [],
        },
        custom_fields: template.custom_fields,
        stages: template.stages,
    };
};

// The fields are divided into groups to support the more complex front-end layout for better UX.
export const buildProjectPrefillFromRequisition = (requisition: Requisition): ProjectFormData => ({
    requisition_id: requisition?.id ?? null,
    is_template: false,
    meta_1: {
        position_name: requisition.display_position_name ?? "",
        display_name: requisition.display_position_name ?? "",
    },
    meta_2: {
        project_dates: {
            start_date: DateTime.now().toISODate(),
        },
    },
    member_group: {
        project_manager_id: requisition.recruiter_id,
        users: chain(requisition.approvals)
            .map(approval => approval.user)
            .push(requisition.author)
            .map(user => user.id)
            .uniq()
            .value(),
    },
});

export default defineComponent({
    name: "ProjectForm",
    methods: {getSummaryForAction},
    components: {StageCategoryPill, BTab, BTabs},
    mixins: [Laraform],
    props: {
        tabClasses: {
            type: String as PropType<string>,
            required: true,
        },
        tabNavClasses: {
            type: String as PropType<string>,
            required: true,
        },
        tabContentClasses: {
            type: String as PropType<string>,
            required: true,
        },
        selectedTemplateItem: {
            type: Object as () => Project | null,
            default: null,
        },
    },
    computed: {
        ACTION_ICONS() {
            return ACTION_ICONS;
        },
        showUncategorizedStagesWarning(): boolean {
            if (!this.data || !this.data.stages) {
                return false;
            }

            if (this.$rlSettings.use_custom_stage_categories) {
                return this.data.stages.some((stage: any) => !stage.custom_stage_category_id);
            }
            return this.data.stages.some((stage: any) => !stage.category);
        },
        isTemplate(): boolean {
            if (!this.data || !this.data.is_template) {
                return false;
            }

            return this.data.is_template;
        },
    },
});
</script>

<template>
    <form
        class="laraform-bs4"
        ref="bform"
        @submit.prevent="submit"
    >
        <b-tabs
            :class="tabClasses"
            :nav-class="['nav nav-tabs form-tabs card-tabs', tabNavClasses]"
            :content-class="tabContentClasses"
        >
            <b-tab :title="$t('Basics')">
                <div
                    class="row"
                    v-if="!schema.stages"
                >
                    <div class="col">
                        <div class="alert alert-warning">
                            <i class="tdi td-info-circle mr-1"></i>
                            {{
                                $t("Stages and actions will be automatically created based on the template you select.")
                            }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <KeyElement
                        :schema="schema.id"
                        name="id"
                        v-ref:elements$
                    />
                    <MetaElement
                        :schema="schema.is_template"
                        name="is_template"
                        v-ref:elements$
                    />
                    <MetaElement
                        :schema="schema.requisition_id"
                        name="requisition_id"
                        v-ref:elements$
                    />
                    <MetaElement
                        :schema="schema.template_id"
                        name="template_id"
                        v-ref:elements$
                        v-if="schema.template_id"
                    />
                    <div class="col-lg-7">
                        <div class="row">
                            <GroupElement
                                :schema="schema.meta_1"
                                name="meta_1"
                                v-ref:elements$
                            />
                            <GroupElement
                                :schema="schema.meta_3"
                                name="meta_3"
                                v-ref:elements$
                            />
                            <ObjectElement
                                :schema="schema.custom_fields"
                                name="custom_fields"
                                v-ref:elements$
                            />
                        </div>
                    </div>
                    <div class="col-lg-5 d-flex flex-column gap-3">
                        <div class="card shadow-sm">
                            <div class="card-body px-3 pb-1">
                                <div class="row">
                                    <GroupElement
                                        :schema="schema.meta_2"
                                        name="meta_2"
                                        v-ref:elements$
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="card shadow-sm">
                            <div class="card-body px-3 pb-1">
                                <div class="row">
                                    <GroupElement
                                        :schema="schema.member_group"
                                        name="member_group"
                                        v-ref:elements$
                                    />
                                </div>
                            </div>
                        </div>
                        <div
                            class="card shadow-sm"
                            v-if="schema.client_group"
                        >
                            <div class="card-body px-3 pb-1">
                                <div class="row">
                                    <GroupElement
                                        :schema="schema.client_group"
                                        name="client_group"
                                        v-ref:elements$
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </b-tab>
            <b-tab v-if="schema.stages">
                <template #title>
                    <span v-b-tooltip.bottom.hover="showUncategorizedStagesWarning ? $t('Uncategorized stages') : ''">
                        {{ $t("Stages") }}
                        <template v-if="showUncategorizedStagesWarning">
                            <i
                                class="tdi td-alert-circle text-danger ml-1"
                                v-if="isTemplate"
                            ></i>
                            <i
                                class="tdi td-alert-triangle text-warning ml-1"
                                v-else
                            ></i>
                        </template>
                    </span>
                </template>
                <template v-if="showUncategorizedStagesWarning">
                    <div
                        class="alert alert-danger d-flex align-items-baseline"
                        v-if="isTemplate"
                    >
                        <i class="tdi td-info-circle mr-2"></i>
                        <div>
                            <p class="mb-0">
                                {{ $t("All stages in a project template must be categorized.") }}
                            </p>
                        </div>
                    </div>
                    <div
                        class="alert alert-warning d-flex align-items-baseline"
                        v-else
                    >
                        <i class="tdi td-info-circle mr-2"></i>
                        <div>
                            <p class="mb-0">
                                {{
                                    $t(
                                        "To provide you with better analytics, it will soon be required to categorize all stages before saving a project."
                                    )
                                }}
                            </p>
                            <p class="mb-0 font-weight-medium">
                                {{ $t("Consider adding categories to all stages.") }}
                            </p>
                        </div>
                    </div>
                </template>
                <div class="row">
                    <ListElement
                        :schema="schema.stages"
                        name="stages"
                        v-ref:elements$
                    />
                </div>
            </b-tab>
            <b-tab v-else-if="selectedTemplateItem !== null">
                <template #title> {{ $t("Stages & actions") }} <i class="tdi td-lock ml-1"></i> </template>
                <div class="alert alert-warning">
                    <i class="tdi td-info-circle mr-1"></i>
                    {{ $t("Stages and actions will be automatically created based on the template you select.") }}
                </div>
                <div class="d-flex flex-column gap-2">
                    <div
                        class="text-sm py-3 px-3 border rounded-lg bg-white"
                        v-for="stage in selectedTemplateItem.stages"
                    >
                        <div class="d-flex align-items-baseline gap-3">
                            <h3 class="font-weight-medium mb-0">{{ stage.name }}</h3>
                            <stage-category-pill :stage="stage"></stage-category-pill>
                        </div>
                        <div
                            class="d-flex flex-column gap-2 mt-3"
                            v-if="stage.actions.length"
                        >
                            <div
                                class="d-flex align-items-baseline"
                                v-for="action in stage.actions"
                            >
                                <i
                                    class="tdi mr-2"
                                    :class="ACTION_ICONS[action.action_type]"
                                ></i>
                                <div>
                                    <h4 class="font-weight-medium mb-1">{{ action.name }}</h4>
                                    <p class="mb-0">{{ getSummaryForAction(action, selectedTemplateItem) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </b-tab>
            <b-tab v-if="selectedTemplateItem !== null">
                <template #title> {{ $t("Project actions") }} <i class="tdi td-lock ml-1"></i> </template>
                <div class="alert alert-warning">
                    <i class="tdi td-info-circle mr-1"></i>
                    {{ $t("Project actions will be automatically created based on the template you select.") }}
                </div>
                <div class="d-flex flex-column gap-2">
                    <div
                        class="d-flex flex-column gap-3 text-sm py-3 px-3 border rounded-lg bg-white"
                        v-if="selectedTemplateItem.project_actions.length"
                    >
                        <div
                            class="d-flex align-items-baseline"
                            v-for="action in selectedTemplateItem.project_actions"
                        >
                            <i
                                class="tdi mr-2"
                                :class="ACTION_ICONS[action.action_type]"
                            ></i>
                            <div>
                                <h4 class="font-weight-medium mb-1">{{ action.name }}</h4>
                                <p class="mb-0">{{ getSummaryForAction(action, selectedTemplateItem) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </b-tab>
        </b-tabs>
    </form>
</template>
