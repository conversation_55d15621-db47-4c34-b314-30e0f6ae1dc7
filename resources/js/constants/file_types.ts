import i18n from "../common/i18n";

export const FILE_TYPE_CV = 'cv';
export const FILE_TYPE_MESSAGE_ATTACHMENT = 'message_attachment';
export const FILE_TYPE_EVENT_SET_FILE = 'event_set_file';
export const FILE_TYPE_CONSENT_INFO = 'consent_info';
export const FILE_TYPE_FORM_SUBMISSION_FILE = 'form_submission_file';
export const FILE_TYPE_CANDIDATE_OTHER = 'candidate_other';
export const FILE_TYPE_COMMENT_ATTACHMENT = 'comment_attachment';
export const FILE_TYPE_CANDIDATE_PHOTO = 'candidate_photo';
export const FILE_TYPE_ORGANIZATION_LOGO = 'organization_logo';
export const FILE_TYPE_USER_AVATAR = 'user_avatar';
export const FILE_TYPE_PROJECT_LOG = 'project_log';
export const FILE_TYPE_LANDING_UPLOAD = 'landing_upload';
export const FILE_STRUCTURED_AD_IMAGE = 'structured_ad_image';
export const FILE_INSTANT_UPLOAD = 'instant_upload';
export const FILE_TYPE_REQUISITION_ATTACHMENT = 'requisition_attachment';
export const FILE_TYPE_EMAIL_MESSAGE = 'email_message';

const FILE_TYPES: {[key: string]: string} = {
    [FILE_TYPE_CV]: i18n.t('CV').toString(),
    [FILE_TYPE_MESSAGE_ATTACHMENT]: i18n.t('Message Attachment').toString(),
    [FILE_TYPE_EVENT_SET_FILE]: i18n.t('Interview file').toString(),
    [FILE_TYPE_CONSENT_INFO]: i18n.t('Consent Info').toString(),
    [FILE_TYPE_FORM_SUBMISSION_FILE]: i18n.t('Form Submission file').toString(),
    [FILE_TYPE_CANDIDATE_OTHER]: i18n.t('Other Candidate file').toString(),
    [FILE_TYPE_COMMENT_ATTACHMENT]: i18n.t('Comment Attachment').toString(),
    [FILE_TYPE_CANDIDATE_PHOTO]: i18n.t('Candidate photo').toString(),
    [FILE_TYPE_ORGANIZATION_LOGO]: i18n.t('Organization Logo').toString(),
    [FILE_TYPE_USER_AVATAR]: i18n.t('User Avatar').toString(),
    [FILE_TYPE_PROJECT_LOG]: i18n.t('Project Log').toString(),
    [FILE_TYPE_LANDING_UPLOAD]: i18n.t('Landing Upload').toString(),
    [FILE_STRUCTURED_AD_IMAGE]: i18n.t('Structured Ad Image').toString(),
    [FILE_INSTANT_UPLOAD]: i18n.t('Instant Upload').toString(),
    [FILE_TYPE_REQUISITION_ATTACHMENT]: i18n.t('Requisition Attachment').toString(),
    [FILE_TYPE_EMAIL_MESSAGE]: i18n.t('Email Message').toString(),
};

export default FILE_TYPES;
