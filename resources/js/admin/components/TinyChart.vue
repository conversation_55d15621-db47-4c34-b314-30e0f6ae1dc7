<script lang="ts">
import {defineComponent} from 'vue'
import {PropType} from "vue/types/options";
// @ts-ignore
import Dailychart from 'dailychart';

export default defineComponent({
    name: "<PERSON><PERSON><PERSON>",
    props: {
        data: {
            type: Array as PropType<number[]>,
            required: true
        },
    },
    mounted() {
        this.renderChart();
    },
    methods: {
        renderChart() {
            if (this.show) {
                this.$refs.chart.innerHTML = '';
                console.log('renderChart Inner');
                Dailychart.create(this.$refs.chart, {
                    lineWidth: 1, fillPositive: '#d6efda',
                    fillNegative: '#fbdddd',
                });
            }else{
            }
        }

    },
    computed: {
        sum(): number {
            return this.data.reduce((a, b) => a + b, 0);
        },
        close(): number {
            return this.sum / this.data.length;
        },
        show(): boolean {
            return this.data.length > 0 && this.sum !== 0;
        },
    },
    watch: {
        data: {
            handler(v,o) {
                let sum = (z) => z.reduce((a, b) => a + b, 0);
                if(sum(v) !== sum(o)){
                    this.renderChart();
                }
            },
            deep: true,
        }
    }
})
</script>

<template>
    <div ref="chart"
         style="height: 30px; width: 100px;"
         :data-dailychart-values="data.join(',')"
         data-dailychart-length="12"
         :data-dailychart-close="close"
    ></div>
</template>
