<script lang="ts">
import {defineComponent} from 'vue'
import MainLayout from "../../common/MainLayout.vue";
import AdminLayout from "../components/AdminLayout.vue";
import EmptyState from "../../common/EmptyState.vue";

export default defineComponent({
    name: "EInvoices",
    components: {EmptyState},
    layout: [MainLayout, AdminLayout],
    props:{
        confirmations: {
            type: Array,
            required: true
        }
    }
})
</script>

<template>
    <div class="card-body">
        <table class="table table-td" v-if="confirmations.length">
            <tbody>
            <tr v-for="confirmation in confirmations">
                <td>{{confirmation.stripe_event.data.object.id}}</td>
                <td>
                    <div class="d-flex align-items-center justify-content-end">
                        <a
                            :href="`/admin/earve/${confirmation.id}`"
                            class="btn btn-sm btn-white"
                        >View
                        </a>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
        <empty-state
            v-else
            title="All good, nothing here!"
        ></empty-state>
    </div>
</template>
