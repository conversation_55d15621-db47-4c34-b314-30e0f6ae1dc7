<script lang="ts">
import {defineComponent} from "vue";
import SettingsPageLayout from "../../settings/components/SettingsPageLayout.vue";
import {getUserRolesAsOptions, getUserStatusesAsOptions} from "../../constants/user_roles";
import {PropType} from "vue/types/options";
import SettingsPageCardLayout from "../../settings/components/SettingsPageCardLayout.vue";
import CheckboxDropdown from "../../common/CheckboxDropdown.vue";
import EmptyState from "../../common/EmptyState.vue";
import TdTable from "../../common/library/TdTable.vue";
import UserActions from "../components/UserActions.vue";
import {BvTableFieldArray} from "bootstrap-vue/src/components/table";
import TdRelativeTime from "../../common/library/TdRelativeTime.vue";
import TdDateTime from "../../common/library/TdDateTime.vue";

export default defineComponent({
    name: "UsersPage",
    components: {
        TdDateTime,
        TdRelativeTime,
        EmptyState,
        CheckboxDropdown,
        UserActions,
        SettingsPageCardLayout,
        TdTable,
    },
    layout: SettingsPageLayout,
    props: {
        users: {
            type: Array as PropType<User[]>,
            required: true,
        },
        teams: {
            type: Array as PropType<NonNullId<Team>[]>,
            required: true,
        },
        filters: {
            type: Object as PropType<{
                roles: string[];
                team_ids: number[];
                statuses: string[];
                sort_by: string | null;
                sort_order: "asc" | "desc" | null;
            }>,
            required: true,
        },
    },
    computed: {
        userRoles(): BMultiSelectOption[] {
            return getUserRolesAsOptions();
        },
        userStatuses(): BMultiSelectOption[] {
            return getUserStatusesAsOptions();
        },
        teamsOptions(): BMultiSelectOption[] {
            return this.teams.map(team => ({
                value: team.id,
                label: team.name,
            }));
        },
        tableFields(): BvTableFieldArray {
            return [
                {key: "name", label: this.$t("Name").toString(), sortable: true},
                {key: "role", label: this.$t("Role").toString(), sortable: true},
                ...(this.$rlSettings.features.teams ? [{key: "team", label: this.$t("Team").toString()}] : []),
                {key: "last_active", label: this.$t("Last active").toString(), sortable: true},
                {key: "actions", label: ""},
            ];
        },
    },
    methods: {
        search() {
            this.$inertia.visit(`/users`, {
                preserveState: true,
                data: this.filters,
                only: ["users"],
            });
        },
        handleUserDeactivated(user: User) {
            user.active = false;
        },
        handleSort({sortBy, sortDesc}: {sortBy: string; sortDesc: boolean}) {
            this.filters.sort_by = sortBy;
            this.filters.sort_order = sortDesc ? "desc" : "asc";
        },
    },
    watch: {
        filters: {
            handler: "search",
            deep: true,
        },
    },
});
</script>

<template>
    <settings-page-card-layout :page-title="$t('Users').toString()">
        <template #settingsHeader>
            <div class="d-flex align-items-center gap-2">
                <a
                    href="https://intercom.help/teamdash/en/articles/6189701-user-roles"
                    class="btn btn-sm btn-info"
                    target="_blank"
                >
                    <i class="tdi td-info-circle mr-2"></i>
                    {{ $t("Help Center") }}: {{ $t("User roles") }}
                </a>
                <Link
                    :href="route('users.bulkImport')"
                    class="btn btn-sm btn-white"
                >
                    {{ $t("Bulk import users") }}
                </Link>
                <Link
                    href="/users/create"
                    class="btn btn-dark btn-sm"
                >
                    <i class="fas fa-plus text-sm mr-2"></i>
                    {{ $t("Add user") }}
                </Link>
            </div>
        </template>
        <template #settingsTabs>
            <div class="card-header d-flex align-items-center gap-2">
                <checkbox-dropdown
                    v-model="filters.statuses"
                    :options="userStatuses"
                    icon="td-eye"
                    :text="$t('Status')"
                ></checkbox-dropdown>
                <checkbox-dropdown
                    v-model="filters.roles"
                    :options="userRoles"
                    icon="td-user"
                    :text="$t('Role')"
                ></checkbox-dropdown>
                <checkbox-dropdown
                    v-model="filters.team_ids"
                    :options="teamsOptions"
                    v-if="teams.length && $rlSettings.features.teams"
                    icon="td-users-group"
                    :text="$t('Team')"
                ></checkbox-dropdown>
            </div>
        </template>
        <template>
            <td-table
                v-if="users.length"
                :items="users"
                :fields="tableFields"
                class="table-td"
                @sort-changed="handleSort"
            >
                <template #cell(name)="{item: user}">
                    <a :href="`users/${user.id}/edit`">
                        {{ user.name }}
                    </a>
                    <br />
                    <small class="text-black-50">{{ user.email }}</small>
                </template>
                <template #cell(role)="{item: user}">
                    {{ user.display_role }} <span v-if="!user.active">({{ $t("Inactive") }})</span>
                </template>
                <template #cell(team)="{item: user}">
                    {{ user.team?.name }}
                </template>
                <template #cell(last_active)="{item: user}">
                    <small
                        v-if="user.last_active_at"
                        class="d-flex flex-column"
                    >
                        <td-relative-time
                            class="text-nowrap"
                            :timestamp="user.last_active_at"
                        />
                        <td-date-time
                            class="text-nowrap"
                            :timestamp="user.last_active_at"
                        />
                    </small>
                </template>
                <template #cell(actions)="{item: user}">
                    <user-actions
                        :user="user"
                        :all-users="users"
                        @deactivated="handleUserDeactivated(user)"
                    />
                </template>
            </td-table>
            <empty-state
                v-else
                :title="$t('No users found').toString()"
            >
            </empty-state>
        </template>
    </settings-page-card-layout>
</template>

<style scoped></style>
