<script lang="ts">
import {defineComponent} from "vue";
import {BDropdown, BDropdownItem, BSkeleton, BSkeletonWrapper} from "bootstrap-vue";
import TdModal from "../../common/components/TdModal.vue";
import ActionsTable from "../../common/components/ActionsTable.vue";
import UserDropdown from "../../common/UserDropdown.vue";
import {ROLE_LIMITED} from "../../constants/user_roles";
import {STATUS_IN_PROGRESS, STATUSES} from "../../constants/project_types";
import {ACTION_ICONS, ACTION_TYPES} from "../../constants/action_types";
import axios from "axios";
import {isEmpty} from "lodash";

export default defineComponent({
    name: "UserActions",
    components: {BSkeleton, BSkeletonWrapper, UserDropdown, ActionsTable, TdModal, BDropdownItem, BDropdown},
    props: {
        user: {
            type: Object as () => User,
            required: true,
        },
        allUsers: {
            type: Array as () => User[],
            required: true,
        },
    },
    data() {
        return {
            showDeactivateModal: false,
            linkedItems: null as null | {
                actionsToReassign: Record<string, Record<string, number>>;
                actionsToDelete: Record<string, Record<string, any[]>>;
                settingsToChange: Record<string, string>[];
            },
            reassignTo: null,
            deactivationRunning: false,
        };
    },
    methods: {
        async resendInvite() {
            const url = `/users/${this.user.id}/resendInvite`;
            const response = await axios.post(url);

            if (response.status === 200) {
                this.$bvToast.toast(response.data.message, {
                    title: this.$t("Invite sent!") as string,
                    variant: "success",
                    autoHideDelay: 5000,
                });
            } else {
                this.$bvToast.toast(this.$t("Failed to resend invite. Please try again later.") as string, {
                    title: this.$t("Error") as string,
                    variant: "danger",
                    autoHideDelay: 5000,
                });
            }
        },
        async fetchLinkedItems() {
            const url = `/users/${this.user.id}/linkedItems`;
            const response = await axios.get(url);
            this.linkedItems = response.data;
        },
        openDeactivateModal() {
            this.linkedItems = null;
            this.showDeactivateModal = true;
            this.fetchLinkedItems();
        },
        async deactivateUser() {
            this.deactivationRunning = true;
            const url = `/users/${this.user.id}/deactivate`;
            const response = await axios.post(url, {
                reassign_to: this.reassignTo?.id,
            });

            if (response.status === 200) {
                this.$bvToast.toast(response.data.message, {
                    title: this.$t("User deactivated!") as string,
                    variant: "success",
                    autoHideDelay: 5000,
                });
                this.showDeactivateModal = false;
                this.$emit("deactivated");
            } else {
                this.$bvToast.toast(this.$t("Failed to deactivate user. Please try again later.") as string, {
                    title: this.$t("Error") as string,
                    variant: "danger",
                    autoHideDelay: 5000,
                });
            }

            this.deactivationRunning = false;
        },
    },
    computed: {
        noLinkedItems() {
            return (
                isEmpty(this.linkedItems?.actionsToReassign) &&
                isEmpty(this.linkedItems?.actionsToDelete) &&
                isEmpty(this.linkedItems?.settingsToChange)
            );
        },
        deactivateWithReassign() {
            return (
                this.linkedItems !== null && (!!this.linkedItems?.actionsToReassign?.length || this.reassignTo !== null)
            );
        },
        availableUsers() {
            return this.allUsers.filter(user => user.id !== this.user.id && user.active && user.role !== ROLE_LIMITED);
        },
        projectStatuses() {
            return STATUSES;
        },
        actionTypes() {
            return ACTION_TYPES;
        },
        actionIcons() {
            return ACTION_ICONS;
        },
        STATUS_IN_PROGRESS() {
            return STATUS_IN_PROGRESS;
        },
        totals() {
            if (!this.linkedItems) {
                return {};
            }

            return {
                totalActionsToReassign: Object.values(this.linkedItems.actionsToReassign).reduce(
                    (acc: number, statusGroups: any) => {
                        return acc + Object.values(statusGroups).reduce((acc: number, count: number) => acc + count, 0);
                    },
                    0
                ),
                totalActionsToDelete: Object.values(this.linkedItems.actionsToDelete).reduce(
                    (acc: number, statusGroups: any) => {
                        return (
                            acc +
                            Object.values(statusGroups).reduce((acc: number, group: any[]) => acc + group.length, 0)
                        );
                    },
                    0
                ),
                totalSettingsToReassign: this.linkedItems.settingsToChange.length,
            };
        },
    },
});
</script>

<template>
    <div class="d-flex align-items-center justify-content-end gap-2">
        <Link
            :href="route('users.edit', {id: user.id})"
            class="btn btn-white btn-sm"
        >
            {{ $t("Edit") }}
        </Link>
        <b-dropdown
            size="sm"
            variant="white"
            no-caret
            right
            :disabled="user.id === $rlSettings.user.id"
        >
            <template v-slot:button-content>
                {{ $t("Actions") }}
                <small class="ml-2">
                    <i class="fas fa-chevron-down"></i>
                </small>
            </template>
            <b-dropdown-item @click="resendInvite">
                {{ $t("Resend invite") }}
            </b-dropdown-item>
            <b-dropdown-item
                v-if="user.active && user.id !== $rlSettings.user.id"
                variant="danger"
                @click="openDeactivateModal"
            >
                {{ $t("Deactivate") }}
            </b-dropdown-item>
        </b-dropdown>

        <td-modal
            size="lg"
            v-model="showDeactivateModal"
            :title="$t('Deactivate user {userName}', {userName: user.name})"
            body-class="position-static"
            content-class="overflow-initial"
            scrollable
        >
            <b-skeleton-wrapper :loading="linkedItems === null">
                <template #loading>
                    <b-skeleton width="80%"></b-skeleton>
                    <b-skeleton width="60%"></b-skeleton>
                    <b-skeleton width="60%"></b-skeleton>
                </template>
                <template v-if="noLinkedItems">
                    <p class="text-dark font-weight-medium text-sm mb-0">
                        {{ $t("Are you sure you want to deactivate this user?") }}
                    </p>
                </template>
                <template v-else>
                    <div class="text-sm">
                        <div class="alert alert-warning">
                            <p class="mb-0">
                                <i class="tdi td-alert-circle mr-2"></i>
                                <strong>{{
                                    $t("Heads up! Before you deactivate this user, review these linked items.")
                                }}</strong>
                            </p>
                        </div>
                        <div
                            class="p-25 bg-lighter rounded-lg mt-3"
                            v-if="totals?.totalActionsToReassign || totals?.totalSettingsToReassign"
                        >
                            <template>
                                <h3 class="d-flex align-items-baseline gap-2 mb-25">
                                    {{ $tc("{count} settings to reassign or clear", totals?.totalSettingsToReassign) }}
                                </h3>
                                <div class="flex flex-column gap-2">
                                    <div v-for="item in linkedItems?.settingsToChange">
                                        <div class="text-dark">
                                            <strong>{{ item.setting_name }}</strong>
                                        </div>
                                        <p v-if="item.change_info">{{ item.change_info }}</p>
                                    </div>
                                </div>
                                <hr />
                            </template>

                            <template v-if="totals?.totalActionsToReassign">
                                <h3 class="d-flex align-items-baseline gap-2 mb-25">
                                    {{ $tc("{count} stage actions to reassign", totals?.totalActionsToReassign) }}
                                </h3>
                                <div class="d-flex flex-column gap-2">
                                    <div
                                        class="text-dark"
                                        v-for="[type, statusGroups] in Object.entries(linkedItems.actionsToReassign)"
                                    >
                                        <h4>
                                            {{ actionTypes[type] }}
                                        </h4>
                                        <div v-for="[status, count] in Object.entries(statusGroups)">
                                            <p class="mb-1 font-weight-medium">
                                                {{
                                                    $t("{count} actions in {status} projects", {
                                                        count,
                                                        status: projectStatuses[status],
                                                    })
                                                }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <div class="mt-3">
                                <p class="text-dark mb-2">
                                    <strong>{{ $t("Choose user to reassign these items to:") }}</strong>
                                </p>
                                <user-dropdown
                                    :button-text="$t('Reassign to...')"
                                    v-if="availableUsers.length > 0"
                                    :selected-users="reassignTo ? [reassignTo] : []"
                                    :users="availableUsers"
                                    allow-empty
                                    @select="reassignTo = $event"
                                >
                                </user-dropdown>
                            </div>
                        </div>
                        <div
                            class="p-25 bg-danger-light rounded-lg mt-25"
                            v-if="totals?.totalActionsToDelete"
                        >
                            <h3 class="d-flex align-items-baseline gap-2 mb-25">
                                {{ $tc("{count} stage actions will be deleted", totals?.totalActionsToDelete) }}
                            </h3>
                            <div class="d-flex flex-column gap-2">
                                <div
                                    class="text-dark"
                                    v-for="[type, statusGroups] in Object.entries(linkedItems.actionsToDelete)"
                                >
                                    <h4>
                                        {{ actionTypes[type] }}
                                    </h4>
                                    <div v-for="[status, statusGroupItems] in Object.entries(statusGroups)">
                                        <div v-if="parseInt(status) === STATUS_IN_PROGRESS">
                                            <p class="mb-1 font-weight-medium">
                                                {{
                                                    $t("{count} actions in {status} projects", {
                                                        count: statusGroupItems.length,
                                                        status: projectStatuses[status],
                                                    })
                                                }}
                                            </p>
                                            <actions-table
                                                :actions="statusGroupItems"
                                                :show-project="true"
                                                :show-table-actions="false"
                                            ></actions-table>
                                        </div>
                                        <p
                                            class="mb-1 font-weight-medium"
                                            v-else
                                        >
                                            {{
                                                $t("{count} actions in {status} projects", {
                                                    count: statusGroupItems.length,
                                                    status: projectStatuses[status] ?? $t("confidential"),
                                                })
                                            }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-dark mt-2 mb-0">
                                <strong>{{ $t("These actions cannot be reassigned.") }}</strong>
                            </p>
                        </div>
                    </div>
                </template>
            </b-skeleton-wrapper>
            <template #footer>
                <button
                    type="button"
                    class="btn btn-sm btn-white"
                    @click="showDeactivateModal = false"
                    :disabled="deactivationRunning"
                >
                    {{ $t("Cancel") }}
                </button>
                <button
                    type="button"
                    class="btn btn-sm btn-danger"
                    @click="deactivateUser"
                    :disabled="deactivationRunning"
                >
                    <template v-if="deactivationRunning">
                        <i class="tdi td-load-spinner fa-spin mr-2"></i>
                        {{ $t("Deactivating...") }}
                    </template>
                    <template v-else-if="noLinkedItems">
                        {{ $t("Deactivate") }}
                    </template>
                    <template v-else>
                        {{
                            deactivateWithReassign
                                ? $t("Deactivate and reassign")
                                : $t("Deactivate without reassigning")
                        }}
                    </template>
                </button>
            </template>
        </td-modal>
    </div>
</template>
