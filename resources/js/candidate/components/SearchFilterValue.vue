<template>
    <div
        class="talentpool--value"
        :class="{'talentpool--value__focused': focused, 'talentpool--value__focusable': focusable}"
    >
        <div class="talentpool--value__item" ref="filterValue">
            <template v-if="svc === 'terms' && showTermOptions">
                <checkbox-dropdown
                    class="w-100"
                    toggle-class="w-100 px-25 white-space-normal d-block text-left rounded-xxl lh-normal"
                    menu-class="w-100"
                    search
                    variant="white"
                    v-model="value.terms"
                    :options="checkboxTermOptions"
                >
                    <template #labelText="{value}">
                        <span v-if="displayValue">
                            {{ displayValue }}
                        </span>
                        <span v-else>{{ $t('None selected') }}</span>
                    </template>
                </checkbox-dropdown>
                <b-spinner small v-if="loadingTerms"></b-spinner>
            </template>
            <template v-if="svc === 'freeTerms'">
                <b-form-tags
                    size="sm"
                    :input-attrs="{size: 0}"
                    input-class="talentpool--free-terms-input"
                    v-model="value.freeTerms"
                    remove-on-delete
                    :placeholder="$t('Terms')"
                    class="talentpool--free-terms"
                    tag-class="badge-pill badge-light"
                    autofocus
                    :add-button-text="$t('Add')"
                    add-button-variant="white"
                ></b-form-tags>
            </template>
            <template v-if="svc === 'location'">
                <location-search
                    size="sm"
                    class="w-100"
                    v-model="value.location.location"
                    :placeholder="$t('Location')"
                ></location-search>
            </template>
            <template v-if="svc === 'date'">
                <input type="date" v-model="value.term" class="form-control flex-grow-1" autofocus>
            </template>
            <template v-if="svc === 'file_type'">
                <div class="d-flex flex-column gap-1 ml-1">
                    <div class="d-flex align-items-center gap-2 overflow-hidden">
                        <span class="text-xs text-nowrap">
                            {{ $t('of type') }}
                        </span>
                        <auto-first-dropdown
                            name="file_type"
                            v-model="value.file.type"
                            :options="fileTypeOptions"
                            variant="white"
                            :dropdown-attrs="{
                                size: 'sm',
                                class: 'overflow-hidden',
                                'toggle-class': 'overflow-hidden',
                                boundary: 'viewport',
                            }"
                        ></auto-first-dropdown>
                    </div>
                    <div class="d-flex align-items-center gap-2 overflow-hidden" v-if="$rlSettings.features.credential_validity">
                        <span class="text-xs text-nowrap">
                            {{ $t('with status') }}
                        </span>
                        <auto-first-dropdown
                            name="file_type"
                            v-model="value.file.status"
                            :options="fileStatusOptions"
                            variant="white"
                            :dropdown-attrs="{
                                size: 'sm',
                                class: 'overflow-hidden',
                                'toggle-class': 'overflow-hidden',
                                boundary: 'viewport',
                            }"
                        ></auto-first-dropdown>
                    </div>
                </div>
            </template>
        </div>
        <!--        <div class="talentpool&#45;&#45;value__display" v-else>-->
        <!--            <clamp-->
        <!--                cut-by=","-->
        <!--                :max-lines="1"-->
        <!--                ellipsis=""-->
        <!--                autoresize-->
        <!--            >-->
        <!--                {{ displayValue }}-->
        <!--                <template v-slot:after="slotProps">-->
        <!--                    <span-->
        <!--                        v-b-tooltip.hover.bottom="displayValue"-->
        <!--                    >-->
        <!--                        {{ getHiddenCount(slotProps.text) }}-->
        <!--                    </span>-->
        <!--                </template>-->
        <!--            </clamp>-->
        <!--        </div>-->
    </div>
</template>

<script lang="ts">
import Multiselect from "@teamdash/vue-multiselect"
import {defineComponent} from "vue";
import {BFormTags} from "bootstrap-vue";
import {directive as onClickaway} from "vue-clickaway";
import Clamp from "../../common/Clamp.vue";
import CheckboxDropdown from "../../common/CheckboxDropdown.vue";
import LocationSearch from "../../common/components/LocationSearch.vue";
import AutoFirstDropdown from "../../common/AutoFirstDropdown.vue";
import {FILE_TYPE_CANDIDATE_OTHER, FILE_TYPE_CV} from "../../constants/file_types";

export default defineComponent({
    name: 'search-filter-value',
    components: {
        AutoFirstDropdown,
        LocationSearch,
        CheckboxDropdown,
        Multiselect,
        BFormTags,
        Clamp,
    },
    directives: {
        onClickaway: onClickaway,
    },
    data() {
        return {
            focused: false,
        }
    },
    props: {
        loadingTerms: {
            type: Boolean,
        },
        state: {
            type: Object,
        },
        svc: {
            type: String,
        },
        value: {
            type: Object,
        },
        showTermOptions: {
            type: Boolean,
        },
    },
    methods: {
        // focus() {
        //     if (this.focusable && !this.focused) {
        //         this.focused = true;
        //
        //         this.$nextTick(() => {
        //             if (this.$refs.terms) {
        //                 // @ts-ignore
        //                 this.$refs.terms.activate();
        //             }
        //         });
        //     }
        // },
        // unfocus(e: Event) {
        //     if (this.focused) {
        //         console.log("UNFOCUS");
        //         console.log(e);
        //
        //
        //         console.log(this.$refs);
        //         // @ts-ignore
        //         if (this.$refs.filterValue && this.$refs.filterValue.contains(e.target as Node)) {
        //             return;
        //         }
        //
        //         this.focused = false;
        //     }
        // },
        getHiddenCount(text: string) {
            if (!this.selectedCount) {
                return '';
            }
            const shownItems = text.split(',').length;
            const hiddenItems = this.selectedCount - shownItems;
            if (hiddenItems) {
                return this.$tc('+ {count} more', hiddenItems);
            }
            return '';
        }
    },
    computed: {
        focusable(): boolean {
            return ['terms', 'freeTerms', 'date', 'location'].includes(this.svc ?? '');
        },
        displayValue(): string | null {
            if (this.svc === 'terms' && this.showTermOptions) {
                return this.state?.termOptions.filter((term: BSelectOption) => this.value?.terms.includes(term.value)).map((term: BSelectOption) => term.text).join(', ');
            }
            if (this.svc === 'freeTerms') {
                return this.value?.freeTerms.join(', ');
            }
            if (this.svc === 'date') {
                return this.value?.term;
            }
            if (this.svc === 'location') {
                return this.value?.location?.location?.label ?? '';
            }
            return null;
        },
        selectedCount(): number | null {
            if (this.svc === 'terms') {
                return this.value?.terms.length;
            }
            if (this.svc === 'freeTerms') {
                return this.value?.freeTerms.length;
            }
            if (this.svc === 'date') {
                return this.value?.term ? 1 : 0;
            }
            return null;
        },
        checkboxTermOptions(): BMultiSelectOption[] {
            if (!this.state?.termOptions.length) {
                return [];
            }
            return this.state.termOptions.map((term: BSelectOption) => ({
                value: term.value,
                label: term.text,
            }));
        },
        fileTypeOptions(): BMultiSelectOption[] {
            const customFileTypeOptions = Object.values(window.fileTypes)
                .sort((a, b) => a.name.localeCompare(b.name))
                .map((type: FileType) => ({
                    value: `${FILE_TYPE_CANDIDATE_OTHER}.${type.id}`,
                    text: type.name,
                }));

            return [
                {value: null, text: this.$t('Any')},
                {value: FILE_TYPE_CV, text: this.$t('CV')},
                {value: FILE_TYPE_CANDIDATE_OTHER, text: this.$t('Other')},
                ...customFileTypeOptions,
            ]
        },
        fileStatusOptions(): BMultiSelectOption[] {
            return [
                {value: null, text: this.$t('Any')},
                {value: 'not_started', text: this.$t('Not started')},
                {value: 'ok', text: this.$t('Valid')},
                {value: 'expiring_soon', text: this.$t('Expiring soon')},
                {value: 'expired', text: this.$t('Expired')},
            ]
        },
    },
});
</script>
