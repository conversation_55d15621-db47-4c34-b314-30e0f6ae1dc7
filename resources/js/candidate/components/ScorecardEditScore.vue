<template>
    <div class="scorecard-score text-right">
        <div class="btn-group">
            <div
                v-for="s in scores"
                class="btn btn-white score"
                :class="[s.class, s.value === value ? scoreActiveClass : '']"
                @click="selectScore(s.value)"
            >
                {{ s.value }}
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {PropType} from "vue/types/options";


export default Vue.extend({
    name: 'Scorecard',
    components: {},
    props: {
        value: {
            type: Number as PropType<number|null>
        },
    },
    data() {
        return {
            scoreActiveClass: 'active',
            scores: [
                {
                    value: 1,
                    class: 'score-1',
                },
                {
                    value: 2,
                    class: 'score-2',
                },
                {
                    value: 3,
                    class: 'score-3',
                },
                {
                    value: 4,
                    class: 'score-4',
                },
                {
                    value: 5,
                    class: 'score-5',
                },
            ]
        }
    },
    methods: {
        selectScore (selectedScore: number) {
            this.$emit('input', selectedScore);
        }
    },
});
</script>
