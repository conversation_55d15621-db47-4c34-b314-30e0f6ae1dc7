<template>
    <div class="search-filter">
        <!--    <div class="form-field-group">-->
        <div class="d-flex align-items-center gap-2">
            <div class="d-flex align-items-baseline gap-2">
                <auto-first-dropdown
                    name="field"
                    v-model="value.field"
                    :options="fieldOptions"
                    variant="transparent"
                    :dropdown-attrs="{
                        'toggle-class': 'p-0',
                    }"
                ></auto-first-dropdown>
                <b-dropdown
                    class="field-op"
                    variant="transparent"
                    v-if="fieldType === 'location'"
                    toggle-class="p-0"
                    no-caret
                >
                    <template v-slot:button-content>
                        {{
                            $t('< {distance} from', {distance: value.location ? (value.location.distance.value + ' ' + value.location.distance.unit) : ''})
                        }}
                        <i class="tdi td-chevron-down ml-1"></i>
                    </template>
                    <b-dropdown-form>
                        <b-form-group :label="$t('Location range')" label-for="location-distance" @submit.stop.prevent>
                            <distance-input
                                :value="value.location ? value.location.distance : undefined"
                                @input="$set(value, 'location', {distance: $event, location: value.location ? value.location.location : null})"
                                size="sm"
                            ></distance-input>
                        </b-form-group>
                    </b-dropdown-form>
                </b-dropdown>
                <auto-first-dropdown
                    name="op"
                    :disabled="value.field === null"
                    v-model="value.op"
                    :options="operationOptions"
                    variant="transparent"
                    v-else-if="!hideOperations"
                    :dropdown-attrs="{
                        'toggle-class': 'p-0',
                    }"
                ></auto-first-dropdown>
            </div>
            <i
                class="tdi td-close ml-auto cursor-pointer"
                @click="$emit('remove')"
                v-b-tooltip.bottom="$t('Remove filter')"
            ></i>
        </div>
        <search-filter-value
            v-if="showValue"
            :loading-terms="loadingTerms"
            :state="state"
            :svc="svc.getInputType(value.field)"
            :showTermOptions="showTermOptions"
            :value="value"
        />
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {
    BDropdown,
    BDropdownForm,
    BFormGroup,
    BFormInput,
    VBTooltip
} from "bootstrap-vue";
import {CandidateSearchFilter, DiscreteOperation} from "../search/filters";
import {SearchFilterService} from "../search/filters";
import {PropType} from "vue/types/options";
import AutoFirstDropdown from "../../common/AutoFirstDropdown.vue";
import SearchFilterValue from "./SearchFilterValue.vue";
import DistanceInput from "../../common/DistanceInput.vue";

Vue.directive('b-tooltip', VBTooltip);

export default Vue.extend({
    components: {
        DistanceInput,
        SearchFilterValue,
        AutoFirstDropdown,
        BDropdown,
        BDropdownForm,
        BFormGroup,
        BFormInput,
    },
    props: {
        value: {
            type: Object as PropType<SearchFilterEditorState | null>,
        }
    },
    mounted() {
        this.$nextTick(() => {
            if (this.value?.field && this.hasTerms) {
                this.fetchTermOptions();
            }
        });
    },
    data() {
        return {
            state: {
                termOptions: [] as BSelectOption[],
            } as SearchFilterEditorState,
            loadingTerms: false,
            svc: new SearchFilterService,
            hydrating: false as boolean,
        }
    },
    methods: {
        async fetchTermOptions() {
            if (!this.hasTerms) {
                return;
            }
            this.loadingTerms = true;
            this.state.termOptions = await this.svc.getTermOptions(this.value?.field ?? null);
            this.loadingTerms = false;
        }
    },
    computed: {
        fieldOptions: {
            get(): BSelectOption[] {
                return this.svc.getFilters().map(({key: value, label: text}) => ({
                    value,
                    text: this.$t(text).toString(),
                }));
            }
        },
        fieldType(): string | null {
            return this.svc.getInputType(this.value?.field ?? null);
        },
        operationOptions: {
            get(): BSelectOption[] {
                if (!this.selectedFilter) {
                    return [];
                }
                const filterOperations = this.svc.getOperations(this.selectedFilter.type);

                return filterOperations.map((op) => ({
                    value: op,
                    text: this.$t(this.svc.getOperationLabel(op)).toString(),
                }));
            }
        },
        hideOperations(): boolean {
            return this.selectedFilter?.hideOperations ?? false;
        },
        hasTerms(): boolean {
            return this.fieldType === "terms";
        },
        selectedFilter(): CandidateSearchFilter | undefined {
            return this.svc.getFilter(this.value?.field ?? null);
        },
        showTermOptions(): boolean {
            return this.value?.op != DiscreteOperation.HAS_NONE
        },
        showValue(): boolean {
            if (this.selectedFilter?.type === 'checkbox') {
                return false;
            }

            if (this.value?.op === DiscreteOperation.HAS_NONE) {
                return false;
            }

            return true;
        }
    },
    watch: {
        'value.field': {
            handler(v) {
                if (this.value?.field && this.hasTerms) {
                    this.fetchTermOptions();
                }
            }
        },
    }
})
</script>
