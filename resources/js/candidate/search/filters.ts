import axios from "axios";
import {AllSettings, Candidate<PERSON>ustomField, CandidateFieldType} from "../../@types/rl-settings";
import i18n from "../../common/i18n";

export enum ModelOperation {
    EXISTS = "EXISTS",
    DOES_NOT_EXIST = "DOES_NOT_EXIST",
}

export enum BooleanOperation {
    TRUE = "TRUE",
    FALSE = "FALSE",
}

export enum StrictCompareOperation {
    IS_ONE_OF = "IS_ONE_OF",
    NOT_ANY_OF = "NOT_ANY_OF",
}

export enum CompareOperation {
    LESS_THAN = "LESS_THAN",
    GREATER_THAN = "GREATER_THAN",
}

export enum DiscreteOperation {
    HAS_ONE_OF = "HAS_ONE_OF",
    HAS_NONE_OF = "HAS_NONE_OF",
    HAS_ALL_OF = "HAS_ALL_OF",
    HAS_NONE = "HAS_NONE",
}

export enum StringOperation {
    CONTAINS_ONE = "CONTAINS_ONE",
    CONTAINS_ALL = "CONTAINS_ALL",
    DOES_NOT_CONTAIN = "DOES_NOT_CONTAIN",
}

export enum LocationOperation {
    IS_WITHIN = "IS_WITHIN",
}

export enum FileTypeOperation {
    HAS_FILE = "HAS_FILE",
}

export type OperationType =
    | BooleanOperation
    | StrictCompareOperation
    | CompareOperation
    | DiscreteOperation
    | StringOperation
    | LocationOperation
    | FileTypeOperation;

export const OperationLabel: Map<string, string> = new Map([
    [DiscreteOperation.HAS_ALL_OF, i18n.t("has all of").toString()],
    [DiscreteOperation.HAS_ONE_OF, i18n.t("has one of").toString()],
    [DiscreteOperation.HAS_NONE_OF, i18n.t("has none of").toString()],
    [DiscreteOperation.HAS_NONE, i18n.t("has none").toString()],
    [StringOperation.CONTAINS_ONE.toString(), i18n.t("contains").toString()],
    [StringOperation.CONTAINS_ALL, i18n.t("contains all").toString()],
    [StringOperation.DOES_NOT_CONTAIN, i18n.t("does not contain").toString()],
    [StrictCompareOperation.IS_ONE_OF, i18n.t("is one of").toString()],
    [StrictCompareOperation.NOT_ANY_OF, i18n.t("not any of").toString()],
    [CompareOperation.GREATER_THAN, i18n.t("greater than").toString()],
    [CompareOperation.LESS_THAN, i18n.t("less than").toString()],
    [BooleanOperation.TRUE, i18n.t("is true").toString()],
    [BooleanOperation.FALSE, i18n.t("is false").toString()],
    [LocationOperation.IS_WITHIN, i18n.t("is near").toString()],
]);

export const OperationTypeMapping: {[key in CandidateFieldType]: OperationType[]} = {
    text: Object.values(StringOperation),
    checkbox: Object.values(BooleanOperation),
    select: Object.values(StrictCompareOperation),
    tags: Object.values(DiscreteOperation),
    datetime: Object.values(CompareOperation),
    location: Object.values(LocationOperation),
    file_type: Object.values(FileTypeOperation),
};

export interface CandidateSearchFilter {
    key: string;
    type: CandidateFieldType;
    label: string;
    custom: boolean;
    hideOperations?: boolean;
    items?: BSelectOption[];
}

export const BaseFilters: CandidateSearchFilter[] = [
    {key: "name", type: "text", label: i18n.t("Name").toString(), custom: false},
    {key: "email", type: "text", label: i18n.t("E-mail").toString(), custom: false},
    {key: "tags", type: "tags", label: i18n.t("Tags").toString(), custom: false},
    {key: "stages.project", type: "tags", label: i18n.t("Projects").toString(), custom: false},
    {key: "stages.project.status", type: "select", label: i18n.t("Projects status").toString(), custom: false},
    {key: "stages.project.manager", type: "tags", label: i18n.t("Projects managed by").toString(), custom: false},
    {key: "stages.project.team", type: "tags", label: i18n.t("Teams").toString(), custom: false},
    {key: "cvs.contents", type: "text", label: i18n.t("CV").toString(), custom: false},
    {key: "comments.content", type: "text", label: i18n.t("Comments").toString(), custom: false},
    {key: "created_at", type: "datetime", label: i18n.t("Candidate created date").toString(), custom: false},
    {key: "place", type: "location", label: i18n.t("Location").toString(), custom: false},
    {key: "applications.dropoutReason", type: "tags", label: i18n.t("Dropout reason").toString(), custom: false},
    {
        key: "sourcingActivities.created_at",
        type: "datetime",
        label: i18n.t("Application date").toString(),
        custom: false,
    },
    {
        key: "firstNoScopeApplication.created_at",
        type: "datetime",
        label: i18n.t("First application date").toString(),
        custom: false,
    },
    {
        key: "lastNoScopeApplication.created_at",
        type: "datetime",
        label: i18n.t("Last application date").toString(),
        custom: false,
    },
    {key: "file", type: "file_type", label: i18n.t("Has file").toString(), hideOperations: true, custom: false},
    {
        key: "activeConsents.consentSubtype",
        type: "tags",
        label: i18n.t("Active consent of type").toString(),
        custom: false,
    },
];

/**
 * Why use different representations for editor state & filters?
 * Term options change, so we can not save them as "filters".
 */
export class SearchFilterService implements ISearchFilterService {
    settings: AllSettings;
    filters: Map<string, CandidateSearchFilter> = this.convertFilterArrayToMap(BaseFilters);

    constructor(settings: AllSettings | undefined = undefined) {
        this.settings = settings ?? window.settings;
        this.setCustomFieldsAsFilters(this.settings.candidate_custom_fields);
    }

    convertFilterArrayToMap(filters: CandidateSearchFilter[]): Map<string, CandidateSearchFilter> {
        return new Map<string, CandidateSearchFilter>(filters.map(filter => [filter.key, filter]));
    }

    setCustomFieldsAsFilters(fields: CandidateCustomField[]): void {
        const formattedFilters: CandidateSearchFilter[] = fields
            .filter(f => f.visibility !== "hidden")
            .map(
                (field: CandidateCustomField): CandidateSearchFilter => ({
                    ...field,
                    custom: true,
                })
            );

        this.filters = this.convertFilterArrayToMap([...BaseFilters, ...formattedFilters]);
    }

    getFilter(key: string | null): CandidateSearchFilter | undefined {
        if (!key) {
            return;
        }
        return this.filters.get(key);
    }

    getFilters(): CandidateSearchFilter[] {
        return Array.from(this.filters.values());
    }

    getOperations(filterType: CandidateFieldType | null): OperationType[] {
        if (!filterType) {
            return [];
        }
        return OperationTypeMapping[filterType];
    }

    getOperationLabel(operation: OperationType): string {
        if (!operation) {
            return "";
        }
        return OperationLabel.get(operation) ?? "-";
    }

    getLabelData(state: SearchFilterEditorState): {fieldLabel: string; opLabel: string} {
        const field = this.getFilter(state.field);

        return {
            fieldLabel: field?.label ?? "-",
            opLabel: state.op ? (OperationLabel.get(state.op) ?? "-") : "-",
        };
    }

    editorStateToFilter(state: SearchFilterEditorState): SearchFilter {
        const filter = this.getFilter(state.field);
        if (!filter || !state.op) {
            throw new Error("Not implemented!");
        }

        let resultingFilterValue: unknown = "";

        switch (filter.type) {
            case "datetime":
                resultingFilterValue = state.term;
                break;
            case "tags":
                resultingFilterValue = state.terms;
                break;
            case "text":
                resultingFilterValue = state.freeTerms;
                break;
            case "checkbox":
                resultingFilterValue = null;
                break;
            case "select":
                resultingFilterValue = state.terms;
                break;
            case "location":
                resultingFilterValue = state.location
                    ? {
                          label: state.location.location?.label,
                          location: [state.location.location?.latitude, state.location.location?.longitude],
                          distance: state.location?.distance,
                      }
                    : null;
                break;
            case "file_type":
                const fileTypeFilters = state.file?.type ? state.file.type.split(".") : [];

                resultingFilterValue = state.file
                    ? {
                          file_type: fileTypeFilters[0],
                          file_type_id: fileTypeFilters[1] ?? null,
                          status: state.file.status,
                      }
                    : null;
                break;
        }

        return {
            field: filter.key,
            op: state.op,
            value: resultingFilterValue,
        };
    }

    async getTermOptions(field: string | null): Promise<BSelectOption[]> {
        const candidateSearchFilter = this.getFilter(field);

        if (!candidateSearchFilter) {
            return [];
        }

        if (candidateSearchFilter.custom) {
            if (candidateSearchFilter.items) {
                return candidateSearchFilter.items;
            } else {
                return [];
            }
        }

        return await this.fetchTermOptions(field!);
    }

    async fetchTermOptions(field: string): Promise<BSelectOption[]> {
        const res = await axios.get("/candidates/loadFilterTerms", {
            params: {field: field},
        });
        return res.data;
    }

    async filterToEditorState(filter: SearchFilter): Promise<SearchFilterEditorState> {
        const candidateSearchFilter = this.getFilter(filter.field);
        if (!candidateSearchFilter) {
            throw new Error("Not implemented!");
        }

        let filterProps: any;

        const candidateSearchFilterInputType = this.getInputType(filter.field);

        switch (candidateSearchFilterInputType) {
            case "terms": {
                const opts: BSelectOption[] = await this.getTermOptions(filter.field);

                filterProps = {
                    termOptions: opts,
                    freeTerms: [],
                    terms: filter.value as (string | number)[],
                    term: null,
                };
                break;
            }
            case "freeTerms": {
                filterProps = {
                    termOptions: [],
                    freeTerms: filter.value as string[],
                    terms: [],
                    term: null,
                };
                break;
            }
            case "date": {
                filterProps = {
                    termOptions: [],
                    freeTerms: [],
                    terms: [],
                    term: filter.value as string | null,
                };
                break;
            }
            case "checkbox": {
                filterProps = {
                    termOptions: [],
                    freeTerms: [],
                    terms: [],
                    term: null,
                };
                break;
            }
            case "location": {
                filterProps = {
                    termOptions: [],
                    freeTerms: [],
                    terms: [],
                    term: null,
                    location: {
                        location: {
                            label: (filter.value as any)?.label,
                            latitude: (filter.value as any)?.location[0],
                            longitude: (filter.value as any)?.location[1],
                        },
                        distance: (filter.value as any)?.distance,
                    },
                };
                break;
            }
            case "file_type": {
                const filterValue = filter.value as any;

                let fileType = filterValue?.file_type;
                if (filterValue?.file_type_id) {
                    fileType += "." + filterValue.file_type_id;
                }

                filterProps = {
                    termOptions: [],
                    freeTerms: [],
                    terms: [],
                    term: null,
                    file: {
                        type: fileType,
                        status: filterValue?.status,
                    },
                };
                break;
            }
            default: {
                throw new Error("Not implemented!");
            }
        }

        return new Promise(resolve => {
            resolve({
                field: filter.field,
                op: filter.op,
                ...filterProps,
            });
        });
    }

    getInputType(field: string | null): CandidateSearchInputType {
        if (field === null) {
            return null;
        }

        const filter = this.getFilter(field);

        switch (filter?.type) {
            case "text": {
                return "freeTerms";
            }
            case "datetime": {
                return "date";
            }
            case "select": {
                return "terms";
            }
            case "tags": {
                return "terms";
            }
            case "checkbox": {
                return "checkbox";
            }
            case "location": {
                return "location";
            }
            case "file_type": {
                return "file_type";
            }
            default: {
                return "terms";
            }
        }
    }
}
