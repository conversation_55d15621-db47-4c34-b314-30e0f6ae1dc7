<template>
    <component :is="tag" class="rl-editor editor" :class="{'editor-inline': inline, 'editor-no-value': (editable && !value)}">
        <bubble-menu :editor="editor" v-if="editor">
            <div class="menububble"
                 :class="{ 'is-active': editor.isActive }"
            >
                <button
                    class="menububble__button"
                    @click="editor.chain().focus().toggleBold().run()"
                    :class="{ 'is-active': editor.isActive('bold') }">
                    bold
                </button>
                <button
                    class="menububble__button"
                    @click="editor.chain().focus().toggleItalic().run()"
                    :class="{ 'is-active': editor.isActive('italic') }">
                    italic
                </button>
                <button
                    v-if="withBulletList"
                    class="menububble__button"
                    @click="editor.chain().focus().toggleBulletList().run()"
                    :class="{ 'is-active': editor.isActive('bulletList') }">
                    bullet list
                </button>
                <button
                    class="menububble__button"
                    @click="setLink"
                    :class="{ 'is-active': editor.isActive('link') }">
                    link
                </button>
                <button
                    class="menububble__button"
                    @click="editor.chain().focus().unsetLink().run()"
                    v-if="editor.isActive('link')">
                    remove
                </button>
                <div v-if="isSafari" class="position-relative">
                    <div @click="showMainPicker = !showMainPicker; showListPicker = false;"
                         :style="{backgroundColor: editor.getAttributes('textStyle').color}"
                         style="width: 40px; height: 22px; outline: 1px solid black; border: 6px solid #ededed;">&nbsp;
                    </div>
                    <chrome-picker
                        v-if="showMainPicker"
                        style="position: absolute"
                        @keyup.enter="showMainPicker = false;"
                        @keyup.esc="showMainPicker = false;"
                        :value="{hex: editor.getAttributes('textStyle').color}"
                        @input="editor.chain().focus().setColor($event.hex).run()"
                    ></chrome-picker>
                    <div @click="showListPicker = !showListPicker; showMainPicker = false;"
                         v-if="editor.isActive('bulletList') && !disableBulletColor"
                         :style="{backgroundColor: bulletColorValue}"
                         style="width: 40px; height: 22px; outline: 1px solid black; border: 6px solid #ededed;">&nbsp;
                    </div>
                    <chrome-picker
                        v-if="showListPicker"
                        style="position: absolute"
                        @keyup.enter="showListPicker = false;"
                        @keyup.esc="showListPicker = false;"
                        :value="{hex: bulletColorValue}"
                        @input="bulletColorValue = $event.hex;"
                    ></chrome-picker>
                </div>
                <template v-else>
                    <input type="color"
                           :value="editor.getAttributes('textStyle').color"
                           @change="editor.chain().focus().setColor($event.target.value).run()">
                    <input type="color"
                           v-if="editor.isActive('bulletList') && !disableBulletColor"
                           v-model="bulletColorValue"
                    >
                </template>
                <button
                    class="menububble__button"
                    v-if="!inline && allowH3"
                    @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
                    :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }">
                    h3
                </button>
            </div>
        </bubble-menu>
        <floating-menu
            class="floating-menu"
            :tippy-options="{ duration: 100 }"
            :editor="editor"
            v-if="editor && allowImages"
        >
            <pic-browser @input="addImage($event)" label="Add image">

            </pic-browser>
        </floating-menu>
        <editor-content
            :editor="editor"
            v-if="editable"
        ></editor-content>
        <html-fragment v-else :html="value"
        ></html-fragment>
    </component>
</template>

<script lang="ts">
import Vue, {PropType} from 'vue';
import {BubbleMenu, Editor, EditorContent, FloatingMenu} from '@tiptap/vue-2';
import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import Text from '@tiptap/extension-text';
import Bold from '@tiptap/extension-bold';
import Italic from '@tiptap/extension-italic';
import HardBreak from '@tiptap/extension-hard-break';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import BulletList from '@tiptap/extension-bullet-list';
import ListItem from '@tiptap/extension-list-item';
import {Heading} from "@tiptap/extension-heading";
import TextStyle from '@tiptap/extension-text-style'
import {Node} from '@tiptap/core';
import './editable-stlyes.scss';
import { Color } from '@tiptap/extension-color';
import {Chrome} from 'vue-color';
import PicBrowser from "./PicBrowser.vue";

const InlineText = Node.create({
    name: 'inlinetxt',
    topNode: true,
    content: 'inline*',
})

Vue.component('html-fragment', {
    functional: true,
    props: ['html'],
    render(h, ctx) {
        const nodes = new Vue({
            beforeCreate() {
                this.$createElement = h
            }, // not necessary, but cleaner imho
            template: `<div>${ctx.props.html ?? ''}</div>`
            // @ts-ignore
        }).$mount()._vnode.children
        return nodes
    }
})

export default Vue.extend({
    components: {
        EditorContent,
        BubbleMenu,
        FloatingMenu,
        PicBrowser,
        'chrome-picker': Chrome,
    },
    props: {
        value: {
            type: String as PropType<string>,
            default: '',
        },
        tag: {
            type: String as PropType<string>,
            default: 'div',
        },
        editable: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        withBulletList: {
            default: false,
            type: Boolean
        },
        inline: {
            type: Boolean as PropType<boolean>,
        },
        bulletColor: {
            type: String as () => string | null,
            default: null,
        },
        disableBulletColor: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        allowH3: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        allowImages: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
    },
    data() {
        const isSafari = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
        return {
            editor: null as null | Editor,
            isSafari,
            showMainPicker: false,
            showListPicker: false,
        }
    },
    mounted() {
        let extensions = [Document, Paragraph, BulletList, ListItem];
        if (this.inline) {
            extensions = [InlineText];
        }
        if (this.allowH3) {
            extensions.push(Heading);
        }
        if (this.allowImages) {
            extensions.push(Image);
        }

        let vm = this;
        this.editor = new Editor({
            content: this.value,
            extensions: [
                ...extensions,
                Text,
                Bold,
                Italic,
                HardBreak,
                Link,
                TextStyle,
                Color,
            ],
            onUpdate: () => {
                if (this.editor) {
                    this.$emit('input', this.editor.getHTML())
                }
            },
            onBlur({editor, event}) {
                vm.showMainPicker = false;
                vm.showListPicker = false;
            },
            onSelectionUpdate({editor}) {
                vm.showMainPicker = false;
                vm.showListPicker = false;
            },
        })
    },
    methods: {
        setLink() {
            const url = window.prompt('URL')

            if (!url || !this.editor) {
                return;
            }

            this.editor.chain().focus().setLink({href: url}).run()
        },
        addImage(url: string) {
            if (this.editor && url) {
                this.editor.chain().focus().setImage({src: url}).run()
            }
        },
    },
    beforeDestroy() {
        if (this.editor) {
            this.editor.destroy()
        }
    },
    computed: {
        bulletColorValue: {
            get(): string | null {
                return this.bulletColor;
            }, set(v: string) {
                this.$emit('change-color', v);
            }
        }
    },
    watch: {
        value(value) {
            if (!this.editor) {
                return;
            }
            // HTML
            const isSame = this.editor.getHTML() === value

            // JSON
            // const isSame = this.editor.getJSON().toString() === value.toString()

            if (isSame) {
                return
            }

            this.editor.commands.setContent(this.value, false)
        },
    },
});
</script>
