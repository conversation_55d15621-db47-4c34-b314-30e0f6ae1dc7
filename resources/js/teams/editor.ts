import Vue from 'vue';
import {initSentry} from "../common/config";
import {Graph} from './vendor/graph-editor/graph';
import * as d3 from 'd3';
import FeatureLock from "../common/directives/FeatureLock";
import axios from "axios";
import {ToastPlugin} from "bootstrap-vue";
import i18n from "../common/i18n";

initSentry();
Vue.directive('can', FeatureLock);

let graph: null | Graph = null;

declare global {
    interface Window {
        auto_increment: number,
        teams: Team[],
        team_edges: TeamEdge[],
    }
}

Vue.use(ToastPlugin);

new Vue({
    el: '#content',
    i18n,
    data() {
        return {}
    },
    mounted() {
        graph = new Graph({
            svg: d3.select("#graph"),
            nodes: window.teams.map(t => {
                return {
                    id: t.id as number,
                    title: t.name,
                    x: t.x,
                    y: t.y,
                    is_deletable: t.is_deletable,
                }
            }),
            edges: window.team_edges.map((e) => {
                return {
                    source: e.source_team_id,
                    target: e.target_team_id,
                }
            }),
            node_id: window.auto_increment
        });
    },
    methods: {
        save() {
            if (graph) {
                axios.post('/teams/save', JSON.parse(graph.toJson())).then(() => {
                    this.$bvToast.toast(this.$t('Teams saved!').toString(), {
                        variant: 'success',
                        autoHideDelay: 2000,
                        solid: true
                    });
                }).catch(() => {
                    this.$bvToast.toast(this.$t('There was an error').toString(), {
                        variant: 'error',
                        autoHideDelay: 2000,
                        solid: true
                    });
                });
            }
        },
        addNode() {
            const name = prompt('Team name');
            if (name) {
                graph?.addNode(name, 400, 400);
            }
        }
    }
});
