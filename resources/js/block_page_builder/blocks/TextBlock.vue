<template>
    <component is="BaseBlockLayout" v-on="$listeners" :editable="editable">
        <template slot="content">
            <div class="text-block">
                <div class="mb-1">
                    <svg height="28" width="22">
                        <circle cx="11" cy="11" r="6" :stroke="theme.brandColor" stroke-width="4" fill="none" />
                    </svg>
                    <h2 class="d-inline-block" :style="{color: theme.brandColor}"><editable v-model="spec.heading" :editable="editable"></editable></h2>
                </div>
                <div class="text-block__body" :style="{borderLeftColor: theme.brandColor}">
                    <editable v-model="spec.body" :editable="editable"></editable>
                </div>
            </div>
        </template>
    </component>
</template>
<script lang="ts">

import Vue from 'vue';
import BaseBlockLayout from "./BaseBlockLayout.vue";
import Editable from "../../landing_page_builder/components/Editable2.vue";

export default Vue.extend({
    name: 'ImageBlock',
    components: {
        BaseBlockLayout,
        Editable,
    },
    props: {
        spec: {
            type: Object as () => TextBlockSpec
        },
        editable: {
            type: Boolean as () => boolean,
        },
        theme: {
            type: Object as () => BlockPageTheme
        },
    },
    methods: {
    },
    computed: {
    }
});

</script>
