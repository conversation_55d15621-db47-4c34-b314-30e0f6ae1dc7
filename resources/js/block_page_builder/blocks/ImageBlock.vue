<template>
    <component is="BaseBlockLayout" v-on="$listeners" :editable="editable">
        <template slot="left-settings">
            <pic-browser v-if="spec.url" v-model="spec.url" label="Choose image" class="d-inline-block">
                <template  v-slot:title="slotProps">
                    <a
                        @click.prevent="slotProps.setShow()" class="btn btn-white-default btn-sm">
                        <i class="fas fa-image"></i>
                    </a>
                </template>
            </pic-browser>
        </template>
        <template slot="content">
            <vue-resizable :active="editable ? ['b'] : []" :height="spec.height" @resize:end="spec.height = $event.height">
                <div :style="{
                height: '100%',
                backgroundImage: bgValue,
            }" class="page-block__content page-block__content--image">
                    <div class="text-center py-4" v-if="!spec.url">
                        <pic-browser v-model="spec.url" label="Choose image"></pic-browser>
                    </div>
                </div>
            </vue-resizable>
        </template>
    </component>
</template>
<script lang="ts">

import Vue from 'vue';
import BaseBlockLayout from "./BaseBlockLayout.vue";
import PicBrowser from "../../landing_page_builder/components/PicBrowser.vue";
import VueResizable from '../../common/components/vue-resizable.vue'

export default Vue.extend({
    name: 'ImageBlock',
    components: {
        BaseBlockLayout,
        PicBrowser,
        VueResizable
    },
    props: {
        spec: {
            type: Object as () => ImageBlockSpec
        },
        editable: {
            type: Boolean as () => boolean,
        }
    },
    methods: {
    },
    computed: {
        bgValue(): string | null {
            if (!this.spec.url) {
                return null;
            } else {
                return `url("${encodeURI(this.spec.url)}")`;
            }
        }
    }
});

</script>
