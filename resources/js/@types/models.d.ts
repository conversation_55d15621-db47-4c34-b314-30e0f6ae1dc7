// eslint-disable-next-line @typescript-eslint/triple-slash-reference
/// <reference path="util.d.ts" />

type HTMLString = string;

interface Model {
    id: number | null;
    created_at?: string;
    updated_at?: string;
    deleted_at?: string;
    updating?: undefined | boolean;

    [key: string]: any;
}

interface MailFeedbackDatum {
    type: string;
    timestamp: string;
}

interface HasColor {
    color: string;
}

interface CalendarFilters {
    users: number[];
    projects: number[];
}

interface CalendarEvent {
    start: string | Date;
    title: string;
    end: string | null | Date;
    class: string;
    event_set_id?: number | null;
    invites?: string[];
    ts_id?: number | null;
    task_id?: number | null;
    model_id?: number | null;
    is_static?: boolean | null;
    // time_slot: TimeSlot,
    time_slots?: TimeSlot[] | null;
    participants?: number;
    editable: boolean;
    draggable: boolean;
    deletable: boolean;
    resizable: boolean;
}

interface Scorecard extends Model {
    name: string;
    questions?: ScorecardQuestion[];
    response_sets: ScorecardResponseSet[];
}

interface ScorecardQuestion extends Model {
    scorecard_id: number;
    question: string;
    sort_order: number;
    responses?: ScorecardResponse[];
}

interface ScorecardResponse extends Model {
    scorecard_question_id: number;
    // deprecated fields
    user_id: never;
    project_id: never;
    candidate_id: never;
    score: number;
    scorecard_response_set_id: number;
}

interface ScorecardResponseSet extends Model {
    scorecard_id: number;
    candidate_id: number;
    stage_id: number | null;
    user_id: number;
    comment: null | string;
    user: User;
    responses: ScorecardResponse[];
    when: string;
}

interface ScorecardComment extends Model {
    user_id: number;
    project_id: number;
    candidate_id: number;
    comment: string | null;
}

interface CalendarAjaxResponse {
    // TODO: very likely the below should be EventSet[]
    calendarEventSets: CalendarEvent[];
    calendarTasks: Task[];
}

interface EditableEventSet extends EventSet {
    slots: CalendarEvent[];
}

interface MessageablePivot {
    bounced_at: string | null;
    clicked_at: string | null;
    complaint_at: string | null;
    delivery_at: string | null;
    open_at: string | null;
    reject_at: string | null;
    render_fail_at: string | null;
    sent_at: string | null;
    message_id: number;
    messageable_id: number;
    messageable_type: string;
    feedback_data: MailFeedbackDatum[];
}

interface Survey extends Model {
    token: string;
    response: null | number;
    comment: string | null;
}

interface Message extends Model {
    user_id: User["id"] | null;
    project_id: Project["id"] | null;
    subject: string | null;
    body: string | null;
    to: JSONValue;
    is_sent: boolean;
    merge_fields: JSONValue;
    is_promotional: boolean;
    video_id: Video["id"] | null;
    delay_until: string | null;
    team_id: Team["id"] | null;
    form_id: Form["id"] | null;
    original_sender_id: User["id"] | null;
    when: string;

    files?: NonNullId<File>[];
    candidates?: NonNullId<Candidate>[];
    references?: NonNullId<CandidateReference>[];
    users?: NonNullId<User>[];
    form?: NonNullId<Form> | null;
    video?: NonNullId<Video> | null;
    event?: NonNullId<Model> | null;
    presentation?: NonNullId<Presentation> | null;
    project?: NonNullId<Project> | null;
    user?: NonNullId<Project> | null;
    originalSender?: NonNullId<User> | null;
    pivot?: MessageablePivot;
    survey?: NonNullId<Survey> | null;
}

type SummaryMode = "off" | "only_summary" | "with_summaries" | "only_summary_with_anonymized_cv";

interface Stage extends Model {
    name: string;
    candidates: Candidate[];
    actions: Action[];
    project: Project;
    project_id: number;
    visible_for_limited: boolean;
    fair_evaluations: boolean;
    category: number;

    summary_mode: SummaryMode;

    position_name?: string;
}

interface Project extends Model {
    position_name: string;
    current_user_is_subscribed: boolean;
    stages: Stage[];
    client: Client | null | undefined;
    scorecard: never;
    project_manager_id: number | null;
    project_failure_reason_id: number | null;

    team: Team | null | undefined;
    is_pinned: undefined | boolean;
    is_perpetual: boolean;
    nps_score?: number | null;
    nps_sent_count?: number;
    nps_response_count?: number;
    start_date?: string | null;
    end_date?: string | null;
    deadline_at?: string | null;
    scorecards?: Scorecard[];
    locations?: Location[];
    is_template: boolean;
    users?: User[];
    requisitions?: Requisition[];
    display_name: Nillable<string>;
    warranty_until: Nillable<string>;
    description: Nillable<string>;
    status: number;
    custom_fields: Nillable<Record<string, JSONValue>>;
    template_id: Nillable<number>;
    perpetual_gdpr_consent_length: Nillable<string>;
    team_id: Nillable<number>;
    accessible_only_members: Nillable<boolean>;
    crm_organization_id: Nillable<number>;
    crm_office_id: Nillable<number>;
    project_actions?: ProjectAction[];
    is_empty?: boolean;
    project_failure_reason?: Nillable<ProjectFailureReason>;
    screening_criteria?: ScreeningCriterion[];
}

interface Client extends Model {
    name: string | null;
    contact_phone: string | null;
    contact_email: string | null;
    contact_name: string | null;
    client_manager_id: number | null;

    manager?: User;
    projects?: Project[];
    active_projects?: Project[];
    contacts?: Obj[];
    offices?: Obj[];

    projects_count?: number;
    active_projects_count?: number;
    contacts_count?: number;
    offices_count?: number;
}

interface Employment extends Model {
    candidate?: Candidate;
    candidate_id: number;
    position_title?: string;
    employer_name?: string;
    date_from_str?: string;
    date_to_str?: string;
    period?: string;
}

interface Education extends Model {
    candidate_id: number;
    degree: number;
    degree_text: string;
    degree_title: string | null;
    institution: string | null;
    programme: string | null;
    start_year: number | null;
    end_year: number | null;
}

type UserRole = "regular" | "limited" | "admin" | "service";

interface User extends Model {
    name: string;
    email: string;
    role: UserRole;
    active?: boolean;
    display_role?: string;
    avatar_url: string;
    team_id: number | null;
    is_teams_admin: boolean;
    last_active_at?: string;

    receivedPresentations?: Presentation[];
    sentPresentations?: Presentation[];
}

interface Sms extends Model {
    user: null | User;
    body: string;
}

interface File extends Model {
    url: string;
    location: string;
    display_name: string;
    type: string;
    project_id: number | null;
    project?: Project | null;
    when?: string;
    busting_location: string;
    has_contents: boolean;
    fileable_type: string;
    fileable_id: number;
}

interface DisplayCustomField {
    label: string;
    value: string;
    valueSlug: string | string[] | number;
    show_on_candidate_card: boolean;
    is_url: boolean;
    key: string;
    type: string;
}

interface Candidate extends Model {
    name?: string;
    email?: string;
    skype?: string;
    phone?: string;
    phone_e164: string | null;
    linkedin_url?: string;
    country?: string;
    city?: string;
    place?: Place | null;
    description?: string;
    entered_by?: User;
    employments: Employment[];
    last_employment: Employment | null;
    educations: Education[];
    stages: Stage[];
    applications: Application[];
    messages: Message[];
    comments?: Comment[];
    new_activity_summaries: NewActivitySummary[] | undefined;
    tags: Tag[] | undefined;
    consents: Consent[] | undefined;
    sms: Sms[];
    last_cv?: File | null;
    comments_count?: number;
    messages_count?: number;
    sms_count?: number;
    received_sms_count?: number;
    pending_invites_count?: number;
    accepted_invites_count?: number;
    answered_video_invites_count?: number;
    pending_video_invites_count?: number;
    pending_consent_renewals_count?: number;
    references_count?: number;
    sent_messages_count?: number;
    bounced_messages_count?: number;
    opened_messages_count?: number;
    clicked_messages_count?: number;
    unresolved_duplicate_links_count?: number;
    pending_references_count?: number;
    submitted_references_count?: number;
    pending_references?: number;
    submitted_references?: number;
    display_custom_fields: DisplayCustomField[];
    last_stage_entry_activity?: StageEntryActivity;

    pivot?: {sort_order: number; stage_id: number; candidate_id: number};

    questions_responses?: ScorecardResponse[];
    scorecard_response_sets: ScorecardResponseSet[];
    summary?: null | CandidateSummary;
    automaticallyDetectedLocationsCache?: Place[];
    meeting_analyses_count: number;
    rating_avg?: Nillable<number | string>;
    rating_count?: Nillable<number>;
    incomplete_tasks?: Task[];
    presentations?: NonNullId<Presentation>[];
}

interface ConsentSubtype extends Model {
    name: string;
    short_name?: string;
    renewal_text?: string;
    renewal_text_translations?: Array<{language: string; renewal_text: string}>;
}

interface ConsentBase extends Model {
    candidate_id: number;
    project_id: number | null;
    ip: string | null;
    source: string | null;
    is_from_public_source: boolean;
    revoked_at: string | null;
    revocation_reason: HTMLString | null;
    revoked_by: User | null;
}

interface ConsentUntilDate extends ConsentBase {
    consent_type: "until_date";
    active_until: string;
    project?: null;
    consent_subtype: NonNullId<ConsentSubtype> | null;
}

interface ConsentUntilProjectEnd extends ConsentBase {
    consent_type: "until_project_end";
    active_until: null;
    project: NonNullId<Project> | undefined;
    consent_subtype: null;
}

type Consent = ConsentUntilDate | ConsentUntilProjectEnd;

interface Application extends Model {
    candidate_id: number;
    stage: Stage;
    stage_id: number;
    project: Project;
    dropout_reason: string | null;
    sort_order: number;
    screening_criterion_responses?: ScreeningCriterionResponse[];
}

interface ConsentRenewal extends Model {
    candidate_id: number;
    message_id: number;
    token: string;
    expires_at: string;
}

interface FullCandidate extends Candidate {
    id: NonNullable<Candidate["id"]>;
    files: NonNullId<File>[];
    stages: Array<NonNullId<Stage> & {project: NonNullId<Stage["project"]>}>;
    applications: Application[];
    consents: Consent[];
    unresolved_duplicate_links: PossibleDuplicateCandidateLink[];
    activeConsent: Consent[];
    automaticallyDetectedLocationsCache: Place[];
}

interface CandidateSummary extends Model {
    summary_json: null | {
        one_sentence_summary: string;
        five_sentence_summary_html: string;
        is_relevant_for_position: boolean;
        fit_for_position_assessment: string;
        highest_level_of_education: string;
        skills: Array<{
            skill_name: string;
            skill_level: string;
        }>;
        work_experience: Array<{
            company: string;
            role: string;
            start_date: string;
            end_date: string;
        }>;
        education: Array<{
            institution: string;
            degree_or_level: string;
            start_date: string;
            end_date: string;
        }>;
    };
    anonymized_cv_html: string | null;
    is_failed: boolean;
    fail_reason: "invalid_response_from_ai_model" | "no_cv_contents" | null;
    raw_model_response: string | null;
    usage_json: JSONValue;
    candidate_id: number;
    project_id: number | null;
    version: string;
    has_anonymized_cv: boolean;
}

interface CandidateFilter extends Model {
    name: string;
    is_public: 0 | 1;
    user_id: number;
    data: CandidateSearchFilters | null;
}

interface CandidateSourceData {
    label: string;
    date: string;
    source: string;
    url: string | null;
}

interface CandidateUploadResult {
    candidate: Candidate;
    file: File;
}

interface StageEntryActivity extends Model {
    days_in_stage: number;
}

interface Activity extends Model {
    activity_type: ActivityType;
    comment: Comment | null;
    message: Message | null;
    call: Call | null;
    project: Project | null;
    submission?: Submission | null;

    is_custom: boolean;
    display_custom_fields: {label: string; value: string; show_on_candidate_card: boolean; key: string}[];
}

interface ActivityType extends Model {
    name: string;
    tab_ref: null | string;
    form_id: number | null;
}

interface NewActivitySummary extends Model {
    count: number;
    activity_type: ActivityType;
    activity_ids: number[];

    [propName: string]: any;
}

interface DetailedStatRow {
    candidate_id: number;
    candidate_name: string;
    stages: {
        stage_id: number;
        entry_at: string;
        category: number;
        days_in_stage: number | null;
        was_rejected: boolean;
        moved_forward: boolean;
    }[];
    source: CandidateSourceData;
}

interface DetailedStatRowGroup {
    source: string;
    budget: number;
    rows: DetailedStatRow[];
}

type PentalRatingRank = "LOWEST" | "LOW" | "MIDDLE" | "HIGH" | "HIGHEST";

interface Comment extends Model {
    candidate_id: number;
    user_id: number | null;
    user: User;
    project_id: number | null;
    project: Project | null;
    content?: string;
    reacts: React[];
    updated_by?: User | null;
    updated_by_id: number | null;
    is_public: boolean;
    is_quick: boolean;
    team_id: number | null;
    rating: number | null;
    pental_rating_rank: PentalRatingRank | null;
    video_interview_invite_id: number | null;
}

interface Call extends Model {
    user: User;
    project_id: number | null;
    project?: Project | null;
    content?: string;
    duration_human?: string | null;
}

interface React extends Model {
    comment_id: number;
    user_id: number;
    user: User;
    emoji: string;
}

interface Tag extends Model {
    name: string;
    color: string;
    light_color: string;
}

interface ProjectRole extends Model {
    name: string;
    color: string;
}

interface LandingTag extends Model {
    id: number | null;
    name: string;
    color: string;
    light_color: string;
}

interface Context {
    candidate_id?: number | null;
    project_id?: number | null;

    [propName: string]: any;
}

interface FormListItem {
    value: any;
    label: string;
}

interface FormListStringItem {
    value: string;
    label: string;
}

interface Task extends Model {
    title: string;
    done: boolean;
    project_id: number;
    project?: Project | null;
}

type ActionType =
    | "message"
    | "video_interview"
    | "move_to_stage"
    | "add_tags"
    | "video_message"
    | "ping_user"
    | "check_references"
    | "schedule_interview"
    | "user_message"
    | "send_webhook";

type ActionTrigger =
    | "manual"
    | "delayed_automatic"
    | "single_automatic"
    | "delayed_after_video_response"
    | "delayed_after_submitting_references"
    | "delayed_after_form_submission"
    | "project_status_change_automatic";

interface Action extends Model {
    name: string;
    action_type: ActionType;
    trigger: ActionTrigger;
    stage_id: number;
    done_candidate_ids: number[];
    is_ready_for_removal: boolean;
    data: {
        target_stage_id?: number;
        send_to_type?: string;
        user_ids?: (string | number)[];
        role_ids?: (string | number)[];
    };
    filters: null | {condition: "has_tag"; tag_id: number}[];
    filters_condition: number;
    filter_items: object;
    form_type: null | "all" | "candidate" | "additional_info" | "survey";
    form_id: null | "";

    form?: Form | null;
}

interface ActionDelay {
    value: number;
    unit: "minutes" | "hours" | "days";
}

interface ProjectAction extends Model {
    name: string;
    action_type: "user_message";
    trigger: "project_status_change_automatic";
    project_id: number;
    data: {};
    filters: null | {condition: "status"; project_statuses: number[]}[];
    filters_condition: number;
    filter_items: object;
}

interface Template extends Model {
    name: string;
    subject: string;
    body: string;
    data: {[key: string]: any} | null;
}

type BSelectOption<PotentialValue extends number | string | null = number | string | null> = {
    value: PotentialValue;
    text: string;
};

interface BMultiSelectOption {
    value: number | string;
    label: string;
    children?: BMultiSelectOption[];
    disabled?: boolean;
}

interface TimeSlot extends Model {
    start_time: string;
    end_time: string;
    start_time_local: string;
    end_time_local: string;
    event_set: EventSet;
    invites: Invite[];
    max_participants: number;
    room_name: string | null;
    room_email: string | null;
    room_address: string | null;
    private_notes: string | null;
}

type Invite = Model;

interface EventSet extends Model {
    time_slots: TimeSlot[];
    title: string;
    spoken_language: string | null;
    invites: Invite[];

    available_time_start?: string;
    available_time_end?: string;
    available_period_start?: string;
    available_period_end?: string;
    available_time_zone?: string;
}

interface TranscriptionSegment {
    text: string;
    time: number;
}

interface MeetingAnalysis extends Model {
    time_slot: TimeSlot | null;
    recording_created_at: string | null;
    recording_length_seconds: number | null;
    summary_by_openai: string[] | null | undefined;
    error_at: string | null;
    transcript_by_openai: TranscriptionSegment[] | null;
    chats: AiChatThread[];
    video: Video | null;
    spoken_language_override: string | null;
    ms_online_meeting_id: string | null;
    video_id: number | null;
    transcribed_ms_call_recording_id: string | null;
    in_progress_since: string | null;
    integration_id: number | null;
    integration?: Integration | null;
}

interface AiChatMessage extends Model {
    query: string;
    response: string | null;
}

interface AiChatThread extends Model {
    messages: AiChatMessage[];
    user: User;
    project: Project;
    candidate: Candidate;
    title: string | null;
}

interface StageSummary {
    stage_id: number;
    project_id: number;
    candidate_count: number;
    with_activity_count: number;
}

interface TermsInfo {
    tosOk: boolean;
    privacyOk: boolean;
    acceptedTos: string | null;
    acceptedPrivacy: string | null;
}

interface ProjectLog extends Model {
    user: User;
    content: string | null;
}

interface PaginatedResults<T> {
    data: T[];
    total: number;
    current_page: number;
    from: number;
    last_page: number;
    last_page_url: string;
    next_page_url: string;
    path: string;
    per_page: number;
    prev_page_url: string;
    to: number;
}

interface GQLPaginatedResponse<T> {
    data: T[];
    paginatorInfo: {
        total: number;
        currentPage: number;
        from: number;
        lastPage: number;
        lastPageUrl: string;
        nextPageUrl: string;
        path: string;
        perPage: number;
        prevPageUrl: string;
        to: number;
    };
}

interface ProjectFilterCustomFields {
    [key: string]: (string | null)[];
}

interface ProjectFilters {
    users: number[];
    clients: number[];
    users_only_managers: boolean;
    statuses: number[];
    locations: number[];
    order_by: string[];
    show_only_my_projects: boolean;
    exclude_continuous: boolean;
    name_query: null | string;
    project_end_date_from: null | string;
    project_end_date_until: null | string;
    project_start_date_from: null | string;
    project_start_date_until: null | string;
    custom_fields: ProjectFilterCustomFields;
    duration: null | DurationPickerGroupedValue;
    user_role: UserRole | null;

    team_ids: number[];

    teams_with_descendants: boolean;
}

interface ProjectStats {
    project_count: number;
    candidate_count: number;
    avg_candidate_count: number;
    avg_duration: number;
}

interface ProjectAjaxResponse {
    projects: PaginatedResults<Project>;
    stage_summaries: StageSummary[];
}

interface LFField {
    label: string;
    type: string;
    rules: string[];
    items: BSelectOption[];
}

interface CustomFieldSpec {
    key: string;
    label: string;
    type: string;
    rules: string[];
    items: FormListItem[];
}

interface DroppedFile {
    stage: Stage;
    file: File;
}

interface VideoInterview extends Model {
    title: string;
    intro_contents: string;
    status: number;
    author_id: User["id"] | null;
    require_answers_in_order: boolean;
    enforce_limits: boolean;

    questions?: VideoInterviewQuestion[];
    questions_count?: number;
    invites?: VideoInterviewInvite[];
    invites_count?: number;
    candidates?: Candidate[];
    candidates_count?: number;
}

interface VideoInterviewInvite extends Model {
    token: string;
    video_interview_id: number;
    candidate_id: number;
    project_id: number;
    message_id: number | null;

    interview?: VideoInterview;
    responses?: VideoInterviewResponse[];
    responses_count?: number;
    candidate?: Candidate;
    project?: Project;
    comments?: Comment[];
    comments_count?: number;
    message?: Message | null;
}

interface VideoInterviewQuestion extends Model {
    video_interview_id: number;
    type: "video" | "text";
    sort_order: number;
    title: string | null;
    contents: string | null;
    video_id: number | null;
    max_response_duration: number | null;
    max_attempts: number | null;

    video?: Video | null;
    interview?: VideoInterview;
    responses?: VideoInterviewResponse[];
    responses_count?: number;

    disable_remove?: boolean;
}

interface VideoInterviewResponse extends Model {
    ran_out_of_time: boolean;
    video_id: number | null;
    video_interview_invite_id: number;
    video_interview_question_id: number;
    confirmed_at: string | null;
    legacy_attempt_count: number | null;

    video?: Video | null;
    question?: VideoInterviewQuestion;
    invite?: VideoInterviewInvite;
}

interface Video extends NonNullId<Model> {
    cf_uid: string;
    ms_call_recording_id: string | null;
    processed_at: string | null;
    token: string | null;
    duration: number;

    file?: File | null;
    hls_playlist?: File | null;
}

interface LoadedVideo extends Video {
    file: File;
    hls_playlist: File;
}

interface WebsiteAdminInfo {
    usersCount: number;
    projectsCount: number;
    candidatesCount: number;

    activityTrend: number;
    weeklyActivityHistory: any[];
    weeklyActiveUserHistory: any[];
    activeUserCount30d: number;

    lastActiveUserName: string | null;
    lastActiveUserAt: string | null;

    mrr: number | null;
}

interface Website extends Model {
    display_name: string;
    table_data: null | WebsiteAdminInfo;
    hostnames?: Hostname[];
}

interface Hostname extends Model {
    fqdn: string;
    late_redirect_to: string | null;
    is_default: boolean;
}

declare enum LandingType {
    V1 = "v1",
    V2 = "v2",
    TALLINK = "tallink",
    V3 = "v3",
}

interface Landing extends Model {
    slug: string;
    status: "active" | "draft" | "archived";
    type: LandingType;
    data?: BaseLandingSpec;
    image_url: string | null;
    public_token: string | null;
    private_token: string | null;

    team_id: number | null;
    is_pinned: boolean;
    is_internal: boolean;
    tags?: LandingTag[] | undefined;
}

interface LandingV2 extends Model {
    slug: string;
    status: "active" | "draft" | "archived";
    type: LandingType;
    data?: BaseLandingSpec;
    permalink: string | null;
    image_url: string | null;
    public_token: string | null;
    private_token: string | null;
    is_pinned: boolean;
    is_internal: boolean;
}

interface JobAdFeed extends Model {
    slug: string;
    name: string;
    output_type: "default" | "cv_keskus" | "vp2";
}

type IntegrationFlag = "places_permission" | "transcripts" | "store_video_recordings";
interface Integration extends Model {
    name: string;
    feed_slug: string;
    remote_type: string;
    flags: IntegrationFlag[] | null;
}

interface ApiKey extends Model {
    name: string;
    api_key: string;
    integration_id: number;
    permissions: string[];
    display_permissions: string[];
}

interface ConnectionData {
    name: string;
    isScreen: boolean;
}

interface BaseLandingSpec {
    meta: {
        title: string;
        share_img: string | null;
        description: string | null;
    };
    content?: {
        [key: string]: any;
    };
}

interface V1LandingSpec extends BaseLandingSpec {
    [key: string]: any;
}

interface HistoryItemValue {
    displayValue: string;
    id: string;
    type: string | null;
    imageUrl: string | null;
    url: string | null;
}

interface HistoryItem {
    actor: HistoryItemValue;
    from: HistoryItemValue | null;
    to: HistoryItemValue | null;
    eventDisplayName: string;
    id: number;
    at: string;
    activity: Activity;
}

interface SubscriptionState {
    fullSeatCount: number;
    fullSeatCents: number;
    limitedSeatCount: number | null;
    limitedSeatCents: number | null;
    enableVideos: boolean;
    videosCents: number | null;
    period: "month" | "year";
    minimumSpendPerPeriodCents: number | null;
}

interface PriceSet {
    full: number;
    limited: number;
    video: number;
}

interface PriceList {
    eur: {
        month: PriceSet;
        year: PriceSet;
    };
}

interface UsageState {
    full: number;
    limited: number;
}

interface StageGroup {
    id: number;
    category: null;
    stage_ids: number[];
    projects: Project[];
    project_names: string;
}

interface CustomStageCategory extends Model {
    name: string;
    system_category: number;
}

interface ProjectStatItem {
    category: number;
    avgMinutesInStage?: number;
    currentCandidateCount: number;
    forwardCandidateCount: number;
    rejectedCandidateCount: number;
}

interface ProjectStatResponse {
    submissions: ProjectStatItem;
    interviews: ProjectStatItem;
    offer: ProjectStatItem;
    hired: ProjectStatItem;
    rejected: ProjectStatItem;
}

interface CropCoordinates {
    width: number;
    height: number;
    top: number;
    left: number;
}

interface Team extends Model {
    name: string;
    x: number;
    y: number;

    is_deletable: boolean;
}

interface TeamEdge {
    source_team_id: number;
    target_team_id: number;
}

interface DurationPickerGroupedValue extends DurationPickerValue {
    group_by: string;
}

interface DurationPickerValue {
    period_type: string;
    period: [string | null, string | null] | [];
}

interface PossibleDuplicateCandidateLink extends Model {
    id: number;
    existing_candidate: Candidate | null;
}

interface CandidateReference extends Model {
    name: string;
    email: string;
    phone: string;
    company_name: string;
    position: string;
    reference_type: string;
    last_message_at: string | null;
    submissions: Submission[];
}

interface LocationSearchResult {
    administrative_area: string | null;
    confidence: number;
    continent: string | null;
    country: string | null;
    country_code: string | null;
    county: string | null;
    label: string | null;
    latitude: number;
    locality: string | null;
    longitude: number;
    map_url: string;
    name: string | null;
    neighbourhood: string | null;
    number: any;
    postal_code: string | null;
    region: string | null;
    region_code: string | null;
    street: string | null;
    type: string;
    detection_method: string;
    source: string;
    source_identifier: string;
    raw_data: object;
    original_query: string;
    attribution: string;
}

type Place = Model &
    LocationSearchResult & {
        distances?: [{distance: number; place_id: string}];
    };

interface Location extends Model {
    name: string;
    place: Place;
    place_id: number;
}

interface StructuredJobAdLink {
    name: String;
    ok: Boolean;
    url: String;
    info: String;
}

interface StructuredJobAd extends Model {
    id: number;
    links: StructuredJobAdLink[];
    is_published: boolean;
    is_active: boolean;
    position_name: string;
    project_id: number;
    project_name: string;
    deadline_at: string;
    unpublished_at: string | null;
}

interface StageImport extends Model {
    source_remote_id: number;
    stage_id: number;
}

interface CopyCandidatesResult {
    candidates_already_in_project_count: number;
    candidates_successfully_added_count: number;
}

interface SubmissionDisplay {
    type: TdFormFieldType;
    value: unknown;
    label: string;
    valueSlug: unknown;
    key: string;
    is_url: boolean;
}

interface SubmissionFileFieldValue {
    file_name: string | null;
    file_url: string | null;
}

interface SubmissionPositionSelectFieldValue {
    stage_id: NonNullable<Stage["id"]>;
    stage_name: Stage["name"];
    project_id: Stage["project_id"];
    project_name: Stage["position_name"];
}

type FormattedSubmissionDisplay = SubmissionDisplay &
    (
        | {type: "select"; value: string}
        | {type: "tags"; value: string}
        | {type: "checkboxgroup"; value: string}
        | {type: "place"; value: string}
        | {type: "list"; value: FormattedSubmissionDisplay[][]}
        | {type: "text"; value: string}
        | {type: "phone"; value: string}
        | {type: "textarea"; value: string}
        | {type: "checkbox_numeric"; value: string}
        | {type: "date"; value: string}
        | {type: "hidden"; value: string}
        | {type: "numericrate"; value: string}
        | {type: "cnps"; value: string}
        | {type: "static"; value: string}
        | {type: "privacypolicy"; value: string}
        | {type: "instantfile"; value: SubmissionFileFieldValue}
        | {type: "file"; value: SubmissionFileFieldValue}
        | {type: "multifile"; value: SubmissionFileFieldValue[]}
        | {type: "checkbox"; value: boolean}
        | {type: "position_select"; value: SubmissionPositionSelectFieldValue[]}
    );

interface Submission extends Model {
    form_id: number;
    candidate_id: number;
    data: {
        [key: string]: unknown;
    };
    user_data: {
        [key: string]: unknown;
    };
    stage_id: number;
    candidate_reference_id?: number;
    display_submission: SubmissionDisplay[];
    formatted_display_submission: FormattedSubmissionDisplay[];
    submitted_user: string;
}

interface Requisition extends Model {
    user_id: number;
    recruiter_id: number;
    project_id: number | null;
    deadline_at: string;
    position_name: string | null;
    info: string | null;
    org_chart_position: unknown[] | null;
    custom_fields: Record<string, JSONValue> | null;
    display_position_name: string | null;
    display_custom_fields: FormListItem[];
    has_succeeded: boolean;
    has_failed: boolean;
    is_ongoing: boolean;
    is_editable: boolean;
}

interface LoadedRequisition extends Requisition {
    id: NonNullable<Requisition["id"]>;
    author: User;
    files: Array<NonNullId<File>>;
    recruiter: User;
    approvals: LoadedRequisitionApproval[];
}

interface RequisitionApproval extends Model {
    requisition_id: number;
    user_id: number;
    approved_at: string | null;
    rejected_at: string | null;
    comment: string | null;
    is_answered: boolean;
}

interface LoadedRequisitionApproval extends RequisitionApproval {
    id: NonNullable<RequisitionApproval["id"]>;
    user: NonNullId<User>;
}

type TdFormFieldType =
    | "text"
    | "phone"
    | "textarea"
    | "file"
    | "instantfile"
    | "multifile"
    | "checkbox"
    | "checkbox_numeric"
    | "position_select"
    | "select"
    | "date"
    | "hidden"
    | "tags"
    | "checkboxgroup"
    | "numericrate"
    | "cnps"
    | "static"
    | "privacypolicy"
    | "place"
    | "list";

type TdFormField = {
    val: unknown;
    slug: string;
    items: string[];
    order: number;
    rules: string[];
    max_val: null | number;
    min_val: null | number;
    db_field: string;
    text_val: unknown;
    field_type: TdFormFieldType;
    position_stages: unknown[];
    label_translations: unknown[];
    upload_field_label: unknown;
    upload_field_label_translations: unknown[];
};

type TdFormType = "candidate" | "reference" | "user" | "additional_info" | "survey";

type TdFormData = {
    id: unknown;
    font: string;
    type: TdFormType;
    cta_url: null | string;
    cta_text: null | string;
    stage_id: null | Stage["id"];
    languages: unknown[];
    custom_css: unknown;
    text_color: unknown;
    submit_text: unknown;
    multilingual: unknown;
    success_text: unknown;
    primary_color: unknown;
    default_language: unknown;
    use_landing_theme: unknown;
    cta_url_translations: unknown[];
    cta_text_translations: unknown[];
    submit_text_translations: unknown[];
    success_text_translations: unknown[];
    formFields: TdFormField[];
};

interface Form extends Model {
    title: string;
    stage_id: number | null;
    token: string;
    form_data: TdFormData;
    schema: {
        schema: {
            [key: string]: {
                type: TdFormFieldType;
                label?: string;
                drop?: boolean;
                rules?: string[];
                columns?: {
                    field?: number | null;
                    label?: number;
                    element?: number;
                };
                messages?: {
                    [key: string]: string;
                };
                trueValue?: unknown;
                falseValue?: unknown;
            };
        };
        button: {
            key?: string;
            class?: string;
            label?: string;
        };
        formData: TdFormData;
        translations: unknown[];
    };
    status: number;
    team_id: number | null;
    type: TdFormType;
    url: string;
    embed_code?: string;
    submissions?: Submission[];
    stage?: Stage;
}

interface FileType extends Model {
    name: string;
}

interface CustomFont extends Model {
    font_family: string;
    regular_font_file_id: number | null;
    bold_font_file_id: number | null;
    italic_font_file_id: number | null;
    bold_italic_font_file_id: number | null;

    regular_font_file?: File | null;
    italic_font_file?: File | null;
    bold_font_file?: File | null;
    bold_italic_font_file?: File | null;
}

interface Audit extends NonNullId<Model> {
    user_type: string;
    user_id: number;
    user?: User;
    event: "updated" | "created" | "deleted" | "retrieved_full_profile";
    auditable_type: string;
    auditable_id: number;
    auditable: NonNullId<Model>;
    old_values: Record<string, unknown>;
    new_values: Record<string, unknown>;
    url: string | null;
    ip_address: string | null;
    user_agent: string | null;
    created_at: string;
    updated_at: string;
}

type LoadedCustomFont = NeverUndefinedProperties<
    NonNullId<CustomFont>,
    "regular_font_file" | "italic_font_file" | "bold_font_file" | "bold_italic_font_file"
>;

interface Presentation extends Model {
    token: string;
    project_id: Project["id"] | null;
    message_id: NonNullable<Message["id"]>;
    expires_at: string;
    summary_mode: SummaryMode;

    message?: Message;
    project?: Project | null;
    users?: User[];
    candidates?: Candidate[];
    permalink?: string;
}

interface DropoutReason extends Model {
    name: string;
    candidate_reason: number;
}

interface ProjectFailureReason extends Model {
    name: string;
    type: "candidate" | "company";
    projects?: Project[];
    projects_count?: number;
}

interface ScreeningCriterion extends Model {
    project_id: number;
    criterion: string;
    is_required: boolean;
    responses?: ScreeningCriterionResponse[];
}

interface ScreeningCriterionResponse extends Model {
    application_id: number;
    screening_criterion_id: number;
    result: string | null;
    details: string | null;
    criterion?: ScreeningCriterion;
    application?: Application;
    error_message?: string | null;
    error_at?: string | null;
}
