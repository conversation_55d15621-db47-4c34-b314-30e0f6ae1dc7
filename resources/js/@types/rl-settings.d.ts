// 1. Make sure to import 'vue' before declaring augmented types

// 2. Specify a file with the types you want to augment
//    <PERSON><PERSON> has the constructor type in types/vue.d.ts
import {route} from "ziggy-js";
import Echo from "laravel-echo";

interface FeatureControl {
    create: boolean;
    view: boolean;
    update: boolean;
}

interface FeatureControls {
    comments: FeatureControl;
    stages: FeatureControl;
    messages: FeatureControl;
    sms: FeatureControl;
    videoInterviews: FeatureControl;
    interviews: FeatureControl;
    projects: FeatureControl;
    consents: FeatureControl;
    tasks: FeatureControl;
    actions: FeatureControl;
    candidates: FeatureControl;
    landings: FeatureControl;
    tags: FeatureControl;
    audits: FeatureControl;
    dropoutReasons: FeatureControl;
    scorecards: FeatureControl;
    meetingAnalysis: FeatureControl;
    requisitions: FeatureControl;
    projectTemplates: FeatureControl;
    forms: FeatureControl;
    fonts: FeatureControl;
    templates: FeatureControl;
    integrations: FeatureControl;
    teams: FeatureControl;
}

interface FeatureSettings {
    video_interviews: boolean;
    requisitions: boolean;
    enable_ai: boolean;
    teams: boolean;
    career_pages: boolean;
    references: boolean;
    sso: boolean;
    job_ads_ip_limit: boolean;
    scim: boolean;
    audit_log: boolean;
    dump_database: boolean;
    ai_transcripts: boolean;
    edit_all_comments: boolean;
    credential_validity: boolean;
    webhook_actions: boolean;
    scorecards: boolean;
    ai_candidate_screening: boolean;
}

export type CandidateFieldType = "text" | "select" | "tags" | "checkbox" | "datetime" | "location" | "file_type";

interface CandidateCustomField {
    key: string;
    label: string;
    type: CandidateFieldType;
    items: BSelectOption[];
    visibility: null | "hidden";
}

interface AllSettings {
    candidate_custom_fields: CandidateCustomField[];
    cc_last_employment: boolean;
    cc_last_education: boolean;
    cc_tags: boolean;
    cc_indicator_comments: boolean;
    cc_indicator_invites: boolean;
    cc_indicator_messages: boolean;
    cc_indicator_video_invites: boolean;
    cc_indicator_references: boolean;
    cc_email: boolean;
    cc_phone: boolean;
    cc_other_active_candidacies: boolean;
    cc_location: boolean;
    cc_source: boolean;
    cc_show_initials_in_blind_mode: boolean;
    mark_underage_candidates: boolean;
    underage_age_under: number;
    sms_enabled: boolean;
    enable_permanent_delete: boolean;
    organization_type: "agency" | "corporation";
    organization_address: string;
    features: FeatureSettings;
    controls: FeatureControls;
    user: User;
    uf_token: string;
    stripe_key: string;
    instance_name: string;
    instance_uuid: string;
    pusher_key: string;
    pusher_host: string;
    timezone: string;
    consent_automation_enabled: boolean;
    consent_automation_send_renewals: boolean;
    survey_question: string;
    environment: "production" | string;
    allow_regular_users_create_tags: boolean;
    ask_for_dropout_reason: boolean;
    use_custom_stage_categories: boolean;
    hide_files_from_limited_users: boolean;
    enable_internal_landings: boolean;
    default_stages: Stage[];
    privacy_policy_url: string;
    is_superadmin: boolean;
    allow_without_email: boolean;
    show_all_project_logs_to_limited: boolean;
    require_project_failure_reasons: boolean;
    is_support_session: boolean;
    projects_private_by_default: boolean;
}

interface Website extends Model {
    features: FeatureSettings;
}

declare module "vue/types/vue" {
    // 3. Declare augmentation for Vue
    interface Vue {
        $rlSettings: AllSettings;
        $echo: Echo<"pusher">;
        route: typeof route;
        $style: Record<string, string>;
    }
}
