declare module '*.vue' {
    import Vue from 'vue'

    export default Vue
    export type VueInstance<Data = unknown, Props = unknown, Instance = unknown, Options = unknown, Emit = unknown>
        = Vue<Data, Props, Instance, Options, Emit>;
}

declare module 'vue-text-highlight';
declare module 'vue-chat-scroll';
declare module 'v-lazy-component/vue2';
declare module 'vue-cal';
declare module 'font-picker-vue';
declare module 'vue-stories-instagram';
declare module 'vue-insta-stories';
declare module 'vue-swatches';
declare module 'vue-tel-input';
declare module 'vue-text-mask';
declare module 'vue-server-renderer/basic';
declare module '@kevinfaguiar/vue-twemoji-picker';
declare module 'prismjs/components/prism-core';
declare module 'vue-prism-editor';
declare module 'vue-js-toggle-button/src/Button.vue';
declare const Sentry: typeof import("@sentry/browser");
declare module '@laraform/laraform/src/utils/condition' {
    import {LaraformForm} from "@laraform/laraform/src";

    export function check(condition: unknown[], path: string | null, form: LaraformForm): boolean
}
declare module '@laraform/laraform/src/mixins/HasConditions'
declare module '@laraform/laraform/src/utils/validation/factory'
declare module '@laraform/laraform/src/components/elements/TextElement.vue'
declare module '@laraform/laraform/src/components/elements/TrixElement'
declare module '@laraform/laraform/src/components/wrappers/Trix'
declare module '@laraform/laraform/src/components/wrappers/Flatpickr'
declare module '@laraform/laraform/src/utils/validation/rules/rule';
declare module '@laraform/laraform/src/utils/validation/rules/helpers';
declare module '@laraform/laraform/src/utils/validation/parse';
declare module '@laraform/laraform/src/directives/sortable';

declare module 'object-fill-missing-keys' {
    export default function fillMissing<T>(toFill: T, template: T): T;
}


interface LaraformPayload {
    updates: object[]
    redirect_url?: string
}

interface LaraformResponse {
    status: string,
    messages: string[],
    payload: LaraformPayload,
}

interface DraggableMoved<T> {
    element: T,
    oldIndex: number,
    newIndex: number,
}

interface DraggableRemoved<T> {
    element: T,
    oldIndex: number
}

interface DraggableAdded<T> {
    element: T,
    newIndex: number,
}

interface DraggableChange<T> {
    moved?: DraggableMoved<T>,
    removed?: DraggableRemoved<T>,
    added?: DraggableAdded<T>,
}

interface StageTransition {
    stage?: { id: number | null, category?: number | null },
    change: DraggableChange<Model>,
}

interface StageTransitionPayload {
    candidate_id: number | null,
    application_id: number | null,
    to_stage_id: number | null,
}

interface ModelUpdates {
    updates: { [key: string]: null | number | boolean | string | object }
}

type VueClassBinding =
    | string
    | string[]
    | Record<string, boolean>
    | Array<string | Record<string, boolean>>

declare module 'tiptap';
declare module 'tiptap-extensions';
declare module 'tiptap-commands';
declare module 'vue-flatpickr-component';
declare module 'colorthief';
declare module 'Bulletlist';
declare module 'vue-clickaway';
declare module 'vue-resizable';

declare module '@laraform/laraform/src/mixins/BaseElement';
declare module '@laraform/laraform/src/mixins/BaseValidation';
declare module '@laraform/laraform/src/mixins/HasFileDrop';
declare module '@laraform/laraform/src/utils';
declare module '@laraform/laraform/src/themes/bs4';
declare module '@laraform/laraform/src/utils/condition';
declare module '@laraform/laraform/src/index.js';
declare module '@laraform/laraform';

declare module 'laravel-vue-pagination' {
    import Vue, {ComponentOptions} from 'vue';

    const laravelVuePaginationComponent: ComponentOptions<Vue>;

    export default Vue;
}

declare module 'trix';

declare let context: any;
