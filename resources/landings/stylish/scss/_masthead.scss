.masthead {
    min-height: 30rem;
    position: relative;
    display: table;
    width: 100%;
    height: auto;
    padding-top: 8rem;
    padding-bottom: 8rem;
    //background: linear-gradient(90deg, fade-out($white, 0.9) 0%, fade-out($white, 0.9) 100%), url('../img/bg-masthead.jpg');
    background-position: center center;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-size: cover;

    h1 {
        font-size: 2.75rem;
        margin: 0;
        padding: 0;
    }
    h3{
        font-size: 1.25rem;
    }

    @media (min-width: 992px) {
        min-height: 100vh;
        h1 {
            font-size: 4.5rem;
        }
        h3 {
            font-size: 1.75rem;
        }
    }
}
