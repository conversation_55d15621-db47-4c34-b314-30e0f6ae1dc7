.side-nav {
    border: $border-width solid $border-color;
    transition: border-color cubic-bezier(0.65, 0, 0.35, 1) 0.5s, margin cubic-bezier(0.65, 0, 0.35, 1) 0.5s, width cubic-bezier(0.65, 0, 0.35, 1) 0.5s;

    .btn {
        z-index: 99;
    }

    .nav-link {
        padding: spacer(1) spacer(2);
        margin-left: -1 * spacer(2);
        margin-right: spacer(1);
        border-radius: spacer(2);
        transition: background-color 0.2s ease-in-out;
        display: flex;
        align-items: center;

        &.active {
            background-color: $info;
        }

        &.router-link-exact-active {
            .btn-circle {
                @include button-variant($info, $info);
            }
        }
    }

    span {
        transition: opacity cubic-bezier(0.5, 0, 0.5, 1) 0.5s, margin cubic-bezier(0.65, 0, 0.35, 1) 0.5s, padding cubic-bezier(0.65, 0, 0.35, 1) 0.5s;
        overflow: hidden;
        opacity: 1;
        padding-right: spacer(3);
        white-space: nowrap;
        font-weight: $font-weight-medium;
        width: 138px;
    }

    &--minimized {
        border-color: transparent;
        margin-right: 0 !important;

        span {
            opacity: 0;
            margin-left: -150px !important;
            padding-right: 0 !important;
        }
    }

    &--maximized  {
        span {
            width: unset;
        }
    }

    &:not(.side-nav--minimized) {
        .nav-link {
            &:hover:not(.active) {
                background-color: $lighter;
            }
        }
    }
}
