.table-td {
    border-spacing: 0 spacer(2);
    border-collapse: separate;
    font-size: $font-size-sm;
    position: relative;

    thead {
        th {
            font-weight: $font-weight-normal;
            border: unset;
            padding: spacer(1) spacer(25);
        }
    }

    tbody {
        th {
            font-weight: $font-weight-medium;
            border: unset;
            padding: spacer(3) spacer(25) spacer(1);
        }
    }

    tfoot {
        th {
            color: $dark;
            font-weight: $font-weight-medium;
            border: unset;
            padding: spacer(1) spacer(25);
        }
    }

    tr {
        td {
            border: 0;
            transition: background-color 0.2s ease-in-out;

            &:not([colspan]) {
                background-color: $lighter;
                vertical-align: middle;
                padding-top: spacer(2);
                padding-bottom: spacer(2);

                &:first-child {
                    border-radius: $border-radius-lg 0 0 $border-radius-lg;
                    font-weight: $font-weight-medium;
                    color: $dark;
                }

                &:last-child {
                    border-radius: 0 $border-radius-lg $border-radius-lg 0;
                }
            }

            &.table-td-gap {
                height: 10px;
                padding-top: 0;
                padding-bottom: 0;
            }

            .table-td-buttons {
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: spacer(2);
                white-space: nowrap;
                flex-wrap: wrap;
            }
        }

        &.landing-pinned {
            td {
                &:not([colspan]) {
                    &:first-child {
                        position: relative;

                        &::after {
                            content: '';
                            width: 0;
                            height: 0;
                            border-style: solid;
                            position: absolute;
                            border-width: 0 0 1rem 1rem;
                            border-color: transparent transparent transparent $dark-light;
                            left: 0;
                            top: 0;
                            border-radius: $border-radius-lg 0 0 0;
                        }
                    }
                }
            }
        }

        &.table-td-with-details,
        &.b-table-has-details {
            position: relative;

            td {
                &:first-child {
                    border-bottom-left-radius: 0;

                    &:after {
                        transition: background-color 0.2s ease-in-out;
                        content: '';
                        display: block;
                        width: 100%;
                        height: spacer(2);
                        background-color: $lighter;
                        position: absolute;
                        left: 0;
                        bottom: -1 * spacer(2);
                    }
                }

                &:last-child {
                    border-bottom-right-radius: 0;
                }
            }
        }

        &.table-td-details,
        &.b-table-details {
            td {
                background-color: $lighter;
                vertical-align: middle;
                padding-top: spacer(2);
                padding-bottom: spacer(2);

                &:first-child {
                    border-bottom-left-radius: $border-radius-lg;
                }

                &:last-child {
                    border-bottom-right-radius: $border-radius-lg;
                }
            }
        }

        @each $color, $value in $theme-colors-light {
            &.bg-#{$color} {
                background-color: unset !important;

                td {
                    &:not([colspan]) {
                        background-color: $value;
                    }
                }

                &.table-td-with-details,
                &.b-table-has-details {
                    td {
                        &:first-child {
                            &:after {
                                background-color: $value;
                            }
                        }
                    }

                    + .b-table-details {
                        td {
                            background-color: $value;
                        }
                    }
                }
            }
        }

        @each $color, $value in $theme-colors-light {
            &.bg-#{$color}-lighter {
                background-color: unset !important;

                td {
                    &:not([colspan]) {
                        background-color: lighten($value, 10%);
                    }
                }

                &.table-td-with-details,
                &.b-table-has-details {
                    td {
                        &:first-child {
                            &:after {
                                background-color: lighten($value, 10%);
                            }
                        }
                    }

                    + .b-table-details {
                        td {
                            background-color: lighten($value, 10%);
                        }
                    }
                }
            }
        }
    }

    tbody tr {
        @include hover() {
            td {
                &:not([colspan]) {
                    background-color: darken($lighter, 3%);
                }

                &:first-child {
                    &:after {
                        background-color: darken($lighter, 3%);
                    }
                }
            }

            + .b-table-details {
                td {
                    background-color: darken($lighter, 3%);
                }
            }

            &.b-table-details {
                td {
                    background-color: darken($lighter, 3%);
                }
            }

            @each $color, $value in $theme-colors-light {
                &.bg-#{$color} {
                    background-color: unset !important;

                    td {
                        background-color: darken($value, 3%);

                        &:first-child {
                            &:after {
                                background-color: darken($value, 3%);
                            }
                        }
                    }

                    + .b-table-details {
                        td {
                            background-color: darken($value, 3%);
                        }
                    }

                    &.b-table-details {
                        td {
                            background-color: darken($value, 3%);
                        }
                    }
                }
            }

            .table-td-darker {
                tr {
                    td {
                        &:not([colspan]) {
                            background-color: darken($light, 3%);
                        }
                    }
                }
            }
        }

        @each $color, $value in $theme-colors-light {
            &.bg-#{$color} {
                + .b-table-details {
                    @include hover() {
                        td {
                            background-color: darken($value, 3%);
                        }
                    }
                }
            }
        }

        &:has(+ .b-table-details:hover) {
            td {
                &:not([colspan]) {
                    background-color: darken($lighter, 3%);
                }

                &:first-child {
                    &:after {
                        background-color: darken($lighter, 3%);
                    }
                }
            }

            @each $color, $value in $theme-colors-light {
                &.bg-#{$color} {
                    td {
                        &:not([colspan]) {
                            background-color: darken($value, 3%);
                        }

                        &:first-child {
                            &:after {
                                background-color: darken($value, 3%);
                            }
                        }
                    }
                }
            }
        }
    }

    &.table-top {
        tr {
            td {
                &:not([colspan]) {
                    vertical-align: top;
                }
            }
        }
    }

    &.table-middle {
        tr {
            td {
                &:not([colspan]) {
                    vertical-align: middle;
                }
            }
        }
    }

    &.table-td-darker {
        tr {
            td {
                &:not([colspan]) {
                    background-color: $light;
                }
            }
        }

        tbody tr {
            @include hover() {
                td {
                    &:not([colspan]) {
                        background-color: darken($light, 3%);
                    }
                }
            }
        }
    }

    &.table-td-sm {
        border-spacing: 0 spacer(1);
        tr {
            td {
                &:not([colspan]) {
                    padding-top: 0.25rem;
                    padding-bottom: 0.25rem;
                }
            }
        }
    }

    &.table-td-horizontal {
        th, td {
            padding-top: spacer(2);
            padding-bottom: spacer(2);
            background-color: $lighter;

            &:first-child {
                border-radius: $border-radius-lg 0 0 $border-radius-lg;
            }

            &:last-child {
                border-radius: 0 $border-radius-lg $border-radius-lg 0;
            }
        }
    }

    &.table-min-col-100 {
        th, td {
            min-width: 100px;
        }
    }

    &.table-td-no-spacing {
        border-spacing: 0;
    }
}

.table-invites {
    .table-invites__candidate_name {
        width: 50%;
    }

    .table-invites__timeslot {
        width: 25ch;
    }
}

.table-fixed {
    table-layout: fixed !important;
}
