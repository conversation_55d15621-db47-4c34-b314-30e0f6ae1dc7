.svg-funnel-js {
    font-family: $font-family-base !important;

    &:not(.svg-funnel-js--vertical) {
        padding-bottom: 0 !important;
    }

    .svg-funnel-js__labels {
        height: calc(150px + 64px) !important;

        .svg-funnel-js__label {
            display: flex;
            flex-direction: column;

            .label__value {
                color: $dark !important;
                font-size: $font-size-base !important;
                font-weight: $font-weight-bold !important;
                order: 2;
            }

            .label__title {
                color: $dark-light !important;
                font-size: $font-size-base !important;
                font-weight: $font-weight-normal !important;
                order: 1;
                margin-bottom: spacer(2);
            }

            .label__percentage {
                order: 3;
                color: $dark-light !important;
                font-size: $font-size-base !important;
                font-weight: $font-weight-normal !important;
            }

            &.label-1 {
                padding-left: unset !important;
            }

            &:not(:first-child) {
                border-left: 1px solid $gray-400 !important;
            }
        }
    }

    .svg-funnel-js__subLabels {
        padding: spacer(2) spacer(2) 0 0;
        margin-top: spacer(2) !important;
        justify-content: start !important;
        position: unset !important;

        .svg-funnel-js__subLabel {
            color: $dark-light !important;
            font-size: $font-size-xs !important;
        }
    }

}
