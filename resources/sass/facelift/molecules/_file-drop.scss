.file-drop-overlay {
    transition: background 200ms;
    .file-drop-overlay__contents{
        display: none;
    }

    &--active{
        .file-drop-overlay__contents{
            display: block;
        }
        display: flex;
        transition: background 0;
        background: RGBA(65, 106, 130, 0.34);
        position: absolute;
    }


    top: 0;
    bottom: 0;
    background: RGBA(65, 106, 130, 0);

    right: -1.5rem;
    left: -1.5rem;
    color: white;

    align-items: center;
    justify-content: center;
}
