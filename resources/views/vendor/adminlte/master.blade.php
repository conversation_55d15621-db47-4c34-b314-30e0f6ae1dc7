<!DOCTYPE html>
<html prefix="og: http://ogp.me/ns#">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="me" content="Teamdash">
    @if(app()->environment('production') && !request()->is('landings/*') && !request()->is('api/*'))
    {{-- Container removed from landings, because clicking anchor where href was a data uri crashed the browser --}}
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-54S2THF');</script>
    <!-- End Google Tag Manager -->
    @endif
    @if(!request()->is('i/*') && !request()->is('p/*') && !request()->is('api/*') && !request()->is('public/*'))
{{--        <script async defer src="https://js.jam.dev/support/4efd8df0-4f22-40a4-9fdf-27bbc0454a32.js"></script>--}}
        @if(app()->environment('production'))
            <script>
                !function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on","addSourceMiddleware","addIntegrationMiddleware","setAnonymousId","addDestinationMiddleware"];analytics.factory=function(e){return function(){var t=Array.prototype.slice.call(arguments);t.unshift(e);analytics.push(t);return analytics}};for(var e=0;e<analytics.methods.length;e++){var key=analytics.methods[e];analytics[key]=analytics.factory(key)}analytics.load=function(key,e){var t=document.createElement("script");t.type="text/javascript";t.async=!0;t.src="https://cdn.segment.com/analytics.js/v1/" + key + "/analytics.min.js";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n);analytics._loadOptions=e};analytics._writeKey="TyaxeHvbHhK6VHZ3EgDwuK4HpBFS7A4h";;analytics.SNIPPET_VERSION="4.15.3";
                    analytics.load("TyaxeHvbHhK6VHZ3EgDwuK4HpBFS7A4h");
                    analytics.page(null, null, {
                        server_response_ms: {{(int)((microtime(true) - LARAVEL_START) * 1000)}}
                    });
                }}();
            </script>
        @else
            <script>
                window.analytics = {
                    track: function(...args){
                        console.log('track', args)
                        if(args[2]){
                            args[2]();
                        }
                    },
                    identify: function(...args){console.log('identify', args)},
                    page: function(...args){console.log('page', args)},
                };
                analytics.page();
            </script>
        @endif
    @else
        <script type="text/javascript">
            !function(){"use strict";window.RudderSnippetVersion="3.0.6";var sdkBaseUrl="{{url('rsa')}}"
            ;var sdkName="page.min.js";var asyncScript=true;window.rudderAnalyticsBuildType="legacy",window.rudderanalytics=[]
            ;var e=["setDefaultInstanceKey","load","ready","page","track","identify","alias","group","reset","setAnonymousId","startSession","endSession","consent"]
            ;for(var n=0;n<e.length;n++){var t=e[n];window.rudderanalytics[t]=function(e){return function(){
                window.rudderanalytics.push([e].concat(Array.prototype.slice.call(arguments)))}}(t)}try{
                new Function('return import("")'),window.rudderAnalyticsBuildType="modern"}catch(a){}
                if(window.rudderAnalyticsMount=function(){
                    "undefined"==typeof globalThis&&(Object.defineProperty(Object.prototype,"__globalThis_magic__",{get:function get(){
                            return this},configurable:true}),__globalThis_magic__.globalThis=__globalThis_magic__,
                        delete Object.prototype.__globalThis_magic__);var e=document.createElement("script")
                    ;e.src="".concat(sdkBaseUrl,"/").concat(window.rudderAnalyticsBuildType,"/").concat(sdkName),e.async=asyncScript,
                        document.head?document.head.appendChild(e):document.body.appendChild(e)
                },"undefined"==typeof Promise||"undefined"==typeof globalThis){var d=document.createElement("script")
                ;d.src="https://polyfill-fastly.io/v3/polyfill.min.js?version=3.111.0&features=Symbol%2CPromise&callback=rudderAnalyticsMount",
                    d.async=asyncScript,document.head?document.head.appendChild(d):document.body.appendChild(d)}else{
                    window.rudderAnalyticsMount()}window.rudderanalytics.load("2hpiT7w0y2ZXy73IYwkUTKabTFC","https://rudder.rlb.ee",{destSDKBaseURL: "{{url('/v3/modern/js-integrations')}}", pluginsSDKBaseURL: "{{url('/rsa/modern/plugins')}}"})}();
        </script>
    @endif

    <title inertia>@yield('title', config('adminlte.title'))@yield('title_postfix', config('adminlte.title_postfix'))</title>
    <x-google-website-data></x-google-website-data>

    @if(!isset($isPublicLanding))
        @if(!Route::is(['job-ad-builder', 'job-ad-builder.*', 'landings.v2.create', 'landings.edit']))
            @vite('resources/sass/facelift/app.scss')
        @else
            @vite('resources/sass/app.scss')
        @endif
    @else
        @if(isset($loadHashlessStyles) && $loadHashlessStyles && !app()->environment('local'))
            {{--
            Our partner CV-Keskus caches the landing HTML. When Vite's default app.<hash>.css is cached by CVK and we
            update code, the referenced file ceases to exist in prod. That would cause style-less landings to be
            showed in CVK.

            So in our vite config we copy the app.<hash>.css to app.css. The hash is added to query string for
            cache busting.

            This is't repeated with js since they just strip all script tags from the HTML 🤗.
            --}}
            <?php
                $viteFilename = \Illuminate\Support\Facades\Vite::asset('resources/sass/app.scss');
                preg_match('/app\.(.*?)\.css/', $viteFilename, $matches);
                $viteHash = $matches[1];
            ?>
            <link rel="stylesheet" href="{{url('/css/app.css')}}?v={{$viteHash}}">
        @else
            @vite('resources/sass/app.scss')
        @endif
    @endif
    @yield('user_css')
    @yield('head_extra')
    {!! \Sentry\Laravel\Integration::sentryMeta() !!}
</head>
<body class="@yield('classes_body')" @yield('body_data')>
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-54S2THF"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
@yield('body')

<div id="snowflakes"></div>

@if(! config('adminlte.enabled_laravel_mix'))
<script src="{{ asset('vendor/jquery/jquery.min.js') }}"></script>
<script src="{{ asset('vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
<script src="{{ asset('vendor/overlayScrollbars/js/jquery.overlayScrollbars.min.js') }}"></script>

@include('adminlte::plugins', ['type' => 'js'])

@yield('adminlte_js')
@else
@vite('resources/js/app.js')
@if(!isset($isPublicLanding))
@endif
@yield('js')
@endif
@if(app()->environment('local'))
@verbatim
    <script type="module" src="http://localhost:5173/@vite-plugin-checker-runtime-entry"></script>
@endverbatim
@endif


<script>
    var topMenu = document.getElementById('menu-drop');
    if(topMenu){
        topMenu.addEventListener('blur', function(e){
            if(!e.relatedTarget){
                e.target.nextElementSibling.classList.remove('show');
            }
        });
    }

    @if(!request()->fullUrlIs('*public*') && !request()->fullUrlIs('*/i/*') && false)
    var snowTimer;
    var snowing = false;

    $(document).bind('mousemove click keydown', function(){
        if(snowing){
            clearSnow();
        }
        clearTimeout(snowTimer);
        prepareToSnow();
    });

    function prepareToSnow(){
        snowTimer = setTimeout(createSnow, 10000);
    }

    function createSnow(){
        var snowContainer = document.getElementById('snowflakes');
        for (var i = 0; i < 100; i++) {
            var el = document.createElement('div');
            el.className = 'snowflake';
            snowContainer.appendChild(el);
        }
        document.body.classList.add('snowing');
        snowing = true;
    }

    function clearSnow(){
        document.getElementById('snowflakes').innerHTML = '';
        document.body.classList.remove('snowing');
    }
    prepareToSnow();
    @endif
</script>

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-54S2THF"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<script>
    if (typeof analytics !== 'undefined' && window && window.settings && window.settings.user) {
        analytics.identify(
            window.settings.user.email,
            {
                name: window.settings.user.name,
                email: window.settings.user.email,
                role: window.settings.user.role,
                instance: window.settings.instance_name
            },
            {
                context: {
                    screen: {
                        width: window.innerWidth,
                        height: window.innerHeight,
                        density: window.devicePixelRatio,
                    },
                }
            }
        )
    }
</script>
</body>
</html>
