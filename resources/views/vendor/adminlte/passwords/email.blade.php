@extends('adminlte::master')

@section('adminlte_css')
    @yield('css')
@stop

@section('classes_body', 'td-login-page')

@php( $password_email_url = View::getSection('password_email_url') ?? config('adminlte.password_email_url', 'password/email') )
@php( $dashboard_url = View::getSection('dashboard_url') ?? config('adminlte.dashboard_url', 'home') )

@if (config('adminlte.use_route_url', false))
    @php( $password_email_url = $password_email_url ? route($password_email_url) : '' )
    @php( $dashboard_url = $dashboard_url ? route($dashboard_url) : '' )
@else
    @php( $password_email_url = $password_email_url ? url($password_email_url) : '' )
    @php( $dashboard_url = $dashboard_url ? url($dashboard_url) : '' )
@endif

@section('body')
    <div class="login-box">
        <div class="login-logo">
            <a href="{{ $dashboard_url }}">
                {{--                {!! config('adminlte.logo', '<b>Admin</b>LTE') !!}--}}
                <img
                    src="{{ config('adminlte.logo_img') }}"
                    alt="{{ config('adminlte.logo_img_alt') }}"
                    class="login-box-logo"
                >
            </a>
        </div>
        <div class="card login-card">
            <div class="card-body login-card-body">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <h3>{{ __('Create a new password') }}</h3>
                </div>
                @if (session('status'))
                    <div class="alert alert-success">
                        {{ session('status') }}
                    </div>
                @else
                    <form action="{{ $password_email_url }}" method="post">
                        {{ csrf_field() }}
                        <div class="form-group mb-3">
                            <input
                                type="email"
                                name="email"
                                class="form-control {{ $errors->has('email') ? 'is-invalid' : '' }}"
                                value="{{ old('email') }}"
                                placeholder="{{ __('Email') }}"
                                autofocus
                            >
                            @if ($errors->has('email'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('email') }}
                                </div>
                            @endif
                        </div>
                        <div class="d-flex align-items-center justify-content-center">
                            <button type="submit" class="btn btn-primary justify-content-center">
                                {{ __('Send Password Reset Link') }}
                            </button>
                        </div>
                    </form>
                @endif
            </div>
        </div>
    </div>
@stop

@section('adminlte_js')
    <script src="{{ asset('vendor/adminlte/dist/js/adminlte.min.js') }}"></script>
    @stack('js')
    @yield('js')
@stop
