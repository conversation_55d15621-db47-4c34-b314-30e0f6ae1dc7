@extends('layouts.settings')

@section('content_header')
@stop

@section('settings_header')
    <a href="{{route('references.create')}}" class="btn btn-primary btn-sm">
        <i class="tdi td-plus mr-2"></i>
        {{ __('Add reference form') }}
    </a>
@endsection

@section('settings_content')
    @include('flash::message')

    <table class="table table-td">
        <thead>
            <tr>
                <th>{{ __('Name') }}</th>
                <th></th>
            </tr>
        </thead>
        @foreach($referenceForms as $referenceForm)
            <tr>
                <td>
                    <a href="{{route('references.edit', $referenceForm->id)}}">
                        {{$referenceForm->title}}
                    </a>
                </td>
                <td class="text-right">
                    <b-dropdown size="sm" variant="white" no-caret>
                        <template v-slot:button-content>
                            {{ __('Actions') }}
                            <small class="ml-2">
                                <i class="fas fa-chevron-down"></i>
                            </small>
                        </template>
                        @if(!$referenceForm->submissions_only_visible_to_admins || auth()->user()->role === 'admin')
                            <b-dropdown-item href="{{route('references.submissions', $referenceForm->id)}}">
                                {{ __('Submissions') }}
                            </b-dropdown-item>
                            <b-dropdown-item href="{{route('references.download', $referenceForm->id)}}">
                                {{ __('Download submissions') }}
                            </b-dropdown-item>
                        @endif
                        <b-dropdown-item href="{{route('references.edit', $referenceForm->id)}}">
                            {{ __('Edit') }}
                        </b-dropdown-item>
                    </b-dropdown>
                </td>
            </tr>
        @endforeach
    </table>
    <div class="mt-3 d-flex justify-content-center">
        {!! $referenceForms->appends(request()->except('page'))->render() !!}
    </div>
@stop

@section('js')
    @vite('resources/js/project/index.ts')
@stop
