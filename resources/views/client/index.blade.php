@extends('adminlte::page')

@section('title', __('Clients'))
@section('content_header')
@stop

@section('content')
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <h2 class="mb-0">
                            {{ __('Clients') }}
                        </h2>
                        <div class="ml-auto d-flex align-items-center">
                            <a
                                href="{{route('clients.create')}}"
                                class="btn btn-primary btn-sm"
                            >
                                <i class="tdi td-plus mr-2"></i>
                                {{ __('Add client') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-td">
                            <thead>
                                <tr>
                                    <th>
                                        {{ __('Client name') }}
                                        @if(isset($order) && $order === 'name')
                                            @if($orderDirection === 'asc')
                                                <a href="{{route('clients.index', array_merge(request()->except(['order', 'dir']), ['order' => 'name', 'dir' => 'desc']))}}">
                                                    <i class="tdi td-arrow-up ml-2"></i>
                                                </a>
                                            @else
                                                <a href="{{route('clients.index', array_merge(request()->except(['order', 'dir']), ['order' => 'name', 'dir' => 'asc']))}}">
                                                    <i class="tdi td-arrow-down ml-2"></i>
                                                </a>
                                            @endif
                                        @endif
                                    </th>
                                    <th>{{ __('Managing recruiter') }}</th>
                                    <th>{{ __('Active projects') }}</th>
                                    <th>{{ __('Total projects') }}</th>
                                    <th></th>
                                </tr>
                            </thead>
                            @foreach($clients as $client)
                                <tr>
                                    <td>
                                        <a href="{{route('clients.show', $client->id)}}">{{$client->name}}</a>
                                    </td>
                                    <td>{{$client->manager ? $client->manager->name : ''}}</td>
                                    <td>{{$client->active_projects_count}}</td>
                                    <td>{{$client->projects_count}}</td>
                                    <td>
                                        <div class="d-flex align-items-center justify-content-end">
                                            <a
                                                href="{{route('clients.edit', $client->id)}}"
                                                class="btn btn-sm btn-white"
                                            >
                                                {{ __('Edit') }}
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </table>
                        <div class="mt-3 d-flex justify-content-center">
                            {!! $clients->appends(request()->except('page'))->render() !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@stop

@section('js')
    @vite('resources/js/project/index.ts')
@stop
