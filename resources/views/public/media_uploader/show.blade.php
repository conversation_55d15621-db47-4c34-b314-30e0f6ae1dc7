@extends('public.layout')

@section('content_header')

@stop


@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="card">
                <div class="card-body">
                    <form action="" method="post" id="form"
                          enctype="multipart/form-data">
                        <input type="file" accept="video/*"
                               name="file"
                               id="video"
                               ref="videoInput">
                        <button type="submit" id="ulbtn">Upload</button>
                    </form>

                </div>
            </div>
        </div>
    </div>
    <script>
        function getOneTimeUploadUrl() {
            // The real implementation of this function should make an API call to your server
            // where a unique one-time upload URL should be generated and returned to the browser.
            // Here we will use a fake one that looks real but won't actually work.
            return "{{data_get($cf, 'result.uploadURL')}}";
        }

        const form = document.getElementById("form");
        const videoInput = document.getElementById("video");
        const btn = document.getElementById('ulbtn');

        form.addEventListener("submit", async (e) => {
            e.preventDefault();
            btn.disabled = true;
            const oneTimeUploadUrl = getOneTimeUploadUrl();
            const video = videoInput.files[0];
            const formData = new FormData();
            formData.append("file", video);
            const uploadResult = await fetch(oneTimeUploadUrl, {
                method: "POST",
                body: formData,
            });
            form.innerHTML = "<h3>Upload successful!</h3>"
        });
    </script>
@stop
