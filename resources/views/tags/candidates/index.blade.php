@extends('layouts.settings')

@section('title', 'Tags')

@section('content_header')
@stop

@section('settings_header')
    <a
        v-if="candidateTags.length > 1"
        @click="openTagMergeModal"
        class="ml-auto mr-2 btn btn-outline-dark btn-sm"
    >
        {{ __('Merge tags') }}
    </a>
    <a
        href="{{route('settings.tags.candidates.create')}}"
        class="btn btn-primary btn-sm"
    >
        <i class="tdi td-plus mr-2"></i>
        {{ __('Create tag') }}
    </a>
@endsection

@section('settings_tabs')
    @include('tags.tabs')
@endsection

@section('settings_content')
    <table class="table table-td">
        <thead>
            <tr>
                <th>{{ __('Name') }}</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr
                v-for="tag in candidateTags"
            >
                <td>
                    <candidate-tag
                        :tag="tag"
                        colorable
                    ></candidate-tag>
                </td>
                <td>
                    <div class="d-flex align-items-center justify-content-end gap-2">
                        <a
                            :href="`/settings/tags/candidates/${tag.id}/edit`"
                            class="btn btn-white btn-sm"
                        >
                            {{ __('Edit') }}
                        </a>
                        <form
                            :action="`/settings/tags/candidates/${tag.id}`"
                            class="d-inline"
                            method="post"
                        >
                            @csrf
                            @method('delete')
                            <button
                                class="btn btn-white-danger btn-sm"
                                onclick="return confirm(`Are you sure you want to delete this tag? It is currently associated with ${tag.candidates_count} candidates.`)"
                            >
                                {{ __('Delete') }}
                            </button>
                        </form>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
    <form-modal
        title="Merge tags"
        v-model="isTagMergeModalOpen"
        form-name="CandidateTagMergeForm"
        @success="reloadPage"
        :label-save="$t('Merge')"
    >
    </form-modal>
@stop

@section('js')
    @vite('resources/js/project/index.ts')
@stop
