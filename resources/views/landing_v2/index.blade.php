@props(['fonts' => collect()])
@extends('adminlte::master')

@section('classes_body', 'landing-builder')
@section('user_css')
    <x-fonts-declaration :fonts="$fonts"/>
@endsection

@section('body')
    <style>
        body {
            -webkit-font-smoothing: antialiased;
            text-rendering: geometricPrecision;
        }
    </style>
    <div id="app" :class="{printing: showFullScreen}"
         class="edit-page"
         v-cloak>
        <header class="header">
            <b-navbar toggleable="sm" type="light" variant="custom" class="header__nav p-0">
                <b-navbar-toggle target="navBuilderCollapse"></b-navbar-toggle>
                <b-collapse id="navBuilderCollapse" class="header__popup" is-nav>
                    <a role="button" @click="goBackInHistoryOrTo('/landings')" class="header__link header__link--collapsible">
                        <small class="mr-2"><i class="fas fa-sign-out-alt fa-flip-horizontal"></i></small>
                        {{ __('Exit') }}
                    </a>
                    <div>
                        <b-button
                            variant="transparent"
                            @click="toggleThemeEditor()"
                        >
                            {{ __('Theme') }}
                            <i class="tdi td-chevron-down ml-2"></i>
                        </b-button>
                    </div>
                    <a href="#" class="header__link toggle justify-content-center mr-0 d-none d-lg-flex"
                       :class="{'is-active': !isMobile}"
                       v-b-popover.hover.bottom="'{{ __('Desktop View') }}'"
                       @click.prevent="isMobile = false"
                    >
                        <small class="m-2"><i class="fas fa-lg fa-desktop"></i></small>
                    </a>
                    <a href="#" class="header__link toggle justify-content-center ml-0 d-none d-lg-flex"
                       :class="{'is-active': isMobile}"
                       v-b-popover.hover.bottom="'{{ __('Mobile View') }}'"
                       @click.prevent="isMobile = true"
                    >
                        <small class="m-2"><i class="fas fa-lg fa-mobile-alt"></i></small>
                    </a>
                    <a href="#"
                       class="header__link toggle justify-content-center ml-0 d-none d-lg-flex"
                       v-b-popover.hover.bottom="'{{ __('Choose template') }}'"
                       @click.prevent="toggleTemplatePicker()"
                       :class="showTemplatePicker ? 'is-active' : null"
                    >
                        <small class="m-2"><i class="fas fa-lg fa-clipboard-list"></i></small>
                    </a>
                    <a class="header__link toggle justify-content-center mr-0 d-none d-lg-flex"
                        href="#"
                       :class="showHistory ? 'is-active' : null"
                       @click.prevent="toggleHistory()"
                    >
                        <i class="fas fa-history m-2"></i>
                    </a>
                    <b-dropdown
                        v-b-popover.hover.bottom="downloadImageURL ? $t('Download as image') : $t('Publish the landing page to download as image')"
                        no-caret
                        variant="link"
                        toggle-class="header__link toggle justify-content-center ml-0 d-none d-lg-flex"
                        :disabled="!downloadImageURL"
                    >
                        <template v-slot:button-content>
                            <small class="m-2"

                            ><i class="fas fa-lg fa-download"></i></small>
                        </template>
                        <b-dropdown-text style="min-width: 300px;">
                            <b-tabs class="mx-n2">
                                <b-tab :title="$t('Image')">
                                    <div class="form-group mt-2">
                                        <label for="" class="control-label">{{ __('Width') }}</label>
                                        <input type="number" class="form-control form-control-sm form-control--square"
                                               v-model="downloadWidth">
                                    </div>

                                    <div class="form-group font-weight-normal">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="sizePreference" id="preferSmallFile" value="smallfile" v-model="downloadPreference">
                                            <label class="form-check-label" for="preferSmallFile">
                                                {{ __('Prefer small file size') }}
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="sizePreference" id="preferQuality" value="quality" v-model="downloadPreference">
                                            <label class="form-check-label" for="preferQuality">
                                                {{ __('Prefer high quality') }}
                                            </label>
                                        </div>
                                    </div>

                                    <a
                                        class="btn btn-sm btn-white-primary"
                                        :href="downloadImageURL"
                                        target="_blank"
                                    >
                                        {{ __('Download') }}
                                    </a>
                                </b-tab>
                                <b-tab :title="$t('String')">
                                    <page-string-import-export
                                        class="mt-2"
                                        @update:page="page = $event"
                                        :page="page"></page-string-import-export>
                                </b-tab>
                            </b-tabs>

                        </b-dropdown-text>
                    </b-dropdown>
                </b-collapse>
            </b-navbar>
            <a class="header__logo" href="/">
                <img class="header__image header__image--mobile img-fluid d-sm-none" src="/android-chrome-192x192.png"
                     alt="Teamdash initials">
                @if($logoUrl = \App\Models\Setting::get(\App\Models\Setting::KEY_LOGO_URL))
                    <img class="header__image img-fluid d-none d-sm-flex" src="{{$logoUrl}}" alt="Teamdash Logo">
                @else
                    <img class="header__image img-fluid d-none d-sm-flex" src="/img/teamdash_logo_black.svg"
                         alt="Teamdash Logo">
                @endif
            </a>
            <nav class="header__nav">
                <span class="header__link text-muted">
                    <small v-if="draftStatus === 'dirty'">{{ __('Unsaved changes') }}</small>
                    <small v-if="draftStatus === 'saved'">
                        {{ __('Draft saved') }}
                        <i class="fas fa-check ml-2 text-muted"></i> <br> {{ __('but not public') }}</small>
                    <small v-if="draftStatus === 'saving'">{{ __('Saving draft...') }}</small>
                </span>
                <span class="header__link">
                    <label class="switch" for="previewMode">
                        <input id="previewMode" class="switch__el" type="checkbox" v-model="editing" :true-value="false"
                               :false-value="true">
                        <span class="switch__holder">
                            <span class="switch__button"></span>
                        </span>
                        <span class="switch__text">
                            <span class="d-none d-md-flex">{{ __('Preview') }}</span>
                            <small class="d-md-none"><i class="fas fa-lg fa-eye"></i></small>
                        </span>
                    </label>
                </span>
                <button @click="showPublishSidebar = true"
                        class="btn header__button ml-3"
                        v-if="user"
                >
                    <span class="text-white mr-2 d-none d-sm-none d-md-inline">{{ __('Publish') }}</span>
                    <small class="text-white"><i class="fas fa-rocket"></i></small>
                </button>
                <button @click="showRegisterModal = true"
                        class="btn header__button ml-3"
                        v-else
                >
                    <span class="text-white mr-2 d-none d-sm-none d-md-inline">{{ __('Save') }}</span>
                    <small class="text-white"><i class="fas fa-save"></i></small>
                </button>
            </nav>
        </header>
        <div class="container-fluid">
            <div class="row">
                <div style="margin-top: 100px;"
                     class="col-3 px-3 position-relative"
                     v-if="showTemplatePicker && editing"
                >
                    <div class="position-fixed" style="width: 24vw;">
                        <h2>{{ __('Choose template') }}</h2>
                        <p class="text-muted">{{ __('Click template to preview') }}</p>
                        <template-picker
                            v-model="activeTemplate"
                            @choose="chooseCurrentTemplate()"
                        ></template-picker>
                    </div>

                </div>
                <div style="margin-top: 100px;"
                     class="col-3 px-3 position-relative"
                     v-if="showThemeEditor && editing"
                >
                    <div class="position-fixed" style="width: 24vw; z-index: 100;">
                        <landing-theme
                            :theme="page.theme"
                            :theme-preview="themePreview"
                            :style="{maxHeight: 'calc(100vh - 100px - 24px)'}"
                        ></landing-theme>
                    </div>

                </div>
                <div class="col-3 px-3 position-relative"
                     style="margin-top: 100px;"
                     v-if="showHistory"
                >
                    <div class="position-fixed" style="width: 24vw; z-index: 100;">
                        <landing-history
                            :style="{maxHeight: 'calc(100vh - 100px - 24px)'}"
                            :landing="landing"
                            @history-item="historyPreviewLandingData = $event"
                        ></landing-history>
                    </div>
                </div>
                <div :class="{
                'col-9': narrowBuilder && editing,
                'col-12': !narrowBuilder,
                'd-flex gap-1': showHistory,
            }"
                >
                    <page
                        class="mb-5"
                        v-if="!activeTemplate"
                        :page="page"
                        :landing="landing"
                        :editing="editing"
                        :style="{'box-shadow': showFullScreen ? 'none' : '', 'flex-grow': showHistory ? 1 : 0}"
                        :class="{'is-mobile': isMobile || (downloadingAsImage && downloadWidth < 722 && !editing) , 'saving-image': downloadingAsImage}"
                        :blocks-class="{'saving-image': downloadingAsImage}"
                        :theme-preview="themePreview"
                    ></page>
                    <page
                        class="mb-5"
                        v-else
                        :page="activeTemplate"
                        :editing="false"
                        :style="{'box-shadow': showFullScreen ? 'none' : ''}"
                        :class="{'is-mobile': isMobile}"
                    ></page>
                    <page
                        class="mb-5"
                        v-if="historyPreviewLandingData && showHistory"
                        :page="historyPreviewLandingData"
                        :landing="landing"
                        :editing="false"
                        :style="{'box-shadow': showFullScreen ? 'none' : ''}"
                        :class="{'is-mobile': isMobile || (downloadingAsImage && downloadWidth < 722 && !editing) , 'saving-image': downloadingAsImage}"
                        :blocks-class="{'saving-image': downloadingAsImage}"
                        :theme-preview="themePreview"
                    ></page>
                    <div v-else-if="showHistory" class="flex-grow w100p text-center" style="margin-top: 100px;">
                        {{__('Choose a version from the history to preview')}}
                    </div>
                </div>
            </div>
        </div>
        <settings-sidebar
            :page="page"
            :landing="landing"
            @set-editing="editing = $event"
            @publish="publish()"
            :saving="saving"
            v-model="showPublishSidebar"></settings-sidebar>
        <b-modal size="lg" v-model="showPublishedModal"
                 hide-footer
                 title="{{ __('All is good') }}">
            <div class="text-center mb-4">
                <h2>🎉 {{ __('Your job ad is public!') }} 🎉</h2>
            </div>
            <div class="mb-4">
                <label for="job-ad-url">{{ __('Your job ad is available at:') }}</label>
                <div class="input-group">
                    <input type="text"
                           id="job-ad-url"
                           class="form-control"
                           v-model="landing.permalink" disabled>
                    <div class="input-group-append">
                        <a :href="landing.permalink"
                           target="_blank" class="btn btn-white-secondary">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="text-center mb-4">
                <h4 class="mb-4">{{ __('What next?') }}</h4>
                <button class="btn btn-white-success" @click="redirectToPublishForm()">
                    <i class="fas fa-rocket mr-2"></i> {{ __('Publish to external channels') }}
                </button>
            </div>
        </b-modal>
        <b-modal v-model="showRegisterModal" title="{{ __("You're almost there!") }}" hide-footer>
            <register-form
                :to-cache="{landing_page: page}"
            ></register-form>
        </b-modal>
        <movable-dialog ref="cssDialog">
            <prism-editor class="my-editor"
                          style="width: 400px; height: 300px;"
                          v-model="page.theme.customCss"
                          :highlight="highlighter"
                          line-numbers></prism-editor>
        </movable-dialog>
    </div>
    @include('vars')
@endsection

@section('js')
    <script src="{{url('/js/iframeResizer.min.js')}}"></script>
    @vite('resources/js/landing_v2/index.ts')
@stop
