<html>
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="content-type">
    <style
        type="text/css">@import url('https://themes.googleusercontent.com/fonts/css?kit=fpjTOVmNbO4Lz34iLyptLUXza5VhXqVC6o75Eld_V98');

        .lst-kix_list_19-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_19-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_14-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_14-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_1-0 {
            list-style-type: none
        }

        .lst-kix_list_14-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_14-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_19-4 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_18-5.start {
            counter-reset: lst-ctn-kix_list_18-5 0
        }

        .lst-kix_list_14-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_14-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_19-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_19-3 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_14-6 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_9-3 {
            list-style-type: none
        }

        ul.lst-kix_list_9-4 {
            list-style-type: none
        }

        ul.lst-kix_list_9-1 {
            list-style-type: none
        }

        ul.lst-kix_list_9-2 {
            list-style-type: none
        }

        ul.lst-kix_list_9-7 {
            list-style-type: none
        }

        ul.lst-kix_list_9-8 {
            list-style-type: none
        }

        ul.lst-kix_list_9-5 {
            list-style-type: none
        }

        ul.lst-kix_list_9-6 {
            list-style-type: none
        }

        ul.lst-kix_list_1-3 {
            list-style-type: none
        }

        ul.lst-kix_list_1-4 {
            list-style-type: none
        }

        ul.lst-kix_list_1-1 {
            list-style-type: none
        }

        ul.lst-kix_list_1-2 {
            list-style-type: none
        }

        ul.lst-kix_list_1-7 {
            list-style-type: none
        }

        ul.lst-kix_list_9-0 {
            list-style-type: none
        }

        ul.lst-kix_list_1-8 {
            list-style-type: none
        }

        ul.lst-kix_list_1-5 {
            list-style-type: none
        }

        .lst-kix_list_14-2 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_1-6 {
            list-style-type: none
        }

        ol.lst-kix_list_16-3.start {
            counter-reset: lst-ctn-kix_list_16-3 0
        }

        ul.lst-kix_list_17-1 {
            list-style-type: none
        }

        ul.lst-kix_list_17-0 {
            list-style-type: none
        }

        ul.lst-kix_list_17-8 {
            list-style-type: none
        }

        .lst-kix_list_19-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_17-7 {
            list-style-type: none
        }

        ul.lst-kix_list_17-6 {
            list-style-type: none
        }

        ul.lst-kix_list_17-5 {
            list-style-type: none
        }

        ul.lst-kix_list_17-4 {
            list-style-type: none
        }

        ul.lst-kix_list_17-3 {
            list-style-type: none
        }

        .lst-kix_list_14-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_17-2 {
            list-style-type: none
        }

        .lst-kix_list_19-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-8 > li {
            counter-increment: lst-ctn-kix_list_18-8
        }

        .lst-kix_list_19-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_19-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_5-0 > li:before {
            content: "\0025cf  "
        }

        ol.lst-kix_list_16-5 {
            list-style-type: none
        }

        ol.lst-kix_list_16-6 {
            list-style-type: none
        }

        ol.lst-kix_list_16-7 {
            list-style-type: none
        }

        ol.lst-kix_list_16-8 {
            list-style-type: none
        }

        .lst-kix_list_5-3 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_16-1 {
            list-style-type: none
        }

        ol.lst-kix_list_16-2 {
            list-style-type: none
        }

        .lst-kix_list_5-2 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_16-3 {
            list-style-type: none
        }

        ol.lst-kix_list_16-4 {
            list-style-type: none
        }

        .lst-kix_list_5-1 > li:before {
            content: "o  "
        }

        ol.lst-kix_list_18-0.start {
            counter-reset: lst-ctn-kix_list_18-0 0
        }

        ol.lst-kix_list_16-0 {
            list-style-type: none
        }

        .lst-kix_list_5-7 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_8-4 {
            list-style-type: none
        }

        ul.lst-kix_list_8-5 {
            list-style-type: none
        }

        .lst-kix_list_5-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_5-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_8-2 {
            list-style-type: none
        }

        ul.lst-kix_list_8-3 {
            list-style-type: none
        }

        ul.lst-kix_list_8-8 {
            list-style-type: none
        }

        ul.lst-kix_list_8-6 {
            list-style-type: none
        }

        ul.lst-kix_list_8-7 {
            list-style-type: none
        }

        .lst-kix_list_18-3 > li {
            counter-increment: lst-ctn-kix_list_18-3
        }

        .lst-kix_list_5-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_5-5 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_8-0 {
            list-style-type: none
        }

        ul.lst-kix_list_8-1 {
            list-style-type: none
        }

        ol.lst-kix_list_16-4.start {
            counter-reset: lst-ctn-kix_list_16-4 0
        }

        .lst-kix_list_6-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_6-3 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-0 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) ". "
        }

        .lst-kix_list_6-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_6-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-1 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ". "
        }

        .lst-kix_list_18-2 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_6-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_6-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_16-1 > li {
            counter-increment: lst-ctn-kix_list_16-1
        }

        .lst-kix_list_6-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_6-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_6-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_2-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_7-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_7-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-5 > li {
            counter-increment: lst-ctn-kix_list_18-5
        }

        .lst-kix_list_2-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_7-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-6 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ".\0025cf ." counter(lst-ctn-kix_list_18-3, decimal) "." counter(lst-ctn-kix_list_18-4, decimal) "." counter(lst-ctn-kix_list_18-5, decimal) "." counter(lst-ctn-kix_list_18-6, decimal) ". "
        }

        ul.lst-kix_list_3-7 {
            list-style-type: none
        }

        ul.lst-kix_list_3-8 {
            list-style-type: none
        }

        .lst-kix_list_10-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_18-4 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ".\0025cf ." counter(lst-ctn-kix_list_18-3, decimal) "." counter(lst-ctn-kix_list_18-4, decimal) ". "
        }

        .lst-kix_list_18-8 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ".\0025cf ." counter(lst-ctn-kix_list_18-3, decimal) "." counter(lst-ctn-kix_list_18-4, decimal) "." counter(lst-ctn-kix_list_18-5, decimal) "." counter(lst-ctn-kix_list_18-6, decimal) "." counter(lst-ctn-kix_list_18-7, decimal) "." counter(lst-ctn-kix_list_18-8, decimal) ". "
        }

        .lst-kix_list_13-7 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_3-1 {
            list-style-type: none
        }

        ul.lst-kix_list_3-2 {
            list-style-type: none
        }

        .lst-kix_list_7-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_3-0 {
            list-style-type: none
        }

        ul.lst-kix_list_3-5 {
            list-style-type: none
        }

        ul.lst-kix_list_3-6 {
            list-style-type: none
        }

        ul.lst-kix_list_3-3 {
            list-style-type: none
        }

        ul.lst-kix_list_3-4 {
            list-style-type: none
        }

        .lst-kix_list_10-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_15-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_10-5 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_18-6.start {
            counter-reset: lst-ctn-kix_list_18-6 0
        }

        .lst-kix_list_10-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_11-7 {
            list-style-type: none
        }

        ul.lst-kix_list_11-6 {
            list-style-type: none
        }

        .lst-kix_list_4-1 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_11-5 {
            list-style-type: none
        }

        ul.lst-kix_list_11-4 {
            list-style-type: none
        }

        ul.lst-kix_list_11-3 {
            list-style-type: none
        }

        .lst-kix_list_15-7 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_11-2 {
            list-style-type: none
        }

        ul.lst-kix_list_11-1 {
            list-style-type: none
        }

        ul.lst-kix_list_11-0 {
            list-style-type: none
        }

        .lst-kix_list_9-2 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_19-7 {
            list-style-type: none
        }

        ul.lst-kix_list_19-6 {
            list-style-type: none
        }

        .lst-kix_list_4-3 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_4-5 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_19-5 {
            list-style-type: none
        }

        ul.lst-kix_list_19-4 {
            list-style-type: none
        }

        ul.lst-kix_list_19-3 {
            list-style-type: none
        }

        ul.lst-kix_list_19-2 {
            list-style-type: none
        }

        ul.lst-kix_list_19-1 {
            list-style-type: none
        }

        ul.lst-kix_list_11-8 {
            list-style-type: none
        }

        ul.lst-kix_list_19-0 {
            list-style-type: none
        }

        .lst-kix_list_15-1 > li:before {
            content: "o  "
        }

        ol.lst-kix_list_18-3.start {
            counter-reset: lst-ctn-kix_list_18-3 0
        }

        .lst-kix_list_9-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_15-3 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_19-8 {
            list-style-type: none
        }

        ol.lst-kix_list_18-7 {
            list-style-type: none
        }

        ol.lst-kix_list_18-8 {
            list-style-type: none
        }

        .lst-kix_list_16-2 > li {
            counter-increment: lst-ctn-kix_list_16-2
        }

        .lst-kix_list_16-8 > li {
            counter-increment: lst-ctn-kix_list_16-8
        }

        ol.lst-kix_list_18-3 {
            list-style-type: none
        }

        ol.lst-kix_list_18-4 {
            list-style-type: none
        }

        ol.lst-kix_list_18-5 {
            list-style-type: none
        }

        ol.lst-kix_list_18-6 {
            list-style-type: none
        }

        .lst-kix_list_9-6 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_16-7.start {
            counter-reset: lst-ctn-kix_list_16-7 0
        }

        ol.lst-kix_list_18-0 {
            list-style-type: none
        }

        ol.lst-kix_list_18-1 {
            list-style-type: none
        }

        .lst-kix_list_9-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_11-3 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_18-4.start {
            counter-reset: lst-ctn-kix_list_18-4 0
        }

        ul.lst-kix_list_2-8 {
            list-style-type: none
        }

        .lst-kix_list_12-3 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_11-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_12-1 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_2-2 {
            list-style-type: none
        }

        ul.lst-kix_list_2-3 {
            list-style-type: none
        }

        ul.lst-kix_list_2-0 {
            list-style-type: none
        }

        ul.lst-kix_list_2-1 {
            list-style-type: none
        }

        .lst-kix_list_9-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_2-6 {
            list-style-type: none
        }

        .lst-kix_list_16-3 > li {
            counter-increment: lst-ctn-kix_list_16-3
        }

        .lst-kix_list_1-1 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_2-7 {
            list-style-type: none
        }

        .lst-kix_list_11-7 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_2-4 {
            list-style-type: none
        }

        ul.lst-kix_list_2-5 {
            list-style-type: none
        }

        ul.lst-kix_list_10-0 {
            list-style-type: none
        }

        .lst-kix_list_1-3 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_16-8.start {
            counter-reset: lst-ctn-kix_list_16-8 0
        }

        .lst-kix_list_13-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_10-8 {
            list-style-type: none
        }

        ul.lst-kix_list_10-7 {
            list-style-type: none
        }

        .lst-kix_list_1-7 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_10-6 {
            list-style-type: none
        }

        ul.lst-kix_list_10-5 {
            list-style-type: none
        }

        ul.lst-kix_list_10-4 {
            list-style-type: none
        }

        ul.lst-kix_list_10-3 {
            list-style-type: none
        }

        .lst-kix_list_1-5 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_10-2 {
            list-style-type: none
        }

        ul.lst-kix_list_10-1 {
            list-style-type: none
        }

        .lst-kix_list_13-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_12-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-4 > li {
            counter-increment: lst-ctn-kix_list_18-4
        }

        .lst-kix_list_12-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_2-1 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_18-2 {
            list-style-type: none
        }

        .lst-kix_list_2-3 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_13-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_3-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_5-7 {
            list-style-type: none
        }

        ul.lst-kix_list_5-8 {
            list-style-type: none
        }

        .lst-kix_list_3-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_3-2 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_5-5 {
            list-style-type: none
        }

        ul.lst-kix_list_5-6 {
            list-style-type: none
        }

        .lst-kix_list_8-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_16-0 > li {
            counter-increment: lst-ctn-kix_list_16-0
        }

        .lst-kix_list_8-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_3-5 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_5-0 {
            list-style-type: none
        }

        .lst-kix_list_18-0 > li {
            counter-increment: lst-ctn-kix_list_18-0
        }

        .lst-kix_list_3-4 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_5-3 {
            list-style-type: none
        }

        .lst-kix_list_3-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_5-4 {
            list-style-type: none
        }

        ul.lst-kix_list_5-1 {
            list-style-type: none
        }

        .lst-kix_list_8-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_5-2 {
            list-style-type: none
        }

        .lst-kix_list_8-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_3-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_8-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_8-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_8-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_13-5 {
            list-style-type: none
        }

        ul.lst-kix_list_13-4 {
            list-style-type: none
        }

        ul.lst-kix_list_13-3 {
            list-style-type: none
        }

        .lst-kix_list_3-6 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_13-2 {
            list-style-type: none
        }

        ul.lst-kix_list_13-1 {
            list-style-type: none
        }

        .lst-kix_list_3-7 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_16-6.start {
            counter-reset: lst-ctn-kix_list_16-6 0
        }

        ul.lst-kix_list_13-0 {
            list-style-type: none
        }

        .lst-kix_list_8-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_11-2 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_13-8 {
            list-style-type: none
        }

        .lst-kix_list_11-1 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_13-7 {
            list-style-type: none
        }

        ul.lst-kix_list_13-6 {
            list-style-type: none
        }

        ol.lst-kix_list_16-0.start {
            counter-reset: lst-ctn-kix_list_16-0 0
        }

        .lst-kix_list_11-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_8-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_16-8 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) "." counter(lst-ctn-kix_list_16-3, decimal) "." counter(lst-ctn-kix_list_16-4, decimal) "." counter(lst-ctn-kix_list_16-5, decimal) "." counter(lst-ctn-kix_list_16-6, decimal) "." counter(lst-ctn-kix_list_16-7, decimal) "." counter(lst-ctn-kix_list_16-8, decimal) ". "
        }

        ol.lst-kix_list_18-7.start {
            counter-reset: lst-ctn-kix_list_18-7 0
        }

        .lst-kix_list_16-7 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) "." counter(lst-ctn-kix_list_16-3, decimal) "." counter(lst-ctn-kix_list_16-4, decimal) "." counter(lst-ctn-kix_list_16-5, decimal) "." counter(lst-ctn-kix_list_16-6, decimal) "." counter(lst-ctn-kix_list_16-7, decimal) ". "
        }

        .lst-kix_list_16-6 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) "." counter(lst-ctn-kix_list_16-3, decimal) "." counter(lst-ctn-kix_list_16-4, decimal) "." counter(lst-ctn-kix_list_16-5, decimal) "." counter(lst-ctn-kix_list_16-6, decimal) ". "
        }

        .lst-kix_list_4-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_4-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_17-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_17-1 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_4-8 {
            list-style-type: none
        }

        .lst-kix_list_16-0 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) ". "
        }

        ul.lst-kix_list_4-6 {
            list-style-type: none
        }

        .lst-kix_list_16-1 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) ". "
        }

        ul.lst-kix_list_4-7 {
            list-style-type: none
        }

        .lst-kix_list_16-2 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) ". "
        }

        ul.lst-kix_list_4-0 {
            list-style-type: none
        }

        .lst-kix_list_16-4 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) "." counter(lst-ctn-kix_list_16-3, decimal) "." counter(lst-ctn-kix_list_16-4, decimal) ". "
        }

        ul.lst-kix_list_4-1 {
            list-style-type: none
        }

        .lst-kix_list_16-3 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) "." counter(lst-ctn-kix_list_16-3, decimal) ". "
        }

        .lst-kix_list_16-5 > li:before {
            content: "" counter(lst-ctn-kix_list_16-0, decimal) "." counter(lst-ctn-kix_list_16-1, decimal) "." counter(lst-ctn-kix_list_16-2, decimal) "." counter(lst-ctn-kix_list_16-3, decimal) "." counter(lst-ctn-kix_list_16-4, decimal) "." counter(lst-ctn-kix_list_16-5, decimal) ". "
        }

        ul.lst-kix_list_4-4 {
            list-style-type: none
        }

        ul.lst-kix_list_4-5 {
            list-style-type: none
        }

        ul.lst-kix_list_4-2 {
            list-style-type: none
        }

        ul.lst-kix_list_4-3 {
            list-style-type: none
        }

        ol.lst-kix_list_18-1.start {
            counter-reset: lst-ctn-kix_list_18-1 0
        }

        ol.lst-kix_list_18-8.start {
            counter-reset: lst-ctn-kix_list_18-8 0
        }

        ul.lst-kix_list_12-6 {
            list-style-type: none
        }

        ul.lst-kix_list_12-5 {
            list-style-type: none
        }

        .lst-kix_list_17-7 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_12-4 {
            list-style-type: none
        }

        .lst-kix_list_16-4 > li {
            counter-increment: lst-ctn-kix_list_16-4
        }

        ul.lst-kix_list_12-3 {
            list-style-type: none
        }

        ul.lst-kix_list_12-2 {
            list-style-type: none
        }

        .lst-kix_list_16-7 > li {
            counter-increment: lst-ctn-kix_list_16-7
        }

        ul.lst-kix_list_12-1 {
            list-style-type: none
        }

        .lst-kix_list_17-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_12-0 {
            list-style-type: none
        }

        .lst-kix_list_17-3 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_17-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_17-4 > li:before {
            content: "o  "
        }

        ul.lst-kix_list_12-8 {
            list-style-type: none
        }

        ul.lst-kix_list_12-7 {
            list-style-type: none
        }

        ol.lst-kix_list_16-5.start {
            counter-reset: lst-ctn-kix_list_16-5 0
        }

        .lst-kix_list_17-6 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_7-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_17-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_16-5 > li {
            counter-increment: lst-ctn-kix_list_16-5
        }

        .lst-kix_list_2-6 > li:before {
            content: "\0025aa  "
        }

        ol.lst-kix_list_16-2.start {
            counter-reset: lst-ctn-kix_list_16-2 0
        }

        .lst-kix_list_2-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_2-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_7-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_7-5 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_7-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_7-5 {
            list-style-type: none
        }

        .lst-kix_list_10-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_7-6 {
            list-style-type: none
        }

        .lst-kix_list_18-5 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ".\0025cf ." counter(lst-ctn-kix_list_18-3, decimal) "." counter(lst-ctn-kix_list_18-4, decimal) "." counter(lst-ctn-kix_list_18-5, decimal) ". "
        }

        ul.lst-kix_list_7-3 {
            list-style-type: none
        }

        ul.lst-kix_list_7-4 {
            list-style-type: none
        }

        .lst-kix_list_13-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_13-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-3 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ".\0025cf ." counter(lst-ctn-kix_list_18-3, decimal) ". "
        }

        .lst-kix_list_18-7 > li:before {
            content: "" counter(lst-ctn-kix_list_18-0, decimal) "." counter(lst-ctn-kix_list_18-1, decimal) ".\0025cf ." counter(lst-ctn-kix_list_18-3, decimal) "." counter(lst-ctn-kix_list_18-4, decimal) "." counter(lst-ctn-kix_list_18-5, decimal) "." counter(lst-ctn-kix_list_18-6, decimal) "." counter(lst-ctn-kix_list_18-7, decimal) ". "
        }

        ul.lst-kix_list_7-7 {
            list-style-type: none
        }

        ul.lst-kix_list_7-8 {
            list-style-type: none
        }

        .lst-kix_list_16-6 > li {
            counter-increment: lst-ctn-kix_list_16-6
        }

        .lst-kix_list_18-6 > li {
            counter-increment: lst-ctn-kix_list_18-6
        }

        ul.lst-kix_list_7-1 {
            list-style-type: none
        }

        ul.lst-kix_list_7-2 {
            list-style-type: none
        }

        ul.lst-kix_list_7-0 {
            list-style-type: none
        }

        .lst-kix_list_7-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_15-4 > li:before {
            content: "o  "
        }

        .lst-kix_list_15-6 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_10-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_10-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_4-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_15-3 {
            list-style-type: none
        }

        ul.lst-kix_list_15-2 {
            list-style-type: none
        }

        .lst-kix_list_15-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_15-1 {
            list-style-type: none
        }

        .lst-kix_list_15-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_15-0 {
            list-style-type: none
        }

        .lst-kix_list_10-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_4-4 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_15-8 {
            list-style-type: none
        }

        .lst-kix_list_4-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_4-6 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_15-7 {
            list-style-type: none
        }

        ul.lst-kix_list_15-6 {
            list-style-type: none
        }

        .lst-kix_list_9-3 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_15-5 {
            list-style-type: none
        }

        ul.lst-kix_list_15-4 {
            list-style-type: none
        }

        .lst-kix_list_15-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_10-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_9-1 > li:before {
            content: "o  "
        }

        .lst-kix_list_9-7 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_11-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_12-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_9-5 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_6-6 {
            list-style-type: none
        }

        ul.lst-kix_list_6-7 {
            list-style-type: none
        }

        ul.lst-kix_list_6-4 {
            list-style-type: none
        }

        ul.lst-kix_list_6-5 {
            list-style-type: none
        }

        ul.lst-kix_list_6-8 {
            list-style-type: none
        }

        .lst-kix_list_12-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_11-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_1-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_6-2 {
            list-style-type: none
        }

        .lst-kix_list_11-8 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_6-3 {
            list-style-type: none
        }

        .lst-kix_list_1-2 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_6-0 {
            list-style-type: none
        }

        .lst-kix_list_12-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_6-1 {
            list-style-type: none
        }

        .lst-kix_list_1-4 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_13-0 > li:before {
            content: "\0025cf  "
        }

        ul.lst-kix_list_14-4 {
            list-style-type: none
        }

        ul.lst-kix_list_14-3 {
            list-style-type: none
        }

        ul.lst-kix_list_14-2 {
            list-style-type: none
        }

        ol.lst-kix_list_16-1.start {
            counter-reset: lst-ctn-kix_list_16-1 0
        }

        .lst-kix_list_13-4 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_14-1 {
            list-style-type: none
        }

        ul.lst-kix_list_14-0 {
            list-style-type: none
        }

        .lst-kix_list_1-6 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_18-7 > li {
            counter-increment: lst-ctn-kix_list_18-7
        }

        ul.lst-kix_list_14-8 {
            list-style-type: none
        }

        ul.lst-kix_list_14-7 {
            list-style-type: none
        }

        .lst-kix_list_2-0 > li:before {
            content: "\0025cf  "
        }

        .lst-kix_list_18-1 > li {
            counter-increment: lst-ctn-kix_list_18-1
        }

        .lst-kix_list_12-6 > li:before {
            content: "\0025aa  "
        }

        ul.lst-kix_list_14-6 {
            list-style-type: none
        }

        ul.lst-kix_list_14-5 {
            list-style-type: none
        }

        .lst-kix_list_1-8 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_2-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_13-2 > li:before {
            content: "\0025aa  "
        }

        .lst-kix_list_12-8 > li:before {
            content: "\0025aa  "
        }

        ol {
            margin: 0;
            padding: 0
        }

        table td, table th {
            padding: 0
        }

        .c14 {
            margin-left: 36pt;
            padding-top: 0pt;
            padding-left: 0pt;
            padding-bottom: 12pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c1 {
            margin-left: 36pt;
            padding-top: 12pt;
            padding-left: 0pt;
            padding-bottom: 0pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c21 {
            margin-left: 36pt;
            padding-top: 0pt;
            padding-left: 0pt;
            padding-bottom: 0pt;
            line-height: 1.0791666666666666;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        .c3 {
            margin-left: 36pt;
            padding-top: 0pt;
            padding-left: 0pt;
            padding-bottom: 0pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c9 {
            margin-left: 36pt;
            padding-top: 0pt;
            padding-left: 0pt;
            padding-bottom: 0pt;
            line-height: 1.0791666666666666;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c10 {
            color: #000000;
            font-weight: 400;
            text-decoration: none;
            vertical-align: baseline;
            font-size: 14pt;
            font-family: "Calibri";
            font-style: normal
        }

        .c2 {
            color: #000000;
            font-weight: 400;
            text-decoration: none;
            vertical-align: baseline;
            font-size: 11pt;
            font-family: "Calibri";
            font-style: normal
        }

        .c7 {
            padding-top: 0pt;
            padding-bottom: 0pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify;
            height: 11pt
        }

        .c16 {
            padding-top: 0pt;
            padding-bottom: 0pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c13 {
            padding-top: 12pt;
            padding-bottom: 0pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c0 {
            padding-top: 12pt;
            padding-bottom: 12pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c15 {
            -webkit-text-decoration-skip: none;
            color: #1155cc;
            font-weight: 700;
            text-decoration: underline;
            text-decoration-skip-ink: none;
            font-family: "Calibri"
        }

        .c26 {
            padding-top: 14pt;
            padding-bottom: 0pt;
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c20 {
            padding-top: 0pt;
            padding-bottom: 6pt;
            line-height: 1.0791666666666666;
            orphans: 2;
            widows: 2;
            text-align: justify
        }

        .c4 {
            background-color: #ffffff;
            font-family: "Calibri";
            color: #000000;
            font-weight: 400
        }

        .c8 {
            text-decoration: none;
            vertical-align: baseline;
            font-size: 11pt;
            font-style: normal
        }

        .c6 {
            font-family: "Calibri";
            color: #000000;
            font-weight: 700
        }

        .c11 {
            font-family: "Calibri";
            color: #000000;
            font-weight: 400
        }

        .c22 {
            max-width: 470.3pt;
            padding: 70.8pt 70.8pt 70.8pt 70.8pt
        }

        .c24 {
            font-weight: 400;
            font-family: "Calibri"
        }

        .c25 {
            margin-left: 36pt;
            padding-left: 0pt
        }

        .c12 {
            padding: 0;
            margin: 0
        }

        .c19 {
            color: inherit;
            text-decoration: inherit
        }

        .c18 {
            font-size: 14pt
        }

        .c23 {
            height: 11pt
        }

        .c17 {
            margin-left: 18pt
        }

        .c5 {
            background-color: #ffffff
        }

        .title {
            padding-top: 24pt;
            color: #000000;
            font-weight: 700;
            font-size: 36pt;
            padding-bottom: 6pt;
            font-family: "Calibri";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        .subtitle {
            padding-top: 18pt;
            color: #666666;
            font-size: 24pt;
            padding-bottom: 4pt;
            font-family: "Georgia";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            font-style: italic;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        li {
            color: #000000;
            font-size: 11pt;
            font-family: "Calibri"
        }

        p {
            margin: 0;
            color: #000000;
            font-size: 11pt;
            font-family: "Calibri"
        }

        h1 {
            padding-top: 24pt;
            color: #000000;
            font-weight: 700;
            font-size: 24pt;
            padding-bottom: 6pt;
            font-family: "Calibri";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        h2 {
            padding-top: 18pt;
            color: #000000;
            font-weight: 700;
            font-size: 18pt;
            padding-bottom: 4pt;
            font-family: "Calibri";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        h3 {
            padding-top: 0pt;
            color: #000000;
            font-weight: 700;
            font-size: 13.5pt;
            padding-bottom: 8pt;
            font-family: "Times New Roman";
            line-height: 1.0;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        h4 {
            padding-top: 12pt;
            color: #000000;
            font-weight: 700;
            font-size: 12pt;
            padding-bottom: 2pt;
            font-family: "Calibri";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        h5 {
            padding-top: 11pt;
            color: #000000;
            font-weight: 700;
            font-size: 11pt;
            padding-bottom: 2pt;
            font-family: "Calibri";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: left
        }

        h6 {
            padding-top: 10pt;
            color: #000000;
            font-weight: 700;
            font-size: 10pt;
            padding-bottom: 2pt;
            font-family: "Calibri";
            line-height: 1.0791666666666666;
            page-break-after: avoid;
            orphans: 2;
            widows: 2;
            text-align: left
        }</style>
</head>
<body class="c5 c22"><p class="c0"><span class="c6">&nbsp;</span><span class="c6 c5 c18">Recruitment Software O&Uuml; Kasutustingimused.</span>
</p>
<p class="c0"><span class="c4">K&auml;esolevad Kasutustingimused sisaldavad k&otilde;iki tingimusi, mis reguleerivad Kliendi juurdep&auml;&auml;su Platvormile ja Teenustele (nii nagu on defineeritud allpool) ning Teenuste kasutamist. Platvormi ja Teenuseid pakutakse teile tingimusel, et te n&otilde;ustute Kasutustingimustega muutmata kujul (v.a Eritingimused (nagu defineeritud allpool)). N&otilde;ustudes Kasutustingimustega, v&otilde;i muul punktis 3.1 s&auml;testatud juhul, moodustavad Kasutustingimused &otilde;iguslikult siduva lepingu teie ja Haldaja (nagu defineeritud allpool) vahel.</span>
</p>
<p class="c0"><span class="c4">N&otilde;ustudes Kasutustingimustega kinnitate, et olete neid lugenud, neist aru saanud ja n&otilde;ustute, et need on teie suhtes siduvad. Kui te ei soovi, et Kasutustingimused oleksid teie suhtes siduvad, siis palun &auml;rge kasutage Platvormi ja Teenuseid.</span>
</p>
<p class="c16 c5"><span class="c6">1. M&Otilde;ISTED</span></p>
<p class="c0"><span class="c6 c5">1.1. Eritingimused</span><span class="c4">&nbsp;Tingimused, millega t&auml;psustatakse, muudetakse v&otilde;i t&auml;iendatakse Kasutustingimusi Poolte kokkuleppel.</span>
</p>
<p class="c0"><span class="c6 c5">1.2. Haldaja</span><span class="c4">&nbsp;Recruitment Software O&Uuml; (registrikood 14936047), aadress Harju maakond, Tallinn, Kesklinna linnaosa, J&auml;rvevana tee 9, 11314).&nbsp;</span>
</p>
<p class="c0"><span class="c6 c5">1.3. Infos&uuml;steem</span><span class="c4">&nbsp;Integreeritud pilvelahendus Teenuste osutamiseks, sealhulgas rakendused, tarkvara, riistvara, andmebaasid, liidesed, seotud meedia, dokumentatsioon, v&auml;rskendused, versiooniuuendused ja muud nendega seotud komponendid v&otilde;i materjalid.</span>
</p>
<p class="c0"><span class="c6 c5">1.4. Kasutustingimused</span><span class="c4">&nbsp;K&auml;esolevad Platvormi ja Teenuste kasutamise t&uuml;&uuml;ptingimused.</span>
</p>
<p class="c0"><span class="c6 c5">1.5. Kasutaja</span><span class="c4">&nbsp;F&uuml;&uuml;siline isik, kes kasutab Platvormi ja Teenuseid Kliendi nimel ja volitusel.</span>
</p>
<p class="c0"><span class="c6 c5">1.6. Kasutajakonto</span><span class="c4">&nbsp;Kliendikontoga seotud Kasutaja profiil Platvormi kasutamiseks, mida kasutatakse Kasutaja tuvastamiseks, personaalseks ligip&auml;&auml;suks Teenustele ning seadistuste muutmiseks ja salvestamiseks.</span>
</p>
<p class="c0"><span class="c6 c5">1.7. Kliendikonto</span><span class="c4">&nbsp;Kliendiga seotud instants Platvormi kasutamiseks, mida kasutatakse Kliendi tuvastamiseks, Kliendiga seotud Kasutajate ligip&auml;&auml;suks Teenustele ning seadistuste muutmiseks ja salvestamiseks.</span>
</p>
<p class="c0"><span class="c6 c5">1.8. Klient</span><span class="c4">&nbsp;Majandus- v&otilde;i kutsetegevuses tegutsev isik, kes on s&otilde;lminud Haldajaga Lepingu.</span>
</p>
<p class="c0"><span class="c6 c5">1.9. Firmakonto</span><span class="c4">&nbsp;Profiil Kliendi erinevate ettev&otilde;tete v&otilde;i osakondadega seotud info eristamiseks ja/v&otilde;i filtreerimiseks loodud Kliendikonto raames (&uuml;ks v&otilde;i mitu).</span>
</p>
<p class="c0"><span class="c6 c5">1.10. Leping</span><span class="c4">&nbsp;Haldaja ja Kliendi vahel vastavalt Kasutustingimuste punktile 3 s&otilde;lmitud kokkulepe Platvormi ja Teenuste kasutamiseks.</span>
</p>
<p class="c0"><span class="c6 c5">1.11. Pool(ed)</span><span class="c4">&nbsp;Ainsuses s&otilde;ltuvalt kontekstist kas Haldaja v&otilde;i Klient, mitmuses m&otilde;lemad.</span>
</p>
<p class="c0"><span class="c6 c5">1.12. Sisu</span><span class="c4">&nbsp;Kasutajate poolt lisatud andmed, teosed ja muud materjalid (video, foto, pilt, skeem, tekst jms). Sisu sisaldab ka isikuandmeid, mida Klient ja/v&otilde;i Kasutajad Platvormil t&ouml;&ouml;tlevad. Seega sisaldavad k&auml;esolevad Kasutustingimused ka GDPR-i artikli 28 kohaselt n&otilde;utud andmet&ouml;&ouml;tluslepingut Kliendi kui vastutava t&ouml;&ouml;tleja ja Haldaja kui volitatud t&ouml;&ouml;tleja vahel (vt Kasutustingimuste punkt 7).</span>
</p>
<p class="c0"><span class="c6 c5">1.13. Platvorm</span><span class="c4">&nbsp;Majandus- ja kutsetegevuses tegutsevatele isikutele suunatud integreeritud tarkvaralahendus t&ouml;&ouml;kuulutuste, v&auml;rbamiskampaaniate, kampaania veebilehtede, v&auml;rbamisprotsessi, Kandidaatide, m&uuml;&uuml;giprotsessi, kliendihaldusprotsessi, klientide jms haldamiseks.</span><span
        class="c24">&nbsp;Platvormi m&otilde;iste h&otilde;lmab </span><span class="c4">Lisarakendust ja Infos&uuml;steemi kogumis nii veebis kui ka mobiilirakenduses.</span>
</p>
<p class="c0"><span class="c6 c5">1.14. Teenus</span><span class="c4">&nbsp;Lepingu alusel Kliendile pakutavad mis tahes teenused.</span>
</p>
<p class="c0"><span class="c6 c5">1.15. Lisarakendus</span><span class="c4">&nbsp;Kliendi serverisse v&otilde;i Kasutaja seadmesse vajadusel paigaldatav tarkvara info s&uuml;nkroniseerimiseks Kliendi teiste lahenduste ja s&uuml;steemidega, mis toimib koos vastavate Teenustega.</span>
</p>
<p class="c0"><span class="c6 c5">1.16. Veebileht</span><span class="c4">&nbsp;K&otilde;ikide Haldajale kuuluvate domeenide (n&auml;iteks recruitlab.ee, recruitlab.lv ja teiste veebilehtede, mis on registreeritud domeeninime &ldquo;recruitlab&rdquo; v&otilde;i &ldquo;recruitmentsoftware&rdquo; all erinevates tippdomeenides) ning nende </span><span
        class="c11">alamdomeenide kaudu </span><span class="c4">k&auml;ttesaadavate veebidokumentide (sealhulgas piltide, videode, php- ja html-failide) kogum.</span>
</p>
<p class="c0"><span class="c6 c5">1.17. </span><span class="c5 c15"><a class="c19"
                                                                       href="/privacy-policy">Privaatsuspoliitika</a></span><span
        class="c4">, mis selgitab, kuidas Haldaja t&ouml;&ouml;tleb Klientide, nende esindajate, Kasutajate, Klientide (potentsiaalsete) klientide, Kandidaatide ja Klientide potentsiaalsete Kandidaadite isikuandmeid.</span>
</p>
<p class="c0"><span class="c6 c5">1.18</span><span class="c6">. Kandidaat </span><span class="c2">F&uuml;&uuml;siline isik, kelle andmeid ja isikuandmeid t&ouml;&ouml;tleb Klient Platvormil vastutava t&ouml;&ouml;tlejana ja kelle isikuandmeid t&ouml;&ouml;tleb Haldaja volitatud t&ouml;&ouml;tlejana.</span>
</p>
<p class="c0"><span class="c6">1.19. Intellektuaalomandi &otilde;igused </span><span class="c11">Autori&otilde;igused, patendid, &otilde;igused leiutistele, &otilde;igused kujunduse suhtes, andmebaasi &uuml;lesehitusega seotud &otilde;igused ning k&otilde;ik muud intellektuaalomandi &otilde;igused olenemata sellest, kas tegu on registreeritud v&otilde;i registreerimata &otilde;igustega, sh k&otilde;ik taotlused ja &otilde;igused taotleda ja saada, uuendada v&otilde;i pikendada neid &otilde;igusi (samuti n&otilde;uda prioriteetsust) ja muid sarnaseid v&otilde;i samav&auml;&auml;rseid &otilde;igusi v&otilde;i kaitsev&otilde;imalusi, mis on maailma eri paikades praegu v&otilde;i tulevikus olemas. </span>
</p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">2. &Uuml;LDP&Otilde;HIM&Otilde;TTED</span></p>
<p class="c0"><span class="c6 c5">2.1.</span><span class="c4">&nbsp;Haldaja annab Kliendile ja Kasutajatele juurdep&auml;&auml;su Platvormile. Klient ja Kasutajad kasutavad Platvormi t&ouml;&ouml;kuulutuste, v&auml;rbamiskampaaniate, kampaania veebilehtede, v&auml;rbamisprotsessi, Kandidaatide, m&uuml;&uuml;giprotsessi, kliendihaldusprotsessi, klientide jms haldamiseks.</span><span>&nbsp; &nbsp;</span>
</p>
<p class="c0"><span class="c6 c5">2.2.</span><span class="c4">&nbsp;Klient ja Kasutajad peavad ise veenduma, et Platvorm sobiks nende vajadustega ja neile kehtivatest seadustest tulenevate n&otilde;uetega. Kliendile on antud juurdep&auml;&auml;s Platvormile, st Klient on teadlik Platvormi funktsioonidest ja v&otilde;imalustest. </span>
</p>
<p class="c0"><span class="c6 c5">2.3.</span><span class="c4">&nbsp;Platvormi t&auml;isfunktsionaalsuse kasutamiseks peab Klient looma Kliendikonto ning teatud juhtudel paigaldama Lisarakenduse oma serverisse ja/v&otilde;i Kasutajate arvutitesse.</span>
</p>
<p class="c0"><span class="c6 c5">2.4.</span><span class="c4">&nbsp;Platvormi v&otilde;ib kasutada &uuml;ksnes selles ulatuses, otstarbel ja eesm&auml;rkidel, mille jaoks Platvormi funktsionaalsus on loodud ning Klientidele ja Kasutajatele k&auml;ttesaadavaks tehtud v&otilde;i milleks sama t&uuml;&uuml;pi tehnoloogialahendusi tavap&auml;raselt kasutatakse. Kasutamine peab toimuma koosk&otilde;las Kasutustingimuste, Haldaja poolt k&auml;ttesaadavaks tehtud n&otilde;uannete ja juhiste ning hea tava ja &otilde;igusaktidega.</span>
</p>
<p class="c0"><span class="c6 c5">2.5.</span><span class="c4">&nbsp;Klient ega Kasutaja ei v&otilde;i:</span></p>
<ul class="c12 lst-kix_list_1-0 start">
    <li class="c1 c5"><span class="c2">kasutada Platvormi &otilde;igusrikkumiste toimepanemiseks ega &otilde;igusrikkumisele &uuml;leskutsumiseks;</span>
    </li>
    <li class="c3 c5"><span class="c2">kasutada Platvormi ulatuses, mille suhtes kehtestatud Kasutustingimustega ta n&otilde;us ei ole;</span>
    </li>
    <li class="c3 c5"><span class="c2">saata Platvormi kaudu teistele Kasutajatele ja Kandidaatidele reklaamkirju, masspostitusi ja muud Sisu, mis on vastuolus Kasutustingimustes kehtestatud n&otilde;uetega;</span>
    </li>
    <li class="c3 c5"><span class="c2">kasutada Platvormi mis tahes muul ebaseaduslikul viisil;</span></li>
    <li class="c14 c5"><span class="c2">kasutada Teenuseid nende k&auml;ttesaadavuse v&otilde;i funktsionaalsuse seireks v&otilde;i muudel konkureerimise eesm&auml;rkidel.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">2.6.</span><span class="c4">&nbsp;Haldaja teeb k&otilde;ik, mida v&otilde;iks temalt m&otilde;istlikult oodata, et Platvorm ja Teenused oleksid Kliendile k&auml;ttesaadavad, toimiksid turvaliselt, kasutaksid uuemaid tehnoloogilisi lahendusi ja et neid oleks Kliendil mugav kasutada. Klient teadvustab ja n&otilde;ustub, et Haldajal on &otilde;igus igal ajal parandada ja t&otilde;hustada Platvormi tehnilist &uuml;lesehitust, turvalisust, k&auml;ttesaadavust ja funktsionaalsust. Haldaja ja Klient v&otilde;ivad Eritingimustes kokku leppida Platvormi t&ouml;&ouml;kindlust, k&auml;ttesaadavust ja turvalisust puudutava teenustaseme kriteeriumid.</span>
</p>
<ul class="c12 lst-kix_list_1-0">
    <li class="c3 c5"><span class="c6 c5">2.7.</span><span class="c4">&nbsp;Klient arvestab ja n&otilde;ustub, et Haldaja v&otilde;ib:</span><span
            class="c2">muuta ja t&auml;iendada Platvormi vastavalt oma n&auml;gemusele, et vastata t&auml;nap&auml;eva lahendustele ja turu vajadustele ning lisada uusi funktsioone ja teenuseid; </span>
    </li>
    <li class="c3 c5"><span class="c2">kehtestada Platvormi teatud osade v&otilde;i funktsionaalsuse kasutamiseks piiranguid (n&auml;iteks Teenuste kasutamiseks vajalik andmemaht, Sisu &uuml;leslaadimise kiirus, talletatava Sisu maht jne);</span>
    </li>
    <li class="c3 c5"><span class="c2">peatada v&otilde;i l&otilde;petada Platvormi pakkumise ja sulgeda selle mis tahes osa ajutiselt v&otilde;i alaliselt. Platvormi pakkumise l&otilde;petamise korral &nbsp;esitab Haldaja Kliendile avalduse Lepingu l&otilde;petamiseks Kasutustingimuste punktis 10.2. toodud tingimustel;</span>
    </li>
    <li class="c3 c5"><span class="c2">keelduda Platvormile juurdep&auml;&auml;su andmast mistahes Kasutajale.</span>
    </li>
</ul>
<p class="c0"><span class="c6 c5">2.8.</span><span class="c4">Kasutustingimused on Lepingu lahutamatuks osaks. Haldaja v&otilde;ib iga Teenuse kohta kehtestada eraldi lisatingimusi (n&auml;iteks Privaatsuspoliitika, hinnapaketid ja kasutusjuhendid), mis loetakse Kasutustingimuste lahutamatuks osaks.</span>
</p>
<p class="c0"><span class="c6 c5">2.9.</span><span class="c4">&nbsp;Klient ja Haldaja v&otilde;ivad lisaks Kasutustingimustele kokku leppida ka Eritingimustes. Eritingimused tuleb vormistada v&auml;hemalt kirjalikku taasesitamist v&otilde;imaldavas vormis ning need loetakse samuti Lepingu lahutamatuks osaks.</span>
</p>
<p class="c0"><span class="c6 c5">2.10.</span><span class="c4">&nbsp;Haldaja suhtleb Kliendi ja Kasutajatega eesti keeles, kui ei ole kokku lepitud teisiti. Leping ja muud Platvormi kasutamist ja Teenuste osutamist k&auml;sitlevad dokumendid on koostatud eesti v&otilde;i inglise keeles.</span>
</p>
<p class="c0"><span class="c6 c5">2.11.</span><span class="c4">&nbsp;Kui Klient v&otilde;i Kasutaja ei n&otilde;ustu Kasutustingimuste v&otilde;i nende muudatustega, ei ole tal &otilde;igust Platvormi ja Teenuseid kasutada ning ta peab selle l&otilde;petama (vt Kasutustingimuste p 4.6). &nbsp;</span>
</p>
<p class="c23 c5 c26"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">3. LEPINGU S&Otilde;LMIMINE</span></p>
<p class="c0"><span class="c6 c5">3.1.</span><span class="c4">&nbsp;Leping loetakse s&otilde;lmituks niipea, kui:</span>
</p>
<ul class="c12 lst-kix_list_3-0 start">
    <li class="c1 c5"><span class="c2">Klient avab Platvormil Haldaja poolt loodud Kliendi keskkonnas Kliendikonto ja/v&otilde;i Kasutajakonto, n&otilde;ustub Kasutustingimustega ning kinnitab, et ta on tutvunud Privaatsuspoliitikaga; v&otilde;i</span>
    </li>
    <li class="c3 c5"><span class="c2">Klient klikib Veebilehel nupul &bdquo;Loo keskkond&ldquo; n&otilde;ustudes seda tehes Kasutustingimustega ning kinnitades, et ta on tutvunud Privaatsuspoliitikaga; v&otilde;i</span>
    </li>
    <li class="c14 c5"><span class="c2">Klient ja Haldaja on allkirjastanud Lepingu dokumendi, mis sisaldab viidet Kasutustingimustele ja Privaatsuspoliitikale.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">3.2.</span><span class="c4">&nbsp;Klient peab enne Lepingu s&otilde;lmimist Kasutustingimuste ja Privaatsuspoliitikaga p&otilde;hjalikult tutvuma ja andma Haldaja n&otilde;udmisel kinnituse, et ta on seda teinud. Klient peab tagama, et ka tema Kasutajad on Kasutustingimuste ja Privaatsuspoliitikaga p&otilde;hjalikult tutvunud.</span>
</p>
<p class="c13"><span class="c6 c5">3.3.</span><span class="c4">&nbsp;Lepingu s&otilde;lmimisel Klient v&otilde;i tema esindaja kinnitab, et:</span>
</p>
<ul class="c12 lst-kix_list_4-0 start">
    <li class="c1 c5"><span class="c2">k&otilde;ik tema esitatud andmed ja antud kinnitused on t&auml;psed, &otilde;iged, t&auml;ielikud ja asjakohased;</span>
    </li>
    <li class="c3 c5"><span class="c2">ta on t&auml;ielikult teov&otilde;imeline f&uuml;&uuml;siline isik (v&auml;hemalt 18-aastane);</span>
    </li>
    <li class="c14 c5"><span class="c2">tal on k&otilde;ik &otilde;igused ja volitused Kliendi nimel Lepingu s&otilde;lmimiseks ja Platvormi ning Teenuste kasutamiseks.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">3.4.</span><span class="c4">&nbsp;Eelnimetatud kinnituste &otilde;igsust eeldatakse ja Haldaja ei ole kohustatud, aga v&otilde;ib neid kontrollida.</span>
</p>
<p class="c13"><span class="c6 c5">3.5.</span><span class="c4">&nbsp;Haldajal on &otilde;igus keelduda Lepingu s&otilde;lmimisest &uuml;ksk&otilde;ik millise isikuga, isegi kui see isik on n&otilde;ustunud Kasutustingimustega.</span>
</p>
<p class="c13"><span class="c6 c5">3.6.</span><span class="c4">&nbsp;Kasutustingimused ja Privaatsuspoliitika j&auml;&auml;vad Kliendile ja Kasutajatele k&auml;ttesaadavaks Veebilehel.</span>
</p>
<p class="c7"><span class="c2"></span></p>
<p class="c7"><span class="c2"></span></p>
<p class="c5 c16"><span class="c6">4. POOLTE &Otilde;IGUSED JA KOHUSTUSED</span></p>
<p class="c0"><span class="c6 c5">4.1.</span><span class="c4">&nbsp;Haldajal on &otilde;igus igal ajal &uuml;hepoolselt muuta Kasutustingimusi, avaldades Veebilehel vastavalt Kasutustingimuste uue redaktsiooni koos muudatustega. Haldaja v&otilde;ib Kasutustingimusi muuta j&auml;rgmistel juhtudel:</span>
</p>
<ul class="c12 lst-kix_list_5-0 start">
    <li class="c1 c5"><span
            class="c2">muudatus kehtivates &otilde;igusaktides v&otilde;i nende t&otilde;lgendamises;</span></li>
    <li class="c3 c5"><span class="c2">Haldaja suhtes j&otilde;ustunud Kasutustingimuste muutmiseks kohustav kohtulahend v&otilde;i haldusakt;</span>
    </li>
    <li class="c3 c5"><span class="c2">senise Teenuse muutumine, pakkumise l&otilde;petamine v&otilde;i uue Teenuse kasutuselev&otilde;tmine;</span>
    </li>
    <li class="c3 c5"><span class="c2">olulised muudatused Platvormi tehnilises &uuml;lesehituses v&otilde;i funktsionaalsuses;</span>
    </li>
    <li class="c3 c5"><span class="c2">Klientide ja Kasutajate ettepanekud ja kaebused;</span></li>
    <li class="c3 c5"><span class="c2">vajadus andmekaitse v&otilde;i muude turvameetmete t&otilde;hustamiseks;</span>
    </li>
    <li class="c3 c5"><span class="c2">muudatused Haldaja &auml;rimudelis, t&ouml;&ouml;korralduses ja/v&otilde;i volitustes;</span>
    </li>
    <li class="c3 c5"><span class="c2">tehnoloogilised arengud, mis v&otilde;imaldavad parandada Platvormi ja Teenuste kasutajamugavust, kvaliteeti ja turvalisust;</span>
    </li>
    <li class="c14 c5"><span class="c2">muud etten&auml;htamatud juhud, kus Kasutustingimuste muutmine on m&otilde;istlikult p&otilde;hjendatud v&otilde;i millega Haldajal ei olnud v&otilde;imalik Kasutustingimuste kehtestamisel arvestada.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">4.2.</span><span class="c2 c5">&nbsp;Haldaja teavitab Kliente Kasutustingimuste muudatustest Veebilehel ja eraldi teatega Kliendi kontaktandmete kaudu v&auml;hemalt 30 p&auml;eva enne nende j&otilde;ustumist. Kui Klient ei n&otilde;ustu nimetatud muudatustega, on tal &otilde;igus Leping l&otilde;petada muudatuste j&otilde;ustumisele eelneva 30 p&auml;eva jooksul. Kui Klient j&auml;tkab Platvormi ja Teenuste kasutamist p&auml;rast nimetatud 30-p&auml;evase t&auml;htaja m&ouml;&ouml;dumist, siis loetakse, et ta on Kasutustingimuste muudatustega n&otilde;ustunud.</span>
</p>
<p class="c13"><span class="c6 c5">4.3</span><span class="c4">. Haldaja on kohustatud: </span></p>
<ul class="c12 lst-kix_list_15-0 start">
    <li class="c1"><span class="c2">tagama Platvormi eesm&auml;rgip&auml;rase kasutamisv&otilde;imaluse Lepingu kehtivuse ajal; </span>
    </li>
    <li class="c3"><span class="c2">viivitamata teavitama Platvormi kasutamist h&auml;irivatest t&otilde;rgetest Klienti;</span>
    </li>
    <li class="c3"><span class="c2">likvideerima Platvormi toimimist&otilde;rked m&otilde;istliku aja jooksul; </span>
    </li>
    <li class="c3"><span class="c2">t&auml;itma oma kohustusi majandus- ja kutsetegevuses tegutsevalt isikult tavaliselt oodatava hoolsusega; </span>
    </li>
    <li class="c3"><span class="c2">p&ouml;&ouml;rduma viivitamatult Lepingu t&auml;itmisel tekkivate k&uuml;simuste korral Kliendi poole. </span>
    </li>
</ul>
<p class="c13"><span class="c2">4.4. &nbsp;Kliendil on &otilde;igus: </span></p>
<ul class="c12 lst-kix_list_15-0">
    <li class="c1"><span class="c2">kasutada Platvormi oma tegevuses Eesti Vabariigi territooriumil, </span></li>
    <li class="c3"><span class="c2">teha ettepanekuid Platvormi muutmiseks v&otilde;i t&auml;iendamiseks. </span></li>
</ul>
<p class="c7"><span class="c8 c6"></span></p>
<p class="c13"><span class="c2">4.9. Klient on kohustatud: </span></p>
<ul class="c12 lst-kix_list_15-0">
    <li class="c1"><span class="c2">tagama omalt poolt tingimused, mis on vajalikud Platvormi kasutamiseks; </span></li>
    <li class="c3"><span class="c2">tasuma Platvormi kasutamise eest kokkulepitud tingimustel; </span></li>
    <li class="c3"><span class="c2">andma Haldajale piisavat informatsiooni oma kohustuste t&auml;itmiseks ja &otilde;iguste kaitseks;</span>
    </li>
    <li class="c3"><span
            class="c2">teavitama Haldajat viivitamata olulistest asjaoludest seoses Platvormi kasutamisega; </span></li>
    <li class="c3"><span class="c2">teavitama Haldajat viivitamata kontaktandmete muutumisest.</span></li>
</ul>
<p class="c7"><span class="c2"></span></p>
<p class="c16 c5"><span class="c8 c6">&nbsp;</span></p>
<p class="c16 c5"><span class="c6">5. KLIENDIKONTO JA KASUTAJAKONTO</span></p>
<p class="c0"><span class="c6 c5">5.1.</span><span class="c4">&nbsp;Platvormi p&otilde;hifunktsionaalsuse kasutamiseks on vajalik Kliendikonto ja Kasutajakonto olemasolu. Kui f&uuml;&uuml;siline isik on seotud mitme Kliendiga, siis iga Kliendikonto all luuakse sellele f&uuml;&uuml;silisele isikule eraldi Kasutajakonto.</span>
</p>
<p class="c0"><span class="c6 c5">5.2.</span><span class="c4">&nbsp;Kasutajakontosid haldab Klient, st Kliendil on oma &auml;ran&auml;gemisel &otilde;igus luua, muuta ja deaktiveerida Kasutajakontosid ja andmeid Kasutaja kohta. Haldajal on &otilde;igus ja kohustus luua uus Kasutajakonto ainult juhul, kui Kliendil puudub juurdep&auml;&auml;s oma Kliendikontole ning &uuml;kski Kasutaja ei ole Kliendi seaduslik esindaja. Kliendi seaduslikuks esindajaks loetakse Kliendi registrikaardile kantud juhatuse liiget v&otilde;i Kliendi seadusliku esindaja ja Haldaja poolt kokku lepitud Eritingimustes Kliendi esindajateks m&auml;rgitud isikuid.</span>
</p>
<p class="c0"><span class="c6 c5">5.3.</span><span class="c4">&nbsp;Oma konto kaudu igakordsel sisselogimisel Platvormile Kasutaja kinnitab, et:</span>
</p>
<ul class="c12 lst-kix_list_6-0 start">
    <li class="c1 c5"><span class="c2">k&otilde;ik tema esitatud andmed ja antud kinnitused on t&auml;psed, &otilde;iged, t&auml;ielikud ja asjakohased;</span>
    </li>
    <li class="c3 c5"><span class="c2">ta on t&auml;ielikult teov&otilde;imeline f&uuml;&uuml;siline isik (v&auml;hemalt 18-aastane);</span>
    </li>
    <li class="c14 c5"><span class="c2">tal on k&otilde;ik &otilde;igused ja volitused Kliendi nimel Platvormi ja Teenuste kasutamiseks.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">5.4.</span><span class="c4">&nbsp;Eelnimetatud kinnituste &otilde;igsust eeldatakse ja Haldaja ei ole kohustatud, aga v&otilde;ib neid kontrollida.</span>
</p>
<p class="c13"><span class="c6 c5">5.5.</span><span class="c4">&nbsp;Konto loomisel valivad Klient ja Kasutaja oma konto tunnuse ja parooli, mille abil on v&otilde;imalik Platvormile sisse logida. Klient ja Kasutajad kohustuvad hoidma oma tunnust ja parooli salajas sellisel viisil, et need ei satuks kolmandate isikute k&auml;tte.&nbsp;</span>
</p>
<p class="c13"><span class="c6 c5">5.6.</span><span class="c4">&nbsp;Klient v&otilde;i Kasutaja peab koheselt Haldajat teavitama:</span>
</p>
<ul class="c12 lst-kix_list_7-0 start">
    <li class="c1 c5"><span class="c2">oma konto kuritarvitamisest;</span></li>
    <li class="c3 c5"><span
            class="c2">oma parooli kadumisest v&otilde;i sattumisest kolmandate isikute valdusesse;</span></li>
    <li class="c14 c5"><span class="c2">oma ametikoha muutumisest v&otilde;i t&ouml;&ouml;lt lahkumisest v&otilde;i muust p&otilde;hjusest, miks Kasutajal ei ole enam &otilde;igust Kliendi nimel Platvormi v&otilde;i Teenuseid kasutada.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">5.7.</span><span class="c4">&nbsp;Kasutustingimuste punktis 5.6 nimetatud juhul v&otilde;tab Haldaja k&otilde;ik m&otilde;istlikud meetmed selleks, et parool uuendada, kontot kaitsta, konto arhiveerida v&otilde;i see kustutada.</span>
</p>
<p class="c13"><span class="c6 c5">5.8.</span><span class="c4">&nbsp;Kliendikonto ja Kasutajakonto kehtivad t&auml;htajatult kuni kustutamiseni v&otilde;i Lepingu l&otilde;ppemiseni. Kui Klient on palunud Haldajal Kliendikonto kustutada, k&auml;sitleb Haldaja seda Lepingu l&otilde;petamisena Kliendi poolt.</span>
</p>
<p class="c7"><span class="c2"></span></p>
<p class="c7"><span class="c2"></span></p>
<p class="c16 c5"><span class="c6">6. TASUMINE</span></p>
<p class="c0"><span class="c6 c5">6.1.</span><span class="c2 c5">&nbsp;Haldajal on &otilde;igus kehtestada Platvormi kasutamiseks tasusid, avaldades kas Kliendile personaalselt v&otilde;i Veebilehel vastavad hinnapaketid. Klient peab Platvormi kasutamiseks valima hinnapakettide seast endale sobiva tasulise hinnapaketi.</span>
</p>
<p class="c0"><span>6.2 Haldajal on &otilde;igus muuta pakettide Tasu suurust teatades sellest Kliendile ette v&auml;hemalt 30 (kolmk&uuml;mmend) p&auml;eva. Juhul, kui Klient uue Tasu suurusega ei n&otilde;ustu, siis on Kliendil &otilde;igus k&auml;esolev Leping l&otilde;petada j&auml;rgides kokkulepitud etteteatamist&auml;htaega. </span>
</p>
<p class="c0"><span class="c6 c5">6.2.</span><span class="c4">&nbsp;Klient kohustub tasuma Haldajale Platvormi kasutamise ja osutatud Teenuste eest vastavalt hinnapaketile, valitud lisadele ja Kliendi poolt valitud Kliendi- ja Kasutajakontode arvule v&otilde;i l&auml;htuvalt Eritingimustest.</span>
</p>
<p class="c0"><span class="c6 c5">6.3.</span><span class="c4">&nbsp;Enne tasulise hinnapaketi valimist on esmakordsel Kliendil v&otilde;imalik proovida Platvormi standardlahendust prooviperioodi jooksul tasuta. Prooviperioodi kestus p&auml;evades avaldatakse Haldaja Veebilehel v&otilde;i lepitakse Kliendiga kokku personaalselt. Prooviperioodi eest Haldaja Kliendile arvet ei esita.</span>
</p>
<p class="c0"><span class="c6 c5">6.4.</span><span class="c4">&nbsp;Kui Klient soovib p&auml;rast tasuta prooviperioodi l&otilde;ppemist Platvormi kasutamist j&auml;tkata tasulise hinnapaketi alusel, siis peab ta endale sobiva tasulise hinnapaketi v&auml;lja valima ja tasuma ettemaksu j&auml;rgmise perioodi eest hiljemalt prooviperioodi l&otilde;puks. Kui Klient seda teinud ei ole, siis prooviperioodi l&otilde;ppemisel on Haldajal &otilde;igus koheselt sulgeda Kliendikonto ja Kasutajakontod, sh kustutada kogu sinna lisatud Sisu, ning l&otilde;petada Leping automaatselt. Kui ei ole teisiti kokku lepitud, s&auml;ilitatakse prooviversiooni Sisu 90 p&auml;eva p&auml;rast prooviversiooni l&otilde;ppemist juhuks, kui Klient otsustab j&auml;tkata Platvormi kasutamist tasulise hinnapaketi alusel.</span>
</p>
<p class="c0"><span class="c6 c5">6.5.</span><span class="c4">&nbsp;Hinnapaketi alusel tasumine toimub perioodilise ettemaksu p&otilde;him&otilde;ttel, st Klient tasub Platvormi kasutamise eest iga j&auml;rgmise perioodi (tavaliselt kalendrikuu v&otilde;i jooksev aasta) eest ette.</span>
</p>
<p class="c0"><span class="c6 c5">6.6.</span><span class="c4">&nbsp;Iga makseperioodi alguses esitatakse Kliendile e-posti teel v&otilde;i e-arve rakenduses arve v&otilde;i maksetaotlus. Klient peab tegema makse arvel v&otilde;i maksetaotlusel m&auml;rgitud kuup&auml;evaks.</span>
</p>
<p class="c0"><span class="c6 c5">6.7.</span><span class="c4">&nbsp;Kui Klient tasub panga- v&otilde;i krediiitkaardiga, volitab Klient Haldajat oma panga- v&otilde;i krediitkaarti v&otilde;i pangakontot debiteerima k&otilde;igi maksmisele kuuluvate tasude ulatuses igal makseperioodil. Samuti volitab Klient Haldajat kasutama kolmandat osapoolt maksete t&ouml;&ouml;tlemiseks.</span>
</p>
<p class="c0"><span class="c6 c5">6.8.</span><span class="c4">&nbsp;Kui Klient tasub arve alusel, esitab Haldaja arve iga makseperioodi alguses. K&otilde;ik arvel n&auml;idatud summad kuuluvad maksmisele arvel m&auml;rgitud kuup&auml;evaks. Makset&auml;htaeg on k&uuml;mme p&auml;eva alates arve k&auml;ttesaamise kuup&auml;evast. </span><span
        class="c2">Maksega viivitamise korral kohustub klient tasuma viivist 0,25% hilinenud summalt iga viivitatud p&auml;eva eest seni, kuni kogu makse on Haldajale laekunud. </span>
</p>
<p class="c0"><span class="c6 c5">6.9.</span><span class="c4">&nbsp;Klient saab oma hinnapaketti muuta, tellida lisasid ja muuta Kasutajakontode arvu. Sellistest muudatustest tulenev muudatus hinnas kajastub Kliendi j&auml;rgmise perioodi arvel. K&otilde;rgema tasuga hinnapaketist tulenevad kasutusv&otilde;imalused j&otilde;ustuvad kohe p&auml;rast seda, kui Klient kinnitab paketi muutmist. Madalama tasuga hinnapaketi muudatused j&otilde;ustuvad alates j&auml;rgmise perioodi algusest.</span>
</p>
<p class="c0"><span class="c6 c5">6.10.</span><span
        class="c4">&nbsp;Tasutud ettemaksu ei tagastata, sealhulgas kui:</span></p>
<ul class="c12 lst-kix_list_8-0 start">
    <li class="c1 c5"><span class="c2">Klient ei ole ettemakstud perioodi jooksul Platvormi kasutanud v&otilde;i on teinud seda ainult osaliselt;</span>
    </li>
    <li class="c3 c5"><span class="c2">Klient vahetab Platvormi hinnapaketti;</span></li>
    <li class="c3 c5"><span class="c2">Klient l&otilde;petab Lepingu &uuml;hepoolselt Kasutustingimuste v&otilde;i seaduse alusel, ilma et Haldaja oleks Lepingut rikkunud;</span>
    </li>
    <li class="c14 c5"><span class="c2">Haldaja l&otilde;petab Lepingu &uuml;hepoolselt Kasutustingimuste v&otilde;i seaduse alusel.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">6.11.</span><span class="c4">&nbsp;Kui Klient hilineb oma makse teostamisega 14 p&auml;eva, on Haldajal &otilde;igus piirata Kliendi juurdep&auml;&auml;su Platvormile ja keelduda Teenuste osutamisest. Seejuures on Haldajal &otilde;igus arvestada tasu ka perioodi eest, mil ta rakendab Kliendi suhtes eelnimetatud piiranguid. Haldaja teavitab Klienti maksekohustuse rikkumise korral kavandatavate piirangute rakendamisest Kliendi e-posti aadressil.</span>
</p>
<p class="c13"><span class="c6 c5">6.12.</span><span class="c4">&nbsp;K&otilde;ik Kasutustingimustes esitatud ja viidatud tasud on esitatud ilma kohanduvate maksudeta. Haldajal on &otilde;igus lisada arvele vastavalt seadustele kohanduvad maksud.</span>
</p>
<p class="c7"><span class="c2"></span></p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">7. SISU JA ANDMETE T&Ouml;&Ouml;TLEMISE KOKKULEPE</span></p>
<p class="c0"><span class="c6 c5">7.1.</span><span class="c4">&nbsp;Haldaja osutab Kliendile Platvormi kaudu Sisu veebis majutamise ja hooldamise teenust. Klient otsustab, millist Sisu, sealhulgas isikuandmeid, ta soovib, ja kas soovib, Platvormil t&ouml;&ouml;delda. Sedasi t&ouml;&ouml;tleb Haldaja Kliendi eest Sisus sisalduvaid isikuandmeid ainult selleks, et pakkuda Platvormi ja osutada Teenuseid, ning tegutseb selliste isikuandmete suhtes volitatud t&ouml;&ouml;tlejana, samas kui Klient tegutseb selliste isikuandmete suhtes vastutava t&ouml;&ouml;tlejana. </span>
</p>
<p class="c0"><span class="c5 c6">7.2.</span><span class="c4">&nbsp;K&otilde;ikjal, peale demo versiooni (mis ei peaks sisaldama reaalseid andmeid, sealhulgas isikuandmeid), v&otilde;i paigaldamise ajal, ei ole Haldajal Sisule ligip&auml;&auml;su ilma Kliendi taotluseta (nt klienditoeks). Kasutajatel on ligip&auml;&auml;s ainult Sisule, mille on Klient neile Platvormi kaudu k&auml;ttesaadavaks teinud.</span>
</p>
<p class="c0"><span class="c6 c5">7.3.</span><span class="c4">&nbsp;Andmesubjektide kategooriad, kelle Sisus sisalduvaid isikuandmeid Klient v&otilde;ib t&ouml;&ouml;delda, v&otilde;ivad h&otilde;lmata muuhulgas identifitseerimisandmeid, kontaktandmeid, t&ouml;&ouml;d puudutavad andmeid, sideandmeid, Kandidaatide hindamiseks vajalikke andmeid, RecruitLabi Platvormi ja Teenuste kasutamisega seotud andmeid. Isikuandmete kategooriad, mida Klient v&otilde;ib Sisus t&ouml;&ouml;delda v&otilde;ivad muuhulgas h&otilde;lmata identifitseerimisandmeid, kontaktandmeid, t&ouml;&ouml;d puudutavad andmeid, sideandmeid, teisi Kandidaatide hindamiseks vajalikke andmeid, RecruitLabi Platvormi ja Teenuste kasutamisega seotud andmeid.</span>
</p>
<p class="c0"><span class="c6 c5">7.4.</span><span class="c4">&nbsp;Klient ja Haldaja soovivad n&otilde;uetekohaselt j&auml;rgida k&otilde;iki enda vastavaid kohustusi, mille alus on Euroopa Parlamendi ja N&otilde;ukogu 27. aprilli 2016 m&auml;&auml;rus (EL) 2016/679, f&uuml;&uuml;siliste isikute kaitse kohta isikuandmete t&ouml;&ouml;tlemisel ja selliste andmete vaba liikumise kohta (isikuandmete kaitse &uuml;ldm&auml;&auml;rus e GDPR) ning igasugused muud asjakohased kohalduvad andmekaitse eeskirjad (koos Andmekaitsealased &otilde;igusaktid).</span>
</p>
<p class="c0"><span class="c6 c5">7.5.</span><span class="c4">&nbsp;K&auml;esolevate Kasutustingimuste t&auml;henduses on terminitel &bdquo;vastutav t&ouml;&ouml;tleja&ldquo;, &bdquo;volitatud t&ouml;&ouml;tleja&ldquo;, &bdquo;isikuandmed&ldquo;, &bdquo;andmesubjekt&ldquo;, &bdquo;isikuandmetega seotud rikkumine&ldquo; GDPR-is antud t&auml;hendus. &bdquo;Alamt&ouml;&ouml;tleja&ldquo; t&auml;hendab teist volitatud t&ouml;&ouml;tlejat, kelle on Haldaja kaasanud Sisus toodud isikuandmete t&ouml;&ouml;tlemiseks.</span>
</p>
<p class="c0"><span class="c6 c5">7.6.</span><span class="c4">&nbsp;Klient kui andmete vastutav t&ouml;&ouml;tleja vastutab t&auml;ielikult tema Platvormi ja Teenuste abil Sisus t&ouml;&ouml;deldavate igasuguste isikuandmete eest. Klient kinnitab, et tema isikuandmete t&ouml;&ouml;tlemise tavad on t&auml;ielikult koosk&otilde;las Andmekaitsealaste &otilde;igusaktidega, sealhulgas, et tal on Sisus sisalduvate isikuandmete t&ouml;&ouml;tlemiseks &otilde;iguslik alus vastavalt k&auml;esolevas dokumendis s&auml;testatule ning, et ta on t&ouml;&ouml;tlemisest andmesubjekte korrap&auml;raselt teavitanud. Kui Kasutaja lisab Platvormile Sisu, peab ta tagama selle t&auml;psuse, &otilde;igsuse, terviklikkuse, asjakohasuse ja selle vastavuse Lepingule, heale tavale ja &otilde;igusaktidele. </span>
</p>
<p class="c0"><span class="c6 c5">7.7.</span><span class="c4">&nbsp;Haldaja:</span></p>
<ul class="c12 lst-kix_list_9-0 start">
    <li class="c1 c5"><span class="c2">t&ouml;&ouml;tleb Sisus sisalduvaid isikuandmeid ainult Kliendi &otilde;igusp&auml;raste dokumenteeritud juhiste alusel ning Platvormi pakkumise ja Teenuste osutamise eesm&auml;rgil, v&auml;lja arvatud kui seda on n&otilde;utud Andmekaitsealaste &otilde;igusaktidega. Sellisel juhul teavitab Haldaja Klienti eelnevalt sellisest vajadusest, kui seadus sellise teabe edastamist ei keela;</span>
    </li>
    <li class="c3 c5"><span class="c2">tagab, et isikud, kellel on volitus t&ouml;&ouml;delda Sisus sisalduvaid isikuandmeid, on v&otilde;tnud endale konfidentsiaalsuskohustuse;</span>
    </li>
    <li class="c3 c5"><span class="c2">v&otilde;ttes arvesse t&ouml;&ouml;tlemise ja Haldajale k&auml;ttesaadava teabe olemust, aitab Kliendil tagada, et viimane j&auml;rgib oma kohustusi vastavalt GDPR-i artiklitele 32 kuni 36;</span>
    </li>
    <li class="c14 c5"><span class="c2">teavitab Klienti kui Haldaja arvates rikub Kliendi juhis Andmekaitsealaseid &Otilde;igusakte.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">7.8.</span><span class="c4">&nbsp;Haldaja rakendab asjakohaseid tehnilisi ja korralduslikke turvameetmeid v&otilde;ttes arvesse (i) tehnika taset, (ii) juurutamise kulusid, (iii) t&ouml;&ouml;tlemise olemust, ulatust, konteksti ja eesm&auml;rke ning (iv) andmesubjekti kantavaid riske. Sellised turvameetmed h&otilde;lmavad muuhulgas kr&uuml;ptitud s&auml;ilitamist ja ligip&auml;&auml;su reguleerimist. Meetmete valikul eeldab Haldaja, et Platvormi ja Teenuseid kasutatakse vastavalt nende sihtotstarbele (Kandidaatide haldamine, -suhtlus ja -hindamine Kandidaatide t&ouml;&ouml;le v&auml;rbamise eesm&auml;rgil, v&auml;rbamisprotsesside ja -projektide haldamine, t&ouml;&ouml;kuulutuste- ja v&auml;rbamiskampaaniate ning veebilehtede haldamine, kliendisuhtluse haldamine, jne), mis ei tohi h&otilde;lmata mitte mingisuguste isikuandmete erikategooriate t&ouml;&ouml;tlemist (vaata ka eespool punkti 7.3.).</span>
</p>
<p class="c13"><span class="c6 c5">7.9.</span><span class="c4">&nbsp;Haldaja teavitab viivitamatult Klienti, kui ta saab andmesubjektilt taotluse seoses oma isikuandmetega Kliendi Sisus ning lubab Kliendil sellele vastata. Haldaja ei vasta andmesubjekti taotlusele ilma Klienti eelneva kirjaliku n&otilde;usolekuta. V&otilde;ttes arvesse t&ouml;&ouml;tlemise olemust, aitab Haldaja Klienti niiv&otilde;rd, kuiv&otilde;rd on see v&otilde;imalik asjakohaste tehniliste ja korralduslike meetmetega, et t&auml;ita Kliendi kohustus vastata andmesubjekti taotlusele vastavalt Andmekaitsealastele &Otilde;igusaktidele.</span>
</p>
<p class="c13"><span class="c6 c5">7.10.</span><span class="c4">&nbsp;Klient volitab Haldajat kasutama j&auml;rgmisi alamt&ouml;&ouml;tlejate kategooriaid:</span>
</p>
<ul class="c12 lst-kix_list_10-0 start">
    <li class="c1 c5"><span class="c2">veebimajutuse teenuse osutajad;</span></li>
    <li class="c3 c5"><span class="c2">haldamise ja s&auml;ilitamise pakkujad;</span></li>
    <li class="c3 c5"><span class="c2">e-posti teenuse osutajad;</span></li>
    <li class="c3 c5"><span class="c2">kliendisuhete haldamise ja tagasiside teenuse osutajad;</span></li>
    <li class="c3 c5"><span class="c2">makseteenuse osutajad.</span></li>
    <li class="c3"><span class="c2">platvormi tarkvaravigade monitoorimisteenuse osutajad</span></li>
    <li class="c14"><span class="c2">kasutajakogemuse anal&uuml;&uuml;si teenuse osutajad</span></li>
</ul>
<p class="c13"><span
        class="c4">Alamt&ouml;&ouml;tlejate t&auml;ielik nimekiri on saadaval vastava taotluse esitamisel.</span></p>
<p class="c13"><span class="c4">Haldaja teavitab Klienti igasugustest plaanitud muudatustest, mis puudutavad teiste alamt&ouml;&ouml;tlejate kategooriate lisamist v&otilde;i vahetust. Klient v&otilde;ib esitada vastuv&auml;ite Haldaja uue alamt&ouml;&ouml;tlejate kategooria kasutamisele, teavitades sellest Haldajat k&uuml;mne t&ouml;&ouml;p&auml;eva jooksul p&auml;rast Haldaja teavituse k&auml;tte saamist. Juhul, kui Klient esitab vastuv&auml;ite uue alamt&ouml;&ouml;tlejate kategooria suhtes, v&otilde;tab Haldaja tarvitusele m&otilde;istlikud j&otilde;upingutused, et pakkuda Kliendile Platvormi ja Teenuseid ilma sellise alamt&ouml;&ouml;tlejate kategooriata. Kui see ei ole v&otilde;imalik, v&otilde;ib Klient Lepingu l&otilde;petada. Haldaja kehtestab alamt&ouml;&ouml;tlejatele samad andmekaitsekohustused, mis on s&auml;testatud k&auml;esolevas dokumendis. Kui alamt&ouml;&ouml;tleja ei t&auml;ida enda andmekaitsekohustusi vastutab Haldaja Kliendi ees alamt&ouml;&ouml;tleja kohustuste t&auml;itmise eest.</span>
</p>
<p class="c13"><span class="c6 c5">7.11.</span><span class="c4">&nbsp;Haldaja ja tema alamt&ouml;&ouml;tlejad v&otilde;ivad edastada isikuandmeid v&auml;ljapoole EL-i ainult siis, kui neil on selleks seaduslik alus, sealhulgas andmete saajale, kes on: i) riigis, kus on isikuandmete kaitse tagatud adekvaatsel tasemel (Ameerika &Uuml;hendriikides h&otilde;lmab see Privacy Shield sertifikaadiga ettev&otilde;tteid); v&otilde;i (ii) dokumendi alusel, mis katab EL-i n&otilde;udeid isikuandmete edastamiseks v&auml;ljapool EL-i olevatele volitatud andmetet&ouml;&ouml;tlejatele.</span>
</p>
<p class="c13"><span class="c6 c5">7.12.</span><span class="c4">&nbsp;Haldaja teavitab Klienti ilma asjatu viivituseta e-posti teel p&auml;rast seda, kui on saanud teada isikuandmetega seotud rikkumisest ning teeb Kliendiga isikuandmetega seotud rikkumisest tulenevalt m&otilde;istlikult koost&ouml;&ouml;d. Sellisel juhul v&otilde;ib Klient kasutada Haldaja edastatud isikuandmetega seotud rikkumist puudutavat teavet ainult selleks, et tagada ja/v&otilde;i n&auml;idata enda koosk&otilde;la Andmekaitsealaste &otilde;igusaktidega. Klient hoiab seda teavet konfidentsiaalsena, v&auml;lja arvatud siis, kui tegu on Kliendi enda konfidentsiaalse teabega v&otilde;i kui selline teave tuleb avaldada vastavalt mis tahes kohalduvatele &otilde;igusaktidele.</span>
</p>
<p class="c13"><span class="c6 c5">7.13.</span><span class="c4">&nbsp;Haldaja teeb Kliendi kirjalikul taotlusel viimasele k&auml;ttesaadavaks teabe, mis on vajalik n&auml;itamaks, et ta j&auml;rgib k&auml;esolevas paragrahvis ja GDPR-i artiklis 28 s&auml;testatud kohustusi, eeldusel, et taotletud teave on Haldaja valduses v&otilde;i kontrolli all. Kui see osutub Kliendile ebapiisavaks, peab Haldaja tegema Kliendiga koost&ouml;&ouml;d, sealhulgas lubama Kliendi v&otilde;i Kliendi volitatud ja Haldaja aktsepteeritud muu audiitori korraldatavaid m&otilde;istlikke auditeid, sealhulgas inspektsioone, ja neile kaasa aitama. Selliste auditite ja inspektsioonide &uuml;ksikasjad lepitakse Poolte vahel kokku, kuid olenemata sellest kohaldub allpool toodu:</span>
</p>
<ul class="c12 lst-kix_list_11-0 start">
    <li class="c1 c5"><span class="c2">Haldaja on kohustatud andma Kliendile teavet, toimikuid ja dokumente ainult siis, kui see on m&otilde;istlikult vajalik, et n&auml;idata tema kohustuste t&auml;itmist vastavalt k&auml;esolevale paragrahvile 7 ja GDPR-i artiklile 28 seoses Sisus sisalduvate isikuandmetega;</span>
    </li>
    <li class="c3 c5"><span class="c2">Haldaja ei avalda mingit teavet, mingeid toimikuid ega muid dokumente, millele kohalduvad tema &auml;risaladused;</span>
    </li>
    <li class="c3 c5"><span class="c2">Haldaja ei avalda mingit teavet, mingeid toimikuid ega muid dokumente, mis tooks kaasa kohalduvatest &otilde;igusaktidest v&otilde;i muude klientide v&otilde;i isikutega s&otilde;lmitud lepingutest tuleneva konfidentsiaalsuskohustuste rikkumise;</span>
    </li>
    <li class="c3 c5"><span class="c2">Haldaja ei avalda mingit teavet, mingeid toimikuid ega muid dokumente, mis on seotud asjaga, mille suhtes on k&auml;imas, on menetluses v&otilde;i &auml;hvardab tulla kohtuvaidlus v&otilde;i muu vaidluse lahendamise mehhanism Kliendi ja Haldaja vahel;</span>
    </li>
    <li class="c3 c5"><span class="c2">Klient k&auml;sitleb konfidentsiaalsena kogu teavet, k&otilde;iki toimikuid ja muid dokumente, mis on Kliendile antud vastavalt k&auml;esolevale paragrahvile 7;</span>
    </li>
    <li class="c14 c5"><span class="c2">Klient ei v&otilde;i rakendada oma &otilde;igust auditi tegemiseks k&auml;esoleva paragrahvil 7 alusel sagedamini kui &uuml;ks kord kalendriaasta jooksul, v&auml;lja arvatud siis, kui tal on p&otilde;hjendatud kahtlus Haldaja kohustuste t&auml;itmises.</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">7.14.</span><span class="c4">&nbsp;Kasutajad ei v&otilde;i Platvormile lisada Sisu, mis sisaldab viiruseid v&otilde;i arvutiprogramme ja faile, mis kahjustavad v&otilde;i katkestavad muul viisil Platvormi tavap&auml;rast t&ouml;&ouml;tamist v&otilde;i mis on salvestatud Kliendi v&otilde;i Kasutaja arvutites ja katkestavad v&otilde;i kahjustavad nende normaalset t&ouml;&ouml;tamist. Klient vastutab selle tagamise eest.</span>
</p>
<p class="c13"><span class="c6 c5">7.15.</span><span class="c4">&nbsp;Klient annab Haldajale Sisu jaoks vajalikud &otilde;igused (sealhulgas lihtlitsentsi, mis puudutab igasugust intellektuaalomandi &otilde;igustega kaitstud Sisu) ning kinnitab, et tal on &otilde;igus niiviisi toimida.</span>
</p>
<p class="c7"><span class="c2"></span></p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">8. INTELLEKTUAALNE OMAND</span></p>
<p class="c0"><span class="c6 c5">8.1.</span><span class="c4">&nbsp;Platvorm, Veebileht ning nende mis tahes osad ja elemendid (sealhulgas andmebaasid ja tarkvara, &auml;rinimed, kaubam&auml;rgid, &auml;risaladused, domeeninimed jms) on kaitstud intellektuaalse omandi &otilde;igustega, mis kuuluvad Haldajale, tema t&ouml;&ouml;tajatele v&otilde;i Haldaja koost&ouml;&ouml;partneritele.</span>
</p>
<p class="c0"><span class="c6 c5">8.2.</span><span class="c2 c5">&nbsp;K&auml;esoleva Lepinguga omandab Klient &uuml;ksnes lihtlitsentsi, mille alusel on Kliendil &otilde;igus Platvormi kasutada. K&auml;esoleva Lepingu alusel omandatav lihtlitsents kehtib kuni k&auml;esoleva Lepingu l&otilde;ppemiseni. Haldaja lubab Lepingu kehtivuse ajal Kliendil ja Kasutajatel kasutada Platvormi funktsionaalsust oma sisemisteks vajadusteks koosk&otilde;las Lepingu tingimustega tavalisel otstarbel, milleks Platvorm on m&otilde;eldud. Haldaja ei anna Kliendile ega Kasutajale mis tahes muid litsentse ega &otilde;igusi ning Kliendil ega Kasutajal ei teki Platvormile ega Veebilehele intellektuaalse omandi &otilde;igusi.</span>
</p>
<p class="c0"><span class="c6 c5">8.3.</span><span class="c4">&nbsp;Kasutaja ega Klient ei v&otilde;i Platvormi, Veebilehte ega nende komponente ilma Haldaja eelneva kirjaliku n&otilde;usolekuta muuta, kopeerida, paljundada, levitada, t&ouml;&ouml;delda, t&otilde;lkida, teha v&auml;ljav&otilde;tteid, edastada, l&uuml;litada teistesse andmebaasidesse ega &uuml;ldsusele k&auml;ttesaadavaks teha ega muul moel kasutada Platvormi v&otilde;i Veebilehe suhtes tekkinud intellektuaalse omandi &otilde;igusi. Samuti ei ole Kliendil ega Kasutajal &otilde;igust anda Platvormi, Veebilehe v&otilde;i nende komponentide kasutamiseks all-litsentse ega luua nende p&otilde;hjal uusi intellektuaalse omandi objekte. Keelatud on Platvormi, Veebilehe v&otilde;i nende mis tahes osi ilma Haldaja eelneva kirjaliku n&otilde;usolekuta m&uuml;&uuml;a, rentida, litsentsida, liidestada Kliendi v&otilde;i kolmandate isikute s&uuml;steemidega v&otilde;i kasutada neid mis tahes programmide abil, mis koormavad v&otilde;i segavad Platvormi v&otilde;i Veebilehe t&ouml;&ouml;d v&otilde;i mis moonutavad Sisu.</span>
</p>
<p class="c7"><span class="c8 c6"></span></p>
<p class="c16"><span class="c8 c6">9. KONFIDENTSIAALSUS</span></p>
<p class="c20 c23"><span class="c8 c6"></span></p>
<p class="c20"><span class="c2">9.1. Pool on kohustatud hoidma Lepingu kehtivuse ajal ning p&auml;rast Lepingu l&otilde;ppemist t&auml;htajatult teise Poole &auml;ri- ja tootmissaladusi (edaspidi Konfidentsiaalne teave) mis on seotud ja saadud seoses k&auml;esoleva lepingu s&otilde;lmimise ja t&auml;itmisega ning v&otilde;tma tarvitusele m&otilde;istlikud meetmed Konfidentsiaalse teabe kolmandate isikute valdusesse sattumise v&auml;ltimiseks. Selle punkti s&auml;tteid ei kohaldata teabe suhtes, mis on avalikult k&auml;ttesaadav k&auml;esoleva lepingu s&otilde;lmimise ajal v&otilde;i muutub avalikult k&auml;ttesaadavaks p&auml;rast selle s&otilde;lmimist muul viisil kui konfidentsiaalsuse punkti rikkumise teel. Lepingupool v&otilde;ib avaldada konfidentsiaalset teavet m&auml;&auml;ral, mil see on n&otilde;utav seaduse v&otilde;i kohtum&auml;&auml;ruse alusel.</span>
</p>
<p class="c20"><span class="c2">9.2. Konfidentsiaalseks teabeks peetakse igasugust teisele poolele teatavaks tehtud kirjalikku v&otilde;i suulist teavet, mida pool ise ei anna &uuml;ldiseks kasutamiseks ja mille sellisest k&auml;sitlemisest on teist poolt teavitatud v&otilde;i mille puhul teine pool peab seda m&otilde;istlikult eeldama.</span>
</p>
<p class="c20"><span class="c2">9.3 Konfidentsiaalseks teabeks loetakse muuhulgas, kuid mitte ainult:</span></p>
<ul class="c12 lst-kix_list_19-0 start">
    <li class="c9"><span class="c2">Lepingud ja kokkulepped sealhulgas nende sisu, pooled ja t&auml;itmine ning tasu suurus;</span>
    </li>
    <li class="c9"><span class="c2">andmed klientide, &auml;ripartnerite, tarnijate kohta;</span></li>
    <li class="c9"><span class="c2">Poole finantsmajanduslik seis, &auml;riplaanid, struktuur; </span></li>
    <li class="c9"><span class="c2">hinnakujundus- ja tootmisinfo;</span></li>
    <li class="c9"><span class="c2">kasutatav tehnoloogia, seadmed, tarkvara, materjalid ja sellega seonduvad dokumendid; </span>
    </li>
    <li class="c9"><span class="c2">tark- ja riistvaraga seonduv informatsioon, arvutiprogrammid; </span></li>
    <li class="c9"><span class="c2">juhtorganite otsused;</span></li>
    <li class="c21"><span class="c2">t&ouml;&ouml;tajate andmed, sealhulgas nende tasustamise tingimused ja meetodid (summad, arvutusmeetodid, maksetingimused jne);</span>
    </li>
    <li class="c21"><span class="c2">igasugune teave seoses teise Lepingupoole intellektuaalomandiga.</span></li>
    <li class="c9"><span class="c2">salas&otilde;nad, koodid; </span></li>
    <li class="c20 c25"><span class="c2">mistahes muu informatsioon, mille saladuses hoidmiseks on teisel Poolel selgelt &auml;ratuntav huvi.</span>
    </li>
</ul>
<p class="c7 c5 c17"><span class="c2"></span></p>
<p class="c7"><span class="c2"></span></p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">10. TUGI-, HOOLDUS- JA ARENDUSTEENUSED</span></p>
<p class="c0"><span class="c6 c5">10.1.</span><span class="c4">&nbsp;Haldaja uuendab pidevalt Platvormi toimimise aluseks olevaid mehhanisme, et pakkuda Klientidele ja Kasutajatele kvaliteetseid Teenuseid. Selle eesm&auml;rgi t&auml;itmiseks v&otilde;ib Haldaja aeg-ajalt Platvormi ja selle komponente muuta, samuti muuta n&otilde;udeid Platvormi ja selle kaudu osutatavate Teenuste kasutamiseks vajalikule riist- ja tarkvarale. Haldaja teavitab Klienti ja Kasutajaid olulisematest muudatustest ette m&otilde;istliku aja enne nende j&otilde;ustumist, v&otilde;ttes arvesse muudatuse t&otilde;en&auml;olist m&otilde;ju Klientidele ja Kasutatele.</span>
</p>
<p class="c0"><span class="c6 c5">10.2.</span><span class="c4">&nbsp;Haldaja v&otilde;ib pakkuda Platvormi kasutamiseks mitmesuguseid abimaterjale, mis on k&auml;ttesaadavad Veebilehel. Probleemide, k&uuml;simuste ja ettepanekute korral on Kliendil ja Kasutajatel v&otilde;imalik Haldajaga &uuml;hendust v&otilde;tta Veebilehel toodud kontaktide kaudu.</span>
</p>
<p class="c0"><span class="c6 c5">10.3.</span><span class="c4">&nbsp;Kui Klient kasutab Platvormi tasulise hinnapaketi alusel, siis tagab Haldaja Kliendile Platvormi tehnilises lahenduses asjakohased versiooniuuendused ja v&auml;rskendused.</span>
</p>
<p class="c0"><span class="c6 c5">10.4.</span><span class="c4">&nbsp;Haldaja v&otilde;ib ajutiselt peatada ligip&auml;&auml;su Platvormile, kui see on vajalik Platvormi muutmise, hooldamise v&otilde;i uuendamise eesm&auml;rgil, kolmandate isikute asendus-, muutmis- v&otilde;i hooldust&ouml;&ouml;de t&otilde;ttu v&otilde;i muudel juhtudel, mis tulenevad &otilde;igusaktidest v&otilde;i p&auml;devate ametkondade otsustest. Regulaarsetest hooldust&ouml;&ouml;dest teavitab Haldaja Kliente ja Kasutajaid ette Platvormi vahendusel v&auml;hemalt 2 t&ouml;&ouml;p&auml;eva. Selleks, et v&otilde;imalikult v&auml;he h&auml;irida Platvormi kasutamist, teostab Haldaja korralisi hooldus- ja/v&otilde;i arendust&ouml;id t&ouml;&ouml;p&auml;eviti vahemikus kell 18.00-07.00 (CET) v&otilde;i n&auml;dalavahetustel.</span>
</p>
<p class="c0"><span class="c6 c5">10.5.</span><span class="c4">&nbsp;Kui Platvormis esineb vigu v&otilde;i mis tahes muid funktsionaalsush&auml;ireid, mille tulemusel Platvormi kasutamine on takistatud, siis teeb Haldaja k&otilde;ik m&otilde;istlikult v&otilde;imaliku, et need h&auml;ired k&otilde;rvaldada esimesel v&otilde;imalusel, kuid mitte hiljem kui 48 tunni jooksul alates veateate saamisest. Kui funktsionaalsuse h&auml;ired on v&auml;het&auml;htsad, v&otilde;ib Haldaja need k&otilde;rvaldada ka hiljem kui 48 tunni jooksul veateate saamisest. Sellisel juhul teavitab Haldaja sellest Klienti.</span>
</p>
<p class="c16 c5"><span class="c8 c6">&nbsp;</span></p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">11. LEPINGU L&Otilde;PPEMINE</span></p>
<p class="c0"><span class="c6 c5">11.1.</span><span class="c4">&nbsp;Leping on s&otilde;lmitud t&auml;htajatult, v.a juhul, kui Eritingimustes teisiti kokku ei ole lepitud. </span><span>Poolel on &otilde;igus Leping mistahes p&otilde;hjusel l&otilde;petada teatades Lepingu l&otilde;petamisest teisele Poolele kirjalikult ette v&auml;hemalt 2 (kaks) kuud</span><span
        class="c4">&nbsp;</span><span class="c4">e-posti teel v&otilde;i Platvormi kaudu.</span></p>
<p class="c0"><span class="c6 c5">11.2.</span><span class="c4">&nbsp;Haldajal on &otilde;igus &uuml;hepoolselt Leping koheselt, ilma etteteatamata &uuml;les &ouml;elda, kui:</span>
</p>
<ul class="c12 lst-kix_list_12-0 start">
    <li class="c1 c5"><span class="c2">Klient on enda kohta esitanud valeandmeid;</span></li>
    <li class="c3 c5"><span class="c2">Klient ei ole Teenust kasutanud j&auml;rjest v&auml;hemalt &uuml;he aasta jooksul;</span>
    </li>
    <li class="c3 c5"><span class="c2">selgub, et Kliendi nimel Platvormi, Teenuseid v&otilde;i Kliendikontot kasutanud isikul puudub esindus&otilde;igus Kliendi nimel tegutsemiseks;</span>
    </li>
    <li class="c3 c5"><span class="c2">on v&auml;lja kuulutatud Kliendi pankrot v&otilde;i saneerimine, samuti kui Kliendi suhtes on alustatud sundl&otilde;petamine v&otilde;i likvideerimine;</span>
    </li>
    <li class="c3 c5"><span
            class="c2">Klient tahtlikult v&otilde;i raskelt hooletult p&otilde;hjustab Haldajale kahju;</span></li>
    <li class="c14 c5"><span class="c2">teistel Lepingus nimetatud p&otilde;hjustel.</span></li>
</ul>
<p class="c13"><span class="c6 c5">11.3.</span><span class="c4">&nbsp;Kummalgi Poolel on &otilde;igus Leping ilma etteteatamata &uuml;les &ouml;elda, kui teine Pool rikub oluliselt Lepingut ega ole seda rikkumist k&otilde;rvaldanud talle antud t&auml;iendava m&otilde;istliku t&auml;htaja jooksul.</span>
</p>
<p class="c13"><span class="c6 c5">11.4.</span><span class="c4">&nbsp;Lepingu l&otilde;ppemisel sulgeb Haldaja vastavad kontod ning, kui ei ole kokku lepitud teisiti, kustutab nendel asuva Sisu 90 p&auml;eva p&auml;rast Lepingu l&otilde;ppemist. Haldaja ei kustuta sellist Sisu, mille s&auml;ilitamine on kohustuslik kohalduva &otilde;iguse alusel. Klient saab laadida alla Sisu koopia &uuml;ldtunnustatud formaadis v&otilde;i, kui Kliendikonto peaks mingil p&otilde;hjusel olema mittekasutatav, paluda Haldajal edastada see 90 p&auml;eva jooksul Lepingu l&otilde;ppemisest.</span>
</p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">12. HALDAJA &Otilde;IGUSKAITSEVAHENDID</span></p>
<p class="c0"><span class="c6 c5">12.1.</span><span class="c4">&nbsp;Haldajal ei ole kohustust kontrollida Kasutajate poolt Platvormile lisatud Sisu ja Kasutaja toiminguid Platvormil. Samuti ei ole Haldaja kohustatud j&auml;lgima Kasutajate tegevust ega teavet v&otilde;i Sisu, mida nad Platvormile lisavad v&otilde;i selle kaudu edastavad, vahem&auml;llu salvestavad v&otilde;i talletavad. Samas on Haldajal info&uuml;hiskonna teenuse seadusest tulenev kohustus teavitada p&auml;devaid j&auml;relevalveasutusi v&otilde;imalikust ebaseaduslikust tegevusest v&otilde;i pakutavast teabest ning identifitseerida neid Kliente ja Kasutajaid, kellele ta osutab andmete talletamise teenust.</span>
</p>
<p class="c0"><span class="c6 c5">12.2.</span><span class="c4">&nbsp;Kui Klient v&otilde;i Kasutaja rikub Lepingut, Platvormil kehtivaid h&auml;id tavasid v&otilde;i &otilde;igusakte, on Haldajal &otilde;igus:</span>
</p>
<ul class="c12 lst-kix_list_13-0 start">
    <li class="c1 c5"><span class="c2">k&otilde;rvaldada rikkumine v&otilde;i &otilde;igusvastane Sisu;</span></li>
    <li class="c3 c5"><span class="c2">n&otilde;uda rikkumise l&otilde;petamist ja k&auml;itumise v&otilde;i Sisu koosk&otilde;lla viimist Lepingu, hea tava v&otilde;i &otilde;igusaktidega;</span>
    </li>
    <li class="c3 c5"><span class="c2">ajutiselt t&otilde;kestada Kliendi v&otilde;i Kasutaja juurdep&auml;&auml;s Platvormile v&otilde;i selle mis tahes osale, sh ajutiselt sulgeda Kasutajakonto;</span>
    </li>
    <li class="c5 c14"><span class="c2">piirata Kliendi v&otilde;i Kasutaja kasutus&otilde;igusi.</span></li>
</ul>
<p class="c13"><span class="c6 c5">12.3.</span><span class="c4">&nbsp;Kui Kliendi v&otilde;i Kasutaja rikkumine on korduv v&otilde;i muul p&otilde;hjusel oluline, siis on Haldajal &otilde;igus keelata p&uuml;sivalt Kliendil v&otilde;i Kasutajal vastava Teenuse v&otilde;i Platvormi osa kasutamine, kustutada Kasutajakonto v&otilde;i Leping l&otilde;petada ilma ette teatamata.</span>
</p>
<p class="c13"><span class="c6 c5">12.4.</span><span class="c4">&nbsp;Haldaja v&otilde;ib Platvormilt kaebuse alusel eemaldatud Sisu uuesti Platvormile lisada v&otilde;i sellele juurdep&auml;&auml;su taastada juhul, kui Haldajale esitatakse veenvad t&otilde;endid Sisu vastavuse kohta Lepingule, Platvormil kehtivatele headele tavadele v&otilde;i &otilde;igusaktidele.</span>
</p>
<p class="c7"><span class="c2"></span></p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">13. VASTUTUS</span></p>
<p class="c0"><span class="c6 c5">13.1.</span><span class="c4">&nbsp;Haldaja pakub Platvormi &bdquo;nii nagu on&ldquo; p&otilde;him&otilde;ttel. Haldaja ei anna Kliendile lisaks Kasutustingimustes s&otilde;naselgelt kirjeldatule mingeid t&auml;iendavaid lubadusi ega v&otilde;ta kohustusi Platvormi kaudu k&auml;ttesaadavate konkreetsete funktsioonide, &otilde;igusaktidele vastavuse, kindlaks eesm&auml;rgiks kasutamisk&otilde;lblikkuse, t&ouml;&ouml;kindluse, k&auml;ttesaadavuse ega Kliendi vajadustele vastavuse kohta, v.a kui Pooled on selles eraldi kokku leppinud.</span>
</p>
<p class="c0"><span class="c6 c5">13.2.</span><span class="c4">&nbsp;Ulatuses, millises see on kohalduva &otilde;iguse alusel lubatud, vastutab Haldaja ainult siis, kui ta on oma tegevuses v&otilde;i tegevusetuses s&uuml;&uuml;di. Haldaja koguvastutus (sh intressid) k&otilde;igi Lepingu rikkumisega seotud n&otilde;uete eest on piiratud kahekordse Lepinguj&auml;rgse kuutasu v&otilde;i tegeliku kahjuga, olenevalt sellest, kumb on v&auml;iksem. Ulatuses, millises see on kohalduva &otilde;iguse alusel lubatud, ei vastuta Haldaja Kliendile v&otilde;i Kasutajale tekitatud saamata j&auml;&auml;nud tulu, puhtmajandusliku kahju ega mittevaralise kahju eest, samuti muude kaudsete, erilaadsete, tegevuse tulemusest johtuvate, hoiatava v&otilde;i karistava iseloomuga kahjude eest. Samuti ei vastuta Haldaja kahju ja muude tagaj&auml;rgede eest, mis on tekkinud j&auml;rgmistel p&otilde;hjustel:</span>
</p>
<ul class="c12 lst-kix_list_14-0 start">
    <li class="c1 c5"><span
            class="c2">Veebileht v&otilde;i Infos&uuml;steem ei toimi m&otilde;nes veebilehitsejas;</span></li>
    <li class="c3 c5"><span class="c2">Kliendi ja Kasutaja v&otilde;i Klientide v&otilde;i Kasutajate vahel tekkinud vaidlused;</span>
    </li>
    <li class="c3 c5"><span class="c2">Kliendi v&otilde;i Kasutaja poolt Platvormi vahendusel kolmandate isikutega (nt Kandidaatide v&otilde;i Kliendi klientidega) tehtud tehingute kehtivuse, t&auml;itmise ja &otilde;igusp&auml;rasuse eest;</span>
    </li>
    <li class="c3 c5"><span class="c2">Kliendi v&otilde;i Kasutaja poolt Platvormile Sisu lisamine, mis ei ole v&otilde;i mida ei kasutata koosk&otilde;las Lepingu, Platvormil kehtiva hea tava v&otilde;i &otilde;igusaktidega;</span>
    </li>
    <li class="c3 c5"><span class="c2">Kliendi poolt Kasutajakontode haldamine, sh Kliendikonto v&otilde;i Kasutajakonto kaudu toimepandud &otilde;igusrikkumised v&otilde;i Kasutustingimuste rikkumised, s&otilde;ltumata sellest, kas tegemist oli Kliendikonto v&otilde;i Kasutajakonto kasutamiseks volitatud isikuga;</span>
    </li>
    <li class="c3 c5"><span class="c2">&otilde;igusaktides ja nende t&otilde;lgendamises toimunud muudatused, nende m&otilde;jud Klientide v&otilde;i Kasutajate &auml;ritegevusele ning vastavate muudatuste kajastamine Platvormis, kui see ei ole Haldajale &otilde;igusaktidest v&otilde;i Haldaja suhtes j&otilde;ustunud kohtulahenditest tulenevalt kohustuslik;</span>
    </li>
    <li class="c3 c5"><span class="c2">v&auml;&auml;ramatu j&otilde;ud ja muud Haldajast mitteolenevad vead v&otilde;i h&auml;ired, mis takistavad Kliendil v&otilde;i Kasutajatel Platvormi, Veebilehe ja/v&otilde;i Teenuste kasutamist (n&auml;iteks katkestused interneti&uuml;henduses jne);</span>
    </li>
    <li class="c3 c5"><span class="c2">Kliendi v&otilde;i Kasutaja seadmetes esinevad vead, kahjustused v&otilde;i Platvormi v&otilde;i Veebilehe kasutamiseks sobimatud seadistused;</span>
    </li>
    <li class="c3 c5"><span class="c2">korraliste hooldus- ja/v&otilde;i arendust&ouml;&ouml;de t&otilde;ttu Platvormi, Veebilehe ja/v&otilde;i Teenuste kasutamisel tekkinud viivitused, katkestused v&otilde;i t&otilde;rked;</span>
    </li>
    <li class="c3 c5"><span class="c2">andmete t&ouml;&ouml;tlemine kolmandate isikute poolt, kellele Haldaja on need edastanud Kliendi v&otilde;i Kasutaja n&otilde;usolekul, v.a alamt&ouml;&ouml;tlejad, kes t&ouml;&ouml;tlevad Sisus olevaid isikuandmeid;</span>
    </li>
    <li class="c3 c5"><span class="c2">kui Haldaja saab teada Platvormi vahendusel toime pandud v&otilde;i kestvast &otilde;igusrikkumisest, k&otilde;rvaldab selle v&otilde;i t&otilde;kestab sellele juurdep&auml;&auml;su v&otilde;i v&otilde;tab aktiivselt tarvitusele muud meetmed &otilde;igusrikkumise l&otilde;petamiseks v&otilde;i tagaj&auml;rgede k&otilde;rvaldamiseks;</span>
    </li>
    <li class="c3 c5"><span class="c2">Haldaja &otilde;iguskaitsevahendite kasutamine ja selle t&otilde;ttu Kliendile, Kasutajale v&otilde;i kolmandale isikule tekkinud kahju v&auml;lja arvatud juhul, kui hiljem selgub, et tegemist ei olnud rikkumisega;</span>
    </li>
    <li class="c3 c5"><span class="c2">Kliendikonto v&otilde;i Kasutajakonto parooli kadumine v&otilde;i volitamata kolmandate isikute k&auml;tte sattumine v&otilde;i kolmandate isikute poolt kasutamine;</span>
    </li>
    <li class="c14 c5"><span class="c2">kolmandate isikute s&uuml;steemides (n&auml;iteks Amazon Web Services, Microsoft Office 365, Facebook, Linkedin, t&ouml;&ouml;portaalid, kolmandate osapoolte v&otilde;i Kliendi veebilehed, jt) esinevad t&otilde;rked ja puudused, mis m&otilde;jutavad Platvormi ja/v&otilde;i Teenuste toimimist ning k&auml;ttesaadavust</span>
    </li>
</ul>
<p class="c13"><span class="c6 c5">13.3.</span><span class="c4">&nbsp;Klient h&uuml;vitab Haldajale igasuguse kahju, mis on Haldajale tekkinud mis tahes n&otilde;uetest, kohustustest, hagidest, kaebustest, s&uuml;&uuml;distustest, kuludest, kulutustest, jms (sh &otilde;igusabikuludest), mis on tingitud sellest v&otilde;i seotud sellega, et Klient on rikkunud oma k&auml;esolevast Lepingust v&otilde;i Andmekaitsealastest &otilde;igusaktidest tulenevaid kohustusi isikuandmete vastutava t&ouml;&ouml;tlejana.</span>
</p>
<p class="c7"><span class="c2"></span></p>
<p class="c7 c5"><span class="c8 c6"></span></p>
<p class="c16 c5"><span class="c6">14. KOHALDUV &Otilde;IGUS JA VAIDLUSTE LAHENDAMINE</span></p>
<p class="c0"><span class="c6 c5">14.1.</span><span class="c4">&nbsp;Lepingule kohaldatakse Eesti Vabariigis kehtivaid &otilde;igusakte.</span>
</p>
<p class="c0"><span class="c6 c5">14.2.</span><span class="c4">&nbsp;Kui Klient ei ole rahul Haldaja tegevusega, on tal &otilde;igus esitada Haldajale kaebus. Haldaja teeb pingutusi, et lahendada lahkarvamused l&auml;bir&auml;&auml;kimiste teel. Ka muud Kliendi ja Haldaja vahelised vaidlused seoses Lepinguga p&uuml;&uuml;takse lahendada l&auml;bir&auml;&auml;kimiste teel.</span>
</p>
<p class="c0"><span class="c6 c5">14.3.</span><span class="c4">&nbsp;Kui kaebuse v&otilde;i muu vaidluse lahendamine l&auml;bir&auml;&auml;kimiste teel eba&otilde;nnestub, siis lahendatakse vaidlus Harju Maakohtus, Eestis.</span>
</p>
<p class="c0"><span class="c4">Kasutustingimused kehtivad alates 22.05.2020</span></p></body>
</html>
