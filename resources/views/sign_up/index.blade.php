@extends('adminlte::master')

@section('classes_body', 'sidebar-collapse')

@section('body')
    <div class="wrapper">
        <div class="content-wrapper">
            <div class="container">
                <div class="row">
                    <div class="col-12 offset-md-4 col-md-4">
                        <div class="card mt-6">
                            <div class="card-body">
                                <div class="text-center row mb-4">
                                    <div class="col-6 offset-3">
                                        <img src="/rl_logo_wide.svg" class="img-fluid" alt="">
                                    </div>
                                </div>
                                <a href="/sign-up/linkedin/redirect">
                                    <img src="/img/Sign-In-Large---Default.png" class="img-fluid" alt="">
                                </a>
                                <div class="separator text-muted my-4">or</div>
                                <p>
                                    Sign up with your work email.
                                </p>
                                <form action="/start-v2" method="post"
                                      id="sign-up-form"
                                    onsubmit="document.getElementById('submit-btn').disabled = true;
                                    setTimeout(() => document.getElementById('setting-up').classList.remove('d-none'), 500)"
                                >
                                    @csrf
                                    <div class="form-group">
                                        <label for="email" class="">Email address</label>
                                        <input name="email" type="email" placeholder="<EMAIL>" id="email"
                                               value="{{old('email')}}"
                                               class="form-control">
                                        <input name="success_url" value="{{old('success_url')}}"
                                               type="hidden">
                                        @if($errors->get('email'))
                                            @foreach($errors->get('email') as $err)
                                                <div class="text-danger mb-2">{{$err}}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="form-group">
                                        <label for="name">Your name</label>
                                        <input
                                            class="form-control"
                                            id="name"
                                            type="text" name="name"
                                            value="{{old('name')}}">
                                        @if($errors->get('name'))
                                            @foreach($errors->get('name') as $err)
                                                <div class="text-danger mb-2">{{$err}}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="form-group">
                                        <label for="phone">Phone number</label>
                                        <input
                                            class="form-control"
                                            id="phone"
                                            type="text" name="phone"
                                            value="{{old('phone')}}">
                                        @if($errors->get('phone'))
                                            @foreach($errors->get('phone') as $err)
                                                <div class="text-danger mb-2">{{$err}}</div>
                                            @endforeach
                                        @endif
                                        <div class="text-sm mt-2 text-muted">Please include country code.</div>
                                    </div>
                                    <div class="form-group">
                                        <label for="volume">How many people do you hire per year?</label>
                                        <input
                                            class="form-control"
                                            id="volume"
                                            type="text" name="volume"
                                            value="{{old('volume')}}">
                                        @if($errors->get('volume'))
                                            @foreach($errors->get('volume') as $err)
                                                <div class="text-danger mb-2">{{$err}}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="form-group">
                                        <label for="password">Create password</label>
                                        <input
                                            class="form-control"
                                            id="name"
                                            type="password" name="password"
                                            value="{{old('password')}}">
                                        @if($errors->get('password'))
                                            @foreach($errors->get('password') as $err)
                                                <div class="text-danger mb-2">{{$err}}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="form-group">
                                        <label for="instance_name">Instance URL</label>
                                        <div class="input-group mb-3">
                                            <input type="text"
                                                   name="instance_name"
                                                   id="instance_name"
                                                   class="form-control"
                                                   value="{{old('instance_name')}}"
                                                   placeholder="companyname"
                                                   aria-describedby="basic-addon2">
                                            <div class="input-group-append">
                                                <span class="input-group-text"
                                                      id="basic-addon2">.{{config('app.top_domain')}}</span>
                                            </div>
                                        </div>
                                        @if($errors->get('instance_name'))
                                            @foreach($errors->get('instance_name') as $err)
                                                <div class="text-danger mb-2">{{$err}}</div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="form-group text-center">
                                        <button type="submit"
                                                id="submit-btn"
                                                class="btn btn-lg btn-success">Start</button>
                                        <div class="mt-2 d-none" id="setting-up">
                                            Setting up your account 😍. This may take up to 10 seconds.
                                        </div>
                                    </div>
                                </form>
                                <script>
                                    setTimeout(() => {
                                        const gdata = JSON.parse(localStorage.getItem('google_id'));
                                        let form = document.getElementById('sign-up-form');
                                        for (const gdataKey in gdata) {
                                            let inputEl = document.createElement('input');
                                            inputEl.type = 'hidden';
                                            inputEl.name = gdataKey;
                                            inputEl.value = gdata[gdataKey];
                                            form.appendChild(inputEl);
                                        }
                                    }, 2000);
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        analytics.track('Loaded sign up form');
    </script>
@stop
