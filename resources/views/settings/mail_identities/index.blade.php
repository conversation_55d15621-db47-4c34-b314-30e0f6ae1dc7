@extends('layouts.settings')

@section('title', __('Mail identities'))

@section('content_header')

@stop

@section('settings_header')
    <a href="{{route('mailIdentities.create')}}" class="btn btn-primary btn-sm">
        <i class="tdi td-plus mr-2"></i>
        {{ __('Add mail identity') }}
    </a>
@endsection

@section('settings_content')
    @if(session()->get('msg'))
        <b-alert show variant="danger">{{session()->get('msg')}}</b-alert>
    @endif
    <div class="text-sm">
        <p>
            {!! trans('Usually email from Teamdash goes <NAME_EMAIL>. You still get all the replies to your inbox because Teamdash includes your email in the Reply-To header.') !!}
        </p>
        <p>
            {!! trans('If you wish to send out all email from @yourdomain.xyz, please set up a mail identity.') !!}
        </p>
    </div>
    @if(!count($mailIdentities))
        <b-alert show variant="dark-light">
            {{ __('You have no mail identities set up yet.') }}
        </b-alert>
    @else
        <table class="table table-td">
            <thead>
                <tr>
                    <th>{{ __('Name') }}</th>
                    <th>{{ __('Provider') }}</th>
                    <th>{{ __('Status') }}</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach($mailIdentities as $identity)
                    <tr>
                        <td>{{$identity->domain}}
                            <strong>{{$identity->isDefault() ? '('. __('default') . ')' :''}}</strong></td>
                        <td>{{$identity->provider}}</td>
                        <td>{{$identity->is_verified ? __('Verified'): __('Pending')}}</td>
                        <td class="text-right">
                            <div class="d-flex align-items-center justify-content-end">
                                @if(!$identity->isDefault() && $identity->is_verified)
                                    <form method="post" action="/mailIdentities/{{$identity->id}}/makeDefault">
                                        @csrf
                                        @method('put')
                                        <button class="btn btn-white btn-sm">
                                            {{ __('Make default') }}
                                        </button>
                                    </form>
                                @elseif(!$identity->is_verified)
                                    <a
                                        href="{{route('mailIdentities.show', $identity->id)}}"
                                        class="btn btn-sm btn-white"
                                    >
                                        {{ __('Setup instructions') }}
                                    </a>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @endif
@stop

@section('js')
    @vite('resources/js/project/index.ts')
@stop

