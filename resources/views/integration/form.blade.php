@extends('layouts.settings')


@section('content_header')

@stop

@section('settings_header')
    <div v-if="$refs['bform']" class="d-flex align-items-center gap-2">
        @if(isset($integration))
            @php($hasMailClient = $integration->hasMailClient())
            @php($hasImports = $integration->imports()->count())
            @php($canDebug = array_key_exists($integration->remote_type, \App\Models\Integration::REMOTE_CLIENT_IMPL_MAP))
            @if($hasMailClient || $hasImports || $canDebug)
                <b-dropdown text="{{ __('Debug') }}" size="sm" variant="white">
                    @if($hasMailClient)
                        <b-dropdown-item href="{{route('integrations.debug', $integration)}}">
                            {{ __('Get debug report') }}
                        </b-dropdown-item>
                        <b-dropdown-item href="{{route('integrations.recreateCache', $integration)}}">
                            {{ __('Recreate project list dropdowns') }}
                        </b-dropdown-item>
                        <b-dropdown-text
                            class="mx-2"
                        >
                            <form action="{{route('integrations.downloadEmail', $integration)}}" method="post">
                                @csrf
                                <div class="row">
                                    <div class="d-flex">
                                        <input
                                            type="text" placeholder="Email ID"
                                            name="id"
                                            class="form-control mr-2 form-control-sm flex-grow-1"
                                        >
                                        <button class="btn btn-sm btn-default">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </b-dropdown-text>
                    @elseif($canDebug)
                        <b-dropdown-item href="{{route('integrations.debug', $integration)}}">
                            {{ __('Get debug report') }}
                        </b-dropdown-item>
                    @endif
                    @if($hasImports)
                        <b-dropdown-item href="{{route('integrations.runImports', $integration)}}">
                            {{ __('Run related imports') }}
                        </b-dropdown-item>
                    @endif
                </b-dropdown>
            @endif
            <a
                href="{{route('integrations.test', $integration)}}"
                v-if="$refs['bform'] && !$refs['bform'].dirty"
                class="btn btn-info btn-sm"
            >
                {{ __('Test connection') }}
            </a>
        @endif
        <button
            :disabled="$refs['bform'].busy || $refs['bform'].submitting"
            class="btn btn-success btn-sm"
            @click="$refs['bform'].submit()"
        >
            {{ __('Save') }}
        </button>
    </div>
@endsection

@section('settings_content')
    @if(isset($integration) && in_array($integration->remote_type, \App\Models\Integration::NOT_IMPLEMENTED))
        <div class="row">
            <div class="col">
                <div class="alert alert-info">
                    {{ __("The request for a new integration reached our Customer Support team. We'll get back to you within
                    two business days.") }}
                </div>
            </div>
        </div>
    @endif
    @if(session('errs') && count(session('errs')))
        <div class="row">
            <div class="col">
                <div class="alert alert-warning">
                    @foreach(session('errs') as $err)
                        {{$err}}
                    @endforeach
                </div>
            </div>
        </div>
    @elseif(session('errs') === [])
        <div class="row">
            <div class="col">
                <div class="alert alert-success">
                    {{ __('Import connection OK!') }}
                </div>
            </div>
        </div>
    @endif
    @if(session('debug'))
        <pre>{{json_encode(session('debug'), JSON_PRETTY_PRINT)}}</pre>
    @endif
    <div class="row mb-3">
        <div class="col text-right">

        </div>
    </div>
    {!! $form->render() !!}
    <x-form-submit-buttons />
{{--    <div v-if="$refs['bform']">--}}
{{--        <button--}}
{{--            :disabled="$refs['bform'].busy || $refs['bform'].submitting"--}}
{{--            class="btn btn-success btn-sm"--}}
{{--            @click="$refs['bform'].submit()"--}}
{{--        >Save--}}
{{--        </button>--}}
{{--    </div>--}}

@stop

@section('js')
    @vite('resources/js/forms.ts')
@stop
