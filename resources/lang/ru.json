{"# of candidates": "# кандидатов", "(Drag and drop to add attachments)": "(Перетаскивание для добавления вложений)", "(and 1 finished project)|(and {count} finished projects)": "", "(with API key)": "(с ключом API)", "+ {count} more": "+ {count} подробнее", "-> Hired": "", "-> Interviews": "", "-> Offers": "", "-> Rejected": "", "... and much more": "", "... or invite new users to Teamdash": "...или пригласить новых пользователей в Teamdash", ".xlsx file": "Файл .xlsx", "0 means the same day that the action is triggered, 1 means the next day, etc.": "0 означает тот же день, когда было запущено действие, 1 - следующий день и т.д.", "1 SMS sent | {count} SMS sent": "1 SMS отправлено | {count} SMS отправлено", "1 accepted | {count} accepted": "1 принято | {count} принято", "1 answered | {count} answered": "1 ответ дан | {count} ответов дано", "1 candidate|{count} candidates": "1 кандидат | {count} кандидата | {count} кандидатов", "1 comment | {count} comments": "1 комментарий | {count} комментария | {count} комментариев", "1 day in stage | {count} days in stage": "1 день на этапе | {count} дня на этапе | {count} дней на этапе", "1 day | {n} days": "1 день | {n} дня | {n} дней", "1 message | {count} messages": "1 сообщение | {count} сообщения | {count} сообщений", "1 month": "1 месяц", "1 open slot | {count} open slots": "1 открытое место | {count} открытых места | {count} открытых мест", "1 pending | {count} pending": "1 в ожидании | {count} в ожидании", "1 project from this template|{count} projects from this template": "", "1 referee | {count} referees": "1 рекомендатель | {count} рекомендателя | {count} рекомендателей", "1 selected|{count} selected": "1 выбран | {count} выбрано", "1 slot|{count} slots": "1 слот | {count} слота | {count} слотов", "1 submitted | {count} submitted": "1 подано | {count} подано", "1 year": "1 год", "1/2 Text with image": "1/2 Текст с изображением", "1/2 Text with video": "1/2 Текст с видео", "10 days": "10 дней", "10 minutes": "10 минут", "10 minutes (:00, :10, :20...)": "10 минут (:00, :10, :20...)", "12M": "12M", "14 days": "14 дней", "15 minutes": "15 минут", "15 minutes (:00, :15, :30, :45)": "15 минут (:00, :15, :30, :45)", "2 Options": "2 Варианты", "2 months": "2 месяца", "2/3 Text with square image": "2/3 Текст с квадратным изображением", "3 days": "3 дня", "3 months": "3 месяца", "30 days": "30 дней", "30 minutes": "30 минут", "30D": "30D", "4 Options": "4 Варианты", "6 months": "6 месяцев", "60 minutes": "60 минут", "6M": "6M", "7 days": "7 дней", "9 days later we purge your database of all candidates data which we didn't get a processing consent for": "Через 9 дней мы очистим вашу базу данных от всех данных кандидатов, на обработку которых мы не получили согласия", "90D": "90D", ":actingUser has assigned you as project manager in the project :positionName.": ":actingUser назначил вас менеджером проекта в проекте :positionName.", ":attribute cannot contain the @ symbol when publishing to CV Keskus.": "Атрибут :не может содержать символ @ при публикации в CV Keskus.", ":author assigned you a task|:author assigned you :count tasks": ":автор поручил вам задание|:автор поручил вам :считать задания", ":count accepted interview times": ":считать принятые интервью", ":count answered video interviews": "Ответили на видеоинтервью", ":count miscellaneous updates": "Разные обновления", ":count new candidates": ":подсчет новых кандидатов", ":count updates to tasks": ":подсчет обновлений задач", ":firstName tagged you in a comment about :candidateName": ":firstName отметил вас в комментарии о :candidateName", ":fromUser invited you to work on project :positionName.": ":fromUser пригласил вас поработать над проектом :positionName.", ":fromUser needs your help - :positionName": ":fromUser нуждается в вашей помощи - :positionName", ":managerName needs your attention for candidates in :stageName stage:": ":managerName требует вашего внимания к кандидатам на этапе :stageName:", ":name changed the deadline for reviewing the job requisition for :position. The new deadline is in :deadline.": "Компания :name изменила срок рассмотрения заявки на вакансию :position. Новый срок указан в :deadline.", ":name created a job requisition and marked you as the recruiter. You don't need to do anything right now. This is just a heads-up.": ":name создал заявку на работу и указал вас в качестве рекрутера. Вам не нужно ничего делать прямо сейчас. Это просто предупреждение.", ":name created a job requisition which needs your approval.": ":имя создало заявку на работу, которая нуждается в вашем одобрении.", ":name marked you as recruiter for :position": ":имя отметило вас как рекрутера на :должность", ":name marked you as the recruiter in the job requisition for :position. You don't need to do anything right now. This is just a heads-up.": "В заявке на вакансию :имя указано, что вы являетесь рекрутером. Вам не нужно ничего делать прямо сейчас. Это просто предупреждение.", ":name needs your approval for :position": ":имя нуждается в вашем одобрении на :должность", ":name requested your approval for a job requisition for :position.": ":ФИО обратился к вам с просьбой одобрить заявку на вакансию на должность :должность.", ":name started a requisition for :position": ":имя подало заявку на :должность", ":name wrote:": ":name wrote:", ":startTime :event with :people": ":startTime :event с :people", ":user shared :count candidates": "", ":user shared :count candidates for :position:": ":пользователь поделился :подсчитать кандидатов на :должность:", ":user tagged you in :position log entry": ":пользователь отметил вас в :записи журнала о положении", ":userName cancelled the event because they are unable to participate.": "Пользователь :userName отменил мероприятие, потому что не может в нем участвовать.", ":userName cancelled the interview.": ":userName отменил собеседование.", ":userName has invited you to use Teamdash": ":userName пригласил вас использовать Teamdash", ":userName has invited you to work on the project :projectName.": ":userName пригласил вас поработать над проектом :projectName.", "< {distance} from": "< {distance} с сайта", "A calendar event has been sent to your e-mail. You can close this window. You can come back to this url any time to see your interview time.": "На вашу электронную почту отправлено событие календаря. Вы можете закрыть это окно. Вы можете вернуться к этому окну в любое время, чтобы посмотреть время собеседования.", "A cleaned-up transcript segment in which only the \"text\" field has been cleaned up, but the \"time\" field has been left unchanged.": "Очищенный фрагмент стенограммы, в котором очищено только поле \"текст\", а поле \"время\" оставлено без изменений.", "A few days before sending out the renewal requests, you get a report - how many and which candidates are going to get renewal messages.": "За несколько дней до рассылки запросов на продление вы получаете отчет - сколько и какие кандидаты получат сообщения о продлении.", "A few ideas:": "Несколько идей:", "A fresh verification link has been sent to your email address.": "На ваш адрес электронной почты отправлена новая ссылка для проверки.", "A job has been unpublished: :position": "", "A new job has been posted: :position": "", "A positive candidate experience can help you attract and hire the best people.": "Положительный опыт кандидатов поможет вам привлечь и нанять лучших сотрудников.", "A reference has already been requested from this person at {lastMessageTime}.": "У этого человека уже запрашивали рекомендацию в {lastMessageTime}.", "A simple text block": "Простой текстовый блок", "A single sentence summarizing a specific aspect of the interview.": "Одно предложение, резюмирующее конкретный аспект интервью.", "A small heads-up!": "Небольшое предупреждение!", "A text string that contains the text representing a transcribed segment of speech.": "", "A timestamp that represents when the segment of speech occurred within the original recording.": "", "A video call link will be generated and included in calendar invites.": "Ссылка на видеозвонок будет сгенерирована и включена в приглашения календаря.", "AI": "AI", "AI Assistance": "Помощь ИИ", "AI-generated analysis": "<PERSON><PERSON><PERSON><PERSON><PERSON>, созданный искусственным интеллектом", "API": "API", "API documentation": "Документация по API", "API key": "Ключ API", "API keys": "", "API token": "API-токен", "Abkhazian": "Абхазия", "About employer": "О работодателе", "Abrasives and Nonmetallic Minerals Manufacturing": "Производство абразивов и неметаллических минералов", "Accept DPA": "Принять DPA", "Accept Terms of use": "Принять условия использования", "Accepted": "Принято", "Accepted interview invite": "Принято приглашение на собеседование", "Access URL": "URL-адрес доступа", "Access controls": "", "Access key": "Ключ доступа", "Accomodation Services": "Услуги по размещению", "Account inactive": "Аккаунт неактивен", "Accountant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Accounting": "Бухгалтерский учет", "Accounting / Auditing": "Бухгалтерский учет / аудит", "Accounting/Financial/Insurance": "Бухгалтерский учет/финансы/страхование", "Action Needed: :integration integration broken": "Необходимые действия: :интеграция сломана", "Action name": "Название действия", "Actions": "Действия", "Active": "Активный", "Active consent of type": "", "Active in other projects:": "Активно участвует в других проектах:", "Active projects": "Активные проекты", "Active until": "", "Activities to report": "Мероприятия для отчетности", "Activity custom fields": "Настраиваемые поля деятельности", "Activity report": "Отчет о деятельности", "Actor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ad unpublished!": "Объявление не опубликовано!", "Add": "Добавить", "Add Block": "Добавить блок", "Add a caption…": "Добавьте надпись...", "Add a comment to start the conversation.": "", "Add a descriptive project title": "Добавьте описательное название проекта", "Add a dropout reason": "Добавьте причину отсева", "Add a list of requirements to the project description": "Добавьте список требований к описанию проекта", "Add a new stage": "Добавить новый этап", "Add a quote from the Manager, it's the most important trust element": "Добавьте цитату управляющего, это самый важный элемент доверия.", "Add a stage action": "Добавить действие на этапе", "Add a stage action to {stageName}": "Добавьте действие на этапе {stageName}", "Add actions to this project to automate your workflow.": "", "Add actions to this stage to automate your workflow.": "Добавьте действия на этом этапе, чтобы автоматизировать рабочий процесс.", "Add another candidate": "Добавить еще одного кандидата", "Add another reference": "Добавьте ещё одну рекомендацию", "Add attachment": "Добавить вложение", "Add authentication provider": "Добавьте провайдера аутентификации", "Add automated project action": "", "Add automated stage action": "Добавьте автоматизированные действия на этапе", "Add bulk comments": "Добавление массовых комментариев", "Add bulk tags": "Добавление массовых тегов", "Add candidate": "Добавить кандидата", "Add candidate referee": "Добавить рекомендателя кандидата", "Add candidate reference": "Добавить рекомендацию кандидата", "Add candidates to existing set": "Добавьте кандидатов к существующему набору", "Add client": "Добавить клиента", "Add comment": "Добавить комментарий", "Add comment to decision": "Добавить комментарий к решению", "Add condition": "Добавить условие", "Add consent": "Добавить согласие", "Add consent subtype": "", "Add contact": "Добавить контакт", "Add contact to ": "Добавить контакт в ", "Add custom fonts": "", "Add dates": "", "Add dropout reason": "Добавить причину отсева", "Add education": "Добавить образование", "Add employment": "Добавить работу", "Add entry": "Добавить запись", "Add existing users...": "Добавьте существующих пользователей...", "Add field": "Добавить поле", "Add file": "Добавить файл", "Add file type": "", "Add filter": "Добавить фильтр", "Add import": "Добавить импорт", "Add integration": "Добавить интеграцию", "Add link": "Добавить ссылку", "Add location": "Добавить местоположение", "Add mail identity": "Добавить почтовый идентификатор", "Add my score": "Добавить свою оценку", "Add new action": "Добавить новое действие", "Add new client": "Добавить нового клиента", "Add new integration": "", "Add new location": "Добавить новое местоположение", "Add new manager": "Добавить нового менеджера", "Add new tag": "Добавить новый тег", "Add new user": "Добавить нового пользователя", "Add office": "Добавить офис", "Add project action": "", "Add project log entry": "Добавить запись в журнал проекта", "Add provider": "Добавить провайдера", "Add quote": "Добавить цитату", "Add rating": "", "Add reference form": "Добавить форму рекомендации", "Add scorecard": "Добавить оценочный лист", "Add stage": "Добавить этап", "Add stage action": "Добавить действие на этапе", "Add tag": "Добавить метку", "Add tags": "Добавить теги", "Add tags to the candidate": "Добавить теги к кандидату", "Add team": "Добавить команду", "Add the tag if...": "Добавьте тег, если...", "Add to favourites": "Добавить в избранное", "Add to project": "Добавить в проект", "Add to this project": "Добавить к этому проекту", "Add user": "Добавить пользователя", "Add users to project": "Добавление пользователей в проект", "Add video interview": "Добавить видеоинтервью", "Add your comment here...": "Добавьте свой комментарий здесь...", "Add {query}": "Добавить {query}", "Add-ons": "Дополнения", "Added": "Добавлено", "Added a dropout reason": "Добавлена причина отсева", "Added at": "Добавлено в", "Added comment": "Добав<PERSON>ен комментарий", "Added from API import": "Добавлено из импорта API", "Added from XLSX import": "Добавлено из импорта XLSX", "Added from sourcing extension": "Добавлено из расширения источников", "Added from xlsx import": "Добавлено из импорта xlsx", "Added manual source override": "Добавлено ручное переопределение источника", "Added to landing {landingLink}": "Добавлено в посадочный {landingLink}", "Additional city (CVbankas)": "", "Additional city 2 (CVbankas)": "", "Additional industries (CV-Library)": "Дополнительные отрасли (CV-Library)", "Additional info form": "", "Additional information form": "", "Additional options": "Дополнительные опции", "Address (SS.lv)": "Ад<PERSON><PERSON><PERSON> (SS.lv)", "Adds hidden custom fields and a diversity questionnaire.": "Добавляет скрытые пользовательские поля и анкету разнообразия.", "Adjuster": "Настройщик", "Admin": "", "Administration": "Администрация", "Administration & Support": "Администрирование и поддержка", "Administration of Justice": "Отправление правосудия", "Administration/work safety": "Администрирование/безопасность труда", "Administrative": "Административный", "Administrative and Support Services": "Административные и вспомогательные службы", "Administrator": "Администратор", "Advert type (profession.hu)": "Тип рекламы (profession.hu)", "Advertising": "Реклама", "Advertising Services": "Рекламные услуги", "Advertising agent": "Рекламный агент", "Advertising manager": "Менеджер по рекламе", "Afar": "<PERSON><PERSON><PERSON><PERSON>", "Afrikaans": "Африканцы", "After completing the API key steps, check your browser address bar": "После выполнения шагов по созданию ключа API проверьте адресную строку браузера", "After school": "После школы", "After you have reviewed the details you will be redirected to the checkout page. After successful payment your campaign details are reviewed by a human at Teamdash. You'll get a notification once your campaign has been approved and launched.": "После просмотра деталей вы будете перенаправлены на страницу оформления заказа. После успешной оплаты детали вашей кампании будут рассмотрены сотрудником Teamdash. Вы получите уведомление, когда ваша кампания будет одобрена и запущена.", "Age": "Возраст", "Agency": "Агентство", "Agency has clients, corporation has managers.": "У агентства есть клиенты, у корпорации - менеджеры.", "Agent": "Агент", "Agricultural Chemical Manufacturing": "Производство сельскохозяйственной химии", "Agriculture": "Сельское хозяйство", "Agriculture / Environmental": "Сельское хозяйство / Экология", "Agriculture, Construction, Mining Machinery Manufacturing": "Сельское хозяйство, строительство, горнодобывающая промышленность", "Agronomist": "Агроном", "Air, Water, and Waste Program Management": "Управление программами по воздуху, воде и отходам", "Airlines and Aviation": "Авиакомпании и авиация", "Akan": "Ак<PERSON><PERSON>", "Albanian": "Албания", "Align text to center": "Выравнивание текста по центру", "All": "Все", "All GDPR messages to candidates will be sent from this user.": "Все сообщения GDPR кандидатам будут отправляться от этого пользователя.", "All day": "Весь день", "All files of this type will appear as {baseFileType} after deletion.": "", "All forms": "", "All invites can be delivered via selected channels": "", "All is good": "Все хорошо", "All references": "Все рекомендации", "All responses": "Все ответы", "All rights reserved.": "Все права защищены.", "All roles": "Все роли", "All shared candidates": "", "All slots in this interview have been cancelled. Please add more slots.": "Все места в этом интервью были отменены. Пожалуйста, добавьте другие места.", "All stages in a project template must be categorized.": "Все этапы в шаблоне проекта должны быть распределены по категориям.", "All stages in all projects are categorized.": "Все этапы всех проектов распределены по категориям.", "All teams": "Все команды", "All times :tz (:offset).": "Все время :tz (:offset).", "All times are {timezone}": "Все время указано {timezone}", "All types": "Все типы", "All users": "Все пользователи", "All users of this instance must verify their phone numbers.": "Все пользователи этого экземпляра должны подтвердить свои номера телефонов.", "Allocation & Distribution": "Распределение и распределение", "Allow candidates without email": "Разрешить кандидатам не иметь электронной почты", "Allow candidates without location": "Разрешить кандидатам без указания местоположения", "Allow direct applications from CVO": "Разрешить прямые заявки от CVO", "Allow direct applications from job portals": "Разрешить прямые заявки с порталов вакансий", "Allow limited users to schedule interviews": "", "Allow login with password": "Разрешить вход с паролем", "Allow password logins": "Разрешить вход по паролю", "Allow regular users to create tags": "Разрешите обычным пользователям создавать теги", "Alphabetic": "Алфавитный", "Alphabetically": "По алфавиту", "Alternative Dispute Resolution": "Альтернативное разрешение споров", "Alternative Medicine": "Альтернативная медицина", "Always ask approval from these users": "Всегда спрашивайте разрешения у этих пользователей", "Ambulance Services": "Услуги скорой помощи", "Ameliorator": "Мелиор<PERSON><PERSON><PERSON>р", "Amharic": "Амхарский язык", "Amusement Parks and Arcades": "Парки развлечений и аркады", "An array containing segments of transcribed speech, ordered by when the speech occurred.": "", "An array in which each element is a summarization sentence.": "<PERSON>а<PERSON><PERSON><PERSON><PERSON>, каждый элемент которого представляет собой предложение с кратким содержанием.", "An array in which each object represents a cleaned-up transcript segment.": "Ма<PERSON><PERSON><PERSON><PERSON>, в котором каждый объект представляет очищенный сегмент транскрипта.", "An error occurred": "Произошла ошибка", "An error occurred when starting the camera.": "При запуске камеры произошла ошибка.", "An error occurred while processing the transcript.": "При обработке транскрипта произошла ошибка.", "An error occurred!": "Произошла ошибка!", "An object representing a single transcribed segment of speech.": "", "An unexpected error occurred.": "", "Analyst": "Аналитика", "And the candidate will get a message like this:": "И кандидат получит такое сообщение:", "Animal Feed Manufacturing": "Производство кормов для животных", "Animation and Post-production": "Анимация и постпродакшн", "Anonymize": "Анонимизация", "Anonymize candidate": "Анонимизация кандидата", "Anonymized CV": "", "Anonymized candidate": "Анонимный кандидат", "Anonymized summary": "", "Answered video interview": "Видеоинтервью с ответами", "Any": "Любой", "Any account": "<PERSON>юб<PERSON>й счет", "Any data you have entered in this form must be present on the landing page and vice versa.": "Все данные, которые вы ввели в форму, должны присутствовать на целевой странице и наоборот.", "Any time when available": "В любое время, когда есть возможность", "Any type": "<PERSON>юб<PERSON>й тип", "Apparatus operator": "Оператор оборудования", "Apparel Manufacturing": "Производство одежды", "Appearance": "", "Appliances, Electrical, and Electronics Manufacturing": "Производство бытовой техники, электрооборудования и электроники", "Applicants target stage": "Целевой этап заявителей", "Application date": "Дата подачи заявки", "Application deadline": "Срок подачи заявки", "Application form": "Форма заявки", "Application language": "Язык приложений", "Application methods": "Методы применения", "Applications": "Приложения", "Applied higher education": "Прикладное высшее образование", "Applied through form": "Применяется через форму", "Applies to all user signatures.": "Применяется ко всем подписям пользователей.", "Apply colours to all links": "Применить цвета ко всем ссылкам", "Applying to this position has ended.": "Прием заявок на эту должность завершен.", "Appraiser": "Оценщик", "Apprenticeship": "Ученичество", "Approval needed from": "Необходимо одобрение", "Approvals": "Одобрения", "Approvals deadline": "Срок утверждения", "Approve": "Утвердить", "Approved": "Одобрено", "Approved at": "Одобрено на", "Approved by:": "Одобрено:", "April": "Апрель", "Arabic": "Арабский язык", "Aragonese": "Арагонезе", "Architect": "Архитектор", "Architectural and Structural Metal Manufacturing": "Производство архитектурных и конструкционных металлов", "Architecture and Planning": "Архитектура и планирование", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archived": "Архивировано", "Archivist": "Архив<PERSON><PERSON><PERSON><PERSON><PERSON>", "Are Ukrainians welcome? (CVBankas)": "Приветствуются ли украинцы? (CVBankas)", "Are you sure you want to archive this form?": "", "Are you sure you want to cancel sending message \"{subject}\"?": "Вы уверены, что хотите отменить отправку сообщения \"{subject}\"?", "Are you sure you want to deactivate this user?": "", "Are you sure you want to delete action {actionName}?": "", "Are you sure you want to delete consent subtype: {consentSubtypeName}?": "", "Are you sure you want to delete file type {fileType}? ": "", "Are you sure you want to delete file {fileName}?": "", "Are you sure you want to delete the API key {apiKeyName}? ": "", "Are you sure you want to delete the landing page {landingName}?": "", "Are you sure you want to delete this API key?": "Вы уверены, что хотите удалить этот ключ API?", "Are you sure you want to delete this client?": "", "Are you sure you want to delete this comment?": "Вы уверены, что хотите удалить этот комментарий?", "Are you sure you want to delete this custom activity?": "", "Are you sure you want to delete this dropout reason?": "Вы уверены, что хотите удалить эту причину отсева?", "Are you sure you want to delete this event slot?": "Вы уверены, что хотите удалить этот слот событий?", "Are you sure you want to delete this image?": "Вы уверены, что хотите удалить это изображение?", "Are you sure you want to delete this tag? It is currently associated with {count} landings.": "Вы уверены, что хотите удалить этот тег? В настоящее время он связан с {count} высадками.", "Are you sure you want to generate a new API key for this integration?": "", "Are you sure you want to permanently delete this chat?": "Вы уверены, что хотите навсегда удалить этот чат?", "Are you sure you want to unpublish job ad {position_name} from {count} job portals?": "Вы уверены, что хотите отменить публикацию объявления о работе {position_name} на порталах {count}?", "Are you sure you want to unpublish job ad {position_name}?": "Вы уверены, что хотите отменить публикацию объявления о работе {position_name}?", "Area Management": "Управление территорией", "Armature fitter": "Монтажник арматуры", "Armed Forces": "Вооруженные силы", "Armenian": "Армянский", "Art / Creative": "Искусство / Творчество", "Artificial Rubber and Synthetic Fiber Manufacturing": "Производство искусственного каучука и синтетических волокон", "Artist": "Художник", "Artists and Writers": "Художники и писатели", "Arts": "Искусство", "Arts/Graphic Design": "Искусство/графический дизайн", "As the transcript has been auto-generated, it might include spelling errors.": "Поскольку стенограмма была сгенерирована автоматически, в ней могут быть орфографические ошибки.", "As the transcript text has been auto-generated, it might include spelling errors.": "", "Ask AI to write": "Попросите искусственный интеллект написать", "Ask candidate for references": "Попросите кандидата предоставить рекомендации", "Ask for data processing consent until": "Запрашивайте согласие на обработку данных до тех пор, пока", "Ask for reference": "Попросите рекомендацию", "Ask for reference again": "Снова попросите рекомендацию", "Ask for references": "Попросите рекомендации", "Ask referee to submit evaluation": "", "Ask reference to submit evaluation": "Попросите рекомендателя предоставить оценку", "Ask user for feedback about hiring process": "", "Assamese": "Ассамцы", "Assembler": "Ассемблер", "Assign room": "Назначить комнату", "Assignee": "Назначение", "Assistant": "Помощник", "Assistant Management": "Помощник руководителя", "Associate": "Ассоциированный", "Associate an existing landing page with this project": "Свяжите существующую целевую страницу с этим проектом", "Associate's degree": "Степень младшего специалиста", "Assure that declining this consent will not affect any pending candidacy.": "Заверьте, что отказ от этого согласия не повлияет на кандидатуру, находящуюся на рассмотрении.", "Async video interview invite": "Приглашение на видеоинтервью", "Asynchronous Video Interviews": "Асинхронные видеоинтервью", "Attach Files": "Прикрепить файлы", "Attachments": "Вложения", "Attachments from current project": "Вложения из текущего проекта", "Attachments from other projects": "Вложения из других проектов", "Attachments not linked to any project": "Вложения, не связанные ни с одним проектом", "Attorney": "Адвокат", "Audio and Video Equipment Manufacturing": "Производство аудио- и видеотехники", "Audit Logging": "Ведение журнала аудита", "Audit log": "<PERSON><PERSON><PERSON><PERSON><PERSON> аудита", "August": "Август", "Authentication providers": "Провайдеры аутентификации", "Author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Auto-add tags AI": "Автодобавление тегов AI", "Autocrane operator": "Оператор автокрана", "Autoelectrician": "Автоэлектрик", "Automate repetitive tasks and communications to save time and improve the candidate experience.": "Автоматизируйте повторяющиеся задачи и коммуникации, чтобы сэкономить время и улучшить работу с кандидатами.", "Automated User Provisioning (SCIM)": "Автоматизированное предоставление пользователей (SCIM)", "Automatic": "Автоматический", "Automatic - after candidate has submitted a form": "", "Automatic - after candidate has submitted referees": "Автоматически - после того, как кандидат представит рекомендателей", "Automatic - after candidate has submitted references": "Автоматически - после предоставления кандидатом рекомендаций", "Automatic - after project status change": "", "Automatic - after receiving video interview response": "Автоматически - после получения ответа на видеоинтервью", "Automatic - after time in stage": "Автоматически - по истечении времени на этапе", "Automatic - inherit from landing": "Автоматические - наследуются от посадочных", "Automatic - specific time in future": "Автоматически - определенное время в будущем", "Automatic actions": "Автоматические действия", "Automatic actions scheduled on a specific date are not cloned.": "Автоматические действия, запланированные на определенную дату, не клонируются.", "Automatic heading sizing": "Автоматическое определение размера заголовка", "Automatic summaries": "Автоматические сводки", "Automatically archive landing pages after application deadline": "", "Automatically archives landing pages after their application deadlines have passed. Applies retroactively to old landing pages.": "", "Automation Machinery Manufacturing": "Автоматизация производства оборудования", "Automations": "Автоматизация", "Automechanic": "Автомеханик", "Automotive/Aerospace": "Автомобильная/космическая промышленность", "Autotinman": "Autotinman", "Autowasherman": "Autowasherman", "Available days": "Доступные дни", "Available merge tags": "Доступные теги слияния", "Available period": "Доступный период", "Available time end": "Доступное время конец", "Available time start": "Доступное время начала", "Avaric": "Ав<PERSON><PERSON><PERSON><PERSON>", "Average": "Среднее", "Average rating": "", "Average time from candidate submission to hire based on people hired in the last :days days.": "Среднее время от подачи кандидата до приема на работу на основе данных о людях, принятых на работу за последние :days дней.", "Average time from project start to end for projects finished in the last :days days.": "Среднее время от начала проекта до его завершения для проектов, завершенных за последние :days дней.", "Average time in stage": "Среднее время на этапе", "Avestan": "Авестийский", "Avg. candidates": "Среднее число кандидатов", "Aviation and Aerospace Component Manufacturing": "Производство авиационных и аэрокосмических компонентов", "Aviation specialist": "Специалист по авиации", "Avoid corporate clichés": "Избегайте корпоративных клише", "Aymara": "Аймара", "Azerbaijani": "Азерб<PERSON>йджан", "Bachelor's degree": "Степень бакалавра", "Back": "Назад", "Back to Requisitions": "Назад к заявкам", "Back to Talent Pool": "Назад к кадровому резерву", "Back to candidate profile": "Вернуться к профилю кандидата", "Back to client details": "Вернуться к деталям клиента", "Back to clients list": "Вернуться к списку клиентов", "Back to project": "Назад к проекту", "Back to projects list": "Вернуться к списку проектов", "Back to stage": "Назад к этапу", "Background": "Фон", "Background color": "Цвет фона", "Background image": "Фоновое изображение", "Bad application conversion": "Плохое преобразование приложений", "Bad click conversion": "Плохая конверсия кликов", "Baked Goods Manufacturing": "Производство хлебобулочных изделий", "Baker": "<PERSON><PERSON>йк<PERSON>р", "Baltics": "Прибалтика", "Bambara": "Бамбара", "Banking": "Банковское дело", "Banking / Insurance": "Банковское дело / Страхование", "Barman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Bars, Taverns, and Nightclubs": "Бары, таверны и ночные клубы", "Bartender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Bashkir": "Башкирия", "Basic": "Основные", "Basic education": "Базовое образование", "Basic information": "Основная информация", "Basics": "Основы", "Basque": "Баски", "Bathhouse attendant": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Be sure to include their personal invite link:": "Обязательно укажите ссылку на их персональное приглашение:", "Bed-and-Breakfasts, Hostels, Homestays": "Кровать и завтрак, хостелы, проживание в семье", "Before presenting, please associate the candidate with a project.": "Перед презентацией, пожалуйста, свяжите кандидата с проектом.", "Before proceeding, please check your email for a verification link.": "Прежде чем приступить к работе, проверьте свою электронную почту на наличие проверочной ссылки.", "Before scheduling an interview, please associate the candidate with a project.": "Прежде чем назначить собеседование, пожалуйста, свяжите кандидата с проектом.", "Belarusian": "Беларусь", "Below you can set custom categories for your stages. This will help you get statistics that are better aligned with your hiring process.": "Ниже вы можете задать пользовательские категории для ваших этапов. Это поможет вам получить статистику, которая будет лучше соответствовать вашему процессу найма.", "Benefits (CVO)": "Льготы (CVO)", "Bengali": "Бенгалия", "Better luck next time!": "Удачи в следующий раз!", "Beverage Manufacturing": "Производство напитков", "Bihari languages": "Бихарские языки", "Billing address": "Адрес для выставления счетов", "Biomass Electric Power Generation": "Производство электроэнергии из биомассы", "Biotechnology Research": "Биотехнологические исследования", "Bislama": "Бислама", "Blacksmith": "Кузнец", "Block background": "Фон блока", "Blockchain Services": "Услуги блокчейна", "Blogs": "Блоги", "Blue collar, workers": "Синие воротнички, рабочие", "Board": "Правление", "Body": "Тело", "Body text": "Основной текст", "Boilers, Tanks, and Shipping Container Manufacturing": "Производство котлов, резервуаров и морских контейнеров", "Bold": "Смелый", "Book Publishing": "Книжное издательство", "Book a 20 min demo": "Закажите 20-минутную демонстрацию", "Book and Periodical Publishing": "Книжные и периодические издания", "Bosnian": "Босния", "Bounced at": "Отскочил в", "Brand": "<PERSON>р<PERSON><PERSON>д", "Breadborder": "Хлебная рамка", "Breeder": "Заводчик", "Breton": "Бретон", "Breweries": "Пивоварни", "Bring your own SMTP": "Принесите свой собственный SMTP", "Broadcast Media Production and Distribution": "Производство и распространение вещательных СМИ", "Broker": "Брокер", "Browse": "Просмотреть", "Browse...": "Просмотреть...", "Buffer time after interview": "Буферное время после собеседования", "Buffer time before interview": "Буферное время перед интервью", "Builder": "Строитель", "Building Construction": "Строительство зданий", "Building Equipment Contractors": "Подрядчики строительного оборудования", "Building Finishing Contractors": "Подрядчики по отделке зданий", "Building Structure and Exterior Contractors": "Строительные конструкции и наружные работы", "Bulgarian": "Болгария", "Bulldozer driver": "Ма<PERSON><PERSON><PERSON><PERSON><PERSON>т бульдозера", "Bullets": "Пули", "Burmese": "Бирманский", "Business Consulting and Services": "Бизнес-консалтинг и услуги", "Business Content": "Бизнес-контент", "Business Development": "Развитие бизнеса", "Business Intelligence Platforms": "Платформы для бизнес-аналитики", "Business Skills Training": "Обучение бизнес-навыкам", "Butcher": "Мясник", "Button color": "Цвет кнопки", "Button in stage": "Кнопка на этапе", "Buttons": "Кнопки", "Buying": "Покупка", "Buying/supply": "Покупка/поставка", "By Project Manager": "Руководитель проекта", "By choice": "По выбору", "By default, candidate profiles are pseudonymized using a one-way cryptographic function.  When they re-apply then their history (comments, projects) is restored. If you enable permanent deletion then candidate histories will not be restored.": "По умолчанию профили кандидатов псевдонимизируются с помощью односторонней криптографической функции.  При повторном обращении кандидата его история (комментарии, проекты) восстанавливается. Если вы включите функцию постоянного удаления, то история кандидатов не будет восстановлена.", "By stage": "По этапу", "By time": "По времени", "Byte": "<PERSON>а<PERSON><PERSON>", "Bytes": "<PERSON>а<PERSON><PERSON>ы", "CANCELLED": "ОТМЕНЕН", "CTA button text": "Текст CTA-кнопки", "CTA button url": "url кнопки CTA", "CV": "АВТОБИОГРАФИЯ", "Cable and Satellite Programming": "Кабельное и спутниковое программирование", "Calendar": "Календарь", "Calendar invite details": "Подробности приглашения в календарь", "Calendar invites FROM email address": "Приглашения календаря ОТ адреса электронной почты", "Calendar navigation": "Навигация по календарю", "Calendar time": "Календарное время", "Call": "Звоните на", "Call (no answer)": "Звонок (нет ответа)", "Call time": "Время звонка", "Campaign is currently running": "В настоящее время кампания продолжается", "Can administer teams": "Может управлять командами", "Can be left empty": "Можно оставить пустым", "Can see all projects?": "Можно ли увидеть все проекты?", "Can we contact you about vacancies at [organization_name]?": "Можем ли мы связаться с вами по поводу вакансий в [название_организации]?", "Cancel": "Отмена", "Cancel interview": "Отменить интервью", "Cancel sending": "Отменить отправку", "Cancelled": "Отменено", "Candidate": "Кандидат", "Candidate A": "Кандидат А", "Candidate B": "Кандидат B", "Candidate CV": "Автобиография кандидата", "Candidate CV summaries, similarity search, writing assistance, and more.": "Резюме кандидатов, поиск сходства, помощь в написании резюме и многое другое.", "Candidate GDPR consent validity": "Действительность согласия кандидата на участие в GDPR", "Candidate ID": "", "Candidate ID (Teamdash)": "", "Candidate Net Promoter Score based on surveys in the last :days days.": "Оценка кандидатов Net Promoter Score, основанная на опросах, проведенных за последние :days дней.", "Candidate added to project": "Кандидат добавлен в проект", "Candidate answered": "Кандидат ответил", "Candidate can't be analyzed as they have no CV.": "", "Candidate card settings": "Настройки карты кандидата", "Candidate created date": "Дата создания кандидата", "Candidate custom fields": "Пользовательские поля кандидата", "Candidate email": "Электронная почта кандидата", "Candidate email (Teamdash)": "", "Candidate form": "", "Candidate forms": "", "Candidate location": "Местонахождение кандидата", "Candidate management": "Управление кандидатами", "Candidate message": "", "Candidate motivation, likes and dislikes": "Мотивация кандидата, его симпатии и антипатии", "Candidate name": "Имя кандидата", "Candidate name (Teamdash)": "", "Candidate phone": "Телефон кандидата", "Candidate photo": "Фотография кандидата", "Candidate presentations": "", "Candidate profile target field": "Целевое поле профиля кандидата", "Candidate progression": "Продвижение кандидатов", "Candidate reach": "", "Candidate reasons": "Кандидатские причины", "Candidate reference ID (Teamdash)": "", "Candidate reference email (Teamdash)": "", "Candidate reference name (Teamdash)": "", "Candidate saved!": "Кандидат спасен!", "Candidate scorecard saved.": "Сохранение оценочного листа кандидата.", "Candidate submission sources": "Источники представления кандидатов", "Candidate survey question": "Вопрос для опроса кандидатов", "Candidates": "Кандидаты", "Candidates added to the project after": "Кандидаты, добавленные в проект после", "Candidates anonymized!": "Кандидаты анонимизированы!", "Candidates can only choose times when all the recruiters are free.": "Кандидаты могут выбирать только то время, когда все рекрутеры свободны.", "Candidates can see this on social media sharing previews.": "Кандидаты могут увидеть это на предварительных просмотрах в социальных сетях.", "Candidates deleted!": "Кандидаты удалены!", "Candidates hate long commutes. If you're hiring for on-site positions, you can make better hiring decisions with location data.": "Кандидаты ненавидят долгие поездки на работу. Если вы нанимаете сотрудников на работу на месте, вы можете принимать более правильные решения о найме, используя данные о местоположении.", "Candidates reached interview stage": "Кандидаты прошли этап собеседования", "Candidates shared!": "Кандидаты поделились!", "Candidates target stage": "Целевой этап кандидатов", "Candidates to add": "Кандидаты для добавления", "Candidates to share": "Кандидаты на участие", "Candidates were not copied.": "Кандидаты не были скопированы.", "Candidates who gave a 9 or 10 are called promoters, 7 or 8 are passives and 0-6 are detractors.": "Кандида<PERSON><PERSON>, получившие 9 или 10 баллов, называются промоутерами, 7 или 8 - пассивами, а 0-6 - отщепенцами.", "Candidates who reached {stage} stage": "", "Candidates with ignored/declined consent renewals": "Кандидаты, проигнорировавшие/отказавшиеся от продления согласия", "Candidates with pending consent renewals": "Кандидаты, у которых еще не продлено согласие", "Capital Markets": "Рынки капитала", "Captain": "Капитан", "Car mechanician": "Автомеханик", "Car painter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Car service master": "Мастер автосервиса", "Career Pages": "Страницы карьеры", "Career experience": "Опыт работы", "Career grid": "Карьерная сетка", "Career list": "Список профессий", "Careers": "Карьера", "Carpenter": "Плотник", "Cashier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Catalan, Valencian": "Каталонский, валенсийский", "Categories (CVK/CVM)": "Категории (CVK/CVM)", "Categories (CVO)": "Категории (CVO)", "Category": "Категория", "Category (CVbankas)": "Категория (CVbankas)", "Category (SS.lv)": "Категория (SS.lv)", "Caterers": "Кей<PERSON>е<PERSON><PERSON><PERSON>г", "Catering": "Кей<PERSON>е<PERSON><PERSON><PERSON>г", "Central Europe": "Центральная Европа", "Central Khmer": "Центральный кхмер", "Chamorro": "Чаморро", "Change my score": "Измените мой счет", "Change response": "", "Change video": "Изменить видео", "Changes made here will not affect existing projects created from this template.": "Изменения, внесенные здесь, не повлияют на существующие проекты, созданные на основе этого шаблона.", "Channels": "Каналы", "Charity": "Благотворительность", "Chart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chechen": "Чечня", "Check for applicants to assess": "Проверка кандидатов для оценки", "Check out who rejected it and if they left a comment:": "Проверьте, кто отклонил его и оставили ли они комментарий:", "Check references": "Проверьте рекомендации", "Checkbox": "Флажок", "Checkbox numeric": "Флажок числовой", "Chemical Manufacturing": "Химическое производство", "Chemical Raw Materials Manufacturing": "Производство химического сырья", "Chichewa, Chewa, Nyanja": "Чичева, Чева, Ньянджа", "Chief - cook": "Шеф - повар", "Child Day Care Services": "Услуги по уходу за детьми", "Chinese": "Китайский", "Chiropractors": "Хиропрактики", "Choices": "Выбор", "Choose": "Выберите", "Choose Indeed Apply as the credential type and click Save.": "Выберите Indeed Apply в качестве типа учетных данных и нажмите Save.", "Choose client": "Выберите клиента", "Choose company settings": "Выберите настройки компании", "Choose image": "Выберите изображение", "Choose interview": "Выберите интервью", "Choose manager": "Выберите менеджера", "Choose or type source": "Выберите или введите источник", "Choose project": "Выберите проект", "Choose project manager": "Выберите руководителя проекта", "Choose scorecard": "Выберите оценочный лист", "Choose statuses": "", "Choose tag": "Выберите метку", "Choose template": "Выберите шаблон", "Choose this value, the other will be removed": "", "Choose user to reassign these items to:": "", "Church Slavonic, Old Bulgarian, Old Church Slavonic": "Церковнославянский, староболгарский, староцерковнославянский", "Chuvash": "Чувашия", "Circuses and Magic Shows": "Цирки и магические шоу", "Cities (cv.lt)": "Города (cv.lt)", "City": "Город", "City (CVK/CVM)": "Город (CVK/CVM)", "City (CVbankas)": "Город (CVbankas)", "City or county (SS.lv)": "Город или округ (SS.lv)", "Civic and Social Organizations": "Гражданские и общественные организации", "Civil Engineering": "Гражданское строительство", "Claims Adjusting, Actuarial Services": "Урегулирование претензий, актуарные услуги", "Classification (profession.hu)": "Классификация (profession.hu)", "Clay and Refractory Products Manufacturing": "Производство глины и огнеупорных изделий", "Cleaner": "Очиститель", "Clear": "Очистить", "Clear all": "Очистить все", "Clear all stage categories": "Очистите все категории этапа", "Clear filters": "Очистить фильтры", "Clear search": "Четкий поиск", "Clear selection": "Очистить выбор", "Clerk": "Клерк", "Click here to read more about cNPS": "Нажмите здесь, чтобы узнать больше о cNPS", "Click on team or edge and press backspace/delete to delete": "Нажмите на команду или край и нажмите backspace/delete, чтобы удалить.", "Click on the \"Register a new application\" button.": "Нажмите на кнопку \"Зарегистрировать новое приложение\".", "Click on the option that should remain after the merge.": "", "Click save to apply your changes": "Нажмите кнопку Сохранить, чтобы применить изменения", "Click template to preview": "Нажмите на шаблон для предварительного просмотра", "Click to copy": "", "Click to insert merge tag.": "Нажмите, чтобы вставить тег слияния.", "Click to reveal the value": "", "Click to see alternatives.": "Нажмите, чтобы увидеть альтернативы.", "Click to see the changes.": "", "Click to select a time for your interview. You can drag the slot around or remove it and reselect, if you wish to change the time.": "Нажмите, чтобы выбрать время для собеседования. Вы можете перетащить слот или удалить его и выбрать заново, если хотите изменить время.", "Clicked at": "Нажал на", "Clicks": "Клики", "Client": "Кли<PERSON><PERSON>т", "Client ID": "Идентификатор клиента", "Client Secret": "Секрет клиента", "Client name": "Имя клиента", "Client secret": "Секрет клиента", "Clients": "Клиенты", "Climber": "Climber", "Clone": "Клон", "Clone project": "Клонированный проект", "Clone requisition": "Заявка на клон", "Clone scorecard": "Клон оценочного листа", "Close": "Закрыть", "Close any other programs which might be using the webcam.": "Закройте все другие программы, которые могут использовать веб-камеру.", "Coal Mining": "Добыча угля", "Code": "<PERSON>од", "Collaborate with hiring managers and assess candidates in a fair and equitable way.": "Сотрудничать с менеджерами по найму и оценивать кандидатов на справедливой и равной основе.", "Collection Agencies": "Коллекторские агентства", "Collector": "Коллектор", "Colour": "Цвет", "Colour:": "Цвет:", "Columns": "Колонны", "Coming soon": "Скоро будет", "Comma separated IP addresses or CIDR ranges.": "IP-адреса или диапазоны CIDR, разделенные запятыми.", "Comment": "Комментарий", "Comment Attachment": "Комментарий Вложение", "Comment by {name}": "<PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {name}", "Comment saved!": "Комментарий сохранен!", "Comments": "Комментарии", "Commercial and Industrial Equipment Rental": "Аренда коммерческого и промышленного оборудования", "Commercial and Industrial Machinery Maintenance": "Обслуживание коммерческого и промышленного оборудования", "Commercial and Service Industry Machinery Manufacturing": "Торговля и сфера услуг Машиностроение", "Commodity researcher": "Исследователь товаров", "Communications Equipment Manufacturing": "Производство оборудования связи", "Community Development and Urban Planning": "Общественное развитие и городское планирование", "Community Services": "Общественные службы", "Company mail signature settings": "Настройки подписи почтовых сообщений компании", "Company name": "Название компании", "Company names": "Названия компаний", "Company network gateway IP": "IP-адрес сетевого шлюза компании", "Company reasons": "Причины компании", "Compensation (salary + bonus):": "Компенсация (оклад + бонус):", "Complained at": "Жаловался на", "Computer": "Компьютер", "Computer Games": "Компьютерные игры", "Computer Hardware Manufacturing": "Производство компьютерного оборудования", "Computer Networking Products": "Продукты для компьютерных сетей", "Computer and Network Security": "Компьютерная и сетевая безопасность", "Computer technician": "Компьютерный техник", "Computers and Electronics Manufacturing": "Производство компьютеров и электроники", "Concreter": "Бетонщик", "Condition": "Состояние", "Confectioner": "Кондитер", "Configure personally identifiable information": "", "Confirm": "Подтвердите", "Confirmation SMS": "", "Consent Info": "Информация о согласии", "Consent renewal": "Продление согласия", "Consent renewal message": "Сообщение о продлении согласия", "Consent renewal message body": "Тело сообщения о продлении согласия", "Consent renewal message subject": "Тема сообщения о продлении согласия", "Consent renewals sent!": "Продление согласия отправлено!", "Consent subtype": "", "Consent subtypes": "", "Consent type": "Тип согласия", "Consent types": "", "Consent valid until": "Согласие действительно до", "Consent valid until end of": "Согласие действует до конца", "Conservation Programs": "Программы по охране природы", "Consider adding categories to all stages.": "Рассмотрите возможность добавления категорий на всех этапах.", "Construction": "Строительство", "Construction / Real-estate": "Строительство / Недвижимость", "Construction Hardware Manufacturing": "Производство строительной фурнитуры", "Construction foreman": "Строительный мастер", "Constructor": "Конструктор", "Consultant": "Кон<PERSON>уль<PERSON><PERSON><PERSON>т", "Consulting": "Кон<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Consumer Goods Rental": "Аренда потребительских товаров", "Consumer Services": "Потребительские услуги", "Contact email": "Контактный адрес электронной почты", "Contact her at {email} to get help with your first job ad.": "Свяжитесь с ней по адресу {email}, чтобы получить помощь в составлении вашего первого объявления о работе.", "Contact info": "Контактная информация", "Contact name": "Контактное имя", "Contact phone": "Контактный телефон", "Contact support to enable AI transcriptions and summaries.": "Обратитесь в службу поддержки, чтобы включить транскрипцию и резюме искусственного интеллекта.", "Contact support to enable AI-powered features": "Обратитесь в службу поддержки, чтобы включить функции, основанные на искусственном интеллекте", "Contact support to enable AI-powered features.": "Обратитесь в службу поддержки, чтобы включить функции, основанные на искусственном интеллекте.", "Contact support to enable SCIM for your account.": "Обратитесь в службу поддержки, чтобы включить SCIM для вашей учетной записи.", "Contact support to enable reference checks.": "Свяжитесь со службой поддержки, чтобы включить проверку рекомендаций.", "Contact support to enable transcripts for your account.": "Обратитесь в службу поддержки, чтобы включить транскрипты для вашей учетной записи.", "Contact support to enable video interviews.": "Обратитесь в службу поддержки, чтобы разрешить проведение видеоинтервью.", "Contact support to enable webhook actions.": "", "Contacts": "Контакты", "Continuous project (always accepting candidates)": "Постоянный проект (всегда принимаем кандидатов)", "Contract": "Контракт", "Contract type": "Ти<PERSON> контракта", "Contractor": "Подрядчик", "Control who can see and edit what. Create teams and assign them to projects, job ads, and candidates.": "Контролируйте, кто и что может видеть и редактировать. Создавайте команды и назначайте их на проекты, объявления о работе и кандидатов.", "Controller": "Контроллер", "Controls": "Контроли<PERSON><PERSON><PERSON>т", "Conversion rate": "Коэффициент конверсии", "Convert to template": "", "Convince the candidate to respond": "Убедите кандидата ответить", "Cook": "<PERSON><PERSON><PERSON><PERSON>", "Copied 1 candidate to selected project. | Copied {count} candidates to selected project.": "Скопирован 1 кандидат в выбранный проект. | Скопировано {count} кандидатов в выбранный проект.", "Copied to clipboard.": "", "Copied {successCount} out of {allCount} candidates. The rest were already in the chosen project.": "Скопировано {successCount} из {allCount} кандидатов. Остальные уже были в выбранном проекте.", "Copied!": "Скопировано!", "Copy and paste the generated credentials into the fields below.": "Скопируйте и вставьте сгенерированные учетные данные в поля ниже.", "Copy email address": "", "Copy feed URL": "Скопируйте URL-адрес фида", "Copy feed slug:": "Копировальная прорезь для подачи:", "Copy landing URL": "Скопируйте URL-адрес посадочной страницы", "Copy the number to field above": "Скопируйте номер в поле выше", "Copy to another project": "Копирование в другой проект", "Copy to project": "Копирование в проект", "Copywriter": "<PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cornish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Corporation": "Корпорация", "Correctional Institutions": "Исправительные учреждения", "Corrector": "Корректор", "Corsican": "Корсиканцы", "Cosmetician": "Косметолог", "Cosmetology and Barber Schools": "Школы косметологии и парикмахерского искусства", "Cost": "Стоимость", "Cost per applicant": "Стоимость на одного заявителя", "Cost per submission": "Стоимость одного представления", "Could not delete stage! There might audit log events or other data associated with this stage.": "Не удалось удалить этап! С этим этапом могут быть связаны события журнала аудита или другие данные.", "Could not remove stage": "Не удалось удалить этап", "Count candidates": "Подсчет кандидатов", "Countdown": "Обратный отсчет", "Countdown to application deadline": "Обратный отсчет времени до окончания приема заявок", "Country": "Страна", "Country (CVK/CVM)": "Страна (CVK/CVM)", "County (CVK/CVM)": "Округ (CVK/CVM)", "Courier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Courts of Law": "Суды", "Crane operator": "Ма<PERSON><PERSON><PERSON><PERSON><PERSON>т крана", "Create": "Создать", "Create API key": "Создайте ключ API", "Create a feed integration {here}, reload the editor and retry.": "Создайте интеграцию с фидом {here}, перезагрузите редактор и повторите попытку.", "Create a new form linked to this project": "Создайте новую форму, связанную с этим проектом", "Create a new job requisition": "Создайте новую заявку на работу", "Create a new landing page": "Создайте новую целевую страницу", "Create a new password": "Создайте новый пароль", "Create a new project": "Создайте новый проект", "Create a new project template": "Создайте новый шаблон проекта", "Create a new template": "Создайте новый шаблон", "Create a project": "Создать проект", "Create a reference form": "Создайте форму рекомендации", "Create a task": "Создать задачу", "Create a video interview": "Создайте видеоинтервью", "Create an automatic form and include it on a landing page linked to this project": "Создайте автоматическую форму и разместите ее на целевой странице, связанной с этим проектом", "Create an event": "Создать событие", "Create and use form": "Создание и использование формы", "Create application forms or forms to ask candidates for additional details or feedback.": "", "Create application forms or forms to ask candidates for additional details.": "", "Create call": "Создать вызов", "Create consent type": "", "Create custom activity": "Создание пользовательской активности", "Create downloadable image ad": "Создайте объявление с загружаемым изображением", "Create file type": "", "Create form": "Создать форму", "Create forms to ask internal feedback or details from other project members.": "", "Create forms to gather applicants.": "", "Create interview": "Создайте интервью", "Create job ad": "Создать объявление о работе", "Create key": "Создать ключ", "Create landing page": "Создайте целевую страницу", "Create legacy page": "Создайте страницу наследия", "Create location": "Создать местоположение", "Create new": "Создать новый", "Create new project from template": "Создание нового проекта из шаблона", "Create new video interview": "Создайте новое видеоинтервью", "Create on-brand customisable career sites. Recruiters can publish all jobs with a single click.": "Создавайте индивидуальные сайты для карьеры. Рекрутеры могут публиковать все вакансии одним щелчком мыши.", "Create password": "Создать пароль", "Create project": "Создать проект", "Create reference form": "Создайте форму рекомендации", "Create requisition": "Создание заявки", "Create requisition & ask approvals": "Создавайте заявки и запрашивайте разрешения", "Create scorecard": "Создайте оценочную карту", "Create tag": "Создать метку", "Create task": "", "Create tasks": "Создание задач", "Create template": "Создать шаблон", "Create template from project": "", "Create video call": "Создание видеозвонка", "Create your beautiful job ad": "Создайте свое красивое объявление о работе", "Created": "Создано", "Created at": "Создано в", "Created on": "", "Created task": "Созданное задание", "Created!": "Создано!", "Creating a call will send your users and candidates the video call link.": "Создав звонок, вы отправите пользователям и кандидатам ссылку на видеозвонок.", "Creative": "Creative", "Creative type": "Творческий тип", "Creatives": "Креативы", "Credit Intermediation": "Кредитное посредничество", "Cree": "<PERSON><PERSON>", "Croatian": "Хорватия", "Crop": "Растениеводство", "Crop & save": "Обрезать и сохранить", "Croupier": "Крупье", "Culture / Entertainment": "Культура / Развлечения", "Current month": "Текущий месяц", "Current value:": "Текущее значение:", "Cusotm fonts": "", "Custom CSS": "Пользовательский CSS", "Custom Domain": "Пользовательский домен", "Custom URL": "Пользовательский URL", "Custom activities": "Нестандартные действия", "Custom activity key": "Пользовательский ключ активности", "Custom categories must be in the same order as system categories": "Пользовательские категории должны располагаться в том же порядке, что и системные категории", "Custom description": "Пользовательское описание", "Custom field filters": "Пользовательские фильтры полей", "Custom field:": "Пользовательское поле:", "Custom fields": "Пользовательские поля", "Custom fonts": "", "Custom fonts can be used in landings and forms. Click the button below to get started.": "", "Custom option value": "Пользовательское значение опции", "Custom range": "Пользовательский диапазон", "Custom stage categories": "Пользовательские категории этапов", "Customer Service": "Обслуживание клиентов", "Customer Services": "Обслуживание клиентов", "Customer service/Services": "Обслуживание клиентов/Услуги", "Customize": "Настроить", "Cutlery and Handtool Manufacturing": "Производство столовых приборов и ручных инструментов", "Czech": "Чехия", "DEI: Inclusive language check": "DEI: Инклюзивная проверка языка", "Daily": "Ежедневно", "Daily budget (€)": "Ежедневный бюджет (€)", "Dairy Product Manufacturing": "Производство молочных продуктов", "Dance Companies": "Танцевальные компании", "Dancer": "Танц<PERSON>р", "Danish": "Датский", "Darker": "Темнее", "Data Infrastructure and Analytics": "Инфраструктура данных и аналитика", "Data Processing Agreement": "Соглашение об обработке данных", "Data Security Software Products": "Программные продукты для защиты данных", "Data from public sources": "Данные из открытых источников", "Data processing consent automation": "Автоматизация согласия на обработку данных", "Data protection specialist": "Специалист по защите данных", "Date": "", "Datetime": "Дата-тайм", "Day": "День", "Days": "<PERSON><PERSON>и", "Deactivate": "", "Deactivate and reassign": "", "Deactivate user {userName}": "", "Deactivate without reassigning": "", "Deactivating...": "", "Deadline": "Крайний срок", "Deadline not set": "Крайний срок не установлен", "Deadline: :deadline": "Крайний срок: :крайний срок", "Deadline: {date}": "Крайний срок: {date}", "Debug": "Отладка", "December": "Декабрь", "Decision pending": "Решение ожидается", "Declarant": "Де<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Decrease Level": "Снижение уровня", "Default": "По умолчанию", "Default data processing consent duration": "Срок действия согласия на обработку данных по умолчанию", "Default data processing consent request validity duration": "Срок действия запроса согласия на обработку данных по умолчанию", "Default language": "Язык по умолчанию", "Default phone country code": "Код страны телефона по умолчанию", "Default stages": "Этапы по умолчанию", "Default survey": "", "Default survey will use the question defined under Organization settings. If you wish to send a form with more questions, create a new Survey form.": "", "Defense and Space Manufacturing": "Оборонная и космическая промышленность", "Define a new font": "", "Define new font": "", "Delay": "Задержка", "Delay sending": "Задержка отправки", "Delete": "Удалить", "Delete chat": "Удалить чат", "Delete comment": "Удалить комментарий", "Delete file": "Удалить файл", "Delete filter": "Удалить фильтр", "Delete permanently": "Удалить навсегда", "Delete project": "Удалить проект", "Delete stage": "Удалить этап", "Delete template": "Удалить шаблон", "Deleted": "", "Deleting a client cannot be undone.": "", "Deliver via": "", "Delivered at": "Доставлено в", "Dentists": "Стоматологи", "Department": "Депар<PERSON><PERSON><PERSON><PERSON><PERSON>т", "Department Management": "Управление департаментом", "Department:": "Департамент:", "Departments (cv.lt)": "Отделы (cv.lt)", "Depending on your specific circumstances, this can be totally fine or might need some action.": "В зависимости от ваших конкретных обстоятельств это может быть совершенно нормально, а может потребовать определенных действий.", "Description": "Описание", "Description section title": "", "Descriptive page name": "Описательное название страницы", "Design": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Design Services": "Услуги по проектированию", "Design/architecture": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>/архитектура", "Designer": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Desired": "Желаемый", "Desktop Computing Software Products": "Программные продукты для настольных компьютеров", "Desktop View": "Вид рабочего стола", "Director": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Disability Confident employer": "Уверенный в себе работодатель с ограниченными возможностями", "Disable": "Отключить", "Disable activity notifications": "Отключение уведомлений о действиях", "Disabled": "Отключено", "Disciple": "Ученик", "Dishwasher": "Посудомоечная машина", "Dismiss": "Увольте", "Dispatcher": "Диспетчер", "Display location": "Расположение дисплея", "Display name": "Отображаемое имя", "Display type": "Тип дисплея", "Displayed on career page preview.": "Отображается при предварительном просмотре страницы карьеры.", "Displayed when sharing page in social media.": "Отображается при публикации страницы в социальных сетях.", "Dispute & invalid": "Спор и недействительность", "Dispute months": "Месяцы споров", "Dispute resolution only": "Только разрешение споров", "Dispute resolution only.": "Только разрешение споров.", "Distance from {locationName} ({locationAddress})": "Расстояние от {locationName} ({locationAddress})", "Distilleries": "Винокурни", "Distribution": "Распространение", "Divehi, Dhivehi, Maldivian": "Дивехи, дивехи, мальдивцы", "Do a fact-check - find any instances where incorrect facts were presented. Do not highlight correct facts.": "Проведите проверку фактов - найдите все случаи, когда были представлены неверные факты. Не выделяйте правильные факты.", "Do not infer or assume any information that has not been explicitly said in the transcript.": "Не делайте выводов и не предполагайте никакой информации, которая не была прямо указана в стенограмме.", "Do you want to {knowMoreCTA} about your interviews automatically?": "Хотите, чтобы на {knowMoreCTA} ваши интервью проходили автоматически?", "Do you want to {knowMoreCTA} without spending hours on interviewing?": "Хотите получить {knowMoreCTA}, не тратя часы на собеседования?", "Docker": "<PERSON>er", "Doctor": "До<PERSON>т<PERSON>р", "Doctor assistant": "Ассистент врача", "Doctor's degree": "Степень доктора", "Documentation": "Документация", "Domain": "До<PERSON><PERSON>н", "Domain must be a valid domain": "Домен должен быть действительным доменом", "Don't worry if you don't have the time, will or skills to choose photos.": "Не волнуйтесь, если у вас нет времени, желания или навыков для выбора фотографий.", "Don't worry if you don't have the time, will or skills to choose videos.": "Не волнуйтесь, если у вас нет времени, желания или навыков для выбора видео.", "Double-click to edit": "Двойной щелчок для редактирования", "Down for maintenance": "Не работает", "Download": "Скачать", "Download as image": "Скачать как изображение", "Download candidates report": "Скачать отчет о кандидатах", "Download custom activity report": "Скачать пользовательский отчет о деятельности", "Download project status report": "Скачать отчет о состоянии проекта", "Download submissions": "", "Downloadable image": "Загружаемое изображение", "Draft": "Проект", "Draft saved": "Черновик сохранен", "Drag and drop CVs onto the project board": "Перетаскивайте резюме на доску проекта", "Drag and drop to add attachments": "", "Drag whitespace to move graph": "Перетащите пробельные символы, чтобы переместить график", "Dragged from mailbox": "Вытащил из почтового ящика", "Driver": "Др<PERSON><PERSON><PERSON><PERSON><PERSON>", "Driver's license (profession.hu)": "", "Drop files here": "Скиньте файлы сюда", "Dropout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dropout reason added.": "Добавлена причина отсева.", "Dropout reasons": "Причины отсева", "Dropped out": "Выбыл", "Duplicate": "Дубли<PERSON>а<PERSON>", "Duration (min)": "Продолжительность (мин)", "Duration in days": "Продолжительность в днях", "Duration in months": "Продолжительность в месяцах", "Dutch": "Голландия", "Dutch, Flemish": "Голландский, фламандский", "Dzongkha": "Дзонгкха", "E-Learning Providers": "Провайдеры электронного обучения", "E-mail": "Электронная почта", "E-mail address of the Indeed account used to manage this integration": "Адрес электронной почты учетной записи Indeed, используемой для управления этой интеграцией", "E-mail notifications are currently turned off. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn on.": "Уведомления по электронной почте в настоящее время отключены. Электронная почта доставляется по будням в 8:00, 13:00, 16:00. Нажмите здесь, чтобы включить.", "Earliest day to offer?": "Самый ранний день для предложения?", "Eastern Europe": "Восточная Европа", "Economic Programs": "Экономические программы", "Economist": "Экономист", "Edit": "Редактировать", "Edit API key": "Редактировать ключ API", "Edit action": "Редактировать действие", "Edit action {actionName}": "Редактировать действие {actionName}", "Edit actions and imports": "Редактирование действий и импорта", "Edit authentication provider": "Редактирование провайдера аутентификации", "Edit candidate": "Редактировать кандидата", "Edit client": "Редактировать клиента", "Edit consent type": "", "Edit contact": "Редактировать контакт", "Edit custom activity": "Редактирование пользовательской активности", "Edit dropout reason": "Редактировать причину отсева", "Edit file type": "", "Edit form": "Редактирование формы", "Edit import": "Редактирование импорта", "Edit integration": "Интеграция редактирования", "Edit interview": "Редактировать интервью", "Edit job ad": "Редактировать объявление о работе", "Edit location": "Редактировать местоположение", "Edit office": "Редактировать офис", "Edit project": "Редактировать проект", "Edit project scorecards": "Редактирование оценочных листов проекта", "Edit reference form": "Редактировать форму рекомендации", "Edit requisition": "Редактирование заявки", "Edit scorecard": "Редактирование оценочного листа", "Edit stage :stage in :project": "Редактировать этап :stage в :project", "Edit structured job ad": "Редактирование структурированного объявления о работе", "Edit tag": "Редактировать тег", "Edit teams hierarchy": "Редактирование иерархии команд", "Edit template": "Редактировать шаблон", "Edit user": "Редактировать пользователя", "Edit video interview": "Редактирование видеоинтервью", "Editable text with custom background image": "Редактируемый текст с пользовательским фоновым изображением", "Editable text with responsive image on one side. Configurable orientation.": "Редактируемый текст с отзывчивым изображением на одной стороне. Настраиваемая ориентация.", "Editable text with responsive video on one side. Configurable orientation.": "Редактируемый текст с отзывчивым видео на одной стороне. Настраиваемая ориентация.", "Editable text with square image on one side. Configurable orientation.": "Редактируемый текст с квадратным изображением на одной стороне. Настраиваемая ориентация.", "Editing uploaded candidate {candidateIndex} of {candidateTotal}": "Редактирование загруженных кандидатов {candidateIndex} из {candidateTotal}", "Editor": "Редактор", "Education": "Образование", "Education (SS.lv)": "Образование (SS.lv)", "Education / Science": "Образование / Наука", "Education Administration Programs": "Программы по управлению образованием", "Education history": "История образования", "Education requirement (profession.hu)": "", "Educator": "Педагог", "Either an e-mail or a phone number is required for each referee.": "Для каждого рекомендателя необходимо указать либо электронную почту, либо номер телефона.", "Electric Lighting Equipment Manufacturing": "Производство электроосветительного оборудования", "Electric Power Generation": "Производство электроэнергии", "Electric Power Transmission, Control, and Distribution": "Передача, управление и распределение электроэнергии", "Electric welder": "Электросварщик", "Electrical Equipment Manufacturing": "Производство электрооборудования", "Electrical/Telecoms": "Электротехника/Телекоммуникации", "Electrician": "Электрик", "Electromechanics": "Электромеханика", "Electronic and Precision Equipment Maintenance": "Обслуживание электронного и прецизионного оборудования", "Electronics": "Электроника", "Electronics / Telecom": "Электроника / Телекоммуникации", "Email": "Электронная почта", "Email Message": "Сообщение электронной почты", "Email address is required in integration settings": "", "Email message conversions": "Конверсия сообщений электронной почты", "Email service provider": "Поставщик услуг электронной почты", "Emails": "Электронные письма", "Embedded Software Products": "Продукты для встраиваемого программного обеспечения", "Emergency and Relief Services": "Службы экстренной и неотложной помощи", "Emojis not found.": "Эмодзи не найдены.", "Employee forms": "", "Employer": "Работодатель", "Employer ID": "Идентификатор работодателя", "Employer brand video": "Видеоролик о бренде работодателя", "Employer brand video URL": "URL-адрес видеоролика бренда работодателя", "Employer info": "Информация о работодателе", "Employer web URL": "URL-адрес сайта работодателя", "Employment - employer name": "Трудоустройство - имя работодателя", "Employment - job title": "Занятость - название должности", "Employment history": "Трудовая книжка", "Employments": "Занятость", "Empty": "Пуст<PERSON>й", "Enable \"Send as user\" for messages": "Включите функцию \"Отправить как пользователь\" для сообщений", "Enable GDPR automation": "Обеспечьте автоматизацию GDPR", "Enable click tracking": "Включите отслеживание кликов", "Enable diversity reporting": "Обеспечение отчетности по многообразию", "Enable integration": "Включить интеграцию", "Enable permanent delete": "Разрешить постоянное удаление", "Enable this if you are sending non-critical content.": "Включите эту опцию, если вы отправляете некритичное содержимое.", "Enable this if you have more than 100 rooms. Will add Place.Read.All permission.": "Включите это, если у вас более 100 комнат. Это добавит разрешение Place.Read.All.", "Enable this if you want AI-powered transcriptions and summaries from meetings. Requires permissions for your events and recordings.": "Включите эту функцию, если вам нужны расшифровки и резюме собраний с помощью искусственного интеллекта. Требуются разрешения для ваших событий и записей.", "Enable this if you want AI-powered transcriptions and summaries from meetings. This will cause meeting created through Teamdash to automatically start recording. Requires permissions for your events and recordings.": "Включите эту опцию, если вы хотите получать расшифровки и резюме совещаний с помощью искусственного интеллекта. Это приведет к тому, что встречи, созданные через Teamdash, автоматически начнут записываться. Требуются разрешения для ваших событий и записей.", "Enable {gdprautomation} to never worry about it again.": "На сайте {gdprautomation} вы можете больше никогда не беспокоиться об этом.", "Enabled": "Включено", "Encryption": "Шифрование", "End": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "End date": "Дата окончания", "End month": "Конечный месяц", "End year": "Конечный год", "Energetics / Electricity": "Энергетика / Электричество", "Energetics/electronics": "Энергетика/электроника", "Engineer": "Ин<PERSON><PERSON><PERSON><PERSON><PERSON>", "Engineering": "Инженерия", "Engineering/mechanics": "Инженерия/механика", "Engines and Power Transmission Equipment Manufacturing": "Производство двигателей и оборудования для передачи энергии", "English": "Английский язык", "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to \"Allow\".": "Обеспечьте доступ браузера к веб-камере. Щелкните значок замка в адресной строке и установите для камеры и микрофона значение \"Разрешить\".", "Enter \"Teamdash\" as the application name and add a short description (e.g. \"Teamdash recruitment software\").": "Введите \"Teamdash\" в качестве названия приложения и добавьте краткое описание (например, \"программа для подбора персонала Teamdash\").", "Enter a URL…": "Введите URL...", "Enter a new reason...": "", "Enter position": "Введите позицию", "Enter warranty date if applicable": "Введите дату гарантии, если применимо", "Enterprise": "Предприятие", "Entertainment Providers": "Поставщики развлечений", "Entry level": "Начальный уровень", "Environmental Quality Programs": "Программы качества окружающей среды", "Environmental Services": "Экологические услуги", "Equipment Rental Services": "Услуги по аренде оборудования", "Error": "Ошибка", "Error when scheduling an interview (:interviewTitle)": "Ошибка при назначении собеседования (:interviewTitle)", "Error:": "Ошибка:", "Esperanto": "Эсперанто", "Essential": "Essential", "Estimator": "Сметчик", "Estonia": "Эстония", "Estonian": "Эстония", "Event": "", "Event :title on :start (:tz) has been cancelled.": "Событие :title на :start (:tz) было отменено.", "Event :title scheduled for :start (:tz)": "Событие :title запланировано на :start (:tz)", "Event [event_title] scheduled for [event_start]": "Событие [event_title] запланировано на [event_start]", "Event scheduled": "", "Event updated successfully!": "", "Events Services": "Услуги по организации мероприятий", "Everyone who is currently using this key will no longer be able to access the API. This may break the connection between job portals and your Teamdash instance!": "", "Everything related to project stages can now be edited directly on the candidates board.": "Все, что связано с этапами проекта, теперь можно редактировать прямо на доске кандидатов.", "Ewe": "<PERSON><PERSON>", "Excavator operator": "Машинист экскаватора", "Excellent": "Превосходно", "Exclude": "Исключить", "Exclude continuous projects": "Исключить непрерывные проекты", "Executive": "Исполнительный директор", "Executive Offices": "Исполнительные офисы", "Executive Search Services": "Услуги по поиску руководителей", "Executive level": "Руководящий уровень", "Exit": "Выход", "Expand": "Развернуть", "Expand column": "Развернуть колонку", "Experience (profession.hu)": "<PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Experience level": "Уровень опыта", "Expired": "Просроченный", "Expired at": "Срок действия истек", "Expiring soon": "", "Expiry period": "", "Expiry period unit": "", "Export": "Экспорт", "Export an ad to a job portal": "Экспорт объявления на портал вакансий", "Export candidates": "Кандидаты на экспорт", "Export candidates to .xlsx": "Экспорт кандидатов в .xlsx", "Exports": "Экспорт", "Exports for {positionName}": "Экспорт для {positionName}", "Extract details with AI": "", "Fabricated Metal Products": "Металлические изделия", "Facilities Services": "Услуги по обслуживанию помещений", "Fact-check": "Проверка фактов", "Failed to copy.": "", "Failed to create transcription.": "", "Failed to deactivate user. Please try again later.": "", "Failed to load available rooms.": "", "Failed to parse the candidate's CV.": "", "Failed to resend invite. Please try again later.": "", "Failed to save rating.": "", "Failed to send unpublish notification: :message": "", "Failed:": "Не удалось:", "Fair": "Ярмарка", "Fair evaluations": "Справедливые оценки", "Family Planning Centers": "Центры планирования семьи", "Farming": "Фермерское хозяйство", "Farming, Ranching, Forestry": "Фермерство, ранчо, лесное хозяйство", "Faroese": "Фарерские", "Fashion": "Мода", "Fashion Accessories Manufacturing": "Производство модных аксессуаров", "Favicon is the small icon that appears in the browser tab on your landing pages.": "", "February": "Февраль", "Feed": "<PERSON>о<PERSON><PERSON>", "Feed URL": "URL-адрес канала", "Feedback form": "", "Feel free to tweak filters and double-check you have access to the project.": "Не стесняйтесь настраивать фильтры и проверять, есть ли у вас доступ к проекту.", "Field": "Поле", "Field MUST include [:mustIncludeTags] :mustIncludeCondition.": "Поле ДОЛЖНО включать [:mustIncludeTags] :mustIncludeCondition.", "Field MUST include [:mustIncludeTags].": "Поле ДОЛЖНО включать [:mustIncludeTags].", "Field can be empty": "Поле может быть пустым", "Field label": "Метка поля", "Field must be filled": "Поле должно быть заполнено", "Field must be valid url": "Поле должно быть действительным url", "Field type": "Тип поля", "Fields": "Поля", "Fijian": "Фиджийцы", "File (legacy version)": "Файл (устаревшая версия)", "File Types": "", "File URL": "URL-адрес файла", "File size is over the 10MB limit.": "Размер файла превышает ограничение в 10 МБ.", "File type": "Тип файла", "File type not supported": "Тип файла не поддерживается", "File types": "", "File uploader": "Загрузчик файлов", "Files": "Файлы", "Fill a form": "", "Fill text fields from landing": "Заполнение текстовых полей из посадочной страницы", "Filling this field": "Заполнение этого поля", "Filter set name": "Имя набора фильтров", "Filters": "Фильтры", "Filters saved!": "Фильтры спасены!", "Finance": "Финан<PERSON>ы", "Finance / Accounting": "Финансы / Бухгалтерия", "Finance/accounting/banking": "Финансы/бухгалтерия/банки", "Financial Services": "Финансовые услуги", "Financial analyst": "Финансовый аналитик", "Find candidates": "Найти кандидатов", "Find candidates similar to": "", "Fine Arts Schools": "Школы изобразительного искусства", "Finish uploading candidates": "Завершите загрузку кандидатов", "Finished projects": "Готовые проекты", "Finished projects + time to fill": "Готовые проекты + время на заполнение", "Finished projects + time to hire": "Готовые проекты + время до найма", "Finnish": "Финский", "Fire Protection": "Противопожарная защита", "Fish processor": "Обработка рыбы", "Fisheries": "Рыболовство", "Fisherman": "<PERSON>ыб<PERSON>к", "Fix": "Исправить", "Fix grammar and spelling": "Исправьте грамматику и орфографию", "Fix integration": "Исправить интеграцию", "Fixed background when scrolling": "Исправлен фон при прокрутке", "Fixed term": "Фиксированный срок", "Flight Training": "Летная подготовка", "Florist": "Флорист", "Font": "<PERSON>ри<PERSON><PERSON>", "Font file (bold style)": "", "Font file (combined bold-and-italic style)": "", "Font file (italic style)": "", "Font file (normal style)": "", "Font name": "", "Font:": "Шрифт:", "Food Industry": "Пищевая промышленность", "Food and Beverage Manufacturing": "Производство продуктов питания и напитков", "Food and Beverage Retail": "Розничная торговля продуктами питания и напитками", "Food and Beverage Services": "Услуги в области продуктов питания и напитков", "Footwear Manufacturing": "Производство обуви", "Footwear and Leather Goods Repair": "Ремонт обуви и изделий из кожи", "Footwear/Accessories": "Обувь/аксессуары", "For conformance with the GDPR, please follow these guidelines for the request message. For more information, consult your DPO, Teamdash support or a lawyer.": "Чтобы соответствовать GDPR, следуйте этим рекомендациям по оформлению сообщения-запроса. Для получения дополнительной информации проконсультируйтесь с вашим DPO, службой поддержки Teamdash или юристом.", "For example an email or a screenshot to prove the revocation.": "", "For example an email, screenshot or a contract.": "Например, электронное письмо, снимок экрана или договор.", "Forbidden": "Запрещенный", "Foreman": "Прораб", "Forest / Woodcutting": "Лес / Лесозаготовка", "Forestry and Logging": "Лесное хозяйство и лесозаготовки", "Form": "", "Form :form cannot be directly linked. Please use the [form_url] merge tag to create candidate-specific links to this form.": "Форма :Форма не может быть напрямую связана. Пожалуйста, используйте тег слияния [form_url] для создания ссылок на эту форму для конкретного кандидата.", "Form Submission file": "Файл подачи формы", "Form builder": "Создатель форм", "Form name": "Название формы", "Form preview": "", "Forms": "Формы", "Forwarder": "Форвардер", "Fossil Fuel Electric Power Generation": "Производство электроэнергии на ископаемом топливе", "Found 1 landing.|Found {count} landings.": "Найдена 1 посадка.|Найдено {count} посадок.", "Free slot": "Бесплатный слот", "Freelance": "<PERSON>ри<PERSON><PERSON><PERSON><PERSON>", "Freight and Package Transportation": "Грузовые и пакетные перевозки", "French": "Французский", "Friday": "Пятница", "From": "С сайта", "From URL": "От URL", "From picture bank": "Из банка фотографий", "From received e-mail (continuous project)": "Из полученной электронной почты (непрерывный проект)", "From received e-mail, without long-term consent": "Из полученной электронной почты, без долгосрочного согласия", "From the left menu, choose API key": "В левом меню выберите API-ключ", "Fruit and Vegetable Preserves Manufacturing": "Производство фруктовых и овощных консервов", "Fulah": "<PERSON><PERSON>", "Full name": "Полное имя", "Full time": "Полная занятость", "Full time with shifts": "Полная занятость со сменами", "Full width picture": "Изображение во всю ширину", "Full width video": "Видео во всю ширину", "Fundraising": "Сбор средств", "Funds and Trusts": "Фонды и трасты", "Furniture and Home Furnishings Manufacturing": "Производство мебели и предметов домашней обстановки", "Furniture maker": "Мебельщик", "GB": "GB", "GDPR": "GDPR", "GDPR Preferences": "Предпочтения по GDPR", "GDPR admin": "Администратор GDPR", "GDPR automation": "Автоматизация GDPR", "GDPR automation disabled": "Автоматизация GDPR отключена", "GDPR consent length": "Длительность согласия в соответствии с GDPR", "GDPR helper": "Помощник по GDPR", "Gaelic, Scottish Gaelic": "Гэльский, шотландский гэльский", "Galician": "Галисийский", "Gambling Facilities and Casinos": "Игорные заведения и казино", "Ganda": "Ганда", "Gardener": "Садовник", "Gather references from candidates and automate outreach to previous employers.": "Собирайте рекомендации от кандидатов и автоматизируйте работу с предыдущими работодателями.", "Gather your applicants together in a recruitment project": "Соберите соискателей в рамках проекта по подбору персонала", "General": "Общие сведения", "General Business": "Общий бизнес", "General secondary education": "Общее среднее образование", "General worker": "Генеральный работник", "General/Department Store": "Магазин товаров для дома/департамента", "Generate a new API key": "", "Generate preview": "Создайте предварительный просмотр", "Georgian": "Грузинская", "Geothermal Electric Power Generation": "Геотермальная генерация электроэнергии", "German": "Немецкий язык", "Get a Quote": "Получить предложение", "Get debug report": "Получение отчета об отладке", "Get insights into your recruitment process. See how long it takes to fill a position, where candidates are dropping off, and more.": "Получите подробную информацию о процессе найма. Узнайте, сколько времени требуется для заполнения вакансии, где кандидаты отсеиваются и многое другое.", "Get started": "Начать", "Gikuyu, Kikuyu": "Г<PERSON><PERSON><PERSON><PERSON><PERSON>, Кикуйю", "Give main quotes about the candidate's work experience so far.": "Приведите основные цитаты об опыте работы кандидата на данный момент.", "Given avg. score": "Средний балл", "Glass Product Manufacturing": "Производство изделий из стекла", "Glass, Ceramics and Concrete Manufacturing": "Производство стекла, керамики и бетона", "Glazier": "Стеклопакет", "Go back": "Вернуться", "Go to requisition": "Перейти к заявке", "Go to the Forms menu and create a new Survey form.": "", "Golf Courses and Country Clubs": "Поля для гольфа и загородные клубы", "Good": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Good job!": "Отличная работа!", "Good luck with filling the position!": "Удачи в заполнении вакансии!", "Good luck!": "Удачи!", "Good morning, :name!": "Доброе утро, :name!", "Google Calendar & Meet": "Календарь Google и встречи", "Government Administration": "Государственное управление", "Government Relations Services": "Услуги по связям с государственными органами", "Grade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Graduate": "Выпускник", "Graphic Design": "Графический дизайн", "Greek (Modern)": "Греческий язык (современный)", "Greenlandic, Kalaallisut": "Гр<PERSON><PERSON><PERSON><PERSON>ндский, Калааллисут", "Grid": "Сетка", "Grid of your other job ads": "Сетка ваших других объявлений о работе", "Ground Passenger Transportation": "Наземные пассажирские перевозки", "Group (CVbankas)": "Группа (CVbankas)", "Group by": "Группа по", "Group job ads by": "Группируйте объявления о работе по", "Guarani": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Guide": "Путеводитель", "Gujarati": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "HR & Training": "Управление персоналом и обучение", "HVAC and Refrigeration Equipment Manufacturing": "Производство оборудования для ОВКВ и холодильного оборудования", "Hairdresser": "Пари<PERSON><PERSON><PERSON><PERSON><PERSON>р", "Haitian, Haitian Creole": "Гаитянский, гаитянский креольский", "Handyman": "Разнорабочий", "Happy hiring": "Счастливый найм", "Has file": "", "Has tag": "Имеет метку", "Hausa": "Хауса", "Have your IT-team configure daily backups of all your candidate data. Requires an NDA.": "Поручите своей IT-команде настроить ежедневное резервное копирование всех данных о кандидатах. Требуется NDA.", "Having a sizeable talent database can significantly lower your cost and effort per hire. Meanwhile processing applicants' personal data without their documented consent leaves your company vulnerable to regulatory fines and litigation.": "Наличие обширной базы данных кадрового резерва может значительно снизить ваши затраты и усилия на подбор персонала. В то же время обработка персональных данных кандидатов без их документально подтвержденного согласия делает вашу компанию уязвимой для штрафов и судебных разбирательств.", "Head": "Глава", "Heading": "Направление", "Headings": "Рубрики", "Headline + short text + background": "Заголовок + короткий текст + фон", "Heads up!": "", "Heads up! Before you deactivate this user, review these linked items.": "", "Health / Social care": "Здравоохранение / Социальный уход", "Health and Human Services": "Здравоохранение и социальные службы", "Health/Beauty": "Здоровье/Красота", "HealthCare Provider": "Медицинский работник", "Healthcare": "Здравоохранение", "Hebrew": "<PERSON>в<PERSON><PERSON>т", "Height": "Высота", "Hello!": "Здравствуйте!", "Hello, :name": "Здравствуйте, :name", "Hello, [recipient_full_name]!": "Здравствуйте, [полное_имя_получателя]!", "Hello, [user_name]!": "Привет, [имя_пользователя]!", "Help Center": "Центр помощи", "Here are 5 suggestions to start with:": "Вот 5 предложений, с которых стоит начать:", "Here are the steps you should take:": "Вот шаги, которые вам следует предпринять:", "Herero": "Герер<PERSON>", "Hero with buttons": "Герой с пуговицами", "Hero with form": "Герой с формой", "Heroes": "Герои", "Hi": "Привет", "Hi!": "Привет!", "Hidden": "Скрытый", "Hide": "Скрыть", "Hide archived": "Скрыть заархивированное", "Hide archived landing pages from public": "", "Hide files from limited users": "Скрывайте файлы от ограниченных пользователей", "Hide files from limited users (except anonymized versions)": "", "Hide inactive users": "", "Hide messages from limited users": "Скрывать сообщения от ограниченных пользователей", "Hide navigation-only events": "", "Hide personal data, but show AI-generated anonymized CV and summary": "", "Hide personal data, but show AI-generated anonymized summary": "", "Hide salary": "Скрыть зарплату", "Hide stage categories": "Скрыть категории этапов", "Hide tags from limited users": "Скрывайте теги от ограниченных пользователей", "Hide talent pool": "Скрыть кадровый резерв", "Hide this integration from other users": "Скрыть эту интеграцию от других пользователей", "Hide weekends": "Скрыть выходные дни", "Higher Education": "Высшее образование", "Higher level managers": "Менеджеры высшего звена", "Higher rated first": "Сначала более высокий рейтинг", "Higher scorecard score first": "Сначала наберите большее количество баллов", "Highway, Street, and Bridge Construction": "Строительство автомобильных дорог, улиц и мостов", "Hindi": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hired": "Наемный персонал", "Hires": "", "Hiri Motu": "<PERSON><PERSON><PERSON><PERSON>", "Historical Sites": "Исторические места", "History": "История", "Holding Companies": "Холдинговые компании", "Holy days": "Святые дни", "Home Health Care Services": "Услуги по уходу за больными на дому", "Home/DIY": "Дом / Самоделки", "Horizontal bar with logo and optional CTA button": "Горизонтальная полоса с логотипом и дополнительной кнопкой CTA", "Horticulture": "Садоводство", "Hospitality": "Гостеприимство", "Hospitality/Hotel": "Гостеприимство/гостиница", "Hospitals": "Больницы", "Hospitals and Health Care": "Больницы и здравоохранение", "Host": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>", "Host your job ads, career sites, and scheduler invites on your own domain. Your branding, our tech.": "Размещайте объявления о работе, сайты о карьере и планировщики приглашений на своем собственном домене. Ваш брендинг, наши технологии.", "Hotels and Motels": "Отели и мотели", "Hours": "<PERSON>а<PERSON>ы", "Household Appliance Manufacturing": "Производство бытовой техники", "Household Services": "Бытовые услуги", "Household and Institutional Furniture Manufacturing": "Производство мебели для дома и учреждений", "Housekeeper": "Домработница", "Housing Programs": "Жилищные программы", "Housing and Community Development": "Жилье и общественное развитие", "How does this work?": "Как это работает?", "How it works": "Как это работает", "How likely are you to recommend Teamdash to a colleague?": "Насколько вероятно, что вы порекомендуете Teamdash коллегам?", "How likely are you to recommend a friend or colleague to apply for a job with us?": "Насколько вероятно, что вы порекомендуете другу или коллеге устроиться к нам на работу?", "How many days to offer?": "Сколько дней нужно предложить?", "How this works": "Как это работает", "How to automate cNPS feedback collection?": "Как автоматизировать сбор отзывов cNPS?", "How to include cNPS surveys in messages?": "Как включить опросы cNPS в сообщения?", "Human Resources": "<PERSON><PERSON><PERSON><PERSON><PERSON> кадров", "Human Resources Services": "Кадровые службы", "Human resources": "Человеческие ресурсы", "Hungarian": "Венгерский", "Hybrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hydroelectric Power Generation": "Производство гидроэлектроэнергии", "I accept the": "Я принимаю", "I acknowledge": "Я признаю", "I agree with having my data stored for 36 months.": "Я согласен с тем, чтобы мои данные хранились в течение 36 месяцев.", "I already have a membership": "У меня уже есть членство", "I am interested in the Teams feature. Can you tell me more?": "Меня интересует функция \"Команды\". Не могли бы вы рассказать мне подробнее?", "I can't find the location": "Я не могу найти местоположение", "I forgot my password": "Я забыл свой пароль", "I need an invoice": "Мне нужен счет-фактура", "I need help with this": "Мне нужна помощь", "I want to limit my consent": "", "IMAP messages sync": "Синхронизация сообщений IMAP", "IMAP path": "Путь IMAP", "IP": "IP", "IT": "IT", "IT Services and IT Consulting": "ИТ-услуги и ИТ-консалтинг", "IT System Custom Software Development": "Разработка программного обеспечения для ИТ-систем на заказ", "IT System Data Services": "Услуги по обработке данных ИТ-систем", "IT System Design Services": "Услуги по проектированию ИТ-систем", "IT System Installation and Disposal": "Установка и утилизация ИТ-систем", "IT System Operations and Maintenance": "Эксплуатация и обслуживание ИТ-систем", "IT System Testing and Evaluation": "Тестирование и оценка ИТ-систем", "IT System Training and Support": "Обучение и поддержка ИТ-систем", "Icelandic": "Исландия", "Icon": "Икона", "Icon color": "Цвет значка", "Icon size": "Размер значка", "Ido": "Идо", "If enabled, GDPR automation sends out \"consent renewal request\" emails.": "Если эта функция включена, автоматизация GDPR отправляет электронные письма с запросом на продление согласия.", "If the integration has API keys that are directly linked, the feed will open only using those keys.": "", "If you agreed on a new time, they can select a time slot from the original scheduling email.": "Если вы договорились о новом времени, они могут выбрать временной интервал из первоначального письма с расписанием.", "If you already have an API key, copy it to the field above, otherwise, generate a new key": "Если у вас уже есть ключ API, скопируйте его в поле выше, в противном случае сгенерируйте новый ключ", "If you are interested in our future open positions offers, please give your consent for data processing here: [consent_renewal_url].": "Если вы заинтересованы в наших будущих предложениях открытых вакансий, пожалуйста, дайте свое согласие на обработку данных здесь: [consent_renewal_url].", "If you are unable to participate": "Если вы не можете принять участие", "If you are unable to participate, :declineLink.": "Если вы не сможете принять участие, :declineLink.", "If you are using Outlook, drag and drop candidate's email from your inbox and they will be automatically added to your project as a candidate.": "Если вы используете Outlook, перетащите письмо кандидата из папки \"Входящие\", и он будет автоматически добавлен в ваш проект в качестве кандидата.", "If you cannot participate, you can cancel the interview and the other participants will be notified that the event needs to be rescheduled.": "Если вы не можете принять участие, вы можете отменить интервью, а остальные участники будут уведомлены о необходимости перенести мероприятие.", "If you did not receive the email": "Если вы не получили письмо по электронной почте", "If you did not request a password reset, no further action is required.": "Если вы не запрашивали сброс пароля, дальнейшие действия не требуются.", "If you don't choose any delivery channel, candidates will not get your invites.": "", "If you don't have a landing page for this job yet, create it": "Если у вас еще нет целевой страницы для этой работы, создайте ее", "If you don't have an ad for this job yet, create it": "Если у вас еще нет объявления об этой работе, создайте его", "If you don't map this field to a candidate field, the field value will still be available on form submissions page.": "Если вы не сопоставите это поле с полем кандидата, значение поля все равно будет доступно на странице отправки формы.", "If you don't understand it, just reply to this email :)": "Если вы не понимаете, просто ответьте на это письмо :)", "If you edit an already published job ad in Teamdash, a new advertisement will be posted to ss.lv.": "", "If you have already shared this feed with someone, you will need to give them a new URL after you create the new key.": "", "If you have any questions, just click the support icon in bottom-right of your screen and ask away.": "Если у вас возникнут вопросы, просто нажмите на значок поддержки в правом нижнем углу экрана и задайте их.", "If you have filters applied, check that you have selected the correct template type.": "", "If you have updated the form, click here to load changes.": "Если вы обновили форму, нажмите здесь, чтобы загрузить изменения.", "If you have updated the form, click the refresh button on the form to load changes.": "Если вы обновили форму, нажмите кнопку обновления на форме, чтобы загрузить изменения.", "If you leave before saving, your changes will be lost.": "Если вы уйдете до сохранения, ваши изменения будут потеряны.", "If you set a value here, a button with this text will be displayed after form submission.": "Если вы зададите здесь значение, то после отправки формы будет отображаться кнопка с этим текстом.", "If you wish to add more slots, you can do that in the :calendarLink.": "Если вы хотите добавить больше слотов, вы можете сделать это в :calendarLink.", "If you wish to let the candidate reschedule the interview, you can add more slots in the :calendarLink.": "Если вы хотите, чтобы кандидат мог перенести собеседование, вы можете добавить дополнительные слоты в :calendarLink.", "If you wish to let the candidate reschedule the interview, you can send them a message under their profile.": "Если вы хотите, чтобы кандидат перенес собеседование, вы можете отправить ему сообщение в его профиле.", "If you wish to send out all email from @yourdomain.xyz, please set up a mail identity.": "Если вы хотите отправлять все письма с @yourdomain.xyz, настройте почтовый идентификатор.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser": "Если у вас возникли проблемы с нажатием кнопки \":actionText\", скопируйте и вставьте в веб-браузер приведенный ниже URL-адрес", "If your address is <i>mycompany</i>.bamboohr.com, then use <i>mycompany</i> as username.": "Если ваш адрес <i>mycompany</i>.bamboohr.com, то в качестве имени пользователя используйте <i>mycompany</i>.", "Igbo": "Игбо", "Image": "Изображение", "Image ad": "Объявление с изображением", "Image position": "Положение изображения", "Image settings": "Настройки изображения", "Image:": "Изображение:", "Images": "", "Import candidates": "Импортные кандидаты", "Import connection OK!": "Импортное соединение OK!", "Import from mailbox": "Импорт из почтового ящика", "Import from {integrationType}": "Импорт из {integrationType}", "Imported :count candidates": "Импортированные кандидаты :count", "Imported from external system": "Импортировано из внешней системы", "Imports": "Импорт", "Imports are synced automatically every 5 minutes.": "Импорт синхронизируется автоматически каждые 5 минут.", "Impressions": "Впечатления", "Improve writing": "Улучшить письмо", "In project view, click the three dots in stage header and choose \"Set automatic stage action\".": "В режиме просмотра проекта нажмите на три точки в заголовке этапа и выберите \"Установить автоматическое действие этапа\".", "In shifts": "Попеременно", "Inactive": "Неактивный", "Include a form that is linked to this project on a landing page": "Включите форму, связанную с этим проектом, на целевую страницу", "Included on {landingPage}": "Включено в {landingPage}", "Incoming message:": "Входящее сообщение:", "Increase Level": "Повысить уровень", "Individual and Family Services": "Индивидуальные и семейные услуги", "Indonesian": "Индонезия", "Industrial Machinery Manufacturing": "Производство промышленного оборудования", "Industry (CV-Library)": "Промышленность (CV-Library)", "Industry Associations": "Отраслевые ассоциации", "Info": "Информация", "Info message": "Информационное сообщение", "Inform that ignoring this message is assumed to be a non-consent.": "Сообщите, что игнорирование этого сообщения расценивается как несогласие.", "Inform that they can immediately decline consent from the link.": "Сообщите, что они могут немедленно отказаться от согласия на переход по ссылке.", "Information Services": "Информационные услуги", "Information Technology": "Информационные технологии", "Information technology": "Информационные технологии", "Information tehcnology": "Информационная технология", "Inherit candidate file access rights from projects.": "", "Inherit limited user file access from projects": "Наследуйте ограниченный доступ к файлам пользователей из проектов", "Initialize comment visibility switch as public": "Инициализируйте переключатель видимости комментария как общедоступный", "Initiator": "Инициатор", "Instance email provider": "Поставщик электронной почты экземпляра", "Instant Upload": "Мгновенная загрузка", "Instant video call": "Мгновенный видеозвонок", "Institution": "Учреждение", "Instructor": "Инструктор", "Insurance": "Страхование", "Insurance Agencies and Brokerages": "Страховые агентства и брокерские компании", "Insurance Carriers": "Страховые компании", "Insurance agent": "Страховой агент", "Insurance and Employee Benefit Funds": "Фонды страхования и выплат сотрудникам", "Insurances": "Страхование", "Integration name": "Название интеграции", "Integrations": "Интеграции", "Interior Design": "Ди<PERSON><PERSON><PERSON>н интерьера", "Interlingua (International Auxiliary Language Association)": "Интерлингва (Международная ассоциация вспомогательных языков)", "Interlingue": "Интерлингва", "Internal form": "", "Internal job ad": "Внутреннее объявление о работе", "Internal name used in Teamdash": "Внутреннее имя, используемое в Teamdash", "International Affairs": "Международные дела", "International Trade and Development": "Международная торговля и развитие", "Internet Marketplace Platforms": "Платформы для интернет-рынков", "Internet News": "Новости интернета", "Internship": "Стажировка", "Interurban and Rural Bus Services": "Междугородние и сельские автобусные рейсы", "Interview": "Интервью", "Interview file": "Досье для интервью", "Interview invite": "Приглашение на собеседование", "Interview length": "Продолжительность интервью", "Interview length (in minutes)": "Продолжительность интервью (в минутах)", "Interview scheduled": "Интервью назначено", "Interview scheduling": "Планирование собеседований", "Interview scheduling is not available within templates. You can add this action after creating a project.": "Планирование интервью недоступно в шаблонах. Вы можете добавить это действие после создания проекта.", "Interview slot": "Слот для интервью", "Interview slot: :title on :start (:tz) has been cancelled.": "Слот для интервью: :title на :start (:tz) был отменен.", "Interview slot: :title scheduled for :start (:tz)": "Слот для интервью: :название назначен на :начало (:tz)", "Interview summary": "Краткое содержание интервью", "Interview time": "Время интервью", "Interview title": "Название интервью", "Interviewees": "Интервьюируемые", "Interviews": "Интервью", "Inuktitut": "Инуктитут", "Inupiaq": "Инупиак", "Invalid": "Неверный", "Invalid color code": "Неверный код цвета", "Invalid confirmation code!": "Неверный код подтверждения!", "Invalid phone number.": "Неверный номер телефона.", "Invalid user to reassign to": "", "Investment Advice": "Инвестиционные консультации", "Investment Banking": "Инвестиционно-банковская деятельность", "Investment Management": "Управление инвестициями", "Invitation details": "Детали приглашения", "Invite accepted": "Приглашение принято", "Invite candidates": "Пригласите кандидатов", "Invite confirmation": "Подтверждение приглашения", "Invite message": "Пригласительное сообщение", "Invite new users": "Приглашайте новых пользователей", "Invite pending": "Приглашение ожидается", "Invite sent to user! If there are further problems, forward the password reset url manually: :url": "", "Invite sent!": "", "Invite subject": "Пригласить субъекта", "Invite users": "Пригласить пользователей", "Invites": "Пригла<PERSON><PERSON><PERSON>т", "Invites not sent!": "Приглашения не отправлены!", "Invites sent!": "Приглашения отправлены!", "Irish": "Ирландский", "Is public (shared with other users)": "Является публичным (доступен другим пользователям)", "Is remote?": "Удаленный?", "Is teams admin?": "Является ли команда администратором?", "Is this an e-mail from a candidate? Drop it here!": "Это письмо от кандидата? Бросьте его сюда!", "Is your database GDPR compliant?": "Соответствует ли ваша база данных требованиям GDPR?", "Issuer URL": "URL-адрес эмитента", "It MUST include [password_url]. It may include [current_user_name], [user_name].": "Он ДОЛЖЕН включать [password_url]. Мож<PERSON>т включать [current_user_name], [user_name].", "It asks your candidates to rate on a 0 to 10 scale -": "В нем кандидатам предлагается оценить себя по шкале от 0 до 10.", "It looks like you're using the new iOS 18.": "", "It may include :tags.": "Он может включать в себя :теги.", "It seems that you don’t have access to this feature.": "Пох<PERSON><PERSON>е, что у вас нет доступа к этой функции.", "It uses your calendar integration to see when participants are busy.": "Он использует интеграцию с календарем, чтобы узнать, когда участники заняты.", "Italian": "Итальянский", "Italic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Items": "Товары", "Janitor": "Уборщик", "Janitorial Services": "Уборка помещений", "January": "Январь", "Japanese": "Японский", "Javanese": "Яванский", "Jeweler": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Job Location": "Место работы", "Job Unpublished (:orgName): :position": "", "Job ad button text": "Текст кнопки объявления о работе", "Job ad count text": "Текст объявления о приеме на работу", "Job ad exports": "Экспорт объявлений о работе", "Job ad has been sent to your email.": "Объявление о вакансии отправлено на вашу электронную почту.", "Job ad image": "Изображение объявления о работе", "Job ad landing page hostname": "Имя хоста целевой страницы объявлений о работе", "Job ads": "Объявления о работе", "Job boards": "Доски объявлений о работе", "Job description preview": "Предварительное описание работы", "Job functions": "Должностные обязанности", "Job info": "Информация о работе", "Job level (CVK/CVM)": "Уровень должности (CVK/CVM)", "Job published!": "Работа опубликована!", "Job requisition": "Заявка на работу", "Job requisition added": "Добавлена заявка на работу", "Job requisition deadline changed: new deadline in :deadline": "Срок подачи заявки на работу изменен: новый срок в :deadline", "Job requisitions": "Заявки на работу", "Job type": "Тип работы", "Join the video interview here:": "Присоединяйтесь к видеоинтервью здесь:", "Join your team on Teamdash": "Присоединяйтесь к своей команде в Teamdash", "Joiner": "Столяр", "Journalist": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "July": "Июль", "June": "Июнь", "KB": "KB", "Kannada": "Каннада", "Kanuri": "Канури", "Kashmiri": "Кашмири", "Kazakh": "Казах", "Keep your Teamdash user list in sync with your company's identity provider.": "Поддерживайте список пользователей Teamdash в синхронизации с поставщиком идентификационных данных вашей компании.", "Keep your accounts secure with two-factor authentication. Requires a mobile phone number.": "Обеспечьте безопасность своих учетных записей с помощью двухфакторной аутентификации. Требуется номер мобильного телефона.", "Key name": "Название ключа", "Keys": "Клю<PERSON>и", "Kinyarwanda": "Киньярванда", "Knitter": "Вязальщица", "Komi": "Коми", "Kongo": "Конго", "Korean": "Корейский", "Kurdish": "Курдский", "Kwanyama, Kuanyama": "Куаньяма, Куаньяма", "Kyrgyz": "Кыргызстан", "LARS code": "Код LARS", "Label": "Этикетка", "Laboratory assistant": "Л<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>т", "Landing": "Посадка", "Landing URL copied to clipboard.": "URL-адрес сайта скопирован в буфер обмена.", "Landing Upload": "Посадка Загрузка", "Landing page": "Посадочная страница", "Landing page URL": "URL-адрес посадочной страницы", "Landing pages": "Посадочные страницы", "Landscaping Services": "Услуги по ландшафтному дизайну", "Language": "Язык", "Language (CVbankas)": "Язык (CVbankas)", "Language Schools": "Языковые школы", "Language skills (profession.hu)": "", "Lao": "Лао", "Last 30 days": "Последние 30 дней", "Last 30 scrapes of :feedName feed": "Последние 30 отпечатков :feedName feed", "Last 60 days": "Последние 60 дней", "Last 90 days": "Последние 90 дней", "Last active": "Последний активный", "Last education": "Последнее образование", "Last employment": "Последнее место работы", "Last project": "Последний проект", "Last scraped at :lastScrapeTime": "Последний раз соскоб был произведен в :lastScrapeTime", "Last updated on {date}": "Последнее обновление {date}", "Latest bounced.": "Последние отскочили.", "Latest clicked.": "Последние щелчки.", "Latest opened.": "Последние открытые.", "Latest sent.": "Последние отправленные.", "Latest version published": "Опубликована последняя версия", "Latin": "Латинская", "Latvia": "Латвия", "Latvian": "Латвия", "Laundry and Drycleaning Services": "Услуги прачечной и химчистки", "Law": "Закон", "Law / Legal": "Право / Юридические науки", "Law Enforcement": "Правоохранительные органы", "Law Practice": "Юридическая практика", "Lawyer": "Адвокат", "Learn more": "Узнать больше", "Leasing Non-residential Real Estate": "Сдача в аренду нежилой недвижимости", "Leasing Real Estate Agents and Brokers": "Агенты и брокеры по аренде недвижимости", "Leasing Residential Real Estate": "Аренда жилой недвижимости", "Leather Product Manufacturing": "Производство изделий из кожи", "Leave empty for default": "Оставьте пустым для значения по умолчанию", "Leave empty if not applicable": "Оставьте пустым, если не применимо", "Leave empty to remove button": "Оставьте пустым, чтобы удалить кнопку", "Leave without editing candidates": "Оставить без редактирования кандидатов", "Left": "Слева", "Left button link override": "Переопределение ссылки на левую кнопку", "Legal": "Юридическая", "Legal Services": "Юридические услуги", "Legal ground": "Правовая основа", "Legislative Offices": "Законодательные учреждения", "Leisure/Hospitality": "Досуг/гостиничный бизнес", "Leisure/Tourism": "Досуг/Туризм", "Less time in stage first": "Меньше времени на первом этапе", "Let AI search the candidate's CV and add relevant tags if applicable.": "Пусть искусственный интеллект осуществляет поиск по резюме кандидата и добавляет соответствующие теги, если это необходимо.", "Let your candidates apply via form": "Дайте кандидатам возможность подать заявку через форму", "Let your managers request new hires and let management approve or reject the requests. All steps are customisable.": "Пусть ваши менеджеры запрашивают новых сотрудников, а руководство утверждает или отклоняет их. Все шаги можно настраивать.", "Letzeburgesch, Luxembourgish": "Летцебургский, люксембургский", "Level": "", "Level of education": "Уровень образования", "Libraries": "Библиотеки", "Lighter": "Зажигалка", "Limburgish, Limburgan, Limburger": "Лимбургский, лимбургский, лимбургский, лимбургерский", "Lime and Gypsum Products Manufacturing": "Производство извести и гипсовых изделий", "Limit interviews to start on ...": "Ограничьте количество собеседований, чтобы начать ...", "Limited user": "Ограниченный пользователь", "Limited users can see the same projects as regular users. Stages hidden from limited users stay hidden.": "", "Lingala": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>а", "Link": "Ссылка", "Link URL": "Ссылка URL", "Link to current project": "Ссылка на текущий проект", "Linked to": "", "LinkedIn": "LinkedIn", "LinkedIn Company ID": "Идентификатор компании LinkedIn", "LinkedIn URL": "URL-адрес LinkedIn", "LinkedIn industry code": "Код отрасли LinkedIn", "Links": "Ссылки", "Linux admin": "Администратор Linux", "List": "Список", "List all kinds of data you might be processing (e.g. CV, contact info, etc)": "Перечислите все виды данных, которые вы можете обрабатывать (например, резюме, контактная информация и т. д.)", "List of Sub-processors": "Список субпроцессоров", "List of your other job ads": "Список ваших других объявлений о работе", "Lithuania": "Литва", "Lithuanian": "Литва", "Load more": "Загрузить больше", "Loader": "Погрузчик", "Loading": "", "Loading landing page preview": "", "Loading...": "Загрузка...", "Loan Brokers": "Кредитные брокеры", "Location": "Расположение", "Location (CVO)": "Местонахождение (CVO)", "Location (profession.hu)": "Местонахождение (profession.hu)", "Location address": "Адрес местонахождения", "Location data": "Данные о местоположении", "Location name": "Название места", "Location range": "Диапазон местоположения", "Location:": "Расположение:", "Locations": "Места", "Locksmith": "Слесарь", "Log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Log in to CV online, make sure you have an administrator account": "Войдите в CV online, убедитесь, что у вас есть учетная запись администратора", "Log out": "Выйти из системы", "Login failed. The user does not have an email address associated with their account.": "Вход в систему не удался. У пользователя нет адреса электронной почты, связанного с его учетной записью.", "Logistician": "Лог<PERSON><PERSON>т", "Logistics": "Логистика", "Logistics / Transport": "Логистика / Транспорт", "Logo": "Лого<PERSON>ип", "Long term consent expiring soon": "Срок действия долгосрочного соглашения истекает в ближайшее время", "Long text": "", "Longer in stage first": "Дольше на первом этапе", "Loss Prevention": "Предотвращение потерь", "Lower rated first": "Сначала более низкий рейтинг", "Lower scorecard score first": "Сначала наберите меньшее количество баллов", "Luba-Katanga": "Луба-Катанга", "MB": "MB", "MS Office messages sync": "Синхронизация сообщений MS Office", "MTD": "MTD", "Macedonian": "Македония", "Machinery Manufacturing": "Машиностроение", "Magnetic and Optical Media Manufacturing": "Производство магнитных и оптических носителей информации", "Mail": "Почта", "Mail folder ID": "Идентификатор почтовой папки", "Mail identities": "Почтовые идентификаторы", "Mail settings": "Настройки почты", "Mail signature settings": "Настройки почтовой подписи", "Make default": "Сделать по умолчанию", "Make import and application form submission comments visible to limited users": "Сделать комментарии к импорту и форме подачи заявки видимыми для ограниченных пользователей", "Make longer": "Сделать длиннее", "Make private": "Сделать частным", "Make public": "Сделать публичным", "Make sure this is a trusted address controlled by your organization.": "", "Make this comment visible to limited users": "Сделать этот комментарий видимым для ограниченного числа пользователей", "Make this entry visible to limited users": "Сделайте эту запись видимой для ограниченного числа пользователей", "Make your job ads accessible only from your company's internal network.": "Сделайте объявления о работе доступными только из внутренней сети вашей компании.", "Malagasy": "Малага<PERSON>и", "Malay": "Малай<PERSON>ы", "Malayalam": "Мал<PERSON><PERSON><PERSON>ам", "Maltese": "Мальтийский", "Manage project access": "Управление доступом к проекту", "Manage stage actions": "Управление действиями этапа", "Manage stage actions for {stageName}": "Управляйте действиями этапа для {stageName}", "Manage teams hierarchy": "Управление иерархией команд", "Manage users": "Управление пользователями", "Management": "Управление", "Management/quality management": "Управление/менеджмент качества", "Manager": "Мен<PERSON>д<PERSON><PERSON><PERSON>", "Manager by staff": "Менеджер по персоналу", "Manager name": "Имя менеджера", "Manager or coworker quotes block": "Блок цитат руководителя или сослуживца", "Manager:": "Менеджер:", "Managing recruiter": "Управляющий рекрутер", "Mandatory two-factor authentication": "Обязательная двухфакторная аутентификация", "Manicurist": "Маникюрша", "Manually added to stage": "Ручное добавление на этап", "Manufacturing": "Производство", "Manufacturing/Surveying": "Производство/Исследование", "Manx": "М<PERSON>н<PERSON><PERSON>", "Maori": "Маори", "Marathi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "March": "Ма<PERSON><PERSON>", "Maritime Transportation": "Морские перевозки", "Mark GDPR consent for imported candidates": "Отметьте согласие на GDPR для импортируемых кандидатов", "Mark as favourite": "Отметить как любимое", "Mark finished": "Марк закончил", "Mark notifications as read": "Пометить уведомления как прочитанные", "Market Research": "Исследование рынка", "Marketing": "Марк<PERSON><PERSON><PERSON>нг", "Marketing / Advertising": "Маркетинг / Реклама", "Marketing Services": "Маркетинговые услуги", "Marketing/advertising": "Маркетинг/реклама", "Marshallese": "Марш<PERSON><PERSON><PERSON>овы", "Mason": "Мейсон", "Masseur": "М<PERSON>с<PERSON><PERSON><PERSON><PERSON><PERSON>т", "Master's degree": "Степень магистра", "Match candidates with all the conditions above.": "Подберите кандидатов, отвечающих всем вышеперечисленным условиям.", "Match candidates with at least one of the conditions above.": "Подберите кандидатов, удовлетворяющих хотя бы одному из вышеперечисленных условий.", "Mattress and Blinds Manufacturing": "Производство матрасов и штор", "Max attempt count: unlimited": "Максимальное количество попыток: неограниченно", "Max attempt count: {maxAttemptCount}": "Максимальное количество попыток: {maxAttemptCount}", "Max candidate attempts": "Максимальное количество попыток кандидата", "Maximum value": "Максимальное значение", "May": "<PERSON><PERSON><PERSON>", "Measuring and Control Instrument Manufacturing": "Производство измерительных и контрольных приборов", "Meat Products Manufacturing": "Производство мясных продуктов", "Mechanic": "Механик", "Media": "СМИ", "Media / PR": "СМИ / PR", "Media Production": "Медиапроизводство", "Media and Telecommunications": "Медиа и телекоммуникации", "Media/communication": "СМИ/коммуникации", "Medical Equipment Manufacturing": "Производство медицинского оборудования", "Medical Practices": "Медицинские практики", "Medical and Diagnostic Laboratories": "Медицинские и диагностические лаборатории", "Medical/Pharmaceutical/Scientific": "Медици<PERSON>/Фармацевтика/Наука", "Medicine/pharmacy": "Медицина/аптека", "Meet your candidates without scheduling a call. Candidates answer your questions on their own time.": "Встречайтесь с кандидатами, не назначая звонка. Кандидаты отвечают на ваши вопросы в свободное время.", "Meeting details": "", "Meeting location": "Место встречи", "Meeting location:": "Место встречи:", "Meeting room:": "", "Members": "Члены", "Members to ping": "Участники для пинга", "Mental Health Care": "Уход за психическим здоровьем", "Mentor:": "Наставник:", "Merchandising": "Мерчандайзинг", "Merge": "Объединить", "Merge duplicate candidates": "Объедините дубликаты кандидатов", "Merge tags": "Объединить теги", "Merged": "Объединенный", "Message": "Сообщение", "Message Attachment": "Вложение сообщения", "Message body": "Тело сообщения", "Message details": "Подробности сообщения", "Message not sent!": "Сообщение не отправлено!", "Message project members": "", "Message sent!": "Сообщение отправлено!", "Message subject": "Тема сообщения", "Message templates": "Шаблоны сообщений", "Message will be sent in": "Сообщение будет отправлено в", "Message:send": "Сообщение", "Messages": "Сообщения", "Meta description": "Мета-описание", "Metal Ore Mining": "Добыча металлических руд", "Metal Treatments": "Обработка металла", "Metal Valve, Ball, and Roller Manufacturing": "Производство металлических клапанов, шаров и роликов", "Metalworking Machinery Manufacturing": "Производство металлообрабатывающего оборудования", "Microsoft Calendar schedule": "Календарь Microsoft Расписание", "Microsoft Teams calls": "Вызовы Microsoft Teams", "Mid-senior level": "Средний-старший уровень", "Middle manager": "Менеджер среднего звена", "Military and International Affairs": "Военные и международные отношения", "Military/Emergency/Government": "Военные/Чрезвычайные ситуации/Правительство", "Milkmaid": "Доярка", "Milling-machine operator": "Машинист фрезерного станка", "Minimize": "Минимиз<PERSON><PERSON><PERSON><PERSON><PERSON>е", "Minimize column": "Минимизировать колонку", "Minimum amount of users needed approval from": "Минимальное количество пользователей, требующее одобрения", "Minimum number of minutes between the interview and the next event": "Минимальное количество минут между интервью и следующим событием", "Minimum number of minutes between the previous event and the interview": "Минимальное количество минут между предыдущим событием и интервью", "Minimum value": "Минимальное значение", "Mining": "Горная промышленность", "Minutes": "Протоколы", "Missing :missingCount :approval.": "Отсутствует :missingCount :approval.", "Missing email!": "Пропала электронная почта!", "Missing or invalid phone!": "Отсутствующий или недействительный телефон!", "Missing recorded video! Make sure you clicked the \"Confirm\" button after recording.": "Пропало записанное видео! Убедитесь, что вы нажали кнопку \"Подтвердить\" после записи.", "Missing social media preview image. Please generate or upload it.": "Отсутствует изображение для предварительного просмотра в социальных сетях. Пожалуйста, сгенерируйте или загрузите его.", "Missing translation in {language}": "Отсутствующий перевод в {language}", "Missing {missingCount} {approval}.": "Отсутствует {missingCount} {approval} .", "Mixed": "Смешанные", "Mobile Computing Software Products": "Программные продукты для мобильных компьютеров", "Mobile Food Services": "Мобильные службы питания", "Mobile Gaming Apps": "Мобильные игровые приложения", "Mobile View": "Мобильный вид", "Model": "Модель", "Moldovan, Moldavian, Romanian": "Молдавский, молдавский, румынский", "Monday": "Понедельник", "Mongolian": "Монголия", "Month": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Months": "", "Months of GDPR consent": "Месяцы на получение согласия в соответствии с GDPR", "More": "Подробнее", "More info": "Дополнительная информация", "Motor Vehicle Manufacturing": "Производство автомобилей", "Motor Vehicle Parts Manufacturing": "Производство запчастей для автомобилей", "Move down": "Двиг<PERSON>йтесь вниз", "Move the candidate to {stage}": "Переместите кандидата в {stage}", "Move this interview to another time and send event updates to all participants.": "Перенесите это интервью на другое время и отправьте обновления всем участникам.", "Move to next stage": "Переход к следующему этапу", "Move to stage": "Переместить на этап", "Move up": "Поднимайтесь", "Moved to stage": "Перемещен на этап", "Moved to stage by action": "Перемещено на этап действием", "Movie and Video Distribution": "Распространение фильмов и видео", "Movies, Videos and Sound Recording": "Фильмы, видео и звукозаписи", "Multimedia": "Мультимедиа", "Multiple files": "Несколько файлов", "Multiple stages": "Несколько этапов", "Multiselect": "Multiselect", "Multiselect (checkboxes)": "Мульти<PERSON><PERSON><PERSON>ект (флажки)", "Museums": "Музеи", "Museums, Historical Sites, and Zoos": "Музеи, исторические места и зоопарки", "Musician": "Музыкант", "Musicians": "Музыканты", "Must accept - :attribute": "Дол<PERSON>ен принять - :attribute", "Must be accepted": "Должны быть приняты", "Must be in lowercase a-z and underscore, at least 5 characters. This is used internally by the system.": "Должен состоять из строчных букв a-z и подчеркивания, не менее 5 символов. Это используется внутри системы.", "Mute e-mail notifications": "Отключение звука уведомлений по электронной почте", "My account": "Мой счет", "My form": "Моя форма", "Name": "Имя", "Names of people": "<PERSON><PERSON><PERSON>на людей", "Nanny": "Няня", "Nanotechnology Research": "Исследования в области нанотехнологий", "Natural Gas Distribution": "Распределение природного газа", "Natural Gas Extraction": "Добыча природного газа", "Nauru": "Науру", "Navajo, Navaho": "Навахо, навахо", "Ndonga": "Ндонга", "Needed for reach estimation": "Требуется для оценки охвата", "Neighbourhood or parish (SS.lv)": "Соседство или приход (SS.lv)", "Nepali": "Непали", "Network administrator": "Сетевой администратор", "Neutral": "Нейтральный", "New Job Posted by :orgName: :position": "", "New Project": "Новый проект", "New SMS from :name": "Новое SMS от :name", "New chat": "Новый чат", "New project": "Новый проект", "New project display name": "Новое отображаемое имя проекта", "New project from scratch": "Новый проект с нуля", "New project name": "Новое название проекта", "New stage name": "Новое название этапа", "New user invite template": "Шаблон приглашения нового пользователя", "Newer applications first": "Сначала новые приложения", "Newest first": "Самый новый первый", "Newspaper Publishing": "Издание газет", "Next": "Следующий", "Next decade": "Следующая декада", "Next file": "Следующий файл", "Next month": "Следующий месяц", "Next year": "Следующий год", "Nice": "<PERSON>и<PERSON><PERSON>", "No": "Нет", "No API keys added yet.": "", "No Event": "Нет события", "No actions yet": "Пока никаких действий", "No active links": "Нет активных ссылок", "No activity recorded yet.": "", "No application methods yet": "Методов применения пока нет", "No candidate has confirmed this time slot yet. You'll get an update once it's confirmed.": "Ни один кандидат пока не подтвердил этот временной интервал. Вы получите обновленную информацию, как только он будет подтвержден.", "No candidates found.": "Кандидаты не найдены.", "No candidates have been shared with you yet.": "", "No comments yet": "", "No consent": "Нет согласия", "No data": "Нет данных", "No data available": "Нет данных", "No date selected": "Дата не выбрана", "No email": "Нет электронной почты", "No file chosen": "Файл не выбран", "No file types added yet.": "", "No forms": "", "No forms added yet.": "Формы пока не добавлены.", "No forms found.": "Формы не найдены.", "No integrations yet": "", "No landing pages yet": "Целевых страниц пока нет", "No landings found": "", "No landings found. Try adjusting the filters.": "Не найдено ни одной посадки. Попробуйте настроить фильтры.", "No lower than higher education": "Не ниже высшего образования", "No lower than secondary education": "Образование не ниже среднего", "No messages": "", "No permissions": "", "No phone": "Нет телефона", "No project log entries": "", "No projects": "Нет проектов", "No projects found": "Проекты не найдены", "No projects have been created from this template yet.": "Из этого шаблона еще не было создано ни одного проекта.", "No ratings": "", "No reason specified": "Причина не указана", "No references": "", "No results found": "", "No results text": "Текст без результатов", "No scorecards added yet.": "Табло пока не добавлено.", "No scorecards found.": "Табло не найдено.", "No sentence may be longer than 200 characters.": "Длина предложения не должна превышать 200 символов.", "No submissions yet": "", "No survey response yet": "Пока нет ответа на опрос", "No team": "Нет команды", "No templates found": "Шаблоны не найдены", "No templates yet": "", "No templates yet. Try changing the filters.": "Шаблонов пока нет. Попробуйте изменить фильтры.", "No transcripts found.": "Транскрипты не найдены.", "No users found": "Пользователи не найдены", "No users found. Try changing the filters.": "Пользователи не найдены. Попробуйте изменить фильтры.", "No video interviews found.": "Видеоинтервью не найдено.", "No, let me double-check": "Нет, позвольте мне перепроверить.", "Non-profit Organizations": "Некоммерческие организации", "None": "Нет", "None selected": "Не выбрано", "Nonmetallic Mineral Mining": "Добыча неметаллических полезных ископаемых", "Nonresidential Building Construction": "Строительство нежилых зданий", "North & Western Europe": "Северная и Западная Европа", "Northern Ndebele": "Северные ндебеле", "Northern Sami": "Северные саамы", "Norwegian": "Норвежский", "Norwegian Bokmål": "Норвежский язык Bokmål", "Norwegian Nynorsk": "Норвежский нюнорск", "Not Found": "Не найдено", "Not a valid Vimeo URL.": "Недействительный URL-адр<PERSON><PERSON> Vimeo.", "Not a valid Youtube URL.": "Недействительный URL-адрес Youtube.", "Not a valid video file URL.": "Неверный URL-адрес видеофайла.", "Not allowed": "Не разрешается", "Not applicable": "Не применимо", "Not duplicate": "Не дублируется", "Not included on any landing pages": "Не включено ни на одной целевой странице", "Not scheduled yet": "Пока не запланировано", "Not specified": "Не указано", "Not started": "", "Notes": "", "Notes (not visible to candidates):": "", "Nothing here": "", "Nothing to show": "", "Notice: integration health check failed - :website": "Уведомление: проверка работоспособности интеграции не удалась - :сайт", "Notification email": "", "Notifications are currently turned on. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn off.": "В настоящее время уведомления включены. Электронная почта доставляется по будням в 8:00, 13:00, 16:00. Нажмите здесь, чтобы отключить.", "November": "Ноябрь", "Now let's create your personal account.": "Теперь давайте создадим вашу личную учетную запись.", "Nuclear Electric Power Generation": "Производство атомной электроэнергии", "Number of days": "Количество дней", "Number of hours per week": "Количество часов в неделю", "Number of positions": "Количество позиций", "Numbers": "Номера", "Numeric rating": "Числовой рейтинг", "Numeric value": "Числовое значение", "Nuosu, Sichuan Yi": "Нуосу, Сычуань И", "Nurse": "Медсестра", "Nursing": "Сестринское дело", "Nursing Homes and Residential Care Facilities": "Дома престарелых и интернаты", "OK": "OK", "Obligatory specialized education": "Обязательное специализированное образование", "Occitan (post 1500)": "Окситанский язык (после 1500 года)", "Occurrences": "Происшествия", "October": "Октябрь", "Off": "С сайта", "Offer": "Предложение", "Offer section title": "", "Offers": "Предложения", "Office": "<PERSON><PERSON><PERSON><PERSON>", "Office Administration": "Управление офисом", "Office Furniture and Fixtures Manufacturing": "Производство офисной мебели и приспособлений", "Offices": "О<PERSON><PERSON><PERSON><PERSON>", "Oh no": "О нет.", "Oil Extraction": "Добыча нефти", "Oil and Coal Product Manufacturing": "Производство нефтепродуктов и угольной продукции", "Oil and Gas": "Нефть и газ", "Oil, Gas, and Mining": "Нефть, газ и горнодобывающая промышленность", "Ojibwa": "Оджибва", "Older applications first": "Старые приложения в первую очередь", "On the second Tuesday of every month all your candidates without a valid data processing consent get a \"consent renewal request\" email.": "Во второй вторник каждого месяца все ваши кандидаты, не имеющие действующего согласия на обработку данных, получают письмо с просьбой продлить согласие.", "On the top menu, click your company name": "В верхнем меню нажмите на название вашей компании", "On-site": "На сайте", "Once logged in, go to the \"Manage app credentials\" here:": "Войдя в систему, перейдите в раздел \"Управление учетными данными приложения\":", "Once someone submits a form, their submission will appear here.": "", "Online Audio and Video Media": "Онлайновые аудио- и видеоносители", "Online and Mail Order Retail": "Розничная торговля через Интернет и по почте", "Only": "Только", "Only YouTube URLs. Used for job board exports.": "Только URL-адреса YouTube. Используется для экспорта на доски объявлений.", "Only YouTube links are supported.": "Поддерживаются только ссылки на YouTube.", "Only active users can log in to the system.": "Войти в систему могут только активные пользователи.", "Only administrators can delete clients.": "", "Only fill if you want to connect to a shared mailbox that your account has access to.": "Заполняется только в том случае, если вы хотите подключиться к общему почтовому ящику, к которому имеет доступ ваша учетная запись.", "Only from service accounts": "Только со счетов обслуживания", "Only my projects": "Только мои проекты", "Only project managers": "Только руководители проектов", "Only run if": "Выполняется только в том случае, если", "Only show comments from current project": "Показывать только комментарии из текущего проекта", "Only show logs for this project": "Показывайте журналы только для этого проекта", "Only summary": "Только краткое содержание", "Only you can see this message.": "Только вы можете увидеть это сообщение.", "Open": "", "Open CV": "Открытое резюме", "Open Teamdash": "Открыть Teamdash", "Open candidate": "Открытый кандидат", "Open candidate :initials profile": "Открыть профиль кандидата :инициалы", "Open form": "Открытая форма", "Open integration": "Открытая интеграция", "Open invite": "Открытое приглашение", "Open landing": "Открытая посадка", "Open profile": "Открытый профиль", "Open project": "Открытый проект", "Open slot": "Открытая щель", "Open slots": "Открытые слоты", "Opened at": "Открыт в", "Operations Consulting": "Операционный консалтинг", "Operator": "Оператор", "Optional info": "Дополнительная информация", "Options": "Опции", "Optometrists": "Оптометристы", "Or upload video": "Или загрузите видео", "Org. location city": "Местонахождение организации город", "Org. name": "Название организации", "Org. registry code": "Код реестра организации", "Org. street address": "Уличный адрес организации", "Org. website URL": "URL веб-сайта организации", "Org. website url": "url веб-сайта организации", "Organisation management": "Управление организацией", "Organization Logo": "Логотип организации", "Organization favicon": "", "Organization logo": "Логотип организации", "Organization name": "Название организации", "Organization name override": "Переопределение названия организации", "Organization office address": "Адрес офиса организации", "Organization settings": "Параметры организации", "Organization settings under the GDPR tab": "Настройки организации на вкладке GDPR", "Organization timezone": "Часовой пояс организации", "Organization type": "Тип организации", "Original location:": "Оригинальное местоположение:", "Oriya": "Ория", "Oromo": "Оромо", "Ossetian, Ossetic": "Осетинский, осетинский", "Other": "Другие", "Other Candidate file": "Другое досье кандидата", "Other Head Office Vacancies": "Другие вакансии в головном офисе", "Other Retailers": "Другие розничные продавцы", "Other exports": "Прочий экспорт", "Other file": "Другой файл", "Other icons": "Другие иконы", "Other integrations": "Другие интеграции", "Other languages": "Другие языки", "Other professions": "Другие профессии", "Otherwise, we will not contact you about future vacancies at [organization_name].": "В противном случае мы не будем связываться с вами по поводу будущих вакансий в [название_организации].", "Outcome description": "Описание результатов", "Outpatient Care Centers": "Центры амбулаторного ухода", "Outsourcing and Offshoring Consulting": "Консалтинг в области аутсорсинга и оффшоринга", "Overdue!": "Просрочка!", "Overlay": "Накладка", "Overrides the organization name from settings. Can be used if you're managing multiple brands": "Заменяет название организации из настроек. Может использоваться, если вы управляете несколькими брендами.", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "P.S. You can configure the question {here}.": "P.S. Вы можете настроить вопрос {here}.", "PB": "PB", "Packaging and Containers Manufacturing": "Производство упаковки и контейнеров", "Packer": "Упаковщик", "Page Expired": "Страница истекла", "Page background": "Фон страницы", "Page background:": "Фон страницы:", "Page meta description": "Метаописание страницы", "Page meta title": "Метазаголовок страницы", "Page saved!": "Страница сохранена!", "Page title": "Название страницы", "Paint, Coating, and Adhesive Manufacturing": "Производство красок, покрытий и клеев", "Painter": "Художник", "Pali": "Пали", "Panjabi, Punjabi": "Панджа<PERSON>и, пенджаби", "Paper and Forest Product Manufacturing": "Производство бумаги и лесной продукции", "Parlourmaid": "Горничная", "Part time": "Неполный рабочий день", "Participants": "Участники", "Participants will be notified that they are being recorded.": "Участники будут уведомлены о том, что ведется запись.", "Participants:": "Участники:", "Pashto, Pushto": "Пушту, пушту", "Password": "Пароль", "Password must not be in top 10M passwords": "Пароль не должен входить в топ-10 миллионов паролей", "Paver": "<PERSON>а<PERSON><PERSON><PERSON>", "Pending": "В ожидании", "Pending job requisition": "Заявка на работу на рассмотрении", "Pension Funds": "Пенсионные фонды", "Perform an action": "Выполнить действие", "Performing Arts": "Исполнительское искусство", "Performing Arts and Spectator Sports": "Исполнительское искусство и зрительский спорт", "Periodical Publishing": "Периодическое издание", "Permalink": "Permalink", "Permanent": "Постоянно", "Permissions": "Разрешения", "Persian": "Персидский", "Person on duty": "Дежурный", "Personal": "Личный", "Personal Care Product Manufacturing": "Производство продуктов личной гигиены", "Personal Care Services": "Услуги по персональному уходу", "Personal and Laundry Services": "Персональные и прачечные услуги", "Personnel/Recruitment": "Персонал/набор персонала", "Pet Services": "Услуги для домашних животных", "Pharmaceutical Manufacturing": "Фармацевтическое производство", "Pharmacist": "Фармацевт", "Pharmacy": "Аптека", "Philanthropic Fundraising Services": "Услуги по сбору средств на благотворительные цели", "Philologist": "Филолог", "Phone": "Телефон", "Phone number": "Номер телефона", "Phone number with country code": "Номер телефона с кодом страны", "Phone with country code": "Телефон с кодом страны", "Photo": "Фото", "Photographer": "Фотограф", "Photography": "Фотография", "Photomodel": "Фотомодель", "Physical, Occupational and Speech Therapists": "Физиотерапевты, оккупационные терапевты и логопеды", "Physicians": "Вра<PERSON>и", "Picker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pin": "Кон<PERSON><PERSON><PERSON>т", "Ping user": "Пинг пользователя", "Pipeline Transportation": "Трубопроводный транспорт", "Pipeline overview": "Обзор трубопровода", "Placement": "Размещение", "Plain text block": "Блок обычного текста", "Plasterer": "Шту<PERSON><PERSON><PERSON><PERSON>р", "Plastics Manufacturing": "Производство пластмасс", "Plastics and Rubber Product Manufacturing": "Производство пластмасс и резиновых изделий", "Platforms": "Платформы", "Please add a cNPS field to the form.": "", "Please add the [interview_url] merge tag!": "Пожалуйста, добавьте тег слияния [interview_url]!", "Please add the [invite_url] merge tag to SMS!": "Пожалуйста, добавьте тег слияния [invite_url] в SMS!", "Please add the [invite_url] merge tag!": "Пожалуйста, добавьте тег слияния [invite_url]!", "Please add the [video_call_details] merge tag!": "Пожалуйста, добавьте тег слияния [video_call_details]!", "Please assign a category to every stage": "Пожалуйста, присвойте категорию каждому этапу", "Please choose a form": "Пожалуйста, выберите форму", "Please choose a form:": "Пожалуйста, выберите форму:", "Please choose an image": "Пожалуйста, выберите изображение", "Please click the link below to verify your e-mail and create your Teamdash instance.": "Пожалуйста, перейдите по ссылке ниже, чтобы подтвердить свой e-mail и создать свой экземпляр Teamdash.", "Please confirm": "Пожалуйста, подтвердите", "Please confirm that you want to anonymize {anonCount} candidates.": "Пожалуйста, подтвердите, что вы хотите анонимизировать кандидатов {anonCount}.", "Please confirm that you want to permanently delete {anonCount} candidates.": "Пожалуйста, подтвердите, что вы хотите навсегда удалить кандидатов с сайта {anonCount}.", "Please confirm your password before continuing.": "Пожалуйста, подтвердите свой пароль, прежде чем продолжить.", "Please connect to a WiFi network before starting.": "", "Please contact support at {mail} to enable SMS features": "Чтобы включить функции SMS, обратитесь в службу поддержки по адресу {mail}.", "Please contact support for custom e-mail setups.": "Для получения информации о настройках электронной почты обращайтесь в службу поддержки.", "Please contact your account manager to get started with cNPS reporting.": "Чтобы начать работу с отчетами cNPS, обратитесь к своему менеджеру по работе с клиентами.", "Please enter a valid email address.": "Пожалуйста, введите действительный адрес электронной почты.", "Please enter your references": "Пожалуйста, введите данные ваших рекомендателей", "Please insert your e-mail!": "Пожалуйста, вставьте свой e-mail!", "Please make your decision": "Пожалуйста, примите решение", "Please note that Indeed expects the content of the published job ad to exactly match the details listed on the landing page.": "Обратите внимание, что <PERSON> ожида<PERSON>т, что содержание опубликованного объявления о работе будет точно соответствовать данным, указанным на целевой странице.", "Please note that ss.lv does not allow editing job ads once they have been published.": "", "Please paste iframe URL below or click {here} to choose a form from a list.": "Пожалуйста, вставьте URL-адрес iframe ниже или нажмите {here}, чтобы выбрать форму из списка.", "Please publish landing to export an image.": "Чтобы экспортировать изображение, опубликуйте приземление.", "Please re-connect your integration": "Пожалуйста, повторно подключите вашу интеграцию", "Please resolve all conflicts before continuing.": "Пожалуйста, разрешите все конфликты, прежде чем продолжить.", "Please resolve conflicting data before merging.": "", "Please review the following:": "Пожалуйста, ознакомьтесь со следующим:", "Please save the landing page to add a form.": "Сохраните целевую страницу, чтобы добавить форму.", "Please select a video.": "Пожалуйста, выберите видео.", "Please select an interview time": "Пожалуйста, выберите время собеседования", "Please select at least one candidate to send a message.": "Пожалуйста, выберите хотя бы одного кандидата, чтобы отправить сообщение.", "Please select at least one consent type or give a general consent.": "", "Please select at least one user to send a message.": "", "Please select time slots": "Пожалуйста, выберите временные интервалы", "Please set a password here: [password_url]": "Пожалуйста, установите пароль здесь: [password_url]", "Please update {updateLink} or contact your system administrator.": "Пожал<PERSON>йста, обновите {updateLink} или обратитесь к системному администратору.", "Please upload an attachment or remove the option": "Пожалуйста, загрузите вложение или удалите эту опцию", "Please use SSO!": "Пожалуйста, используйте SSO!", "Please write briefly what you want them to do.": "Пожалуйста, напишите вкратце, что вы хотите, чтобы они сделали.", "Polish": "Польша", "Political Organizations": "Политические организации", "Poor": "Бедный", "Port": "Порт", "Portuguese": "Португальский", "Position": "Позиция", "Position details": "Информация о должности", "Position fit:": "Позиция подходит:", "Position fit: {positionFit}%": "Подгонка по месту: {positionFit}%", "Position info": "Информация о должности", "Position name": "Название должности", "Position name is required!": "Название должности обязательно!", "Position select": "Выбор позиции", "Position title": "Название должности", "Position type": "Тип должности", "Position type (cv.lt)": "Тип должности (cv.lt)", "Possible duplicates": "Возможные дубликаты", "Possible duplicates found": "Возможные дубликаты найдены", "Possibly irrelevant candidate": "Возможно, неактуальный кандидат", "Post": "Пост", "Postal Services": "Почтовые услуги", "Postcode": "Почтовый индекс", "Postman": "Почтальон", "Postmark": "Почтовый штемпель", "Pre-scheduled": "Предварительно запланированный", "Precise location": "Точное местоположение", "Prefer high quality": "Предпочитают высокое качество", "Prefer small file size": "Предпочитайте небольшой размер файлов", "Preferences": "Предпочтения", "Present candidates": "Присутствующие кандидаты", "Present your why-work-here arguments with this block.": "Представьте в этом блоке свои аргументы \"почему - работать - здесь\".", "Preview": "Предварительный просмотр", "Preview font": "", "Preview font when hovering": "Предварительный просмотр шрифта при наведении курсора", "Preview form": "", "Previous": "Предыдущий", "Previous chats": "Предыдущие беседы", "Previous decade": "Предыдущее десятилетие", "Previous file": "Предыдущий файл", "Previous month": "Предыдущий месяц", "Previous week": "Предыдущая неделя", "Previous year": "Предыдущий год", "Previously uploaded": "Ранее загруженные", "Primary Metal Manufacturing": "Производство первичных металлов", "Primary and Secondary Education": "Начальное и среднее образование", "Primary color (hex)": "Основной цвет (hex)", "Primary colour": "Основной цвет", "Primary colour:": "Основной цвет:", "Printing Services": "Полиграфические услуги", "Printing worker": "Работник типографии", "Privacy notice": "", "Privacy policy": "Политика конфиденциальности", "Privacy policy URL": "URL-адрес политики конфиденциальности", "Private integration": "Частная интеграция", "Private job ads": "Частные объявления о работе", "Probation period (days)": "", "Product Management": "Управление продуктами", "Production": "Производство", "Production / Manufactoring": "Производство / Мануфактура", "Professional": "Профессия", "Professional Organizations": "Профессиональные организации", "Professional Services": "Профессиональные услуги", "Programme": "Программа", "Programmer": "Программист", "Project": "Проект", "Project (Teamdash)": "", "Project / stage": "", "Project ID": "", "Project ID (Teamdash)": "", "Project Log": "Журнал проекта", "Project Management": "Управление проектами", "Project action added!": "", "Project actions": "", "Project actions for {position}": "", "Project actions will be automatically created based on the template you select.": "", "Project couldn't be converted to a template.": "", "Project created": "", "Project created!": "Проект создан!", "Project custom fields": "Пользовательские поля проекта", "Project end date": "Дата окончания проекта", "Project entry date": "Дата начала проекта", "Project finished!": "Проект завершен!", "Project log": "Журнал проекта", "Project log for {position}": "<PERSON>у<PERSON><PERSON>л проекта для {position}", "Project manager": "Руководитель проекта", "Project manager (Teamdash)": "", "Project stages": "", "Project start date": "Дата начала проекта", "Project statistics": "Статистика проекта", "Project status is": "", "Project status, deadlines, warranties and other dates can be found here now.": "Статус проекта, сроки, гарантии и другие даты теперь можно найти здесь.", "Project template": "Шаблон проекта", "Project updated": "", "Project users and teams can now be added and removed in one convenient place.": "Теперь пользователей и команды проекта можно добавлять и удалять в одном удобном месте.", "Project visible only for members": "Проект, видимый только для пользователей", "Projects": "Проекты", "Projects count": "Подсчет проектов", "Projects managed by": "Проекты под управлением", "Projects using {templateName} as template": "Проекты, использующие {templateName} в качестве шаблона", "Promotional content": "Рекламный контент", "Prompt for a reason when a candidate drops out": "Уточните причину, когда кандидат выбывает из игры", "Property Services": "Услуги по недвижимости", "Provider": "Поставщик", "Psychologist": "Психолог", "Public Assistance Programs": "Программы государственной помощи", "Public Health": "Общественное здравоохранение", "Public Policy Offices": "Офисы государственной политики", "Public Relations": "Связи с общественностью", "Public Relations and Communications Services": "Службы по связям с общественностью и коммуникациям", "Public Safety": "Общественная безопасность", "Public Sector": "Государственный сектор", "Public administration/civil service": "Государственное управление/гражданская служба", "Public domain": "Общественное достояние", "Public servant": "Государственный служащий", "Publish": "Опубликовать", "Publish a new job ad": "Опубликуйте новое объявление о работе", "Publish new": "Опубликовать новый", "Publish the landing page to download as image": "Опубликуйте целевую страницу для загрузки в виде изображения", "Publish to external channels": "Публикация на внешних каналах", "Publish {landingName}": "Опубликовать {landingName}", "Published as": "Опубликовано как", "Published job ads": "Опубликованные объявления о работе", "Published on": "Опубликовано на", "Published position": "Опубликованная позиция", "Published until": "Опубликовано до", "Publishing to this combination of channels is currently impossible.": "Публикация по такой комбинации каналов в настоящее время невозможна.", "Purchasing": "Закупки", "Qualification type": "Тип квалификации", "Qualifications": "Квалификация", "Quality assurance": "Обеспечение качества", "QualityAssurance": "Обеспечение качества", "Quechua": "Кечуа", "Question": "Вопрос", "Question {questionNumber}": "Вопрос {questionNumber}", "Questions": "Вопросы", "Quick call": "Быстрый вызов", "Quote": "Цитировать", "Quote author photo": "Цитата автор фото", "Quotes": "Цитаты", "Racetracks": "Ипподромы", "Radio and Television Broadcasting": "Радио- и телевещание", "Radio engineer": "Радиои<PERSON><PERSON><PERSON><PERSON><PERSON>р", "Rail Transportation": "Железнодорожные перевозки", "Railroad Equipment Manufacturing": "Производство железнодорожного оборудования", "Ranching": "Ранчо", "Ranching and Fisheries": "Ранчо и рыболовство", "Range": "Диа<PERSON>азон", "Rate the candidate:": "", "Rating": "<PERSON>ей<PERSON>инг", "Rating saved.": "", "Reach": "Достичь", "Read activities": "Читательская деятельность", "Read audit logs": "Чтение журналов аудита", "Read candidates": "Кандидаты на чтение", "Read database dumps": "Чтение дампов баз данных", "Read feeds": "Читаемые материалы", "Read landing statistics": "Ознакомьтесь со статистикой посадок", "Read more about AI-generated transcripts and summaries {asyncLink}.": "Подробнее о транскриптах и резюме, созданных искусственным интеллектом {asyncLink}.", "Read more about asynchronous video interviews {asyncLink}.": "Подробнее об асинхронных видеоинтервью {asyncLink}.", "Read more about cNPS": "Подробнее о cNPS", "Read more about cNPS.": "", "Read more about internal forms": "", "Read project status reports": "Читайте отчеты о состоянии проекта", "Read projects": "", "Read settings": "Параметры чтения", "Reading candidate profile...": "Чтение профиля кандидата...", "Ready! Set! Hire!": "Готовы! Начать! Нанимайте!", "Real Estate": "Недвижимость", "Real Estate and Equipment Rental Services": "Услуги по аренде недвижимости и оборудования", "Real estate": "Недвижимость", "Real estate agent": "Агент по недвижимости", "Reason": "Причина", "Reason for candidate to drop out of applying": "Причина, по которой кандидат отказался от участия в конкурсе", "Reason for company to reject candidate": "Причина, по которой компания отказала кандидату", "Reason for consent revocation": "", "Reason for hiring:": "Причина найма:", "Reassign to...": "", "Received SMS": "Полученное SMS", "Received message via integration": "Получение сообщения через интеграцию", "Receptionist, doorman": "Секретарь, швейцар", "Record": "Запись", "Record activity:": "Записывайте активность:", "Record new activity": "Запись новой активности", "Record new video": "Запись нового видео", "Record response": "", "Record video": "Запись видео", "Recorded call placeholder": "Запись звонка", "Records all candidate data accesses and modifications. The logs are retained according to your internal policies.": "Регистрирует все обращения к данным кандидата и их изменения. Журналы хранятся в соответствии с вашими внутренними правилами.", "Recreate project list dropdowns": "Восстановление выпадающих списков проектов", "Recreational Facilities": "Сооружения для отдыха", "Recruiter": "Рекрутер", "Recruiter contacts": "Контакты рекрутера", "Recruiters": "Рекрутеры", "Recruitment": "Рекрутинг", "Recruitment Performance": "Эффективность найма", "Recruitment performance": "Результаты набора персонала", "Recruitment software invite": "Приглашение программного обеспечения для рекрутинга", "Redirect URL": "URL-адрес перенаправления", "Redo": "Redo", "Reference Checks": "Проверки рекомендаций", "Reference form": "", "Reference forms": "Формы рекомендаций", "Reference request sent": "Запрос на рекомендацию отправлен", "Reference submitted on {submissionTime}": "Рекомендация представлена {submissionTime}", "Reference submitted on {submissionTime} by {submissionUser}": "Рекомендация представлена {submissionTime} {submissionUser}", "Reference type": "Тип рекомендации", "References": "Рекомендации", "References pending first": "Сначала ожидающие рекомендации", "References submitted first": "Сначала отправленные рекомендации", "Referent": "Референт", "Referral budget (€)": "", "Referral fee percentage": "", "Refresh this page.": "Обновите эту страницу.", "Regards": "С уважением,", "Regenerate": "", "Regenerate summary": "Краткое описание регенерации", "Region": "Регион", "Region (CV-Library)": "Регион (CV-Library)", "Register": "Регистрация", "Register a new membership": "Зарегистрируйте новое членство", "Regular user": "Постоянный пользователь", "Reject": "Отклонить", "Rejected": "Отклонено", "Rejected at": "Отклонено в", "Relax!": "Расслабьтесь!", "Religious Institutions": "Религиозные учреждения", "Reload": "", "Relocate from EU": "", "Relocate from eu": "Переселиться из ЕС", "Remember Me": "Помни меня", "Remember, that the candidate will not receive notification of new slots, so you may wish to send them a message under their profile.": "Помните, что кандидат не будет получать уведомления о новых вакансиях, поэтому вы можете отправить ему сообщение под его профилем.", "Reminder: Job Ad Builder Tool by Teamdash": "Напоминание: Инструмент для создания объявлений о работе от Teamdash", "Remote": "Удаленный", "Remote links": "Удаленные ссылки", "Remote project": "Удаленный проект", "Remote project ID:": "Идентификатор удаленного проекта:", "Remove": "Удалить", "Remove candidate": "Убрать кандидата", "Remove filter": "Удалить фильтр", "Remove from favourites": "Удалить из избранного", "Remove from project": "Удалить из проекта", "Remove image": "Удалить изображение", "Remove import": "Удалить импорт", "Remove link": "Удалить ссылку", "Remove quote": "Удалить цитату", "Remove slot": "Извлеките слот", "Remove this candidate from the event.": "Снимите этого кандидата с мероприятия.", "Removed dropout reason": "Удалена причина отсева", "Render failed at": "Рендер не удался на", "Renew token": "Обновить токен", "Renewable Energy Semiconductor Manufacturing": "Возобновляемые источники энергии Производство полупроводников", "Renewal link is valid for": "Ссылка на продление действительна для", "Renewal text": "", "Repair and Maintenance": "Ремонт и обслуживание", "Report generation started": "", "Required field": "Обязательное поле", "Required languages": "Необходимые языки", "Requirements": "Требования", "Requirements section title": "", "Requisition Attachment": "Приложение к заявке", "Requisition custom fields": "Пользовательские поля заявки", "Requisition info field template": "Шаблон информационного поля заявки", "Requisitions": "Заявки", "Reschedule": "Перенос сроков", "Reschedule time slot": "Перенос временного интервала", "Research": "Исследование", "Research Services": "Исследовательские услуги", "Resend invite": "Повторная отправка приглашения", "Reset": "Сброс", "Reset Password": "Сброс пароля", "Reset Password Notification": "Уведомление о сбросе пароля", "Residential Building Construction": "Строительство жилых домов", "Resolved task": "Решенная задача", "Responded": "Ответил", "Response": "Ответ", "Response rate": "Доля ответов", "Response time limit": "Ограничение времени отклика", "Response time limit: {timeLimit}": "Ограничение времени ответа: {timeLimit}", "Responses": "Ответы", "Restaurants": "Рестораны", "Restore": "Восстановить", "Retail": "Розничная торговля", "Retail Apparel and Fashion": "Розничная торговля одеждой и модой", "Retail Appliances, Electrical, and Electronic Equipment": "Розничная торговля бытовой техникой, электрическим и электронным оборудованием", "Retail Art Dealers": "Розничные арт-диллеры", "Retail Art Supplies": "Розничная торговля художественными принадлежностями", "Retail Books and Printed News": "Розничная торговля книгами и печатными изданиями", "Retail Building Materials and Garden Equipment": "Розничная торговля строительными материалами и садовым инвентарем", "Retail Florists": "Розничные флористы", "Retail Furniture and Home Furnishings": "Розничная торговля мебелью и товарами для дома", "Retail Gasoline": "Розничная торговля бензином", "Retail Groceries": "Розничная торговля продуктами питания", "Retail Health and Personal Care Products": "Розничная торговля товарами для здоровья и личной гигиены", "Retail Luxury Goods and Jewelry": "Розничная торговля предметами роскоши и ювелирными изделиями", "Retail Motor Vehicles": "Розничная торговля автотранспортными средствами", "Retail Musical Instruments": "Розничная торговля музыкальными инструментами", "Retail Office Equipment": "Розничное офисное оборудование", "Retail Office Supplies and Gifts": "Розничная торговля офисными принадлежностями и подарками", "Retail Recyclable Materials & Used Merchandise": "Розничная торговля вторсырьем и подержанными товарами", "Retail/Purchasing": "Розничная торговля/закупки", "Retrieved full profile": "", "Retry": "Повторная попытка", "Retype password": "Повторите пароль", "Reupholstery and Furniture Repair": "Обивка и ремонт мебели", "Review": "Обзор", "Review approvals": "Обзор утверждений", "Review integrations": "Обзор интеграций", "Review now": "Обзор сейчас", "Revoke": "", "Revoke consent": "", "Revoked by": "", "Right": "Справа", "Right button link override": "Переопределение ссылки на правую кнопку", "Role": "Роль", "Role for new users": "Роль для новых пользователей", "Romanian": "Румыния", "Romansh": "<PERSON>о<PERSON><PERSON><PERSON><PERSON>", "Roofer": "Кровельщик", "Room address": "Адрес номера", "Room address:": "", "Room assignment successful!": "Распределение по комнатам прошло успешно!", "Rows": "Строки", "Rubber Products Manufacturing": "Производство резиновых изделий", "Rules": "Правила", "Run an AI-assisted social media campaign for attracting passive talent.": "Запустите кампанию в социальных сетях с помощью искусственного интеллекта для привлечения пассивных талантов.", "Run check": "Проверка выполнения", "Run now": "Беги сейчас", "Run related imports": "Выполните связанный импорт", "Rundi": "Рунди", "Russian": "Русский", "SES": "SES", "SMS": "SMS", "SMS body": "SMS-телефон", "SMS confirmation": "", "SMS confirmation message": "", "SMS message": "SMS-сообщение", "SMS sent!": "SMS отправлено!", "SSO": "SSO", "SSO is enabled!": "SSO включен!", "Sailor": "Матр<PERSON>с", "Salary currency": "Валюта заработной платы", "Salary from": "", "Salary from (gross)": "Заработная плата (брутто)", "Salary info": "Информация о зарплате", "Salary period": "Период выплаты заработной платы", "Salary range": "Диапазон заработной платы", "Salary rate": "Ставка заработной платы", "Salary to": "", "Salary to (gross)": "Зарплата до (брутто)", "Sales": "Продажи", "Sales Assistants": "Помощники по продажам", "Samoan": "Самоанский", "Sample text": "Образец текста", "Sango": "Санго", "Sanitary technician": "Санитарный техник", "Sanskrit": "Санскрит", "Sardinian": "Сардиния", "Satellite Telecommunications": "Спутниковые телекоммуникации", "Saturday": "Суббота", "Save": "Сохранить", "Save and close": "Сохранить и закрыть", "Save and next": "Сохранить и перейти к следующему", "Save as new": "Сохранить как новый", "Save as template": "Сохранить как шаблон", "Save candidate and add to current project": "Сохраните кандидата и добавьте в текущий проект", "Save changes": "Сохранить изменения", "Save or update active filters for later use.": "Сохраните или обновите активные фильтры для последующего использования.", "Save project action": "", "Save stage action": "Сохранить действие этапа", "Saved": "", "Saved filters": "Сохраненные фильтры", "Saved!": "Спасен!", "Saved.": "Спасен.", "Saving draft...": "Сохранение черновика...", "Saving...": "Экономия...", "Savings Institutions": "Сберегательные учреждения", "Sawyer": "Сойер", "Scandinavia": "Скандинавия", "Schedule a Consultation": "Запланируйте консультацию", "Schedule interview": "Запланируйте интервью", "Schedule interviews": "Планируйте интервью", "Scheduled": "Запланировано", "Scheduled meetings:": "Запланированные встречи:", "Scheduling type": "Тип планирования", "School and Employee Bus Services": "Школьные и служебные автобусы", "Science": "Наука", "Scientific": "Научный", "Score": "Оценка", "Scorecard": "Оценочный лист", "Scorecard comments": "Комментарии к оценочному листу", "Scorecard summary": "Резюме оценочного листа", "Scorecards": "Оценочные листы", "Scored candidate": "Кандидат, получивший баллы", "Screening questions": "Вопросы для скрининга", "Scroll to zoom in or out": "Прокрутка для увеличения или уменьшения масштаба", "Seafood Product Manufacturing": "Производство морепродуктов", "Seaman": "Матр<PERSON>с", "Seamstress": "Швея", "Search": "Поиск", "Search by image name...": "Поиск по названию изображения...", "Search by name, tags, file contents, etc...": "Поиск по имени, тегам, содержимому файла и т.д..", "Search by video name...": "Поиск по названию видео...", "Search candidate": "", "Search here.": "Искать здесь.", "Search user": "", "Search...": "Поиск...", "Seasonal": "Сезонная", "Secondary Colour": "Вторичный цвет", "Secondary Colour:": "Вторичный цвет:", "Secretarial Schools": "Школы секретарей", "Secretary": "Секретарь", "Section with links to your social media pages": "Раздел со ссылками на ваши страницы в социальных сетях", "Section with recruiter contacts": "Раздел с контактами рекрутеров", "Sector type": "Тип сектора", "Sectors (profession.hu)": "Секторы (profession.hu)", "Securities and Commodity Exchanges": "Ценные бумаги и товарные биржи", "Security": "Безопасность", "Security & compliance": "Безопасность и соответствие нормативным требованиям", "Security / Rescue / Defence": "Безопасность / Спасательные работы / Оборона", "Security Guards and Patrol Services": "Охранники и патрульные", "Security Systems Services": "Услуги систем безопасности", "Security and Investigations": "Безопасность и расследования", "Security guard": "Охранник", "Security services": "Услуги безопасности", "See more...": "Подробнее...", "See you soon!": "До скорой встречи!", "Select": "Выберите", "Select a Block": "Выберите блок", "Select a category": "Выберите категорию", "Select a font": "Выберите шрифт", "Select a reason...": "", "Select a reference form": "Выберите форму рекомендации", "Select a room": "Выберите комнату", "Select a template": "Выберите шаблон", "Select a thread to view messages.": "", "Select a time": "Выберите время", "Select all": "Выбрать все", "Select all {count} candidates": "Выберите всех кандидатов {count} ", "Select an action from the left to add to the stage.": "Выберите действие слева, чтобы добавить его на этап.", "Select an option from the left to start your new project.": "Выберите вариант слева, чтобы начать новый проект.", "Select another profile to merge with {candidateName}": "Выберите другой профиль для объединения {candidateName}", "Select candidates to merge": "", "Select interview": "Выберите интервью", "Select interview to see preview.": "Выберите интервью, чтобы посмотреть предварительный просмотр.", "Select none": "", "Select users to add": "Выберите пользователей для добавления", "Select video": "Выберите видео", "Selected candidate is already in the chosen project. | Selected candidates are already in the chosen project.": "Выбранный кандидат уже участвует в выбранном проекте. | Выбранные кандидаты уже участвуют в выбранном проекте.", "Selected date": "Выбранная дата", "Selected fields": "", "Selected time": "Выбранное время", "Self-Managed Backups": "Самоуправляемые резервные копии", "Seller": "Продавец", "Semiconductor Manufacturing": "Производство полупроводников", "Send": "Отправить", "Send Password Reset Link": "Отправить ссылку для сброса пароля", "Send SMS": "Отправить SMS", "Send a message to project members": "", "Send a message to start the conversation.": "", "Send a message to the candidate": "Отправить сообщение кандидату", "Send a message to {candidateName}": "Отправить сообщение на {candidateName}", "Send a message to {count} candidates": "Отправить сообщение на {count} кандидатам", "Send a message to {count} users": "", "Send a message to {userName}": "", "Send a reference check request to the candidate's referees": "Отправьте запрос на проверку рекомендаций рекомендателям кандидата", "Send a video interview to the candidate": "Отправьте кандидату видеоинтервью", "Send a video message to the candidate": "Отправьте видеосообщение кандидату", "Send a video message to {candidateName}": "Отправьте видеосообщение на {candidateName}", "Send a video message to {count} candidates": "Отправьте видеообращение кандидатам на {count} ", "Send an SMS to {candidateName}": "Отправьте SMS на {candidateName}", "Send an SMS to {count} candidates": "Отправьте SMS на {count} кандидатам", "Send an interview scheduling request to the candidate": "Отправьте кандидату запрос на назначение собеседования", "Send applicants to": "Отправляйте кандидатов по адресу", "Send as": "Отправить как", "Send candidates": "Отправить кандидатов", "Send consent renewal messages": "Отправляйте сообщения о продлении согласия", "Send consent renewals": "Отправляйте продления согласий", "Send homework assignments, screening questions, consent requests or ask for any additional data from candidates.": "", "Send interview": "Отправить интервью", "Send invitations": "Отправьте приглашения", "Send invites": "Отправить приглашения", "Send message": "Отправить сообщение", "Send out cNPS surveys and ask candidates for feedback.": "", "Send out message at": "Отправьте сообщение по адресу", "Send out the invite link as an SMS too and be sure the invite is seen. Text messages longer than 160 characters are automatically split into segments and then re-assembled when they are received. This allows you to send long SMS messages, but this increases your per-message cost because SMS is billed per segment.": "Отправьте ссылку на приглашение в виде SMS и убедитесь, что приглашение увидят. Текстовые сообщения длиной более 160 символов автоматически разбиваются на сегменты, а затем снова собираются при получении. Это позволяет отправлять длинные SMS-сообщения, но увеличивает стоимость каждого сообщения, поскольку SMS тарифицируется по сегментам.", "Send tentative scheduler time slots to interviewers": "Отправьте интервьюерам предварительные расписания.", "Send to :recipient": "Отправить :получателю", "Send to BambooHR ATS": "Отправить в BambooHR ATS", "Send video interview": "Отправить видеоинтервью", "Send video interview invites": "Отправляйте приглашения на видеоинтервью", "Send video message": "Отправить видеосообщение", "Send webhook": "", "Sending report": "Отправка отчета", "Sending your message...": "", "Sending...": "Посылаю...", "Sent": "Отправлено", "Sent SMS": "Отправленные SMS", "Sent at": "Отправлено в", "Sent message": "Отправленное сообщение", "Sent to HRIS": "", "September": "Сентябрь", "Serbian": "Сербия", "Server Error": "Ошибка сервера", "Service account": "Сервисный счет", "Service industry": "Сфера услуг", "Service user": "", "Service worker": "Работник сферы обслуживания", "Services": "Услуги", "Services for the Elderly and Disabled": "Услуги для пожилых людей и инвалидов", "Set as new project manager": "Назначьте нового руководителя проекта", "Set automatic stage action": "Установите автоматическое действие этапа", "Set dates": "", "Set type to {type}": "Установите тип на {type}", "Set up Azure AD SSO": "Настройка Azure AD SSO", "Set up on": "Установить на", "Settings": "Настройки", "Setup": "Настройка", "Setup instructions": "Инструкции по настройке", "Share": "Поделиться", "Share on Facebook": "Поделиться на Facebook", "Share on LinkedIn": "Поделиться на LinkedIn", "Share to": "Поделиться с", "Share with": "Поделитесь с", "Share with Teamdash users?": "", "Share with users": "", "Shared by": "", "Shared candidate": "Об<PERSON>ий кандидат", "Shared candidates": "", "Shared mailbox address": "Адрес общего почтового ящика", "Shared with": "", "Sharing preview photo": "Совместное использование фотографий предварительного просмотра", "Sheet Music Publishing": "Издательство нот", "Shift-click on a team and drag to another team to connect them with a directed edge": "Нажмите Shift на команде и перетащите ее на другую команду, чтобы соединить их направленным краем", "Shift-click on team to change its label": "Щелкните по команде, чтобы изменить ее название", "Shift-click on whitespace to create a team": "Нажмите на пробел, чтобы создать команду", "Shipbuilding": "Судостроение", "Shoemaker": "Сапожник", "Shona": "<PERSON><PERSON><PERSON>", "Short message": "Краткое сообщение", "Short name": "", "Short name for the consent type. Max 4 characters. We suggest an emoji.": "", "Show": "Показать", "Show advanced filters": "Показать расширенные фильтры", "Show all personal data": "", "Show all projects to limited users": "", "Show archived": "Показать архивные", "Show candidate initials in blind hiring mode": "", "Show candidate journeys": "Показать путешествия кандидатов", "Show candidates with new activities": "Покажите кандидатам новые виды деятельности", "Show comments indicator": "Показать индикатор комментариев", "Show email": "Показать электронную почту", "Show entire image": "Показать все изображение", "Show filter": "Показать фильтр", "Show icon for location": "Показать значок местоположения", "Show if candidate is active in other projects": "Покажите, активен ли кандидат в других проектах", "Show images in grid": "Показать изображения в сетке", "Show inactive users": "", "Show invites together": "Показать приглашения вместе", "Show location": "Показать местоположение", "Show me the candidates": "Покажите мне кандидатов", "Show messages indicator": "Индикатор сообщений", "Show non-public project logs to limited users": "Показывать непубличные журналы проекта ограниченным пользователям", "Show on candidate card": "Показать на карточке кандидата", "Show on job ad": "Показать в объявлении о работе", "Show only my tasks": "Показать только мои задачи", "Show other active candidacies": "Показать другие активные кандидатуры", "Show pending/answered video interview indicator": "Показать индикатор ожидающих/отвеченных видеоинтервью", "Show pending/scheduled interview indicator": "Показать индикатор ожидающих/запланированных интервью", "Show pending/submitted references indicator": "Показать индикатор ожидающих/предоставленных рекомендаций", "Show personal data alongside AI-generated summary": "", "Show phone": "Показать телефон", "Show source": "Показать источник", "Show stage categories": "Показать категории этапов", "Show talent pool": "Показать кадровый резерв", "Show tasks from all projects": "", "Show weekends": "Показать выходные дни", "Shows how candidates are divided across stage categories at specific moments in time": "Показывает, как кандидаты распределяются по категориям этапа в определенные моменты времени", "Shuttles and Special Needs Transportation Services": "Шаттлы и услуги по перевозке людей с особыми потребностями", "Sick-nurse": "Медсестра", "Sightseeing Transportation": "Экскурсионный транспорт", "Sign in": "Войти", "Sign into your Indeed account": "Войдите в свою учетную запись Indeed", "Signature accent color": "Фирменный акцентный цвет", "Signature additional text": "Дополнительный текст подписи", "Signature preview": "Предварительный просмотр подписей", "Silkscreen operator": "Оператор шелкографии", "Similarity to selected": "Сходство с выбранным", "Simplify language": "Упростить язык", "Since this is an SMS, keep it short.": "", "Sindhi": "<PERSON>ин<PERSON><PERSON>и", "Single Sign-On": "Единый вход", "Single stage": "Одноступенчатый", "Sinhala, Sinhalese": "Сингальский, сингальский", "Size": "Размер", "Skiing Facilities": "Оборудование для катания на лыжах", "Skilled Labor": "Квалифицированный труд", "Skilled worker": "Квалифицированный рабочий", "Skills": "Навыки", "Skip": "Скип", "Skip this month": "Пропустите этот месяц", "Slots": "Слоты", "Slovak": "Словакия", "Slovenian": "Словения", "Slug": "Слизняк", "Sms sent!": "Смс отправлено!", "Soap and Cleaning Product Manufacturing": "Производство мыла и чистящих средств", "Social Care": "Социальный уход", "Social Networking Platforms": "Платформы социальных сетей", "Social media": "Социальные сети", "Social media sharing image": "Изображение для совместного использования в социальных сетях", "Social worker": "Социальный работник", "Software Development": "Разработка программного обеспечения", "Solar Electric Power Generation": "Производство солнечной электроэнергии", "Somali": "Сомали", "Some candidates were not be copied.": "Некоторым кандидатам не удавалось скопировать текст.", "Some inactive users are selected. Deselect them to hide inactive users.": "", "Some invites can't be delivered via selected channels.": "", "Someone": "", "Someone applied via email?": "Кто-то подал заявку по электронной почте?", "Someone has booked this time already": "Кто-то уже забронировал это время.", "Something went wrong!": "Что-то пошло не так!", "Something went wrong! Please try again.": "Что-то пошло не так! Пожалуйста, попробуйте еще раз.", "Sort by": "Сортировать по", "Sort by:": "Сортировать по:", "Sort candidates": "Сортировка кандидатов", "Sort manually": "Сортировка вручную", "Sorter": "Сортировщик", "Sotho, Southern": "Сото, южный", "Sound Recording": "Звукозапись", "Source": "Источник", "Source tags": "Теги источник", "South Ndebele": "Южный Ндебеле", "Space Research and Technology": "Космические исследования и технологии", "Spanish": "Испанский", "Spanish, Castilian": "Испанский, кастильский", "Specialist": "Специалист", "Specialty Trade Contractors": "Специальные торговые подрядчики", "Specific height": "Удельная высота", "Specific slots": "Конкретные слоты", "Spectator Sports": "Зрительские виды спорта", "Sporting Goods Manufacturing": "Производство спортивных товаров", "Sports Teams and Clubs": "Спортивные команды и клубы", "Sports and Recreation Instruction": "Инструктаж по спорту и рекреации", "Spring and Wire Product Manufacturing": "Производство пружин и проволочных изделий", "Staffing and Recruiting": "Подбор персонала и рекрутинг", "Stage": "Эта<PERSON>", "Stage (Teamdash)": "", "Stage ID": "Идентификатор этапа", "Stage ID (Teamdash)": "", "Stage action added!": "Этапное действие добавлено!", "Stage category": "Категория этапа", "Stage deleted successfully.": "Этап успешно удалён.", "Stage header dropdown menu": "Выпадающее меню заголовка этапа", "Stage hidden from limited users": "Этап, скрытая от ограниченных пользователей", "Stage name": "Название этапа", "Stage visibility settings": "Настройки видимости этапа", "Stages": "Эта<PERSON>ы", "Stages & actions": "Этапы и действия", "Stages and actions will be automatically created based on the template you select.": "Этапы и действия будут автоматически созданы на основе выбранного вами шаблона.", "Standard fields": "", "Start": "Начало", "Start SSO setup": "Начните настройку SSO", "Start a new AI chat with this interview": "Начните новый чат ИИ с этого интервью", "Start a project": "Начать проект", "Start date": "Дата начала", "Start month": "Месяц начала", "Start tracking your candidate experience using cNPS surveys.": "Начните отслеживать опыт кандидатов с помощью опросов cNPS.", "Start year": "Год начала", "Started: {date}": "Начало: {date}", "State": "Государство", "Static text": "Статический текст", "Statistics": "Статистика", "Statistics for {landingName}": "Статистика для {landingName}", "Status": "Статус", "Status: Enabled": "Статус: Включено", "Steam and Air-Conditioning Supply": "Поставки пара и кондиционеров", "Steward": "Стю<PERSON><PERSON>д", "Stomatologist": "Стоматолог", "Store Management": "Управление магазином", "Store the cleaned transcript segments. Should be called with an array in which each object represents a cleaned-up transcript segment.": "Храните очищенные сегменты транскрипта. Должен вызываться с массивом, в котором каждый объект представляет очищенный сегмент транскрипта.", "Store the interview summary in a structured format. Should be called with an array in which each element is a string containing a summarization sentence.": "Храните резюме интервью в структурированном формате. Вызывается с массивом, в котором каждый элемент является строкой, содержащей предложение резюме.", "Storekeeper": "Кладовщик", "Stovemaker": "Печник", "Strategic Management Services": "Услуги по стратегическому управлению", "Strategy / Planning": "Стратегия / планирование", "Strikethrough": "Зачеркивание", "Stripper": "Стриппер", "Structured Ad Image": "Структурированное рекламное изображение", "Structured job ads": "Структурированные объявления о работе", "Student": "Студент", "Styling": "Стай<PERSON><PERSON>нг", "Subdivision of Land": "Разделение земли", "Subject": "Тема", "Submission": "", "Submissions": "Материалы", "Submissions for :form": "Представления для :формы", "Submissions: :form": "", "Submit": "Отправить", "Submit button label": "Ярлык кнопки отправки", "Submit reference": "Отправить рекомендацию", "Submit reference from {referenceName}": "Предоставьте рекомендацию от {referenceName}", "Submitted a form": "Отправил форму", "Submitted for {positionName} on {uploadTime}": "Отправлено на {positionName} на {uploadTime}", "Submitted referees": "Представленные рекомендатели", "Submitted reference": "Представленная рекомендация", "Submitting": "Представление", "Success text": "Текст успеха", "Success!": "Успех!", "Sugar and Confectionery Product Manufacturing": "Производство сахара и кондитерских изделий", "Summarization failed": "Подведение итогов не удалось", "Sundanese": "Сунданцы", "Sunday": "Воскресенье", "Supermarket/Food": "Супермаркет/Продукты питания", "Supervisor": "Супервайзер", "Supply Chain": "Цепочка поставок", "Supports merge tags.": "Поддерживает теги слияния.", "Survey": "", "Survey form": "", "Survey response": "", "Surveyor": "Геодезист", "Surveys sent": "Отправленные опросы", "Swahili": "Суахили", "Swati": "Свати", "Swedish": "Шведский", "Switch off asking for dropout reasons": "Выключить, спрашивая о причинах отсева", "Switch to columns view": "Переключение в режим просмотра колонок", "Switch to table view": "Переключение в режим просмотра таблицы", "TB": "TB", "TTF can only be calculated if your filters include finished projects.": "Расчет TTF возможен только в том случае, если в ваши фильтры включены готовые проекты.", "Table": "Таблица", "Tag": "Тег", "Tag name": "Название тега", "Tagalog": "Тагальский язык", "Tags": "Теги", "Tags to add": "Теги для добавления", "Tahitian": "Таитянский", "Tailor": "Портной", "Tajik": "Тад<PERSON><PERSON><PERSON>истан", "Take fields from": "Возьмите поля из", "Talenme.com": "", "Talent Pool": "Кадровый резерв", "Talent.com Job category": "Talent.com Категория вакансий", "Tamil": "Тамил", "Target stage": "Целевой этап", "Target tag": "Целевая метка", "Task": "Задание", "Task in project {project}": "", "Task title": "Название задачи", "Task title:": "Название задания:", "Tasks": "Зада<PERSON>и", "Tasks with deadline today:": "Задачи со сроком выполнения сегодня:", "Tatar": "<PERSON>а<PERSON><PERSON><PERSON>", "Taxi and Limousine Services": "Такси и лимузины", "Teacher": "Учитель", "Team": "Команда", "Team settings:": "Настройки команды:", "Team-Based Access Controls": "Контроль доступа на основе команд", "Team-based access controls make permission handling easier and facilitate managing multiple departments or brands under one account.": "Управление доступом на основе команд упрощает обработку разрешений и облегчает управление несколькими отделами или брендами под одной учетной записью.", "Teamdash Feed": "Feed <PERSON>", "Teamdash Report": "Отчет Teamdash", "Teamdash Social Media Tool distributes your job ad in social media. This helps you attract passive talent and reach a more diverse audience than just job board visitors.": "Teamdash Social Media Tool распространяет ваше объявление о работе в социальных сетях. Это поможет вам привлечь пассивных талантов и охватить более разнообразную аудиторию, чем просто посетители доски объявлений.", "Teamdash Team": "Команда Teamdash", "Teamdash is an easy-to-use recruitment software, a place you can manage all your recruitment projects with your team members.": "Teamdash - это простое в использовании программное обеспечение для подбора персонала, с помощью которого вы можете управлять всеми проектами по подбору персонала вместе с членами вашей команды.", "Teamdash will anonymize {candidateCount} {anonymizeDiff}.": "Teamdash анонимизирует {candidateCount} {anonymizeDiff} .", "Teamdash will ask data processing consent renewals {candidateCount} {renewalDiff}.": "Teamdash будет запрашивать продление согласия на обработку данных {candidateCount} {renewalDiff} .", "Teamdash's GDPR automation helps you acquire, manage, document and monitor all your candidates' data processing consents. It works like this:": "Автоматизация GDPR в Teamdash поможет вам получить, управлять, документировать и отслеживать все согласия ваших кандидатов на обработку данных. Это работает следующим образом:", "Teams": "Команды", "Teams admins can modify the teams hierarchy and assign any user to any team.": "Администраторы команд могут изменять иерархию команд и назначать любого пользователя в любую команду.", "Teams saved!": "Команды спасены!", "Technical / Engineering": "Технические / инженерные", "Technical and Vocational Training": "Техническое и профессиональное обучение", "Technical support": "", "Technician": "Техник", "Technologist": "Технолог", "Technology": "Технология", "Technology and Information": "Технология и информация", "Technology, Information and Internet": "Технологии, информация и Интернет", "Technology, Information and Media": "Технологии, информация и медиа", "Telecommunications": "Телекоммуникации", "Telecommunications Carriers": "Операторы связи", "Telecoms": "Телекоммуникации", "Telephone Call Centers": "Телефонные колл-центры", "Telugu": "Телугу", "Template": "Шабл<PERSON>н", "Template created": "", "Template created!": "Шаблон создан!", "Template display name": "", "Template log": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Template name": "Имя шаблона", "Template name should be descriptive eg. \"Interview invite\". This is not visible to the candidate.": "Название шаблона должно быть описательным, например \"Приглашение на собеседование\". Это не будет видно кандидату.", "Template name should be descriptive eg. \"Rejection letter\". This is not the subject.": "Название шаблона должно быть описательным, например \"Письмо с отказом\". Это не тема.", "Template name should be descriptive eg. \"Request for feedback\". This is not the subject.": "", "Template owner": "Влад<PERSON><PERSON><PERSON><PERSON> шаблона", "Template visible for": "Шаблон, видимый для", "Templates": "Шаблоны", "Temporary": "Временные", "Temporary Help Services": "Услуги временной помощи", "Tentative": "Предварительный", "Terms": "Условия", "Terms of use": "Условия использования", "Test connection": "Тестовое соединение", "Tester": "Тестер", "Text": "Текст", "Text color": "Цвет текста", "Text color (hex)": "Цвет текста (hex)", "Text of transcribed speech": "", "Text shadow": "Тень от текста", "Text signature override": "Переопределение текстовой подписи", "Text with background": "Текст с фоном", "Textarea": "Textarea", "Textile Manufacturing": "Текстильное производство", "Thai": "Тайский", "Thank you for reporting a problem. We will use your feedback to improve the location search functionality.": "Благодарим вас за сообщение о проблеме. Мы используем ваш отзыв для улучшения функциональности поиска местоположения.", "Thank you for using Teamdash!": "Спасибо за использование Teamdash!", "Thank you for your response!": "Спасибо за ваш ответ!", "Thank you!": "Спасибо!", "Thank you! We have saved your preferences!": "Спасибо! Мы сохранили ваши предпочтения!", "Thank you! Your details have been sent!": "Спасибо! Ваши данные отправлены!", "Thank you! Your interview is scheduled at": "Спасибо! Ваше собеседование назначено на", "Thank you! Your references have been saved.": "Спасибо! Ваши рекомендации сохранены.", "The SMS body supports these merge tags: [recipient_full_name], [recipient_first_name], [recipient_last_name]. If sending from project context, [position_name] is also available. <br> Since this is an SMS, keep it short.": "Тело SMS поддерживает эти теги слияния: [recipient_full_name], [recipient_first_name], [recipient_last_name]. При отправке из контекста проекта также доступен [имя_позиции]. <br> Поскольку это SMS, будьте кратки.", "The URL must begin with HTTPS.": "", "The action will be automatically executed after receiving a video interview response. It will be delayed for selected time.": "Действие будет автоматически выполнено после получения ответа на видеоинтервью. Оно будет отложено на выбранное время.", "The action will be automatically executed after the project status has changed.": "", "The action will be automatically executed when the candidate has been in stage longer than the selected time.": "Действие будет выполнено автоматически, если кандидат находится на этапе дольше выбранного времени.", "The action will be executed only when you click the button that appears in stage header.": "Действие будет выполнено только при нажатии на кнопку, которая появится в заголовке этапа.", "The array must contain between 3 and 10 sentences.": "Массив должен содержать от 3 до 10 предложений.", "The body colour may be hard to read on the background colour. Try increasing the contrast.": "Цвет корпуса может быть трудночитаемым на фоне цвета фона. Попробуйте увеличить контрастность.", "The candidate chose a time, but due to the error they did not get an email confirmation. Please contact support.": "Кандидат выбрал время, но из-за ошибки не получил подтверждения по электронной почте. Пожалуйста, обратитесь в службу поддержки.", "The candidate gets a link where they can choose an interview time.": "Кандидат получает ссылку, по которой он может выбрать время собеседования.", "The candidates have no conflicting fields. Ready to merge!": "", "The category field is required.": "Поле категории является обязательным.", "The cleaned-up text of a transcript segment in which spelling mistakes have been fixed.": "Очищенный текст фрагмента стенограммы, в котором исправлены орфографические ошибки.", "The consent expires when this project is finished.": "Срок действия согласия истекает после завершения проекта.", "The deadline for review is in :deadline, so you better do it now.": "Крайний срок подачи рецензии - :deadline, так что лучше сделать это сейчас.", "The deadline for review is in :deadline. You will be notified if the job requisition succeeds.": "Крайний срок рассмотрения заявки указан в :deadline. Вы получите уведомление, если заявка на работу будет успешно рассмотрена.", "The default language for new users in your organization.": "Язык по умолчанию для новых пользователей в вашей организации.", "The display name becomes visible to candidates when using the [position_display_name] merge tag.": "Отображаемое имя становится видимым для кандидатов при использовании тега слияния [position_display_name].", "The final score is calculated: cNPS = % promoters - % detractors.": "Итоговая оценка рассчитывается так: cNPS = % промоутеров - % недоброжелателей.", "The following e-mail will be sent to recipient:": "Получателю будет отправлено следующее письмо:", "The font file extensions must be one of the following: :extensions.": "", "The function parameter must be an array in which each object represents a cleaned-up segment with the \"text\" field contains the cleaned-up text and \"time\" field remains unchanged.": "Параметр функции должен представлять собой массив, в котором каждый объект представляет собой очищенный сегмент, поле \"текст\" которого содержит очищенный текст, а поле \"время\" остается неизменным.", "The heading colour may be hard to read on the background colour. Try increasing the contrast.": "Цвет заголовка может быть трудночитаемым на фоне цвета фона. Попробуйте увеличить контрастность.", "The interview has multiple people being interviewed and their names are as follows:": "В интервью участвуют несколько человек, и имена их следующие:", "The interview is conducted by a person named :interviewer.": "Интервью проводит человек по имени :интервьюер.", "The interview is conducted by the following interviewers:": "Интервью проводят следующие интервьюеры:", "The interview is for a job position titled \":position\" in the company \":company\".": "Собеседование проводится на вакансию под названием \":position\" в компании \":company\".", "The interview is for a job position titled \":position\" with involvement by the following companies: :companies.": "Собеседование проводится на вакансию \":должность\" с участием следующих компаний: :компании.", "The interview is for a job position titled \":position\".": "Собеседование проводится на вакансию под названием \":position\".", "The job ad can be accessed only by logged in users and from internal network.": "Доступ к объявлению о работе могут получить только авторизованные пользователи и из внутренней сети.", "The latest error was:": "Последняя ошибка была:", "The maximum value must be greater than the minimum value.": "", "The person being interviewed is named :person.": "Собеседника зовут :person.", "The processing of the transcript has not been finished yet. Please check again later.": "Обработка транскрипта еще не завершена. Пожалуйста, проверьте еще раз позже.", "The same value as the original transcript segment had for the key \"time\". Should not be modified.": "То же значение, что и в оригинальном сегменте транскрипта для ключа \"time\". Не должно быть изменено.", "The segments returned must be in the exact same order as input.": "", "The segments returned must be in the exact same order as input. Give response in following format: {\"segments\": [...]}": "Возвращаемые сегменты должны быть в том же порядке, что и вводимые. Дайте ответ в следующем формате: {\"сегменты\": [...]}", "The selected candidate does not have a valid phone number.": "У выбранного кандидата нет действующего номера телефона.", "The selected candidate does not have an email address.": "У выбранного кандидата нет адреса электронной почты.", "The speech that was originally transcribed might have included the following proper names:": "В первоначальной транскрипции речь могла содержать следующие имена собственные:", "The summary should be useful for making good hiring decisions.": "Резюме должно быть полезным для принятия правильного решения о найме.", "The survey responses are aggregated into three groups by their score.": "Ответы на вопросы опроса объединены в три группы по количеству баллов.", "The table must have the following columns: Name, Email, Phone, Tags. The file may include one or more comment columns named Comment1, Comment2 etc. Download sample file <a href=\"/files/import_sample.xlsx\" target=\"_blank\">here</a>.": "Таблица должна содержать следующие столбцы: Им<PERSON>, Em<PERSON>, Телефон, Теги. Файл может содержать один или несколько столбцов комментариев с именами Comment1, Comment2 и т. д. Скачать пример файла <a href=\"/files/import_sample.xlsx\" target=\"_blank\">можно здесь</a>.", "The uploaded file is too large.": "Загруженный файл слишком велик.", "The user will send you the transcript which will be a JSON array of transcript segments": "Пользователь отправит вам стенограмму, которая будет представлять собой JSON-массив сегментов стенограммы.", "The user will send you the transcript which will be a JSON array of transcript segments, each being a JSON object with two keys: \"text\" and \"time\".": "Пользователь отправит вам стенограмму, которая будет представлять собой JSON-массив сегментов стенограммы, каждый из которых является JSON-объектом с двумя ключами: \"текст\" и \"время\".", "The {anonymizedCv} option will also show an anonymized version of the candidate's CV.": "", "Theater Companies": "Театральные компании", "Theme": "Тема", "Then choose \"Message\" and include the [survey] tag inside the message.": "Затем выберите \"Сообщение\" и включите тег [опрос] в сообщение.", "There are candidates, actions or statistics associated with this stage.": "С этим этапом связаны кандидаты, действия или статистика.", "There are no actions defined for this stage.": "Для этого этапа не определено никаких действий.", "There are no cNPS responses yet.": "Отзывов на cNPS пока нет.", "There are no candidates in this slot.": "В этом слоте нет кандидатов.", "There are no comments yet.": "Комментариев пока нет.", "There are no integrations yet.": "Интеграций пока нет.", "There are no messages yet.": "Сообщения пока отсутствуют.", "There are no more open slots in this interview.": "В этом интервью больше нет свободных мест.", "There are no project log entries yet.": "Пока нет записей в журнале проекта.", "There are no published job ads for this project yet.": "Для этого проекта пока нет опубликованных объявлений о работе.", "There are no upcoming interviews.": "В ближайшее время собеседований не будет.", "There is no data available for the selected filters.": "Для выбранных фильтров нет данных.", "There is no data to display.": "Нет данных для отображения.", "There is not enough data to display this report. Try changing the selected filters and/or date range.": "Для отображения этого отчета недостаточно данных. Попробуйте изменить выбранные фильтры и/или диапазон дат.", "There must be at least one category for each system category": "Для каждой категории системы должна быть хотя бы одна категория", "There must be at least one custom category for every system category": "Для каждой системной категории должна быть хотя бы одна пользовательская категория.", "There was a problem loading the form options.": "Возникла проблема с загрузкой параметров формы.", "There was a problem with sending the candidates.": "Возникла проблема с отправкой кандидатов.", "There was an error": "Произошла ошибка", "There was an error anonymizing the candidates. Please refresh this page.": "Произошла ошибка при анонимизации кандидатов. Пожалуйста, обновите эту страницу.", "There was an error assigning the room.": "При назначении номера произошла ошибка.", "There was an error committing stage transitions. Please refresh the page.": "Произошла ошибка при совершении переходов между этапами. Пожалуйста, обновите страницу.", "There was an error loading this report.": "При загрузке этого отчета произошла ошибка.", "There was an error removing the action.": "", "There was an error removing the stage.": "Произошла ошибка при удалении этапа.", "There was an error removing this stage action.": "", "There was an error sending your messages.": "При отправке ваших сообщений произошла ошибка.", "There was an error submitting the form. Please try again.": "При отправке формы произошла ошибка. Пожалуйста, попробуйте еще раз.", "There was an error updating the event.": "", "There was an error uploading the file.": "При загрузке файла произошла ошибка.", "There was an error.": "Произошла ошибка.", "There was an error. Please try again.": "Произошла ошибка. Пожалуйста, попробуйте еще раз.", "There were candidates for whom we could not determine some fields. You can review the information below and update it if necessary.": "Были кандидаты, для которых мы не смогли определить некоторые поля. Вы можете просмотреть приведенную ниже информацию и при необходимости обновить ее.", "There's a project associated with this requisition.": "С этой заявкой связан проект.", "These actions cannot be reassigned.": "", "These candidates have one or more overlapping fields. Click on a name to compare.": "У этих кандидатов есть одно или несколько совпадающих полей. Нажмите на имя, чтобы сравнить.", "These fields will be added to the job ad publication form.": "", "These fields will be shown in the job ad publication form.": "", "These images will be added to the job posting. You can drag and drop to change the order.": "", "These tags will be removed.": "Эти метки будут удалены.", "These users will be added to every project you create from this template.": "", "These users will be asked to approve or reject the requisition.": "Этим пользователям будет предложено одобрить или отклонить заявку.", "They will be notified when you post the comment.": "Они получат уведомление, когда вы опубликуете комментарий.", "They will get a notification about the cancellation.": "Они получат уведомление об отмене.", "They wrote:": "Они написали:", "Think Tanks": "Аналитические центры", "Third sector / NGO": "Третий сектор / НПО", "This action can only be triggered automatically.": "Это действие может быть вызвано только автоматически.", "This action cannot be undone.": "Это действие нельзя отменить.", "This action is automatically executed if the candidate is in stage on or after {actionScheduledAtUser}. You can execute the action immediately by pressing this button.": "Это действие выполняется автоматически, если кандидат находится на этапе {actionScheduledAtUser} или позже. Вы можете выполнить действие немедленно, нажав эту кнопку.", "This action is automatically executed once the candidate has been in stage for {actionDelayValue} {actionDelayUnit}. You can execute the action immediately by pressing this button.": "Это действие выполняется автоматически, когда кандидат находится на этапе {actionDelayValue} {actionDelayUnit} . Вы можете выполнить действие немедленно, нажав эту кнопку.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after receiving a video interview response.": "Это действие выполняется автоматически {actionDelayValue} {actionDelayUnit} после получения ответа на видеоинтервью.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted a form.": "", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted their references.": "Это действие выполняется автоматически {actionDelayValue} {actionDelayUnit} после того, как кандидат отправил свои рекомендации.", "This action was sent to {candidateCount} candidates at {actionTime}.": "Эта акция была отправлена кандидатам на {candidateCount} по адресу {actionTime}.", "This allows you to send semi-automatic messages and video interviews": "Это позволяет отправлять полуавтоматические сообщения и проводить видеоинтервью", "This can help you make less biased decision for a more inclusive hiring process.": "Это поможет вам принимать менее предвзятые решения, чтобы процесс найма был более инклюзивным.", "This can not be undone.": "Этого уже не исправить.", "This candidate does not have any references yet.": "У этого кандидата пока нет рекомендаций.", "This candidate has a pending consent renewal.": "У этого кандидата еще не продлено согласие.", "This candidate has been anonymized.": "Этот кандидат был анонимизирован.", "This candidate is in {count} unfinished projects.": "", "This category contains candidates from ongoing recruitment projects.": "В этой категории собраны кандидаты из текущих проектов по подбору персонала.", "This category contains candidates from recently finished projects.": "В этой категории собраны кандидаты из недавно завершенных проектов.", "This client does not have any offices.": "У этого клиента нет офисов.", "This client does not have any projects.": "У этого клиента нет ни одного проекта.", "This client is associated with 1 active project|This client is associated with {count} active projects": "", "This client is associated with 1 finished project.|This client is associated with {count} finished projects.": "", "This client is not associated with any projects.": "", "This consent covers only processing related to the position they applied to.": "Это согласие распространяется только на обработку данных, связанных с должностью, на которую они подали заявку.", "This consent request has expired.": "", "This data was extracted with the help of AI": "", "This distinction is critical to ensure that your most important messages do not go into Gmail promotions tab.": "Это различие очень важно для того, чтобы ваши самые важные сообщения не попадали на вкладку \"Промоакции\" Gmail.", "This document is valid until :expiry.": "Этот документ действителен до :истечения срока действия.", "This e-mail is sent when you add a new user to your organization.": "Это письмо отправляется, когда вы добавляете нового пользователя в свою организацию.", "This field MUST include the [presentation_url] tag.": "Это поле ДОЛЖНО содержать тег [presentation_url].", "This field is used internally and for reporting. It can contain lowercase a-z and underscores (e.g. if your activity name is \"Sent out free t-shirt\" then you'd put tshirt_sent here).": "Это поле используется внутри организации и для отчетности. Оно может содержать строчные буквы a-z и символы подчеркивания (например, если название вашей деятельности - \"Рассылка бесплатных футболок\", то вы поместите сюда tshirt_sent).", "This form cannot be accessed directly, because it does not contain an email field. Please include the form in an e-mail via the [form_url] merge tag.": "К этой форме нельзя обратиться напрямую, так как она не содержит поля для ввода email. Пожалуйста, включите форму в письмо с помощью тега слияния [form_url].", "This form cannot be accessed directly. Please include the form in an e-mail via the [form_url] merge tag.": "Доступ к этой форме напрямую невозможен. Пожалуйста, включите форму в электронное письмо с помощью тега слияния [form_url].", "This form is automatic and uses the stage defined on the landing page": "Эта форма является автоматической и использует этап, определенный на целевой странице", "This form is directly linked to a stage, applicants will always be added to this stage": "Эта форма напрямую связана с этапом, кандидаты всегда будут добавляться на этот этап", "This form is used in one or more templates: {templates}": "", "This generates automatic summaries of candidate CV-s.": "Это позволяет автоматически составлять резюме кандидатов.", "This includes confidential projects. Do not turn on hastily.": "Это касается и конфиденциальных проектов. Не включайте его поспешно.", "This integration is currently not linked to any API keys.": "", "This integration is using the following API keys:": "", "This integration publishes a feed at:": "", "This interview has been cancelled.": "Это интервью было отменено.", "This interview slot was not yet booked.": "Это место для интервью еще не забронировано.", "This interview was prescheduled.": "Это интервью было запланировано заранее.", "This is a manual action, only executed when you click this button.": "Это ручное действие, которое выполняется только при нажатии на эту кнопку.", "This is a preview of the form. Submissions will not be saved.": "", "This is the url where shared candidates are listed.": "Это url, в котором перечислены общие кандидаты.", "This is used for all time inputs and outputs. ": "Это используется для всех временных входов и выходов. ", "This is used in internal menus. Candidates can not see this.": "Это используется во внутренних меню. Кандидаты не могут этого видеть.", "This is used to determine how long before the expiry date is the file considered to be expiring soon.": "", "This is used to determine the expiry date if the file doesn't have an expiration date specified.": "", "This job ad is for internal access only. Please": "Это объявление о работе предназначено только для внутреннего доступа. Пожалуйста,", "This job requisition needs your approval to move forward.": "Эта заявка на работу требует вашего одобрения, чтобы двигаться дальше.", "This key is used by integration {integrationName}. Deleting this key will break the connection between the integration and your Teamdash instance.": "", "This location was detected automatically with high confidence based on data provided in the CV.": "Это местоположение было определено автоматически с высокой степенью достоверности на основе данных, представленных в резюме.", "This location was detected automatically with low confidence based on data provided in the CV.": "Это место было определено автоматически с низкой степенью достоверности на основе данных, представленных в резюме.", "This location was detected automatically with medium confidence based on data provided in the CV.": "Это место было определено автоматически со средней степенью достоверности на основе данных, представленных в резюме.", "This means that candidate imports or job postings might not work correctly.": "Это означает, что импорт кандидатов или размещение вакансий могут работать некорректно.", "This means that if you are scheduling video interviews, Teamdash will use a fallback video calls provider, not :integration.": "Это означает, что если вы планируете видеоинтервью, Teamdash будет использовать резервного поставщика видеозвонков, а не :интеграцию.", "This means that incoming emails from candidates are not displayed in Teamdash.": "Это означает, что входящие письма от кандидатов не отображаются в Teamdash.", "This means that when you're scheduling interviews, you might not see your colleagues' calendars correctly.": "Это означает, что при составлении расписания собеседований вы можете не видеть календари своих коллег.", "This means that you cannot send candidates to your HRIS from their profiles.": "Это означает, что вы не сможете отправлять кандидатов в HRIS из их профилей.", "This message will be sent to :allCandidates in the interview :immediatelyAfterSaving": "Это сообщение будет отправлено :всем кандидатам на собеседование :сразу после сохранения", "This message will be sent to the candidate :afterTimeSelected.": "Это сообщение будет отправлено кандидату :afterTimeSelected.", "This option allows you to add candidates without email. This will make some features fail.": "Эта опция позволяет добавлять кандидатов без электронной почты. При этом некоторые функции не будут работать.", "This option allows you to require adding a location for each candidate.": "Эта опция позволяет требовать добавления местоположения для каждого кандидата.", "This password reset link will expire in :count days.": "Срок действия этой ссылки для сброса пароля истечет через :дней.", "This person is marked as the sender.": "Этот человек отмечен как отправитель.", "This presentation was shared with :recipient.": "Эта презентация была передана :получателю.", "This project and the candidates within are only visible to project members.": "", "This project does not accept candidates through any application methods yet.": "Этот проект пока не принимает кандидатов с помощью каких-либо методов подачи заявок.", "This project doesn't have a position display name set.": "", "This project is finished and read-only.": "Этот проект завершен и доступен только для чтения.", "This project is not associated with any landing pages yet.": "Этот проект еще не связан ни с одной целевой страницей.", "This reason cannot be deleted as it has already been assigned to some candidates.": "Эту причину нельзя удалить, так как она уже была назначена некоторым кандидатам.", "This requisition was approved!": "Заявка была одобрена!", "This setting applies to all users who view this stage. Limited users can not change this setting.": "Эта настройка применяется ко всем пользователям, просматривающим этот этап. Ограниченные пользователи не могут изменять эту настройку.", "This setting is nondestructive - you will always be able to turn it off again, without losing any data.": "Эта настройка является неразрушающей - вы всегда сможете отключить ее снова, не потеряв при этом никаких данных.", "This tag will take the place of all source tags.": "Этот тег заменяет все исходные теги.", "This week": "На этой неделе", "This will appear as the image when sharing on social media and IM platforms.": "Это изображение будет отображаться при публикации в социальных сетях и IM-платформах.", "This will appear in list view.": "Это отобразится в виде списка.", "This will apply to all users. It can later be turned back on in Organization settings.": "Это будет применяться ко всем пользователям. Позже его можно будет снова включить в настройках организации.", "This will be a select option for candidates in the consent renewal request.": "", "This will be in your page URL.": "Это будет указано в URL-адресе вашей страницы.", "This will be sent to LinkedIn as the job description.": "Это будет отправлено в LinkedIn в качестве описания вакансии.", "This will be stored as the organization privacy policy URL.": "Это будет сохранено как URL-адрес политики конфиденциальности организации.", "This will not affect existing actions in projects previously created from this template.": "Это не повлияет на существующие действия в проектах, ранее созданных на основе этого шаблона.", "Thursday": "Четверг", "Tibetan": "Тибетский", "Tigrinya": "Тигринья", "Tiler": "Плиточник", "Time": "Время", "Time slot updated successfully!": "", "Time to fill": "Время заполнять", "Time to hire": "Время нанимать", "Timestamp": "Временная метка", "Timestamp of speech occurrence": "", "Timezone": "Часовой пояс", "Tinsmith": "Жестянщик", "Tip: If you want to run something when the candidate submits the form, create another action with the trigger set to \"after candidate has submitted a form\".": "", "Tire worker": "Рабочий-шиномонтажник", "Title": "Название", "To automate your feedback collection process, use the [survey] tag inside your automatic stage actions.": "Чтобы автоматизировать процесс сбора отзывов, используйте тег [survey] в автоматических действиях на этапе.", "To find your employer ID:": "Чтобы найти идентификатор работодателя:", "To find/generate your API key:": "Чтобы найти/создать свой ключ API:", "To find/generate your Client ID and Secret:": "Чтобы найти/создать идентификатор и секрет клиента:", "To get the best results:": "Для достижения наилучших результатов:", "To get your credentials for integrating with RetailChoice, please contact RetailChoice over one of the methods available at": "Чтобы получить учетные данные для интеграции с RetailChoice, свяжитесь с RetailChoice одним из способов, доступных по адресу", "To get your credentials for integrating with Totaljobs, please contact Totaljobs over one of the methods available at": "Чтобы получить учетные данные для интеграции с Totaljobs, свяжитесь с Totaljobs одним из способов, доступных по адресу", "To know what your candidates really think about your recruitment process, you'll need to ask them for actual feedback.": "Чтобы узнать, что на самом деле думают ваши кандидаты о процессе подбора персонала, вам нужно спросить их о реальных отзывах.", "To provide you with better analytics, it will soon be required to categorize all stages before saving a project.": "Чтобы предоставить вам более точную аналитику, вскоре потребуется классифицировать все этапы перед сохранением проекта.", "To show you project funnel statistics, all project stages need to be categorized.": "Чтобы показать статистику воронки проекта, все этапы проекта должны быть распределены по категориям.", "To use Teamdash Platform You should accept": "Чтобы использовать платформу Teamdash, вы должны принять", "To use the Teamdash Platform you must accept the": "Чтобы использовать платформу Teamdash, вы должны принять", "Tobacco Manufacturing": "Табачное производство", "Today": "Сегодня", "Today is the day to create your first job ad with Job Ad Tool by Teamdash!": "Сегодня самое время создать свое первое объявление о работе с помощью инструмента Job Ad Tool от Teamdash!", "Tomorrow": "Завтра", "Tonga (Tonga Islands)": "Тонга (Острова Тонга)", "Too Many Requests": "Слишком много запросов", "Total": "Всего", "Total average": "Общее среднее значение", "Total candidates": "Всего кандидатов", "Total of 1 comment|Total of {count} comments": "Всего 1 комментарий|Всего {count} комментариев", "Total projects": "Всего проектов", "Tour operator": "Туроператор", "Tourism / Hotels / Catering": "Туризм / Отели / Кейтеринг", "Town": "Город", "Town or postcode": "Город или почтовый индекс", "Tractor driver": "Тракторист", "Trade": "Торговля", "Trade/retail": "Торговля/розничная торговля", "Train driver": "Машинист поезда", "Trainer": "Тренер", "Training": "Обучение", "Training description": "Описание тренинга", "Training/education/culture": "Обучение/образование/культура", "Transcribe the provided text.": "Расшифруйте предоставленный текст.", "Transcription segment": "", "Transcription segments": "", "Transcripts": "Транскрипты", "Translation and Localization": "Перевод и локализация", "Translator": "Переводчик", "Transport driving": "Вождение транспорта", "Transport/logistics management": "Управление транспортом/логистикой", "Transportation": "Транспорт", "Transportation Equipment Manufacturing": "Производство транспортного оборудования", "Transportation Programs": "Транспортные программы", "Transportation, Logistics, Supply Chain and Storage": "Транспорт, логистика, цепочки поставок и хранение", "Travel": "Путешествие", "Travel Arrangements": "Организация поездок", "Trigger": "Триггер", "Triin from Teamdash has built many job ads and recruitment campaigns.": "Трийн из Teamdash создал множество объявлений о работе и кампаний по подбору персонала.", "Troubleshooting:": "Устранение неполадок:", "Truck Transportation": "Грузовые перевозки", "Trusts and Estates": "Трасты и наследство", "Try it out": "", "Tsonga": "Тсонга", "Tswana": "Тсвана", "Tuesday": "Вторник", "Turkish": "Турецкая", "Turkmen": "Туркмен", "Turn on e-mail notifications": "Включите уведомления по электронной почте", "Turned Products and Fastener Manufacturing": "Производство токарных изделий и крепежа", "Turner": "Терн<PERSON>р", "Twi": "Twi", "Two-Factor Authentication": "Двухфакторная аутентификация", "Type": "Тип", "Type (CV-Library)": "Тип (CV-Library)", "Type @ to tag a colleague.": "Введите @, чтобы отметить коллегу.", "Type question here...": "Напечатайте вопрос здесь...", "Type to search or add...": "Введите, чтобы найти или добавить...", "Type to search...": "Введите для поиска...", "Typesetter": "Печатная машина", "UK & Ireland": "Великобритания и Ирландия", "UKPRN": "UKPRN", "UPDATED": "ОБНОВЛЕННЫЙ", "URL": "URL", "Uighur, Uyghur": "Уйгур, уйгурский", "Ukrainian": "Украина", "Unable to extract data from this file.": "", "Unable to respond to interview": "Невозможно ответить на вопросы интервью", "Unauthorized": "Неавторизованный", "Unavailable": "Недоступно", "Uncategorized": "Без категории", "Uncategorized stages": "Категоризированные этапы", "Undo": "Отменить", "Unfortunately, it has a bug with uploading videos on a cellular connection.": "", "Unfortunately, there are no available interview times left. Please contact your recruiter.": "К сожалению, свободных дней для собеседования не осталось. Пожалуйста, свяжитесь с вашим рекрутером.", "Unique candidates": "", "Unknown": "Неизвестный", "Unlimited": "Неограниченное количество", "Unlink": "Разблокировать ссылку", "Unlink file from its project": "Отсоедините файл от его проекта", "Unpin": "Развернуть", "Unpublish": "Отменить публикацию", "Unpublished at": "", "Unsaved changes": "Несохраненные изменения", "Unseen notifications first": "Невидимые уведомления в первую очередь", "Unselect all": "", "Unselect video": "Отмените выбор видео", "Until": "До", "Until date": "До даты", "Until project end": "До окончания проекта", "Until specific date": "До определенной даты", "Update": "Обновление", "Update comment": "Обновить комментарий", "Update requisition": "Обновление заявки", "Updated": "", "Updates for :project": "Обновления для :проекта", "Upgrade": "Обновление", "Upgrade Teamdash": "Обновление Teamdash", "Upgrade to access this feature": "Обновление, чтобы получить доступ к этой функции", "Upload": "Загрузить", "Upload a file of the :version version of the font.": "", "Upload a video file": "Загрузите видеофайл", "Upload button text": "Текст кнопки загрузки", "Upload photo": "Загрузить фотографию", "Upload video": "Загрузить видео", "Urban Transit Services": "Городские транзитные службы", "Urdu": "Урду", "Usages": "", "Use AI to compare candidates. To avoid bias, you should select multiple candidates at a time.": "Используйте искусственный интеллект для сравнения кандидатов. Чтобы избежать предвзятости, следует выбирать сразу несколько кандидатов.", "Use AI to generate a transcript and summary based on the video call recording.": "Используйте искусственный интеллект для создания стенограммы и резюме на основе записи видеозвонка.", "Use AI to generate a transcript and summary based on the video call recording.)": "Используйте искусственный интеллект для создания стенограммы и резюме на основе записи видеозвонка).", "Use a [survey] merge tag like this:": "Используйте тег слияния [survey] следующим образом:", "Use a font which is easy to read": "Используйте шрифт, который легко читается", "Use cursor keys to navigate calendar dates": "Используйте клавиши курсора для перемещения по датам календаря", "Use custom description for LinkedIn": "Используйте пользовательское описание для LinkedIn", "Use custom stage categories": "Используйте пользовательские категории этапов", "Use heading colour": "Используйте цвет заголовка", "Use landing theme": "Используйте тему для посадки", "Use multiple languages": "Используйте несколько языков", "Use photos of your team members or office, not stock images": "Используйте фотографии членов вашей команды или офиса, а не стоковые изображения", "Use plain background": "Используйте однотонный фон", "Use primary colour": "Используйте основной цвет", "Use text signature": "Используйте текстовую подпись", "Use this field to link the form with a project. All the applicants will go to the selected stage.": "Используйте это поле, чтобы связать форму с проектом. Все претенденты перейдут на выбранный этап.", "Use this for confidentiality notices, disclaimers, etc.": "Используйте его для уведомлений о конфиденциальности, отказов от ответственности и т. д.", "Use this form as template for creating new forms": "Используйте эту форму в качестве шаблона для создания новых форм", "Use this value": "Используйте это значение", "Use your company's identity provider to log in to Teamdash. Requires a company email address.": "Для входа в Teamdash используйте идентификатор вашей компании. Требуется адрес электронной почты компании.", "Used for GDPR compliance": "Используется для обеспечения соответствия GDPR", "Used for job board exports.": "Используется для экспорта на доски объявлений.", "Used forbidden tag: [:tag] is not allowed.": "Использован запрещенный тег: [:tag] запрещен.", "Used in email signatures": "Используется в подписях к электронным письмам", "Used in email signatures, job board exports.": "Используется в подписях к электронным письмам, при экспорте на доски объявлений.", "Used in email signatures, job board exports. PNG format suggested.": "Используется в подписях к электронным письмам, при экспорте на доски объявлений. Предполагается формат PNG.", "Used in job board exports.": "Используется при экспорте с доски объявлений.", "Useful for collaborating with external recruitment partners. After enabling this setting, an admin can switch on password access for specific user accounts.": "Полезно для сотрудничества с внешними партнерами по подбору персонала. Включив эту настройку, администратор может включить доступ к паролю для определенных учетных записей пользователей.", "User": "", "User Avatar": "Аватар пользователя", "User administration": "Администрирование пользователей", "User deactivated": "", "User deactivated!": "", "User message": "", "User role": "Роль пользователя", "User roles": "Роли пользователей", "User-Agent": "User-Agent", "Username": "Имя пользователя", "Users": "Пользователи", "Users & accounts": "Пользователи и учетные записи", "Users added!": "Пользователи добавлены!", "Users count": "Количество пользователей", "Usually email from Teamdash goes <NAME_EMAIL>. You still get all the replies to your inbox because Teamdash includes your email in the Reply-To header.": "Обычно письма от Teamdash отправляются с адреса <EMAIL>. Вы по-прежнему получаете все ответы на свой почтовый ящик, потому что Teamdash включает ваш email в заголовок Reply-To.", "Utilities": "Утилиты", "Utilities Administration": "Управление коммунальным хозяйством", "Utility System Construction": "Строительство коммунальных систем", "Uzbek": "Узбек", "VP2 Feed": "VP2 Feed", "Valid": "Действительный", "Valid \"until date\" consent": "", "Valid HEX color codes start with a # followed by 3 or 6 characters.": "Допустимые коды цветов HEX начинаются с символа #, за которым следуют 3 или 6 символов.", "Valid data processing consent missing.": "Отсутствует действительное согласие на обработку данных.", "Valid long term consent": "Действительное долгосрочное согласие", "Valid until": "Действует до", "Valid, but missing long term consent": "Верно, но не хватает долгосрочного согласия", "Valid, but without \"until date\" consent": "", "Validity": "", "Value": "Значение", "Value is saved to database, label is visible to candidate.": "Значение сохраняется в базе данных, метка видна кандидату.", "Value must be a number": "Значение должно быть числом", "Value must not be a number.": "Значение не должно быть числом.", "Values": "Значения", "Varnisher": "Лакировщик", "Vehicle Repair and Maintenance": "Ремонт и техническое обслуживание автомобилей", "Venda": "Венда", "Venture Capital and Private Equity Principals": "Руководители компаний венчурного капитала и прямых инвестиций", "Verified": "Проверено", "Verify": "Проверьте", "Vertical position": "Вертикальное положение", "Veterinary": "Ветеринария", "Veterinary Services": "Ветеринарные услуги", "Video": "Видео", "Video call": "Видеозвонок", "Video call URL": "URL-адрес видеозвонка", "Video call created!": "Видеозвонок создан!", "Video call password": "Пароль для видеозвонков", "Video call password:": "Пароль для видеозвонков:", "Video interview": "Видеоинтервью", "Video interview title": "Название видеоинтервью", "Video interviews": "Видеоинтервью", "Video interviews sent!": "Видеоинтервью отправлены!", "Video message": "Видеообращение", "Video message sent!": "Видеосообщение отправлено!", "Video source": "Источник видео", "Vietnamese": "Вьетнамцы", "View": "Посмотреть", "View Job": "", "View ad": "Посмотреть объявление", "View all shared candidates": "", "View all tasks": "Просмотреть все задания", "View cNPS results": "Посмотреть результаты cNPS", "View candidate": "", "View comments": "Посмотреть комментарии", "View details": "Посмотреть детали", "View feed": "Просмотр подачи", "View form": "Посмотреть форму", "View image ad": "Посмотреть изображение объявления", "View instructions.": "Посмотреть инструкции.", "View invitation": "Посмотреть приглашение", "View invites": "Посмотреть приглашения", "View landing": "Просмотр посадки", "View landing page": "Просмотр целевой страницы", "View messages": "", "View project": "Посмотреть проект", "View public form": "Просмотр публичной формы", "View requisition": "Просмотр заявки", "View social media campaign results": "Просматривайте результаты кампаний в социальных сетях", "View stages": "Просмотр этапов", "View submission": "", "View submissions": "", "View template": "Посмотреть шаблон", "Viewed file preview": "", "Viewed profile": "", "Viewing file {cvIndex} of {cvsTotal}": "Просмотр файла {cvIndex} из {cvsTotal}", "Vimeo": "Vimeo", "Visagiste": "Visagiste", "Visibility": "Видимость", "Visible for limited users": "Видимость для ограниченных пользователей", "Visible to all": "Видно всем", "Visual Merchandising": "Визуальный мерчендайзинг", "Vocalist": "Вокалист", "Vocational Rehabilitation Services": "Услуги по профессиональной реабилитации", "Vocational education": "Профессиональное образование", "Vocational secondary education": "Профессиональное среднее образование", "Volap_k": "Volap_k", "Voluntary": "Добровольно", "Volunteer": "Волонтер", "Waiter": "Офици<PERSON><PERSON>т", "Walloon": "Валлония", "Warehouse": "Склад", "Warehousing": "Складское хозяйство", "Warehousing and Storage": "Складирование и хранение", "Warning period": "", "Warning period unit": "", "Warranty until": "Гарантия до", "Washerman": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Waste Collection": "Сбор отходов", "Waste Treatment and Disposal": "Обработка и утилизация отходов", "Water Supply and Irrigation Systems": "Системы водоснабжения и ирригации", "Water, Waste, Steam, and Air Conditioning Services": "Услуги по водоснабжению, утилизации отходов, паров и кондиционированию воздуха", "We are currently processing this candidate's profile. Please check again in a few minutes.": "В настоящее время мы обрабатываем анкету этого кандидата. Пожалуйста, проверьте еще раз через несколько минут.", "We are glad to have you on board. If you ever forget your Teamdash login address, just find this email.": "Мы рады видеть вас в команде. Если вы вдруг забудете свой адрес для входа в Teamdash, просто найдите это письмо.", "We are informing you that [organization_name] is currently storing your personal data. The data might include your CV, contact information and other information you provided when applying to a position.": "Сообщаем вам, что [название_организации] в настоящее время хранит ваши персональные данные. Эти данные могут включать ваше резюме, контактные данные и другую информацию, которую вы предоставили при подаче заявки на вакансию.", "We are updating our": "Мы обновляем наш", "We could not parse the uploaded file.": "Мы не смогли разобрать загруженный файл.", "We have populated the form with default categories. Add or remove categories to match your hiring process. You can drag the items to re-order them.": "Мы заполнили форму категориями по умолчанию. Добавьте или удалите категории в соответствии с вашим процессом найма. Вы можете перетаскивать элементы, чтобы изменить их порядок.", "We offer": "Мы предлагаем", "We received the following error with your Google Calendar integration:": "Мы получили следующую ошибку при интеграции с календарем Google:", "We will remind you in specified time!": "Мы напомним вам об этом в указанное время!", "We will use different infrastructure to deliver the message.": "Мы будем использовать различные инфраструктуры для передачи сообщения.", "We won't ask for dropout reasons anymore. You can always turn this back on in Settings.": "", "Web designer": "Веб-диза<PERSON><PERSON><PERSON>р", "Webhook endpoint URL": "", "Wednesday": "Среда", "Week": "Неделя", "Weekdays": "Будни", "Weeks": "", "Weighting": "Взвешивание", "Welcome to Teamdash": "Добро пожаловать в Teamdash", "Welcome to Teamdash – please verify your e-mail": "Добро пожаловать в Teamdash - пожалуйста, проверьте свой e-mail", "Welder": "Сварщик", "Wellness and Fitness Services": "Велнес и фитнес-услуги", "Welsh": "Уэльс", "Western Frisian": "Западнофризский", "What happens when this action is triggered:": "Что происходит при выполнении этого действия:", "What is Teamdash?": "Что такое Teamdash?", "What is cNPS?": "Что такое cNPS?", "What motivates the candidate? What are their likes and dislikes? Use specific quotes.": "Что мотивирует кандидата? Что им нравится и не нравится? Используйте конкретные цитаты.", "What next?": "Что дальше?", "When": "", "When creating new projects, these stages are used by default.": "При создании новых проектов эти этапы используются по умолчанию.", "When publishing to LinkedIn, use City, Country format (e.g. \"London, United Kingdom\"). For remote positions, use only country.": "При публикации в LinkedIn используйте формат \"Город, страна\" (например, \"Лондон, Великобритания\"). Для удаленных вакансий используйте только страну.", "When someone applies to a position, they implicitly consent to you processing their data.": "Когда человек подает заявку на вакансию, он косвенно дает согласие на обработку его данных.", "When switched on, this comment will be visible when sharing the candidate with :client.": "При включении этот комментарий будет виден при передаче кандидата клиенту.", "When switched on, this entry will be visible when sharing the candidate with :client.": "При включении эта запись будет видна при обмене кандидатом с :клиентом.", "When the project ends, you must not further process these candidates' data.": "После окончания проекта вы не должны больше обрабатывать данные кандидатов.", "When there are candidates without email addresses or phone numbers, you can only schedule a fixed time for the interview.": "", "When there are candidates without email addresses, you can only schedule a fixed time for the interview.": "Если у кандидатов нет адресов электронной почты, вы можете назначить только фиксированное время для собеседования.", "When this form is included in a landing page, colors will be taken from the landing page theme.": "Когда эта форма включена в целевую страницу, цвета будут взяты из темы целевой страницы.", "When this is turned off, candidate files from all projects are visible to all users.": "", "When you apply, we may conduct a background check using public databases and websites and utilizing a web search engine. Your resume may be retained for a maximum period of one year.": "Когда вы подаете заявление, мы можем провести проверку вашей биографии с использованием общедоступных баз данных и веб-сайтов, а также с помощью поисковой системы в Интернете. Ваше резюме может храниться не более одного года.", "When you choose :anyTime as the scheduling type, the candidates receive an e-mail with a link where they can freely choose a slot in the calendar that is open for all recruiters.": "При выборе типа планирования :anyTime кандидаты получают электронное письмо со ссылкой, по которой они могут свободно выбрать слот в календаре, открытый для всех рекрутеров.", "When you link a resource (i.e. project, landing) with a team, it will be accessible to that team and all teams above that team.": "Когда вы связываете ресурс (например, проект, посадочную площадку) с командой, он будет доступен этой команде и всем командам, находящимся выше нее.", "When you mark a recruitment project as finished, your right to process the candidate's data ends.": "Когда вы отмечаете проект по подбору персонала как завершенный, ваше право на обработку данных кандидата заканчивается.", "Where do hires come from?": "Откуда берутся сотрудники?", "Which features do you value the most?": "Какие функции вы цените больше всего?", "While the speakers are not identified in the transcript, you will do your best to differentiate the speakers.": "Хотя в стенограмме докладчики не обозначены, вы постараетесь сделать все возможное, чтобы различать выступающих.", "While you were away": "Пока тебя не было", "White collar, specialists": "Белые воротнички, специалисты", "Who are you talking to?": "С кем вы разговариваете?", "Wholesale": "Оптовая торговля", "Wholesale Alcoholic Beverages": "Оптовая торговля алкогольными напитками", "Wholesale Apparel and Sewing Supplies": "Оптовая торговля одеждой и швейными принадлежностями", "Wholesale Appliances, Electrical, and Electronics": "Оптовая торговля бытовой техникой, электротехникой и электроникой", "Wholesale Building Materials": "Оптовая торговля строительными материалами", "Wholesale Chemical and Allied Products": "Оптовая торговля химическими и сопутствующими товарами", "Wholesale Computer Equipment": "Оптовая торговля компьютерным оборудованием", "Wholesale Drugs and Sundries": "Оптовая торговля лекарствами и товарами", "Wholesale Food and Beverage": "Оптовая торговля продуктами питания и напитками", "Wholesale Footwear": "Обувь оптом", "Wholesale Furniture and Home Furnishings": "Оптовая торговля мебелью и товарами для дома", "Wholesale Hardware, Plumbing, Heating Equipment": "Оптовая торговля скобяными изделиями, сантехникой, отопительным оборудованием", "Wholesale Import and Export": "Оптовый импорт и экспорт", "Wholesale Luxury Goods and Jewelry": "Оптовая торговля предметами роскоши и ювелирными изделиями", "Wholesale Machinery": "Оптовые машины", "Wholesale Metals and Minerals": "Оптовая торговля металлами и минералами", "Wholesale Motor Vehicles and Parts": "Оптовая торговля автотранспортными средствами и запчастями", "Wholesale Paper Products": "Оптовая торговля бумажной продукцией", "Wholesale Petroleum and Petroleum Products": "Оптовая торговля нефтью и нефтепродуктами", "Wholesale Photography Equipment and Supplies": "Оптовая торговля фотооборудованием и принадлежностями", "Wholesale Raw Farm Products": "Оптовая торговля сырыми фермерскими продуктами", "Wholesale Recyclable Materials": "Оптовая продажа вторсырья", "Whoops!": "Упс!", "Why do candidates drop out?": "Почему кандидаты выбывают из игры?", "Why not start with a real open vacancy. Who is missing from your team?": "Почему бы не начать с реальной открытой вакансии. Кого не хватает в вашей команде?", "Why should I use cNPS?": "Почему я должен использовать cNPS?", "Why work here?": "Почему стоит работать здесь?", "Width": "Ши<PERSON><PERSON><PERSON>", "Wind Electric Power Generation": "Ветроэнергетика", "Windows master": "Мастер Windows", "Wineries": "Винодельни", "Wireless Services": "Услуги беспроводной связи", "With active exports": "С активным экспортом", "With best": "С лучшими", "With descendant teams": "С командами потомков", "With summary": "С резюме", "Without active exports": "Без активного экспорта", "Wolof": "Волоф", "Women's Handbag Manufacturing": "Производство женских сумок", "Wood Product Manufacturing": "Производство изделий из дерева", "Woodcutter": "Дровосек", "Work in shifts": "Работайте посменно", "Work schedule (profession.hu)": "", "Work time (cv.lt)": "Рабочее время (cv.lt)", "Work times": "Время работы", "Work times (CVK/CVM)": "Время работы (CVK/CVM)", "Worker": "Рабочий", "Working days": "Рабочие дни", "Working days (SS.lv)": "Рабочие дни (SS.lv)", "Working hours": "", "Working hours (SS.lv)": "Рабочее время (SS.lv)", "Working schedule (profession.hu)": "", "Working week description": "Описание рабочей недели", "Workplace type": "Тип рабочего места", "Write a interview time confirmation SMS": "", "Write a message for candidate to choose a time": "Напишите сообщение для кандидата, чтобы он выбрал время", "Write a message to send the video interview link": "Напишите сообщение, чтобы отправить ссылку на видеоинтервью", "Write a reference check message": "Напишите сообщение для проверки рекомендаций", "Write a rejection letter": "Напишите письмо с отказом", "Write activities": "Письменная деятельность", "Write candidates": "Пишите кандидатам", "Writing / Editing": "Написание / редактирование", "Writing and Editing": "Написание и редактирование", "Xhosa": "Xhosa", "YTD": "С НАЧАЛА ГОДА", "Year": "Год", "Years": "Годы", "Yes": "Да", "Yes, anonymize the candidates!": "Да, анонимизируйте кандидатов!", "Yes, delete the candidates permanently!": "Да, удалите кандидатов навсегда!", "Yiddish": "Иди<PERSON>", "Yoruba": "Йоруба", "You approved this requisition.": "Вы одобрили эту заявку.", "You are a summarization tool integrated within an applicant tracking system (ATS).": "Вы - инструмент для подведения итогов, интегрированный в систему отслеживания кандидатов (ATS).", "You are a tool meant for cleaning up transcripts integrated within an applicant tracking system (ATS).": "Вы - инструмент, предназначенный для очистки транскриптов, интегрированный в систему отслеживания кандидатов (ATS).", "You are currently using :full_seats and :limited_seats.": "В данный момент вы используете :full_seats и :limited_seats.", "You are currently using :seats.": "Вы сейчас используете :места.", "You are editing a project template.": "Вы редактируете шаблон проекта.", "You are editing an action in a template.": "Вы редактируете действие в шаблоне.", "You are not allowed to edit this comment.": "Вы не можете редактировать этот комментарий.", "You are receiving this email because we received a password reset request for your account.": "Вы получаете это письмо, потому что мы получили запрос на сброс пароля для вашей учетной записи.", "You are using an outdated version of MS Edge. All features might not work as intended.": "Вы используете устаревшую версию MS Edge. Все функции могут работать не так, как задумано.", "You can add a new form by clicking the button below.": "", "You can add an API key by clicking the button below.": "", "You can add file types by clicking the button below.": "", "You can add integrations to:": "", "You can also refuse consent from the link. If you consent to data processing, you can withdraw your consent at any time in the future. If you ignore this message, we will assume you do not consent to further data processing.": "Вы также можете отказаться от согласия по ссылке. Если вы согласны на обработку данных, вы можете отозвать свое согласие в любое время в будущем. Если вы проигнорируете это сообщение, мы будем считать, что вы не согласны на дальнейшую обработку данных.", "You can also use [survey] to include a feedback survey with a 0-10 rating scale.": "Вы также можете использовать [survey], чтобы включить опрос обратной связи со шкалой оценок 0-10.", "You can ask for data processing consent renewals from {candidateCount}.": "", "You can choose whether to use the question from here or your custom form when sending a message to the candidate.": "", "You can contact these candidates about relevant job offers.": "Вы можете связаться с этими кандидатами, чтобы узнать о соответствующих предложениях работы.", "You can create an API key by clicking the BambooHR logo in top right corner and choosing \"API Keys\".": "Вы можете создать ключ API, нажав на логотип BambooHR в правом верхнем углу и выбрав \"Ключи API\".", "You can embed this form on your landing page.": "Вы можете встроить эту форму на свою целевую страницу.", "You can give additional instructions to the AI using the \"Add the tag if...\" field.": "Вы можете дать дополнительные указания ИИ, используя поле \"Добавить метку, если...\".", "You can leave additional feedback:": "Вы можете оставить дополнительный отзыв:", "You can limit the time available to the candidate by setting the days, dates and times between which they must choose a slot.": "Вы можете ограничить время, доступное кандидату, установив дни, даты и время, между которыми он должен выбрать слот.", "You can load saved SMS templates later.": "Сохраненные шаблоны SMS можно загрузить позже.", "You can load saved message templates later. Attachments are not saved.": "Сохраненные шаблоны сообщений можно загрузить позже. Вложения не сохраняются.", "You can modify which reasons are available in the Organization settings.": "", "You can now send cNPS surveys with more questions!": "", "You can now track why candidates dropped out of the application process. If this candidate applies again, you will see this information on their candidate card.": "", "You can optionally associate the candidate presentation with a project.": "", "You can read about our privacy statement here.": "Вы можете ознакомиться с нашим заявлением о конфиденциальности здесь.", "You can revoke an individual consent. This will not affect other consents.": "", "You can revoke this consent any time by contacting {org} at {email}.": "Вы можете отозвать данное согласие в любое время, связавшись с {org} по адресу {email}.", "You can send cNPS feedback surveys with all types of messages.": "Вы можете отправлять опросы обратной связи cNPS с любыми типами сообщений.", "You can send this message to all references or only to personal or professional references.": "Вы можете отправить это сообщение всем рекомендателям или только личным или профессиональным рекомендателям.", "You can set the link to the privacy policy in {organizationSettings}.": "Вы можете установить ссылку на политику конфиденциальности на сайте {organizationSettings}.", "You can use the <i>Only summary</i> mode to hide candidate name, gender, ethnicity, and age.": "Вы можете использовать режим <i>Only summary</i>, чтобы скрыть имя, пол, этническую принадлежность и возраст кандидата.", "You can use the {onlySummary} mode to hide candidate name, gender, ethnicity, and age.": "В режиме {onlySummary} можно скрыть имя, пол, этническую принадлежность и возраст кандидата.", "You can:": "Вы можете:", "You do not have permission to create new dropout reasons.": "", "You do not have permission to disable asking for dropout reasons.": "", "You don't have access to this requisition.": "У вас нет доступа к этой заявке.", "You dont have permission to move candidates in project. Your changes will not be saved.": "У вас нет прав на перемещение кандидатов в проекте. Ваши изменения не будут сохранены.", "You have 1 invite pending for this interview.|You have :count invites pending for this interview.": "У вас есть 1 приглашение на это собеседование.|У вас есть :считанные приглашения на это собеседование.", "You have :count incomplete tasks without a deadline.": "У вас есть :считать незавершенные задачи без срока выполнения.", "You have :count tasks overdue.": "У вас есть :подсчет просроченных задач.", "You have a valid data processing consent for {consentScore}% of your database.": "У вас есть действительное согласие на обработку данных для {consentScore}% вашей базы данных.", "You have already asked for reference from this person at {lastMessageTime}.": "Вы уже запрашивали рекомендации у этого человека на сайте {lastMessageTime}.", "You have no mail identities set up yet.": "У вас еще не установлены почтовые идентификаторы.", "You have no requisitions yet.": "У вас еще нет заявок.", "You have no video interviews yet.": "У вас еще нет видеоинтервью.", "You have not created any consent subtypes yet.": "", "You have not sent this presentation via e-mail yet, but you can share this url with anyone.": "Вы еще не отправили эту презентацию по электронной почте, но вы можете поделиться этим url с кем угодно.", "You have {candidateCount} and {slotCount}": "У вас есть {candidateCount} и {slotCount}", "You haven't added any custom fonts yet.": "", "You may not change the time (timestamp) data of the segments; you may only change the text.": "", "You may only omit input segments from your output if you are very certain that they are noise and do not contain human speech.": "Вы можете исключить входные сегменты из выходных данных, только если вы очень уверены, что они являются шумом и не содержат человеческой речи.", "You must accept the privacy policy.": "Вы должны принять политику конфиденциальности.", "You must be a Microsoft tenant administrator to continue.": "Чтобы продолжить, вы должны быть администратором арендатора Microsoft.", "You must call the provided tool function with an array of strings in which each string is a single sentence that summarizes some aspect of the interview.": "Вы должны вызвать предоставленную функцию с массивом строк, в котором каждая строка представляет собой одно предложение, кратко описывающее какой-то аспект интервью.", "You must call the provided tool function with the cleaned up transcript segments. ": "Вы должны вызвать предоставленную функцию инструмента с очищенными сегментами транскрипта. ", "You must get approval from :users": "Вы должны получить одобрение от :пользователей", "You must include yourself as manager or member for confidential projects.": "В конфиденциальных проектах вы должны указывать себя в качестве руководителя или участника.", "You must select at least one slot in this scheduling type.": "Вы должны выбрать хотя бы один слот в этом типе расписания.", "You must try your best to fix spelling errors and improve readability without removing information or adding new content.": "Вы должны постараться исправить орфографические ошибки и улучшить читабельность, не удаляя информацию и не добавляя новый контент.", "You must try your best to fix spelling errors and improve readability without removing information or adding new content. You may replace words if they seem out of context.": "Вы должны постараться исправить орфографические ошибки и улучшить читабельность, не удаляя информацию и не добавляя новое содержание. Вы можете заменить слова, если они кажутся вырванными из контекста.", "You probably want to automate your GDPR compliance": "Вероятно, вы хотите автоматизировать соблюдение требований GDPR", "You rejected this requisition.": "Вы отклонили эту заявку.", "You will always have the option to skip a month. Same goes with purging the database, you always get a notification beforehand and an option to skip. You will always have the final say on any actions.": "У вас всегда будет возможность пропустить месяц. То же самое касается чистки базы данных - вы всегда получите уведомление заранее и возможность пропустить. За вами всегда остается последнее слово при принятии любых решений.", "You will be notified once everybody has made their decision.": "Вы получите уведомление, как только все примут решение.", "You're almost there!": "Вы почти у цели!", "You've been assigned project manager in the project :positionName": "Вы назначены руководителем проекта :positionName", "You've been removed as recruiter from a job requisition": "Вы были удалены из заявки на вакансию в качестве рекрутера", "Your Teamdash integration :integration has not been working correctly.": "Ваша интеграция с Teamdash :интеграция работает некорректно.", "Your Teamdash login details": "Ваши данные для входа в систему Teamdash", "Your URL should be like \"/employer/12345/settings/...\"": "Ваш URL должен иметь вид \"/employer/12345/settings/...\".", "Your account must be accessed via SSO": "Доступ к вашей учетной записи должен осуществляться через SSO", "Your account needs verification": "Ваша учетная запись нуждается в проверке", "Your approval is needed for :position": "Ваше согласие необходимо для :должности", "Your approval is needed for a job requisition for :position.": "Необходимо ваше одобрение для заявки на вакансию :должность.", "Your approval is no longer needed for :position": "Ваше согласие больше не требуется для :должности", "Your daily agenda": "Ваш ежедневник", "Your decisions is missing.": "Ваши решения отсутствуют.", "Your e-mail has been verified and your organization's Teamdash instance has been created.": "Ваш e-mail был проверен, и экземпляр Teamdash для вашей организации был создан.", "Your employer is special - prove it with interesting arguments": "Ваш работодатель особенный - докажите это с помощью интересных аргументов", "Your job ad is available at:": "Ваше объявление о работе размещено на сайте:", "Your job ad is public!": "Ваше объявление о работе опубликовано!", "Your meta description will appear when sharing on social media and IM platforms. Keep the length below 150 characters.": "Ваше метаописание будет отображаться при публикации в социальных сетях и на платформах IM. Не превышайте 150 символов.", "Your only function is to clean up an auto-generated transcript segments that will be provided to you by the user.": "Ваша единственная функция - очистка сегментов автогенерируемого транскрипта, который будет предоставлен вам пользователем.", "Your only function is to clean up the text of auto-generated transcript segments that will be provided to you by the user.": "", "Your page title will appear when sharing on social media and IM platforms. Keep the length below 55 characters.": "Заголовок вашей страницы будет отображаться при публикации в социальных сетях и на платформах IM. Не превышайте 55 символов.", "Your primary function is to summarize a video interview using a transcript that is provided to you by the user.": "Ваша основная задача - обобщить видеоинтервью, используя стенограмму, предоставленную вам пользователем.", "Your question": "Ваш вопрос", "Your report will be emailed to you.": "Отчет будет отправлен вам по электронной почте.", "Your requisition :position has been approved": "Ваша заявка :позиция была одобрена", "Your requisition :position was rejected!": "Ваша заявка :позиция была отклонена!", "Your selection contains candidates without a valid phone number. Please uncheck them.": "В вашей подборке есть кандидаты без действительного номера телефона. Пожалуйста, снимите с них отметку.", "Your selection contains candidates without email addresses. Please uncheck them.": "В вашей подборке есть кандидаты без адресов электронной почты. Пожалуйста, снимите с них флажки.", "Your selection contains candidates without email addresses. You can only create pre-scheduled interviews for this selection.": "В вашей подборке есть кандидаты без адресов электронной почты. Для этой подборки можно создавать только предварительные собеседования.", "Your session has expired and your last change was not saved. Sorry. We will refresh the page for you.": "Ваша сессия истекла, и последнее изменение не было сохранено. Извините. Мы обновим страницу для вас.", "Your subscription includes :generic_seats.": "Ваша подписка включает :generic_seats.", "Your subscription includes :included_full_seats and :included_limited_seats.": "Ваша подписка включает :included_full_seats и :included_limited_seats.", "Your username is the subdomain name you use for logging in to BambooHR.": "Ваше имя пользователя - это имя поддомена, которое вы используете для входа в BambooHR.", "Youtube": "Youtube", "Zhuang, Chuang": "Ч<PERSON><PERSON><PERSON><PERSON>, Чуан", "Zoom calls": "Вызовы Zoom", "Zoos and Botanical Gardens": "Зоопарки и ботанические сады", "Zulu": "Zulu", "[current_user_name] has invited you to use Teamdash.": "[имя_текущего_пользователя] пригласил вас использовать Teamdash.", "a project": "проект", "after the candidate submits a form": "", "after the candidate submits a video response": "после того, как кандидат представит видеоответ", "after the candidate submits their referees": "после того, как кандидат представит своих рекомендателей", "after they have selected their preferred time slot": "после того, как они выберут желаемое время", "after {delay} {unit} in this stage": "после {delay} {unit} на этом этапе", "agriculture / forestry / fishing": "сельское хозяйство / лесное хозяйство / рыболовство", "all candidates": "все кандидаты", "and": "и", "annually": "ежегодно", "anonymized candidate": "анонимизированный кандидат", "answer_saved": "Ответ сохранен!", "apprenticeships.gov.uk entity ID": "apprenticeships.gov.uk ID организации", "approval|approvals": "одобрение|approvals", "assisting / administration": "ассистирование / администрирование", "attempts_exhausted": "Вы исчерпали лимит попыток. Вы все еще можете отправить свой ответ, но рекрутер увидит количество ваших повторных попыток.", "attempts_left": "Попытки остались: {0}", "automatically": "автоматически", "automatically at {time}": "автоматически при {time}", "available_period": "Доступный период", "banking": "банковское дело", "between {start} and {end}": "между {start} и {end}", "bold": "", "bold-and-italic": "", "but not public": "но не публичный", "cNPS": "cNPS", "cNPS comment": "", "cNPS is an excellent indicator for aligning your recruitment team behind improving candidate experience.": "cNPS - это отличный показатель для того, чтобы направить усилия вашей команды по подбору персонала на улучшение качества работы с кандидатами.", "cNPS or Candidate Net Promoter Score is a measure to gauge your candidate satisfaction.": "cNPS или Candidate Net Promoter Score - это показатель для оценки удовлетворенности кандидатов.", "cNPS or Candidate Net Promoter Score is a measure to gauge your candidates' satisfaction with the hiring process.": "cNPS или Candidate Net Promoter Score - это показатель, позволяющий оценить удовлетворенность кандидатов процессом найма.", "cNPS responses": "ответы cNPS", "cNPS score": "", "cNPS survey": "Опрос cNPS", "cNPS survey form": "", "cNPS survey message for candidate": "Сообщение о результатах опроса cNPS для кандидата", "cNPS survey message with merge tag": "Сообщение об опросе cNPS с меткой слияния", "cNPS.question.here": "здесь", "candidate has tag {tag}": "", "candidate | candidates": "кандидат | кандидаты", "career page": "страница карьеры", "catering": "кейтеринг", "check the integration credentials": "проверьте учетные данные для интеграции", "click here to decline the invitation": "нажмите здесь, чтобы отклонить приглашение", "click here to request another": "нажмите здесь, чтобы запросить другой", "click the link below and re-connect by clicking the Save button": "перейдите по ссылке ниже и повторно подключитесь, нажав кнопку Сохранить", "clicked": "нажмите", "clicked conversion": "конверсия по клику", "clients": "клиенты", "color picker": "подборщик цветов", "confidential": "", "confirm": "Подтвердите", "construction / real estate": "строительство / недвижимость", "contains": "содер<PERSON><PERSON>т", "contains all": "содержит все", "culture / entertainment /recreation": "культура / развлечения / отдых", "current": "текущий", "customer service": "обслуживание клиентов", "data_use": "Ваши данные будут использованы для связи с вами по поводу будущих открытых вакансий.", "day": "день", "days": "<PERSON><PERSON><PERSON><PERSON>", "daysInStageShort": "d", "default": "по умолчанию", "does not contain": "не содержит", "draft": "проект", "due on {dueOnDate}": "в срок {dueOnDate}", "due {dueInDiff}": "причитает<PERSON>я {dueInDiff}", "durationpicker.custom": "Пользовательское", "e.g #007bff": "например, #007bff", "e.g. Feedback request": "", "e.g. Send thx 4 appl. or Send rejection letter": "Наприм<PERSON><PERSON>, Отправить Thx 4 заявки или Отправить письмо с отказом", "e.g. example.org": "например, example.org", "e.g. {imap.gmail.com:993/imap/ssl}INBOX or {outlook.office365.com:993/imap/ssl}INBOX": "наприм<PERSON>р, {imap.gmail.com:993/imap/ssl}INBOX или {outlook.office365.com:993/imap/ssl}INBOX.", "edited": "отредактировано", "edited by {userName}": "под редакцией {userName}", "education / science / research": "образование / наука / исследования", "electronics / telecommunication": "электроника / телекоммуникации", "elements.file.dndDescription": "elements.file.dndDescription", "elements.file.dndTitle": "elements.file.dndTitle", "elements.file.uploadButton": "elements.file.uploadButton", "elements.gallery.dndDescription": "", "elements.gallery.dndTitle": "", "elements.gallery.uploadButton": "", "elements.list.remove": "elements.list.remove", "elements.multifile.dndDescription": "elements.multifile.dndDescription", "elements.multifile.dndTitle": "elements.multifile.dndTitle", "energetics / natural resources": "энергетика / природные ресурсы", "every 10th minute (:00, :10, :20, :30, :40, :50)": "каждую 10-ю минуту (:00, :10, :20, :30, :40, :50)", "every 15th minute (:00, :15, :30, :45)": "каждые 15 минут (:00, :15, :30, :45)", "failed": "не удалось", "feedback_title": "Вы можете оставить дополнительный отзыв:", "finance": "финан<PERSON>ы", "finished": "готово", "form": "форма", "formbuilder.items.label": "Этикетка", "from 1 candidate|from {count} candidates": "от 1 кандидата|от {count} кандидатов", "from global settings": "из глобальных настроек", "from project": "из проекта", "from team :team": "из команды :команда", "full hour (:00)": "полный час (:00)", "full seat|full seats": "полное сиденье|полные сиденья", "get insights": "получить информацию", "greater than": "больше, чем", "has all of": "имеет все", "has already submitted reference": "уже представил рекомендацию", "has none": "не имеет", "has none of": "не имеет ни одного из", "has one of": "имеет один из", "health care / social work": "здравоохранение / социальная работа", "hello": "Здравствуйте, {name}!", "here": "здесь", "hourly": "почасовая", "hours": "часы", "human resources / training": "человеческие ресурсы / обучение", "i_consent": "Я даю свое согласие на обработку моих данных на сайте {org} для связи со мной по поводу вакансий до {date}", "i_dont_consent": "Я не даю согласия на дальнейшую обработку моих персональных данных", "if": "", "if this is a video call interview": "если это собеседование по видеосвязи", "if you think this is a mistake, activate the integration again": "Если вы считаете, что это ошибка, активируйте интеграцию снова", "iframe URL": "URL-ад<PERSON><PERSON><PERSON> iframe", "iframe from URL": "iframe из URL", "immediately after saving this form": "сразу после сохранения этой формы", "in progress": "в процессе", "in project {project}": "", "in stage {stage}": "", "in {anonymizeTime}": "в {anonymizeTime}", "in {project}": "в {project}", "in {renewalTime}": "в {renewalTime}", "inactive": "неактивный", "info": "На вашу электронную почту отправлено событие календаря. Вы можете закрыть это окно. Вы можете вернуться к этому окну в любое время, чтобы посмотреть время собеседования.", "information technology / e-commerce": "информационные технологии / электронная коммерция", "interview": "интервью", "interview_length": "Продолжительность интервью", "interview_scheduled": "Интервью назначено", "interview_scheduling": "Планирование собеседований", "interview_slot_text": "Интервью", "intro": "{name} с сайта {org} просит вашего согласия на дальнейшую обработку ваших персональных данных. Эти данные могут включать вашу контактную информацию, резюме и другие данные, которые вы предоставили при подаче заявки на вакансию в {org}.", "intro_1": "Вас пригласили на видеоинтервью. Рекрутер подготовил для вас один вопрос. | Вы были приглашены на видеоинтервью. Рекрутер подготовил для вас {count} вопросов.", "intro_2": "Когда вы нажмете на кнопку запуска, вам будет предложено дать разрешение на использование веб-камеры и микрофона.", "intro_3": "NB! Не забудьте нажать кнопку \"Отправить\" для каждого ответа, который вы хотите отправить.", "invite": "пригласить", "invited": "приглашенные", "invite|invites": "приглашать|invites", "is false": "ложный", "is near": "рядом", "is one of": "один из", "is true": "правда", "italic": "", "job_ad_category_export": "Экспорт", "key": "", "know more about {candidateName}": "узнать больше о {candidateName}", "last contacted :timeAgo": "Последний контакт :timeAgo", "law": "закон", "less than": "менее", "limited seat|limited seats": "ограниченное место|ограниченное количество мест", "log in": "войти в систему", "mailbox": "почтовый ящик", "management": "управление", "managers": "менеджеры", "manufacturing / production": "изготовление / производство", "marketing / advertising / pr": "маркетинг / реклама / pr", "mechanics / engineering": "механика / машиностроение", "media / new media / creative": "медиа / новые медиа / креатив", "messages.cta_text": "сообщения.cta_text", "messages.cta_url": "messages.cta_url", "messages.success_text": "messages.success_text", "minutes": "минут", "missing email address": "", "missing phone number": "", "mo": "мо", "month": "мес<PERSON><PERSON>", "monthly": "ежемесячно", "no responses yet": "ответов пока нет", "no_available_slots": "К сожалению, свободных дней для собеседования не осталось. Пожалуйста, свяжитесь с вашим рекрутером.", "no_camera": "Если в вашем текущем устройстве нет камеры, воспользуйтесь смартфоном.", "no_camera_step_1": "Откройте камеру", "none": "нет", "normal": "", "not any of": "ни один из", "not opened": "не открыт", "of type": "", "ok": "хорошо", "on hold": "в режиме ожидания", "opened": "открыл", "opened conversion": "открытое преобразование", "optional": "опция", "or": "или", "per annum": "в год", "per day": "в день", "per hour": "в час", "play_recording": "Воспроизведение записи", "policy_review": "После ознакомления с нашей политикой конфиденциальности <a href=\"{url}\" class=\"text-medium\" target=\"_blank\"></a> , пожалуйста, примите решение ниже:", "present": "настоящее", "privacy policy": "политика конфиденциальности", "private": "частный", "project is in status {status}": "", "public": "публичный", "public / governmental service": "общественная / государственная служба", "publish your job ads in various job portals": "", "quarter": "квартал", "question_no": "Вопрос #{0}", "record": "Запись", "record_again": "Отбросьте видео и запишите снова", "record_new": "Запись нового видео", "recorder.confirm": "Подтвердите", "recorder.ok": "Видео ОК:", "recorder.play_recording": "Воспроизведение записи", "recorder.record": "Запись", "recorder.record_again": "Отбросьте видео и запишите снова", "recorder.record_new": "Запись нового видео", "recorder.stop": "Остановить запись", "recorder.time_left": "Оставшееся время", "recorder.warning_not_sent_user": "Видео еще не сохранено. Нажмите Подтвердить, чтобы сохранить вопрос.", "reply to this message to contact support": "ответьте на это сообщение, чтобы связаться со службой поддержки", "revocation": "отзыв", "sales / retail": "продажи / розничная торговля", "schedule_button": "Запланируйте интервью", "seat|seats": "сиденье|seats", "seconds": "секунды", "security": "безопасность", "see incoming e-mails from candidates directly on the candidate profile": "", "see your existing calendar events when scheduling interviews": "", "select_time": "Пожалуйста, выберите время собеседования", "send candidate information to your HR platform": "", "slot_selector_help": "Нажмите, чтобы выбрать время для собеседования. Вы можете перетащить слот или удалить его и выбрать заново, если хотите изменить время.", "sourced": "получено", "ss.lv images": "", "stage": " этап", "start": "Начало", "stop": "Остановить запись", "submit": "Отправить", "talent pool": "кадровый резерв", "thanks": "Спасибо! Мы получили ваши ответы!", "thanks_ok": "Спасибо! Ваше собеседование назначено на", "time_full": "Кто-то уже забронировал это время.", "time_left": "Оставшееся время", "time_limit": "Ограничение времени ответа {0}", "title": "название", "to_here": "здесь", "tourism / hotels": "туризм / гостиницы", "transportation / logistics": "транспорт / логистика", "trix.acceptedExtensions": "trix.acceptedExtensions", "trix.acceptedMimes": "trix.acceptedMimes", "undefined": "неопределенный", "unknown": "неизвестно", "upgrade": "обновление", "video_info": "Присоединяйтесь к видеоинтервью здесь:", "video_info_password": "Пароль для видеозвонков:", "video_ok": "Видео OK!", "view_question": "Посмотреть вопрос", "voluntary work": "добровольная работа", "warning_not_sent": "Ответ еще не отправлен. Нажмите кнопку Отправить, чтобы отправить свой ответ.", "week": "неделя", "when a button is clicked": "при нажатии на кнопку", "when this template is used for video call interviews": "когда этот шаблон используется для собеседований по видеосвязи", "with status": "", "work at sea": "работа в море", "you": "вы", "{0} There are no more open slots in this interview.|{1} There is one open slot in this interview.|[2,*] There are :count open slots in this interview.": "{0} В этом интервью больше нет свободных мест.|{1} В этом интервью есть одно свободное место.|[2,*] В этом интервью есть :count свободных мест.", "{0} are no more open slots|{1} is one open slot|[2,*] are :count open slots": "{0} больше нет свободных слотов|{1} есть один свободный слот| [2,*] есть :считанные свободные слоты", "{1} :count project from this template|[2,*] :count projects from this template": "{1}:count project from this template|[2,*] :count projects from this template", "{count} actions in {status} projects": "", "{count} received": "{count} получено", "{count} stage actions to reassign": "", "{count} stage actions will be deleted": "", "{count} stage|{count} stages": "{count} этап|{count} этапа|{count} этапов", "{count} user": "", "{dataLength} of {dataTotal}": "{dataLength} из {dataTotal}", "{delay} {unit} after the project status changes": "", "{mergeTag} will be blank.": "", "{selected} of {total} candidates selected": "{selected} отобранных кандидатов {total} ", "{selected} of {total} selected": "{selected} из {total} выбранных", "{total} of {total} selected": "{total} из {total} выбранных", "{upgradeCTA} to get access to all of our video features.": "{upgradeCTA} чтобы получить доступ ко всем нашим видеофункциям.", "{upgradeCTA} to get access to all of our video interview analysis tools.": "{upgradeCTA} чтобы получить доступ ко всем нашим инструментам для анализа видеоинтервью.", "{userName} shared {candidateCount} candidates": "", "{userName} shared {candidateCount} candidates for {projectName}": "{userName} общий {candidateCount} кандидатов на {projectName}", "{user} shared 1 candidate for {position} | {user} shared {count} candidates for {position}": "", "{user} shared 1 candidate | {user} shared {count} candidates": ""}