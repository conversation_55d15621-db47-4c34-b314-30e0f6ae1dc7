{"# of Members": "# of Members", "# of candidates": "# of candidates", "(and 1 finished project)|(and {count} finished projects)": "(and 1 finished project)|(and {count} finished projects)", "(with API key)": "(with API key)", "+ {count} more": "+ {count} more", "-> Hired": "-> <PERSON><PERSON>", "-> Interviews": "-> Interviews", "-> Offers": "-> Offers", "-> Rejected": "-> Rejected", "... and much more": "... and much more", "...or fill out a form": "...or fill out a form", ".xlsx file": ".xlsx file", "0 means the same day that the action is triggered, 1 means the next day, etc.": "0 means the same day that the action is triggered, 1 means the next day, etc.", "1 SMS sent | {count} SMS sent": "1 SMS sent | {count} SMS sent", "1 accepted | {count} accepted": "1 accepted | {count} accepted", "1 answered | {count} answered": "1 answered | {count} answered", "1 attempt left|{count} attempts left": "1 attempt left|{count} attempts left", "1 attempt|{count} attempts": "1 attempt|{count} attempts", "1 candidate|{count} candidates": "1 candidate|{count} candidates", "1 comment | {count} comments": "1 comment | {count} comments", "1 day in stage | {count} days in stage": "1 day in stage | {count} days in stage", "1 day | {n} days": "1 day | {n} days", "1 message | {count} messages": "1 message | {count} messages", "1 month": "1 month", "1 open slot | {count} open slots": "1 open slot | {count} open slots", "1 pending | {count} pending": "1 pending | {count} pending", "1 project from this template|{count} projects from this template": "1 project from this template|{count} projects from this template", "1 referee | {count} referees": "1 referee | {count} referees", "1 slot|{count} slots": "1 slot|{count} slots", "1 submitted | {count} submitted": "1 submitted | {count} submitted", "1 year": "1 year", "1/2 Text with image": "1/2 Text with image", "1/2 Text with video": "1/2 Text with video", "10 days": "10 days", "10 minutes": "10 minutes", "10 minutes (:00, :10, :20...)": "10 minutes (:00, :10, :20...)", "12M": "12M", "14 days": "14 days", "15 minutes": "15 minutes", "15 minutes (:00, :15, :30, :45)": "15 minutes (:00, :15, :30, :45)", "1st Group Disabled": "1st Group Disabled", "2 Options": "2 Options", "2 months": "2 months", "2/3 Text with square image": "2/3 Text with square image", "2nd Group Disabled": "2nd Group Disabled", "3 days": "3 days", "3 months": "3 months", "30 days": "30 days", "30 minutes": "30 minutes", "30D": "30D", "3rd Group Disabled": "3rd Group Disabled", "4 Options": "4 Options", "6 months": "6 months", "60 minutes": "60 minutes", "6M": "6M", "7 days": "7 days", "9 days later we purge your database of all candidates data which we didn't get a processing consent for": "9 days later we purge your database of all candidates data which we didn't get a processing consent for", "90D": "90D", ":actingUser has assigned you as project manager in the project :positionName.": ":acting<PERSON><PERSON> has assigned you as project manager in the project :positionName.", ":attribute cannot contain the @ symbol when publishing to CV Keskus.": ":attribute cannot contain the @ symbol when publishing to CV Keskus.", ":author assigned you a task|:author assigned you :count tasks": ":author assigned you a task|:author assigned you :count tasks", ":count accepted interview times": ":count accepted interview times", ":count answered video interviews": ":count answered video interviews", ":count miscellaneous updates": ":count miscellaneous updates", ":count new candidates": ":count new candidates", ":count updates to tasks": ":count updates to tasks", ":firstName tagged you in a comment about :candidateName": ":<PERSON><PERSON><PERSON> tagged you in a comment about :candidate<PERSON><PERSON>", ":fromUser invited you to work on project :positionName.": ":fromUser invited you to work on project :positionName.", ":fromUser needs your help - :positionName": ":from<PERSON><PERSON> needs your help - :positionName", ":managerName needs your attention for candidates in :stageName stage:": ":manager<PERSON><PERSON> needs your attention for candidates in :stageName stage:", ":name changed the deadline for reviewing the job requisition for :position. The new deadline is in :deadline.": ":name changed the deadline for reviewing the job requisition for :position. The new deadline is in :deadline.", ":name marked you as recruiter for :position": ":name marked you as recruiter for :position", ":name marked you as the recruiter in the job requisition for :position. You don't need to do anything right now. This is just a heads-up.": ":name marked you as the recruiter in the job requisition for :position. You don't need to do anything right now. This is just a heads-up.", ":name needs your approval for :position": ":name needs your approval for :position", ":name requested your approval for a job requisition for :position.": ":name requested your approval for a job requisition for :position.", ":name wrote:": ":name wrote:", ":startTime :event with :people": ":startTime :event with :people", ":user shared :count candidates": ":user shared :count candidates", ":user shared :count candidates for :position:": ":user shared :count candidates for :position:", ":user tagged you in :position log entry": ":user tagged you in :position log entry", ":userName cancelled the event because they are unable to participate.": ":userName cancelled the event because they are unable to participate.", ":userName cancelled the interview.": ":userName cancelled the interview.", ":userName has invited you to use Teamdash": ":userName has invited you to use Teamdash", ":userName has invited you to work on the project :projectName.": ":userName has invited you to work on the project :projectName.", "< {distance} from": "< {distance} from", "A calendar event has been sent to your e-mail. You can close this window. You can come back to this url any time to see your interview time.": "A calendar event has been sent to your e-mail. You can close this window. You can come back to this url any time to see your interview time.", "A few days before sending out the renewal requests, you get a report - how many and which candidates are going to get renewal messages.": "A few days before sending out the renewal requests, you get a report - how many and which candidates are going to get renewal messages.", "A few ideas:": "A few ideas:", "A fresh verification link has been sent to your email address.": "A fresh verification link has been sent to your email address.", "A job has been unpublished: :position": "A job has been unpublished: :position", "A new job has been posted: :position": "A new job has been posted: :position", "A positive candidate experience can help you attract and hire the best people.": "A positive candidate experience can help you attract and hire the best people.", "A reference has already been requested from this person at {lastMessageTime}.": "A reference has already been requested from this person at {lastMessageTime}.", "A simple text block": "A simple text block", "A single sentence summarizing a specific aspect of the interview.": "A single sentence summarizing a specific aspect of the interview.", "A small heads-up!": "A small heads-up!", "A text string that contains the text representing a transcribed segment of speech.": "A text string that contains the text representing a transcribed segment of speech.", "A timestamp that represents when the segment of speech occurred within the original recording.": "A timestamp that represents when the segment of speech occurred within the original recording.", "A video call link will be generated and included in calendar invites.": "A video call link will be generated and included in calendar invites.", "AI": "AI", "AI Assistance": "AI Assistance", "AI Provider": "AI Provider", "AI features must be enabled for the AI screener to work. Please contact your account manager.": "AI features must be enabled for the AI screener to work. Please contact your account manager.", "AI-generated analysis": "AI-generated analysis", "API": "API", "API documentation": "API documentation", "API key": "API key", "API keys": "API keys", "API token": "API token", "Abkhazian": "Abkhazian", "About employer": "About employer", "Abrasives and Nonmetallic Minerals Manufacturing": "Abrasives and Nonmetallic Minerals Manufacturing", "Accept DPA": "Accept DPA", "Accept Terms of use": "Accept Terms of use", "Accepted interview invite": "Accepted interview invite", "Access URL": "Access URL", "Access controls": "Access controls", "Access key": "Access key", "Accomodation Services": "Accomodation Services", "Account inactive": "Account inactive", "Accounting": "Accounting", "Accounting / Auditing": "Accounting / Auditing", "Accounting/Financial/Insurance": "Accounting/Financial/Insurance", "Action Needed: :integration integration broken": "Action Needed: :integration integration broken", "Action name": "Action name", "Actions": "Actions", "Active": "Active", "Active consent of type": "Active consent of type", "Active in other projects:": "Active in other projects:", "Active projects": "Active projects", "Active until": "Active until", "Activities to report": "Activities to report", "Activity custom fields": "Activity custom fields", "Activity report": "Activity report", "Ad unpublished!": "Ad unpublished!", "Add": "Add", "Add Block": "Add Block", "Add a comment to start the conversation.": "Add a comment to start the conversation.", "Add a copy of the file to current project": "Add a copy of the file to current project", "Add a descriptive project title": "Add a descriptive project title", "Add a dropout reason": "Add a dropout reason", "Add a list of requirements to the project description": "Add a list of requirements to the project description", "Add a new candidate": "Add a new candidate", "Add a new stage": "Add a new stage", "Add a quote from the Manager, it's the most important trust element": "Add a quote from the Manager, it's the most important trust element", "Add actions to this project to automate your workflow.": "Add actions to this project to automate your workflow.", "Add actions to this stage to automate your workflow.": "Add actions to this stage to automate your workflow.", "Add another candidate": "Add another candidate", "Add another reference": "Add another reference", "Add attachment": "Add attachment", "Add authentication provider": "Add authentication provider", "Add automated project action": "Add automated project action", "Add bulk comments": "Add bulk comments", "Add bulk tags": "Add bulk tags", "Add candidate": "Add candidate", "Add candidate referee": "Add candidate referee", "Add candidates to existing set": "Add candidates to existing set", "Add child": "Add child", "Add client": "Add client", "Add comment": "Add comment", "Add comment to decision": "Add comment to decision", "Add condition": "Add condition", "Add consent": "Add consent", "Add consent subtype": "Add consent subtype", "Add contact": "Add contact", "Add contact to ": "Add contact to ", "Add custom fonts": "Add custom fonts", "Add dropout reason": "Add dropout reason", "Add education": "Add education", "Add employment": "Add employment", "Add entry": "Add entry", "Add existing users...": "Add existing users...", "Add field": "Add field", "Add file": "Add file", "Add file type": "Add file type", "Add filter": "Add filter", "Add import": "Add import", "Add integration": "Add integration", "Add link": "Add link", "Add location": "Add location", "Add mail identity": "Add mail identity", "Add my score": "Add my score", "Add new action": "Add new action", "Add new candidate reason": "Add new candidate reason", "Add new client": "Add new client", "Add new company reason": "Add new company reason", "Add new criterion": "Add new criterion", "Add new integration": "Add new integration", "Add new location": "Add new location", "Add new manager": "Add new manager", "Add new role": "Add new role", "Add new tag": "Add new tag", "Add office": "Add office", "Add project action": "Add project action", "Add project failure reason": "Add project failure reason", "Add project log entry": "Add project log entry", "Add project role": "Add project role", "Add provider": "Add provider", "Add quote": "Add quote", "Add rating": "Add rating", "Add reference form": "Add reference form", "Add scorecard": "Add scorecard", "Add stage": "Add stage", "Add stage action": "Add stage action", "Add tag": "Add tag", "Add tags": "Add tags", "Add tags to the candidate": "Add tags to the candidate", "Add team": "Add team", "Add the tag if...": "Add the tag if...", "Add to favourites": "Add to favourites", "Add to project": "Add to project", "Add to this project": "Add to this project", "Add user": "Add user", "Add video interview": "Add video interview", "Add your comment here...": "Add your comment here...", "Add {query}": "Add {query}", "Add-ons": "Add-ons", "Added": "Added", "Added a dropout reason": "Added a dropout reason", "Added at": "Added at", "Added comment": "Added comment", "Added from API import": "Added from API import", "Added from XLSX import": "Added from XLSX import", "Added from sourcing extension": "Added from sourcing extension", "Added from xlsx import": "Added from xlsx import", "Added manual source override": "Added manual source override", "Added to landing {landingLink}": "Added to landing {landingLink}", "Additional city (CVbankas)": "Additional city (CVbankas)", "Additional city 2 (CVbankas)": "Additional city 2 (CVbankas)", "Additional industries (CV-Library)": "Additional industries (CV-Library)", "Additional info form": "Additional info form", "Additional information form": "Additional information form", "Additional information forms": "Additional information forms", "Additional options": "Additional options", "Address": "Address", "Address (SS.lv)": "Address (SS.lv)", "Adds hidden custom fields and a diversity questionnaire.": "Adds hidden custom fields and a diversity questionnaire.", "Admin": "Admin", "Administration": "Administration", "Administration & Support": "Administration & Support", "Administration of Justice": "Administration of Justice", "Administration/work safety": "Administration/work safety", "Administrative": "Administrative", "Administrative and Support Services": "Administrative and Support Services", "Administrator": "Administrator", "Advert type (profession.hu)": "Advert type (profession.hu)", "Advertising": "Advertising", "Advertising Services": "Advertising Services", "Afar": "Afar", "Afrikaans": "Afrikaans", "After completing the API key steps, check your browser address bar": "After completing the API key steps, check your browser address bar", "After school": "After school", "After you have reviewed the details you will be redirected to the checkout page. After successful payment your campaign details are reviewed by a human at Teamdash. You'll get a notification once your campaign has been approved and launched.": "After you have reviewed the details you will be redirected to the checkout page. After successful payment your campaign details are reviewed by a human at Teamdash. You'll get a notification once your campaign has been approved and launched.", "Age": "Age", "Agency": "Agency", "Agency has clients, corporation has managers.": "Agency has clients, corporation has managers.", "Agricultural Chemical Manufacturing": "Agricultural Chemical Manufacturing", "Agriculture": "Agriculture", "Agriculture / Environmental": "Agriculture / Environmental", "Agriculture / Forestry / Fishing": "Agriculture / Forestry / Fishing", "Agriculture, Construction, Mining Machinery Manufacturing": "Agriculture, Construction, Mining Machinery Manufacturing", "Air, Water, and Waste Program Management": "Air, Water, and Waste Program Management", "Airlines and Aviation": "Airlines and Aviation", "Akan": "<PERSON><PERSON>", "Albanian": "Albanian", "Align text to center": "Align text to center", "All": "All", "All GDPR messages to candidates will be sent from this user.": "All GDPR messages to candidates will be sent from this user.", "All applications that are currently marked with the dropout reason {originalDropoutReason} will be changed to {mergedDropoutReason}.": "All applications that are currently marked with the dropout reason {originalDropoutReason} will be changed to {mergedDropoutReason}.", "All day": "All day", "All files of this type will appear as {baseFileType} after deletion.": "All files of this type will appear as {baseFileType} after deletion.", "All forms": "All forms", "All invites can be delivered via selected channels": "All invites can be delivered via selected channels", "All is good": "All is good", "All project users": "All project users", "All references": "All references", "All responses": "All responses", "All rights reserved.": "All rights reserved.", "All shared candidates": "All shared candidates", "All slots in this interview have been cancelled. Please add more slots.": "All slots in this interview have been cancelled. Please add more slots.", "All stages in a project template must be categorized.": "All stages in a project template must be categorized.", "All stages in all projects are categorized.": "All stages in all projects are categorized.", "All times :tz (:offset).": "All times :tz (:offset).", "All times are {timezone}": "All times are {timezone}", "All types": "All types", "All users": "All users", "All users of this instance must verify their phone numbers.": "All users of this instance must verify their phone numbers.", "Allocation & Distribution": "Allocation & Distribution", "Allow candidates without email": "Allow candidates without email", "Allow candidates without location": "Allow candidates without location", "Allow direct applications from job portals": "Allow direct applications from job portals", "Allow limited users to schedule interviews": "Allow limited users to schedule interviews", "Allow login with password": "Allow login with password", "Allow password logins": "Allow password logins", "Allow regular users to create tags": "Allow regular users to create tags", "Alphabetic": "Alphabetic", "Alphabetically": "Alphabetically", "Alternative Dispute Resolution": "Alternative Dispute Resolution", "Alternative Medicine": "Alternative Medicine", "Always ask approval from these users": "Always ask approval from these users", "Always ask requisition approval from users": "Always ask requisition approval from users", "Ambulance Services": "Ambulance Services", "Amharic": "Amharic", "Amusement Parks and Arcades": "Amusement Parks and Arcades", "An array containing segments of transcribed speech, ordered by when the speech occurred.": "An array containing segments of transcribed speech, ordered by when the speech occurred.", "An array in which each element is a summarization sentence.": "An array in which each element is a summarization sentence.", "An error occurred": "An error occurred", "An error occurred when starting the camera.": "An error occurred when starting the camera.", "An error occurred while processing the transcript.": "An error occurred while processing the transcript.", "An error occurred!": "An error occurred!", "An object representing a single transcribed segment of speech.": "An object representing a single transcribed segment of speech.", "An unexpected error occurred.": "An unexpected error occurred.", "Analyse again": "Analyse again", "Analyst": "Analyst", "And the candidate will get a message like this:": "And the candidate will get a message like this:", "Animal Feed Manufacturing": "Animal Feed Manufacturing", "Animation and Post-production": "Animation and Post-production", "Anonymize": "Anonymize", "Anonymize candidate": "Anonymize candidate", "Anonymized CV": "Anonymized CV", "Anonymized candidate": "Anonymized candidate", "Anonymized summary": "Anonymized summary", "Answer time": "Answer time", "Answered video interview": "Answered video interview", "Answers accepted in strict order": "Answers accepted in strict order", "Answers allowed in any order": "Answers allowed in any order", "Answers only visible to admins": "Answers only visible to admins", "Any": "Any", "Any account": "Any account", "Any data you have entered in this form must be present on the landing page and vice versa.": "Any data you have entered in this form must be present on the landing page and vice versa.", "Any time when available": "Any time when available", "Any type": "Any type", "Apparel Manufacturing": "Apparel Manufacturing", "Appearance": "Appearance", "Appliances, Electrical, and Electronics Manufacturing": "Appliances, Electrical, and Electronics Manufacturing", "Applicants target stage": "Applicants target stage", "Application date": "Application date", "Application date and time format": "Application date and time format", "Application deadline": "Application deadline", "Application form": "Application form", "Application forms": "Application forms", "Application language": "Application language", "Application methods": "Application methods", "Applications": "Applications", "Applied higher education": "Applied higher education", "Applies to all user signatures.": "Applies to all user signatures.", "Apply colours to all links": "Apply colours to all links", "Apply with resume": "Apply with resume", "Applying to this position has ended.": "Applying to this position has ended.", "Apprenticeship": "Apprenticeship", "Approval needed from": "Approval needed from", "Approvals": "Approvals", "Approvals deadline": "Approvals deadline", "Approve": "Approve", "Approved": "Approved", "Approved at": "Approved at", "April": "April", "Arabic": "Arabic", "Aragonese": "Aragonese", "Architectural and Structural Metal Manufacturing": "Architectural and Structural Metal Manufacturing", "Architecture and Planning": "Architecture and Planning", "Archive": "Archive", "Archived": "Archived", "Are Ukrainians welcome? (CVBankas)": "Are Ukrainians welcome? (<PERSON><PERSON><PERSON><PERSON>)", "Are you sure you want to archive this form?": "Are you sure you want to archive this form?", "Are you sure you want to cancel sending message \"{subject}\"?": "Are you sure you want to cancel sending message \"{subject}\"?", "Are you sure you want to cancel the meeting with {candidateName}?": "Are you sure you want to cancel the meeting with {<PERSON><PERSON><PERSON>}?", "Are you sure you want to deactivate this user?": "Are you sure you want to deactivate this user?", "Are you sure you want to delete action {actionName}?": "Are you sure you want to delete action {actionName}?", "Are you sure you want to delete consent subtype: {consentSubtypeName}?": "Are you sure you want to delete consent subtype: {consentSubtypeName}?", "Are you sure you want to delete dropout reason {dropoutReason}?": "Are you sure you want to delete dropout reason {dropoutReason}?", "Are you sure you want to delete file type {fileType}? ": "Are you sure you want to delete file type {fileType}? ", "Are you sure you want to delete file {fileName}?": "Are you sure you want to delete file {fileName}?", "Are you sure you want to delete project role {role}? ": "Are you sure you want to delete project role {role}? ", "Are you sure you want to delete the API key {apiKeyName}? ": "Are you sure you want to delete the API key {apiKeyName}? ", "Are you sure you want to delete the landing page {landingName}?": "Are you sure you want to delete the landing page {landingName}?", "Are you sure you want to delete the project failure reason \"{projectFailureReason}\"?": "Are you sure you want to delete the project failure reason \"{projectFailureReason}\"?", "Are you sure you want to delete this API key?": "Are you sure you want to delete this API key?", "Are you sure you want to delete this client?": "Are you sure you want to delete this client?", "Are you sure you want to delete this comment?": "Are you sure you want to delete this comment?", "Are you sure you want to delete this custom activity?": "Are you sure you want to delete this custom activity?", "Are you sure you want to delete this event slot?": "Are you sure you want to delete this event slot?", "Are you sure you want to delete this image?": "Are you sure you want to delete this image?", "Are you sure you want to delete this tag? It is currently associated with {count} landings.": "Are you sure you want to delete this tag? It is currently associated with {count} landings.", "Are you sure you want to generate a new API key for this integration?": "Are you sure you want to generate a new API key for this integration?", "Are you sure you want to permanently delete this chat?": "Are you sure you want to permanently delete this chat?", "Are you sure you want to permanently delete this interview?": "Are you sure you want to permanently delete this interview?", "Are you sure you want to permanently delete this video?": "Are you sure you want to permanently delete this video?", "Are you sure you want to unpublish job ad {position_name} from {count} job portals?": "Are you sure you want to unpublish job ad {position_name} from {count} job portals?", "Are you sure you want to unpublish job ad {position_name}?": "Are you sure you want to unpublish job ad {position_name}?", "Area Management": "Area Management", "Armed Forces": "Armed Forces", "Armenian": "Armenian", "Art / Creative": "Art / Creative", "Artificial Rubber and Synthetic Fiber Manufacturing": "Artificial Rubber and Synthetic Fiber Manufacturing", "Artists and Writers": "Artists and Writers", "Arts": "Arts", "Arts/Graphic Design": "Arts/Graphic Design", "As the transcript text has been auto-generated, it might include spelling errors.": "As the transcript text has been auto-generated, it might include spelling errors.", "Ask AI to write": "Ask AI to write", "Ask for data processing consent until": "Ask for data processing consent until", "Ask for reference": "Ask for reference", "Ask for reference again": "Ask for reference again", "Ask for references": "Ask for references", "Ask referee to submit evaluation": "Ask referee to submit evaluation", "Ask user for feedback about hiring process": "Ask user for feedback about hiring process", "Assamese": "Assamese", "Assigned to": "Assigned to", "Assignee": "Assignee", "Assistant Management": "Assistant Management", "Assistant, Customer Support Specialist, Sales Specialist, Аccount Мanager...": "Assistant, Customer Support Specialist, Sales Specialist, Аccount <PERSON><PERSON>ger...", "Assisting / Administration": "Assisting / Administration", "Associate": "Associate", "Associate an existing landing page with this project": "Associate an existing landing page with this project", "Associate's degree": "Associate's degree", "Assure that declining this consent will not affect any pending candidacy.": "Assure that declining this consent will not affect any pending candidacy.", "Async video interview invite": "Async video interview invite", "Asynchronous Video Interviews": "Asynchronous Video Interviews", "Asynchronous video interview": "Asynchronous video interview", "Attachments": "Attachments", "Attachments from current project": "Attachments from current project", "Attachments from other projects": "Attachments from other projects", "Attachments not linked to any project": "Attachments not linked to any project", "Attempts exhausted": "Attempts exhausted", "Audio and Video Equipment Manufacturing": "Audio and Video Equipment Manufacturing", "Audit Logging": "<PERSON>t Logging", "Audit log": "Audit log", "August": "August", "Authentication providers": "Authentication providers", "Author": "Author", "Auto sync": "Auto sync", "Auto-add tags AI": "Auto-add tags AI", "Automate repetitive tasks and communications to save time and improve the candidate experience.": "Automate repetitive tasks and communications to save time and improve the candidate experience.", "Automated User Provisioning (SCIM)": "Automated User Provisioning (SCIM)", "Automatic": "Automatic", "Automatic - after candidate has submitted a form": "Automatic - after candidate has submitted a form", "Automatic - after candidate has submitted referees": "Automatic - after candidate has submitted referees", "Automatic - after project status change": "Automatic - after project status change", "Automatic - after receiving video interview response": "Automatic - after receiving video interview response", "Automatic - after time in stage": "Automatic - after time in stage", "Automatic - inherit from landing": "Automatic - inherit from landing", "Automatic - specific time in future": "Automatic - specific time in future", "Automatic actions": "Automatic actions", "Automatic actions scheduled on a specific date are not cloned.": "Automatic actions scheduled on a specific date are not cloned.", "Automatic heading sizing": "Automatic heading sizing", "Automatic summaries": "Automatic summaries", "Automatically archive landing pages after application deadline": "Automatically archive landing pages after application deadline", "Automatically archives landing pages after their application deadlines have passed. Applies retroactively to old landing pages.": "Automatically archives landing pages after their application deadlines have passed. Applies retroactively to old landing pages.", "Automation Machinery Manufacturing": "Automation Machinery Manufacturing", "Automations": "Automations", "Automotive/Aerospace": "Automotive/Aerospace", "Autoplay": "Autoplay", "Auxiliary Worker": "Auxiliary Worker", "Available days": "Available days", "Available merge tags": "Available merge tags", "Available period": "Available period", "Available time end": "Available time end", "Available time start": "Available time start", "Avaric": "<PERSON><PERSON>", "Average": "Average", "Average rating": "Average rating", "Average time from candidate submission to hire based on people hired in the last :days days.": "Average time from candidate submission to hire based on people hired in the last :days days.", "Average time from project start to end for projects finished in the last :days days.": "Average time from project start to end for projects finished in the last :days days.", "Average time in stage": "Average time in stage", "Avestan": "Avestan", "Avg. candidates": "Avg. candidates", "Aviation and Aerospace Component Manufacturing": "Aviation and Aerospace Component Manufacturing", "Avoid corporate clichés": "Avoid corporate clichés", "Aymara": "Aymara", "Azerbaijani": "Azerbaijani", "Azure OpenAI (data never leaves EU)": "Azure OpenAI (data never leaves EU)", "Bachelor's degree": "Bachelor's degree", "Back": "Back", "Back to Talent Pool": "Back to Talent Pool", "Back to candidate profile": "Back to candidate profile", "Back to client details": "Back to client details", "Back to clients list": "Back to clients list", "Back to project": "Back to project", "Back to projects list": "Back to projects list", "Back to stage": "Back to stage", "Background": "Background", "Background color": "Background color", "Bad application conversion": "Bad application conversion", "Bad click conversion": "Bad click conversion", "Baked Goods Manufacturing": "Baked Goods Manufacturing", "Baltics": "Baltics", "Bambara": "Bambara", "Bank account": "Bank account", "Banking": "Banking", "Banking / Insurance": "Banking / Insurance", "Banner image": "Banner image", "Banner image for job ad": "Banner image for job ad", "Bars, Taverns, and Nightclubs": "Bars, Taverns, and Nightclubs", "Bashkir": "Bashkir", "Basic": "Basic", "Basic education": "Basic education", "Basic information": "Basic information", "Basics": "Basics", "Basque": "Basque", "Be sure to include their personal invite link:": "Be sure to include their personal invite link:", "Bed-and-Breakfasts, Hostels, Homestays": "Bed-and-Breakfasts, Hostels, Homestays", "Before proceeding, please check your email for a verification link.": "Before proceeding, please check your email for a verification link.", "Before scheduling an interview, please associate the candidate with a project.": "Before scheduling an interview, please associate the candidate with a project.", "Belarusian": "Belarusian", "Below you can set custom categories for your stages. This will help you get statistics that are better aligned with your hiring process.": "Below you can set custom categories for your stages. This will help you get statistics that are better aligned with your hiring process.", "Benefits (CVO)": "Benefits (CVO)", "Bengali": "Bengali", "Better luck next time!": "Better luck next time!", "Beverage Manufacturing": "Beverage Manufacturing", "Bihari languages": "Bihari languages", "Billing address": "Billing address", "Biomass Electric Power Generation": "Biomass Electric Power Generation", "Biotechnology Research": "Biotechnology Research", "Birth date": "Birth date", "Bislama": "B<PERSON>lama", "Block background": "Block background", "Blockchain Services": "Blockchain Services", "Blogs": "Blogs", "Blue collar, workers": "Blue collar, workers", "Board": "Board", "Body": "Body", "Body text": "Body text", "Boilers, Tanks, and Shipping Container Manufacturing": "Boilers, Tanks, and Shipping Container Manufacturing", "Book Publishing": "Book Publishing", "Book a 20 min demo": "Book a 20 min demo", "Book and Periodical Publishing": "Book and Periodical Publishing", "Bosnian": "Bosnian", "Bounced at": "Bounced at", "Brand": "Brand", "Breton": "Breton", "Breweries": "Breweries", "Bring your own SMTP": "Bring your own SMTP", "Broadcast Media Production and Distribution": "Broadcast Media Production and Distribution", "Browse": "Browse", "Browse...": "Browse...", "Buffer time after interview": "Buffer time after interview", "Buffer time before interview": "Buffer time before interview", "Builder, Electrician, Driver, Tailor, Plumber, Cook, Teacher...": "<PERSON><PERSON><PERSON>, Electrician, Driver, Tailor, Plumber, Cook, Teacher...", "Building Construction": "Building Construction", "Building Equipment Contractors": "Building Equipment Contractors", "Building Finishing Contractors": "Building Finishing Contractors", "Building Structure and Exterior Contractors": "Building Structure and Exterior Contractors", "Bulgarian": "Bulgarian", "Bulk import users": "Bulk import users", "Burmese": "Burmese", "Business Consulting and Services": "Business Consulting and Services", "Business Content": "Business Content", "Business Development": "Business Development", "Business Intelligence Platforms": "Business Intelligence Platforms", "Business Skills Training": "Business Skills Training", "Button color": "Button color", "Button in stage": "<PERSON><PERSON> in stage", "Buttons": "Buttons", "Buying": "Buying", "Buying/supply": "Buying/supply", "By Project Manager": "By Project Manager", "By choice": "By choice", "By default, candidate profiles are pseudonymized using a one-way cryptographic function.  When they re-apply then their history (comments, projects) is restored. If you enable permanent deletion then candidate histories will not be restored.": "By default, candidate profiles are pseudonymized using a one-way cryptographic function.  When they re-apply then their history (comments, projects) is restored. If you enable permanent deletion then candidate histories will not be restored.", "By stage": "By stage", "By time": "By time", "By uploading your CV, you agree that our AI will process and extract relevant information to pre-fill the form fields. Click the link above to opt out and fill the form manually.": "By uploading your CV, you agree that our AI will process and extract relevant information to pre-fill the form fields. Click the link above to opt out and fill the form manually.", "CANCELLED": "CANCELLED", "CTA button text": "CTA button text", "CTA button url": "CTA button url", "CV": "CV", "Cable and Satellite Programming": "Cable and Satellite Programming", "Calendar": "Calendar", "Calendar invite details": "Calendar invite details", "Calendar invites FROM email address": "Calendar invites FROM email address", "Calendar navigation": "Calendar navigation", "Calendar time": "Calendar time", "Call": "Call", "Call (no answer)": "Call (no answer)", "Call time": "Call time", "Campaign is currently running": "<PERSON> is currently running", "Campaign type": "Campaign type", "Can administer teams": "Can administer teams", "Can be left empty": "Can be left empty", "Can see all projects?": "Can see all projects?", "Can we contact you about vacancies at [organization_name]?": "Can we contact you about vacancies at [organization_name]?", "Cancel": "Cancel", "Cancel interview": "Cancel interview", "Cancel sending": "Cancel sending", "Cancelled": "Cancelled", "Candidate": "Candidate", "Candidate CV": "Candidate CV", "Candidate CV summaries, similarity search, writing assistance, and more.": "Candidate CV summaries, similarity search, writing assistance, and more.", "Candidate GDPR consent validity": "Candidate GDPR consent validity", "Candidate ID": "Candidate ID", "Candidate ID (Teamdash)": "Candidate ID (Teamdash)", "Candidate Net Promoter Score based on surveys in the last :days days.": "Candidate Net Promoter Score based on surveys in the last :days days.", "Candidate Screening for {position}": "Candidate Screening for {position}", "Candidate added to project": "Candidate added to project", "Candidate answered": "Candi<PERSON> answered", "Candidate can't be analyzed as they have no CV.": "Candidate can't be analyzed as they have no CV.", "Candidate card settings": "Candidate card settings", "Candidate created date": "Candidate created date", "Candidate custom fields": "Candidate custom fields", "Candidate email": "Candidate email", "Candidate email (Teamdash)": "Candidate email (Teamdash)", "Candidate exceeded the attempt limit ({attemptLimitNumber}). There were {attemptCountNumber} attempts.": "Candidate exceeded the attempt limit ({attemptLimitNumber}). There were {attemptCountNumber} attempts.", "Candidate exists in {hrisName}": "Candidate exists in {hrisName}", "Candidate forms": "Candidate forms", "Candidate location": "Candidate location", "Candidate management": "Candidate management", "Candidate message": "Candidate message", "Candidate motivation, likes and dislikes": "Candidate motivation, likes and dislikes", "Candidate name": "Candidate name", "Candidate name (Teamdash)": "Candidate name (Teamdash)", "Candidate not in {hrisName}": "Candidate not in {hrisName}", "Candidate phone": "Candidate phone", "Candidate photo": "Candidate photo", "Candidate presentations": "Candidate presentations", "Candidate profile target field": "Candidate profile target field", "Candidate progression": "Candidate progression", "Candidate reach": "Candidate reach", "Candidate reached the time limit.": "Candidate reached the time limit.", "Candidate reasons": "Candidate reasons", "Candidate reference ID (Teamdash)": "Candidate reference ID (Teamdash)", "Candidate reference email (Teamdash)": "Candidate reference email (Teamdash)", "Candidate reference name (Teamdash)": "Candidate reference name (Teamdash)", "Candidate scorecard saved.": "Candidate scorecard saved.", "Candidate sent to :integrationName": "Candidate sent to :integrationName", "Candidate sent to BambooHR": "Candidate sent to BambooHR", "Candidate sent to {hrisName}": "Candidate sent to {hrisName}", "Candidate submission sources": "Candidate submission sources", "Candidate survey question": "Candidate survey question", "Candidates": "Candidates", "Candidates added to the project after": "Candidates added to the project after", "Candidates anonymized!": "Candidates anonymized!", "Candidates below this age will be marked as underage on the candidate card.": "Candidates below this age will be marked as underage on the candidate card.", "Candidates can only choose times when all the recruiters are free.": "Candidates can only choose times when all the recruiters are free.", "Candidates can see this on social media sharing previews.": "Candidates can see this on social media sharing previews.", "Candidates deleted!": "Candidates deleted!", "Candidates hate long commutes. If you're hiring for on-site positions, you can make better hiring decisions with location data.": "Candidates hate long commutes. If you're hiring for on-site positions, you can make better hiring decisions with location data.", "Candidates shared!": "Candidates shared!", "Candidates target stage": "Candidates target stage", "Candidates to add": "Candidates to add", "Candidates to share": "Candidates to share", "Candidates under min age ({age})": "Candidates under min age ({age})", "Candidates were not copied.": "Candidates were not copied.", "Candidates who gave a 9 or 10 are called promoters, 7 or 8 are passives and 0-6 are detractors.": "Candidates who gave a 9 or 10 are called promoters, 7 or 8 are passives and 0-6 are detractors.", "Candidates who reached {stage} stage": "Candidates who reached {stage} stage", "Candidates with ignored/declined consent renewals": "Candidates with ignored/declined consent renewals", "Candidates with pending consent renewals": "Candidates with pending consent renewals", "Capital Markets": "Capital Markets", "Career Pages": "Career Pages", "Career experience": "Career experience", "Career grid": "Career grid", "Career list": "Career list", "Catalan": "Catalan", "Catalan, Valencian": "Catalan, Valencian", "Categories (CVK/CVM)": "Categories (CVK/CVM)", "Categories (CVO)": "Categories (CVO)", "Category": "Category", "Category (CVbankas)": "Category (CVbankas)", "Category (SS.lv)": "Category (SS.lv)", "Caterers": "Caterers", "Catering": "Catering", "Central Europe": "Central Europe", "Central Khmer": "Central Khmer", "Chamorro": "<PERSON><PERSON><PERSON>", "Change my score": "Change my score", "Change response": "Change response", "Change video": "Change video", "Changes made here will not affect existing projects created from this template.": "Changes made here will not affect existing projects created from this template.", "Channels": "Channels", "Charity": "Charity", "Chart": "Chart", "Chat thread": "Chat thread", "Chechen": "Chechen", "Check for applicants to assess": "Check for applicants to assess", "Check out who rejected it and if they left a comment:": "Check out who rejected it and if they left a comment:", "Check references": "Check references", "Checkbox": "Checkbox", "Checkbox numeric": "Checkbox numeric", "Chemical Manufacturing": "Chemical Manufacturing", "Chemical Raw Materials Manufacturing": "Chemical Raw Materials Manufacturing", "Chichewa, Chewa, Nyanja": "Chichewa, Chewa, Nyanja", "Child Day Care Services": "Child Day Care Services", "Children": "Children", "Chinese": "Chinese", "Chiropractors": "Chiropractors", "Choices": "Choices", "Choose": "<PERSON><PERSON>", "Choose Indeed Apply as the credential type and click Save.": "Choose Indeed Apply as the credential type and click Save.", "Choose a dropout reason to merge {dropoutReason} with.": "Choose a dropout reason to merge {dropoutReason} with.", "Choose a version from the history to preview": "Choose a version from the history to preview", "Choose client": "Choose client", "Choose company settings": "Choose company settings", "Choose dropout reason(s)": "Choose dropout reason(s)", "Choose image": "Choose image", "Choose interview": "Choose interview", "Choose manager": "Choose manager", "Choose or type source": "Choose or type source", "Choose project": "Choose project", "Choose project manager": "Choose project manager", "Choose statuses": "Choose statuses", "Choose tag": "Choose tag", "Choose template": "Choose template", "Choose this value, the other will be removed": "Choose this value, the other will be removed", "Choose user to reassign these items to:": "Choose user to reassign these items to:", "Church Slavonic, Old Bulgarian, Old Church Slavonic": "Church Slavonic, Old Bulgarian, Old Church Slavonic", "Chuvash": "Chuvash", "Circuses and Magic Shows": "Circuses and Magic Shows", "Cities (cv.lt)": "Cities (cv.lt)", "City": "City", "City (CVK/CVM)": "City (CVK/CVM)", "City (CVbankas)": "City (CVbankas)", "City or county (SS.lv)": "City or county (SS.lv)", "Civic and Social Organizations": "Civic and Social Organizations", "Civil Engineering": "Civil Engineering", "Claims Adjusting, Actuarial Services": "Claims Adjusting, Actuarial Services", "Classification (profession.hu)": "Classification (profession.hu)", "Clay and Refractory Products Manufacturing": "Clay and Refractory Products Manufacturing", "Cleaner, Auxiliary Worker, Production Worker, Warehouse Worker...": "Cleaner, Auxiliary Worker, Production Worker, Warehouse Worker...", "Clear": "Clear", "Clear all": "Clear all", "Clear all stage categories": "Clear all stage categories", "Clear filters": "Clear filters", "Clear screener results": "Clear screener results", "Clear search": "Clear search", "Clearing...": "Clearing...", "Click here to read more about cNPS": "Click here to read more about cNPS", "Click on team or edge and press backspace/delete to delete": "Click on team or edge and press backspace/delete to delete", "Click on the \"Register a new application\" button.": "Click on the \"Register a new application\" button.", "Click on the option that should remain after the merge.": "Click on the option that should remain after the merge.", "Click save to apply your changes": "Click save to apply your changes", "Click template to preview": "Click template to preview", "Click to copy": "Click to copy", "Click to insert merge tag.": "Click to insert merge tag.", "Click to reveal the value": "Click to reveal the value", "Click to see alternatives.": "Click to see alternatives.", "Click to see the changes.": "Click to see the changes.", "Click to select a time for your interview. You can drag the slot around or remove it and reselect, if you wish to change the time.": "Click to select a time for your interview. You can drag the slot around or remove it and reselect, if you wish to change the time.", "Clicked at": "Clicked at", "Clicks": "<PERSON>licks", "Client": "Client", "Client ID": "Client ID", "Client Secret": "Client Secret", "Client company name": "Client company name", "Client name": "Client name", "Client secret": "Client secret", "Clients": "Clients", "Clone": "<PERSON><PERSON>", "Clone project": "Clone project", "Clone requisition": "Clone requisition", "Clone scorecard": "Clone scorecard", "Close": "Close", "Close any other programs which might be using the webcam.": "Close any other programs which might be using the webcam.", "Coal Mining": "Coal Mining", "Collaborate with hiring managers and assess candidates in a fair and equitable way.": "Collaborate with hiring managers and assess candidates in a fair and equitable way.", "Collection Agencies": "Collection Agencies", "Colour": "Colour", "Columns": "Columns", "Coming soon": "Coming soon", "Comma separated IP addresses or CIDR ranges.": "Comma separated IP addresses or CIDR ranges.", "Comment": "Comment", "Comment Attachment": "Comment Attachment", "Comment by {name}": "Comment by {name}", "Comments": "Comments", "Commercial and Industrial Equipment Rental": "Commercial and Industrial Equipment Rental", "Commercial and Industrial Machinery Maintenance": "Commercial and Industrial Machinery Maintenance", "Commercial and Service Industry Machinery Manufacturing": "Commercial and Service Industry Machinery Manufacturing", "Communications Equipment Manufacturing": "Communications Equipment Manufacturing", "Community Development and Urban Planning": "Community Development and Urban Planning", "Community Services": "Community Services", "Company": "Company", "Company mail signature settings": "Company mail signature settings", "Company name": "Company name", "Company names": "Company names", "Company network gateway IP": "Company network gateway IP", "Company reasons": "Company reasons", "Compensation (salary + bonus):": "Compensation (salary + bonus):", "Complained at": "Complained at", "Computer": "Computer", "Computer Games": "Computer Games", "Computer Hardware Manufacturing": "Computer Hardware Manufacturing", "Computer Networking Products": "Computer Networking Products", "Computer and Network Security": "Computer and Network Security", "Computers and Electronics Manufacturing": "Computers and Electronics Manufacturing", "Condition": "Condition", "Configure personally identifiable information": "Configure personally identifiable information", "Confirm": "Confirm", "Confirm & merge": "Confirm & merge", "Confirm current response": "Confirm current response", "Confirmation SMS": "Confirmation SMS", "Consent Info": "Consent Info", "Consent renewal": "Consent renewal", "Consent renewal message": "Consent renewal message", "Consent renewal message body": "Consent renewal message body", "Consent renewal message subject": "Consent renewal message subject", "Consent renewals sent!": "Consent renewals sent!", "Consent subtype": "Consent subtype", "Consent subtypes": "Consent subtypes", "Consent type": "Consent type", "Consent types": "Consent types", "Consent valid until": "Consent valid until", "Consent valid until end of": "Consent valid until end of", "Conservation Programs": "Conservation Programs", "Consider adding categories to all stages.": "Consider adding categories to all stages.", "Construction": "Construction", "Construction / Real Estate": "Construction / Real Estate", "Construction / Real-estate": "Construction / Real-estate", "Construction Hardware Manufacturing": "Construction Hardware Manufacturing", "Consulting": "Consulting", "Consumer Goods Rental": "Consumer Goods Rental", "Consumer Services": "Consumer Services", "Contact email": "Contact email", "Contact her at {email} to get help with your first job ad.": "Contact her at {email} to get help with your first job ad.", "Contact info": "Contact info", "Contact name": "Contact name", "Contact phone": "Contact phone", "Contact support to enable AI transcriptions and summaries.": "Contact support to enable AI transcriptions and summaries.", "Contact support to enable AI-powered features.": "Contact support to enable AI-powered features.", "Contact support to enable SCIM for your account.": "Contact support to enable SCIM for your account.", "Contact support to enable reference checks.": "Contact support to enable reference checks.", "Contact support to enable transcripts for your account.": "Contact support to enable transcripts for your account.", "Contact support to enable video interviews.": "Contact support to enable video interviews.", "Contact support to enable webhook actions.": "Contact support to enable webhook actions.", "Contacts": "Contacts", "Continue interview": "Continue interview", "Continuous project (always accepting candidates)": "Continuous project (always accepting candidates)", "Contract": "Contract", "Contract type": "Contract type", "Contractor": "Contractor", "Control who can see and edit what. Create teams and assign them to projects, job ads, and candidates.": "Control who can see and edit what. Create teams and assign them to projects, job ads, and candidates.", "Controls": "Controls", "Conversion rate": "Conversion rate", "Convert to template": "Convert to template", "Convince the candidate to respond": "Convince the candidate to respond", "Copied 1 candidate to selected project. | Copied {count} candidates to selected project.": "Copied 1 candidate to selected project. | Copied {count} candidates to selected project.", "Copied to clipboard.": "Copied to clipboard.", "Copied {successCount} out of {allCount} candidates. The rest were already in the chosen project.": "Copied {successCount} out of {allCount} candidates. The rest were already in the chosen project.", "Copied!": "Copied!", "Copy and paste the generated credentials into the fields below.": "Copy and paste the generated credentials into the fields below.", "Copy email address": "Copy email address", "Copy feed URL": "Copy feed URL", "Copy feed slug:": "Copy feed slug:", "Copy landing URL": "Copy landing URL", "Copy the number to field above": "Copy the number to field above", "Copy to another project": "Copy to another project", "Copy to project": "Copy to project", "Cornish": "Cornish", "Corporation": "Corporation", "Correctional Institutions": "Correctional Institutions", "Corsican": "Corsican", "Cosmetology and Barber Schools": "Cosmetology and Barber Schools", "Cost": "Cost", "Cost per applicant": "Cost per applicant", "Cost per submission": "Cost per submission", "Could not delete stage! There might audit log events or other data associated with this stage.": "Could not delete stage! There might audit log events or other data associated with this stage.", "Could not find any stages in the selected project. Please check that you have sufficient permissions and try again.": "Could not find any stages in the selected project. Please check that you have sufficient permissions and try again.", "Could not remove stage": "Could not remove stage", "Could not select this time. Please choose a new time.": "Could not select this time. Please choose a new time.", "Count candidates": "Count candidates", "Countdown": "Countdown", "Countdown to application deadline": "Countdown to application deadline", "Country": "Country", "Country (CVK/CVM)": "Country (CVK/CVM)", "Country code": "Country code", "County (CVK/CVM)": "County (CVK/CVM)", "Courts of Law": "Courts of Law", "Create": "Create", "Create API key": "Create API key", "Create a feed integration {here}, reload the editor and retry.": "Create a feed integration {here}, reload the editor and retry.", "Create a new form linked to this project": "Create a new form linked to this project", "Create a new job requisition": "Create a new job requisition", "Create a new landing page": "Create a new landing page", "Create a new password": "Create a new password", "Create a new project": "Create a new project", "Create a new project failure reason": "Create a new project failure reason", "Create a new project template": "Create a new project template", "Create a new template": "Create a new template", "Create a reference form": "Create a reference form", "Create a task": "Create a task", "Create a transcript from video file...": "Create a transcript from video file...", "Create a video interview": "Create a video interview", "Create an automatic form and include it on a landing page linked to this project": "Create an automatic form and include it on a landing page linked to this project", "Create an event": "Create an event", "Create and use form": "Create and use form", "Create call": "Create call", "Create consent type": "Create consent type", "Create custom activity": "Create custom activity", "Create dropout reason": "Create dropout reason", "Create file type": "Create file type", "Create form": "Create form", "Create forms to ask internal feedback or details from other project members.": "Create forms to ask internal feedback or details from other project members.", "Create forms to gather applicants.": "Create forms to gather applicants.", "Create interview": "Create interview", "Create job ad": "Create job ad", "Create key": "Create key", "Create landing page": "Create landing page", "Create legacy page": "Create legacy page", "Create location": "Create location", "Create new": "Create new", "Create new project from template": "Create new project from template", "Create on-brand customisable career sites. Recruiters can publish all jobs with a single click.": "Create on-brand customisable career sites. Recruiters can publish all jobs with a single click.", "Create password": "Create password", "Create project": "Create project", "Create project-based roles": "Create project-based roles", "Create reference form": "Create reference form", "Create requisition": "Create requisition", "Create requisition & ask approvals": "Create requisition & ask approvals", "Create scorecard": "Create scorecard", "Create tag": "Create tag", "Create task": "Create task", "Create tasks": "Create tasks", "Create template": "Create template", "Create template from project": "Create template from project", "Create user": "Create user", "Create video call": "Create video call", "Create your beautiful job ad": "Create your beautiful job ad", "Created": "Created", "Created at": "Created at", "Created on": "Created on", "Created task": "Created task", "Created!": "Created!", "Creating a call will send your users and candidates the video call link.": "Creating a call will send your users and candidates the video call link.", "Creative": "Creative", "Creative type": "Creative type", "Creatives": "Creatives", "Credit Intermediation": "Credit Intermediation", "Cree": "<PERSON><PERSON>", "Croatian": "Croatian", "Crop": "Crop", "Crop & save": "Crop & save", "Culture / Entertainment": "Culture / Entertainment", "Culture / Entertainment / Recreation": "Culture / Entertainment / Recreation", "Current month": "Current month", "Current role": "Current role", "Current team": "Current team", "Current value:": "Current value:", "Cusotm fonts": "Cusotm fonts", "Custom CSS": "Custom CSS", "Custom Domain": "Custom Domain", "Custom URL": "Custom URL", "Custom activities": "Custom activities", "Custom activity key": "Custom activity key", "Custom categories must be in the same order as system categories": "Custom categories must be in the same order as system categories", "Custom description": "Custom description", "Custom field:": "Custom field:", "Custom fields": "Custom fields", "Custom fonts": "Custom fonts", "Custom fonts can be used in landings and forms. Click the button below to get started.": "Custom fonts can be used in landings and forms. Click the button below to get started.", "Custom option value": "Custom option value", "Custom range": "Custom range", "Custom signature": "Custom signature", "Custom stage categories": "Custom stage categories", "Customer Service": "Customer Service", "Customer Services": "Customer Services", "Customer service/Services": "Customer service/Services", "Customize": "Customize", "Cutlery and Handtool Manufacturing": "Cutlery and Handtool Manufacturing", "Czech": "Czech", "DEI: Inclusive language check": "DEI: Inclusive language check", "Daily": "Daily", "Daily budget (€)": "Daily budget (€)", "Dairy Product Manufacturing": "Dairy Product Manufacturing", "Dance Companies": "Dance Companies", "Danish": "Danish", "Darker": "Darker", "Data Infrastructure and Analytics": "Data Infrastructure and Analytics", "Data Processing Agreement": "Data Processing Agreement", "Data Security Software Products": "Data Security Software Products", "Data from public sources": "Data from public sources", "Data processing consent automation": "Data processing consent automation", "Date": "Date", "Date (calendar)": "Date (calendar)", "Date and time format": "Date and time format", "Datetime": "Datetime", "Day": "Day", "Days": "Days", "Deactivate": "Deactivate", "Deactivate and reassign": "Deactivate and reassign", "Deactivate user {userName}": "Deactivate user {userName}", "Deactivate without reassigning": "Deactivate without reassigning", "Deactivating...": "Deactivating...", "Deadline": "Deadline", "Deadline not set": "Deadline not set", "Deadline: :deadline": "Deadline: :deadline", "Deadline: {date}": "Deadline: {date}", "Debug": "Debug", "Debug report for {integrationName}": "Debug report for {integrationName}", "December": "December", "Decision pending": "Decision pending", "Default": "<PERSON><PERSON><PERSON>", "Default careers page": "Default careers page", "Default data processing consent duration": "Default data processing consent duration", "Default data processing consent request validity duration": "Default data processing consent request validity duration", "Default language": "Default language", "Default phone country code": "Default phone country code", "Default stages": "Default stages", "Default survey": "Default survey", "Default survey will use the question defined under Organization settings. If you wish to send a form with more questions, create a new Survey form.": "Default survey will use the question defined under Organization settings. If you wish to send a form with more questions, create a new Survey form.", "Defense and Space Manufacturing": "Defense and Space Manufacturing", "Define a new font": "Define a new font", "Define new font": "Define new font", "Delay": "Delay", "Delay sending": "Delay sending", "Delete": "Delete", "Delete chat": "Delete chat", "Delete comment": "Delete comment", "Delete file": "Delete file", "Delete filter": "Delete filter", "Delete permanently": "Delete permanently", "Delete project": "Delete project", "Delete stage": "Delete stage", "Delete template": "Delete template", "Delete video only": "Delete video only", "Deleted": "Deleted", "Deleting a client cannot be undone.": "Deleting a client cannot be undone.", "Deliver via": "Deliver via", "Delivered at": "Delivered at", "Dentists": "Dentists", "Department": "Department", "Department Management": "Department Management", "Department:": "Department:", "Departments (cv.lt)": "Departments (cv.lt)", "Depending on your specific circumstances, this can be totally fine or might need some action.": "Depending on your specific circumstances, this can be totally fine or might need some action.", "Description": "Description", "Description section title": "Description section title", "Descriptive page name": "Descriptive page name", "Design": "Design", "Design Services": "Design Services", "Design/architecture": "Design/architecture", "Desired": "Desired", "Desktop Computing Software Products": "Desktop Computing Software Products", "Desktop View": "Desktop View", "Director": "Director", "Disability Confident employer": "Disability Confident employer", "Disability group": "Disability group", "Disability group from date": "Disability group from date", "Disability group to date": "Disability group to date", "Disable": "Disable", "Disable activity notifications": "Disable activity notifications", "Disabled": "Disabled", "Dismiss": "<PERSON><PERSON><PERSON>", "Display name": "Display name", "Display type": "Display type", "Displayed on career page preview.": "Displayed on career page preview.", "Displayed when sharing page in social media.": "Displayed when sharing page in social media.", "Dispute & invalid": "Dispute & invalid", "Dispute months": "Dispute months", "Dispute resolution only": "Dispute resolution only", "Dispute resolution only.": "Dispute resolution only.", "Distance from {locationName} ({locationAddress})": "Distance from {locationName} ({locationAddress})", "Distilleries": "Distilleries", "Distribution": "Distribution", "Divehi, Dhivehi, Maldivian": "Divehi, Dhivehi, Maldivian", "Do a fact-check - find any instances where incorrect facts were presented. Do not highlight correct facts.": "Do a fact-check - find any instances where incorrect facts were presented. Do not highlight correct facts.", "Do not infer or assume any information that has not been explicitly said in the transcript.": "Do not infer or assume any information that has not been explicitly said in the transcript.", "Do you want to {knowMoreCTA} about your interviews automatically?": "Do you want to {knowMoreCTA} about your interviews automatically?", "Do you want to {knowMoreCTA} without spending hours on interviewing?": "Do you want to {knowMoreCTA} without spending hours on interviewing?", "Doctor's degree": "Doctor's degree", "Documentation": "Documentation", "Domain": "Domain", "Domain must be a valid domain": "Domain must be a valid domain", "Don't worry if you don't have the time, will or skills to choose photos.": "Don't worry if you don't have the time, will or skills to choose photos.", "Don't worry if you don't have the time, will or skills to choose videos.": "Don't worry if you don't have the time, will or skills to choose videos.", "Double-click to edit": "Double-click to edit", "Down for maintenance": "Down for maintenance", "Download": "Download", "Download as PDF": "Download as PDF", "Download as image": "Download as image", "Download candidates report": "Download candidates report", "Download custom activity report": "Download custom activity report", "Download monthly hiring report": "Download monthly hiring report", "Download project status report": "Download project status report", "Download recording": "Download recording", "Download submissions": "Download submissions", "Downloadable image": "Downloadable image", "Downloaded on {date}": "Downloaded on {date}", "Downloading report": "Downloading report", "Downloading report...": "Downloading report...", "Draft": "Draft", "Draft saved": "Draft saved", "Drag and drop CVs onto the project board": "Drag and drop CVs onto the project board", "Drag and drop to add attachments": "Drag and drop to add attachments", "Drag whitespace to move graph": "Drag whitespace to move graph", "Dragged from mailbox": "Dragged from mailbox", "Driver's license (profession.hu)": "Driver's license (profession.hu)", "Drivers license": "Drivers license", "Driving": "Driving", "Drop files here": "Drop files here", "Dropout": "Dropout", "Dropout reason": "Dropout reason", "Dropout reason :reasonName can not be deleted because it is still linked to :count applications": "Dropout reason :reasonName can not be deleted because it is still linked to :count applications", "Dropout reason added.": "Dropout reason added.", "Dropout reason deleted successfully.": "Dropout reason deleted successfully.", "Dropout reason was deleted.": "Dropout reason was deleted.", "Dropout reason {originalDropoutReason} will be deleted.": "Dropout reason {originalDropoutReason} will be deleted.", "Dropout reasons": "Dropout reasons", "Dropout reasons merged successfully.": "Dropout reasons merged successfully.", "Dropout reasons were merged.": "Dropout reasons were merged.", "Dropped out": "Dropped out", "Dry run (deliver debug info via email)": "Dry run (deliver debug info via email)", "Dry run: candidate sent to :integrationName": "Dry run: candidate sent to :integrationName", "Duplicate": "Duplicate", "Duration (min)": "Duration (min)", "Duration in days": "Duration in days", "Duration in months": "Duration in months", "Dutch": "Dutch", "Dutch, Flemish": "Dutch, Flemish", "Duunitori.fi": "Duunitori.fi", "Dzongkha": "Dzongkha", "E-Learning Providers": "E-Learning Providers", "E-mail": "E-mail", "E-mail address of the Indeed account used to manage this integration": "E-mail address of the Indeed account used to manage this integration", "E-mail notifications are currently turned off. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn on.": "E-mail notifications are currently turned off. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn on.", "Earliest day to offer?": "Earliest day to offer?", "Eastern Europe": "Eastern Europe", "Easy Apply": "Easy Apply", "Easy Apply button text": "Easy Apply button text", "Easy Apply disclaimer": "Easy Apply disclaimer", "Easy Apply fallback option text": "Easy Apply fallback option text", "Economic Programs": "Economic Programs", "Edit": "Edit", "Edit API key": "Edit API key", "Edit action": "Edit action", "Edit action {actionName}": "Edit action {actionName}", "Edit actions and imports": "Edit actions and imports", "Edit authentication provider": "Edit authentication provider", "Edit candidate": "Edit candidate", "Edit candidate referee {refereeName}": "Edit candidate referee {referee<PERSON><PERSON>}", "Edit client": "Edit client", "Edit consent type": "Edit consent type", "Edit contact": "Edit contact", "Edit custom activity": "Edit custom activity", "Edit dropout reason": "Edit dropout reason", "Edit file type": "Edit file type", "Edit form": "Edit form", "Edit import": "Edit import", "Edit integration": "Edit integration", "Edit interview": "Edit interview", "Edit job ad": "Edit job ad", "Edit location": "Edit location", "Edit office": "Edit office", "Edit project": "Edit project", "Edit project scorecards": "Edit project scorecards", "Edit project-based roles": "Edit project-based roles", "Edit reference form": "Edit reference form", "Edit requisition": "Edit requisition", "Edit scorecard": "Edit scorecard", "Edit stage :stage in :project": "Edit stage :stage in :project", "Edit structured job ad": "Edit structured job ad", "Edit tag": "Edit tag", "Edit teams hierarchy": "Edit teams hierarchy", "Edit template": "Edit template", "Edit user": "Edit user", "Edit video interview": "Edit video interview", "Editable text with custom background image": "Editable text with custom background image", "Editable text with responsive image on one side. Configurable orientation.": "Editable text with responsive image on one side. Configurable orientation.", "Editable text with responsive video on one side. Configurable orientation.": "Editable text with responsive video on one side. Configurable orientation.", "Editable text with square image on one side. Configurable orientation.": "Editable text with square image on one side. Configurable orientation.", "Editing uploaded candidate {candidateIndex} of {candidateTotal}": "Editing uploaded candidate {candidateIndex} of {candidateTotal}", "Education": "Education", "Education (SS.lv)": "Education (SS.lv)", "Education / Science": "Education / Science", "Education / Science / Research": "Education / Science / Research", "Education Administration Programs": "Education Administration Programs", "Education history": "Education history", "Education requirement (profession.hu)": "Education requirement (profession.hu)", "Either an e-mail or a phone number is required for each referee.": "Either an e-mail or a phone number is required for each referee.", "Electric Lighting Equipment Manufacturing": "Electric Lighting Equipment Manufacturing", "Electric Power Generation": "Electric Power Generation", "Electric Power Transmission, Control, and Distribution": "Electric Power Transmission, Control, and Distribution", "Electrical Equipment Manufacturing": "Electrical Equipment Manufacturing", "Electrical/Telecoms": "Electrical/Telecoms", "Electronic and Precision Equipment Maintenance": "Electronic and Precision Equipment Maintenance", "Electronics": "Electronics", "Electronics / Telecom": "Electronics / Telecom", "Electronics / Telecommunication": "Electronics / Telecommunication", "Email": "Email", "Email Message": "Email Message", "Email address is required in integration settings": "Email address is required in integration settings", "Email message conversions": "Email message conversions", "Email service provider": "Email service provider", "Emails": "Emails", "Embedded Software Products": "Embedded Software Products", "Emergency and Relief Services": "Emergency and Relief Services", "Emergency contact phone and name": "Emergency contact phone and name", "Emojis not found.": "Emojis not found.", "Employee forms": "Employee forms", "Employer": "Employer", "Employer ID": "Employer ID", "Employer brand video": "Employer brand video", "Employer brand video URL": "Employer brand video URL", "Employer info": "Employer info", "Employer web URL": "Employer web URL", "Employment - employer name": "Employment - employer name", "Employment - job title": "Employment - job title", "Employment Start Date": "Employment Start Date", "Employment history": "Employment history", "Employment type": "Employment type", "Employments": "Employments", "Empty": "Empty", "Enable": "Enable", "Enable \"Send as user\" for messages": "Enable \"Send as user\" for messages", "Enable Easy Apply": "Enable Easy Apply", "Enable GDPR automation": "Enable GDPR automation", "Enable click tracking": "Enable click tracking", "Enable diversity reporting": "Enable diversity reporting", "Enable integration": "Enable integration", "Enable permanent delete": "Enable permanent delete", "Enable this if you are sending non-critical content.": "Enable this if you are sending non-critical content.", "Enable this if you have more than 100 rooms. Will add Place.Read.All permission.": "Enable this if you have more than 100 rooms. Will add Place.Read.All permission.", "Enable this if you want AI-powered transcriptions and summaries from meetings. Requires permissions for your events and recordings.": "Enable this if you want AI-powered transcriptions and summaries from meetings. Requires permissions for your events and recordings.", "Enable this to store video recordings in Teamdash.": "Enable this to store video recordings in Teamdash.", "Enable {gdprautomation} to never worry about it again.": "Enable {gdprautomation} to never worry about it again.", "Enabled": "Enabled", "Encryption": "Encryption", "End": "End", "End date": "End date", "End month": "End month", "End year": "End year", "Energetics / Electricity": "Energetics / Electricity", "Energetics / Natural resources": "Energetics / Natural resources", "Energetics/electronics": "Energetics/electronics", "Enforce limits": "Enforce limits", "Enforcing limits will automatically submit the final attempt and end the video recording when the time limit is reached. By not enforcing, candidates are advised on the limits. but are not restricted from exceeding them. You will be shown if attempts and/or time limits have been exceeded.": "Enforcing limits will automatically submit the final attempt and end the video recording when the time limit is reached. By not enforcing, candidates are advised on the limits. but are not restricted from exceeding them. You will be shown if attempts and/or time limits have been exceeded.", "Engineering": "Engineering", "Engineering/mechanics": "Engineering/mechanics", "Engines and Power Transmission Equipment Manufacturing": "Engines and Power Transmission Equipment Manufacturing", "English": "English", "English (UK)": "English (UK)", "English (US)": "English (US)", "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to \"Allow\".": "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to \"Allow\".", "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to &quot;Allow&quot;.": "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to &quot;Allow&quot;.", "Enter \"Teamdash\" as the application name and add a short description (e.g. \"Teamdash recruitment software\").": "Enter \"Teamdash\" as the application name and add a short description (e.g. \"Teamdash recruitment software\").", "Enter a new reason...": "Enter a new reason...", "Enter position": "Enter position", "Enter reference manually": "Enter reference manually", "Enter warranty date if applicable": "Enter warranty date if applicable", "Entertainment Providers": "Entertainment Providers", "Entry level": "Entry level", "Environmental Quality Programs": "Environmental Quality Programs", "Environmental Services": "Environmental Services", "Equipment Rental Services": "Equipment Rental Services", "Error": "Error", "Error when scheduling an interview (:interviewTitle)": "Error when scheduling an interview (:interview<PERSON><PERSON><PERSON>)", "Error:": "Error:", "Esperanto": "Esperanto", "Essential": "Essential", "Estonia": "Estonia", "Estonian": "Estonian", "Event": "Event", "Event :title on :start has been cancelled.": "Event :title on :start has been cancelled.", "Event :title scheduled for :start": "Event :title scheduled for :start", "Event [event_title] scheduled for [event_start]": "Event [event_title] scheduled for [event_start]", "Event scheduled": "Event scheduled", "Event updated successfully!": "Event updated successfully!", "Events Services": "Events Services", "Everyone who is currently using this key will no longer be able to access the API. This may break the connection between job portals and your Teamdash instance!": "Everyone who is currently using this key will no longer be able to access the API. This may break the connection between job portals and your Teamdash instance!", "Everything related to project stages can now be edited directly on the candidates board.": "Everything related to project stages can now be edited directly on the candidates board.", "Ewe": "<PERSON><PERSON>", "Excellent": "Excellent", "Exclude": "Exclude", "Exclude continuous projects": "Exclude continuous projects", "Executive": "Executive", "Executive Offices": "Executive Offices", "Executive Search Services": "Executive Search Services", "Exit": "Exit", "Expand": "Expand", "Expand column": "Expand column", "Expected role": "Expected role", "Expected team": "Expected team", "Experience (profession.hu)": "Experience (profession.hu)", "Experience level": "Experience level", "Expired": "Expired", "Expired at": "Expired at", "Expires at": "Expires at", "Expiring soon": "Expiring soon", "Expiry period": "Expiry period", "Expiry period unit": "Expiry period unit", "Export": "Export", "Export an ad to a job portal": "Export an ad to a job portal", "Export candidates": "Export candidates", "Exported by {exporter} ({exporterEmail})": "Exported by {exporter} ({exporterEmail})", "Exports": "Exports", "Extract details with AI": "Extract details with AI", "FTE": "FTE", "Fabricated Metal Products": "Fabricated Metal Products", "Facilities Services": "Facilities Services", "Fact-check": "Fact-check", "Failed to clear screening results": "Failed to clear screening results", "Failed to copy.": "Failed to copy.", "Failed to deactivate user. Please try again later.": "Failed to deactivate user. Please try again later.", "Failed to delete project failure reason.": "Failed to delete project failure reason.", "Failed to generate screening criteria": "Failed to generate screening criteria", "Failed to load available rooms.": "Failed to load available rooms.", "Failed to parse the candidate's CV.": "Failed to parse the candidate's CV.", "Failed to remove criterion": "Failed to remove criterion", "Failed to resend invite. Please try again later.": "Failed to resend invite. Please try again later.", "Failed to retry transcription.": "Failed to retry transcription.", "Failed to run screener": "Failed to run screener", "Failed to run screener - CV contents empty or missing.": "Failed to run screener - CV contents empty or missing.", "Failed to save rating.": "Failed to save rating.", "Failed to save settings.": "Failed to save settings.", "Failed to send candidate to :integrationName": "Failed to send candidate to :integrationName", "Failed to send unpublish notification: :message": "Failed to send unpublish notification: :message", "Failed to start recording. Please refresh the page.": "Failed to start recording. Please refresh the page.", "Failed to submit answer.": "Failed to submit answer.", "Failed to upload recording. Please refresh the page.": "Failed to upload recording. Please refresh the page.", "Failed:": "Failed:", "Failure reason": "Failure reason", "Failure to find a suitable candidate": "Failure to find a suitable candidate", "Failure type": "Failure type", "Fair": "Fair", "Fair evaluations": "Fair evaluations", "Family Planning Centers": "Family Planning Centers", "Farming": "Farming", "Farming, Ranching, Forestry": "Farming, Ranching, Forestry", "Faroese": "Faroese", "Fashion": "Fashion", "Fashion Accessories Manufacturing": "Fashion Accessories Manufacturing", "Favicon is the small icon that appears in the browser tab on your landing pages.": "Favicon is the small icon that appears in the browser tab on your landing pages.", "February": "February", "Feed": "Feed", "Feed URL": "Feed URL", "Feel free to tweak filters and double-check you have access to the project.": "Feel free to tweak filters and double-check you have access to the project.", "Female": "Female", "Fetch video from Microsoft Teams": "Fetch video from Microsoft Teams", "Field MUST include [:mustIncludeTags] :mustIncludeCondition.": "Field MUST include [:mustIncludeTags] :mustIncludeCondition.", "Field MUST include [:mustIncludeTags].": "Field MUST include [:mustIncludeTags].", "Field can be empty": "Field can be empty", "Field label": "Field label", "Field must be filled": "Field must be filled", "Field must be valid url": "Field must be valid url", "Field type": "Field type", "Fields": "Fields", "Fijian": "Fijian", "File (legacy version)": "File (legacy version)", "File URL": "File URL", "File must contain columns: name, email, role (admin, regular, limited). All added users will receive an email to create a password for their account.": "File must contain columns: name, email, role (admin, regular, limited). All added users will receive an email to create a password for their account.", "File size is over the 10MB limit.": "File size is over the 10MB limit.", "File type": "File type", "File type not supported": "File type not supported", "File types": "File types", "File uploader": "File uploader", "Files": "Files", "Fill a form": "Fill a form", "Fill text fields from landing": "Fill text fields from landing", "Filter set name": "Filter set name", "Filters": "Filters", "Filters saved!": "Filters saved!", "Finance": "Finance", "Finance / Accounting": "Finance / Accounting", "Finance/accounting/banking": "Finance/accounting/banking", "Financial Services": "Financial Services", "Find candidates": "Find candidates", "Find candidates similar to": "Find candidates similar to", "Fine Arts Schools": "Fine Arts Schools", "Finish interview": "Finish interview", "Finish uploading candidates": "Finish uploading candidates", "Finished projects": "Finished projects", "Finished projects + time to fill": "Finished projects + time to fill", "Finland": "Finland", "Finnish": "Finnish", "Fire Protection": "Fire Protection", "First application date": "First application date", "First name": "First name", "Fisheries": "Fisheries", "Fix": "Fix", "Fix grammar and spelling": "Fix grammar and spelling", "Fix integration": "Fix integration", "Fixed background when scrolling": "Fixed background when scrolling", "Fixed term": "Fixed term", "Flight Training": "Flight Training", "Font": "Font", "Font file (bold style)": "Font file (bold style)", "Font file (combined bold-and-italic style)": "Font file (combined bold-and-italic style)", "Font file (italic style)": "Font file (italic style)", "Font file (normal style)": "Font file (normal style)", "Font name": "Font name", "Food Industry": "Food Industry", "Food and Beverage Manufacturing": "Food and Beverage Manufacturing", "Food and Beverage Retail": "Food and Beverage Retail", "Food and Beverage Services": "Food and Beverage Services", "Footwear Manufacturing": "Footwear Manufacturing", "Footwear and Leather Goods Repair": "Footwear and Leather Goods Repair", "Footwear/Accessories": "Footwear/Accessories", "For conformance with the GDPR, please follow these guidelines for the request message. For more information, consult your DPO, Teamdash support or a lawyer.": "For conformance with the GDPR, please follow these guidelines for the request message. For more information, consult your DPO, Teamdash support or a lawyer.", "For example an email or a screenshot to prove the revocation.": "For example an email or a screenshot to prove the revocation.", "For example an email, screenshot or a contract.": "For example an email, screenshot or a contract.", "Forbidden": "Forbidden", "Forest / Woodcutting": "Forest / Woodcutting", "Forestry and Logging": "Forestry and Logging", "Form": "Form", "Form :form cannot be directly linked. Please use the [form_url] merge tag to create candidate-specific links to this form.": "Form :form cannot be directly linked. Please use the [form_url] merge tag to create candidate-specific links to this form.", "Form Submission file": "Form Submission file", "Form builder": "Form builder", "Form name": "Form name", "Form preview": "Form preview", "Form submission failed": "Form submission failed", "Form submitted successfully": "Form submitted successfully", "Form type": "Form type", "Forms": "Forms", "Fossil Fuel Electric Power Generation": "Fossil Fuel Electric Power Generation", "Found 1 landing.|Found {count} landings.": "Found 1 landing.|Found {count} landings.", "Free slot": "Free slot", "Freelance": "Freelance", "Freight and Package Transportation": "Freight and Package Transportation", "French": "French", "Friday": "Friday", "From": "From", "From URL": "From URL", "From picture bank": "From picture bank", "From received e-mail (continuous project)": "From received e-mail (continuous project)", "From received e-mail, without long-term consent": "From received e-mail, without long-term consent", "From the left menu, choose API key": "From the left menu, choose API key", "Fruit and Vegetable Preserves Manufacturing": "Fruit and Vegetable Preserves Manufacturing", "Fulah": "<PERSON><PERSON>", "Full name": "Full name", "Full time": "Full time", "Full time with shifts": "Full time with shifts", "Full width picture": "Full width picture", "Full width video": "Full width video", "Fundraising": "Fundraising", "Funds and Trusts": "Funds and Trusts", "Furniture and Home Furnishings Manufacturing": "Furniture and Home Furnishings Manufacturing", "GDPR": "GDPR", "GDPR Consent admin": "GDPR Consent admin", "GDPR Preferences": "GDPR Preferences", "GDPR admin": "GDPR admin", "GDPR automation": "GDPR automation", "GDPR automation disabled": "GDPR automation disabled", "GDPR consent length": "GDPR consent length", "GDPR helper": "GDPR helper", "Gaelic, Scottish Gaelic": "Gaelic, Scottish Gaelic", "Galician": "Galician", "Gambling Facilities and Casinos": "Gambling Facilities and Casinos", "Ganda": "Ganda", "Gather references from candidates and automate outreach to previous employers.": "Gather references from candidates and automate outreach to previous employers.", "Gender": "Gender", "General": "General", "General Business": "General Business", "General Manager, Company Director, Regional Manager, CEO...": "General Manager, Company Director, Regional Manager, CEO...", "General secondary education": "General secondary education", "General/Department Store": "General/Department Store", "Generate Screening Criteria": "Generate Screening Criteria", "Generate a new API key": "Generate a new API key", "Generate preview": "Generate preview", "Generating...": "Generating...", "Georgian": "Georgian", "Geothermal Electric Power Generation": "Geothermal Electric Power Generation", "German": "German", "Get a Quote": "Get a Quote", "Get debug report": "Get debug report", "Get insights into your recruitment process. See how long it takes to fill a position, where candidates are dropping off, and more.": "Get insights into your recruitment process. See how long it takes to fill a position, where candidates are dropping off, and more.", "Get started": "Get started", "Gikuyu, Kikuyu": "Gikuyu, Kikuyu", "Give main quotes about the candidate's work experience so far.": "Give main quotes about the candidate's work experience so far.", "Given avg. score": "Given avg. score", "Glass Product Manufacturing": "Glass Product Manufacturing", "Glass, Ceramics and Concrete Manufacturing": "Glass, Ceramics and Concrete Manufacturing", "Go back": "Go back", "Go to requisition": "Go to requisition", "Go to the Forms menu and create a new Survey form.": "Go to the Forms menu and create a new Survey form.", "Golf Courses and Country Clubs": "Golf Courses and Country Clubs", "Good": "Good", "Good luck with filling the position!": "Good luck with filling the position!", "Good luck!": "Good luck!", "Good morning, :name!": "Good morning, :name!", "Google Calendar & Meet": "Google Calendar & Meet", "Government Administration": "Government Administration", "Government Relations Services": "Government Relations Services", "Grade": "Grade", "Graduate": "Graduate", "Graphic Design": "Graphic Design", "Greek": "Greek", "Greek (Modern)": "Greek (Modern)", "Greenlandic, Kalaallisut": "Greenlandic, Kalaallisut", "Grid": "Grid", "Grid of your other job ads": "Grid of your other job ads", "Ground Passenger Transportation": "Ground Passenger Transportation", "Group (CVbankas)": "Group (CVbankas)", "Group by": "Group by", "Group job ads by": "Group job ads by", "Guarani": "Guarani", "Gujarati": "Gujarati", "HR & Training": "HR & Training", "HVAC and Refrigeration Equipment Manufacturing": "HVAC and Refrigeration Equipment Manufacturing", "Haitian, Haitian Creole": "Haitian, Haitian Creole", "Happy hiring": "Happy hiring", "Has dropout reason(s)": "Has dropout reason(s)", "Has file": "Has file", "Has tag": "Has tag", "Hausa": "Hausa", "Have your IT-team configure daily backups of all your candidate data. Requires an NDA.": "Have your IT-team configure daily backups of all your candidate data. Requires an NDA.", "Having a sizeable talent database can significantly lower your cost and effort per hire. Meanwhile processing applicants' personal data without their documented consent leaves your company vulnerable to regulatory fines and litigation.": "Having a sizeable talent database can significantly lower your cost and effort per hire. Meanwhile processing applicants' personal data without their documented consent leaves your company vulnerable to regulatory fines and litigation.", "Headings": "Headings", "Headline + short text + background": "Headline + short text + background", "Heads up!": "Heads up!", "Heads up! Before you deactivate this user, review these linked items.": "Heads up! Before you deactivate this user, review these linked items.", "Health / Social care": "Health / Social care", "Health Care / Social work": "Health Care / Social work", "Health and Human Services": "Health and Human Services", "Health/Beauty": "Health/Beauty", "HealthCare Provider": "HealthCare Provider", "Healthcare": "Healthcare", "Hebrew": "Hebrew", "Height": "Height", "Hello!": "Hello!", "Hello, :name": "Hello, :name", "Hello, [recipient_full_name]!": "Hello, [recipient_full_name]!", "Hello, [user_name]!": "Hello, [user_name]!", "Help Center": "Help Center", "Here are 5 suggestions to start with:": "Here are 5 suggestions to start with:", "Here are the steps you should take:": "Here are the steps you should take:", "Herero": "<PERSON><PERSON>", "Hero with buttons": "Hero with buttons", "Hero with form": "Hero with form", "Heroes": "Heroes", "Hi": "Hi", "Hi!": "Hi!", "Hidden": "Hidden", "Hide": "<PERSON>de", "Hide archived": "Hide archived", "Hide archived landing pages from public": "Hide archived landing pages from public", "Hide files from limited users (except anonymized versions)": "Hide files from limited users (except anonymized versions)", "Hide inactive users": "Hide inactive users", "Hide messages from limited users": "Hide messages from limited users", "Hide navigation-only events": "Hide navigation-only events", "Hide personal data, but show AI-generated anonymized CV and summary": "Hide personal data, but show AI-generated anonymized CV and summary", "Hide personal data, but show AI-generated anonymized summary": "Hide personal data, but show AI-generated anonymized summary", "Hide question": "Hide question", "Hide salary": "Hide salary", "Hide stage categories": "Hide stage categories", "Hide tags from limited users": "Hide tags from limited users", "Hide talent pool": "Hide talent pool", "Hide this integration from other users": "Hide this integration from other users", "Hide weekends": "Hide weekends", "Higher Education": "Higher Education", "Higher level managers": "Higher level managers", "Higher rated first": "Higher rated first", "Higher scorecard score first": "Higher scorecard score first", "Highway, Street, and Bridge Construction": "Highway, Street, and Bridge Construction", "Hindi": "Hindi", "Hired": "<PERSON><PERSON>", "Hires": "<PERSON><PERSON>", "Hiri Motu": "<PERSON><PERSON>", "Hiring process suspended by the company": "Hiring process suspended by the company", "Historical Sites": "Historical Sites", "History": "History", "Holding Companies": "Holding Companies", "Holiday Allowance": "Holiday Allowance", "Holy days": "Holy days", "Home Health Care Services": "Home Health Care Services", "Home/DIY": "Home/DIY", "Horizontal bar with logo and optional CTA button": "Horizontal bar with logo and optional CTA button", "Horticulture": "Horticulture", "Hospitality": "Hospitality", "Hospitality/Hotel": "Hospitality/Hotel", "Hospitals": "Hospitals", "Hospitals and Health Care": "Hospitals and Health Care", "Host": "Host", "Host your job ads, career sites, and scheduler invites on your own domain. Your branding, our tech.": "Host your job ads, career sites, and scheduler invites on your own domain. Your branding, our tech.", "Hotels and Motels": "Hotels and Motels", "Hours": "Hours", "Household Appliance Manufacturing": "Household Appliance Manufacturing", "Household Services": "Household Services", "Household and Institutional Furniture Manufacturing": "Household and Institutional Furniture Manufacturing", "Housing Programs": "Housing Programs", "Housing and Community Development": "Housing and Community Development", "How does this work?": "How does this work?", "How it works": "How it works", "How likely are you to recommend Teamdash to a colleague?": "How likely are you to recommend <PERSON><PERSON><PERSON> to a colleague?", "How likely are you to recommend a friend or colleague to apply for a job with us?": "How likely are you to recommend a friend or colleague to apply for a job with us?", "How many days to offer?": "How many days to offer?", "How this works": "How this works", "How to automate cNPS feedback collection?": "How to automate cNPS feedback collection?", "How to include cNPS surveys in messages?": "How to include cNPS surveys in messages?", "Human Resources": "Human Resources", "Human Resources / Training": "Human Resources / Training", "Human Resources Services": "Human Resources Services", "Human resources": "Human resources", "Hungarian": "Hungarian", "Hungary": "Hungary", "Hybrid": "Hybrid", "Hydroelectric Power Generation": "Hydroelectric Power Generation", "Hyper campaign": "Hyper campaign", "I accept the": "I accept the", "I agree with having my data stored for 36 months.": "I agree with having my data stored for 36 months.", "I already have a membership": "I already have a membership", "I am interested in the Teams feature. Can you tell me more?": "I am interested in the Teams feature. Can you tell me more?", "I can't find the location": "I can't find the location", "I forgot my password": "I forgot my password", "I need an invoice": "I need an invoice", "I need help with this": "I need help with this", "I want to limit my consent": "I want to limit my consent", "IMAP messages sync": "IMAP messages sync", "IMAP path": "IMAP path", "IP": "IP", "IT": "IT", "IT Services and IT Consulting": "IT Services and IT Consulting", "IT System Custom Software Development": "IT System Custom Software Development", "IT System Data Services": "IT System Data Services", "IT System Design Services": "IT System Design Services", "IT System Installation and Disposal": "IT System Installation and Disposal", "IT System Operations and Maintenance": "IT System Operations and Maintenance", "IT System Testing and Evaluation": "IT System Testing and Evaluation", "IT System Training and Support": "IT System Training and Support", "Icelandic": "Icelandic", "Icon": "Icon", "Icon color": "Icon color", "Icon size": "Icon size", "Ido": "Ido", "If a user is in multiple SCIM groups with conflicting roles, the most permissive role is used.": "If a user is in multiple SCIM groups with conflicting roles, the most permissive role is used.", "If enabled, GDPR automation sends out \"consent renewal request\" emails.": "If enabled, GDPR automation sends out \"consent renewal request\" emails.", "If enabled, the auto-sync is run when group memberships are changed via SCIM or group mappings are changed in Teamdash.": "If enabled, the auto-sync is run when group memberships are changed via SCIM or group mappings are changed in Teamdash.", "If the integration has API keys that are directly linked, the feed will open only using those keys.": "If the integration has API keys that are directly linked, the feed will open only using those keys.", "If you agreed on a new time, they can select a time slot from the original scheduling email.": "If you agreed on a new time, they can select a time slot from the original scheduling email.", "If you already have an API key, copy it to the field above, otherwise, generate a new key": "If you already have an API key, copy it to the field above, otherwise, generate a new key", "If you are interested in our future open positions offers, please give your consent for data processing here: [consent_renewal_url].": "If you are interested in our future open positions offers, please give your consent for data processing here: [consent_renewal_url].", "If you are unable to participate": "If you are unable to participate", "If you are unable to participate, :declineLink.": "If you are unable to participate, :declineLink.", "If you are using Outlook, drag and drop candidate's email from your inbox and they will be automatically added to your project as a candidate.": "If you are using Outlook, drag and drop candidate's email from your inbox and they will be automatically added to your project as a candidate.", "If you cannot participate, you can cancel the interview and the other participants will be notified that the event needs to be rescheduled.": "If you cannot participate, you can cancel the interview and the other participants will be notified that the event needs to be rescheduled.", "If you did not receive the email": "If you did not receive the email", "If you did not request a password reset, no further action is required.": "If you did not request a password reset, no further action is required.", "If you don't choose any delivery channel, candidates will not get your invites.": "If you don't choose any delivery channel, candidates will not get your invites.", "If you don't have a landing page for this job yet, create it": "If you don't have a landing page for this job yet, create it", "If you don't have an ad for this job yet, create it": "If you don't have an ad for this job yet, create it", "If you don't map this field to a candidate field, the field value will still be available on form submissions page.": "If you don't map this field to a candidate field, the field value will still be available on form submissions page.", "If you don't reassign, GDPR automation will be disabled.": "If you don't reassign, GDPR automation will be disabled.", "If you don't understand it, just reply to this email :)": "If you don't understand it, just reply to this email :)", "If you edit an already published job ad in Teamdash, a new advertisement will be posted to ss.lv.": "If you edit an already published job ad in Teamdash, a new advertisement will be posted to ss.lv.", "If you have already shared this feed with someone, you will need to give them a new URL after you create the new key.": "If you have already shared this feed with someone, you will need to give them a new URL after you create the new key.", "If you have any questions, just click the support icon in bottom-right of your screen and ask away.": "If you have any questions, just click the support icon in bottom-right of your screen and ask away.", "If you have filters applied, check that you have selected the correct template type.": "If you have filters applied, check that you have selected the correct template type.", "If you have updated the form, click here to load changes.": "If you have updated the form, click here to load changes.", "If you have updated the form, click the refresh button on the form to load changes.": "If you have updated the form, click the refresh button on the form to load changes.", "If you leave before saving, your changes will be lost.": "If you leave before saving, your changes will be lost.", "If you set a value here, a button with this text will be displayed after form submission.": "If you set a value here, a button with this text will be displayed after form submission.", "If you wish to add more slots, you can do that in the :calendarLink.": "If you wish to add more slots, you can do that in the :calendarLink.", "If you wish to let the candidate reschedule the interview, you can add more slots in the :calendarLink.": "If you wish to let the candidate reschedule the interview, you can add more slots in the :calendarLink.", "If you wish to let the candidate reschedule the interview, you can send them a message under their profile.": "If you wish to let the candidate reschedule the interview, you can send them a message under their profile.", "If you wish to send out all email from @yourdomain.xyz, please set up a mail identity.": "If you wish to send out all email from @yourdomain.xyz, please set up a mail identity.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser": "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser", "If your address is <i>mycompany</i>.bamboohr.com, then use <i>mycompany</i> as username.": "If your address is <i>mycompany</i>.bamboohr.com, then use <i>mycompany</i> as username.", "Igbo": "Igbo", "Image": "Image", "Image ad": "Image ad", "Image generation URL": "Image generation URL", "Image position": "Image position", "Image settings": "Image settings", "Image:": "Image:", "Images": "Images", "Import candidates": "Import candidates", "Import connection OK!": "Import connection OK!", "Import from mailbox": "Import from mailbox", "Import from {integrationType}": "Import from {integrationType}", "Import users": "Import users", "Imported :count candidates": "Imported :count candidates", "Imported from external system": "Imported from external system", "Imports": "Imports", "Imports are synced automatically every 5 minutes.": "Imports are synced automatically every 5 minutes.", "Impressions": "Impressions", "Improve writing": "Improve writing", "In project view, click the three dots in stage header and choose \"Set automatic stage action\".": "In project view, click the three dots in stage header and choose \"Set automatic stage action\".", "In shifts": "In shifts", "Inactive": "Inactive", "Include a form that is linked to this project on a landing page": "Include a form that is linked to this project on a landing page", "Included on {landingPage}": "Included on {landingPage}", "Incoming message:": "Incoming message:", "Individual and Family Services": "Individual and Family Services", "Indonesian": "Indonesian", "Industrial Machinery Manufacturing": "Industrial Machinery Manufacturing", "Industry (CV-Library)": "Industry (CV-Library)", "Industry Associations": "Industry Associations", "Info": "Info", "Info message": "Info message", "Inform that ignoring this message is assumed to be a non-consent.": "Inform that ignoring this message is assumed to be a non-consent.", "Inform that they can immediately decline consent from the link.": "Inform that they can immediately decline consent from the link.", "Information Services": "Information Services", "Information Technology": "Information Technology", "Information technology": "Information technology", "Information tehcnology": "Information tehcnology", "Inherit candidate file access rights from projects.": "Inherit candidate file access rights from projects.", "Initialize comment visibility switch as public": "Initialize comment visibility switch as public", "Initialize project visibility switch as private": "Initialize project visibility switch as private", "Initiator": "Initiator", "Instance email provider": "Instance email provider", "Instant Upload": "Instant Upload", "Instant video call": "Instant video call", "Institution": "Institution", "Insurance": "Insurance", "Insurance Agencies and Brokerages": "Insurance Agencies and Brokerages", "Insurance Carriers": "Insurance Carriers", "Insurance and Employee Benefit Funds": "Insurance and Employee Benefit Funds", "Insurances": "Insurances", "Integration OK!": "Integration OK!", "Integration name": "Integration name", "Integrations": "Integrations", "Interior Design": "Interior Design", "Interlingua (International Auxiliary Language Association)": "Interlingua (International Auxiliary Language Association)", "Interlingue": "Interlingue", "Internal form": "Internal form", "Internal job ad": "Internal job ad", "Internal name used in Teamdash": "Internal name used in Teamdash", "International Affairs": "International Affairs", "International Trade and Development": "International Trade and Development", "Internet Marketplace Platforms": "Internet Marketplace Platforms", "Internet News": "Internet News", "Internship": "Internship", "Interurban and Rural Bus Services": "Interurban and Rural Bus Services", "Interview": "Interview", "Interview file": "Interview file", "Interview invite": "Interview invite", "Interview length": "Interview length", "Interview length (in minutes)": "Interview length (in minutes)", "Interview scheduled": "Interview scheduled", "Interview scheduling": "Interview scheduling", "Interview scheduling is not available within templates. You can add this action after creating a project.": "Interview scheduling is not available within templates. You can add this action after creating a project.", "Interview slot": "Interview slot", "Interview slot: :title on :start has been cancelled.": "Interview slot: :title on :start has been cancelled.", "Interview slot: :title scheduled for :start": "Interview slot: :title scheduled for :start", "Interview summary": "Interview summary", "Interview time": "Interview time", "Interview title": "Interview title", "Interview with {candidateName}": "Interview with {<PERSON><PERSON><PERSON>}", "Interviewees": "Interviewees", "Interviews": "Interviews", "Inuktitut": "Inuktitut", "Inupiaq": "Inupiaq", "Invalid": "Invalid", "Invalid color code": "Invalid color code", "Invalid confirmation code!": "Invalid confirmation code!", "Invalid data in row :row": "Invalid data in row :row", "Invalid phone number.": "Invalid phone number.", "Invalid user to reassign to": "Invalid user to reassign to", "Investment Advice": "Investment Advice", "Investment Banking": "Investment Banking", "Investment Management": "Investment Management", "Invitation details": "Invitation details", "Invite accepted": "<PERSON><PERSON><PERSON> accepted", "Invite candidates": "Invite candidates", "Invite confirmation": "Invite confirmation", "Invite message": "Invite message", "Invite new users": "Invite new users", "Invite pending": "Invite pending", "Invite sent to user! If there are further problems, forward the password reset url manually: :url": "Invite sent to user! If there are further problems, forward the password reset url manually: :url", "Invite sent!": "Invite sent!", "Invite subject": "Invite subject", "Invite users": "Invite users", "Invites": "<PERSON><PERSON><PERSON>", "Invites sent!": "<PERSON><PERSON><PERSON> sent!", "Irish": "Irish", "Is public (shared with other users)": "Is public (shared with other users)", "Is remote?": "Is remote?", "Is teams admin?": "Is teams admin?", "Is this an e-mail from a candidate? Drop it here!": "Is this an e-mail from a candidate? Drop it here!", "Is your database GDPR compliant?": "Is your database GDPR compliant?", "Issuer URL": "Issuer URL", "It MUST include [password_url]. It may include [current_user_name], [user_name].": "It MUST include [password_url]. It may include [current_user_name], [user_name].", "It asks your candidates to rate on a 0 to 10 scale -": "It asks your candidates to rate on a 0 to 10 scale -", "It may include :tags.": "It may include :tags.", "It seems that you don’t have access to this feature.": "It seems that you don’t have access to this feature.", "It uses your calendar integration to see when participants are busy.": "It uses your calendar integration to see when participants are busy.", "Italian": "Italian", "Items": "Items", "Jacket size": "Jacket size", "Janitorial Services": "Janitorial Services", "January": "January", "Japanese": "Japanese", "Javanese": "Javanese", "Job Location": "Job Location", "Job Unpublished (:orgName): :position": "Job Unpublished (:orgName): :position", "Job ad button text": "Job ad button text", "Job ad count text": "Job ad count text", "Job ad has been sent to your email.": "Job ad has been sent to your email.", "Job ad image": "Job ad image", "Job ad landing page hostname": "Job ad landing page hostname", "Job ads": "Job ads", "Job boards": "Job boards", "Job description preview": "Job description preview", "Job functions": "Job functions", "Job info": "Job info", "Job level (CVK/CVM)": "Job level (CVK/CVM)", "Job published!": "Job published!", "Job requisition": "Job requisition", "Job requisition added": "Job requisition added", "Job requisition deadline changed: new deadline in :deadline": "Job requisition deadline changed: new deadline in :deadline", "Job requisitions": "Job requisitions", "Job type": "Job type", "Join the video interview here:": "Join the video interview here:", "Join your team on Teamdash": "Join your team on Teamdash", "July": "July", "June": "June", "Kannada": "Kannada", "Kanuri": "<PERSON><PERSON><PERSON>", "Kashmiri": "Kashmiri", "Kazakh": "Kazakh", "Keep current timezone": "Keep current timezone", "Keep your Teamdash user list in sync with your company's identity provider.": "Keep your Teamdash user list in sync with your company's identity provider.", "Keep your accounts secure with two-factor authentication. Requires a mobile phone number.": "Keep your accounts secure with two-factor authentication. Requires a mobile phone number.", "Key name": "Key name", "Keys": "Keys", "Kinyarwanda": "Kinyarwanda", "Komi": "<PERSON><PERSON>", "Kongo": "Kong<PERSON>", "Korean": "Korean", "Kurdish": "Kurdish", "Kwanyama, Kuanyama": "Kwanyama, Kuanyama", "Kyrgyz": "Kyrgyz", "LARS code": "LARS code", "Label": "Label", "Landing": "Landing", "Landing URL copied to clipboard.": "Landing URL copied to clipboard.", "Landing Upload": "Landing Upload", "Landing page": "Landing page", "Landing page URL": "Landing page URL", "Landing pages": "Landing pages", "Landscaping Services": "Landscaping Services", "Language": "Language", "Language (CVbankas)": "Language (CVbankas)", "Language Proficiency level": "Language Proficiency level", "Language Schools": "Language Schools", "Language skills (profession.hu)": "Language skills (profession.hu)", "Lao": "Lao", "Last 30 days": "Last 30 days", "Last 30 scrapes of :feedName feed": "Last 30 scrapes of :feedName feed", "Last 60 days": "Last 60 days", "Last 90 days": "Last 90 days", "Last active": "Last active", "Last application date": "Last application date", "Last attempt": "Last attempt", "Last education": "Last education", "Last employment": "Last employment", "Last project": "Last project", "Last recommended attempt": "Last recommended attempt", "Last scraped at :lastScrapeTime": "Last scraped at :lastScrapeTime", "Last updated on {date}": "Last updated on {date}", "Latest bounced.": "Latest bounced.", "Latest clicked.": "Latest clicked.", "Latest opened.": "Latest opened.", "Latest sent.": "Latest sent.", "Latest version published": "Latest version published", "Latin": "Latin", "Latvia": "Latvia", "Latvian": "Latvian", "Laundry and Drycleaning Services": "Laundry and Drycleaning Services", "Law": "Law", "Law / Legal": "Law / Legal", "Law Enforcement": "Law Enforcement", "Law Practice": "Law Practice", "Learn more": "Learn more", "Leasing Non-residential Real Estate": "Leasing Non-residential Real Estate", "Leasing Real Estate Agents and Brokers": "Leasing Real Estate Agents and Brokers", "Leasing Residential Real Estate": "Leasing Residential Real Estate", "Leather Product Manufacturing": "Leather Product Manufacturing", "Leave empty for default": "Leave empty for default", "Leave empty if not applicable": "Leave empty if not applicable", "Leave empty to remove button": "Leave empty to remove button", "Leave without editing candidates": "Leave without editing candidates", "Left": "Left", "Left button link override": "Left button link override", "Legal": "Legal", "Legal Services": "Legal Services", "Legal ground": "Legal ground", "Legislative Offices": "Legislative Offices", "Leisure/Hospitality": "Leisure/Hospitality", "Leisure/Tourism": "Leisure/Tourism", "Less time in stage first": "Less time in stage first", "Let AI search the candidate's CV and add relevant tags if applicable.": "Let AI search the candidate's CV and add relevant tags if applicable.", "Let your candidates apply via form": "Let your candidates apply via form", "Let your managers request new hires and let management approve or reject the requests. All steps are customisable.": "Let your managers request new hires and let management approve or reject the requests. All steps are customisable.", "Letzeburgesch, Luxembourgish": "Letzeburgesch, Luxembourgish", "Level": "Level", "Level of education": "Level of education", "Libraries": "Libraries", "Lighter": "Lighter", "Limburgish, Limburgan, Limburger": "Limburgish, Limburgan, Limburger", "Lime and Gypsum Products Manufacturing": "Lime and Gypsum Products Manufacturing", "Limit interviews to start on ...": "Limit interviews to start on ...", "Limit:": "Limit:", "Limited user": "Limited user", "Limited users can see the same projects as regular users. Stages hidden from limited users stay hidden.": "Limited users can see the same projects as regular users. Stages hidden from limited users stay hidden.", "Limits enforced": "Limits enforced", "Limits not enforced": "Limits not enforced", "Lingala": "Lingala", "Link URL": "Link URL", "Link to current project": "Link to current project", "Linked to": "Linked to", "LinkedIn": "LinkedIn", "LinkedIn Company ID": "LinkedIn Company ID", "LinkedIn URL": "LinkedIn URL", "LinkedIn industry code": "LinkedIn industry code", "List": "List", "List all kinds of data you might be processing (e.g. CV, contact info, etc)": "List all kinds of data you might be processing (e.g. CV, contact info, etc)", "List of Sub-processors": "List of Sub-processors", "List of your other job ads": "List of your other job ads", "Lithuania": "Lithuania", "Lithuanian": "Lithuanian", "Load more": "Load more", "Loading": "Loading", "Loading landing page preview": "Loading landing page preview", "Loading...": "Loading...", "Loan Brokers": "Loan Brokers", "Location": "Location", "Location (CVO)": "Location (CVO)", "Location (profession.hu)": "Location (profession.hu)", "Location address": "Location address", "Location data": "Location data", "Location name": "Location name", "Location range": "Location range", "Location:": "Location:", "Locations": "Locations", "Log": "Log", "Log in to CV online, make sure you have an administrator account": "Log in to CV online, make sure you have an administrator account", "Log out": "Log out", "Login failed. The user does not have an email address associated with their account.": "<PERSON><PERSON> failed. The user does not have an email address associated with their account.", "Logistics": "Logistics", "Logistics / Transport": "Logistics / Transport", "Logo": "Logo", "Long term consent expiring soon": "Long term consent expiring soon", "Long text": "Long text", "Longer in stage first": "Longer in stage first", "Loss Prevention": "Loss Prevention", "Lower rated first": "Lower rated first", "Lower scorecard score first": "Lower scorecard score first", "Luba-Katanga": "Luba-Katanga", "MS Office messages sync": "MS Office messages sync", "MTD": "MTD", "Macedonian": "Macedonian", "Machinery Manufacturing": "Machinery Manufacturing", "Magnetic and Optical Media Manufacturing": "Magnetic and Optical Media Manufacturing", "Mail": "Mail", "Mail folder ID": "Mail folder ID", "Mail identities": "Mail identities", "Mail settings": "Mail settings", "Mail signature settings": "Mail signature settings", "Make default": "Make default", "Make import and application form submission comments visible to limited users": "Make import and application form submission comments visible to limited users", "Make longer": "Make longer", "Make private": "Make private", "Make public": "Make public", "Make sure this is a trusted address controlled by your organization.": "Make sure this is a trusted address controlled by your organization.", "Make this comment visible to limited users": "Make this comment visible to limited users", "Make this entry visible to limited users": "Make this entry visible to limited users", "Make your job ads accessible only from your company's internal network.": "Make your job ads accessible only from your company's internal network.", "Malagasy": "Malagasy", "Malay": "Malay", "Malayalam": "Malayalam", "Male": "Male", "Maltese": "Maltese", "Manage project access": "Manage project access", "Manage stage actions": "Manage stage actions", "Manage stage actions for {stageName}": "Manage stage actions for {stageName}", "Manage teams hierarchy": "Manage teams hierarchy", "Manage users": "Manage users", "Management": "Management", "Management/quality management": "Management/quality management", "Manager": "Manager", "Manager name": "Manager name", "Manager or coworker quotes block": "Manager or coworker quotes block", "Manager:": "Manager:", "Managing recruiter": "Managing recruiter", "Mandatory two-factor authentication": "Mandatory two-factor authentication", "Manually added to stage": "Manually added to stage", "Manufacturing": "Manufacturing", "Manufacturing / Production": "Manufacturing / Production", "Manufacturing/Surveying": "Manufacturing/Surveying", "Manx": "Manx", "Maori": "<PERSON><PERSON>", "Marathi": "Marathi", "March": "March", "Maritime Transportation": "Maritime Transportation", "Mark GDPR consent for imported candidates": "Mark GDPR consent for imported candidates", "Mark as favourite": "<PERSON> as favourite", "Mark finished": "<PERSON> finished", "Mark notifications as read": "Mark notifications as read", "Mark underage candidates": "Mark underage candidates", "Market Research": "Market Research", "Marketing": "Marketing", "Marketing / Advertising": "Marketing / Advertising", "Marketing / Advertising / PR": "Marketing / Advertising / PR", "Marketing Services": "Marketing Services", "Marketing/advertising": "Marketing/advertising", "Marshallese": "<PERSON><PERSON>", "Master's degree": "Master's degree", "Match candidates with all the conditions above.": "Match candidates with all the conditions above.", "Match candidates with at least one of the conditions above.": "Match candidates with at least one of the conditions above.", "Mattress and Blinds Manufacturing": "Mattress and Blinds Manufacturing", "Max attempt count: {count}": "Max attempt count: {count}", "Max candidate attempts": "Max candidate attempts", "Maximum value": "Maximum value", "May": "May", "Measuring and Control Instrument Manufacturing": "Measuring and Control Instrument Manufacturing", "Meat Products Manufacturing": "Meat Products Manufacturing", "Mechanics / Engineering": "Mechanics / Engineering", "Media": "Media", "Media / New media / Creative": "Media / New media / Creative", "Media / PR": "Media / PR", "Media Production": "Media Production", "Media and Telecommunications": "Media and Telecommunications", "Media/communication": "Media/communication", "Medical Equipment Manufacturing": "Medical Equipment Manufacturing", "Medical Practices": "Medical Practices", "Medical and Diagnostic Laboratories": "Medical and Diagnostic Laboratories", "Medical/Pharmaceutical/Scientific": "Medical/Pharmaceutical/Scientific", "Medicine/pharmacy": "Medicine/pharmacy", "Meet your candidates without scheduling a call. Candidates answer your questions on their own time.": "Meet your candidates without scheduling a call. Candidates answer your questions on their own time.", "Meeting analysis is already in progress.": "Meeting analysis is already in progress.", "Meeting details": "Meeting details", "Meeting location": "Meeting location", "Meeting location:": "Meeting location:", "Meeting room:": "Meeting room:", "Members": "Members", "Mental Health Care": "Mental Health Care", "Mentor:": "Mentor:", "Merchandising": "Merchandising", "Merge": "<PERSON><PERSON>", "Merge dropout reasons": "Merge dropout reasons", "Merge duplicate candidates": "Merge duplicate candidates", "Merge tags": "Merge tags", "Message": "Message", "Message Attachment": "Message Attachment", "Message body": "Message body", "Message details": "Message details", "Message not sent!": "Message not sent!", "Message project members": "Message project members", "Message sent!": "Message sent!", "Message subject": "Message subject", "Message templates": "Message templates", "Message will be sent in {delayRelative}": "Message will be sent in {delayRelative}", "Message:send": "Message", "Messages": "Messages", "Meta description": "Meta description", "Metal Ore Mining": "Metal Ore Mining", "Metal Treatments": "Metal Treatments", "Metal Valve, Ball, and Roller Manufacturing": "Metal Valve, Ball, and Roller Manufacturing", "Metalworking Machinery Manufacturing": "Metalworking Machinery Manufacturing", "Microsoft Calendar schedule": "Microsoft Calendar schedule", "Microsoft Teams calls": "Microsoft Teams calls", "Mid-senior level": "Mid-senior level", "Midlevel Manager / Senior Specialist": "Midlevel Manager / Senior Specialist", "Military and International Affairs": "Military and International Affairs", "Military/Emergency/Government": "Military/Emergency/Government", "Minimize": "Minimize", "Minimize column": "Minimize column", "Minimum amount of users needed approval from": "Minimum amount of users needed approval from", "Minimum number of minutes between the interview and the next event": "Minimum number of minutes between the interview and the next event", "Minimum number of minutes between the previous event and the interview": "Minimum number of minutes between the previous event and the interview", "Minimum value": "Minimum value", "Mining": "Mining", "Minutes": "Minutes", "Missing email!": "Missing email!", "Missing or invalid phone!": "Missing or invalid phone!", "Missing recorded video! Make sure you clicked the \"Confirm\" button after recording.": "Missing recorded video! Make sure you clicked the \"Confirm\" button after recording.", "Missing related project.": "Missing related project.", "Missing social media preview image. Please generate or upload it.": "Missing social media preview image. Please generate or upload it.", "Missing translation in {language}": "Missing translation in {language}", "Missing {missingCount} {approval}.": "Missing {missingCount} {approval}.", "Mixed": "Mixed", "Mobile Computing Software Products": "Mobile Computing Software Products", "Mobile Food Services": "Mobile Food Services", "Mobile Gaming Apps": "Mobile Gaming Apps", "Mobile View": "Mobile View", "Moldovan, Moldavian, Romanian": "Moldovan, Moldavian, Romanian", "Monday": "Monday", "Mongolian": "Mongolian", "Month": "Month", "Months": "Months", "Months of GDPR consent": "Months of GDPR consent", "More": "More", "More info": "More info", "Motor Vehicle Manufacturing": "Motor Vehicle Manufacturing", "Motor Vehicle Parts Manufacturing": "Motor Vehicle Parts Manufacturing", "Move candidates with a score": "Move candidates with a score", "Move down": "Move down", "Move the candidate to {stage}": "Move the candidate to {stage}", "Move this interview to another time and send event updates to all participants.": "Move this interview to another time and send event updates to all participants.", "Move to next stage": "Move to next stage", "Move to stage": "Move to stage", "Move up": "Move up", "Move {count} candidates": "Move {count} candidates", "Moved to stage": "Moved to stage", "Moved to stage by action": "Moved to stage by action", "Movie and Video Distribution": "Movie and Video Distribution", "Movies, Videos and Sound Recording": "Movies, Videos and Sound Recording", "Multimedia": "Multimedia", "Multiple files": "Multiple files", "Multiple stages": "Multiple stages", "Multiselect": "Multiselect", "Multiselect (checkboxes)": "Multiselect (checkboxes)", "Museums": "Museums", "Museums, Historical Sites, and Zoos": "Museums, Historical Sites, and Zoos", "Musicians": "Musicians", "Must accept - :attribute": "Must accept - :attribute", "Must be accepted": "Must be accepted", "Must be in lowercase a-z and underscore, at least 5 characters. This is used internally by the system.": "Must be in lowercase a-z and underscore, at least 5 characters. This is used internally by the system.", "Mute e-mail notifications": "Mute e-mail notifications", "My account": "My account", "My form": "My form", "Name": "Name", "Names of people": "Names of people", "Nanotechnology Research": "Nanotechnology Research", "Natural Gas Distribution": "Natural Gas Distribution", "Natural Gas Extraction": "Natural Gas Extraction", "Nauru": "Nauru", "Navajo, Navaho": "Navajo, Navaho", "Ndonga": "Ndonga", "Needed for reach estimation": "Needed for reach estimation", "Neighbourhood or parish (SS.lv)": "Neighbourhood or parish (SS.lv)", "Nepali": "Nepali", "Neutral": "Neutral", "New Job Posted by :orgName: :position": "New Job Posted by :orgName: :position", "New Project": "New Project", "New SMS from :name": "New SMS from :name", "New chat": "New chat", "New project": "New project", "New project display name": "New project display name", "New project from scratch": "New project from scratch", "New project name": "New project name", "New stage name": "New stage name", "New user invite template": "New user invite template", "Newer applications first": "Newer applications first", "Newest first": "Newest first", "Newspaper Publishing": "Newspaper Publishing", "Next": "Next", "Next decade": "Next decade", "Next file": "Next file", "Next month": "Next month", "Next question": "Next question", "Next year": "Next year", "Nice": "Nice", "No": "No", "No API keys added yet.": "No API keys added yet.", "No Event": "No Event", "No actions yet": "No actions yet", "No active links": "No active links", "No activity recorded yet.": "No activity recorded yet.", "No application methods yet": "No application methods yet", "No candidate has confirmed this time slot yet. You'll get an update once it's confirmed.": "No candidate has confirmed this time slot yet. You'll get an update once it's confirmed.", "No candidates": "No candidates", "No candidates found": "No candidates found", "No candidates have been shared with you yet.": "No candidates have been shared with you yet.", "No comments yet": "No comments yet", "No consent": "No consent", "No consent at all": "No consent at all", "No criteria generated. There might be too little context available. Project landing pages and descriptions are used to generate the criteria. You can enter the criteria manually.": "No criteria generated. There might be too little context available. Project landing pages and descriptions are used to generate the criteria. You can enter the criteria manually.", "No data": "No data", "No data available": "No data available", "No data processing consents": "No data processing consents", "No date selected": "No date selected", "No email": "No email", "No file chosen": "No file chosen", "No file types added yet.": "No file types added yet.", "No forms": "No forms", "No integrations yet": "No integrations yet", "No landing pages yet": "No landing pages yet", "No landings found": "No landings found", "No lower than higher education": "No lower than higher education", "No lower than secondary education": "No lower than secondary education", "No messages": "No messages", "No permissions": "No permissions", "No phone": "No phone", "No project log entries": "No project log entries", "No project roles added yet.": "No project roles added yet.", "No projects": "No projects", "No projects are included in this report. Change the filters to include some projects.": "No projects are included in this report. Change the filters to include some projects.", "No projects found": "No projects found", "No projects have been created from this template yet.": "No projects have been created from this template yet.", "No ratings": "No ratings", "No reason specified": "No reason specified", "No references": "No references", "No results found": "No results found", "No results text": "No results text", "No scheduled interviews found": "No scheduled interviews found", "No scorecards added yet.": "No scorecards added yet.", "No scorecards found.": "No scorecards found.", "No sentence may be longer than 200 characters.": "No sentence may be longer than 200 characters.", "No submissions yet": "No submissions yet", "No survey response yet": "No survey response yet", "No tasks found": "No tasks found", "No team": "No team", "No templates found": "No templates found", "No templates yet": "No templates yet", "No transcripts found": "No transcripts found", "No upcoming interviews": "No upcoming interviews", "No users found": "No users found", "No users will be notified.|1 user will be notified.|{count} users will be notified.": "No users will be notified.|1 user will be notified.|{count} users will be notified.", "No video interviews": "No video interviews", "No, let me double-check": "No, let me double-check", "Non-profit Organizations": "Non-profit Organizations", "None": "None", "None selected": "None selected", "Nonmetallic Mineral Mining": "Nonmetallic Mineral Mining", "Nonresidential Building Construction": "Nonresidential Building Construction", "North & Western Europe": "North & Western Europe", "Northern Ndebele": "Northern Ndebele", "Northern Sami": "Northern Sami", "Norwegian": "Norwegian", "Norwegian Bokmål": "Norwegian Bokmål", "Norwegian Nynorsk": "Norwegian Nynorsk", "Not Found": "Not Found", "Not a valid Vimeo URL.": "Not a valid Vimeo URL.", "Not a valid Youtube URL.": "Not a valid Youtube URL.", "Not a valid video file URL.": "Not a valid video file URL.", "Not allowed": "Not allowed", "Not applicable": "Not applicable", "Not duplicate": "Not duplicate", "Not included on any landing pages": "Not included on any landing pages", "Not scheduled yet": "Not scheduled yet", "Not specified": "Not specified", "Not started": "Not started", "Notes": "Notes", "Notes (not visible to candidates):": "Notes (not visible to candidates):", "Nothing here": "Nothing here", "Nothing to show": "Nothing to show", "Notice: integration health check failed - :website": "Notice: integration health check failed - :website", "Notification email": "Notification email", "Notifications are currently turned on. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn off.": "Notifications are currently turned on. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn off.", "November": "November", "Now let's create your personal account.": "Now let's create your personal account.", "Nuclear Electric Power Generation": "Nuclear Electric Power Generation", "Number of days": "Number of days", "Number of hours per week": "Number of hours per week", "Number of positions": "Number of positions", "Numeric rating": "Numeric rating", "Numeric value": "Numeric value", "Nuosu, Sichuan Yi": "Nuosu, Sichuan Yi", "Nursing": "Nursing", "Nursing Homes and Residential Care Facilities": "Nursing Homes and Residential Care Facilities", "OK": "OK", "OVP card": "OVP card", "Obligatory specialized education": "Obligatory specialized education", "Occitan (post 1500)": "Occitan (post 1500)", "Occurrences": "Occurrences", "October": "October", "Off": "Off", "Offer": "Offer", "Offer section title": "Offer section title", "Offers": "Offers", "Office": "Office", "Office Administration": "Office Administration", "Office Furniture and Fixtures Manufacturing": "Office Furniture and Fixtures Manufacturing", "Office Worker / Specialist": "Office Worker / Specialist", "Offices": "Offices", "Oh no": "Oh no", "Oil Extraction": "Oil Extraction", "Oil and Coal Product Manufacturing": "Oil and Coal Product Manufacturing", "Oil and Gas": "Oil and Gas", "Oil, Gas, and Mining": "Oil, Gas, and Mining", "Ojibwa": "Ojibwa", "Older applications first": "Older applications first", "On the second Tuesday of every month all your candidates without a valid data processing consent get a \"consent renewal request\" email.": "On the second Tuesday of every month all your candidates without a valid data processing consent get a \"consent renewal request\" email.", "On the top menu, click your company name": "On the top menu, click your company name", "On-site": "On-site", "Once logged in, go to the \"Manage app credentials\" here:": "Once logged in, go to the \"Manage app credentials\" here:", "Once someone submits a form, their submission will appear here.": "Once someone submits a form, their submission will appear here.", "Online Audio and Video Media": "Online Audio and Video Media", "Online and Mail Order Retail": "Online and Mail Order Retail", "Only": "Only", "Only YouTube URLs. Used for job board exports.": "Only YouTube URLs. Used for job board exports.", "Only YouTube links are supported.": "Only YouTube links are supported.", "Only active users can log in to the system.": "Only active users can log in to the system.", "Only administrators can delete clients.": "Only administrators can delete clients.", "Only fill if you want to connect to a shared mailbox that your account has access to.": "Only fill if you want to connect to a shared mailbox that your account has access to.", "Only from service accounts": "Only from service accounts", "Only my projects": "Only my projects", "Only project managers": "Only project managers", "Only run if": "Only run if", "Only show comments from current project": "Only show comments from current project", "Only show logs for this project": "Only show logs for this project", "Only summary": "Only summary", "Only you can see this message.": "Only you can see this message.", "Oops! Something went wrong.": "Oops! Something went wrong.", "Open": "Open", "Open CV": "Open CV", "Open Teamdash": "Open Teamdash", "Open candidate": "Open candidate", "Open candidate :initials profile": "Open candidate :initials profile", "Open form": "Open form", "Open integration": "Open integration", "Open invite": "Open invite", "Open landing": "Open landing", "Open profile": "Open profile", "Open project": "Open project", "Open slot": "Open slot", "Open slots": "Open slots", "Open the link in your phone.": "Open the link in your phone.", "OpenAI (US-based)": "OpenAI (US-based)", "Opened at": "Opened at", "Operations Consulting": "Operations Consulting", "Optional info": "Optional info", "Optionally choose a single form which triggers this action.": "Optionally choose a single form which triggers this action.", "Options": "Options", "Optometrists": "Optometrists", "Or add a criterion manually": "Or add a criterion manually", "Or upload video": "Or upload video", "Org. location city": "Org. location city", "Org. name": "Org. name", "Org. registry code": "Org. registry code", "Org. street address": "Org. street address", "Org. website URL": "Org. website URL", "Org. website url": "Org. website url", "Organisation management": "Organisation management", "Organization Logo": "Organization Logo", "Organization favicon": "Organization favicon", "Organization logo": "Organization logo", "Organization name": "Organization name", "Organization name override": "Organization name override", "Organization office address": "Organization office address", "Organization settings": "Organization settings", "Organization settings under the GDPR tab": "Organization settings under the GDPR tab", "Organization timezone": "Organization timezone", "Organization type": "Organization type", "Original location:": "Original location:", "Original value (you don't have access to it)": "Original value (you don't have access to it)", "Oriya": "Oriya", "Oromo": "Oromo", "Ossetian, Ossetic": "Ossetian, Ossetic", "Other": "Other", "Other Candidate file": "Other Candidate file", "Other Head Office Vacancies": "Other Head Office Vacancies", "Other Retailers": "Other Retailers", "Other exports": "Other exports", "Other file": "Other file", "Other icons": "Other icons", "Other integrations": "Other integrations", "Other languages": "Other languages", "Otherwise, we will not contact you about future vacancies at [organization_name].": "Otherwise, we will not contact you about future vacancies at [organization_name].", "Outcome description": "Outcome description", "Outpatient Care Centers": "Outpatient Care Centers", "Outsourcing and Offshoring Consulting": "Outsourcing and Offshoring Consulting", "Overdue!": "Overdue!", "Overlay": "Overlay", "Overrides the organization name from settings. Can be used if you're managing multiple brands": "Overrides the organization name from settings. Can be used if you're managing multiple brands", "Owner": "Owner", "P.S. You can configure the question {here}.": "P.S. You can configure the question {here}.", "Packaging and Containers Manufacturing": "Packaging and Containers Manufacturing", "Page Expired": "Page Expired", "Page background": "Page background", "Page meta description": "Page meta description", "Page meta title": "Page meta title", "Page saved!": "Page saved!", "Page title": "Page title", "Paint, Coating, and Adhesive Manufacturing": "Paint, Coating, and Adhesive Manufacturing", "Pali": "<PERSON><PERSON>", "Panjabi, Punjabi": "<PERSON><PERSON><PERSON>, Punjabi", "Paper and Forest Product Manufacturing": "Paper and Forest Product Manufacturing", "Part time": "Part time", "Participants": "Participants", "Participants will be notified that they are being recorded.": "Participants will be notified that they are being recorded.", "Participants:": "Participants:", "Pashto, Pushto": "Pashto, Pushto", "Password": "Password", "Password must not be in top 10M passwords": "Password must not be in top 10M passwords", "Pending": "Pending", "Pending job requisition": "Pending job requisition", "Pension Funds": "Pension Funds", "Perform an action": "Perform an action", "Performing Arts": "Performing Arts", "Performing Arts and Spectator Sports": "Performing Arts and Spectator Sports", "Periodical Publishing": "Periodical Publishing", "Permalink": "Permalink", "Permanent": "Permanent", "Permissions": "Permissions", "Persian": "Persian", "Person code": "Person code", "Personal": "Personal", "Personal Care Product Manufacturing": "Personal Care Product Manufacturing", "Personal Care Services": "Personal Care Services", "Personal and Laundry Services": "Personal and Laundry Services", "Personnel/Recruitment": "Personnel/Recruitment", "Pet Services": "Pet Services", "Pharmaceutical Manufacturing": "Pharmaceutical Manufacturing", "Pharmacy": "Pharmacy", "Philanthropic Fundraising Services": "Philanthropic Fundraising Services", "Phone": "Phone", "Phone number": "Phone number", "Phone number with country code": "Phone number with country code", "Phone with country code": "Phone with country code", "Photo": "Photo", "Photography": "Photography", "Physical, Occupational and Speech Therapists": "Physical, Occupational and Speech Therapists", "Physicians": "Physicians", "Ping all project users": "Ping all project users", "Ping select users": "Ping select users", "Ping user": "Ping user", "Ping users with roles": "Ping users with roles", "Pipeline Transportation": "Pipeline Transportation", "Pipeline overview": "Pipeline overview", "Placement": "Placement", "Plain text block": "Plain text block", "Plastics Manufacturing": "Plastics Manufacturing", "Plastics and Rubber Product Manufacturing": "Plastics and Rubber Product Manufacturing", "Platforms": "Platforms", "Play": "Play", "Play question videos": "Play question videos", "Please add a cNPS field to the form.": "Please add a cNPS field to the form.", "Please add screening criteria to get started.": "Please add screening criteria to get started.", "Please add the [:tag] merge tag!": "Please add the [:tag] merge tag!", "Please allow access to your camera and microphone.": "Please allow access to your camera and microphone.", "Please assign a category to every stage": "Please assign a category to every stage", "Please choose a form": "Please choose a form", "Please choose a form:": "Please choose a form:", "Please choose an image": "Please choose an image", "Please click the link below to verify your e-mail and create your Teamdash instance.": "Please click the link below to verify your e-mail and create your Teamdash instance.", "Please confirm": "Please confirm", "Please confirm that you want to anonymize {anonCount} candidates.": "Please confirm that you want to anonymize {anonCount} candidates.", "Please confirm that you want to permanently delete {anonCount} candidates.": "Please confirm that you want to permanently delete {anonCount} candidates.", "Please confirm your password before continuing.": "Please confirm your password before continuing.", "Please contact support at {mail} to enable SMS features": "Please contact support at {mail} to enable SMS features", "Please contact support for custom e-mail setups.": "Please contact support for custom e-mail setups.", "Please contact your account manager to enable AI candidate screening.": "Please contact your account manager to enable AI candidate screening.", "Please contact your account manager to get started with cNPS reporting.": "Please contact your account manager to get started with cNPS reporting.", "Please enter a valid email address.": "Please enter a valid email address.", "Please enter your references": "Please enter your references", "Please insert your e-mail!": "Please insert your e-mail!", "Please make your decision": "Please make your decision", "Please note that Indeed expects the content of the published job ad to exactly match the details listed on the landing page.": "Please note that <PERSON> expects the content of the published job ad to exactly match the details listed on the landing page.", "Please note that ss.lv does not allow editing job ads once they have been published.": "Please note that ss.lv does not allow editing job ads once they have been published.", "Please paste iframe URL below or click {here} to choose a form from a list.": "Please paste iframe URL below or click {here} to choose a form from a list.", "Please publish landing to export an image.": "Please publish landing to export an image.", "Please re-connect your integration": "Please re-connect your integration", "Please resolve all conflicts before continuing.": "Please resolve all conflicts before continuing.", "Please resolve conflicting data before merging.": "Please resolve conflicting data before merging.", "Please review the following:": "Please review the following:", "Please save the landing page to add a form.": "Please save the landing page to add a form.", "Please select a response.": "Please select a response.", "Please select a video.": "Please select a video.", "Please select an interview time": "Please select an interview time", "Please select at least one candidate to send a message.": "Please select at least one candidate to send a message.", "Please select at least one consent type or give a general consent.": "Please select at least one consent type or give a general consent.", "Please select at least one user to send a message.": "Please select at least one user to send a message.", "Please select either members or roles to ping.": "Please select either members or roles to ping.", "Please select time slots": "Please select time slots", "Please set a password here: [password_url]": "Please set a password here: [password_url]", "Please update {updateLink} or contact your system administrator.": "Please update {updateLink} or contact your system administrator.", "Please upload an attachment or remove the option": "Please upload an attachment or remove the option", "Please use SSO!": "Please use SSO!", "Please write briefly what you want them to do.": "Please write briefly what you want them to do.", "Please {updateYourBrowserHere} or contact your system administrator.": "Please {updateYourBrowserHere} or contact your system administrator.", "Polish": "Polish", "Political Organizations": "Political Organizations", "Poor": "Poor", "Port": "Port", "Portuguese": "Portuguese", "Position": "Position", "Position details": "Position details", "Position fit:": "Position fit:", "Position fit: {positionFit}%": "Position fit: {positionFit}%", "Position info": "Position info", "Position name": "Position name", "Position name is required!": "Position name is required!", "Position select": "Position select", "Position title": "Position title", "Position type": "Position type", "Position type (cv.lt)": "Position type (cv.lt)", "Possible duplicates": "Possible duplicates", "Possible duplicates found": "Possible duplicates found", "Possibly irrelevant candidate": "Possibly irrelevant candidate", "Post": "Post", "Postal Services": "Postal Services", "Postcode": "Postcode", "Postmark": "Postmark", "Pre-scheduled": "Pre-scheduled", "Prebooked Holidays": "Prebooked Holidays", "Precise location": "Precise location", "Prefer high quality": "Prefer high quality", "Prefer small file size": "Prefer small file size", "Preferences": "Preferences", "Preferred Name": "Preferred Name", "Premium campaign": "Premium campaign", "Present candidates": "Present candidates", "Present your why-work-here arguments with this block.": "Present your why-work-here arguments with this block.", "Preview": "Preview", "Preview font": "Preview font", "Preview form": "Preview form", "Previous": "Previous", "Previous chats": "Previous chats", "Previous decade": "Previous decade", "Previous file": "Previous file", "Previous month": "Previous month", "Previous week": "Previous week", "Previous year": "Previous year", "Previously uploaded": "Previously uploaded", "Primary Metal Manufacturing": "Primary Metal Manufacturing", "Primary and Secondary Education": "Primary and Secondary Education", "Primary color (hex)": "Primary color (hex)", "Primary colour": "Primary colour", "Printing Services": "Printing Services", "Privacy notice": "Privacy notice", "Privacy policy": "Privacy policy", "Privacy policy URL": "Privacy policy URL", "Private email": "Private email", "Private integration": "Private integration", "Private job ads": "Private job ads", "Private talent pool for teams": "Private talent pool for teams", "Probation period (days)": "Probation period (days)", "Product Management": "Product Management", "Production": "Production", "Production / Manufactoring": "Production / Manufactoring", "Professional": "Professional", "Professional Organizations": "Professional Organizations", "Professional Services": "Professional Services", "Programme": "Programme", "Project": "Project", "Project (Teamdash)": "Project (Teamdash)", "Project / stage": "Project / stage", "Project ID": "Project ID", "Project ID (Teamdash)": "Project ID (Teamdash)", "Project Log": "Project Log", "Project Management": "Project Management", "Project action added!": "Project action added!", "Project actions": "Project actions", "Project actions for {position}": "Project actions for {position}", "Project actions will be automatically created based on the template you select.": "Project actions will be automatically created based on the template you select.", "Project couldn't be converted to a template.": "Project couldn't be converted to a template.", "Project created": "Project created", "Project created!": "Project created!", "Project custom fields": "Project custom fields", "Project end date": "Project end date", "Project entry date": "Project entry date", "Project failure reason saved!": "Project failure reason saved!", "Project failure reasons": "Project failure reasons", "Project finished!": "Project finished!", "Project log": "Project log", "Project log for {position}": "Project log for {position}", "Project manager": "Project manager", "Project manager (Teamdash)": "Project manager (Teamdash)", "Project roles": "Project roles", "Project stages": "Project stages", "Project start date": "Project start date", "Project statistics": "Project statistics", "Project status is": "Project status is", "Project status, deadlines, warranties and other dates can be found here now.": "Project status, deadlines, warranties and other dates can be found here now.", "Project updated": "Project updated", "Project users and teams can now be added and removed in one convenient place.": "Project users and teams can now be added and removed in one convenient place.", "Project visible only for members": "Project visible only for members", "Project-based roles": "Project-based roles", "Projects": "Projects", "Projects count": "Projects count", "Projects managed by": "Projects managed by", "Projects status": "Projects status", "Projects using {templateName} as template": "Projects using {templateName} as template", "Promotional content": "Promotional content", "Prompt for a reason when a candidate drops out": "Prompt for a reason when a candidate drops out", "Property Services": "Property Services", "Provider": "Provider", "Public / Governmental Service": "Public / Governmental Service", "Public Assistance Programs": "Public Assistance Programs", "Public Health": "Public Health", "Public Policy Offices": "Public Policy Offices", "Public Relations": "Public Relations", "Public Relations and Communications Services": "Public Relations and Communications Services", "Public Safety": "Public Safety", "Public Sector": "Public Sector", "Public administration/civil service": "Public administration/civil service", "Public domain": "Public domain", "Public servant": "Public servant", "Publish": "Publish", "Publish a new job ad": "Publish a new job ad", "Publish new": "Publish new", "Publish the landing page to download as image": "Publish the landing page to download as image", "Publish to external channels": "Publish to external channels", "Publish {landingName}": "Publish {landingName}", "Published as": "Published as", "Published job ads": "Published job ads", "Published on": "Published on", "Published until": "Published until", "Publishing to this combination of channels is currently impossible.": "Publishing to this combination of channels is currently impossible.", "Purchasing": "Purchasing", "Push candidates to BambooHR ATS": "Push candidates to BambooHR ATS", "Qualification type": "Qualification type", "Qualifications": "Qualifications", "Quality assurance": "Quality assurance", "QualityAssurance": "QualityAssurance", "Quechua": "Quechua", "Question": "Question", "Question {no}": "Question {no}", "Question {questionNumber}": "Question {questionNumber}", "Questions": "Questions", "Questions:": "Questions:", "Quick call": "Quick call", "Quote author photo": "Quote author photo", "Quotes": "Quotes", "Racetracks": "Racetracks", "Radio and Television Broadcasting": "Radio and Television Broadcasting", "Rail Transportation": "Rail Transportation", "Railroad Equipment Manufacturing": "Railroad Equipment Manufacturing", "Ranching": "Ranching", "Ranching and Fisheries": "Ranching and Fisheries", "Range": "Range", "Rate the candidate:": "Rate the candidate:", "Rating": "Rating", "Rating saved.": "Rating saved.", "Reach": "Reach", "Read activities": "Read activities", "Read audit logs": "Read audit logs", "Read candidates": "Read candidates", "Read database dumps": "Read database dumps", "Read feeds": "Read feeds", "Read landing statistics": "Read landing statistics", "Read more about AI-generated transcripts and summaries {asyncLink}.": "Read more about AI-generated transcripts and summaries {asyncLink}.", "Read more about asynchronous video interviews {asyncLink}.": "Read more about asynchronous video interviews {asyncLink}.", "Read more about cNPS": "Read more about cNPS", "Read more about cNPS.": "Read more about cNPS.", "Read more about internal forms": "Read more about internal forms", "Read project status reports": "Read project status reports", "Read projects": "Read projects", "Read settings": "Read settings", "Reading candidate profile...": "Reading candidate profile...", "Ready to answer": "Ready to answer", "Ready! Set! Hire!": "Ready! Set! Hire!", "Real Estate": "Real Estate", "Real Estate and Equipment Rental Services": "Real Estate and Equipment Rental Services", "Real estate": "Real estate", "Reason": "Reason", "Reason for candidate to drop out of applying": "Reason for candidate to drop out of applying", "Reason for company to reject candidate": "Reason for company to reject candidate", "Reason for consent revocation": "Reason for consent revocation", "Reason for hiring:": "Reason for hiring:", "Reassign to...": "Reassign to...", "Received SMS": "Received SMS", "Received message via integration": "Received message via integration", "Recommended attempts exhausted": "Recommended attempts exhausted", "Record": "Record", "Record activity:": "Record activity:", "Record new activity": "Record new activity", "Record new video": "Record new video", "Record response": "Record response", "Record video": "Record video", "Recorded call placeholder": "Recorded call placeholder", "Recording stopped automatically after time ran out.": "Recording stopped automatically after time ran out.", "Records all candidate data accesses and modifications. The logs are retained according to your internal policies.": "Records all candidate data accesses and modifications. The logs are retained according to your internal policies.", "Recreate project list dropdowns": "Recreate project list dropdowns", "Recreational Facilities": "Recreational Facilities", "Recruiter": "Rec<PERSON>er", "Recruiter contacts": "Recruiter contacts", "Recruiters": "Recruiters", "Recruitment": "Recruitment", "Recruitment Performance": "Recruitment Performance", "Recruitment performance": "Recruitment performance", "Recruitment software invite": "Recruitment software invite", "Redirect URL": "Redirect URL", "Reference Checks": "Reference Checks", "Reference form": "Reference form", "Reference forms": "Reference forms", "Reference request sent": "Reference request sent", "Reference submitted on {submissionTime}": "Reference submitted on {submissionTime}", "Reference submitted on {submissionTime} by {submissionUser}": "Reference submitted on {submissionTime} by {submissionUser}", "Reference type": "Reference type", "References": "References", "References pending first": "References pending first", "References submitted first": "References submitted first", "Referral budget (€)": "Referral budget (€)", "Referral fee percentage": "Referral fee percentage", "Refresh this page.": "Refresh this page.", "Regards": "Regards", "Regenerate": "Regenerate", "Regenerate summary": "Regenerate summary", "Region": "Region", "Region (CV-Library)": "Region (CV-Library)", "Register": "Register", "Register a new membership": "Register a new membership", "Regular user": "Regular user", "Reject": "Reject", "Rejected": "Rejected", "Rejected at": "Rejected at", "Relax!": "Relax!", "Religious Institutions": "Religious Institutions", "Reload": "Reload", "Relocate from EU": "Relocate from EU", "Remember Me": "Remember Me", "Remember, that the candidate will not receive notification of new slots, so you may wish to send them a message under their profile.": "Remember, that the candidate will not receive notification of new slots, so you may wish to send them a message under their profile.", "Reminder: Job Ad Builder Tool by Teamdash": "Reminder: Job Ad Builder Tool by Teamdash", "Remote": "Remote", "Remote links": "Remote links", "Remote project": "Remote project", "Remove": "Remove", "Remove candidate": "Remove candidate", "Remove criterion": "Remove criterion", "Remove filter": "Remove filter", "Remove from favourites": "Remove from favourites", "Remove from project": "Remove from project", "Remove image": "Remove image", "Remove import": "Remove import", "Remove link": "Remove link", "Remove quote": "Remove quote", "Remove slot": "Remove slot", "Remove this candidate from the event.": "Remove this candidate from the event.", "Removed dropout reason": "Removed dropout reason", "Removed from project": "Removed from project", "Render failed at": "<PERSON>der failed at", "Renew token": "Renew token", "Renewable Energy Semiconductor Manufacturing": "Renewable Energy Semiconductor Manufacturing", "Renewal link is valid for": "Renewal link is valid for", "Renewal text": "Renewal text", "Repair and Maintenance": "Repair and Maintenance", "Report generation started": "Report generation started", "Require answers in strict order": "Require answers in strict order", "Require project failure reasons?": "Require project failure reasons?", "Required": "Required", "Required field": "Required field", "Required languages": "Required languages", "Requirements": "Requirements", "Requirements section title": "Requirements section title", "Requisition Attachment": "Requisition Attachment", "Requisition custom fields": "Requisition custom fields", "Requisition info field template": "Requisition info field template", "Requisitions": "Requisitions", "Reschedule": "Reschedule", "Reschedule time slot": "Reschedule time slot", "Research": "Research", "Research Services": "Research Services", "Resend invite": "Resend invite", "Reset": "Reset", "Reset Password": "Reset Password", "Reset Password Notification": "Reset Password Notification", "Residential Building Construction": "Residential Building Construction", "Resolved task": "Resolved task", "Responded": "Responded", "Response": "Response", "Response rate": "Response rate", "Response time limit": "Response time limit", "Response time limit: {timeLimit}": "Response time limit: {timeLimit}", "Responses": "Responses", "Restaurants": "Restaurants", "Restore": "Rest<PERSON>", "Retail": "Retail", "Retail Apparel and Fashion": "Retail Apparel and Fashion", "Retail Appliances, Electrical, and Electronic Equipment": "Retail Appliances, Electrical, and Electronic Equipment", "Retail Art Dealers": "Retail Art Dealers", "Retail Art Supplies": "Retail Art Supplies", "Retail Books and Printed News": "Retail Books and Printed News", "Retail Building Materials and Garden Equipment": "Retail Building Materials and Garden Equipment", "Retail Florists": "Retail Florists", "Retail Furniture and Home Furnishings": "Retail Furniture and Home Furnishings", "Retail Gasoline": "Retail Gasoline", "Retail Groceries": "Retail Groceries", "Retail Health and Personal Care Products": "Retail Health and Personal Care Products", "Retail Luxury Goods and Jewelry": "Retail Luxury Goods and Jewelry", "Retail Motor Vehicles": "Retail Motor Vehicles", "Retail Musical Instruments": "Retail Musical Instruments", "Retail Office Equipment": "Retail Office Equipment", "Retail Office Supplies and Gifts": "Retail Office Supplies and Gifts", "Retail Recyclable Materials & Used Merchandise": "Retail Recyclable Materials & Used Merchandise", "Retail/Purchasing": "Retail/Purchasing", "Retrieved full profile": "Retrieved full profile", "Retry": "Retry", "Retry meeting analysis": "Retry meeting analysis", "Retype password": "Retype password", "Reupholstery and Furniture Repair": "Reupholstery and Furniture Repair", "Review": "Review", "Review approvals": "Review approvals", "Review integrations": "Review integrations", "Review now": "Review now", "Revoke": "Revoke", "Revoke consent": "Revoke consent", "Revoked by": "Revoked by", "Right": "Right", "Right button link override": "Right button link override", "Role": "Role", "Role for new users": "Role for new users", "Romanian": "Romanian", "Romansh": "Romansh", "Room address:": "Room address:", "Rows": "Rows", "Rubber Products Manufacturing": "Rubber Products Manufacturing", "Rules": "Rules", "Run an AI-assisted social media campaign for attracting passive talent.": "Run an AI-assisted social media campaign for attracting passive talent.", "Run check": "Run check", "Run now": "Run now", "Run related imports": "Run related imports", "Run screener": "Run screener", "Run screener anyway": "Run screener anyway", "Rundi": "<PERSON><PERSON>", "Running...": "Running...", "Russian": "Russian", "SCIM group mappings": "SCIM group mappings", "SCIM group synchronisation follows these rules:": "SCIM group synchronisation follows these rules:", "SCIM groups and roles": "SCIM groups and roles", "SES": "SES", "SMS": "SMS", "SMS body": "SMS body", "SMS confirmation message": "SMS confirmation message", "SMS message": "SMS message", "SMS sent!": "SMS sent!", "SSO": "SSO", "SSO is enabled!": "SSO is enabled!", "Salary": "Salary", "Salary currency": "Salary currency", "Salary from": "Salary from", "Salary from (gross)": "Salary from (gross)", "Salary info": "Salary info", "Salary period": "Salary period", "Salary range": "Salary range", "Salary rate": "Salary rate", "Salary to": "Salary to", "Salary to (gross)": "Salary to (gross)", "Sales": "Sales", "Sales / Retail": "Sales / Retail", "Sales Assistants": "Sales Assistants", "Sales Manager, Head of Department, Chief Accountant, Marketing Manager, Construction Manager...": "Sales Manager, Head of Department, Chief Accountant, Marketing Manager, Construction Manager...", "Salesman, Cashier, Waiter, Hotel Administrator, Security Guard...": "Salesman, Cashier, Waiter, Hotel Administrator, Security Guard...", "Samoan": "Samoan", "Sample text": "Sample text", "Sango": "Sango", "Sanskrit": "Sanskrit", "Sardinian": "Sardinian", "Satellite Telecommunications": "Satellite Telecommunications", "Saturday": "Saturday", "Save": "Save", "Save and close": "Save and close", "Save and next": "Save and next", "Save as new": "Save as new", "Save as template": "Save as template", "Save candidate and add to current project": "Save candidate and add to current project", "Save changes": "Save changes", "Save or update active filters for later use.": "Save or update active filters for later use.", "Save project action": "Save project action", "Save stage action": "Save stage action", "Saved": "Saved", "Saved filters": "Saved filters", "Saved!": "Saved!", "Saved.": "Saved.", "Saving draft...": "Saving draft...", "Saving...": "Saving...", "Savings Institutions": "Savings Institutions", "Scan the QR code.": "Scan the QR code.", "Scandinavia": "Scandinavia", "Schedule a Consultation": "Schedule a Consultation", "Schedule an interview or upload a video.": "Schedule an interview or upload a video.", "Schedule interview": "Schedule interview", "Schedule interviews": "Schedule interviews", "Scheduled": "Scheduled", "Scheduled meetings:": "Scheduled meetings:", "Scheduling type": "Scheduling type", "School and Employee Bus Services": "School and Employee Bus Services", "Science": "Science", "Scientific": "Scientific", "Score": "Score", "Scorecard": "Scorecard", "Scorecard comments": "Scorecard comments", "Scorecards": "Scorecards", "Scored candidate": "Scored candidate", "Screening": "Screening", "Screening questions": "Screening questions", "Screening results cleared": "Screening results cleared", "Scroll to zoom in or out": "Scroll to zoom in or out", "Seafood Product Manufacturing": "Seafood Product Manufacturing", "Search": "Search", "Search by image name...": "Search by image name...", "Search by name, tags, file contents, etc...": "Search by name, tags, file contents, etc...", "Search by video name...": "Search by video name...", "Search candidate": "Search candidate", "Search here.": "Search here.", "Search user": "Search user", "Search...": "Search...", "Seasonal": "Seasonal", "Secondary Colour": "Secondary Colour", "Secretarial Schools": "Secretarial Schools", "Section with links to your social media pages": "Section with links to your social media pages", "Section with recruiter contacts": "Section with recruiter contacts", "Sector type": "Sector type", "Sectors (profession.hu)": "Sectors (profession.hu)", "Securities and Commodity Exchanges": "Securities and Commodity Exchanges", "Security": "Security", "Security & compliance": "Security & compliance", "Security / Rescue / Defence": "Security / Rescue / Defence", "Security Guards and Patrol Services": "Security Guards and Patrol Services", "Security Systems Services": "Security Systems Services", "Security and Investigations": "Security and Investigations", "Security services": "Security services", "See more...": "See more...", "See you soon!": "See you soon!", "Select": "Select", "Select a Block": "Select a Block", "Select a category": "Select a category", "Select a font": "Select a font", "Select a reason...": "Select a reason...", "Select a reference form": "Select a reference form", "Select a room": "Select a room", "Select a template": "Select a template", "Select a thread to view messages.": "Select a thread to view messages.", "Select a time": "Select a time", "Select all": "Select all", "Select all {count} candidates": "Select all {count} candidates", "Select an action from the left to add to the stage.": "Select an action from the left to add to the stage.", "Select an option from the left to start your new project.": "Select an option from the left to start your new project.", "Select another profile to merge with {candidateName}": "Select another profile to merge with {candidate<PERSON>ame}", "Select candidates to merge": "Select candidates to merge", "Select interview": "Select interview", "Select interview to see preview.": "Select interview to see preview.", "Select none": "Select none", "Select roles": "Select roles", "Select users": "Select users", "Select video": "Select video", "Selected candidate is already in the chosen project. | Selected candidates are already in the chosen project.": "Selected candidate is already in the chosen project. | Selected candidates are already in the chosen project.", "Selected date": "Selected date", "Selected fields": "Selected fields", "Selected time": "Selected time", "Self-Managed Backups": "Self-Managed Backups", "Semiconductor Manufacturing": "Semiconductor Manufacturing", "Send": "Send", "Send Password Reset Link": "Send Password Reset Link", "Send SMS": "Send SMS", "Send a message to project members": "Send a message to project members", "Send a message to start the conversation.": "Send a message to start the conversation.", "Send a message to the candidate": "Send a message to the candidate", "Send a message to {candidateName}": "Send a message to {<PERSON><PERSON><PERSON>}", "Send a message to {count} candidates": "Send a message to {count} candidates", "Send a message to {count} users": "Send a message to {count} users", "Send a message to {userName}": "Send a message to {userName}", "Send a reference check request to the candidate's referees": "Send a reference check request to the candidate's referees", "Send a video interview to the candidate": "Send a video interview to the candidate", "Send a video message to the candidate": "Send a video message to the candidate", "Send a video message to {candidateName}": "Send a video message to {candidate<PERSON><PERSON>}", "Send a video message to {count} candidates": "Send a video message to {count} candidates", "Send an SMS to {candidateName}": "Send an SMS to {candidate<PERSON><PERSON>}", "Send an SMS to {count} candidates": "Send an SMS to {count} candidates", "Send an interview scheduling request to the candidate": "Send an interview scheduling request to the candidate", "Send as": "Send as", "Send candidates": "Send candidates", "Send consent renewal messages": "Send consent renewal messages", "Send consent renewals": "Send consent renewals", "Send homework assignments, screening questions, consent requests or ask for any additional data from candidates.": "Send homework assignments, screening questions, consent requests or ask for any additional data from candidates.", "Send interview": "Send interview", "Send invitations": "Send invitations", "Send invites": "Send invites", "Send message": "Send message", "Send out cNPS surveys and ask candidates for feedback.": "Send out cNPS surveys and ask candidates for feedback.", "Send out message at": "Send out message at", "Send tentative scheduler time slots to interviewers": "Send tentative scheduler time slots to interviewers", "Send this candidate an invite to get started.": "Send this candidate an invite to get started.", "Send to": "Send to", "Send to :recipient": "Send to :recipient", "Send to {hrisName}": "Send to {hrisName}", "Send video interview": "Send video interview", "Send video interview invites": "Send video interview invites", "Send video message": "Send video message", "Send webhook": "Send webhook", "Sending report": "Sending report", "Sending your message...": "Sending your message...", "Sending...": "Sending...", "Senior Manager / Director": "Senior Manager / Director", "Sent": "<PERSON><PERSON>", "Sent SMS": "Sent SMS", "Sent at": "Sent at", "Sent message": "Sent message", "Sent to HRIS": "Sent to HRIS", "September": "September", "Serbian": "Serbian", "Server Error": "Server Error", "Service & Sales Representative": "Service & Sales Representative", "Service account": "Service account", "Service industry": "Service industry", "Service user": "Service user", "Services": "Services", "Services for the Elderly and Disabled": "Services for the Elderly and Disabled", "Set as new project manager": "Set as new project manager", "Set dates": "Set dates", "Set type to {type}": "Set type to {type}", "Set up Azure AD SSO": "Set up Azure AD SSO", "Set up on": "Set up on", "Settings": "Settings", "Setup": "Setup", "Setup instructions": "Setup instructions", "Share": "Share", "Share on Facebook": "Share on Facebook", "Share on LinkedIn": "Share on LinkedIn", "Share to": "Share to", "Share with": "Share with", "Share with Teamdash users?": "Share with Teamdash users?", "Share with users": "Share with users", "Shared by": "Shared by", "Shared candidate": "Shared candidate", "Shared candidates": "Shared candidates", "Shared mailbox address": "Shared mailbox address", "Shared with": "Shared with", "Sharing preview photo": "Sharing preview photo", "Sheet Music Publishing": "Sheet Music Publishing", "Shift-click on a team and drag to another team to connect them with a directed edge": "Shift-click on a team and drag to another team to connect them with a directed edge", "Shift-click on team to change its label": "Shift-click on team to change its label", "Shift-click on whitespace to create a team": "Shift-click on whitespace to create a team", "Shipbuilding": "Shipbuilding", "Shoe size": "Shoe size", "Shona": "<PERSON><PERSON><PERSON>", "Short message": "Short message", "Short name": "Short name", "Short name for the consent type. Max 4 characters. We suggest an emoji.": "Short name for the consent type. Max 4 characters. We suggest an emoji.", "Show": "Show", "Show advanced filters": "Show advanced filters", "Show all personal data": "Show all personal data", "Show all projects to limited users": "Show all projects to limited users", "Show archived": "Show archived", "Show candidate initials in blind hiring mode": "Show candidate initials in blind hiring mode", "Show candidate journeys": "Show candidate journeys", "Show candidates with new activities": "Show candidates with new activities", "Show comments indicator": "Show comments indicator", "Show done tasks": "Show done tasks", "Show email": "Show email", "Show entire image": "Show entire image", "Show filter": "Show filter", "Show icon for location": "Show icon for location", "Show if candidate is active in other projects": "Show if candidate is active in other projects", "Show images in grid": "Show images in grid", "Show inactive users": "Show inactive users", "Show invites together": "Show invites together", "Show location": "Show location", "Show me the candidates": "Show me the candidates", "Show messages indicator": "Show messages indicator", "Show non-public project logs to limited users": "Show non-public project logs to limited users", "Show on candidate card": "Show on candidate card", "Show on job ad": "Show on job ad", "Show only my tasks": "Show only my tasks", "Show pending/answered video interview indicator": "Show pending/answered video interview indicator", "Show pending/scheduled interview indicator": "Show pending/scheduled interview indicator", "Show pending/submitted references indicator": "Show pending/submitted references indicator", "Show personal data alongside AI-generated summary": "Show personal data alongside AI-generated summary", "Show phone": "Show phone", "Show question": "Show question", "Show source": "Show source", "Show stage categories": "Show stage categories", "Show talent pool": "Show talent pool", "Show tasks from all projects": "Show tasks from all projects", "Show weekends": "Show weekends", "Shows how candidates are divided across stage categories at specific moments in time": "Shows how candidates are divided across stage categories at specific moments in time", "Shuttles and Special Needs Transportation Services": "Shuttles and Special Needs Transportation Services", "Sightseeing Transportation": "Sightseeing Transportation", "Sign in": "Sign in", "Sign into your Indeed account": "Sign into your Indeed account", "Signature accent color": "Signature accent color", "Signature additional text": "Signature additional text", "Signature preview": "Signature preview", "Similarity to selected": "Similarity to selected", "Simplify language": "Simplify language", "Since this is an SMS, keep it short.": "Since this is an SMS, keep it short.", "Sindhi": "Sindhi", "Single Sign-On": "Single Sign-On", "Single stage": "Single stage", "Sinhala, Sinhalese": "Sinhala, Sinhalese", "Size": "Size", "Skiing Facilities": "Skiing Facilities", "Skilled Labor": "Skilled Labor", "Skilled Worker / Professional": "Skilled Worker / Professional", "Skills": "Skills", "Skip": "<PERSON><PERSON>", "Skip this month": "Skip this month", "Slots": "Slots", "Slovak": "Slovak", "Slovenian": "Slovenian", "Slug": "Slug", "Soap and Cleaning Product Manufacturing": "Soap and Cleaning Product Manufacturing", "Social Care": "Social Care", "Social Networking Platforms": "Social Networking Platforms", "Social media": "Social media", "Social media sharing image": "Social media sharing image", "Software Development": "Software Development", "Solar Electric Power Generation": "Solar Electric Power Generation", "Somali": "Somali", "Some candidates were not be copied.": "Some candidates were not be copied.", "Some inactive users are selected. Deselect them to hide inactive users.": "Some inactive users are selected. Deselect them to hide inactive users.", "Some invites can't be delivered via selected channels.": "Some invites can't be delivered via selected channels.", "Someone": "Someone", "Someone applied via email?": "Someone applied via email?", "Someone has booked this time already": "Someone has booked this time already", "Something went wrong!": "Something went wrong!", "Something went wrong! Please try again.": "Something went wrong! Please try again.", "Sort by": "Sort by", "Sort by:": "Sort by:", "Sort candidates": "Sort candidates", "Sort manually": "Sort manually", "Sorting": "Sorting", "Sotho, Southern": "Sotho, Southern", "Sound Recording": "Sound Recording", "Source": "Source", "Source tags": "Source tags", "South Ndebele": "South Ndebele", "Space Research and Technology": "Space Research and Technology", "Spanish": "Spanish", "Spanish, Castilian": "Spanish, Castilian", "Specialty Trade Contractors": "Specialty Trade Contractors", "Specific form": "Specific form", "Specific height": "Specific height", "Specific slots": "Specific slots", "Specifying the language may improve transcription accuracy. If left unspecified, language will be detected automatically.": "Specifying the language may improve transcription accuracy. If left unspecified, language will be detected automatically.", "Spectator Sports": "Spectator Sports", "Spoken language": "Spoken language", "Sporting Goods Manufacturing": "Sporting Goods Manufacturing", "Sports Teams and Clubs": "Sports Teams and Clubs", "Sports and Recreation Instruction": "Sports and Recreation Instruction", "Spring and Wire Product Manufacturing": "Spring and Wire Product Manufacturing", "Staffing and Recruiting": "Staffing and Recruiting", "Stage": "Stage", "Stage (Teamdash)": "Stage (Teamdash)", "Stage ID": "Stage ID", "Stage ID (Teamdash)": "Stage ID (Teamdash)", "Stage action added!": "Stage action added!", "Stage category": "Stage category", "Stage deleted successfully.": "Stage deleted successfully.", "Stage header dropdown menu": "Stage header dropdown menu", "Stage hidden from limited users": "Stage hidden from limited users", "Stage name": "Stage name", "Stage visibility settings": "Stage visibility settings", "Stages": "Stages", "Stages & actions": "Stages & actions", "Stages and actions will be automatically created based on the template you select.": "Stages and actions will be automatically created based on the template you select.", "Standard campaign": "Standard campaign", "Standard fields": "Standard fields", "Start": "Start", "Start Date": "Start Date", "Start SSO setup": "Start SSO setup", "Start a new AI chat with this interview": "Start a new AI chat with this interview", "Start a project": "Start a project", "Start date": "Start date", "Start interview": "Start interview", "Start month": "Start month", "Start recording": "Start recording", "Start tracking your candidate experience using cNPS surveys.": "Start tracking your candidate experience using cNPS surveys.", "Start year": "Start year", "Started: {date}": "Started: {date}", "State": "State", "Static text": "Static text", "Statistics": "Statistics", "Statistics for {landingName}": "Statistics for {landingName}", "Status": "Status", "Status: Enabled": "Status: Enabled", "Steam and Air-Conditioning Supply": "Steam and Air-Conditioning Supply", "Stop recording": "Stop recording", "Store Management": "Store Management", "Store the interview summary in a structured format. Should be called with an array in which each element is a string containing a summarization sentence.": "Store the interview summary in a structured format. Should be called with an array in which each element is a string containing a summarization sentence.", "Strategic Management Services": "Strategic Management Services", "Strategy / Planning": "Strategy / Planning", "Structured Ad Image": "Structured Ad Image", "Styling": "Styl<PERSON>", "Subdivision of Land": "Subdivision of Land", "Subject": "Subject", "Submission": "Submission", "Submission for {formName}": "Submission for {formName}", "Submissions": "Submissions", "Submissions for :form": "Submissions for :form", "Submissions: :form": "Submissions: :form", "Submit": "Submit", "Submit answer": "Submit answer", "Submit button label": "Submit button label", "Submit reference from {referenceName}": "Submit reference from {referenceName}", "Submitted a form": "Submitted a form", "Submitted for {positionName} on {uploadTime}": "Submitted for {positionName} on {uploadTime}", "Submitted referees": "Submitted referees", "Submitted reference": "Submitted reference", "Submitting": "Submitting", "Success": "Success", "Success text": "Success text", "Success!": "Success!", "Sugar and Confectionery Product Manufacturing": "Sugar and Confectionery Product Manufacturing", "Summarization failed": "Summarization failed", "Sundanese": "Sundanese", "Sunday": "Sunday", "Super campaign": "Super campaign", "Supermarket/Food": "Supermarket/Food", "Supervisor": "Supervisor", "Supply Chain": "Supply Chain", "Supports merge tags.": "Supports merge tags.", "Surname": "Surname", "Survey": "Survey", "Survey form": "Survey form", "Survey response": "Survey response", "Surveys sent": "Surveys sent", "Swahili": "Swahili", "Swati": "<PERSON><PERSON>", "Swedish": "Swedish", "Switch off asking for dropout reasons": "Switch off asking for dropout reasons", "Switch to columns view": "Switch to columns view", "Switch to table view": "Switch to table view", "Sync all users": "Sync all users", "T-shirt size": "T-shirt size", "TTF can only be calculated if your filters include finished projects.": "TTF can only be calculated if your filters include finished projects.", "Table": "Table", "Tag": "Tag", "Tag name": "Tag name", "Tagalog": "Tagalog", "Tags": "Tags", "Tags to add": "Tags to add", "Tahitian": "Tahitian", "Tajik": "Tajik", "Take fields from": "Take fields from", "Talenme.com": "Talenme.com", "Talent Pool": "Talent Pool", "Talent.com Job category": "Talent.com Job category", "Tamil": "Tamil", "Target stage": "Target stage", "Target tag": "Target tag", "Task": "Task", "Task in project {project}": "Task in project {project}", "Task title": "Task title", "Task title:": "Task title:", "Tasks": "Tasks", "Tasks with deadline today:": "Tasks with deadline today:", "Tatar": "Tatar", "Taxi and Limousine Services": "Taxi and Limousine Services", "Team": "Team", "Team settings:": "Team settings:", "Team-Based Access Controls": "Team-Based Access Controls", "Team-based access controls make permission handling easier and facilitate managing multiple departments or brands under one account.": "Team-based access controls make permission handling easier and facilitate managing multiple departments or brands under one account.", "Teamdash Feed": "<PERSON><PERSON><PERSON>ed", "Teamdash Report": "Teamdash Report", "Teamdash Social Media Tool distributes your job ad in social media. This helps you attract passive talent and reach a more diverse audience than just job board visitors.": "Teamdash Social Media Tool distributes your job ad in social media. This helps you attract passive talent and reach a more diverse audience than just job board visitors.", "Teamdash Team": "Teamdash Team", "Teamdash is an easy-to-use recruitment software, a place you can manage all your recruitment projects with your team members.": "Teamdash is an easy-to-use recruitment software, a place you can manage all your recruitment projects with your team members.", "Teamdash will anonymize {candidateCount} {anonymizeDiff}.": "Teamdash will anonymize {candidateCount} {anonymizeDiff}.", "Teamdash will ask data processing consent renewals {candidateCount} {renewalDiff}.": "Teamdash will ask data processing consent renewals {candidateCount} {renewalDiff}.", "Teamdash's GDPR automation helps you acquire, manage, document and monitor all your candidates' data processing consents. It works like this:": "Teamdash's GDPR automation helps you acquire, manage, document and monitor all your candidates' data processing consents. It works like this:", "Teams": "Teams", "Teams admins can modify the teams hierarchy and assign any user to any team.": "Teams admins can modify the teams hierarchy and assign any user to any team.", "Teams saved!": "Teams saved!", "Tech support": "Tech support", "Technical / Engineering": "Technical / Engineering", "Technical and Vocational Training": "Technical and Vocational Training", "Technology": "Technology", "Technology and Information": "Technology and Information", "Technology, Information and Internet": "Technology, Information and Internet", "Technology, Information and Media": "Technology, Information and Media", "Telecommunications": "Telecommunications", "Telecommunications Carriers": "Telecommunications Carriers", "Telecoms": "Telecoms", "Telephone Call Centers": "Telephone Call Centers", "Telugu": "Telugu", "Template": "Template", "Template created": "Template created", "Template created!": "Template created!", "Template display name": "Template display name", "Template log": "Template log", "Template name": "Template name", "Template name should be descriptive eg. \"Interview invite\". This is not visible to the candidate.": "Template name should be descriptive eg. \"Interview invite\". This is not visible to the candidate.", "Template name should be descriptive eg. \"Rejection letter\". This is not the subject.": "Template name should be descriptive eg. \"Rejection letter\". This is not the subject.", "Template name should be descriptive eg. \"Request for feedback\". This is not the subject.": "Template name should be descriptive eg. \"Request for feedback\". This is not the subject.", "Template owner": "Template owner", "Template visible for": "Template visible for", "Templates": "Templates", "Temporary": "Temporary", "Temporary Help Services": "Temporary Help Services", "Tentative": "Tentative", "Terms": "Terms", "Terms of use": "Terms of use", "Test connection": "Test connection", "Text": "Text", "Text color": "Text color", "Text color (hex)": "Text color (hex)", "Text of transcribed speech": "Text of transcribed speech", "Text shadow": "Text shadow", "Text with background": "Text with background", "Textarea": "Textarea", "Textile Manufacturing": "Textile Manufacturing", "Thai": "Thai", "Thank you for reporting a problem. We will use your feedback to improve the location search functionality.": "Thank you for reporting a problem. We will use your feedback to improve the location search functionality.", "Thank you for using Teamdash!": "Thank you for using Teamdash!", "Thank you for your response!": "Thank you for your response!", "Thank you!": "Thank you!", "Thank you! We have saved your preferences!": "Thank you! We have saved your preferences!", "Thank you! Your details have been sent!": "Thank you! Your details have been sent!", "Thank you! Your feedback was received!": "Thank you! Your feedback was received!", "Thank you! Your interview is scheduled at": "Thank you! Your interview is scheduled at", "Thank you! Your references have been saved.": "Thank you! Your references have been saved.", "The URL must begin with HTTPS.": "The URL must begin with HTTPS.", "The action will be automatically executed after receiving a video interview response. It will be delayed for selected time.": "The action will be automatically executed after receiving a video interview response. It will be delayed for selected time.", "The action will be automatically executed after the project status has changed.": "The action will be automatically executed after the project status has changed.", "The action will be automatically executed when the candidate has been in stage longer than the selected time.": "The action will be automatically executed when the candidate has been in stage longer than the selected time.", "The action will be executed only when you click the button that appears in stage header.": "The action will be executed only when you click the button that appears in stage header.", "The array must contain between 3 and 10 sentences.": "The array must contain between 3 and 10 sentences.", "The body colour may be hard to read on the background colour. Try increasing the contrast.": "The body colour may be hard to read on the background colour. Try increasing the contrast.", "The candidate chose a time, but due to the error they did not get an email confirmation. Please contact support.": "The candidate chose a time, but due to the error they did not get an email confirmation. Please contact support.", "The candidate did not record any videos.": "The candidate did not record any videos.", "The candidate gets a link where they can choose an interview time.": "The candidate gets a link where they can choose an interview time.", "The candidate has not yet submitted the preboarding information.": "The candidate has not yet submitted the preboarding information.", "The candidate has submitted preboarding information.": "The candidate has submitted preboarding information.", "The candidate has submitted the following preboarding information. It will be sent to {hrisName} together with the handover form.": "The candidate has submitted the following preboarding information. It will be sent to {hrisName} together with the handover form.", "The candidate might not have a CV attached or the CV might be unreadable or too long.": "The candidate might not have a CV attached or the CV might be unreadable or too long.", "The candidate recorded {count} videos, but did not submit any of them.": "The candidate recorded {count} videos, but did not submit any of them.", "The candidates have no conflicting fields. Ready to merge!": "The candidates have no conflicting fields. Ready to merge!", "The category field is required.": "The category field is required.", "The consent expires when this project is finished.": "The consent expires when this project is finished.", "The deadline for review is in :deadline, so you better do it now.": "The deadline for review is in :deadline, so you better do it now.", "The deadline for review is in :deadline. You will be notified if the job requisition succeeds.": "The deadline for review is in :deadline. You will be notified if the job requisition succeeds.", "The default date and time format for your organization.": "The default date and time format for your organization.", "The default language for new users in your organization.": "The default language for new users in your organization.", "The display name becomes visible to candidates when using the [position_display_name] merge tag.": "The display name becomes visible to candidates when using the [position_display_name] merge tag.", "The file must contain name, email, and role columns.": "The file must contain name, email, and role columns.", "The final score is calculated: cNPS = % promoters - % detractors.": "The final score is calculated: cNPS = % promoters - % detractors.", "The following criteria may be discriminatory or otherwise unsuitable and should be reviewed:": "The following criteria may be discriminatory or otherwise unsuitable and should be reviewed:", "The following e-mail will be sent to recipient:": "The following e-mail will be sent to recipient:", "The font file extensions must be one of the following: :extensions.": "The font file extensions must be one of the following: :extensions.", "The interview has multiple people being interviewed and their names are as follows:": "The interview has multiple people being interviewed and their names are as follows:", "The interview is conducted by a person named :interviewer.": "The interview is conducted by a person named :interviewer.", "The interview is conducted by the following interviewers:": "The interview is conducted by the following interviewers:", "The interview is for a job position titled \":position\" in the company \":company\".": "The interview is for a job position titled \":position\" in the company \":company\".", "The interview is for a job position titled \":position\" with involvement by the following companies: :companies.": "The interview is for a job position titled \":position\" with involvement by the following companies: :companies.", "The interview is for a job position titled \":position\".": "The interview is for a job position titled \":position\".", "The job ad can be accessed only by logged in users and from internal network.": "The job ad can be accessed only by logged in users and from internal network.", "The latest error was:": "The latest error was:", "The maximum value must be greater than the minimum value.": "The maximum value must be greater than the minimum value.", "The meeting recording is not yet available for analysis.": "The meeting recording is not yet available for analysis.", "The person being interviewed is named :person.": "The person being interviewed is named :person.", "The recruiter has prepared one question for you. | The recruiter has prepared {count} questions for you.": "The recruiter has prepared one question for you. | The recruiter has prepared {count} questions for you.", "The segments returned must be in the exact same order as input.": "The segments returned must be in the exact same order as input.", "The selected candidate does not have a valid phone number.": "The selected candidate does not have a valid phone number.", "The selected candidate does not have an email address.": "The selected candidate does not have an email address.", "The speech that was originally transcribed might have included the following proper names:": "The speech that was originally transcribed might have included the following proper names:", "The summary should be useful for making good hiring decisions.": "The summary should be useful for making good hiring decisions.", "The survey responses are aggregated into three groups by their score.": "The survey responses are aggregated into three groups by their score.", "The table must have the following columns: Name, Email, Phone, Tags. The file may include one or more comment columns named Comment1, Comment2 etc. Download sample file <a href=\"/files/import_sample.xlsx\" target=\"_blank\">here</a>.": "The table must have the following columns: Name, Email, Phone, Tags. The file may include one or more comment columns named Comment1, Comment2 etc. Download sample file <a href=\"/files/import_sample.xlsx\" target=\"_blank\">here</a>.", "The team with the biggest number of accessible teams is chosen.": "The team with the biggest number of accessible teams is chosen.", "The transcribed text will not be affected.": "The transcribed text will not be affected.", "The uploaded file is too large.": "The uploaded file is too large.", "The user will send you the transcript which will be a JSON array of transcript segments": "The user will send you the transcript which will be a JSON array of transcript segments", "The user will send you the transcript which will be a JSON array of transcript segments, each being a JSON object with two keys: \"text\" and \"time\".": "The user will send you the transcript which will be a JSON array of transcript segments, each being a JSON object with two keys: \"text\" and \"time\".", "The video recording failed unexpectedly. Please refresh the page.": "The video recording failed unexpectedly. Please refresh the page.", "The video recording {willBeChangedAutomatically} after the time has run out.": "The video recording {willBeChangedAutomatically} after the time has run out.", "The {anonymizedCv} option will also show an anonymized version of the candidate's CV.": "The {anonymizedCv} option will also show an anonymized version of the candidate's CV.", "Theater Companies": "Theater Companies", "Theme": "Theme", "Then choose \"Message\" and include the [survey] tag inside the message.": "Then choose \"Message\" and include the [survey] tag inside the message.", "There are candidates, actions or statistics associated with this stage.": "There are candidates, actions or statistics associated with this stage.", "There are no actions defined for this stage.": "There are no actions defined for this stage.", "There are no cNPS responses yet.": "There are no cNPS responses yet.", "There are no candidates in this time slot.": "There are no candidates in this time slot.", "There are no more open slots in this interview.": "There are no more open slots in this interview.", "There are no other dropout reasons to merge with.": "There are no other dropout reasons to merge with.", "There are no project log entries yet.": "There are no project log entries yet.", "There are no upcoming interviews. You can try changing the filters above the calendar.": "There are no upcoming interviews. You can try changing the filters above the calendar.", "There is no data available for the selected filters.": "There is no data available for the selected filters.", "There is no data to display.": "There is no data to display.", "There is not enough data to display this report. Try changing the selected filters and/or date range.": "There is not enough data to display this report. Try changing the selected filters and/or date range.", "There must be at least one category for each system category": "There must be at least one category for each system category", "There must be at least one custom category for every system category": "There must be at least one custom category for every system category", "There was a problem loading the form options.": "There was a problem loading the form options.", "There was a problem with at least one of the video responses.": "There was a problem with at least one of the video responses.", "There was a problem with sending the candidates.": "There was a problem with sending the candidates.", "There was an error": "There was an error", "There was an error anonymizing the candidates. Please refresh this page.": "There was an error anonymizing the candidates. Please refresh this page.", "There was an error committing stage transitions. Please refresh the page.": "There was an error committing stage transitions. Please refresh the page.", "There was an error loading this report.": "There was an error loading this report.", "There was an error removing the action.": "There was an error removing the action.", "There was an error removing this stage action.": "There was an error removing this stage action.", "There was an error sending your messages.": "There was an error sending your messages.", "There was an error submitting the form. Please try again.": "There was an error submitting the form. Please try again.", "There was an error updating the event.": "There was an error updating the event.", "There was an error uploading the file.": "There was an error uploading the file.", "There was an error with the interview. Please contact support.": "There was an error with the interview. Please contact support.", "There was an error.": "There was an error.", "There was an error. Please try again.": "There was an error. Please try again.", "There were candidates for whom we could not determine some fields. You can review the information below and update it if necessary.": "There were candidates for whom we could not determine some fields. You can review the information below and update it if necessary.", "There's a project associated with this requisition.": "There's a project associated with this requisition.", "These actions cannot be reassigned.": "These actions cannot be reassigned.", "These are the videos the candidate submitted:": "These are the videos the candidate submitted:", "These candidates have one or more overlapping fields. Click on a name to compare.": "These candidates have one or more overlapping fields. Click on a name to compare.", "These fields will be added to the handover form.": "These fields will be added to the handover form.", "These fields will be added to the job ad publication form.": "These fields will be added to the job ad publication form.", "These fields will be shown in the job ad publication form.": "These fields will be shown in the job ad publication form.", "These images will be added to the job posting. You can drag and drop to change the order.": "These images will be added to the job posting. You can drag and drop to change the order.", "These tags will be removed.": "These tags will be removed.", "These users are out of sync with their roles from SCIM": "These users are out of sync with their roles from SCIM", "These users will be added to every project you create from this template.": "These users will be added to every project you create from this template.", "These users will be asked to approve or reject the requisition.": "These users will be asked to approve or reject the requisition.", "They will be notified when you post the comment.": "They will be notified when you post the comment.", "They will get a notification about the cancellation.": "They will get a notification about the cancellation.", "They wrote:": "They wrote:", "Think Tanks": "Think Tanks", "Third sector / NGO": "Third sector / NGO", "This action can only be triggered automatically.": "This action can only be triggered automatically.", "This action cannot be undone.": "This action cannot be undone.", "This action is automatically executed if the candidate is in stage on or after {actionScheduledAtUser}. You can execute the action immediately by pressing this button.": "This action is automatically executed if the candidate is in stage on or after {actionScheduledAtUser}. You can execute the action immediately by pressing this button.", "This action is automatically executed once the candidate has been in stage for {actionDelayValue} {actionDelayUnit}. You can execute the action immediately by pressing this button.": "This action is automatically executed once the candidate has been in stage for {actionDelayValue} {actionDelayUnit}. You can execute the action immediately by pressing this button.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after receiving a video interview response.": "This action is automatically executed {actionDelayValue} {actionDelayUnit} after receiving a video interview response.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted a form.": "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted a form.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted their references.": "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted their references.", "This action was sent to {candidateCount} candidates at {actionTime}.": "This action was sent to {candidateCount} candidates at {actionTime}.", "This allows you to send semi-automatic messages and video interviews": "This allows you to send semi-automatic messages and video interviews", "This campaign is particularly recommended for roles that are relatively easy to fill, such as customer service representatives, warehouse workers, and other similar positions.": "This campaign is particularly recommended for roles that are relatively easy to fill, such as customer service representatives, warehouse workers, and other similar positions.", "This campaign is the go-to choice for critical yet moderately easy-to-fill positions such as restaurant workers and junior-level professionals.": "This campaign is the go-to choice for critical yet moderately easy-to-fill positions such as restaurant workers and junior-level professionals.", "This campaign is the ideal choice for filling challenging positions, such as healthcare professionals and experienced technical or commercial specialists.": "This campaign is the ideal choice for filling challenging positions, such as healthcare professionals and experienced technical or commercial specialists.", "This campaign level is suitable for supporting individual, highly challenging recruitment eﬀorts, such as software developers or experienced professionals.": "This campaign level is suitable for supporting individual, highly challenging recruitment eﬀorts, such as software developers or experienced professionals.", "This can help you make less biased decision for a more inclusive hiring process.": "This can help you make less biased decision for a more inclusive hiring process.", "This can not be undone.": "This can not be undone.", "This candidate does not have any data processing consents.": "This candidate does not have any data processing consents.", "This candidate does not have any references yet.": "This candidate does not have any references yet.", "This candidate has a pending consent renewal.": "This candidate has a pending consent renewal.", "This candidate has been anonymized.": "This candidate has been anonymized.", "This candidate has not been sent any video interviews.": "This candidate has not been sent any video interviews.", "This candidate is in {count} unfinished projects.": "This candidate is in {count} unfinished projects.", "This category contains candidates from ongoing recruitment projects.": "This category contains candidates from ongoing recruitment projects.", "This category contains candidates from recently finished projects.": "This category contains candidates from recently finished projects.", "This client does not have any offices.": "This client does not have any offices.", "This client does not have any projects.": "This client does not have any projects.", "This client is associated with 1 active project|This client is associated with {count} active projects": "This client is associated with 1 active project|This client is associated with {count} active projects", "This client is associated with 1 finished project.|This client is associated with {count} finished projects.": "This client is associated with 1 finished project.|This client is associated with {count} finished projects.", "This client is not associated with any projects.": "This client is not associated with any projects.", "This consent covers only processing related to the position they applied to.": "This consent covers only processing related to the position they applied to.", "This consent request has expired.": "This consent request has expired.", "This data was extracted with the help of AI": "This data was extracted with the help of AI", "This distinction is critical to ensure that your most important messages do not go into Gmail promotions tab.": "This distinction is critical to ensure that your most important messages do not go into Gmail promotions tab.", "This document is valid until :expiry.": "This document is valid until :expiry.", "This e-mail is sent when you add a new user to your organization.": "This e-mail is sent when you add a new user to your organization.", "This failure reason cannot be deleted as it has already been assigned to some projects.": "This failure reason cannot be deleted as it has already been assigned to some projects.", "This field MUST include the [presentation_url] tag.": "This field MUST include the [presentation_url] tag.", "This field is used internally and for reporting. It can contain lowercase a-z and underscores (e.g. if your activity name is \"Sent out free t-shirt\" then you'd put tshirt_sent here).": "This field is used internally and for reporting. It can contain lowercase a-z and underscores (e.g. if your activity name is \"Sent out free t-shirt\" then you'd put tshirt_sent here).", "This form cannot be accessed directly, because it does not contain an email field. Please include the form in an e-mail via the [form_url] merge tag.": "This form cannot be accessed directly, because it does not contain an email field. Please include the form in an e-mail via the [form_url] merge tag.", "This form is automatic and uses the stage defined on the landing page": "This form is automatic and uses the stage defined on the landing page", "This form is directly linked to a stage, applicants will always be added to this stage": "This form is directly linked to a stage, applicants will always be added to this stage", "This form is used in one or more templates: {templates}": "This form is used in one or more templates: {templates}", "This generates automatic summaries of candidate CV-s.": "This generates automatic summaries of candidate CV-s.", "This includes confidential projects. Do not turn on hastily.": "This includes confidential projects. Do not turn on hastily.", "This inherits all access rights to candidate profiles from projects.": "This inherits all access rights to candidate profiles from projects.", "This integration is currently not linked to any API keys.": "This integration is currently not linked to any API keys.", "This integration is using the following API keys:": "This integration is using the following API keys:", "This integration publishes a feed at:": "This integration publishes a feed at:", "This interview has been cancelled.": "This interview has been cancelled.", "This interview slot was not yet booked.": "This interview slot was not yet booked.", "This interview was prescheduled.": "This interview was prescheduled.", "This is a manual action, only executed when you click this button.": "This is a manual action, only executed when you click this button.", "This is a preview of the form. Submissions will not be saved.": "This is a preview of the form. Submissions will not be saved.", "This is the url where shared candidates are listed.": "This is the url where shared candidates are listed.", "This is used for all time inputs and outputs. ": "This is used for all time inputs and outputs. ", "This is used in internal menus. Candidates can not see this.": "This is used in internal menus. Candidates can not see this.", "This is used to determine how long before the expiry date is the file considered to be expiring soon.": "This is used to determine how long before the expiry date is the file considered to be expiring soon.", "This is used to determine the expiry date if the file doesn't have an expiration date specified.": "This is used to determine the expiry date if the file doesn't have an expiration date specified.", "This job ad is for internal access only. Please :loginLink to see the ad": "This job ad is for internal access only. Please :loginLink to see the ad", "This job requisition needs your approval to move forward.": "This job requisition needs your approval to move forward.", "This key is used by integration {integrationName}. Deleting this key will break the connection between the integration and your Teamdash instance.": "This key is used by integration {integrationName}. Deleting this key will break the connection between the integration and your Teamdash instance.", "This landing page appears at :url": "This landing page appears at :url", "This location was detected automatically with high confidence based on data provided in the CV.": "This location was detected automatically with high confidence based on data provided in the CV.", "This location was detected automatically with low confidence based on data provided in the CV.": "This location was detected automatically with low confidence based on data provided in the CV.", "This location was detected automatically with medium confidence based on data provided in the CV.": "This location was detected automatically with medium confidence based on data provided in the CV.", "This means that candidate imports or job postings might not work correctly.": "This means that candidate imports or job postings might not work correctly.", "This means that if you are scheduling video interviews, Teamdash will use a fallback video calls provider, not :integration.": "This means that if you are scheduling video interviews, Teamdash will use a fallback video calls provider, not :integration.", "This means that incoming emails from candidates are not displayed in Teamdash.": "This means that incoming emails from candidates are not displayed in Teamdash.", "This means that when you're scheduling interviews, you might not see your colleagues' calendars correctly.": "This means that when you're scheduling interviews, you might not see your colleagues' calendars correctly.", "This means that you cannot send candidates to your HRIS from their profiles.": "This means that you cannot send candidates to your HRIS from their profiles.", "This message will be sent to :allCandidates in the interview :immediatelyAfterSaving": "This message will be sent to :allCandidates in the interview :immediatelyAfterSaving", "This message will be sent to the candidate :afterTimeSelected.": "This message will be sent to the candidate :afterTimeSelected.", "This might take up to an hour. Check again later.": "This might take up to an hour. Check again later.", "This option allows you to add candidates without email. This will make some features fail.": "This option allows you to add candidates without email. This will make some features fail.", "This option allows you to require adding a location for each candidate.": "This option allows you to require adding a location for each candidate.", "This password reset link will expire in :count days.": "This password reset link will expire in :count days.", "This person is marked as the sender.": "This person is marked as the sender.", "This presentation was shared with :recipient.": "This presentation was shared with :recipient.", "This project and the candidates within are only visible to project members.": "This project and the candidates within are only visible to project members.", "This project does not accept candidates through any application methods yet.": "This project does not accept candidates through any application methods yet.", "This project doesn't have a position display name set.": "This project doesn't have a position display name set.", "This project is finished and read-only.": "This project is finished and read-only.", "This project is not associated with any landing pages yet.": "This project is not associated with any landing pages yet.", "This reason cannot be deleted as it has already been assigned to some candidates.": "This reason cannot be deleted as it has already been assigned to some candidates.", "This requires users to choose a reason when changing a project's status to 'Failed'.": "This requires users to choose a reason when changing a project's status to 'Failed'.", "This requisition was approved!": "This requisition was approved!", "This role cannot be deleted as it has been assigned to a user, project action or stage action.": "This role cannot be deleted as it has been assigned to a user, project action or stage action.", "This setting applies to all users who view this stage. Limited users can not change this setting.": "This setting applies to all users who view this stage. Limited users can not change this setting.", "This setting is nondestructive - you will always be able to turn it off again, without losing any data.": "This setting is nondestructive - you will always be able to turn it off again, without losing any data.", "This tag will take the place of all source tags.": "This tag will take the place of all source tags.", "This week": "This week", "This will allow candidates to apply with their resume in one click. The form details are prefilled from the resume. The candidate can still edit the details before submitting.": "This will allow candidates to apply with their resume in one click. The form details are prefilled from the resume. The candidate can still edit the details before submitting.", "This will appear as the image when sharing on social media and IM platforms.": "This will appear as the image when sharing on social media and IM platforms.", "This will appear in list view.": "This will appear in list view.", "This will apply to all users. It can later be turned back on in Organization settings.": "This will apply to all users. It can later be turned back on in Organization settings.", "This will be a select option for candidates in the consent renewal request.": "This will be a select option for candidates in the consent renewal request.", "This will be in your page URL.": "This will be in your page URL.", "This will be sent to LinkedIn as the job description.": "This will be sent to LinkedIn as the job description.", "This will be stored as the organization privacy policy URL.": "This will be stored as the organization privacy policy URL.", "This will not affect existing actions in projects previously created from this template.": "This will not affect existing actions in projects previously created from this template.", "This will override the default signature.": "This will override the default signature.", "Thursday": "Thursday", "Tibetan": "Tibetan", "Tigrinya": "<PERSON><PERSON><PERSON><PERSON>", "Time": "Time", "Time slot updated successfully!": "Time slot updated successfully!", "Time to fill": "Time to fill", "Time to hire": "Time to hire", "Timestamp": "Timestamp", "Timestamp of speech occurrence": "Timestamp of speech occurrence", "Timezone": "Timezone", "Timezone mismatch detected": "Timezone mismatch detected", "Time’s up! Try to finish soon.": "Time’s up! Try to finish soon.", "Tip: If you want to run something when the candidate submits the form, create another action with the trigger set to \"after candidate has submitted a form\".": "Tip: If you want to run something when the candidate submits the form, create another action with the trigger set to \"after candidate has submitted a form\".", "Title": "Title", "To automate your feedback collection process, use the [survey] tag inside your automatic stage actions.": "To automate your feedback collection process, use the [survey] tag inside your automatic stage actions.", "To find your employer ID:": "To find your employer ID:", "To find/generate your API key:": "To find/generate your API key:", "To find/generate your Client ID and Secret:": "To find/generate your Client ID and Secret:", "To get the best results:": "To get the best results:", "To get your credentials for integrating with RetailChoice, please contact RetailChoice over one of the methods available at": "To get your credentials for integrating with RetailChoice, please contact RetailChoice over one of the methods available at", "To get your credentials for integrating with Totaljobs, please contact Totaljobs over one of the methods available at": "To get your credentials for integrating with Totaljobs, please contact Totaljobs over one of the methods available at", "To know what your candidates really think about your recruitment process, you'll need to ask them for actual feedback.": "To know what your candidates really think about your recruitment process, you'll need to ask them for actual feedback.", "To provide you with better analytics, it will soon be required to categorize all stages before saving a project.": "To provide you with better analytics, it will soon be required to categorize all stages before saving a project.", "To show you project funnel statistics, all project stages need to be categorized.": "To show you project funnel statistics, all project stages need to be categorized.", "To use Teamdash Platform You should accept": "To use Teamdash Platform You should accept", "To use the Teamdash Platform you must accept the": "To use the Teamdash Platform you must accept the", "Tobacco Manufacturing": "Tobacco Manufacturing", "Today": "Today", "Today is the day to create your first job ad with Job Ad Tool by Teamdash!": "Today is the day to create your first job ad with Job Ad Tool by Teamdash!", "Tomorrow": "Tomorrow", "Tonga (Tonga Islands)": "Tonga (Tonga Islands)", "Too Many Requests": "Too Many Requests", "Total": "Total", "Total average": "Total average", "Total candidates": "Total candidates", "Total of 1 comment|Total of {count} comments": "Total of 1 comment|Total of {count} comments", "Total projects": "Total projects", "Tourism / Hotels": "Tourism / Hotels", "Tourism / Hotels / Catering": "Tourism / Hotels / Catering", "Town": "Town", "Town or postcode": "Town or postcode", "Trade": "Trade", "Trade/retail": "Trade/retail", "Training": "Training", "Training description": "Training description", "Training/education/culture": "Training/education/culture", "Transcribe the provided text.": "Transcribe the provided text.", "Transcript": "Transcript", "Transcription segment": "Transcription segment", "Transcription segments": "Transcription segments", "Transcription will begin automatically after the recording is available.": "Transcription will begin automatically after the recording is available.", "Transcripts": "Transcripts", "Translation and Localization": "Translation and Localization", "Transport driving": "Transport driving", "Transport/logistics management": "Transport/logistics management", "Transportation": "Transportation", "Transportation / Logistics": "Transportation / Logistics", "Transportation Equipment Manufacturing": "Transportation Equipment Manufacturing", "Transportation Programs": "Transportation Programs", "Transportation, Logistics, Supply Chain and Storage": "Transportation, Logistics, Supply Chain and Storage", "Travel": "Travel", "Travel Arrangements": "Travel Arrangements", "Trigger": "<PERSON><PERSON>", "Triin from Teamdash has built many job ads and recruitment campaigns.": "<PERSON><PERSON> from Teamdash has built many job ads and recruitment campaigns.", "Troubleshooting:": "Troubleshooting:", "Trousers size": "Trousers size", "Truck Transportation": "Truck Transportation", "Trusts and Estates": "Trusts and Estates", "Try again": "Try again", "Try changing your search criteria": "Try changing your search criteria", "Try it out": "Try it out", "Tsonga": "Tsonga", "Tswana": "Tswana", "Tuesday": "Tuesday", "Turkish": "Turkish", "Turkmen": "Turkmen", "Turn on e-mail notifications": "Turn on e-mail notifications", "Turned Products and Fastener Manufacturing": "Turned Products and Fastener Manufacturing", "Twi": "Twi", "Two-Factor Authentication": "Two-Factor Authentication", "Type": "Type", "Type (CV-Library)": "Type (CV-Library)", "Type @ to tag a colleague.": "Type @ to tag a colleague.", "Type of employment: full time, part time, permanent, temporary, etc.": "Type of employment: full time, part time, permanent, temporary, etc.", "Type question here...": "Type question here...", "Type to search or add...": "Type to search or add...", "Type to search...": "Type to search...", "UK & Ireland": "UK & Ireland", "UKPRN": "UKPRN", "UPDATED": "UPDATED", "URL": "URL", "Uighur, Uyghur": "Uighur, Uyghur", "Ukrainian": "Ukrainian", "Unable to extract data from this file.": "Unable to extract data from this file.", "Unable to respond to interview": "Unable to respond to interview", "Unassigned": "Unassigned", "Unauthorized": "Unauthorized", "Unavailable": "Unavailable", "Uncategorized": "Uncategorized", "Uncategorized stages": "Uncategorized stages", "Underage": "Underage", "Underage threshold (years)": "Underage threshold (years)", "Undo": "Undo", "Unfortunately, there are no available interview times left. Please contact your recruiter.": "Unfortunately, there are no available interview times left. Please contact your recruiter.", "Unfortunately, your browser is not supported.": "Unfortunately, your browser is not supported.", "Unique candidates": "Unique candidates", "Unknown": "Unknown", "Unlimited": "Unlimited", "Unlink file from its project": "Unlink file from its project", "Unpublish": "Unpublish", "Unpublished at": "Unpublished at", "Unsaved changes": "Unsaved changes", "Unseen notifications first": "Unseen notifications first", "Unselect all": "Unselect all", "Unselect video": "Unselect video", "Unspecified": "Unspecified", "Unsuitable Criteria Detected": "Unsuitable Criteria Detected", "Until": "Until", "Until date": "Until date", "Until project end": "Until project end", "Until specific date": "Until specific date", "Update": "Update", "Update SCIM group mappings": "Update SCIM group mappings", "Update comment": "Update comment", "Update project failure reason": "Update project failure reason", "Update requisition": "Update requisition", "Updated": "Updated", "Updates for :project": "Updates for :project", "Upgrade": "Upgrade", "Upgrade Teamdash": "Upgrade Teamdash", "Upgrade to access this feature": "Upgrade to access this feature", "Upload": "Upload", "Upload a file of the :version version of the font.": "Upload a file of the :version version of the font.", "Upload a video file": "Upload a video file", "Upload button text": "Upload button text", "Upload photo": "Upload photo", "Upload video": "Upload video", "Uploaded video: {fileName}": "Uploaded video: {fileName}", "Urban Transit Services": "Urban Transit Services", "Urdu": "Urdu", "Usages": "Usages", "Use AI language models to pre-fill the form from the file uploaded.": "Use AI language models to pre-fill the form from the file uploaded.", "Use AI to compare candidates. To avoid bias, you should select multiple candidates at a time.": "Use AI to compare candidates. To avoid bias, you should select multiple candidates at a time.", "Use AI to generate a transcript and summary based on the video call recording.": "Use AI to generate a transcript and summary based on the video call recording.", "Use a [survey] merge tag like this:": "Use a [survey] merge tag like this:", "Use a font which is easy to read": "Use a font which is easy to read", "Use browser timezone": "Use browser timezone", "Use cursor keys to navigate calendar dates": "Use cursor keys to navigate calendar dates", "Use custom description for LinkedIn": "Use custom description for LinkedIn", "Use custom signature": "Use custom signature", "Use custom stage categories": "Use custom stage categories", "Use heading colour": "Use heading colour", "Use landing theme": "Use landing theme", "Use multiple languages": "Use multiple languages", "Use photos of your team members or office, not stock images": "Use photos of your team members or office, not stock images", "Use plain background": "Use plain background", "Use primary colour": "Use primary colour", "Use this field to link the form with a project. All the applicants will go to the selected stage.": "Use this field to link the form with a project. All the applicants will go to the selected stage.", "Use this for confidentiality notices, disclaimers, etc.": "Use this for confidentiality notices, disclaimers, etc.", "Use this form as template for creating new forms": "Use this form as template for creating new forms", "Use your company's identity provider to log in to Teamdash. Requires a company email address.": "Use your company's identity provider to log in to Teamdash. Requires a company email address.", "Used for GDPR compliance": "Used for GDPR compliance", "Used for job board exports.": "Used for job board exports.", "Used forbidden tag: [:tag] is not allowed.": "Used forbidden tag: [:tag] is not allowed.", "Used in email signatures": "Used in email signatures", "Used in email signatures, job board exports.": "Used in email signatures, job board exports.", "Used in email signatures, job board exports. PNG format suggested.": "Used in email signatures, job board exports. PNG format suggested.", "Used in job board exports.": "Used in job board exports.", "Useful for collaborating with external recruitment partners. After enabling this setting, an admin can switch on password access for specific user accounts.": "Useful for collaborating with external recruitment partners. After enabling this setting, an admin can switch on password access for specific user accounts.", "User": "User", "User Avatar": "User Avatar", "User administration": "User administration", "User deactivated": "User deactivated", "User deactivated!": "User deactivated!", "User in project not found": "User in project not found", "User message": "User message", "User role": "User role", "User roles": "User roles", "User roles to ping": "User roles to ping", "User-Agent": "User-Agent", "Username": "Username", "Users": "Users", "Users & accounts": "Users & accounts", "Users count": "Users count", "Users imported!": "Users imported!", "Users to ping": "Users to ping", "Usually email from Teamdash goes <NAME_EMAIL>. You still get all the replies to your inbox because Teamdash includes your email in the Reply-To header.": "Usually email from Teamdash goes <NAME_EMAIL>. You still get all the replies to your inbox because Teamdash includes your email in the Reply-To header.", "Utilities": "Utilities", "Utilities Administration": "Utilities Administration", "Utility System Construction": "Utility System Construction", "Uzbek": "Uzbek", "VP2 Feed": "VP2 Feed", "Valid": "<PERSON><PERSON>", "Valid \"until date\" consent": "Valid \"until date\" consent", "Valid HEX color codes start with a # followed by 3 or 6 characters.": "Valid HEX color codes start with a # followed by 3 or 6 characters.", "Valid data processing consent missing.": "Valid data processing consent missing.", "Valid until": "Valid until", "Valid, but without \"until date\" consent": "Valid, but without \"until date\" consent", "Validation Error": "Validation Error", "Validity": "Validity", "Value": "Value", "Value is saved to database, label is visible to candidate.": "Value is saved to database, label is visible to candidate.", "Value must be a number": "Value must be a number", "Value must not be a number.": "Value must not be a number.", "Values": "Values", "Vehicle Repair and Maintenance": "Vehicle Repair and Maintenance", "Venda": "<PERSON><PERSON><PERSON>", "Venture Capital and Private Equity Principals": "Venture Capital and Private Equity Principals", "Verified": "Verified", "Verify": "Verify", "Vertical position": "Vertical position", "Veterinary Services": "Veterinary Services", "Video": "Video", "Video call": "Video call", "Video call URL": "Video call URL", "Video call created!": "Video call created!", "Video call password": "Video call password", "Video call password:": "Video call password:", "Video interview": "Video interview", "Video interview title": "Video interview title", "Video interviews": "Video interviews", "Video interviews sent!": "Video interviews sent!", "Video message": "Video message", "Video message sent!": "Video message sent!", "Video source": "Video source", "Vietnamese": "Vietnamese", "View": "View", "View Job": "View Job", "View ad": "View ad", "View all shared candidates": "View all shared candidates", "View all tasks": "View all tasks", "View cNPS results": "View cNPS results", "View candidate": "View candidate", "View comments": "View comments", "View details": "View details", "View feed": "View feed", "View form": "View form", "View image ad": "View image ad", "View instructions.": "View instructions.", "View invitation": "View invitation", "View invites": "View invites", "View landing": "View landing", "View landing page": "View landing page", "View messages": "View messages", "View project": "View project", "View public form": "View public form", "View requisition": "View requisition", "View social media campaign results": "View social media campaign results", "View stages": "View stages", "View submission": "View submission", "View submissions": "View submissions", "View template": "View template", "Viewed file preview": "Viewed file preview", "Viewed profile": "Viewed profile", "Viewing file {cvIndex} of {cvsTotal}": "Viewing file {cvIndex} of {cvsTotal}", "Vimeo": "Vimeo", "Visibility": "Visibility", "Visible for limited users": "Visible for limited users", "Visible to all": "Visible to all", "Visma Hop": "Visma Hop", "Visual Merchandising": "Visual Merchandising", "Vocational Rehabilitation Services": "Vocational Rehabilitation Services", "Vocational education": "Vocational education", "Vocational secondary education": "Vocational secondary education", "Volap_k": "Volap_k", "Voluntary": "Voluntary", "Voluntary work": "Voluntary work", "Volunteer": "Volunteer", "Waiting for camera and microphone access.": "Waiting for camera and microphone access.", "Waiting for recording to start.": "Waiting for recording to start.", "Walloon": "Walloon", "Warehouse": "Warehouse", "Warehousing": "Warehousing", "Warehousing and Storage": "Warehousing and Storage", "Warning period": "Warning period", "Warning period unit": "Warning period unit", "Warranty until": "Warranty until", "Waste Collection": "Waste Collection", "Waste Treatment and Disposal": "Waste Treatment and Disposal", "Water Supply and Irrigation Systems": "Water Supply and Irrigation Systems", "Water, Waste, Steam, and Air Conditioning Services": "Water, Waste, Steam, and Air Conditioning Services", "We are currently generating the transcription": "We are currently generating the transcription", "We are currently processing this candidate's profile. Please check again in a few minutes.": "We are currently processing this candidate's profile. Please check again in a few minutes.", "We are glad to have you on board. If you ever forget your Teamdash login address, just find this email.": "We are glad to have you on board. If you ever forget your Teamdash login address, just find this email.", "We are informing you that [organization_name] is currently storing your personal data. The data might include your CV, contact information and other information you provided when applying to a position.": "We are informing you that [organization_name] is currently storing your personal data. The data might include your CV, contact information and other information you provided when applying to a position.", "We are updating our": "We are updating our", "We could not parse the uploaded file.": "We could not parse the uploaded file.", "We have populated the form with default categories. Add or remove categories to match your hiring process. You can drag the items to re-order them.": "We have populated the form with default categories. Add or remove categories to match your hiring process. You can drag the items to re-order them.", "We offer": "We offer", "We received the following error with your Google Calendar integration:": "We received the following error with your Google Calendar integration:", "We will remind you in specified time!": "We will remind you in specified time!", "We will use different infrastructure to deliver the message.": "We will use different infrastructure to deliver the message.", "We won't ask for dropout reasons anymore. You can always turn this back on in Settings.": "We won't ask for dropout reasons anymore. You can always turn this back on in Settings.", "Webhook endpoint URL": "Webhook endpoint URL", "Wednesday": "Wednesday", "Week": "Week", "Weekdays": "Weekdays", "Weeks": "Weeks", "Weighting": "Weighting", "Welcome to Teamdash": "Welcome to Teamdash", "Welcome to Teamdash – please verify your e-mail": "Welcome to Teamdash – please verify your e-mail", "Wellness and Fitness Services": "Wellness and Fitness Services", "Welsh": "Welsh", "Western Frisian": "Western Frisian", "What happens when this action is triggered:": "What happens when this action is triggered:", "What is Teamdash?": "What is <PERSON><PERSON><PERSON>?", "What is cNPS?": "What is cNPS?", "What motivates the candidate? What are their likes and dislikes? Use specific quotes.": "What motivates the candidate? What are their likes and dislikes? Use specific quotes.", "What next?": "What next?", "When": "When", "When creating new projects, these stages are used by default.": "When creating new projects, these stages are used by default.", "When publishing to LinkedIn, use City, Country format (e.g. \"London, United Kingdom\"). For remote positions, use only country.": "When publishing to LinkedIn, use City, Country format (e.g. \"London, United Kingdom\"). For remote positions, use only country.", "When someone applies to a position, they implicitly consent to you processing their data.": "When someone applies to a position, they implicitly consent to you processing their data.", "When switched on, this comment will be visible when sharing the candidate with :client.": "When switched on, this comment will be visible when sharing the candidate with :client.", "When switched on, this entry will be visible when sharing the candidate with :client.": "When switched on, this entry will be visible when sharing the candidate with :client.", "When the project ends, you must not further process these candidates' data.": "When the project ends, you must not further process these candidates' data.", "When there are candidates without email addresses or phone numbers, you can only schedule a fixed time for the interview.": "When there are candidates without email addresses or phone numbers, you can only schedule a fixed time for the interview.", "When there are candidates without email addresses, you can only schedule a fixed time for the interview.": "When there are candidates without email addresses, you can only schedule a fixed time for the interview.", "When this form is included in a landing page, colors will be taken from the landing page theme.": "When this form is included in a landing page, colors will be taken from the landing page theme.", "When this is turned off, candidate files from all projects are visible to all users.": "When this is turned off, candidate files from all projects are visible to all users.", "When you apply, we may conduct a background check using public databases and websites and utilizing a web search engine. Your resume may be retained for a maximum period of one year.": "When you apply, we may conduct a background check using public databases and websites and utilizing a web search engine. Your resume may be retained for a maximum period of one year.", "When you choose :anyTime as the scheduling type, the candidates receive an e-mail with a link where they can freely choose a slot in the calendar that is open for all recruiters.": "When you choose :anyTime as the scheduling type, the candidates receive an e-mail with a link where they can freely choose a slot in the calendar that is open for all recruiters.", "When you link a resource (i.e. project, landing) with a team, it will be accessible to that team and all teams above that team.": "When you link a resource (i.e. project, landing) with a team, it will be accessible to that team and all teams above that team.", "When you mark a recruitment project as finished, your right to process the candidate's data ends.": "When you mark a recruitment project as finished, your right to process the candidate's data ends.", "Where do hires come from?": "Where do hires come from?", "Which features do you value the most?": "Which features do you value the most?", "Which form types should trigger this?": "Which form types should trigger this?", "Which timezone would you like to use?": "Which timezone would you like to use?", "While the speakers are not identified in the transcript, you will do your best to differentiate the speakers.": "While the speakers are not identified in the transcript, you will do your best to differentiate the speakers.", "While you were away": "While you were away", "White collar, specialists": "White collar, specialists", "Who are you talking to?": "Who are you talking to?", "Wholesale": "Wholesale", "Wholesale Alcoholic Beverages": "Wholesale Alcoholic Beverages", "Wholesale Apparel and Sewing Supplies": "Wholesale Apparel and Sewing Supplies", "Wholesale Appliances, Electrical, and Electronics": "Wholesale Appliances, Electrical, and Electronics", "Wholesale Building Materials": "Wholesale Building Materials", "Wholesale Chemical and Allied Products": "Wholesale Chemical and Allied Products", "Wholesale Computer Equipment": "Wholesale Computer Equipment", "Wholesale Drugs and Sundries": "Wholesale Drugs and Sundries", "Wholesale Food and Beverage": "Wholesale Food and Beverage", "Wholesale Footwear": "Wholesale Footwear", "Wholesale Furniture and Home Furnishings": "Wholesale Furniture and Home Furnishings", "Wholesale Hardware, Plumbing, Heating Equipment": "Wholesale Hardware, Plumbing, Heating Equipment", "Wholesale Import and Export": "Wholesale Import and Export", "Wholesale Luxury Goods and Jewelry": "Wholesale Luxury Goods and Jewelry", "Wholesale Machinery": "Wholesale Machinery", "Wholesale Metals and Minerals": "Wholesale Metals and Minerals", "Wholesale Motor Vehicles and Parts": "Wholesale Motor Vehicles and Parts", "Wholesale Paper Products": "Wholesale Paper Products", "Wholesale Petroleum and Petroleum Products": "Wholesale Petroleum and Petroleum Products", "Wholesale Photography Equipment and Supplies": "Wholesale Photography Equipment and Supplies", "Wholesale Raw Farm Products": "Wholesale Raw Farm Products", "Wholesale Recyclable Materials": "Wholesale Recyclable Materials", "Whoops!": "Whoops!", "Why do candidates drop out?": "Why do candidates drop out?", "Why not start with a real open vacancy. Who is missing from your team?": "Why not start with a real open vacancy. Who is missing from your team?", "Why should I use cNPS?": "Why should I use cNPS?", "Why work here?": "Why work here?", "Width": "<PERSON><PERSON><PERSON>", "Will They Be a Manager": "Will They Be a Manager", "Wind Electric Power Generation": "Wind Electric Power Generation", "Wineries": "Wineries", "Wireless Services": "Wireless Services", "With active exports": "With active exports", "With best": "With best", "With descendant teams": "With descendant teams", "With summary": "With summary", "Without active exports": "Without active exports", "Wolof": "<PERSON><PERSON><PERSON>", "Women's Handbag Manufacturing": "Women's Handbag Manufacturing", "Wood Product Manufacturing": "Wood Product Manufacturing", "Work at sea": "Work at sea", "Work in shifts": "Work in shifts", "Work schedule (profession.hu)": "Work schedule (profession.hu)", "Work time (cv.lt)": "Work time (cv.lt)", "Work times": "Work times", "Work times (CVK/CVM)": "Work times (CVK/CVM)", "Working days": "Working days", "Working hours": "Working hours", "Working schedule (profession.hu)": "Working schedule (profession.hu)", "Working week description": "Working week description", "Workplace type": "Workplace type", "Write a interview time confirmation SMS": "Write a interview time confirmation SMS", "Write a message for candidate to choose a time": "Write a message for candidate to choose a time", "Write a message to send the video interview link": "Write a message to send the video interview link", "Write a reference check message": "Write a reference check message", "Write a rejection letter": "Write a rejection letter", "Write activities": "Write activities", "Write candidates": "Write candidates", "Write projects": "Write projects", "Writing / Editing": "Writing / Editing", "Writing and Editing": "Writing and Editing", "Xhosa": "Xhosa", "Xlsx file": "Xlsx file", "YTD": "YTD", "Year": "Year", "Years": "Years", "Yes": "Yes", "Yes, anonymize the candidates!": "Yes, anonymize the candidates!", "Yes, delete the candidates permanently!": "Yes, delete the candidates permanently!", "Yiddish": "Yiddish", "Yoruba": "Yoruba", "You approved this requisition.": "You approved this requisition.", "You are a summarization tool integrated within an applicant tracking system (ATS).": "You are a summarization tool integrated within an applicant tracking system (ATS).", "You are a tool meant for cleaning up transcripts integrated within an applicant tracking system (ATS).": "You are a tool meant for cleaning up transcripts integrated within an applicant tracking system (ATS).", "You are about to submit an empty form. Are you sure? Click Submit again to confirm.": "You are about to submit an empty form. Are you sure? <PERSON><PERSON> Submit again to confirm.", "You are editing a project template.": "You are editing a project template.", "You are editing an action in a template.": "You are editing an action in a template.", "You are not allowed to edit this comment.": "You are not allowed to edit this comment.", "You are not authorized to perform this action.": "You are not authorized to perform this action.", "You are receiving this email because we received a password reset request for your account.": "You are receiving this email because we received a password reset request for your account.", "You are using an outdated version of MS Edge. All features might not work as intended.": "You are using an outdated version of MS Edge. All features might not work as intended.", "You can add a new candidate below.": "You can add a new candidate below.", "You can add a new form by clicking the button below.": "You can add a new form by clicking the button below.", "You can add a video interview by clicking the button below.": "You can add a video interview by clicking the button below.", "You can add an API key by clicking the button below.": "You can add an API key by clicking the button below.", "You can add file types by clicking the button below.": "You can add file types by clicking the button below.", "You can add integrations to:": "You can add integrations to:", "You can add project roles by clicking the button below.": "You can add project roles by clicking the button below.", "You can also refuse consent from the link. If you consent to data processing, you can withdraw your consent at any time in the future. If you ignore this message, we will assume you do not consent to further data processing.": "You can also refuse consent from the link. If you consent to data processing, you can withdraw your consent at any time in the future. If you ignore this message, we will assume you do not consent to further data processing.", "You can also use [survey] to include a feedback survey with a 0-10 rating scale.": "You can also use [survey] to include a feedback survey with a 0-10 rating scale.", "You can ask for data processing consent renewals from {candidateCount}.": "You can ask for data processing consent renewals from {candidateCount}.", "You can choose whether to use the question from here or your custom form when sending a message to the candidate.": "You can choose whether to use the question from here or your custom form when sending a message to the candidate.", "You can contact these candidates about relevant job offers.": "You can contact these candidates about relevant job offers.", "You can create an API key by clicking the BambooHR logo in top right corner and choosing \"API Keys\".": "You can create an API key by clicking the BambooHR logo in top right corner and choosing \"API Keys\".", "You can embed this form on your landing page.": "You can embed this form on your landing page.", "You can give additional instructions to the AI using the \"Add the tag if...\" field.": "You can give additional instructions to the AI using the \"Add the tag if...\" field.", "You can leave additional feedback:": "You can leave additional feedback:", "You can limit the time available to the candidate by setting the days, dates and times between which they must choose a slot.": "You can limit the time available to the candidate by setting the days, dates and times between which they must choose a slot.", "You can load saved SMS templates later.": "You can load saved SMS templates later.", "You can load saved message templates later. Attachments are not saved.": "You can load saved message templates later. Attachments are not saved.", "You can modify the filters to change this.": "You can modify the filters to change this.", "You can modify which reasons are available in the Organization settings.": "You can modify which reasons are available in the Organization settings.", "You can not merge the same dropout reason.": "You can not merge the same dropout reason.", "You can now close this window.": "You can now close this window.", "You can now proceed to the next question.": "You can now proceed to the next question.", "You can now send cNPS surveys with more questions!": "You can now send cNPS surveys with more questions!", "You can now track why candidates dropped out of the application process. If this candidate applies again, you will see this information on their candidate card.": "You can now track why candidates dropped out of the application process. If this candidate applies again, you will see this information on their candidate card.", "You can optionally associate the candidate presentation with a project.": "You can optionally associate the candidate presentation with a project.", "You can optionally specify the spoken language. It may improve results.": "You can optionally specify the spoken language. It may improve results.", "You can read about our privacy statement here.": "You can read about our privacy statement here.", "You can revoke an individual consent. This will not affect other consents.": "You can revoke an individual consent. This will not affect other consents.", "You can revoke this consent any time by contacting {org} at {email}.": "You can revoke this consent any time by contacting {org} at {email}.", "You can send cNPS feedback surveys with all types of messages.": "You can send cNPS feedback surveys with all types of messages.", "You can send this message to all references or only to personal or professional references.": "You can send this message to all references or only to personal or professional references.", "You can set the link to the privacy policy in {organizationSettings}.": "You can set the link to the privacy policy in {organizationSettings}.", "You can use the {onlySummary} mode to hide candidate name, gender, ethnicity, and age.": "You can use the {onlySummary} mode to hide candidate name, gender, ethnicity, and age.", "You can:": "You can:", "You do not have permission to create new dropout reasons.": "You do not have permission to create new dropout reasons.", "You do not have permission to disable asking for dropout reasons.": "You do not have permission to disable asking for dropout reasons.", "You don't have access to the submissions of this form.": "You don't have access to the submissions of this form.", "You don't have access to this requisition.": "You don't have access to this requisition.", "You dont have permission to move candidates in project. Your changes will not be saved.": "You dont have permission to move candidates in project. Your changes will not be saved.", "You have 1 invite pending for this interview.|You have :count invites pending for this interview.": "You have 1 invite pending for this interview.|You have :count invites pending for this interview.", "You have :count incomplete tasks without a deadline.": "You have :count incomplete tasks without a deadline.", "You have :count tasks overdue.": "You have :count tasks overdue.", "You have a asynchronous video interview in progress. You can continue your progress.": "You have a asynchronous video interview in progress. You can continue your progress.", "You have a valid data processing consent for {consentScore}% of your database.": "You have a valid data processing consent for {consentScore}% of your database.", "You have active filters. Clear filters to hide this.": "You have active filters. Clear filters to hide this.", "You have already finished this asynchronous video interview.": "You have already finished this asynchronous video interview.", "You have been invited to a video interview.": "You have been invited to a video interview.", "You have exhausted your attempt limit. You can no longer answer this question.": "You have exhausted your attempt limit. You can no longer answer this question.", "You have no SCIM groups.": "You have no SCIM groups.", "You have no mail identities set up yet.": "You have no mail identities set up yet.", "You have no requisitions yet.": "You have no requisitions yet.", "You have no video interviews yet.": "You have no video interviews yet.", "You have not created any consent subtypes yet.": "You have not created any consent subtypes yet.", "You have not created any project failure reasons yet.": "You have not created any project failure reasons yet.", "You have not sent this presentation via e-mail yet, but you can share this url with anyone.": "You have not sent this presentation via e-mail yet, but you can share this url with anyone.", "You have reached the recommended attempt limit, but you can still submit responses.": "You have reached the recommended attempt limit, but you can still submit responses.", "You have successfully finished the interview.": "You have successfully finished the interview.", "You have {candidateCount} and {slotCount}": "You have {candidateCount} and {slotCount}", "You haven't added any custom fonts yet.": "You haven't added any custom fonts yet.", "You may not change the number of segments. The number of segments in the output must be the same as in the input.": "You may not change the number of segments. The number of segments in the output must be the same as in the input.", "You may not change the time (timestamp) data of the segments; you may only change the text.": "You may not change the time (timestamp) data of the segments; you may only change the text.", "You must accept the privacy policy.": "You must accept the privacy policy.", "You must be a Microsoft tenant administrator to continue.": "You must be a Microsoft tenant administrator to continue.", "You must call the provided tool function with an array of strings in which each string is a single sentence that summarizes some aspect of the interview.": "You must call the provided tool function with an array of strings in which each string is a single sentence that summarizes some aspect of the interview.", "You must ensure that the timestamps of segments do not change during processing. The timestamp of when a certain segment happened must be the same in the output as it was in the input.": "You must ensure that the timestamps of segments do not change during processing. The timestamp of when a certain segment happened must be the same in the output as it was in the input.", "You must get approval from :users": "You must get approval from :users", "You must include yourself as manager or member for confidential projects.": "You must include yourself as manager or member for confidential projects.", "You must select at least one slot in this scheduling type.": "You must select at least one slot in this scheduling type.", "You must try your best to fix spelling errors and improve readability without removing information or adding new content. You may replace words if they seem out of context.": "You must try your best to fix spelling errors and improve readability without removing information or adding new content. You may replace words if they seem out of context.", "You probably want to automate your GDPR compliance": "You probably want to automate your GDPR compliance", "You rejected this requisition.": "You rejected this requisition.", "You will always have the option to skip a month. Same goes with purging the database, you always get a notification beforehand and an option to skip. You will always have the final say on any actions.": "You will always have the option to skip a month. Same goes with purging the database, you always get a notification beforehand and an option to skip. You will always have the final say on any actions.", "You will be notified once everybody has made their decision.": "You will be notified once everybody has made their decision.", "You're almost there!": "You're almost there!", "You've been assigned project manager in the project :positionName": "You've been assigned project manager in the project :position<PERSON><PERSON>", "You've been removed as recruiter from a job requisition": "You've been removed as recruiter from a job requisition", "Your Teamdash integration :integration has not been working correctly.": "Your Teamdash integration :integration has not been working correctly.", "Your Teamdash login details": "Your Teamdash login details", "Your URL should be like \"/employer/12345/settings/...\"": "Your URL should be like \"/employer/12345/settings/...\"", "Your account must be accessed via SSO": "Your account must be accessed via SSO", "Your account needs verification": "Your account needs verification", "Your approval is needed for :position": "Your approval is needed for :position", "Your approval is needed for a job requisition for :position.": "Your approval is needed for a job requisition for :position.", "Your approval is no longer needed for :position": "Your approval is no longer needed for :position", "Your browser timezone ({browserTimezone}) is different from your current timezone setting ({currentTimezone}).": "Your browser timezone ({browserTimezone}) is different from your current timezone setting ({currentTimezone}).", "Your daily agenda": "Your daily agenda", "Your e-mail has been verified and your organization's Teamdash instance has been created.": "Your e-mail has been verified and your organization's Teamdash instance has been created.", "Your employer is special - prove it with interesting arguments": "Your employer is special - prove it with interesting arguments", "Your job ad is available at:": "Your job ad is available at:", "Your job ad is public!": "Your job ad is public!", "Your meta description will appear when sharing on social media and IM platforms. Keep the length below 150 characters.": "Your meta description will appear when sharing on social media and IM platforms. Keep the length below 150 characters.", "Your only function is to clean up the text of auto-generated transcript segments that will be provided to you by the user.": "Your only function is to clean up the text of auto-generated transcript segments that will be provided to you by the user.", "Your page title will appear when sharing on social media and IM platforms. Keep the length below 55 characters.": "Your page title will appear when sharing on social media and IM platforms. Keep the length below 55 characters.", "Your primary function is to summarize a video interview using a transcript that is provided to you by the user.": "Your primary function is to summarize a video interview using a transcript that is provided to you by the user.", "Your question": "Your question", "Your report will be emailed to you.": "Your report will be emailed to you.", "Your requisition :position has been approved": "Your requisition :position has been approved", "Your requisition :position was rejected!": "Your requisition :position was rejected!", "Your response is uploading. Please wait until it is finished.": "Your response is uploading. Please wait until it is finished.", "Your responses have been recorded": "Your responses have been recorded", "Your selection contains candidates without a valid phone number. Please uncheck them.": "Your selection contains candidates without a valid phone number. Please uncheck them.", "Your selection contains candidates without email addresses. Please uncheck them.": "Your selection contains candidates without email addresses. Please uncheck them.", "Your selection contains candidates without email addresses. You can only create pre-scheduled interviews for this selection.": "Your selection contains candidates without email addresses. You can only create pre-scheduled interviews for this selection.", "Your session has expired and your last change was not saved. Sorry. We will refresh the page for you.": "Your session has expired and your last change was not saved. Sorry. We will refresh the page for you.", "Your username is the subdomain name you use for logging in to BambooHR.": "Your username is the subdomain name you use for logging in to BambooHR.", "Youtube": "Youtube", "Zhuang, Chuang": "Zhuang, Chuang", "Zoom calls": "Zoom calls", "Zoos and Botanical Gardens": "Zoos and Botanical Gardens", "Zulu": "Zulu", "[current_user_name] has invited you to use Teamdash.": "[current_user_name] has invited you to use Teamdash.", "after the candidate submits a form": "after the candidate submits a form", "after the candidate submits a video response": "after the candidate submits a video response", "after the candidate submits their referees": "after the candidate submits their referees", "after they have selected their preferred time slot": "after they have selected their preferred time slot", "after {duration} in this stage": "after {duration} in this stage", "all": "all", "all candidates": "all candidates", "and": "and", "annually": "annually", "anonymized candidate": "anonymized candidate", "answer_saved": "Answer saved!", "apprenticeships.gov.uk entity ID": "apprenticeships.gov.uk entity ID", "approval|approvals": "approval|approvals", "attempted": "attempted", "automatically": "automatically", "automatically at {time}": "automatically at {time}", "between {start} and {end}": "between {start} and {end}", "bold": "bold", "bold-and-italic": "bold-and-italic", "but not public": "but not public", "buttons.submit_text": "buttons.submit_text", "cNPS": "cNPS", "cNPS comment": "cNPS comment", "cNPS is an excellent indicator for aligning your recruitment team behind improving candidate experience.": "cNPS is an excellent indicator for aligning your recruitment team behind improving candidate experience.", "cNPS or Candidate Net Promoter Score is a measure to gauge your candidates' satisfaction with the hiring process.": "cNPS or Candidate Net Promoter Score is a measure to gauge your candidates' satisfaction with the hiring process.", "cNPS responses": "cNPS responses", "cNPS score": "cNPS score", "cNPS survey": "cNPS survey", "cNPS survey form": "cNPS survey form", "cNPS survey forms": "cNPS survey forms", "cNPS survey message for candidate": "cNPS survey message for candidate", "cNPS survey message with merge tag": "cNPS survey message with merge tag", "cNPS.question.here": "here", "candidate has tag {tag}": "candidate has tag {tag}", "career page": "career page", "check the integration credentials": "check the integration credentials", "click here to decline the invitation": "click here to decline the invitation", "click here to request another": "click here to request another", "click the link below and re-connect by clicking the Save button": "click the link below and re-connect by clicking the Save button", "clicked": "clicked", "clicked conversion": "clicked conversion", "clients": "clients", "color picker": "color picker", "completed": "completed", "confidential": "confidential", "contains": "contains", "contains all": "contains all", "current": "current", "data_use": "Your data will be used for contacting you about future open positions.", "day": "day", "days": "days", "daysInStageShort": "d", "default": "default", "does not contain": "does not contain", "draft": "draft", "due on {dueOnDate}": "due on {dueOnDate}", "due {dueInDiff}": "due {dueInDiff}", "durationpicker.custom": "Custom", "e.g #007bff": "e.g #007bff", "e.g. Feedback request": "e.g. <PERSON><PERSON><PERSON> request", "e.g. Send thx 4 appl. or Send rejection letter": "e.g. Send thx 4 appl. or Send rejection letter", "e.g. example.org": "e.g. example.org", "e.g. {imap.gmail.com:993/imap/ssl}INBOX or {outlook.office365.com:993/imap/ssl}INBOX": "e.g. {imap.gmail.com:993/imap/ssl}INBOX or {outlook.office365.com:993/imap/ssl}INBOX", "edited by {userName}": "edited by {<PERSON><PERSON><PERSON>}", "elements.file.dndDescription": "elements.file.dndDescription", "elements.file.dndTitle": "elements.file.dndTitle", "elements.file.uploadButton": "elements.file.uploadButton", "elements.gallery.dndDescription": "elements.gallery.dndDescription", "elements.gallery.dndTitle": "elements.gallery.dndTitle", "elements.gallery.uploadButton": "elements.gallery.uploadButton", "elements.list.remove": "elements.list.remove", "elements.multifile.dndDescription": "elements.multifile.dndDescription", "elements.multifile.dndTitle": "elements.multifile.dndTitle", "every 10th minute (:00, :10, :20, :30, :40, :50)": "every 10th minute (:00, :10, :20, :30, :40, :50)", "every 15th minute (:00, :15, :30, :45)": "every 15th minute (:00, :15, :30, :45)", "excluding weekends": "excluding weekends", "failed": "failed", "fill handover form": "fill handover form", "finished": "finished", "form": "form", "formbuilder.items.label": "Label", "from 1 candidate|from {count} candidates": "from 1 candidate|from {count} candidates", "from global settings": "from global settings", "from project": "from project", "from team :team": "from team :team", "full hour (:00)": "full hour (:00)", "get insights": "get insights", "greater than": "greater than", "has all of": "has all of", "has already submitted reference": "has already submitted reference", "has none": "has none", "has none of": "has none of", "has one of": "has one of", "hello": "Hello, {name}!", "here": "here", "higher than": "higher than", "hourly": "hourly", "hours": "hours", "i_consent": "I consent to {org} processing my data for contacting me about vacancies until {date}", "i_dont_consent": "I do not consent to further processing of my personal data", "if": "if", "if this is a video call interview": "if this is a video call interview", "if you think this is a mistake, activate the integration again": "if you think this is a mistake, activate the integration again", "iframe URL": "iframe URL", "iframe from URL": "<PERSON><PERSON><PERSON> from URL", "immediately after saving this form": "immediately after saving this form", "in progress": "in progress", "in project {project}": "in project {project}", "in stage {stage}": "in stage {stage}", "in {anonymizeTime}": "in {anonymizeTime}", "in {project}": "in {project}", "in {renewalTime}": "in {renewalTime}", "interview": "interview", "intro": "{name} from {org} would like your consent to keep processing your personal data. This data might include your contact information, CV and other data you submitted when applying to a position with {org}.", "intro_2": "When you click on start-button below, you will be asked to give permissions to your webcam and microphone.", "intro_3": "NB! Make sure to click the Submit button for every response you want to submit.", "invite": "invite", "invited": "invited", "is false": "is false", "is near": "is near", "is one of": "is one of", "is true": "is true", "italic": "italic", "job_ad_category_export": "Export", "key": "key", "know more about {candidateName}": "know more about {candidate<PERSON><PERSON>}", "last contacted :timeAgo": "last contacted :timeAgo", "less than": "less than", "log in": "log in", "lower than": "lower than", "mailbox": "mailbox", "managers": "managers", "messages.cta_text": "messages.cta_text", "messages.cta_url": "messages.cta_url", "messages.success_text": "messages.success_text", "minutes": "minutes", "missing email address": "missing email address", "missing phone number": "missing phone number", "mo": "mo", "month": "month", "monthly": "monthly", "no attempts left|1 attempt left|{count} attempts left": "no attempts left|1 attempt left|{count} attempts left", "no responses yet": "no responses yet", "no_camera": "If your current device does not have a camera, use your smartphone.", "no_camera_step_1": "Open your camera", "none": "none", "normal": "normal", "not any of": "not any of", "not opened": "not opened", "of type": "of type", "on hold": "on hold", "opened": "opened", "opened conversion": "opened conversion", "optional": "optional", "or": "or", "per annum": "per annum", "per day": "per day", "per hour": "per hour", "policy_review": "Once you have reviewed our <a href=\"{url}\" class=\"text-medium\" target=\"_blank\">privacy policy</a>, please make a decision below:", "present": "present", "privacy policy": "privacy policy", "private": "private", "project is in status {status}": "project is in status {status}", "public": "public", "publish your job ads in various job portals": "publish your job ads in various job portals", "quarter": "quarter", "question_no": "Question #{0}", "record_again": "Discard video and record again", "recorder.confirm": "Confirm", "recorder.ok": "Video OK:", "recorder.play_recording": "Play recording", "recorder.record": "Record", "recorder.record_again": "Discard video and record again", "recorder.record_new": "Record new video", "recorder.stop": "Stop recording", "recorder.warning_not_sent_user": "The video has not been saved yet. <PERSON>lick Confirm to save your question.", "reply to this message to contact support": "reply to this message to contact support", "seconds": "seconds", "see incoming e-mails from candidates directly on the candidate profile": "see incoming e-mails from candidates directly on the candidate profile", "see your existing calendar events when scheduling interviews": "see your existing calendar events when scheduling interviews", "send candidate information to your HR platform": "send candidate information to your HR platform", "sourced": "sourced", "submit": "Submit", "talent pool": "talent pool", "thanks": "Thank you! We have received your answers!", "to stage": "to stage", "to_here": "here", "trix.acceptedExtensions": "trix.acceptedExtensions", "trix.acceptedMimes": "trix.acceptedMimes", "undefined": "undefined", "unknown": "unknown", "unlimited": "unlimited", "update your browser here": "update your browser here", "upgrade": "upgrade", "warning_not_sent": "The answer has not been submitted yet. Click Submit to send your answer.", "week": "week", "when a button is clicked": "when a button is clicked", "when this template is used for video call interviews": "when this template is used for video call interviews", "will be stopped automatically": "will be stopped automatically", "will be submitted automatically": "will be submitted automatically", "with status": "with status", "you": "you", "{0} There are no more open slots in this interview.|{1} There is one open slot in this interview.|[2,*] There are :count open slots in this interview.": "{0} There are no more open slots in this interview.|{1} There is one open slot in this interview.|[2,*] There are :count open slots in this interview.", "{0} are no more open slots|{1} is one open slot|[2,*] are :count open slots": "{0} are no more open slots|{1} is one open slot|[2,*] are :count open slots", "{count} actions in {status} projects": "{count} actions in {status} projects", "{count} candidate|{count} candidates": "{count} candidate|{count} candidates", "{count} project will be included in the report.|{count} projects will be included in the report.": "{count} project will be included in the report.|{count} projects will be included in the report.", "{count} received": "{count} received", "{count} settings to reassign or clear": "{count} settings to reassign or clear", "{count} stage actions to reassign": "{count} stage actions to reassign", "{count} stage actions will be deleted": "{count} stage actions will be deleted", "{count} stage|{count} stages": "{count} stage|{count} stages", "{count} task|{count} tasks": "{count} task|{count} tasks", "{count} user": "{count} user", "{dataLength} of {dataTotal}": "{dataLength} of {dataTotal}", "{duration} after the project status changes": "{duration} after the project status changes", "{mergeTag} will be blank.": "{mergeTag} will be blank.", "{remainingAttempts}/{maxAttempts} attempt(s) left": "{remainingAttempts}/{maxAttempts} attempt(s) left", "{selected} of {total} candidates selected": "{selected} of {total} candidates selected", "{total} of {total} selected": "{total} of {total} selected", "{upgradeCTA} to get access to all of our video features.": "{upgradeCTA} to get access to all of our video features.", "{upgradeCTA} to get access to all of our video interview analysis tools.": "{upgradeCTA} to get access to all of our video interview analysis tools.", "{userName} shared {candidateCount} candidates": "{userName} shared {candidateCount} candidates", "{userName} shared {candidateCount} candidates for {projectName}": "{userName} shared {candidateCount} candidates for {projectName}", "{user} shared 1 candidate for {position} | {user} shared {count} candidates for {position}": "{user} shared 1 candidate for {position} | {user} shared {count} candidates for {position}", "{user} shared 1 candidate | {user} shared {count} candidates": "{user} shared 1 candidate | {user} shared {count} candidates"}