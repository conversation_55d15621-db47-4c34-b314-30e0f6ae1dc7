{"# of Members": "# narių", "# of candidates": "# kandidatų", "(Drag and drop to add attachments)": "(Norėdami p<PERSON> p<PERSON>, vilkite pele)", "(and 1 finished project)|(and {count} finished projects)": "(ir 1 baigtas projektas)|(ir {count} užbaigtų projektų)", "(with API key)": "(su <PERSON> r<PERSON>)", "+ {count} more": "+ {count} daug<PERSON>u", "-> Hired": "-> <PERSON><PERSON><PERSON><PERSON><PERSON>", "-> Interviews": "-> Interviu", "-> Offers": "-> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "-> Rejected": "-> <PERSON><PERSON><PERSON>", "... and much more": "... ir daug daugiau", "... or invite new users to Teamdash": "... arba pakvieskite naujų naudotojų į „Teamdash“", "...or fill out a form": "...arba užpildykite formą", ".xlsx file": ".xlsx failas", "0 means the same day that the action is triggered, 1 means the next day, etc.": "0 reiškia tą pačią dien<PERSON>, kai ve<PERSON> p<PERSON>, 1 – kit<PERSON> dieną ir t. t.", "1 SMS sent | {count} SMS sent": "Išsiųsta 1 SMS |  Išsiųsta SMS: {count}", "1 accepted | {count} accepted": "1 priimė | {count} priimė", "1 answered | {count} answered": "1 atsakė | {count} atsakė", "1 attempt left|{count} attempts left": "Liko 1 bandymas| Liko bandymų: {count}", "1 attempt|{count} attempts": "1 bandymas| {count} bandymai", "1 candidate|{count} candidates": "1 kandidatą|{count} kandidatai", "1 comment | {count} comments": "1 komentaras | {count} komentarai", "1 day in stage | {count} days in stage": "1 diena stadijoje | {count} dienų stadijoje", "1 day | {n} days": "1 diena | {n} dienų", "1 message | {count} messages": "1 žinutė | {count} žinučių", "1 month": "1 mėnuo", "1 open slot | {count} open slots": "1 laisva vieta | {count} laisvų vietų", "1 pending | {count} pending": "1 laukiama | {count} laukiama", "1 project from this template|{count} projects from this template": "1 projektas iš šio šablono| {count} projektai iš šio šablono", "1 referee | {count} referees": "1 rekomendacijos davėjas | {count} rekomendacijos dav<PERSON> (-ų)", "1 selected|{count} selected": "1 pasirinkta|{count} pasirinkta", "1 slot|{count} slots": "1 vieta|{count} vietos", "1 submitted | {count} submitted": "1 pateiktas | {count} pateikti (-ų)", "1 year": "1 metai", "1/2 Text with image": "1/2 Teksto su paveikslėliu", "1/2 Text with video": "1/2 Teksto su vaizdo įrašu", "10 days": "10 dienų", "10 minutes": "10 minučių", "10 minutes (:00, :10, :20...)": "10 minučių (:00, :10, :20...)", "12M": "12M", "14 days": "14 dienų", "15 minutes": "15 minučių", "15 minutes (:00, :15, :30, :45)": "15 minučių (:00, :15, :30, :45)", "1st Group Disabled": "1-oji neįgaliųjų grupė", "2 Options": "2 parinktys", "2 months": "2 mėnesiai", "2/3 Text with square image": "2/3 Tekstas su kvadratiniu vaizdu", "2nd Group Disabled": "2-oji neįgaliųjų grupė", "3 days": "3 dienos", "3 months": "3 mėnesiai", "30 days": "30 dienų", "30 minutes": "30 minučių", "30D": "30D", "3rd Group Disabled": "3-ioji neįgaliųjų grupė", "4 Options": "4 parinktys", "6 months": "6 mėnesiai", "60 minutes": "60 minučių", "6M": "6M", "7 days": "7 dienos", "9 days later we purge your database of all candidates data which we didn't get a processing consent for": "Po 9 dienų iš jūsų duomenų bazės išvalome visus kandidatų duomenis, d<PERSON><PERSON> kurių tvarkymo sutikimo negavome.", "90D": "90D", ":actingUser has assigned you as project manager in the project :positionName.": ":acting<PERSON>ser paskyrė jus projekto vadovu projekte :positionName.", ":attribute cannot contain the @ symbol when publishing to CV Keskus.": ":attribute negali turėti simbolio @, kai skel<PERSON>ma CV Keskus.", ":author assigned you a task|:author assigned you :count tasks": ":author pris<PERSON><PERSON><PERSON> jums užduotį|:author pris<PERSON><PERSON><PERSON> jums :count <PERSON><PERSON><PERSON><PERSON><PERSON> (-čių)", ":count accepted interview times": ":count pri<PERSON><PERSON><PERSON> interviu laikus", ":count answered video interviews": ":count <PERSON><PERSON><PERSON><PERSON> (-ų) vai<PERSON><PERSON> poka<PERSON> (-ių)", ":count miscellaneous updates": ":count įvairūs (-ių) atnaujinimai (-ų)", ":count new candidates": ":count nauju<PERSON> kandi<PERSON>tus", ":count updates to tasks": ":count <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ":firstName tagged you in a comment about :candidateName": ":firstName jus pažymėjo komentare apie :candidate<PERSON><PERSON>", ":fromUser invited you to work on project :positionName.": ":fromUser pakvietė jus dirbti su projektu :positionName.", ":fromUser needs your help - :positionName": ":fromUser reikia jūsų pagalbos – :positionName", ":managerName needs your attention for candidates in :stageName stage:": ":manager<PERSON>ame reikia j<PERSON>ų dėmesio kandidatams :stageName etape:", ":name changed the deadline for reviewing the job requisition for :position. The new deadline is in :deadline.": ":pavadin<PERSON><PERSON> pake<PERSON> termin<PERSON>, per kurį turi būti perži<PERSON><PERSON><PERSON>ta darbo paraiška dėl :pareigų. Naujasis terminas nurodytas :deadline.", ":name created a job requisition and marked you as the recruiter. You don't need to do anything right now. This is just a heads-up.": ":name suk<PERSON><PERSON><PERSON> darbo para<PERSON>ą ir pažymėjo jus kaip įdarbintoją. Dabar jums nieko nereikia daryti. Tai tik įspėjimas.", ":name created a job requisition which needs your approval.": ":name suk<PERSON><PERSON><PERSON> da<PERSON>, kuri<PERSON> reikia pat<PERSON>.", ":name marked you as recruiter for :position": ":vardas ir pavard<PERSON> pažymėjo jus kaip įdarbintoją į :poziciją", ":name marked you as the recruiter in the job requisition for :position. You don't need to do anything right now. This is just a heads-up.": ":vardas ir pava<PERSON> p<PERSON><PERSON><PERSON>, kad esate įdarbintojas darbo skelbime dėl :pareigų. Dabar jums nieko nereikia daryti. Tai tik informacinis pranešimas.", ":name needs your approval for :position": ":name turi būti jū<PERSON> patvir<PERSON> :position", ":name requested your approval for a job requisition for :position.": ":vardas ir pavard<PERSON> pap<PERSON> jūsų patvirtin<PERSON> d<PERSON> darbo paraiškos į :pareigas.", ":name started a requisition for :position": ":name prad<PERSON><PERSON> teikti para<PERSON> dėl :position", ":name wrote:": ":name ra<PERSON><PERSON>:", ":startTime :event with :people": ":startTime :event su :people", ":user shared :count candidates": ":user pasidalino :count kandidatais", ":user shared :count candidates for :position:": ":user pasidalijo :count kandidatais (-ų) į :position:", ":user tagged you in :position log entry": ":user pa<PERSON><PERSON><PERSON><PERSON> jus :position žurnalo įraše", ":userName cancelled the event because they are unable to participate.": ":<PERSON><PERSON><PERSON>, nes negali jame <PERSON>.", ":userName cancelled the interview.": ":user<PERSON><PERSON> atšaukė pokalbį.", ":userName has invited you to use Teamdash": ":userName pakvietė jus naudotis  „Teamdash“", ":userName has invited you to work on the project :projectName.": ":userName pakvietė jus dirbti su projektu :projectName.", "< {distance} from": "< {distance} nuo", "A calendar event has been sent to your e-mail. You can close this window. You can come back to this url any time to see your interview time.": "Į jūsų el. paštą išsiųstas susitikimo informacija. Galite uždaryti šį langą. Bet kuriuo metu galite grįžti į šį url adresą ir pamatyti savo pokalbio laiką.", "A cleaned-up transcript segment in which only the \"text\" field has been cleaned up, but the \"time\" field has been left unchanged.": "Išvalytas nuoraš<PERSON> segment<PERSON>, k<PERSON><PERSON> buvo išvalytas tik laukas \"tekstas\", o laukas \"laikas\" liko nepakeistas.", "A few days before sending out the renewal requests, you get a report - how many and which candidates are going to get renewal messages.": "Likus kelioms dienoms iki atnaujinimo prašymų išsiuntimo, gausite ataskaitą - kiek ir kurie kandidatai gaus atnaujinimo pranešimus.", "A few ideas:": "Keletas idėjų:", "A fresh verification link has been sent to your email address.": "Į jūsų el. pašto ad<PERSON> buvo išsiųsta nauja patvirtinimo nuoroda.", "A job has been unpublished: :position": "Darbo vieta buvo panaikinta: :position", "A new job has been posted: :position": "Paskelbta nauja darbo vieta: :pozicija", "A positive candidate experience can help you attract and hire the best people.": "Teigiama kandidatų patirtis gali padėti pritraukti ir įdarbinti geria<PERSON><PERSON> darbuo<PERSON>.", "A reference has already been requested from this person at {lastMessageTime}.": "<PERSON><PERSON> asmens jau paprašyta pateikti rekomendaciją {lastMessageTime}.", "A simple text block": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> blo<PERSON>", "A single sentence summarizing a specific aspect of the interview.": "<PERSON><PERSON><PERSON>, apibendrinantis konkretų interviu aspektą.", "A small heads-up!": "Trumpas įspėjimas!", "A text string that contains the text representing a transcribed segment of speech.": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, reiškiantis transkribuotą kalbos segmentą.", "A timestamp that represents when the segment of speech occurred within the original recording.": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, kada kalbos segmentas įvyko pradiniame įraše.", "A video call link will be generated and included in calendar invites.": "Vaizdo skambučio nuoroda bus sukurta ir įtraukta į kalendoriaus kvietimus.", "AI": "DI", "AI Assistance": "DI pagalba", "AI Provider": "DI tiekėjas", "AI-generated analysis": "Dirbtinio intelekto sukurta analizė", "API": "API", "API documentation": "API dokumentacija", "API key": "API raktas", "API keys": "API raktai", "API token": "API simbolis", "Abkhazian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "About employer": "<PERSON>pie darbdavį", "Abrasives and Nonmetallic Minerals Manufacturing": "Abrazyvų ir nemetalinių mineralų gamyba", "Accept DPA": "Sutikti su DPA", "Accept Terms of use": "Sutikti su naudojimo sąlygomis", "Accepted": "<PERSON><PERSON><PERSON><PERSON>", "Accepted interview invite": "Priimtas kvietimas į pokalbį", "Access URL": "Prieigos URL", "Access controls": "Prieigos kontrolė", "Access key": "Prieigos raktas", "Accomodation Services": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Account inactive": "<PERSON><PERSON><PERSON>", "Accountant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Accounting": "<PERSON><PERSON><PERSON><PERSON>", "Accounting / Auditing": "Apskaita / auditas", "Accounting/Financial/Insurance": "<PERSON><PERSON><PERSON><PERSON> / finansai / draudimas", "Action Needed: :integration integration broken": "Reikiami veiksmai: :integration integracija neveikia", "Action name": "<PERSON>eiks<PERSON> p<PERSON>", "Actions": "Veiks<PERSON><PERSON>", "Active": "Aktyvus", "Active consent of type": "Aktyvus tipo sutikimas", "Active in other projects:": "Aktyviai dalyvauja kituose projektuose:", "Active projects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> proje<PERSON>", "Active until": "Aktyvus iki", "Activities to report": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Activity custom fields": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> la<PERSON>i", "Activity report": "<PERSON><PERSON><PERSON><PERSON>", "Actor": "<PERSON><PERSON><PERSON><PERSON>", "Ad unpublished!": "<PERSON><PERSON><PERSON><PERSON> nepaskelbtas!", "Add": "<PERSON><PERSON><PERSON><PERSON>", "Add Block": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "Add a caption…": "Pridėkite antraštę...", "Add a comment to start the conversation.": "Pridėkite komentarą, kad pradėtumėte pokalbį.", "Add a descriptive project title": "Pridėkite aprašomąjį projekto pavadinimą", "Add a dropout reason": "<PERSON><PERSON><PERSON><PERSON> atmetimo priežastį", "Add a list of requirements to the project description": "Projekto aprašymo papildymas reikalavimų sąrašu", "Add a new candidate": "Pridėti naują kandidatą", "Add a new stage": "Pridėti naują etapą", "Add a quote from the Manager, it's the most important trust element": "Pridėkite vadybininko citatą – tai svarbiausias pasitikėjimo elementas", "Add a stage action": "P<PERSON>ėti etapo ve<PERSON>", "Add a stage action to {stageName}": "<PERSON><PERSON><PERSON><PERSON> etapo veiksmą prie {stageName}", "Add actions to this project to automate your workflow.": "Į šį projektą įtraukti veiksmus darbo eigai automatizuoti.", "Add actions to this stage to automate your workflow.": "Į šį etapą įtraukti veiksmus darbo eigai automatizuoti.", "Add another candidate": "Pridėti kit<PERSON> kandidatą", "Add another reference": "Pridėti kit<PERSON> rekomendaciją", "Add attachment": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Add authentication provider": "Pridėti autentifikavimo paslaugų teikėją", "Add automated project action": "Pridėti automatizuotą projekto veiksmą", "Add automated stage action": "Pridėti automatizuotą etapo veiksmą", "Add bulk comments": "Pridėti bendrą komentarą", "Add bulk tags": "<PERSON><PERSON><PERSON><PERSON>", "Add candidate": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Add candidate referee": "P<PERSON><PERSON><PERSON>ko<PERSON>j<PERSON>", "Add candidate reference": "<PERSON><PERSON><PERSON><PERSON> kandidato reko<PERSON>j<PERSON>", "Add candidates to existing set": "Kandidatų įtraukimas į esamą rinkinį", "Add child": "Pridėti vai<PERSON>ą", "Add client": "<PERSON><PERSON><PERSON><PERSON>", "Add comment": "Pridėti komentarą", "Add comment to decision": "Pridėti komentarą prie sprendimo", "Add condition": "<PERSON><PERSON><PERSON><PERSON>", "Add consent": "<PERSON><PERSON><PERSON><PERSON>", "Add consent subtype": "Pridėti sutikim<PERSON>ą", "Add contact": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Add contact to ": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>aktą į", "Add custom fonts": "<PERSON><PERSON><PERSON><PERSON> pasirin<PERSON><PERSON>", "Add dates": "P<PERSON><PERSON><PERSON> datas", "Add dropout reason": "<PERSON><PERSON><PERSON><PERSON> iškrit<PERSON> priežastį", "Add education": "Pridėti išsilavinimą", "Add employment": "<PERSON><PERSON><PERSON><PERSON>", "Add entry": "<PERSON><PERSON><PERSON><PERSON> įrašą", "Add existing users...": "<PERSON><PERSON><PERSON><PERSON> es<PERSON>us naudo<PERSON>...", "Add field": "<PERSON><PERSON><PERSON><PERSON>", "Add file": "<PERSON><PERSON><PERSON><PERSON>", "Add file type": "Pridėti failo tip<PERSON>", "Add filter": "<PERSON><PERSON><PERSON><PERSON> filt<PERSON>", "Add import": "<PERSON><PERSON><PERSON><PERSON>", "Add integration": "<PERSON><PERSON><PERSON><PERSON> integra<PERSON>j<PERSON>", "Add link": "Pridėti nuorodą", "Add location": "Pridėti vietą", "Add mail identity": "Pridėti pa<PERSON>", "Add my score": "<PERSON><PERSON><PERSON><PERSON> mano rezultat<PERSON>", "Add new action": "Pridėti naują ve<PERSON>", "Add new candidate reason": "<PERSON><PERSON><PERSON><PERSON> nauj<PERSON> kandidato priežastį", "Add new client": "Pridėti naują k<PERSON>", "Add new company reason": "Pridėkite naujos įmonės priežastį", "Add new integration": "Pridėti naują integraciją", "Add new location": "Pridėti naują vietą", "Add new manager": "Pridėti naują vadybininką", "Add new tag": "Pridėti naują žymą", "Add new user": "Pridėti naują naudotoją", "Add office": "<PERSON><PERSON><PERSON><PERSON>", "Add project action": "Pridėti projekto veiksmą", "Add project failure reason": "Pridėkite projekto nesėkmės priežastį", "Add project log entry": "Pridėti projekto žurnalo įrašą", "Add provider": "Pridėti paslaugų teikėją", "Add quote": "Pridėti citatą", "Add rating": "<PERSON><PERSON><PERSON><PERSON> įvertinimą", "Add reference form": "Pridėti rekomendacijos formą", "Add scorecard": "Pridėti vertinimo kort<PERSON>", "Add stage": "<PERSON><PERSON><PERSON><PERSON>", "Add stage action": "Pridėti scenos veiksmą", "Add tag": "<PERSON><PERSON><PERSON><PERSON>", "Add tags": "<PERSON><PERSON><PERSON><PERSON>", "Add tags to the candidate": "<PERSON><PERSON><PERSON><PERSON> prie kandidato", "Add team": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Add the tag if...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jei...", "Add to favourites": "Pridėti prie mėgstamiausių", "Add to project": "Pridėti prie projekto", "Add to this project": "Pridėti prie šio projekto", "Add user": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>", "Add users to project": "Naudotojų įtraukimas į projektą", "Add video interview": "Pridėti vaizdo interviu", "Add your comment here...": "Pridėkite savo komentarą čia...", "Add {query}": "<PERSON><PERSON><PERSON><PERSON> {query}", "Add-ons": "Pried<PERSON>", "Added": "<PERSON><PERSON><PERSON><PERSON>", "Added a dropout reason": "Pridėta iškritimo priežastis", "Added at": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "Added comment": "Pridėtas komentaras", "Added from API import": "Pridėta iš API importo", "Added from XLSX import": "Pridėta iš XLSX importo", "Added from sourcing extension": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> p<PERSON>", "Added from xlsx import": "Pridėta iš xlsx importo", "Added manual source override": "Pridėtas rankinis šaltin<PERSON>", "Added to landing {landingLink}": "Pridėta prie puslapio {landingLink}", "Additional city (CVbankas)": "Papildomas miestas", "Additional city 2 (CVbankas)": "Papildomas miestas 2", "Additional industries (CV-Library)": "Papildomos pramon<PERSON> (CV-Library)", "Additional info form": "Papildomos informacijos forma", "Additional information form": "Papildomos informacijos forma", "Additional information forms": "Papildomos informacijos formos", "Additional options": "Papildo<PERSON> parink<PERSON>", "Address": "<PERSON><PERSON><PERSON>", "Address (SS.lv)": "<PERSON><PERSON><PERSON> (SS.lv)", "Adds hidden custom fields and a diversity questionnaire.": "<PERSON>da slaptą papildomą laukelį ir įvairovės klausimyną.", "Adjuster": "<PERSON><PERSON><PERSON>jas", "Admin": "<PERSON><PERSON>", "Administration": "Administracija", "Administration & Support": "Administravi<PERSON> ir pagalba", "Administration of Justice": "Teisingumo administravimas", "Administration/work safety": "Administravimas / darbo sauga", "Administrative": "Admini<PERSON><PERSON><PERSON>", "Administrative and Support Services": "Administracinės ir paga<PERSON><PERSON> p<PERSON>", "Administrator": "<PERSON><PERSON>", "Advert type (profession.hu)": "<PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Advertising": "<PERSON><PERSON><PERSON>", "Advertising Services": "Rekla<PERSON>", "Advertising agent": "Reklamos agentas", "Advertising manager": "Reklamos vadybininkas", "Afar": "Afar", "Afrikaans": "Afrikiečių kalba", "After completing the API key steps, check your browser address bar": "Atlikę API rakto ve<PERSON>, patikrinkite naršyklės adreso juostą", "After school": "Po mokyklos", "After you have reviewed the details you will be redirected to the checkout page. After successful payment your campaign details are reviewed by a human at Teamdash. You'll get a notification once your campaign has been approved and launched.": "Po to, kai peržiūrėsite informaciją, būsite nukreipti į apmokėjimo puslapį. Sėkmingai atlikus mokėjimą, jūsų kampanijos duomenis perž<PERSON><PERSON><PERSON> \"Teamdash\" darbuotojas. Gaus<PERSON>, kai jūsų kampanija bus patvirtinta ir paleista.", "Age": "<PERSON><PERSON><PERSON>", "Agency": "<PERSON><PERSON><PERSON>", "Agency has clients, corporation has managers.": "Agentūra turi k<PERSON>, o korporacija - vadovų.", "Agent": "Agentas", "Agricultural Chemical Manufacturing": "Žemės ūkio cheminių medžiagų gamyba", "Agriculture": "<PERSON><PERSON><PERSON><PERSON>", "Agriculture / Environmental": "Žemė<PERSON> / A<PERSON>linkosauga", "Agriculture / Forestry / Fishing": "<PERSON><PERSON><PERSON><PERSON> / Miškininkyst<PERSON> / Žvejy<PERSON>", "Agriculture, Construction, Mining Machinery Manufacturing": "<PERSON><PERSON><PERSON><PERSON>, staty<PERSON>, kasybos mašinų gamyba", "Agronomist": "Agronomas", "Air, Water, and Waste Program Management": "<PERSON>, vandens ir atliekų programų valdymas", "Airlines and Aviation": "Oro linijos ir aviacija", "Akan": "<PERSON><PERSON>", "Albanian": "Albanų kalba", "Align text to center": "Sulygiuoti tekstą į centrą", "All": "Visi", "All GDPR messages to candidates will be sent from this user.": "Visi BDAR pranešimai kandidatams bus siunčiami iš šio naudotojo.", "All applications that are currently marked with the dropout reason {originalDropoutReason} will be changed to {mergedDropoutReason}.": "<PERSON><PERSON><PERSON>, kuriose š<PERSON>o metu pažymėta iškritimo priežastis {originalDropoutReason}, bus pakeistos į {mergedDropoutReason}.", "All day": "<PERSON><PERSON><PERSON>", "All files of this type will appear as {baseFileType} after deletion.": "Visi šio tipo failai po ištrynimo bus rodomi kaip {baseFileType}.", "All forms": "Visos formos", "All invites can be delivered via selected channels": "Visi kvietimai gali būti pristatomi pasirinktais kanalais", "All is good": "<PERSON><PERSON><PERSON> gera<PERSON>", "All references": "Visos <PERSON>", "All responses": "Visi atsakymai", "All rights reserved.": "<PERSON><PERSON><PERSON>.", "All roles": "Visi vaidmenys", "All shared candidates": "Visi bendri kandidatai", "All slots in this interview have been cancelled. Please add more slots.": "Visi šio pokalbio laikai atšaukti. Prašome pridėti daugiau laiko tarpų.", "All stages in a project template must be categorized.": "Visi projekto šablono etapai turi būti suskirstyti į kategorijas.", "All stages in all projects are categorized.": "Visų projektų etapai yra suskirstyti į kategorijas.", "All teams": "<PERSON><PERSON>os koman<PERSON>", "All times :tz (:offset).": "Visi laikai :tz (:offset).", "All times are {timezone}": "Visi laikai yra {timezone}", "All types": "Visi tipai", "All users": "Visi naudotojai", "All users of this instance must verify their phone numbers.": "Visi šios instancijos naudotojai turi patikrinti savo telefono numerius.", "Allocation & Distribution": "Pa<PERSON><PERSON><PERSON><PERSON> ir p<PERSON>", "Allow candidates without email": "<PERSON><PERSON><PERSON><PERSON> kandidatus be el. p<PERSON>", "Allow candidates without location": "<PERSON><PERSON><PERSON> kandidatus be nurodytos vietos", "Allow direct applications from CVO": "Leidimas teikti ties<PERSON>gin<PERSON> paraiškas iš CVO", "Allow direct applications from job portals": "Leisti teikti ties<PERSON>gin<PERSON> paraiškas iš darbo skelbimų portalų", "Allow limited users to schedule interviews": "Leiskite ribotiems naudotojams planuoti pokalbius", "Allow login with password": "Leisti prisijungti su slaptažodžiu", "Allow password logins": "Leisti prisijungti slap<PERSON>žodžiu", "Allow regular users to create tags": "Leidimas įprastiems naudotojams kurti žymas", "Alphabetic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Alphabetically": "<PERSON><PERSON> a<PERSON>", "Alternative Dispute Resolution": "Alternatyvus ginčų sprendimas", "Alternative Medicine": "Alternatyvioji medicina", "Always ask approval from these users": "Visada prašykite šių naudotojų patvirtinimo", "Always ask requisition approval from users": "Visada prašykite naudotojų patvirtinti užklausą", "Ambulance Services": "Greitosios pagalbos paslaugos", "Ameliorator": "<PERSON><PERSON><PERSON><PERSON>", "Amharic": "Amharų kalba", "Amusement Parks and Arcades": "Pramogų parkai ir arkados", "An array containing segments of transcribed speech, ordered by when the speech occurred.": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> yra <PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON> pagal tai, kada įvyko kalba.", "An array in which each element is a summarization sentence.": "<PERSON><PERSON><PERSON>, kuriame k<PERSON>nas elementas yra apibendr<PERSON><PERSON> sa<PERSON>.", "An array in which each object represents a cleaned-up transcript segment.": "<PERSON><PERSON><PERSON>, kuriame kiekvienas objektas yra išvalytas nuorašo segmentas.", "An error occurred": "Įvyko klaida", "An error occurred when starting the camera.": "Paleidžiant fotoaparatą įvyko klaida.", "An error occurred while processing the transcript.": "<PERSON><PERSON><PERSON><PERSON><PERSON> nuo<PERSON> įvyko klaida.", "An error occurred!": "Įvyko klaida!", "An object representing a single transcribed segment of speech.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, vaizduojantis vieną transkribuotą kalbos segmentą.", "An unexpected error occurred.": "Įvyko net<PERSON><PERSON><PERSON> k<PERSON>.", "Analyse again": "Analizuoti dar kartą", "Analyst": "<PERSON><PERSON><PERSON><PERSON>", "And the candidate will get a message like this:": "<PERSON>r kandi<PERSON>s gaus tokią žinutę:", "Animal Feed Manufacturing": "Gyvūnų pašarų gamyba", "Animation and Post-production": "Animacija ir postprodukcija", "Anonymize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Anonymize candidate": "<PERSON><PERSON><PERSON><PERSON>", "Anonymized CV": "Anoniminis CV", "Anonymized candidate": "<PERSON><PERSON><PERSON><PERSON> kandi<PERSON>", "Anonymized summary": "Anonimiška santrauka", "Answer time": "<PERSON><PERSON><PERSON><PERSON>", "Answered video interview": "Atsakytas vaizdo interviu", "Answers accepted in strict order": "Atsakymai pri<PERSON>ti numa<PERSON>ta tvarka", "Answers allowed in any order": "Leidžiami atsakymai bet kokia tvarka", "Answers only visible to admins": "Atsaky<PERSON><PERSON> matomi tik <PERSON>s", "Any": "Bet kuris", "Any account": "Bet kuri paskyra", "Any data you have entered in this form must be present on the landing page and vice versa.": "Visi šioje formoje įvesti duomenys turi būti pateikti sukurtame puslapyje ir atvirkšč<PERSON>i.", "Any time when available": "Bet kuriuo metu, kai tik yra galim<PERSON>", "Any type": "Bet kokio tipo", "Apparatus operator": "Aparatų operatorius", "Apparel Manufacturing": "Drabužių gamyba", "Appearance": "Išvaizda", "Appliances, Electrical, and Electronics Manufacturing": "<PERSON><PERSON><PERSON><PERSON><PERSON>, elektros ir elektronikos prietaisų gamyba", "Applicants target stage": "Pareiškėjų tikslinis etapas", "Application date": "Paraiškos data", "Application date and time format": "Paraiškos data ir laiko formatas", "Application deadline": "Paraiškų teikimo terminas", "Application form": "Paraiškos forma", "Application forms": "Paraiškų formos", "Application language": "Paraiškų kalba", "Application methods": "Paraiškos teiki<PERSON> būdai", "Applications": "Paraiškos", "Applied higher education": "Taikomasis au<PERSON> mok<PERSON>", "Applied through form": "Taikoma per formą", "Applies to all user signatures.": "Taikoma visiems naudotojų parašams.", "Apply colours to all links": "Taikyti spalvas visoms nuorodoms", "Apply with resume": "Kandidatuokite su gyvenimo a<PERSON>šymu", "Applying to this position has ended.": "Kandidatavimas į šią poziciją baig<PERSON>si.", "Appraiser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Apprenticeship": "Pameistryst<PERSON>", "Approval needed from": "Reikia gauti pat<PERSON> iš", "Approvals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Approvals deadline": "Patvirtinimų terminas", "Approve": "<PERSON><PERSON><PERSON><PERSON>", "Approved": "<PERSON><PERSON><PERSON><PERSON>", "Approved at": "<PERSON><PERSON><PERSON><PERSON>", "Approved by:": "<PERSON><PERSON><PERSON>:", "April": "<PERSON><PERSON><PERSON>", "Arabic": "Arabų kalba", "Aragonese": "Aragoneso", "Architect": "Architektas", "Architectural and Structural Metal Manufacturing": "Architektūros ir metalo konstrukcijų gamyba", "Architecture and Planning": "Architektū<PERSON> ir <PERSON>", "Archive": "<PERSON><PERSON><PERSON>", "Archived": "Archyvuota", "Archivist": "<PERSON><PERSON><PERSON><PERSON>", "Are Ukrainians welcome? (CVBankas)": "Ar ukrainiečiai yra la<PERSON>? (CVBankas)", "Are you sure you want to archive this form?": "Ar tikrai norite archyvuoti šią formą?", "Are you sure you want to cancel sending message \"{subject}\"?": "Ar tikrai norite at<PERSON><PERSON><PERSON> „{subject}“  siuntimą?", "Are you sure you want to cancel the meeting with {candidateName}?": "Ar tikrai norite at<PERSON><PERSON><PERSON> susi<PERSON> su {candidateName}?", "Are you sure you want to deactivate this user?": "Ar tikrai norite deaktyvuoti šį naudotoją?", "Are you sure you want to delete action {actionName}?": "Ar tikrai norite iš<PERSON> {actionName} veiksmą?", "Are you sure you want to delete consent subtype: {consentSubtypeName}?": "Ar tikrai norite ištrinti sutikimo tipą: {consentSubtypeName}?", "Are you sure you want to delete dropout reason {dropoutReason}?": "Ar tikrai norite i<PERSON>nti iškritimo priežastį {dropoutReason}?", "Are you sure you want to delete file type {fileType}? ": "Ar tikrai norite ištrinti {fileType} failo tipą? ", "Are you sure you want to delete file {fileName}?": "Ar tikrai norite i<PERSON>nti failą {fileName}?", "Are you sure you want to delete the API key {apiKeyName}? ": "Ar tikrai norite ištrinti API raktą {apiKeyName}? ", "Are you sure you want to delete the landing page {landingName}?": "Ar tikrai norite i<PERSON>nti puslapį {landingName}?", "Are you sure you want to delete the project failure reason \"{projectFailureReason}\"?": "Ar tikrai norite ištrinti projekto nesėkmės priežastį „ {projectFailureReason} “?", "Are you sure you want to delete this API key?": "Ar tikrai norite ištrinti šį API raktą?", "Are you sure you want to delete this client?": "Ar tikrai norite ištrinti šį klientą?", "Are you sure you want to delete this comment?": "Ar tikrai norite ištrinti šį komentarą?", "Are you sure you want to delete this custom activity?": "Ar tikrai norite ištrinti šią pasirinktinę veiklą?", "Are you sure you want to delete this dropout reason?": "Ar tikrai norite ištrinti šią atmetimo priežastį?", "Are you sure you want to delete this event slot?": "Ar tikrai norite ištrinti šį įvykį?", "Are you sure you want to delete this image?": "Ar tikrai norite ištrinti šį paveikslėlį?", "Are you sure you want to delete this tag? It is currently associated with {count} landings.": "Ar tikrai norite ištrinti ši<PERSON> žymą? Šiuo metu ji susijusi su {count} puslapiais.", "Are you sure you want to generate a new API key for this integration?": "Ar tikrai norite sukurti naują API raktą šiai integracijai?", "Are you sure you want to permanently delete this chat?": "Ar tikrai norite visam laikui i<PERSON>trinti šį pokalbį?", "Are you sure you want to permanently delete this interview?": "Ar tikrai norite visam laikui ištrinti šį interviu?", "Are you sure you want to permanently delete this video?": "Ar tikrai norite visam laikui ištrinti šį vaizdo įrašą?", "Are you sure you want to unpublish job ad {position_name} from {count} job portals?": "Ar tikrai norite <PERSON> {position_name} darbo skelbimo publikavimą {count} darbo portal<PERSON> (-ų)?", "Are you sure you want to unpublish job ad {position_name}?": "Ar tikrai norite nebepublikuoti darbo skel<PERSON> {position_name}?", "Area Management": "Teritorijos valdym<PERSON>", "Armature fitter": "<PERSON>at<PERSON><PERSON>", "Armed Forces": "<PERSON>ink<PERSON><PERSON><PERSON><PERSON>", "Armenian": "Armėnų", "Art / Creative": "Menas / kūryba", "Artificial Rubber and Synthetic Fiber Manufacturing": "<PERSON><PERSON><PERSON><PERSON><PERSON> gumos ir sintetinio pluoš<PERSON> gam<PERSON>", "Artist": "Menininkas", "Artists and Writers": "Menininkai ir rašytojai", "Arts": "<PERSON><PERSON>", "Arts/Graphic Design": "<PERSON><PERSON> / grafinis dizainas", "As the transcript has been auto-generated, it might include spelling errors.": "Kadangi nuorašas buvo sukurtas automatiškai, joje gali būti rašybos klaidų.", "As the transcript text has been auto-generated, it might include spelling errors.": "Kadangi nuorašo tekstas buvo sugeneruotas automatiškai, jame gali būti rašybos klaidų.", "Ask AI to write": "DI Pagalba", "Ask candidate for references": "Paprašyti kandidato rekomendacijų", "Ask for data processing consent until": "Prašyti sutikimo tvarkyti duomenis iki", "Ask for reference": "Paprašykite rekomendacijos", "Ask for reference again": "Dar kartą paprašykite rekomendacijos", "Ask for references": "Paprašyti rekomendacijų", "Ask referee to submit evaluation": "Paprašykite atstovo pateikti įvertinimą", "Ask reference to submit evaluation": "Paprašyti rekomendacijos pateikti vertinimą", "Ask user for feedback about hiring process": "Paprašyti naudotojo atsiliepimo apie įdarbinimo procesą", "Assamese": "Asamių kalba", "Assembler": "<PERSON><PERSON><PERSON>jas", "Assign room": "Priskirti kambarį", "Assigned to": "Priskirta", "Assignee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Assistant": "<PERSON><PERSON><PERSON><PERSON>", "Assistant Management": "Vady<PERSON>", "Assistant, Customer Support Specialist, Sales Specialist, Аccount Мanager...": "<PERSON><PERSON><PERSON><PERSON>, klientų aptarnavimo specialistas, pardavimų specialistas, klient<PERSON> vadybinink<PERSON>...", "Assisting / Administration": "Pagalba / Administravimas", "Associate": "Asocijuota<PERSON> darbuo<PERSON>", "Associate an existing landing page with this project": "Susieti esamą nukreipimo puslapį su šiuo projektu", "Associate's degree": "Asoci<PERSON><PERSON><PERSON> la<PERSON>", "Assure that declining this consent will not affect any pending candidacy.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON><PERSON> suti<PERSON>o atsisakymas neturės įtakos joki<PERSON> nebaigtoms kandidatūroms.", "Async video interview invite": "Kvietimas į asinchroninį vaizdo pokalbį", "Asynchronous Video Interviews": "Asinchroniniai vaizdo interviu", "Asynchronous video interview": "Asinch<PERSON><PERSON>s vaizdo interviu", "Attach Files": "<PERSON><PERSON><PERSON><PERSON>", "Attachments": "Pried<PERSON>", "Attachments from current project": "<PERSON>bar<PERSON>io projekto p<PERSON>ai", "Attachments from other projects": "Kitų projektų priedai", "Attachments not linked to any project": "<PERSON><PERSON><PERSON>, nesusiję su jokiais projektais", "Attempts exhausted": "Išnaudoti bandymai", "Attorney": "Advokatas", "Audio and Video Equipment Manufacturing": "Garso ir vaizdo įrangos gamyba", "Audit Logging": "Audito registravimas", "Audit log": "Audito žurnalas", "August": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Authentication providers": "Autentifikavimo paslaugų teikėjai", "Author": "Autorius", "Auto sync": "<PERSON><PERSON><PERSON><PERSON>", "Auto-add tags AI": "Automatinis žymų pridėjimas AI", "Autocrane operator": "Autokrano operatorius", "Autoelectrician": "Autoelektrikas", "Automate repetitive tasks and communications to save time and improve the candidate experience.": "Automatizuokite pasikartojančias užduotis ir komunikaciją, kad sutaupytumėte laiko ir pagerintumėte kandidatų patirtį.", "Automated User Provisioning (SCIM)": "Automatizuotas naudotojų aprūpinimas (SCIM)", "Automatic": "Automatinis", "Automatic - after candidate has submitted a form": "Automatiškai – po to, kai kandidatas pateikia formą", "Automatic - after candidate has submitted referees": "Automatiškai – po to, kai kandidatas pateikia rekomendacijos da<PERSON>s", "Automatic - after candidate has submitted references": "Automatiškai – po to, kai kandidatas pateikia rekomendacijas", "Automatic - after project status change": "Automatiškai – po to, kai pasikeičia projekto būklė", "Automatic - after receiving video interview response": "Automatiškai - gavus vaizdo interviu atsakymą", "Automatic - after time in stage": "Automatinis - po laiko, praleisto etape", "Automatic - inherit from landing": "Automatinis - paimtas iš <PERSON>", "Automatic - specific time in future": "Automatinis - konkretus laikas ateity<PERSON>", "Automatic actions": "Automatiniai veiksmai", "Automatic actions scheduled on a specific date are not cloned.": "Automatiniai veiksmai, suplanuoti konkrečią dat<PERSON>, nėra klonuojami.", "Automatic heading sizing": "Automatinis <PERSON> d<PERSON>ž<PERSON> nustatym<PERSON>", "Automatic summaries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Automatically archive landing pages after application deadline": "Automatiškai archyvuokite kurtus puslapius pasibaigus paraiškos pateikimo terminui", "Automatically archives landing pages after their application deadlines have passed. Applies retroactively to old landing pages.": "Automatiškai archyvuoja puslapius pasibaigus jų paraiškų pateikimo terminams. Taikoma atgaline data seniau kurtiems puslapiams.", "Automation Machinery Manufacturing": "Automatikos mašinų gamyba", "Automations": "Automatizacijos", "Automechanic": "Automechanikas", "Automotive/Aerospace": "Automobilių / kosmoso pramonė", "Autoplay": "Automat<PERSON><PERSON> paleidimas", "Autotinman": "Automobilių kėbulo remonto specialistas", "Autowasherman": "Automobilių plovėjas", "Auxiliary Worker": "<PERSON><PERSON><PERSON><PERSON>", "Available days": "<PERSON><PERSON><PERSON> die<PERSON>", "Available merge tags": "<PERSON><PERSON><PERSON>", "Available period": "<PERSON><PERSON><PERSON>", "Available time end": "<PERSON><PERSON><PERSON> laiko p<PERSON>", "Available time start": "<PERSON><PERSON><PERSON> la<PERSON> p<PERSON>", "Avaric": "<PERSON><PERSON>", "Average": "Vidutiniškai", "Average rating": "Vid<PERSON><PERSON> įvertinimas", "Average time from candidate submission to hire based on people hired in the last :days days.": "<PERSON><PERSON><PERSON><PERSON> kandidato įdarbinimo laikas per pastarąsias :days dienas.", "Average time from project start to end for projects finished in the last :days days.": "Vidutinis projekto trukm<PERSON>s laikas per paskutines :days dienas.", "Average time in stage": "Vidutinis etapo laikas", "Avestan": "Avestos", "Avg. candidates": "Kandidatų vidurkis", "Aviation and Aerospace Component Manufacturing": "Aviacijos ir kosmoso komponentų gamyba", "Aviation specialist": "Aviacijos <PERSON>", "Avoid corporate clichés": "Venkite bendrųjų klišių", "Aymara": "Aymara", "Azerbaijani": "Azerbaidžanas", "Azure OpenAI (data never leaves EU)": "Azure OpenAI (duomenys niekada nepalieka ES)", "Bachelor's degree": "<PERSON><PERSON><PERSON><PERSON>", "Back": "Atgal", "Back to Requisitions": "Grįžti į užklausas", "Back to Talent Pool": "Grįžti į kandidatų duombazę", "Back to candidate profile": "Grįžti į kandidato profilį", "Back to client details": "Grįžti į informaciją apie klientą", "Back to clients list": "Grįžti į k<PERSON>ų sąrašą", "Back to project": "Atgal į projektą", "Back to projects list": "Atgal į projektų sąrašą", "Back to stage": "Atgal į etapą", "Background": "<PERSON><PERSON><PERSON>", "Background color": "Fono spalva", "Background image": "Fono paveikslėlis", "Bad application conversion": "Blogas aplikacijų konvertavimas", "Bad click conversion": "Bloga paspaudimų konversija", "Baked Goods Manufacturing": "Kepinių gamyba", "Baker": "<PERSON><PERSON><PERSON><PERSON>", "Baltics": "<PERSON><PERSON><PERSON><PERSON>", "Bambara": "Bambara", "Bank account": "Banko sąskaita", "Banking": "Bankininkystė", "Banking / Insurance": "Bankininkystė / Draudimas", "Banner image": "<PERSON><PERSON><PERSON><PERSON>", "Banner image for job ad": "<PERSON><PERSON> s<PERSON><PERSON><PERSON> rekla<PERSON> p<PERSON>", "Barman": "Barmenas", "Bars, Taverns, and Nightclubs": "<PERSON><PERSON>, smuklės ir naktiniai klubai", "Bartender": "Barmenas", "Bashkir": "Baškirų", "Basic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basic education": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basic information": "Pagrindinė informacija", "Basics": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Basque": "Baskų kalba", "Bathhouse attendant": "<PERSON><PERSON><PERSON> da<PERSON>", "Be sure to include their personal invite link:": "Būtinai įtraukite asmeninio kvietimo nuorodą:", "Bed-and-Breakfasts, Hostels, Homestays": "<PERSON><PERSON><PERSON><PERSON><PERSON>, host<PERSON><PERSON>", "Before presenting, please associate the candidate with a project.": "Prieš pristatydami kandidatą susiekite su projektu.", "Before proceeding, please check your email for a verification link.": "<PERSON><PERSON>š tęsdami patikrinkite savo el. la<PERSON>, jame rasite patvir<PERSON> nuorod<PERSON>.", "Before scheduling an interview, please associate the candidate with a project.": "<PERSON><PERSON><PERSON> planuodami pokalbį, sujunkite kandidatą su projektu.", "Belarusian": "Baltarusijos", "Below you can set custom categories for your stages. This will help you get statistics that are better aligned with your hiring process.": "Žemiau galite nustatyti tinkamas kategorijas savo atrankų etapams. Tai padės gauti statistinius duomenis, kurie geriau atitinka jūsų įdarbinimo procesą.", "Benefits (CVO)": "<PERSON><PERSON><PERSON><PERSON> (CVO)", "Bengali": "Bengalų kalba", "Better luck next time!": "Linkime s<PERSON>kmingo kito karto!", "Beverage Manufacturing": "Gėrimų gamyba", "Bihari languages": "Biharų kalbos", "Billing address": "Atsiskaitymo ad<PERSON>", "Biomass Electric Power Generation": "Elektros energijos gamyba iš biomasės", "Biotechnology Research": "Biotechnologijų moksliniai tyrimai", "Birth date": "Gimimo data", "Bislama": "B<PERSON>lama", "Blacksmith": "<PERSON><PERSON><PERSON>", "Block background": "<PERSON><PERSON><PERSON> fonas", "Blockchain Services": "\"Blockchain\" paslau<PERSON>", "Blogs": "Tinklaraščiai", "Blue collar, workers": "\"Blue collar\" darbininkai", "Board": "<PERSON><PERSON>", "Body": "Žinutė", "Body text": "Kūno te<PERSON>", "Boilers, Tanks, and Shipping Container Manufacturing": "<PERSON><PERSON><PERSON>, rezervuarų ir jūrinių konteinerių gamyba", "Bold": "Drąsiai", "Book Publishing": "Knygų leidyba", "Book a 20 min demo": "Registruokitės 20 min. skamb<PERSON>č<PERSON>i", "Book and Periodical Publishing": "Knygų ir periodinių leidinių leidyba", "Bosnian": "Bosnijos ir Her<PERSON>", "Bounced at": "Atmestas", "Brand": "Prekės ženkla<PERSON>", "Breadborder": "Elektronikos prototipų kūrėjas", "Breeder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Breton": "Breton<PERSON>", "Breweries": "<PERSON><PERSON>", "Bring your own SMTP": "Atsineškite savo SMTP", "Broadcast Media Production and Distribution": "Transliavimo žiniasklaidos gamyba ir platinimas", "Broker": "<PERSON><PERSON><PERSON>", "Browse": "Naršykite", "Browse...": "Naršykite...", "Buffer time after interview": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> po pokalbio", "Buffer time before interview": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> p<PERSON> pokalbį", "Builder": "Statybininkas", "Builder, Electrician, Driver, Tailor, Plumber, Cook, Teacher...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, santechnik<PERSON>, v<PERSON>ė<PERSON>, mokytoja<PERSON>...", "Building Construction": "Pastatų statyba", "Building Equipment Contractors": "Statybos įrangos rangovai", "Building Finishing Contractors": "Pastatų apdailos <PERSON>", "Building Structure and Exterior Contractors": "Pastatų konstrukcijų ir eks<PERSON><PERSON>o rang<PERSON>i", "Bulgarian": "Bulgarų", "Bulk import users": "Masinis naudotojų importas", "Bulldozer driver": "Buldozerio <PERSON>", "Bullets": "Punktai", "Burmese": "Birmos", "Business Consulting and Services": "<PERSON><PERSON><PERSON> konsultacijos ir paslaugos", "Business Content": "<PERSON><PERSON><PERSON> turinys", "Business Development": "<PERSON><PERSON><PERSON> plėtra", "Business Intelligence Platforms": "Verslo žvalgybos platformos", "Business Skills Training": "Verslo įgūdžių mokymas", "Butcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Button color": "<PERSON><PERSON>uk<PERSON> spalva", "Button in stage": "<PERSON><PERSON><PERSON><PERSON> et<PERSON>", "Buttons": "<PERSON><PERSON><PERSON><PERSON>", "Buying": "Pirkimas", "Buying/supply": "Pirkimas ir (arba) tiekimas", "By Project Manager": "Pagal projekto vadovą", "By choice": "<PERSON><PERSON>", "By default, candidate profiles are pseudonymized using a one-way cryptographic function.  When they re-apply then their history (comments, projects) is restored. If you enable permanent deletion then candidate histories will not be restored.": "<PERSON><PERSON> numatytu<PERSON><PERSON> nustatymus kandidatų profiliai pseudonimizuojami naudojant vienakryptę kriptografinę funkciją.  Kai jie vėl kandidatuoja, jų istorija (komentarai, projektai) atkuriama. Jei įjungsite nuolatinį ištrynimą, kandidatų istorijos nebus atkuriamos.", "By stage": "Pagal etapą", "By time": "<PERSON><PERSON>", "By uploading your CV, you agree that our AI will process and extract relevant information to pre-fill the form fields. Click the link above to opt out and fill the form manually.": "Įkeldami savo CV sutinkate, kad mūsų dirbtinis intelektas apdorotų ir išskirtų atitinkamą informaciją, kad būtų iš anksto užpildyti formos laukai. Norėdami atsisakyti ir užpildyti formą rankiniu būdu, spustelėkite aukščiau esančią nuorodą.", "Byte": "Baitas", "Bytes": "Bitai", "CANCELLED": "ATŠAUKTAS", "CTA button text": "CTA mygtuko tekstas", "CTA button url": "CTA mygtuko url", "CV": "CV", "Cable and Satellite Programming": "Ka<PERSON><PERSON><PERSON><PERSON> ir palydo<PERSON>avimas", "Calendar": "<PERSON><PERSON><PERSON><PERSON>", "Calendar invite details": "Kvietimo informacija", "Calendar invites FROM email address": "Kalendoriaus kvietimai IŠ el. pašto adreso", "Calendar navigation": "<PERSON><PERSON>dor<PERSON>us navigacija", "Calendar time": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>s", "Call": "Skambinkite", "Call (no answer)": "Skambutis (neatsiliepia)", "Call time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Campaign is currently running": "Šiuo metu vykdoma kampanija", "Campaign type": "Kampanijos tip<PERSON>", "Can administer teams": "Gali administru<PERSON>i komandas", "Can be left empty": "Galima palikti tuščią", "Can see all projects?": "Gali matyti visus projektus?", "Can we contact you about vacancies at [organization_name]?": "Ar galime su jumis susisiekti dėl laisvų darbo vietų [organization_name]?", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Cancel interview": "<PERSON><PERSON><PERSON><PERSON> darbo pokalbį", "Cancel sending": "<PERSON><PERSON><PERSON><PERSON>", "Cancelled": "<PERSON><PERSON>uk<PERSON>", "Candidate": "Kandidatas", "Candidate A": "Kandidatas A", "Candidate B": "Kandidatas B", "Candidate CV": "Kandidato CV", "Candidate CV summaries, similarity search, writing assistance, and more.": "Kandidatų gy<PERSON>, panašumų p<PERSON>, pagalba rašant ir dar daug<PERSON>.", "Candidate GDPR consent validity": "Kandidato BDAR sutikimo galiojimas", "Candidate ID": "Kandidato ID", "Candidate ID (Teamdash)": "Kandidato ID (Teamdash)", "Candidate Net Promoter Score based on surveys in the last :days days.": "Kandidatų grynojo populiarumo bala<PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> remiantis apklausomis per pastarąsias :days dienas.", "Candidate added to project": "Į projektą įtrauktas kandidatas", "Candidate answered": "Kandidatas at<PERSON>ė", "Candidate can't be analyzed as they have no CV.": "Kandidatas negali b<PERSON><PERSON> anal<PERSON>, nes neturi CV.", "Candidate card settings": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> nust<PERSON>", "Candidate created date": "Kandidato sukūrimo data", "Candidate custom fields": "Kandidatų pasirinktiniai laukai", "Candidate email": "Kandidato el. paštas", "Candidate email (Teamdash)": "Kandidato el. <PERSON> (Teamdash)", "Candidate exceeded the attempt limit ({attemptLimitNumber}). There were {attemptCountNumber} attempts.": "Kandidatas viršijo bandymų limitą ( {attemptLimitNumber} ). Buvo {attemptCountNumber} bandymų.", "Candidate exists in {hrisName}": "Kandidatas yra {hrisName}", "Candidate form": "Kandi<PERSON><PERSON> forma", "Candidate forms": "<PERSON><PERSON><PERSON><PERSON> form<PERSON>", "Candidate location": "Kandidato vieta", "Candidate management": "Kandidatų valdymas", "Candidate message": "Kandi<PERSON><PERSON>", "Candidate motivation, likes and dislikes": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>, pomėgiai ir antipatijos", "Candidate name": "<PERSON><PERSON><PERSON><PERSON> vardas ir pava<PERSON>", "Candidate name (Teamdash)": "<PERSON><PERSON><PERSON><PERSON> (Teamdash)", "Candidate not in {hrisName}": "<PERSON><PERSON><PERSON><PERSON> {hrisName}", "Candidate phone": "Kandidato telefonas", "Candidate photo": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "Candidate presentations": "<PERSON><PERSON><PERSON><PERSON>", "Candidate profile target field": "<PERSON><PERSON><PERSON><PERSON> profi<PERSON>", "Candidate progression": "Kandidato p<PERSON>žanga", "Candidate reach": "Kandidatų pasiekiamumas", "Candidate reached the time limit.": "Kandidatas pasiek<PERSON> la<PERSON>.", "Candidate reasons": "Kandidatų priežastys", "Candidate reference ID (Teamdash)": "Kandidato rekomendacijos ID (Teamdash)", "Candidate reference email (Teamdash)": "Kandidato informacinis el. <PERSON> (Teamdash)", "Candidate reference name (Teamdash)": "Kandidato reko<PERSON><PERSON><PERSON> (Teamdash)", "Candidate saved!": "Kandidatas iš<PERSON>ugota<PERSON>!", "Candidate scorecard saved.": "Kandidato vertinimo k<PERSON> iš<PERSON>ug<PERSON>.", "Candidate sent to :integrationName": "Kandidatas išsiųstas į :integrationName", "Candidate sent to BambooHR": "Kandidatas persiųstas į BambooHR", "Candidate sent to {hrisName}": "Kandidatas i<PERSON> {hrisName}", "Candidate submission sources": "<PERSON><PERSON><PERSON> ka<PERSON>", "Candidate survey question": "Kandidatų apklausos klausimas", "Candidates": "Kandidatai", "Candidates added to the project after": "<PERSON><PERSON><PERSON><PERSON>, įtraukti į projektą po", "Candidates anonymized!": "Kandidatai anonimizuoti!", "Candidates can only choose times when all the recruiters are free.": "Kandidatai gali pasirinkti tik tuos laikus, kai visi įdarbinimo specialistai yra laisvi.", "Candidates can see this on social media sharing previews.": "Kandidatai tai gali pamatyti socialinės žiniasklaidos dalijimosi per<PERSON>rose.", "Candidates deleted!": "Kandidatai ištrinti!", "Candidates hate long commutes. If you're hiring for on-site positions, you can make better hiring decisions with location data.": "Kandidatai nekenčia ilgų kelionių į darbą ir atgal. Jei samdote darbuotojus į darbo vietą, naudodamiesi vietos duomenimis galite priimti geresnius sprendimus.", "Candidates reached interview stage": "Kandidatai pasiekė pokalbio etapą", "Candidates shared!": "Kandidatai pasidalijo!", "Candidates target stage": "Kandidatų tikslinis etapas", "Candidates to add": "<PERSON><PERSON><PERSON><PERSON>, kuriuos reikia pridėti", "Candidates to share": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "Candidates were not copied.": "Kandidatai nebuvo nukopijuoti.", "Candidates who gave a 9 or 10 are called promoters, 7 or 8 are passives and 0-6 are detractors.": "<PERSON><PERSON><PERSON><PERSON>, davę 9 arba 10 balų, vadi<PERSON>i s<PERSON>is, 7 arba 8 balai - pasyviaisiais, o 0-6 balai - neigiamaisiais.", "Candidates who reached {stage} stage": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> {stage} etapą", "Candidates with ignored/declined consent renewals": "<PERSON><PERSON><PERSON><PERSON>, kurie ignoravo arba atsisakė pratęsti sutikimus", "Candidates with pending consent renewals": "<PERSON><PERSON><PERSON><PERSON>, kurių sutikimas dar nepratęstas", "Capital Markets": "<PERSON><PERSON><PERSON>", "Captain": "<PERSON><PERSON><PERSON><PERSON>", "Car mechanician": "Automobilių mechanikas", "Car painter": "Automobilių dažytojas", "Car service master": "Automobilių serviso meistras", "Career Pages": "<PERSON><PERSON><PERSON><PERSON>", "Career experience": "<PERSON><PERSON><PERSON><PERSON>", "Career grid": "<PERSON><PERSON><PERSON><PERSON> tinkle<PERSON>", "Career list": "<PERSON><PERSON><PERSON><PERSON>", "Careers": "<PERSON><PERSON><PERSON><PERSON>", "Carpenter": "Dailidė", "Cashier": "<PERSON><PERSON><PERSON><PERSON>", "Catalan": "Katalonų", "Catalan, Valencian": "Katalonų, Valensijos", "Categories (CVK/CVM)": "Kategorijos (CVK/CVM)", "Categories (CVO)": "<PERSON><PERSON><PERSON><PERSON> (CVO)", "Category": "Kategorija", "Category (CVbankas)": "Kategorija (CVbankas)", "Category (SS.lv)": "<PERSON><PERSON><PERSON> (SS.lv)", "Caterers": "<PERSON><PERSON><PERSON>", "Catering": "<PERSON><PERSON><PERSON><PERSON>", "Central Europe": "Vidurio Europa", "Central Khmer": "Centriniai Khmerai", "Chamorro": "<PERSON><PERSON><PERSON>", "Change my score": "Pakeisti mano rezultatą", "Change response": "<PERSON><PERSON><PERSON>", "Change video": "<PERSON><PERSON><PERSON> vai<PERSON>do įrašą", "Changes made here will not affect existing projects created from this template.": "Čia atlikti pakeitimai neturės įtakos esamiems projektams, sukurtiems pagal šį šabloną.", "Channels": "Kanalai", "Charity": "<PERSON><PERSON><PERSON>", "Chart": "Diagrama", "Chat thread": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chechen": "Čečėnija", "Check for applicants to assess": "<PERSON><PERSON><PERSON><PERSON><PERSON> kandidatus, k<PERSON><PERSON><PERSON> reikia įvertinti", "Check out who rejected it and if they left a comment:": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kas jį atmetė ir ar paliko komentarą:", "Check references": "<PERSON><PERSON><PERSON><PERSON>", "Checkbox": "<PERSON>žym<PERSON><PERSON> langelis", "Checkbox numeric": "Skaitmen<PERSON><PERSON> langelis", "Chemical Manufacturing": "Cheminė gamyba", "Chemical Raw Materials Manufacturing": "Cheminių žaliavų gamyba", "Chichewa, Chewa, Nyanja": "Chichewa, Chewa, Nyanja", "Chief - cook": "Virtu<PERSON><PERSON><PERSON> virėjas", "Child Day Care Services": "Vaikų dienos priežiūros p<PERSON>lau<PERSON>", "Children": "Vaikai", "Chinese": "Kinų", "Chiropractors": "Chiropraktikai", "Choices": "Pasirink<PERSON><PERSON>", "Choose": "Pasirinkite", "Choose Indeed Apply as the credential type and click Save.": "Pasirinkite \"Indeed Apply\" ir spustelėkite Išsaugoti.", "Choose a dropout reason to merge {dropoutReason} with.": "Pasirinkite iškritimo priežastį, su kuria norite sujungti {dropoutReason}.", "Choose a version from the history to preview": "Pasirinkite versiją i<PERSON> is<PERSON>, kurią norite perž<PERSON>", "Choose client": "Pasirinkite klientą", "Choose company settings": "Pasirinkite įmonės nustatymus", "Choose dropout reason(s)": "Pasirinkite iškritimo priežastį (-is)", "Choose image": "Pasirinkite paveikslėlį", "Choose interview": "Pasirinkite interviu", "Choose manager": "Pasirinkite vadybininką", "Choose or type source": "Pasirinkite arba įveskite šaltinį", "Choose project": "Pasirinkite projektą", "Choose project manager": "Pasirinkite projekto vadovą", "Choose scorecard": "Pasirinkite vertinimo kortelę", "Choose statuses": "Pasirink<PERSON> bū<PERSON>as", "Choose tag": "Pasirinkite žymą", "Choose template": "Pasirinkite šabloną", "Choose this value, the other will be removed": "Pasirinkite š<PERSON>, kita bus pašalinta", "Choose user to reassign these items to:": "Pasirinkite naudotoją, kuriam norite iš naujo priskirti šiuos elementus:", "Church Slavonic, Old Bulgarian, Old Church Slavonic": "ba<PERSON>nytinė slavų kalba, senoji bulgarų kalba, senoji bažnytinė slavų kalba", "Chuvash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Circuses and Magic Shows": "Cirkai ir magi<PERSON>", "Cities (cv.lt)": "Miestai (cv.lt)", "City": "Miestas", "City (CVK/CVM)": "Miestas (CVK/CVM)", "City (CVbankas)": "Miestas (CVbankas)", "City or county (SS.lv)": "Miestas arba a<PERSON>kritis (SS.lv)", "Civic and Social Organizations": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir socialinės organizaci<PERSON>", "Civil Engineering": "Civilinė inžinerija", "Claims Adjusting, Actuarial Services": "Žalų reguliavimas, aktuarinės p<PERSON>laugos", "Classification (profession.hu)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Clay and Refractory Products Manufacturing": "<PERSON><PERSON> ir ugniai atsparių produktų gamyba", "Cleaner": "<PERSON><PERSON><PERSON><PERSON>", "Cleaner, Auxiliary Worker, Production Worker, Warehouse Worker...": "<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>, gam<PERSON><PERSON> da<PERSON>, <PERSON><PERSON><PERSON>...", "Clear": "<PERSON><PERSON><PERSON><PERSON>", "Clear all": "Išvalyti viską", "Clear all stage categories": "Išvalyti visas etapo kategorijas", "Clear filters": "Išvalyti filtrus", "Clear search": "Išvalyti paiešką", "Clear selection": "Išvalyti pasirinkimą", "Clerk": "<PERSON><PERSON><PERSON>", "Click here to read more about cNPS": "Spustelė<PERSON><PERSON>, jei norite daugiau suž<PERSON>ti apie cNPS", "Click on team or edge and press backspace/delete to delete": "Spustelėkite komandą arba kraštą ir paspauskite backspace/delete, kad ištrintumėte", "Click on the \"Register a new application\" button.": "Spustelėkite mygtuką \"Užregistruoti naują paraišką\".", "Click on the option that should remain after the merge.": "Spustelėkite parinktį, kuri turėt<PERSON> likti po sujungimo.", "Click save to apply your changes": "Spustelė<PERSON><PERSON>, kad pritaikyt<PERSON> pake<PERSON>", "Click template to preview": "Spustelėkite šabloną, kad <PERSON>tum<PERSON>", "Click to copy": "Spustelėkite norėdami nukopijuoti", "Click to insert merge tag.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , jei norite įterpti sujungimo ž<PERSON>.", "Click to reveal the value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad matytusi reikšmė", "Click to see alternatives.": "Spustelėkite norėdami perž<PERSON>ū<PERSON> alternatyvas.", "Click to see the changes.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad pama<PERSON> pake<PERSON>.", "Click to select a time for your interview. You can drag the slot around or remove it and reselect, if you wish to change the time.": "Spustelėkite ir pasirinkite pokalbio laiką. Jei norite pakeisti laiką, galite vilkti laiko intervalą arba jį pašalinti ir vėl pasirinkti.", "Clicked at": "Pa<PERSON>audė ties", "Clicks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Client": "<PERSON><PERSON><PERSON>", "Client ID": "Kliento ID", "Client Secret": "<PERSON><PERSON><PERSON>", "Client company name": "Kliento įmonės pavadinimas", "Client name": "Kliento pavadinimas", "Client secret": "<PERSON><PERSON><PERSON>", "Clients": "Klientai", "Climber": "Lai<PERSON><PERSON>jas", "Clone": "<PERSON><PERSON><PERSON>", "Clone project": "Klonuoti projektą", "Clone requisition": "Klonavimo <PERSON>", "Clone scorecard": "Klonavimo rezultatų suvestinė", "Close": "Uždaryti", "Close any other programs which might be using the webcam.": "Uždarykite visas kitas programas, kurios gali naudoti kompiuterio kamerą.", "Coal Mining": "<PERSON><PERSON> ka<PERSON>ba", "Code": "<PERSON><PERSON>", "Collaborate with hiring managers and assess candidates in a fair and equitable way.": "Bendradarbiaukite su samdymo vadovais ir objektyviai bei teisingai vertinkite kandidatus.", "Collection Agencies": "<PERSON>šieš<PERSON><PERSON><PERSON>", "Collector": "<PERSON><PERSON><PERSON><PERSON>", "Colour": "Spalva", "Colour:": "Spalva:", "Columns": "Stulpeliai", "Coming soon": "<PERSON><PERSON><PERSON>", "Comma separated IP addresses or CIDR ranges.": "Kableliais atskirti IP adresai arba CIDR intervalai.", "Comment": "Komentaras", "Comment Attachment": "Koment<PERSON><PERSON>", "Comment by {name}": "Komentaras {name}", "Comment saved!": "Komentaras i<PERSON>!", "Comments": "Komentarai", "Commercial and Industrial Equipment Rental": "Komercinės ir pramoninės įrangos nuoma", "Commercial and Industrial Machinery Maintenance": "Komercinių ir pramoninių mašinų priežiūra", "Commercial and Service Industry Machinery Manufacturing": "Prekybos ir paslaugų pramonės mašinų gamyba", "Commodity researcher": "Prekių tyrėjas", "Communications Equipment Manufacturing": "Ryšių įrangos gamyba", "Community Development and Urban Planning": "Bendruomenės plėtra ir miestų planavimas", "Community Services": "<PERSON><PERSON>ug<PERSON>", "Company": "Įmonė", "Company mail signature settings": "Įmonės pa<PERSON>to para<PERSON> nustatymai", "Company name": "Įmonės pavadinimas", "Company names": "Įmonių pavadinimai", "Company network gateway IP": "Įmonės tinklo vartų IP", "Company reasons": "Įmonės priež<PERSON>ys", "Compensation (salary + bonus):": "Atlyginimas (darbo užmokestis + premija):", "Complained at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Computer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Computer Games": "Kompiuteriniai žaidimai", "Computer Hardware Manufacturing": "Kompiuterinės įrangos gamyba", "Computer Networking Products": "Kompiuterių tinklų produktai", "Computer and Network Security": "Kompiuterių ir tinklų saugumas", "Computer technician": "Kompiuterių technikas", "Computers and Electronics Manufacturing": "Kompiuterių ir elektronikos gamyba", "Concreter": "Betonuotojas", "Condition": "Būklė", "Confectioner": "<PERSON><PERSON><PERSON><PERSON>", "Configure personally identifiable information": "Asmenį identifikuojančios informacijos konfigūravimas", "Confirm": "Patvirtinkite", "Confirm & merge": "<PERSON><PERSON><PERSON><PERSON> ir sujungti", "Confirmation SMS": "<PERSON><PERSON><PERSON><PERSON>", "Consent Info": "Sutikimo informacija", "Consent renewal": "<PERSON><PERSON><PERSON><PERSON>", "Consent renewal message": "Pranešimas apie sutikimo pratęsimą", "Consent renewal message body": "Pranešimo apie sutikimo pratęsimą tekstas", "Consent renewal message subject": "Pranešimo apie sutikimo pratęsimą tema", "Consent renewals sent!": "Išsiunčiami sutikimo pratęsimai!", "Consent subtype": "Sutikimo tipas", "Consent subtypes": "Sutikimo tipai", "Consent type": "Sutikimo tipas", "Consent types": "Sutikimo tipai", "Consent valid until": "Sutikimas galioja iki", "Consent valid until end of": "Sutikimas galioja iki", "Conservation Programs": "Išsaugojimo programos", "Consider adding categories to all stages.": "Apsvarstykite galimybę į visus etapus įtraukti kategorijas.", "Construction": "Statybos", "Construction / Real Estate": "Statyba / Nekilnojamas turtas", "Construction / Real-estate": "Statyba / Nekilnojamasis turtas", "Construction Hardware Manufacturing": "Statybinės įrangos gamyba", "Construction foreman": "Statybos darbų vadovas", "Constructor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Consultant": "Konsultantas", "Consulting": "Konsultacijos", "Consumer Goods Rental": "Vartojimo prekių nuoma", "Consumer Services": "Vartotojų paslaugos", "Contact email": "Kontaktinis el. paštas", "Contact her at {email} to get help with your first job ad.": "Kreipkitės į ją adresu {email} ir paprašykite pagalbos rengiant pirmąjį darbo skelbimą.", "Contact info": "Kontaktinė informacija", "Contact name": "<PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON> ir p<PERSON>", "Contact phone": "Kontaktinis telefonas", "Contact support to enable AI transcriptions and summaries.": "Kreipkitės į klientų aptarnavimo komandą, kad būtų įjungtos dirbtinio intelekto nuorašai ir santraukos.", "Contact support to enable AI-powered features": "Kreipkitės į k<PERSON>ų aptarnavimą, kad įjungtumėte dirbtinio intelekto v<PERSON><PERSON>", "Contact support to enable AI-powered features.": "<PERSON><PERSON><PERSON>kitės į palaikymo ta<PERSON>, kad būtų įjungtos dirbtinio intelekto valdo<PERSON>.", "Contact support to enable SCIM for your account.": "Kreipkitės į palaik<PERSON>o ta<PERSON>, kad jūs<PERSON> paskyroje būtų įjungta SCIM funkcija.", "Contact support to enable reference checks.": "Kreipkitės į palaikymo tarn<PERSON>, kad būtų įjungtas rekomendacijų tikrinimas.", "Contact support to enable transcripts for your account.": "Kreipkitės į klientų aptarnavimo komandą, kad įjungtumėte nuorašus savo paskyroje.", "Contact support to enable video interviews.": "Kreipkitės į palaikymo tarn<PERSON>, kad būtų įjungti vaizdo pokal<PERSON>i.", "Contact support to enable webhook actions.": "Susisiekite su klientų aptarnavimo komanda, kad įgalintumėte „Webhook“ veiksmus.", "Contacts": "Kontaktai", "Continue interview": "Tęsti interviu", "Continuous project (always accepting candidates)": "<PERSON><PERSON><PERSON><PERSON><PERSON> proje<PERSON> (visada priimami kandidatai)", "Contract": "<PERSON><PERSON><PERSON>", "Contract type": "Kontrak<PERSON> tip<PERSON>", "Contractor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Control who can see and edit what. Create teams and assign them to projects, job ads, and candidates.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas ką gali matyti ir redaguoti. Kurkite komandas ir priskirkite jas projekt<PERSON>, darbo skelbi<PERSON> ir kandidatams.", "Controller": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Controls": "Valdikliai", "Conversion rate": "<PERSON>n<PERSON><PERSON><PERSON>", "Convert to template": "Konvertuoti į šabloną", "Convince the candidate to respond": "Įtikinti kandidatą atsakyti", "Cook": "<PERSON><PERSON><PERSON><PERSON>", "Copied 1 candidate to selected project. | Copied {count} candidates to selected project.": "Nukopijuotas 1 kandidatas į pasirinktą projektą. | Nukopijuota kandidatų į pasirinktą projektą: {count}.", "Copied to clipboard.": "Nukopijuota į iškarpinę.", "Copied {successCount} out of {allCount} candidates. The rest were already in the chosen project.": "Nukopijuota {successCount} iš {allCount} kandidatų. Likusieji jau buvo pasirinktame projekte.", "Copied!": "Nukopijuota!", "Copy and paste the generated credentials into the fields below.": "Nukopijuokite ir įklijuokite sugeneruotus įgaliojimus į toliau esan<PERSON> lauku<PERSON>.", "Copy email address": "Nukopijuokite el. pašto ad<PERSON>ą", "Copy feed URL": "Kopijuoti srauto URL", "Copy feed slug:": "Nukopijuokite kanalo parašą:", "Copy landing URL": "Nukopijuokite puslapio URL", "Copy the number to field above": "Nukopijuokite numerį į viršuje esantį lauką", "Copy to another project": "Kopijuoti į kitą projektą", "Copy to project": "Kopijuoti į projektą", "Copywriter": "Reklaminių tekstų kūrėjas", "Cornish": "Korn<PERSON>io", "Corporation": "Korporacija", "Correctional Institutions": "Įkalinimo įstaigos", "Corrector": "<PERSON><PERSON><PERSON><PERSON>", "Corsican": "Korsikos", "Cosmetician": "Kosmetologas", "Cosmetology and Barber Schools": "Kosmetologijos ir kirpėjų mokyklos", "Cost": "<PERSON><PERSON><PERSON><PERSON>", "Cost per applicant": "<PERSON><PERSON><PERSON><PERSON> per kandidatą", "Cost per submission": "<PERSON><PERSON><PERSON> kandidato kaina", "Could not delete stage! There might audit log events or other data associated with this stage.": "Nepavyko ištrinti etapo! Su šiuo etapu gali būti susiję audito žurnalo įvykiai arba kiti duomenys.", "Could not find any stages in the selected project. Please check that you have sufficient permissions and try again.": "Nepavyko rasti jokių pasirinkto projekto etapų. <PERSON><PERSON>rinkite, ar turite reiki<PERSON>us leid<PERSON>, ir bandykite dar kartą.", "Could not remove stage": "Nepavyko p<PERSON>linti etapo", "Could not select this time. Please choose a new time.": "Šio laiko nepavyko pasirinkti. Prašome pasirinkti naują laiką.", "Count candidates": "Suskaičiuokite kandidatus", "Countdown": "<PERSON><PERSON><PERSON><PERSON>", "Countdown to application deadline": "Atgalinis skaičiavimas iki paraiškų teikimo termino pabaigos", "Country": "<PERSON><PERSON>", "Country (CVK/CVM)": "Šalis (CVK/CVM)", "Country code": "<PERSON><PERSON><PERSON> k<PERSON>", "County (CVK/CVM)": "Apskritis (CVK/CVM)", "Courier": "<PERSON><PERSON><PERSON><PERSON>", "Courts of Law": "<PERSON><PERSON><PERSON>", "Crane operator": "Krano operatorius", "Create": "<PERSON><PERSON><PERSON><PERSON>", "Create API key": "Sukurti API raktą", "Create a feed integration {here}, reload the editor and retry.": "Sukurkite srauto integra<PERSON> {here}, iš naujo įkelkite redaktorių ir bandykite dar kartą.", "Create a new form linked to this project": "Sukurti naują formą, susietą su šiuo projektu", "Create a new job requisition": "Sukurti naują darbo para<PERSON>ą", "Create a new landing page": "Sukurti naują nukreipimo puslapį", "Create a new password": "Sukurti naują slaptažodį", "Create a new project": "Sukurti naują projektą", "Create a new project failure reason": "Sukurkite naujo projekto nesėkmės priežastį", "Create a new project template": "Sukurti naują projekto šabloną", "Create a new template": "Sukurti naują <PERSON>ą", "Create a project": "Sukurti projektą", "Create a reference form": "Sukurti rekomendacijos formą", "Create a task": "Sukurti užduotį", "Create a transcript from video file...": "Sukurkite nuorašą iš video failo...", "Create a video interview": "Sukurkite vaizdo interviu", "Create an automatic form and include it on a landing page linked to this project": "Sukurti automatinę formą ir įtraukti ją į nukreipimo puslapį, susietą su šiuo projektu.", "Create an event": "Sukurti įvykį", "Create and use form": "Sukurti ir naudoti formą", "Create application forms or forms to ask candidates for additional details or feedback.": "Sukurkite paraiškos formas arba formas, kuriose kandidatų prašoma papildomos informacijos arba atsiliepimų.", "Create application forms or forms to ask candidates for additional details.": "Sukurkite paraiš<PERSON> form<PERSON>, kad gautumėte papildomos informacijos iš kandidatų.", "Create call": "Sukurti skambutį", "Create consent type": "Sukurti sutikimo tipą", "Create custom activity": "Sukurti pasirinktinę veiklą", "Create downloadable image ad": "Sukurti atsisiunčiamą paveiksl<PERSON><PERSON>", "Create dropout reason": "Su<PERSON><PERSON><PERSON> iškritimo priežastį", "Create file type": "Su<PERSON>rti failo tipą", "Create form": "Sukurti formą", "Create forms to ask internal feedback or details from other project members.": "Sukurk<PERSON> formas, kad gal<PERSON>tumėte prašyti vidinių atsiliepimų ar informacijos iš kitų projekto narių.", "Create forms to gather applicants.": "Sukurkite paraiškų rinkimo formas.", "Create interview": "Sukurti interviu", "Create job ad": "<PERSON><PERSON><PERSON><PERSON> darbo s<PERSON>", "Create key": "Sukurti raktą", "Create landing page": "Sukurti puslapį", "Create legacy page": "<PERSON><PERSON><PERSON><PERSON> kar<PERSON> puslapį", "Create location": "Sukurti vietą", "Create new": "Sukurti naują", "Create new project from template": "Sukurti naują projektą iš <PERSON>o", "Create new video interview": "Sukurti naują vaizdo interviu", "Create on-brand customisable career sites. Recruiters can publish all jobs with a single click.": "Sukurkite savo prekės ženklui pritaikytus karjeros puslapius. Samdymo specialistai gali skelbti visas darbo vietas vienu spustelėjimu.", "Create password": "Sukurti slaptažodį", "Create project": "Sukurti projektą", "Create reference form": "Sukurti rekomendacijos formą", "Create requisition": "Sukurti užklausą", "Create requisition & ask approvals": "Sukurti užklausą ir paprašyti patvirtinimų", "Create scorecard": "Sukurti vertinimo kortelę", "Create tag": "Sukurti žymą", "Create task": "Sukurti užduotį", "Create tasks": "<PERSON><PERSON><PERSON><PERSON>", "Create template": "Sukurti <PERSON>ą", "Create template from project": "Sukurti šabloną iš projekto", "Create user": "Sukurti naudotoją", "Create video call": "Sukurti vaizdo skambutį", "Create your beautiful job ad": "Sukurkite nuostabų darbo skelbimą", "Created": "Sukurta", "Created at": "<PERSON><PERSON><PERSON> ad<PERSON>", "Created on": "Sukurta", "Created task": "<PERSON><PERSON><PERSON>", "Created!": "<PERSON><PERSON><PERSON>!", "Creating a call will send your users and candidates the video call link.": "<PERSON><PERSON><PERSON><PERSON> skambutį, naudotojams ir kandidatams bus išsiųsta vaizdo skambučio nuoroda.", "Creative": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Creative type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Creatives": "<PERSON><PERSON><PERSON><PERSON>", "Credit Intermediation": "<PERSON><PERSON><PERSON>", "Cree": "Krykai", "Croatian": "<PERSON><PERSON><PERSON><PERSON>", "Crop": "Pasėliai", "Crop & save": "Apdoroti ir iš<PERSON>i", "Croupier": "Krupjė", "Culture / Entertainment": "Kultūra / Pramogos", "Culture / Entertainment / Recreation": "Ku<PERSON><PERSON>ra / Pramogos / Poilsis", "Current month": "<PERSON><PERSON><PERSON> m<PERSON>", "Current role": "<PERSON><PERSON><PERSON> v<PERSON>", "Current team": "<PERSON><PERSON><PERSON>", "Current value:": "<PERSON><PERSON><PERSON><PERSON> vertė:", "Cusotm fonts": "Pasirinktiniai <PERSON>", "Custom CSS": "Pasirinktinis CSS", "Custom Domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom URL": "Pasirinktinis URL adresas", "Custom activities": "<PERSON><PERSON><PERSON><PERSON><PERSON> veikla", "Custom activity key": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON> r<PERSON>", "Custom categories must be in the same order as system categories": "Pasirinktinės kategorijos turi būti išdėstytos ta pačia tvarka kaip ir sistemos kategorijos.", "Custom description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom field filters": "Pasirinktiniai laukų filtrai", "Custom field:": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>:", "Custom fields": "Pasirinktiniai laukai", "Custom fonts": "Pasirinktiniai <PERSON>", "Custom fonts can be used in landings and forms. Click the button below to get started.": "<PERSON><PERSON><PERSON><PERSON><PERSON> š<PERSON>us galima naudoti puslapiuose ir formose. Spustelėkite toliau esantį mygtuką ir pradėkite.", "Custom option value": "Pasirinktinės parinkties vertė", "Custom range": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom signature": "<PERSON><PERSON><PERSON>", "Custom stage categories": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sce<PERSON> ka<PERSON>", "Customer Service": "Klientų aptarnavimas", "Customer Services": "Klientų aptarnavimas", "Customer service/Services": "Klientų aptarnavimas / paslaugos", "Customize": "Pritaikyti", "Cutlery and Handtool Manufacturing": "Stalo įrankių ir rankinių įrankių gamyba", "Czech": "Čekų", "DEI: Inclusive language check": "DEI: įtraukiosios kalbos patikra", "Daily": "Kasdien", "Daily budget (€)": "Dienos biudžetas (€)", "Dairy Product Manufacturing": "Pieno produktų gamyba", "Dance Companies": "Šokių kompanijos", "Dancer": "<PERSON><PERSON><PERSON><PERSON>", "Danish": "Danų", "Darker": "<PERSON><PERSON><PERSON>", "Data Infrastructure and Analytics": "Duomenų infrastruktūra ir analizė", "Data Processing Agreement": "Duomenų tvarkymo sutartis", "Data Security Software Products": "Duomen<PERSON> saugumo programinės įrangos produktai", "Data from public sources": "Duomenys i<PERSON> v<PERSON> šaltinių", "Data processing consent automation": "Duomenų tvarkymo sutikimo automatizavimas", "Data protection specialist": "Duomenų apsaugos specialistas", "Date": "Data", "Date (calendar)": "Data (kalendorius)", "Date and time format": "<PERSON><PERSON> ir laiko formatas", "Datetime": "Datavi<PERSON>", "Day": "<PERSON><PERSON>", "Days": "<PERSON><PERSON>", "Deactivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Deactivate and reassign": "Deaktyvuoti ir priskirti iš naujo", "Deactivate user {userName}": "Deaktyvuoti naudotoją {userName}", "Deactivate without reassigning": "Deaktyvuoti be priskyrimo iš naujo", "Deactivating...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "Deadline": "Terminas", "Deadline not set": "<PERSON><PERSON><PERSON>", "Deadline: :deadline": "Galutinis terminas: :deadline", "Deadline: {date}": "Galutinis terminas: {date}", "Debug": "<PERSON><PERSON><PERSON>", "Debug report for {integrationName}": "{integrationName} derinimo atask<PERSON>a", "December": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Decision pending": "<PERSON><PERSON><PERSON><PERSON><PERSON> dar ne<PERSON><PERSON>", "Declarant": "Pareiškėjas", "Decrease Level": "Sumažinti lygį", "Default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default data processing consent duration": "Numatytoji duomenų tvarkymo sutikimo trukmė", "Default data processing consent request validity duration": "Numatytoji duomenų tvarkymo sutikimo prašymo galiojimo trukmė", "Default language": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Default phone country code": "Numatytasis telefono šalies kodas", "Default stages": "Standartiniai etapai", "Default survey": "Standartin<PERSON> a<PERSON>kla<PERSON>", "Default survey will use the question defined under Organization settings. If you wish to send a form with more questions, create a new Survey form.": "Standartinė apklausa naudos klaus<PERSON>, apibr<PERSON>ž<PERSON>ą Organizacijos nustatymų skiltyje. Jei norite siųsti formą su daugiau klausimų, sukurkite naują apklausos formą.", "Defense and Space Manufacturing": "Gynybos ir kosmoso gam<PERSON>ba", "Define a new font": "Nustatyti naują šriftą", "Define new font": "Nustatyti naują šriftą", "Delay": "Atidėjimas", "Delay sending": "Vėlavimas si<PERSON>i", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Delete chat": "<PERSON><PERSON><PERSON><PERSON> pokalbį", "Delete comment": "Ištrinti komentarą", "Delete file": "<PERSON><PERSON><PERSON><PERSON>", "Delete filter": "<PERSON><PERSON><PERSON><PERSON> filtrą", "Delete permanently": "<PERSON><PERSON> la<PERSON>", "Delete project": "Iš<PERSON>nti projektą", "Delete stage": "Ištrinti etapą", "Delete template": "<PERSON><PERSON><PERSON><PERSON>", "Delete video only": "<PERSON>štrinti tik vaizdo įrašą", "Deleted": "<PERSON>š<PERSON>nta", "Deleting a client cannot be undone.": "Kliento p<PERSON><PERSON><PERSON><PERSON> atšaukti negalima.", "Deliver via": "<PERSON><PERSON><PERSON><PERSON><PERSON> per", "Delivered at": "Pristato<PERSON> ad<PERSON>", "Dentists": "Odontologai", "Department": "Departamentas", "Department Management": "Departamento valdymas", "Department:": "Skyrius:", "Departments (cv.lt)": "Departamentai (cv.lt)", "Depending on your specific circumstances, this can be totally fine or might need some action.": "Priklausomai nuo konkrečių aplinkybių, tai gali būti visiškai priimtina arba gali reikėti imtis tam tikrų veiksmų.", "Description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Description section title": "<PERSON><PERSON><PERSON><PERSON><PERSON>us pavadin<PERSON>s", "Descriptive page name": "<PERSON><PERSON>š<PERSON><PERSON> pusla<PERSON> pavadin<PERSON>s", "Design": "<PERSON><PERSON><PERSON>", "Design Services": "<PERSON>jekt<PERSON><PERSON>", "Design/architecture": "Dizainas / architektūra", "Designer": "<PERSON><PERSON><PERSON><PERSON>", "Desired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Desktop Computing Software Products": "Stalinių kompiuterių programinės įrangos produktai", "Desktop View": "Darbalaukio v<PERSON>zda<PERSON>", "Director": "<PERSON><PERSON><PERSON><PERSON>", "Disability Confident employer": "Neįgalumu pasiti<PERSON>tis darb<PERSON>s", "Disability group": "Neįgalumo grupė", "Disability group from date": "Neįgalumo grupė nuo datos", "Disability group to date": "<PERSON><PERSON> ve<PERSON>anti neįgaliųjų grupė", "Disable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disable activity notifications": "Veiklos p<PERSON> išjungimas", "Disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disciple": "<PERSON><PERSON><PERSON>", "Dishwasher": "Indų plovėjas", "Dismiss": "<PERSON><PERSON><PERSON>", "Dispatcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Display location": "<PERSON><PERSON><PERSON> vieta", "Display name": "<PERSON><PERSON><PERSON>", "Display type": "<PERSON><PERSON><PERSON>", "Displayed on career page preview.": "Rodoma karjer<PERSON> pu<PERSON>.", "Displayed when sharing page in social media.": "<PERSON><PERSON> dali<PERSON>tis puslapiu socialinėje žiniasklaidoje.", "Dispute & invalid": "Gin<PERSON><PERSON> ir negal<PERSON>", "Dispute months": "<PERSON><PERSON><PERSON><PERSON>", "Dispute resolution only": "Tik ginčų sprendimas", "Dispute resolution only.": "Tik ginčų sprendimas.", "Distance from {locationName} ({locationAddress})": "Atstumas nuo {locationName} ({locationAddress})", "Distilleries": "<PERSON>st<PERSON><PERSON><PERSON>", "Distribution": "<PERSON><PERSON><PERSON><PERSON>", "Divehi, Dhivehi, Maldivian": "Divehi, Dhivehi, Maldyvų", "Do a fact-check - find any instances where incorrect facts were presented. Do not highlight correct facts.": "Atlikite faktų patikrinimą - raskite visus atvejus, kai buvo pateikti neteisingi faktai. Neišskirkite teisingų faktų.", "Do not infer or assume any information that has not been explicitly said in the transcript.": "Nedarykite išvadų ir neįterpkite jokios informacijos, kuri nebuvo aiškiai nurodyta nuoraše.", "Do you want to {knowMoreCTA} about your interviews automatically?": "Ar norite {knowMoreCTA} apie jūsų pokalbius automatiškai?", "Do you want to {knowMoreCTA} without spending hours on interviewing?": "Ar norite rasti {knowMoreCTA}, nesugaišdami valandų pokalbiams?", "Docker": "Dokininkas", "Doctor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Doctor assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Doctor's degree": "<PERSON><PERSON><PERSON>", "Documentation": "Dokumentacija", "Domain": "<PERSON><PERSON>", "Domain must be a valid domain": "<PERSON><PERSON> turi b<PERSON><PERSON>", "Don't worry if you don't have the time, will or skills to choose photos.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jei <PERSON>urite <PERSON>, noro ar įgūdžių atrinkti nuotra<PERSON>.", "Don't worry if you don't have the time, will or skills to choose videos.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jei <PERSON>urite la<PERSON>, noro ar įgūdžių pasirinkti vaizdo įrašą.", "Double-click to edit": "Du<PERSON>t spustelėkite norėdami redaguoti", "Down for maintenance": "Neveikia dėl techninės priež<PERSON>", "Download": "Atsisiųsti", "Download as PDF": "Atsisiųsti ka<PERSON>", "Download as image": "Atsisiųsti kaip paveikslėlį", "Download candidates report": "Atsisiųsti kandidatų ataskaitą", "Download custom activity report": "Atsisiųsti pasirinktinę veiklos ataskaitą", "Download monthly hiring report": "Atsisiųskite mėnesinę atrankų ataskaitą", "Download project status report": "Atsisiųsti projekto būklės ataskaitą", "Download recording": "Atsisiųsti įrašą", "Download submissions": "Atsisiųsti paraiškas", "Downloadable image": "Atsisiunčiamas vaizda<PERSON>", "Downloaded on {date}": "At<PERSON>iųsta {date}", "Downloading report": "Ataiskai<PERSON> si<PERSON>", "Downloading report...": "Ataiskaita si<PERSON>č<PERSON>...", "Draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Draft saved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Drag and drop CVs onto the project board": "Pele vilkite CV į projekto lentą", "Drag and drop to add attachments": "Užveskite ir palikite, kad p<PERSON><PERSON><PERSON> priedus", "Drag whitespace to move graph": "Vilkite baltąjį tarpą, kad perkel<PERSON>te grafiką", "Dragged from mailbox": "Ištraukta iš paš<PERSON>", "Driver": "Vairuotojas", "Driver's license (profession.hu)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Drivers license": "Vairuotojo p<PERSON>ž<PERSON>jimas", "Driving": "<PERSON><PERSON><PERSON><PERSON>", "Drop files here": "Įmeskite failus čia", "Dropout": "Iškritimas", "Dropout reason :reasonName can not be deleted because it is still linked to :count applications": "Iškritimo priežasties :<PERSON><PERSON><PERSON>, nes ji vis dar susieta su :count kandida<PERSON><PERSON><PERSON>", "Dropout reason added.": "Pridėta iškritimo priežastis.", "Dropout reason deleted successfully.": "Iškritimo priežastis sėkmingai ištrinta.", "Dropout reason was deleted.": "Iškritimo priežastis buvo ištrinta.", "Dropout reason {originalDropoutReason} will be deleted.": "Iškritimo priežastis {originalDropoutReason} bus ištrinta.", "Dropout reasons": "<PERSON>š<PERSON><PERSON><PERSON>", "Dropout reasons merged successfully.": "Iškritimo priežastys sėkmingai sujungtos.", "Dropout reasons were merged.": "Iškritimo p<PERSON>ž<PERSON>ys buvo sujungtos.", "Dropped out": "Iškrito", "Dry run (deliver debug info via email)": "<PERSON><PERSON><PERSON> paleidima<PERSON> (informaciją pateikite el. paštu)", "Dry run: candidate sent to :integrationName": "Bandomasis paleidimas: kandidatas išsiųstas į :integrationName", "Duplicate": "Dublikatas", "Duration (min)": "<PERSON><PERSON><PERSON><PERSON> (min)", "Duration in days": "<PERSON>ruk<PERSON><PERSON>", "Duration in months": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Dutch": "Olandų kalba", "Dutch, Flemish": "olandų, flamandų", "Duunitori.fi": "Duunitori.fi", "Dzongkha": "Dzongkha", "E-Learning Providers": "<PERSON><PERSON> m<PERSON> paslaugų teikėjai", "E-mail": "El. <PERSON>", "E-mail address of the Indeed account used to manage this integration": "\"Indeed\" <PERSON><PERSON><PERSON><PERSON>, na<PERSON><PERSON><PERSON> integra<PERSON> vald<PERSON>, el. p<PERSON><PERSON><PERSON> ad<PERSON>as", "E-mail notifications are currently turned off. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn on.": "Šiuo metu el. pašto pranešimai yra išjungti. Elektroniniai pranešimai siunčiami darbo dieno<PERSON> 8:00, 13:00 ir 16:00 val. <PERSON><PERSON><PERSON><PERSON> įjungti, spustelėkite čia.", "Earliest day to offer?": "Anksčiausia siūloma diena?", "Eastern Europe": "Rytų Europa", "Easy Apply": "<PERSON><PERSON><PERSON>", "Easy Apply button text": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON>g<PERSON>\" tekstas", "Easy Apply disclaimer": "<PERSON><PERSON><PERSON>", "Easy Apply fallback option text": "<PERSON><PERSON><PERSON> at<PERSON>gin<PERSON> tekstas", "Economic Programs": "Ekonominės programos", "Economist": "Ekonomistas", "Edit": "Red<PERSON><PERSON><PERSON>", "Edit API key": "Redaguoti API raktą", "Edit action": "Radaguoti veiksmą", "Edit action {actionName}": "Redaguoti veiksmą {actionName}", "Edit actions and imports": "Redaguoti veiksmus ir importą", "Edit authentication provider": "Redaguoti autentifikavimo paslaugų teikėją", "Edit candidate": "Redaguoti kandidatą", "Edit candidate referee {refereeName}": "Redaguoti kandidato rekomendacijos te<PERSON> {referee<PERSON>ame}", "Edit client": "Redaguoti klientą", "Edit consent type": "Redaguoti sutikimo tipą", "Edit contact": "Redaguoti kontaktą", "Edit custom activity": "Redaguoti pasirinktinę veiklą", "Edit dropout reason": "Redaguoti iškritimo priežastį", "Edit file type": "Redaguoti failo tipą", "Edit form": "Redaguoti formą", "Edit import": "Redaguoti importavimą", "Edit integration": "Redaguoti integraciją", "Edit interview": "Redaguoti interviu", "Edit job ad": "Redaguoti darbo skel<PERSON>", "Edit location": "Redaguoti vietą", "Edit office": "Redaguot<PERSON> bi<PERSON>", "Edit project": "Redaguoti projektą", "Edit project scorecards": "Redaguoti projekto vertinimo korteles", "Edit reference form": "Redaguoti rekomendacijos formą", "Edit requisition": "Redaguoti paraišką", "Edit scorecard": "Redaguoti vertinimo kortelę", "Edit stage :stage in :project": "Redaguoti etapą :stage esame :project", "Edit structured job ad": "Redaguoti struktūrizuotą darbo skelbimą", "Edit tag": "Redaguoti žymą", "Edit teams hierarchy": "Redaguoti komandų hierarchiją", "Edit template": "Redaguoti šabloną", "Edit user": "Redaguoti naudotoją", "Edit video interview": "Redaguoti vaizdo interviu", "Editable text with custom background image": "Redaguojamas tekstas su pasirinktiniu fono paveikslėliu", "Editable text with responsive image on one side. Configurable orientation.": "Redaguojamas tekstas su reaguojančiu paveikslėliu vienoje pu<PERSON>ėje. Konfigūruojama orientacija.", "Editable text with responsive video on one side. Configurable orientation.": "Redaguojamas tekstas su reaguojančiu vaizdo įrašu vienoje pusėje. Konfigūruojama orientacija.", "Editable text with square image on one side. Configurable orientation.": "Redaguojamas tekstas su kvadratiniu paveikslėliu vienoje pusėje. Konfigūruojama orientacija.", "Editing uploaded candidate {candidateIndex} of {candidateTotal}": "Redaguojamas įkeltas kandidatas {candidateIndex} iš {candidateTotal}", "Editor": "<PERSON><PERSON><PERSON><PERSON>", "Education": "Švietimas", "Education (SS.lv)": "Š<PERSON><PERSON><PERSON> (SS.lv)", "Education / Science": "Švietimas / Mokslas", "Education / Science / Research": "Švietimas / Mo<PERSON>las / Moksliniai t<PERSON>imai", "Education Administration Programs": "Švietimo administravimo programos", "Education history": "Švietimo istorija", "Education requirement (profession.hu)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Educator": "Pedagogas", "Either an e-mail or a phone number is required for each referee.": "B<PERSON>tina nurodyti kiekvieno rekomendacijos davėjo el. pašto adresą arba telefono numerį.", "Electric Lighting Equipment Manufacturing": "Elektros apšvietimo įrangos gamyba", "Electric Power Generation": "Elektros energijos gamyba", "Electric Power Transmission, Control, and Distribution": "Elektros energijos perda<PERSON>, vald<PERSON><PERSON> ir p<PERSON>mas", "Electric welder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Electrical Equipment Manufacturing": "Elektros įrangos gamyba", "Electrical/Telecoms": "Elektros ir telekomunikacijų", "Electrician": "Elektrikas", "Electromechanics": "Elektromechanika", "Electronic and Precision Equipment Maintenance": "Elektroninės ir tik<PERSON>liosio<PERSON> įrangos priežiūra", "Electronics": "Elektronika", "Electronics / Telecom": "Elektronika / Telekomunikacijos", "Electronics / Telecommunication": "Elektronika / Telekomunikacijos", "Email": "El. <PERSON>", "Email Message": "<PERSON><PERSON> <PERSON><PERSON>", "Email address is required in integration settings": "Integravimo nustatymuose b<PERSON><PERSON>s el. pašto adresas", "Email message conversions": "El. laiškų konversijos", "Email service provider": "El. pa<PERSON><PERSON> p<PERSON> teik<PERSON>", "Emails": "Elektroniniai laiškai", "Embedded Software Products": "Įterptosios programinės įrangos produktai", "Emergency and Relief Services": "<PERSON><PERSON><PERSON><PERSON> pagal<PERSON> paslaugos", "Emergency contact phone and name": "Kontaktinis telefonas ir vardas ir pavard<PERSON>", "Emojis not found.": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON>.", "Employee forms": "Darbuotojų formos", "Employer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Employer ID": "Darbdavio ID", "Employer brand video": "Darbdavio pre<PERSON> ženk<PERSON> vaiz<PERSON> įrašas", "Employer brand video URL": "Darbdavio prekės ženklo vaizdo įrašo URL", "Employer info": "Darbdavio informacija", "Employer web URL": "Darbdavio interneto svetainės URL", "Employment - employer name": "Įdarbinimas - da<PERSON><PERSON><PERSON> pava<PERSON>", "Employment - job title": "Įdarbinimas - pareigų pavadinimas", "Employment Start Date": "Įdarbinimo pradžios data", "Employment history": "Įdarbinimo istorija", "Employment type": "Įdarbinimo tipas", "Employments": "Darbo vietos", "Empty": "<PERSON><PERSON><PERSON><PERSON>", "Enable": "Įjungti", "Enable \"Send as user\" for messages": "Įjungti \"Siųsti kaip naudo<PERSON>\" pranešimams", "Enable Easy Apply": "Įjungti \"<PERSON>g<PERSON>\"", "Enable GDPR automation": "Įjungti GDPR automatizavimą", "Enable click tracking": "Įjungti paspaudimų sekimą", "Enable diversity reporting": "Įjungti įvairovės ataskaitų teikimą", "Enable integration": "Įjungti integraciją", "Enable permanent delete": "Įjungti visišką ištrynimą", "Enable this if you are sending non-critical content.": "Įjunkite šią <PERSON>, jei <PERSON> nekritinį turinį.", "Enable this if you have more than 100 rooms. Will add Place.Read.All permission.": "Įjunkite <PERSON>i<PERSON>, jei turite daugiau nei 100 kambarių. Bus pridėtas Place.Read.All leidimas.", "Enable this if you want AI-powered transcriptions and summaries from meetings. Requires permissions for your events and recordings.": "Įjunkite <PERSON>i<PERSON>, jei norite gauti dirbtinio intelekto sugeneruotą susitikimų nuorašą ir santrauką. Reikalingi jūsų susitikimų ir įrašų leidimai.", "Enable this if you want AI-powered transcriptions and summaries from meetings. This will cause meeting created through Teamdash to automatically start recording. Requires permissions for your events and recordings.": "Įjunkite ši<PERSON>, jei norite gauti dirbtinio intelekto sukurtus susitikimų nuorašus ir santraukas. Dėl to per \"Teamdash\" sukurtas susitikimas bus automatiškai pradėtas įrašinėti. Reikalingi jūsų leidimai susitikimams ir įrašams.", "Enable this to store video recordings in Teamdash.": "Įjunkite <PERSON>ią <PERSON>, kad vaizdo įrašai būtų saugomi Teamdash platformoje.", "Enable {gdprautomation} to never worry about it again.": "Įjunkite {gdprautomation} ir daug<PERSON>u ni<PERSON> d<PERSON> to nesijaudinkite.", "Enabled": "Įjungta", "Encryption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "End": "Pabaiga", "End date": "Galutinė data", "End month": "Mėnesio pabaiga", "End year": "Metų pabaiga", "Energetics / Electricity": "Energetika / Elektra", "Energetics / Natural resources": "Energetika / Gamtos <PERSON>i", "Energetics/electronics": "Energetika / elektronika", "Enforce limits": "Įgyvendinti apribojimus", "Enforcing limits will automatically submit the final attempt and end the video recording when the time limit is reached. By not enforcing, candidates are advised on the limits. but are not restricted from exceeding them. You will be shown if attempts and/or time limits have been exceeded.": "Priėmus apriboji<PERSON>, bus automatiškai pateiktas paskutinis bandymas, o vaizdo įrašymas bus baigtas, kai pasiba<PERSON> laikas. Neįgyvendinus, kandidatai informuojami apie ribas. tačiau nėra ribojama jų viršyti. Jums bus <PERSON>ta, jei bandymai ir (arba) laiko limitai buvo viršyti.", "Engineer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Engineering": "Inžinerija", "Engineering/mechanics": "Inžinerija / mechanika", "Engines and Power Transmission Equipment Manufacturing": "Variklių ir galios perdavimo įrangos gamyba", "English": "Anglų kalba", "English (UK)": "<PERSON><PERSON><PERSON> kal<PERSON> (Jungtinė Karalystė)", "English (US)": "<PERSON><PERSON><PERSON> kal<PERSON> (JAV)", "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to \"Allow\".": "Užtikrinkite naršyklės prieigą prie interneto kameros. Spustelėkite adreso juostoje esančią užrakto piktogramą ir nustatykite kameros ir mikrofono funkciją \"Leisti\".", "Ensure browser access to webcam. Click the lock-icon on address bar and set camera and microphone to &quot;Allow&quot;.": "Užtikrinkite naršyklės prieigą prie internetinės kameros. Spustelėkite užrakto piktogramą adreso juostoje ir nustatykite kamerą bei mikrofoną į &quot; Leisti &quot;.", "Enter \"Teamdash\" as the application name and add a short description (e.g. \"Teamdash recruitment software\").": "Kaip programos pavadinimą įveskite \"Teamdash\" ir pridėkite trumpą apra<PERSON> (pvz., \"Teamdash\" įdarbinimo programinė įranga).", "Enter a URL…": "Įveskite URL adresą...", "Enter a new reason...": "Įveskite naują priežastį...", "Enter position": "Įveskite poziciją", "Enter reference manually": "Įveskite rekomenda<PERSON><PERSON><PERSON> rankiniu būdu", "Enter warranty date if applicable": "Įveskite garan<PERSON><PERSON> dat<PERSON>, jei ta<PERSON>ma", "Enterprise": "Įmonė", "Entertainment Providers": "Pramogų teikėjai", "Entry level": "Pradinis lygis", "Environmental Quality Programs": "Aplinkos kokybės programos", "Environmental Services": "Aplinkosaug<PERSON> paslau<PERSON>", "Equipment Rental Services": "Įrangos nuomos paslaugos", "Error": "<PERSON><PERSON><PERSON>", "Error when scheduling an interview (:interviewTitle)": "<PERSON><PERSON><PERSON> (:interview<PERSON><PERSON><PERSON>) darbo pokalbį", "Error:": "<PERSON>laid<PERSON>:", "Esperanto": "Esperanto", "Essential": "<PERSON><PERSON><PERSON><PERSON>", "Estimator": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Estonia": "Estija", "Estonian": "Estų kalba", "Event": "Įvykis", "Event :title on :start (:tz) has been cancelled.": "Įvykis :title suplanuotas :start (:tz) atšauktas.", "Event :title on :start has been cancelled.": "Susitikimas :title on :start atšauktas.", "Event :title scheduled for :start": "Susitikimas :title numatytas :start", "Event :title scheduled for :start (:tz)": "Įvykis :title suplanuotas :start (:tz)", "Event [event_title] scheduled for [event_start]": "Įvykis [event_title] suplanuotas [event_start]", "Event scheduled": "Suplanuotas susitikimas", "Event updated successfully!": "Susitikimas sėkmingai atnaujintas!", "Events Services": "Renginių paslaugos", "Everyone who is currently using this key will no longer be able to access the API. This may break the connection between job portals and your Teamdash instance!": "<PERSON><PERSON>, k<PERSON><PERSON>o metu naudoja šį raktą, nebegal<PERSON>s pasiekti API. Tai gali nutraukti ryšį tarp darbo skelbimų portalų ir Jūsų Teamdash paskyros!", "Everything related to project stages can now be edited directly on the candidates board.": "<PERSON><PERSON><PERSON>, kas susiję su projekto etapais, dabar galima redaguoti tiesiogiai kandidatų lentoje.", "Ewe": "<PERSON><PERSON><PERSON>", "Excavator operator": "Ekskavatoriaus operatorius", "Excellent": "<PERSON><PERSON><PERSON>", "Exclude": "<PERSON>š<PERSON><PERSON>", "Exclude continuous projects": "Neįtraukti tęstinių projektų", "Executive": "Vykdomasis", "Executive Offices": "Vykdomieji <PERSON>", "Executive Search Services": "Vadovų paieškos paslaugos", "Executive level": "Vadovo lygmuo", "Exit": "<PERSON><PERSON><PERSON><PERSON>", "Expand": "Išplėsti", "Expand column": "Išskleisti stulpelį", "Expected role": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "Expected team": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Experience (profession.hu)": "<PERSON><PERSON><PERSON> (profession.hu)", "Experience level": "Patirties lygis", "Expired": "Pasibaigęs", "Expired at": "<PERSON><PERSON><PERSON>", "Expires at": "<PERSON><PERSON><PERSON> iki", "Expiring soon": "<PERSON><PERSON><PERSON> nust<PERSON>i", "Expiry period": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "Expiry period unit": "Galiojimo laiko vienetas", "Export": "Eksportuoti", "Export an ad to a job portal": "Eksportuoti skelbimą į darbo skelbimų portalą", "Export candidates": "Eksportuo<PERSON> kandidatus", "Export candidates to .xlsx": "Kandidatų eksportavimas į .xlsx", "Exported by {exporter} ({exporterEmail})": "Eksportavo {exporter} ( {exporterEmail} )", "Exports": "Eksportas", "Exports for {positionName}": "Eksportas {positionName}", "Extract details with AI": "Gaukite detales naudodami dirbtinį intelektą", "FTE": "Etatas", "Fabricated Metal Products": "Metalo gami<PERSON>i", "Facilities Services": "<PERSON><PERSON><PERSON><PERSON>, susi<PERSON><PERSON><PERSON> su įrenginiais", "Fact-check": "Faktų patikrinimas", "Failed to copy.": "Nepavyko nukop<PERSON>i.", "Failed to create transcription.": "Nepavyko sukurti nuorašo.", "Failed to deactivate user. Please try again later.": "Nepavyko deak<PERSON>oti naudotojo. Prašome pabandyti vėliau.", "Failed to delete project failure reason.": "Nepavyko ištrinti projekto nesėkmės priežasties.", "Failed to load available rooms.": "Nepavyko įkelti laisvų kambarių.", "Failed to parse the candidate's CV.": "Nepavyko išanalizuoti kandidato CV.", "Failed to resend invite. Please try again later.": "Nepavyko pakartotinai išsiųsti kvietimo. Prašome pabandyti vėliau.", "Failed to retry transcription.": "Nepavyko pakartoti nuorašo.", "Failed to save rating.": "Nepavyko iš<PERSON>ugoti įvertinimo.", "Failed to save settings.": "Nepavyko išsaugoti nustatymų.", "Failed to send candidate to :integrationName": "Nepavyko per<PERSON>i kandidato į :integrationName", "Failed to send unpublish notification: :message": "Nepavyko išsiųsti pranešimo apie paskelbimo atšaukimą: :message", "Failed to start recording. Please refresh the page.": "Nepavyko <PERSON> įrašymo. Atnaujinkite puslapį.", "Failed to submit answer.": "Nepavyko pateikti atsakymo.", "Failed to upload recording. Please refresh the page.": "Nepavyko įkelti įrašo. Atnaujinkite puslapį.", "Failed:": "Nepavyko:", "Failure reason": "Nesėkmė<PERSON> p<PERSON>", "Failure to find a suitable candidate": "Nepavyko rasti tinkamo kandidato", "Failure type": "Nesėkmės tip<PERSON>", "Fair": "S<PERSON>žining<PERSON>", "Fair evaluations": "Sąžiningi vertinimai", "Family Planning Centers": "Šeimos planavimo centrai", "Farming": "Ūkininkavimas", "Farming, Ranching, Forestry": "Ūkininkavimas, <PERSON><PERSON><PERSON><PERSON>, miškininkystė", "Faroese": "Farerų salos", "Fashion": "<PERSON><PERSON>", "Fashion Accessories Manufacturing": "Mados aksesuarų gamyba", "Favicon is the small icon that appears in the browser tab on your landing pages.": "Favicon yra ma<PERSON>a p<PERSON>, kuri rodoma puslapių naršyk<PERSON>.", "February": "<PERSON><PERSON><PERSON>", "Feed": "<PERSON><PERSON><PERSON>", "Feed URL": "<PERSON><PERSON><PERSON> n<PERSON>a", "Feedback form": "Atsiliepimų forma", "Feel free to tweak filters and double-check you have access to the project.": "<PERSON><PERSON><PERSON> keisti filtrus ir dar kartą patik<PERSON>, ar turite prieigą prie projekto.", "Female": "<PERSON><PERSON><PERSON>", "Fetch video from Microsoft Teams": "Gaukite vaizdo įrašą iš „Microsoft Teams“.", "Field": "<PERSON><PERSON>", "Field MUST include [:mustIncludeTags] :mustIncludeCondition.": "Laukas TURI apimti [:mustIncludeTags] :mustIncludeCondition.", "Field MUST include [:mustIncludeTags].": "<PERSON><PERSON> būti [:mustIncludeTags].", "Field can be empty": "Laukas gali bū<PERSON> t<PERSON>", "Field label": "Srities etiketė", "Field must be filled": "Lau<PERSON> turi b<PERSON><PERSON>", "Field must be valid url": "Lau<PERSON> turi b<PERSON>ti <PERSON> url", "Field type": "<PERSON><PERSON>", "Fields": "Laukai", "Fijian": "<PERSON><PERSON><PERSON><PERSON>", "File (legacy version)": "F<PERSON>as (senoji versija)", "File Types": "Failų tipai", "File URL": "Failo URL adresas", "File must contain columns: name, email, role (admin, regular, limited). All added users will receive an email to create a password for their account.": "Faile turi būti š<PERSON> stulpeliai: var<PERSON>, el. <PERSON><PERSON>, v<PERSON><PERSON><PERSON> (administratorius, įprastas, ribotas). Visi pridėti naudotojai gaus el. <PERSON>, kuriame bus sukurtas jų paskyros slap<PERSON>žodis.", "File size is over the 10MB limit.": "Failo dydis viršija 10 MB ribą.", "File type": "Failo tipas", "File type not supported": "Nepalaikomas failo tipas", "File types": "Failo tipai", "File uploader": "Failų įkėlimo programa", "Files": "<PERSON><PERSON><PERSON>", "Fill a form": "Užpildyti formą", "Fill text fields from landing": "Užpildykite teksto la<PERSON> i<PERSON> tinkla<PERSON>", "Filling this field": "<PERSON><PERSON> la<PERSON> p<PERSON>", "Filter set name": "Filtrų rinkinio pavadinimas", "Filters": "Filtrai", "Filters saved!": "Filtrai išsaugoti!", "Finance": "Fin<PERSON><PERSON>", "Finance / Accounting": "Finansai / apskaita", "Finance/accounting/banking": "Finansai / apskaita / bankininkystė", "Financial Services": "Finansin<PERSON><PERSON> p<PERSON>", "Financial analyst": "Finansų analitikas", "Find candidates": "Ieškokite kandidatų", "Find candidates similar to": "Ieškoti kandidatų, panašių į", "Fine Arts Schools": "<PERSON><PERSON><PERSON><PERSON>", "Finish interview": "Baigti interviu", "Finish uploading candidates": "Baigti įkelti kandidatus", "Finished projects": "Baigti projektai", "Finished projects + time to fill": "Baigti projektai + laikas iki įdarbinimo", "Finished projects + time to hire": "Baigti projektai + laikas iki įdarbinimo", "Finland": "Suomija", "Finnish": "<PERSON><PERSON><PERSON>", "Fire Protection": "Prieš<PERSON><PERSON><PERSON><PERSON> a<PERSON>auga", "First application date": "Pirmosios paraiškos data", "First name": "Vardas", "Fish processor": "<PERSON><PERSON><PERSON>", "Fisheries": "Žuvininkystė", "Fisherman": "Ž<PERSON><PERSON><PERSON>", "Fix": "<PERSON><PERSON><PERSON><PERSON>", "Fix grammar and spelling": "Pataisyti gramatiką ir rašybą", "Fix integration": "Pataisyti integraciją", "Fixed background when scrolling": "Pataisytas fonas slinkimo metu", "Fixed term": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>", "Flight Training": "Skrydžių mokymas", "Florist": "Floristas", "Font": "<PERSON><PERSON><PERSON>", "Font file (bold style)": "<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON> stilius)", "Font file (combined bold-and-italic style)": "<PERSON><PERSON><PERSON> (paryškintas ir kursyvinis stil<PERSON>)", "Font file (italic style)": "<PERSON><PERSON><PERSON> (k<PERSON><PERSON><PERSON><PERSON> s<PERSON>)", "Font file (normal style)": "<PERSON><PERSON><PERSON> (įprastas stilius)", "Font name": "Šrifto pavadinimas", "Font:": "Šriftas:", "Food Industry": "<PERSON><PERSON>", "Food and Beverage Manufacturing": "Maisto produktų ir gėrimų gamyba", "Food and Beverage Retail": "Mažmeninė prekyba maisto produktais ir g<PERSON>rimais", "Food and Beverage Services": "Maisto ir gėrimų paslaugos", "Footwear Manufacturing": "Avalynė<PERSON> gam<PERSON>", "Footwear and Leather Goods Repair": "Avalynės ir odos dirbini<PERSON> taisymas", "Footwear/Accessories": "Avalynė / priedai", "For conformance with the GDPR, please follow these guidelines for the request message. For more information, consult your DPO, Teamdash support or a lawyer.": "Siekdami laikytis BDAR reikalavimų, laikykit<PERSON><PERSON> šių užklausos pranešimo gairių. Jei norite gauti daugiau informacijos, kreipkitės į savo DPO, \"Teamdash\" komandą arba teisininką.", "For example an email or a screenshot to prove the revocation.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el. <PERSON><PERSON><PERSON><PERSON> arba e<PERSON>, pat<PERSON><PERSON><PERSON>.", "For example an email, screenshot or a contract.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, ekrano nuotrauką arba sutartį.", "Forbidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Foreman": "<PERSON><PERSON><PERSON>", "Forest / Woodcutting": "<PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON> k<PERSON>", "Forestry and Logging": "Miškininkyst<PERSON> ir medienos ruoša", "Form": "Forma", "Form :form cannot be directly linked. Please use the [form_url] merge tag to create candidate-specific links to this form.": "Forma :forma negali būti <PERSON>i susieta. Norėdami sukurti specifines kandidatui skirtas nuorodas į šią formą, naudokite sujungimo <PERSON> [form_url].", "Form Submission file": "Formos pateikimo failas", "Form builder": "<PERSON><PERSON>", "Form name": "Formos pavadinimas", "Form preview": "<PERSON><PERSON>", "Form submission failed": "Nepavyko pateikti formos", "Form submitted successfully": "Forma pateikta <PERSON>", "Form type": "Formos tipas", "Forms": "Formos", "Forwarder": "Eks<PERSON><PERSON><PERSON>", "Fossil Fuel Electric Power Generation": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuro elektros energijos gamyba", "Found 1 landing.|Found {count} landings.": "Rastas 1 puslapis.|Rasta {count} puslapių.", "Free slot": "<PERSON><PERSON><PERSON><PERSON> lizdas", "Freelance": "<PERSON><PERSON><PERSON><PERSON> samdomas", "Freight and Package Transportation": "Krovinių ir siuntų vežimas", "French": "Prancūzų kalba", "Friday": "Penktadienis", "From": "<PERSON><PERSON>", "From URL": "<PERSON>š <PERSON> adreso", "From picture bank": "<PERSON>š <PERSON> banko", "From received e-mail (continuous project)": "<PERSON><PERSON> g<PERSON> el<PERSON> p<PERSON> (tęstinis projektas)", "From received e-mail, without long-term consent": "<PERSON><PERSON> g<PERSON>o el. pa<PERSON>to be ilgalaikio sutikimo", "From the left menu, choose API key": "Kairiajame meniu pasirinkite API raktą", "Fruit and Vegetable Preserves Manufacturing": "Vaisių ir daržovių konservų gamyba", "Fulah": "<PERSON><PERSON>", "Full name": "Visas vardas ir pavard<PERSON>", "Full time": "<PERSON><PERSON><PERSON> darbo dien<PERSON>", "Full time with shifts": "<PERSON><PERSON><PERSON> darbo dieną su pamainomis", "Full width picture": "Pilno ploč<PERSON> nuo<PERSON>", "Full width video": "Pilno pločio vaizdo įrašas", "Fundraising": "<PERSON><PERSON><PERSON><PERSON>", "Funds and Trusts": "Fondai", "Furniture and Home Furnishings Manufacturing": "Baldų ir namų apstatymo reikmenų gamyba", "Furniture maker": "Baldų gamintojas", "GB": "GB", "GDPR": "BDAR", "GDPR Consent admin": "BDAR sutikimas administratorius", "GDPR Preferences": "BDAR nuostatos", "GDPR admin": "BDAR administratorius", "GDPR automation": "BDAR automatizavimas", "GDPR automation disabled": "BDAR automatizavimas išjungtas", "GDPR consent length": "BDAR sutikimo trukmė", "GDPR helper": "BDAR pagalbininkas", "Gaelic, Scottish Gaelic": "Gėlų kalba, škotų gėlų kalba", "Galician": "<PERSON><PERSON><PERSON><PERSON>", "Gambling Facilities and Casinos": "Azartinių lošimų įrenginiai ir kazino", "Ganda": "Ganda", "Gardener": "Sodininkas", "Gather references from candidates and automate outreach to previous employers.": "Surinkite kandidatų rekomendacijas ir automatizuotai kreipkitės į ankstesnius darbdavius.", "Gather your applicants together in a recruitment project": "Surinkite kandidatus į atrankų projektą", "Gender": "Lyt<PERSON>", "General": "Bendra", "General Business": "<PERSON><PERSON><PERSON> vers<PERSON>", "General Manager, Company Director, Regional Manager, CEO...": "<PERSON><PERSON><PERSON> direktorius, įmonės direktorius, region<PERSON> v<PERSON>, generalinis direktorius...", "General secondary education": "<PERSON><PERSON><PERSON> vidurinis u<PERSON>", "General worker": "<PERSON><PERSON><PERSON>", "General/Department Store": "Bendrosios / skyrių parduotuvės", "Generate a new API key": "Sukurti naują API raktą", "Generate preview": "<PERSON><PERSON><PERSON><PERSON>", "Georgian": "Gruzinų kalba", "Geothermal Electric Power Generation": "Geoterminės elektros energijos gamyba", "German": "Vokiečių kalba", "Get a Quote": "<PERSON><PERSON><PERSON>", "Get debug report": "<PERSON><PERSON><PERSON> derinimo ataskait<PERSON>", "Get insights into your recruitment process. See how long it takes to fill a position, where candidates are dropping off, and more.": "Gaukite įžvalgų apie įdarbinimo procesą. <PERSON><PERSON><PERSON><PERSON><PERSON>, kiek laiko užtrunka užpildyti darbo vietą, kur kandidatai iškrenta atrankų procese ir dar daugiau.", "Get started": "Pradėkite", "Gikuyu, Kikuyu": "Gikuyu, Kikuyu", "Give main quotes about the candidate's work experience so far.": "Pateikite pagrindines citatas apie kandidato iki šiol įgytą darbo patirtį.", "Given avg. score": "Atsižvelgiant į rezultato vidurkį", "Glass Product Manufacturing": "St<PERSON>lo gaminių gamyba", "Glass, Ceramics and Concrete Manufacturing": "<PERSON><PERSON><PERSON>, keramikos ir betono gamyba", "Glazier": "<PERSON><PERSON><PERSON>", "Go back": "Grįžti atgal", "Go to requisition": "Pereiti prie paraiškos", "Go to the Forms menu and create a new Survey form.": "Eikite į Formų meniu ir sukurkite naują apklausos formą.", "Golf Courses and Country Clubs": "Golfo aikštynai ir šalies klubai", "Good": "<PERSON><PERSON><PERSON>", "Good job!": "<PERSON><PERSON><PERSON> da<PERSON>!", "Good luck with filling the position!": "Sėkmės užimti šią poziciją!", "Good luck!": "Sėkmės!", "Good morning, :name!": "<PERSON><PERSON> rytas, :name!", "Google Calendar & Meet": "\"Google\" kalendorius ir \"Meet\"", "Government Administration": "Vyriausybės administravimas", "Government Relations Services": "Ryšių su vyriausybėmis paslaugos", "Grade": "<PERSON><PERSON><PERSON>", "Graduate": "Absolventas", "Graphic Design": "<PERSON><PERSON><PERSON>", "Greek": "Graikų", "Greek (Modern)": "Graik<PERSON> kalba (šiuolaikinė)", "Greenlandic, Kalaallisut": "Grenlandų kalba, kalaallisut", "Grid": "<PERSON><PERSON><PERSON>", "Grid of your other job ads": "Kitų jū<PERSON>ų darbo skelbimų tinklelis", "Ground Passenger Transportation": "Antžeminis keleivių vežimas", "Group (CVbankas)": "Grupė (CVbankas)", "Group by": "Rušiuoti pagal", "Group job ads by": "Grupuoti darbo skel<PERSON><PERSON> pagal", "Guarani": "Guarani", "Guide": "Gidas", "Gujarati": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HR & Training": "Žmogiškieji ištekliai ir mokymas", "HVAC and Refrigeration Equipment Manufacturing": "ŠVOK ir šaldymo įrangos gamyba", "Hairdresser": "<PERSON><PERSON><PERSON><PERSON>", "Haitian, Haitian Creole": "<PERSON><PERSON><PERSON> kalba, Haičio kreolų kalba", "Handyman": "<PERSON><PERSON><PERSON>", "Happy hiring": "Sėkmingo įdarbinimo", "Has dropout reason(s)": "<PERSON><PERSON> priežastį (-is)", "Has file": "<PERSON><PERSON>", "Has tag": "<PERSON><PERSON>", "Hausa": "Hausa", "Have your IT-team configure daily backups of all your candidate data. Requires an NDA.": "Tegul jūsų IT komanda sukonfigūruoja kasdienes visų kandidatų duomenų atsargines kopijas. Reikia NDA.", "Having a sizeable talent database can significantly lower your cost and effort per hire. Meanwhile processing applicants' personal data without their documented consent leaves your company vulnerable to regulatory fines and litigation.": "Turėdami didelę talentingų darbuotojų duomenų bazę galite gerokai sumažinti išlaidas ir pastangas vienam darbuotojui įdarbinti. O tvarkant kandidatų asmeninius duomenis be dokumentais patvirtinto jų sutikimo, j<PERSON><PERSON><PERSON> įmonei gali būti skirtos teisės aktų numatytos baudos ir iškelta byla.", "Head": "<PERSON><PERSON><PERSON>", "Heading": "Antraštė", "Headings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Headline + short text + background": "Antraštė + trumpas tekstas + fonas", "Heads up!": "Įspėjimas!", "Heads up! Before you deactivate this user, review these linked items.": "Įspėjimas! <PERSON><PERSON><PERSON> šį naudotoją, per<PERSON><PERSON><PERSON><PERSON>ėkite šiuos susietus elementus.", "Health / Social care": "Sveika<PERSON> / socialin<PERSON> p<PERSON>", "Health Care / Social work": "Sveikatos p<PERSON> / Socialinis darb<PERSON>", "Health and Human Services": "Sveikatos ir žmogiškųjų paslaugų", "Health/Beauty": "Sveikata / grožis", "HealthCare Provider": "Sveikatos priežiūros paslaugų teikėjas", "Healthcare": "Sveikatos priežiū<PERSON>", "Hebrew": "Hebrajų kalba", "Height": "<PERSON><PERSON><PERSON><PERSON>", "Hello!": "Sveiki!", "Hello, :name": "Sveiki, :name", "Hello, [recipient_full_name]!": "<PERSON><PERSON><PERSON>, [recipient_full_name]!", "Hello, [user_name]!": "<PERSON><PERSON><PERSON>, [user_name]!", "Help Center": "Pagalbos centras", "Here are 5 suggestions to start with:": "Pateikiame 5 p<PERSON><PERSON><PERSON><PERSON>, nuo kurių galite pradėti:", "Here are the steps you should take:": "Štai kokių veiksmų turėtumėte imtis:", "Herero": "<PERSON><PERSON>", "Hero with buttons": "<PERSON><PERSON><PERSON><PERSON><PERSON> langas su mygtukais", "Hero with form": "Pag<PERSON><PERSON><PERSON> langas su forma", "Heroes": "<PERSON><PERSON><PERSON><PERSON>", "Hi": "Sveiki", "Hi!": "Sveiki!", "Hidden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hide": "Paslėpti", "Hide archived": "Paslėpti archyvuotus", "Hide archived landing pages from public": "<PERSON>lėpti archyvu<PERSON>us pu<PERSON>, kad jie nebūt<PERSON> vieši", "Hide files from limited users": "Nerodyti failų ribotiems naudotojams", "Hide files from limited users (except anonymized versions)": "Slėpti failus nuo ribotą prieigą turinčių naudotojų (išskyrus anonimines versijas)", "Hide inactive users": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>us var<PERSON>", "Hide messages from limited users": "Nerodyti žinučių ribotiems naudotojams", "Hide navigation-only events": "Paslėpti tik navigacijos įvykius", "Hide personal data, but show AI-generated anonymized CV and summary": "Slėpti asmens duomenis, bet rodyti dirbtinio intelekto sukurtą anoniminį CV ir santrauką", "Hide personal data, but show AI-generated anonymized summary": "Slėpti asmeninius duomenis, bet rodyti DI sugeneruotą anoniminę suvestinę", "Hide question": "Paslėpti klausimą", "Hide salary": "Paslėpti atlyginimą", "Hide stage categories": "Paslėpti etapo kategorijas", "Hide tags from limited users": "Nerodyti žymų ribotiems naudotojams", "Hide talent pool": "Paslėpti talentų duombazę", "Hide this integration from other users": "Paslėpti šią integraciją nuo kitų naudotojų", "Hide weekends": "Paslėpti sa<PERSON>galius", "Higher Education": "Auk<PERSON><PERSON><PERSON> m<PERSON>", "Higher level managers": "Aukštesnio lygio vadovai", "Higher rated first": "Pirmiausia aukštes<PERSON> re<PERSON>", "Higher scorecard score first": "Pirmiausia aukštesnis vertinimo kortelės rezultatas", "Highway, Street, and Bridge Construction": "Greitkelių, gatvių ir tiltų statyba", "Hindi": "Hindi", "Hired": "Pasamdyta", "Hires": "Įdarbinta", "Hiri Motu": "<PERSON><PERSON>", "Hiring process suspended by the company": "Įmonė sustabdė įdarbinimo procesą", "Historical Sites": "Is<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "History": "Istorija", "Holding Companies": "<PERSON><PERSON><PERSON><PERSON>oja<PERSON><PERSON><PERSON><PERSON>", "Holiday Allowance": "Atostogų išmoka", "Holy days": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Home Health Care Services": "Sveikatos priež<PERSON><PERSON><PERSON> p<PERSON> na<PERSON>", "Home/DIY": "Namai / DIY", "Horizontal bar with logo and optional CTA button": "Horizontali juosta su logotipu ir pasirenkamu CTA mygtuku", "Horticulture": "Sodininkystė", "Hospitality": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hospitality/Hotel": "Svetingumas / viešbutis", "Hospitals": "Ligonin<PERSON><PERSON>", "Hospitals and Health Care": "Ligoninės ir sveika<PERSON> p<PERSON>ž<PERSON>ū<PERSON>", "Host": "Šeimininkas", "Host your job ads, career sites, and scheduler invites on your own domain. Your branding, our tech.": "<PERSON><PERSON>, ka<PERSON><PERSON><PERSON> puslapius ir interviu kvietimus talpinkite savo įmonės domene. Jūsų prek<PERSON>, mūsų technika.", "Hotels and Motels": "Viešbučiai ir moteliai", "Hours": "Valandos", "Household Appliance Manufacturing": "Buitinių prietaisų gamyba", "Household Services": "Namų ū<PERSON> p<PERSON>lau<PERSON>", "Household and Institutional Furniture Manufacturing": "Buitinių ir institucinių baldų gam<PERSON>ba", "Housekeeper": "Ūkvedys", "Housing Programs": "Būsto programos", "Housing and Community Development": "<PERSON>ū<PERSON> ir bend<PERSON><PERSON> p<PERSON>", "How does this work?": "<PERSON>p tai veikia?", "How it works": "<PERSON><PERSON> tai veikia", "How likely are you to recommend Teamdash to a colleague?": "<PERSON><PERSON>, ka<PERSON> <PERSON><PERSON><PERSON><PERSON> \"Teamdash\" kolegai?", "How likely are you to recommend a friend or colleague to apply for a job with us?": "<PERSON><PERSON>, kad rekomen<PERSON>ite draugui ar kolegai kre<PERSON> dėl darbo pas mus?", "How many days to offer?": "<PERSON>ek dien<PERSON>?", "How this works": "<PERSON><PERSON> tai veikia", "How to automate cNPS feedback collection?": "Kaip automatizuoti cNPS atsiliepimų rinkimą?", "How to include cNPS surveys in messages?": "Kaip įtraukti cNPS apklausas į pranešimus?", "Human Resources": "Žmogiškieji ištekliai", "Human Resources / Training": "Žmogiškieji ištekliai / Mokymas", "Human Resources Services": "Žmogiškųjų išteklių paslaugos", "Human resources": "Žmogiškieji ištekliai", "Hungarian": "Vengrų", "Hungary": "<PERSON><PERSON><PERSON><PERSON>", "Hybrid": "<PERSON><PERSON><PERSON><PERSON>", "Hydroelectric Power Generation": "Hidroelektrinės energijos gam<PERSON>ba", "Hyper campaign": "\"Hyper\" kampanija", "I accept the": "<PERSON><PERSON>", "I acknowledge": "Pripažįstu.", "I agree with having my data stored for 36 months.": "<PERSON><PERSON><PERSON>, kad mano duomenys b<PERSON><PERSON> saugomi 36 mėnesius.", "I already have a membership": "<PERSON><PERSON> turiu na<PERSON>", "I am interested in the Teams feature. Can you tell me more?": "Mane domina funkcija „Komandos“. Gal galite papasakoti daugiau?", "I can't find the location": "Negaliu rasti vietos", "I forgot my password": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "I need an invoice": "Man reikia s<PERSON> fakt<PERSON>", "I need help with this": "<PERSON> reikia paga<PERSON>", "I want to limit my consent": "<PERSON><PERSON> apriboti savo sutikimą", "IMAP messages sync": "IMAP pranešimų sinchronizavimas", "IMAP path": "IMAP kelias", "IP": "IP", "IT": "IT", "IT Services and IT Consulting": "IT paslaugos ir IT konsultacijos", "IT System Custom Software Development": "IT sistemos užsakomosios programinės įrangos kūrimas", "IT System Data Services": "IT sistemos duomenų paslaugos", "IT System Design Services": "IT sistemos projektavimo paslaugos", "IT System Installation and Disposal": "IT sistemų diegimas ir šalinimas", "IT System Operations and Maintenance": "IT sistemos veikimas ir priežiūra", "IT System Testing and Evaluation": "IT sistemos testavimas ir vertinimas", "IT System Training and Support": "IT sistemų mokymas ir palaikymas", "Icelandic": "Islandijos", "Icon": "<PERSON><PERSON><PERSON>", "Icon color": "Piktogramos spalva", "Icon size": "Piktogramos dydis", "Ido": "Ido", "If a user is in multiple SCIM groups with conflicting roles, the most permissive role is used.": "Jei vartotojas priklauso kelioms SCIM grupėms su nesuderinamais vaidmenimis, naudojamas labiausiai leistinas vaidmuo.", "If enabled, GDPR automation sends out \"consent renewal request\" emails.": "Jei ši funkcija įjungta, GDPR automatizavimas siunčia el. laiškus su prašymu pratęsti sutikimą.", "If enabled, the auto-sync is run when group memberships are changed via SCIM or group mappings are changed in Teamdash.": "<PERSON>i ši funkcija įjungta, automatinė sinchronizacija paleidžiama, kai grupių narystė keičiama per SCIM arba grupių susiejimai keičiami \"Teamdash\" platformoje.", "If the integration has API keys that are directly linked, the feed will open only using those keys.": "Jei integracija turi <PERSON> raktus, kurie yra <PERSON> susieti, kanalas bus atidarytas tik naudojant š<PERSON> raktus.", "If you agreed on a new time, they can select a time slot from the original scheduling email.": "<PERSON>i susitar<PERSON>te d<PERSON>l na<PERSON> laiko, jie gali pasirinkti laiko intervalą iš pradinio planavimo el. la<PERSON>.", "If you already have an API key, copy it to the field above, otherwise, generate a new key": "Jei jau turite API raktą, nukopijuokite jį į pirmiau esantį lauką, kitu atveju sukurkite naują raktą.", "If you are interested in our future open positions offers, please give your consent for data processing here: [consent_renewal_url].": "Jei jus domina mūsų būsimi laisvų darbo vietų pasiūlymai, čia pateikite savo sutikimą dėl duomenų tvarkymo: [consent_renewal_url].", "If you are unable to participate": "Jei negalite daly<PERSON>", "If you are unable to participate, :declineLink.": "Jei negalite daly<PERSON>, :declineLink.", "If you are using Outlook, drag and drop candidate's email from your inbox and they will be automatically added to your project as a candidate.": "Jei naudojate \"Outlook\", užtempkite kandidato el. laišką iš gautųjų laiškų dėžut<PERSON>s ir jis bus automatiškai įtrauktas į projektą kaip kandidatas.", "If you cannot participate, you can cancel the interview and the other participants will be notified that the event needs to be rescheduled.": "Jei negalite daly<PERSON>, galite at<PERSON><PERSON> pokalbį, o kitiems dalyviams bus praneš<PERSON>, kad susitikimas turi būti per<PERSON>.", "If you did not receive the email": "Jei negavote el. la<PERSON>", "If you did not request a password reset, no further action is required.": "Jei nepraš<PERSON> iš naujo nustatyti slaptažodžio, joki<PERSON> tolesnių veiksmų imtis nereikia.", "If you don't choose any delivery channel, candidates will not get your invites.": "Jei nepasirinksite jokio pristatymo kanalo, kandidatai negaus jū<PERSON>ų kvietimų.", "If you don't have a landing page for this job yet, create it": "Jei dar neturite šios darbo vietos puslapio, sukurk<PERSON> jį", "If you don't have an ad for this job yet, create it": "Jei dar neturite šio da<PERSON>o s<PERSON>, sukurk<PERSON> jį", "If you don't map this field to a candidate field, the field value will still be available on form submissions page.": "<PERSON>i šio lauko <PERSON>izduos<PERSON> į kandidato lauk<PERSON>, lauko <PERSON> vis tiek bus prieinama formos pateikimo puslapyje.", "If you don't reassign, GDPR automation will be disabled.": "Jei nepakeisite paskyrimo, BDAR automatizavimas bus išjungtas.", "If you don't understand it, just reply to this email :)": "<PERSON><PERSON>, ties<PERSON>g atsakykite į šį laišką :)", "If you edit an already published job ad in Teamdash, a new advertisement will be posted to ss.lv.": "Jei redagu<PERSON>te jau paskelbtą darbo skelbimą „Teamdash“, naujasis skelbimas bus paskelbtas ss.lv.", "If you have already shared this feed with someone, you will need to give them a new URL after you create the new key.": "Jei jau bendrinate šį sklaidos kanalą su kuo nors, suk<PERSON><PERSON><PERSON> naują raktą turėsite suteikti jam naują URL.", "If you have any questions, just click the support icon in bottom-right of your screen and ask away.": "Jei turite klausimų, spustelėkite pagalbos piktogramą ekrano apačioje dešinėje ir klauskite.", "If you have filters applied, check that you have selected the correct template type.": "<PERSON>i pasirinkote filtrus, pat<PERSON><PERSON><PERSON><PERSON>, ar pasirinkote tinkamą šablono tipą.", "If you have updated the form, click here to load changes.": "<PERSON>i at<PERSON>ote formą, spustelėki<PERSON> č<PERSON>, kad įkeltumėte pakeitimus.", "If you have updated the form, click the refresh button on the form to load changes.": "<PERSON>i atnaujinote formą, spustelėkite formos atnaujinimo my<PERSON>, kad būtų įkelti pakeitimai.", "If you leave before saving, your changes will be lost.": "Jei paliksite neišsaugoję, pakeitimai bus prarasti.", "If you set a value here, a button with this text will be displayed after form submission.": "Jei čia nustatysite reikšmę, pateikus formą bus rodomas mygtukas su šiuo tekstu.", "If you wish to add more slots, you can do that in the :calendarLink.": "Jei norite pridėti daugiau laik<PERSON>, tai galite padaryti čia: :calendarLink.", "If you wish to let the candidate reschedule the interview, you can add more slots in the :calendarLink.": "Jei norite leisti kandidatui perkelti pokal<PERSON>, galite pridėti daugiau laiko tarpsnių :calendarLink.", "If you wish to let the candidate reschedule the interview, you can send them a message under their profile.": "Jei norite leisti kandidatui perkelti darbo pokalbį, galite išsiųsti jam žinutę jo profilyje.", "If you wish to send out all email from @yourdomain.xyz, please set up a mail identity.": "Jei norite visus el. laiškus siųsti iš adreso @yourdomain.xyz, nustatykite pašto tapatybę.", "If you're having trouble clicking the \":actionText\" button, copy and paste the URL below into your web browser": "<PERSON><PERSON> kyla sunkumų paspaudus mygtuką „:actionText“, nukopijuokite ir įklijuokite toliau pateiktą URL adresą į savo interneto naršyklę.", "If your address is <i>mycompany</i>.bamboohr.com, then use <i>mycompany</i> as username.": "<PERSON><PERSON> jū<PERSON> adresas yra <i>mycompany</i>.bamboohr.com, ka<PERSON> var<PERSON><PERSON> vardą naudokite <i>mycompany</i>.", "Igbo": "Igbo", "Image": "Vaizdas", "Image ad": "<PERSON><PERSON><PERSON><PERSON>", "Image generation URL": "Paveikslėlio generavimo URL", "Image position": "<PERSON><PERSON><PERSON><PERSON>", "Image settings": "<PERSON><PERSON><PERSON><PERSON>", "Image:": "Vaizdas:", "Images": "<PERSON><PERSON><PERSON><PERSON>", "Import candidates": "<PERSON><PERSON><PERSON><PERSON><PERSON> kandidatus", "Import connection OK!": "Importuoti ryšį Gerai!", "Import from mailbox": "Importuoti iš pa<PERSON>", "Import from {integrationType}": "<PERSON><PERSON>rt<PERSON><PERSON> i<PERSON> {integrationType}", "Import users": "I<PERSON>rt<PERSON><PERSON> na<PERSON>", "Imported :count candidates": "Importuota :count kandidatus", "Imported from external system": "Importuota iš <PERSON>", "Imports": "Importas", "Imports are synced automatically every 5 minutes.": "Importas sinchronizuojamas automatiškai kas 5 minutes.", "Impressions": "Įspūdžiai", "Improve writing": "Pagerinti <PERSON>š<PERSON>", "In project view, click the three dots in stage header and choose \"Set automatic stage action\".": "Projekto rodinyje s<PERSON>ėkite tris taškus etapo antraštėje ir pasirinkite „Nustatyti automatinį etapo veiksmą“.", "In shifts": "<PERSON><PERSON><PERSON><PERSON>", "Inactive": "Neaktyvus", "Include a form that is linked to this project on a landing page": "Įtraukti su šiuo projektu susietą formą į nukreipimo puslapį", "Included on {landingPage}": "Įtraukta į {landingPage}", "Incoming message:": "Įeinantis pranešimas:", "Increase Level": "<PERSON><PERSON><PERSON><PERSON> lygį", "Individual and Family Services": "<PERSON>ios paslaugos ir paslau<PERSON>", "Indonesian": "Indonezijos", "Industrial Machinery Manufacturing": "Pramonės ma<PERSON> gamyba", "Industry (CV-Library)": "Pramonė (CV-Library)", "Industry Associations": "Pramon<PERSON><PERSON>", "Info": "Informacija", "Info message": "Informacinis <PERSON>", "Inform that ignoring this message is assumed to be a non-consent.": "Informuoki<PERSON>, kad <PERSON><PERSON> p<PERSON> ignoravimas laikomas nesutikimu.", "Inform that they can immediately decline consent from the link.": "In<PERSON><PERSON><PERSON><PERSON>, kad jie gali nedel<PERSON>dami atsisakyti duoti sutikimą d<PERSON>l nuorod<PERSON>.", "Information Services": "Informacin<PERSON><PERSON>", "Information Technology": "Informacinės technologijos", "Information technology": "Informacinės technologijos", "Information tehcnology": "Informacinės technologijos", "Inherit candidate file access rights from projects.": "Gauti kandidatų failų prieigos teises iš projektų.", "Inherit limited user file access from projects": "<PERSON><PERSON><PERSON> ribotą naudotojo prieigą prie failų iš projektų", "Initialize comment visibility switch as public": "Inicializuokite komentarų matomumo jungiklį kaip viešą", "Initiator": "Iniciatorius", "Instance email provider": "Instancijos el. pašto paslaugų teikėjas", "Instant Upload": "Momentinis įkėlimas", "Instant video call": "<PERSON><PERSON><PERSON> v<PERSON>", "Institution": "Institucija", "Instructor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Insurance": "<PERSON><PERSON><PERSON><PERSON>", "Insurance Agencies and Brokerages": "<PERSON><PERSON><PERSON>", "Insurance Carriers": "<PERSON><PERSON><PERSON>", "Insurance agent": "<PERSON><PERSON><PERSON>", "Insurance and Employee Benefit Funds": "<PERSON><PERSON><PERSON>š<PERSON>ų darbuotojams fondai", "Insurances": "<PERSON><PERSON><PERSON><PERSON>", "Integration name": "Integracijos pavadin<PERSON>s", "Integrations": "Integracijos", "Interior Design": "Interjero dizainas", "Interlingua (International Auxiliary Language Association)": "Interlingua (Tarptautinė pagalbinių kalbų asociacija)", "Interlingue": "Interlingue", "Internal form": "<PERSON><PERSON><PERSON><PERSON> forma", "Internal job ad": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> s<PERSON>", "Internal name used in Teamdash": "<PERSON><PERSON><PERSON><PERSON> pavadin<PERSON>s naudo<PERSON><PERSON>", "International Affairs": "Tarptautiniai reikalai", "International Trade and Development": "Tarptautinė prekyba ir vysty<PERSON>is", "Internet Marketplace Platforms": "Interneto rinkos platformos", "Internet News": "Interneto naujienos", "Internship": "Stažuotė", "Interurban and Rural Bus Services": "Tarpmiestinių ir kaimo vietovių autobusų paslaugos", "Interview": "<PERSON><PERSON> pokalbis", "Interview file": "<PERSON><PERSON><PERSON> byla", "Interview invite": "Kvietimas į interviu", "Interview length": "Interviu t<PERSON>", "Interview length (in minutes)": "<PERSON><PERSON><PERSON> t<PERSON> (minutėmis)", "Interview scheduled": "Suplanuotas interviu", "Interview scheduling": "<PERSON><PERSON><PERSON>", "Interview scheduling is not available within templates. You can add this action after creating a project.": "Interviu planavimas <PERSON> negalimas. Šį veiksmą galite pridėti sukūrę projektą.", "Interview slot": "<PERSON><PERSON><PERSON> la<PERSON>", "Interview slot: :title on :start (:tz) has been cancelled.": "Darbo pokalbis: :title suplanuotas :start (:tz) buvo at<PERSON><PERSON><PERSON>.", "Interview slot: :title on :start has been cancelled.": "<PERSON>viu laikas: :title on :start buvo atšauktas.", "Interview slot: :title scheduled for :start": "Interviu laikas: :title numatytas :start", "Interview slot: :title scheduled for :start (:tz)": "Darbo pokalbis: :title planuojamas :start (:tz)", "Interview summary": "<PERSON>viu sant<PERSON>", "Interview time": "<PERSON><PERSON> poka<PERSON> la<PERSON>", "Interview title": "Interviu pavadin<PERSON>s", "Interview with {candidateName}": "Interviu su {candidate<PERSON><PERSON>}", "Interviewees": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Interviews": "Interviu", "Inuktitut": "Inuktitutas", "Inupiaq": "Inupiakų kalba", "Invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Invalid color code": "Netinkamas spalvos kodas", "Invalid confirmation code!": "Negaliojantis pat<PERSON><PERSON> koda<PERSON>!", "Invalid data in row :row": "Neteisingi duomenys eil<PERSON> :row", "Invalid phone number.": "Negaliojantis telefono numeris.", "Invalid user to reassign to": "<PERSON><PERSON><PERSON><PERSON>, kurį norite iš naujo priskirti", "Investment Advice": "Investavimo konsultacijos", "Investment Banking": "Investicinė bankininkystė", "Investment Management": "Investicijų valdymas", "Invitation details": "Kvietimo informacija", "Invite accepted": "Kvietimas p<PERSON>imtas", "Invite candidates": "<PERSON><PERSON><PERSON><PERSON> kandidatus", "Invite confirmation": "<PERSON><PERSON><PERSON><PERSON>", "Invite message": "Kvietimo žinutė", "Invite new users": "<PERSON><PERSON><PERSON> na<PERSON> na<PERSON>", "Invite pending": "Laukiama kvietimo", "Invite sent to user! If there are further problems, forward the password reset url manually: :url": "Kvietimas išsiųstas naudotojui! Jei kyla papildomų problemų, persiųskite slaptažodžio atstatymo url adresą rankiniu būdu: :url", "Invite sent!": "Kvietimas išsiųstas!", "Invite subject": "Kvie<PERSON><PERSON> tema", "Invite users": "<PERSON><PERSON><PERSON>", "Invites": "Kvietimai", "Invites not sent!": "Kvietimai neišsiųsti!", "Invites sent!": "Kvietimai išsiųsti!", "Irish": "Airių", "Is public (shared with other users)": "<PERSON><PERSON> v<PERSON> (bendrinamas su kitais naudotojais)", "Is remote?": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>?", "Is teams admin?": "Ar komandos yra administratorius?", "Is this an e-mail from a candidate? Drop it here!": "Ar tai kandidato el. <PERSON>? Tempkite jį čia!", "Is your database GDPR compliant?": "Ar jūs<PERSON> duomenų bazė atitinka BDAR reikalavimus?", "Issuer URL": "Išdavėjo URL", "It MUST include [password_url]. It may include [current_user_name], [user_name].": "<PERSON><PERSON> būti [password_url]. <PERSON><PERSON> gali būti [current_user_name], [user_name].", "It asks your candidates to rate on a 0 to 10 scale -": "Kandidatų prašoma įvertinti nuo 0 iki 10 balų.", "It looks like you're using the new iOS 18.": "<PERSON><PERSON><PERSON>, kad naudojate naująją „iOS 18“.", "It may include :tags.": "<PERSON><PERSON> b<PERSON>ti :tags.", "It seems that you don’t have access to this feature.": "<PERSON><PERSON><PERSON>, kad neturite prieigos prie šios funkcijos.", "It uses your calendar integration to see when participants are busy.": "<PERSON><PERSON><PERSON> bū<PERSON> naudojama jūsų kalendoriaus integracija, kad bū<PERSON><PERSON> matoma<PERSON> u<PERSON>.", "Italian": "Italų kalba", "Italic": "<PERSON><PERSON><PERSON><PERSON>", "Items": "Prek<PERSON><PERSON>", "Jacket size": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Janitor": "<PERSON><PERSON><PERSON><PERSON>", "Janitorial Services": "<PERSON><PERSON><PERSON>", "January": "Sausis", "Japanese": "Japonų", "Javanese": "Javos", "Jeweler": "<PERSON><PERSON><PERSON><PERSON>", "Job Location": "Darbo vieta", "Job Unpublished (:orgName): :position": "Nepaskelbtas darbas (:orgName): :position", "Job ad button text": "<PERSON><PERSON> s<PERSON><PERSON>mo my<PERSON>o te<PERSON>", "Job ad count text": "Darbo skelbimų skaičiaus tekstas", "Job ad exports": "Darbo skelbimų eksportas", "Job ad has been sent to your email.": "Darbo skelbimas buvo išsiųstas į jūsų el. paštą.", "Job ad image": "Darbo skelbimo vaizdas", "Job ad landing page hostname": "Darbo skelbimo nukreipimo puslapio serverio pavadinimas", "Job ads": "<PERSON><PERSON>", "Job boards": "Darbo skelbimų lentos", "Job description preview": "<PERSON><PERSON>", "Job functions": "<PERSON><PERSON>", "Job info": "Darbo informacija", "Job level (CVK/CVM)": "Darbo lygis (CVK/CVM)", "Job published!": "Paskelbtas darbas!", "Job requisition": "Darbo užklausa", "Job requisition added": "Pridėta darbo užklausa", "Job requisition deadline changed: new deadline in :deadline": "Pakeistas darbo paraiškos terminas: naujas terminas :deadline", "Job requisitions": "<PERSON><PERSON> užkla<PERSON>", "Job type": "<PERSON><PERSON>", "Join the video interview here:": "Prisijunkite prie video interviu čia:", "Join us next Tuesday, on the 29th of April @ 1 pm EEST to learn about asynchronous video interviews.": "Prisijunkite prie mūsų kitą antradienį, balandžio 29 d., 13 val. <PERSON><PERSON><PERSON>, kad <PERSON><PERSON>te daugiau apie nesinchroninius vaizdo interviu.", "Join your team on Teamdash": "Prisijunkite prie savo komandos  „Teamdash“", "Joiner": "<PERSON><PERSON><PERSON>", "Journalist": "Žurnalistas", "July": "<PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "KB": "KB", "Kannada": "Kanados", "Kanuri": "<PERSON><PERSON><PERSON>", "Kashmiri": "Kašmyras", "Kazakh": "Kazachų", "Keep current timezone": "<PERSON><PERSON><PERSON> da<PERSON> laiko juostą", "Keep your Teamdash user list in sync with your company's identity provider.": "Sinchronizuokite \"Teamdash\" naudotojų sąrašą su savo įmonės tapatybės teikėju.", "Keep your accounts secure with two-factor authentication. Requires a mobile phone number.": "Saugokite savo paskyras naudodami dviejų veiksnių autentifikavimą. Reikalingas mobiliojo telefono numeris.", "Key name": "<PERSON>kto pavadin<PERSON>s", "Keys": "Raktai", "Kinyarwanda": "Kinyarwanda", "Knitter": "<PERSON><PERSON>g<PERSON><PERSON>", "Komi": "<PERSON><PERSON>", "Kongo": "Kong<PERSON>", "Korean": "Korėjiečių kalba", "Kurdish": "Kurdų", "Kwanyama, Kuanyama": "Kwanyama, Kuanyama", "Kyrgyz": "Kirgizija", "LARS code": "LARS kodas", "Label": "<PERSON><PERSON><PERSON>", "Laboratory assistant": "Laboratorijos <PERSON>istent<PERSON>", "Landing": "Nusileidimas", "Landing URL copied to clipboard.": "Puslapio URL nukopijuotas", "Landing Upload": "Puslapio įkėlimas", "Landing page": "<PERSON><PERSON><PERSON>", "Landing page URL": "Puslapio URL", "Landing pages": "Visi puslapiai", "Landscaping Services": "Kraštovaizdžio tvarkymo paslaugos", "Language": "Kalba", "Language (CVbankas)": "Kalba (CVbankas)", "Language Proficiency level": "Kalbos mokėjimo lygis", "Language Schools": "<PERSON><PERSON><PERSON><PERSON> mokyklos", "Language skills (profession.hu)": "<PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Lao": "Lao", "Last 30 days": "Paskutinės 30 dienų", "Last 30 scrapes of :feedName feed": "Paskutiniai 30 :feedName srauto nuskaitym<PERSON>", "Last 60 days": "Paskutinės 60 dienų", "Last 90 days": "Paskutinės 90 dienų", "Last active": "Paskutinis aktyvus", "Last application date": "Paskutinės para<PERSON>škos data", "Last attempt": "Paskuti<PERSON>ym<PERSON>", "Last education": "Pask<PERSON><PERSON> iš<PERSON>", "Last employment": "Paskutinė darbovietė", "Last project": "Paskutinis projektas", "Last recommended attempt": "Paskutinis rekomendu<PERSON><PERSON> band<PERSON>", "Last scraped at :lastScrapeTime": "Paskutinį kartą nuskaityta :lastScrapeTime", "Last updated on {date}": "Paskutinį kartą atnaujinta {date}", "Latest bounced.": "<PERSON><PERSON><PERSON><PERSON><PERSON>šokę<PERSON>.", "Latest clicked.": "Paskutinį kartą spustel<PERSON>.", "Latest opened.": "Paskutinį kartą atidarytas.", "Latest sent.": "Paskutinis iš<PERSON>stas.", "Latest version published": "Paskelbta naujausia versija", "Latin": "Lotynų kalba", "Latvia": "Latvija", "Latvian": "Lat<PERSON><PERSON>", "Laundry and Drycleaning Services": "Skalbimo ir cheminio valymo p<PERSON>lau<PERSON>", "Law": "Teis<PERSON>", "Law / Legal": "Teisė / teisė", "Law Enforcement": "Teisėsauga", "Law Practice": "Teis<PERSON><PERSON> prak<PERSON>", "Lawyer": "<PERSON><PERSON><PERSON><PERSON>", "Learn more": "Suž<PERSON>ti daugiau", "Leasing Non-residential Real Estate": "Negyvenamosios paskirties nekilnojamojo turto nuoma", "Leasing Real Estate Agents and Brokers": "Išperkamoji nuoma Nekilnojamojo turto agentai ir brokeriai", "Leasing Residential Real Estate": "Gyvenamosios paskirties nekilnojamojo turto nuoma", "Leather Product Manufacturing": "Odos gaminių gamyba", "Leave empty for default": "Palikite tušč<PERSON>ą standartinę reikšmė", "Leave empty if not applicable": "Palik<PERSON> t<PERSON>, jei <PERSON>", "Leave empty to remove button": "Palikite tu<PERSON>, kad pa<PERSON>lint<PERSON>te mygtuką", "Leave without editing candidates": "<PERSON><PERSON><PERSON> neredagavus kandidat<PERSON>", "Left": "Kair<PERSON>", "Left button link override": "<PERSON><PERSON>jo mygtuko nuorod<PERSON>", "Legal": "<PERSON><PERSON><PERSON>", "Legal Services": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Legal ground": "<PERSON><PERSON><PERSON> p<PERSON>", "Legislative Offices": "Įstatymų le<PERSON><PERSON><PERSON>osios valdžios tarnybos", "Leisure/Hospitality": "<PERSON><PERSON><PERSON><PERSON><PERSON> / svetingumas", "Leisure/Tourism": "Laisvalaikis / turizmas", "Less time in stage first": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> pir<PERSON> etape", "Let AI search the candidate's CV and add relevant tags if applicable.": "Leiskite dirbtiniam intelektui peržvelgti kandidato CV ir, j<PERSON>, pridėkite atitinka<PERSON>.", "Let your candidates apply via form": "Leiskite kandidatams teikti paraiškas per formą", "Let your managers request new hires and let management approve or reject the requests. All steps are customisable.": "Leiskite vadovams pateikti prašymus priimti naujus darb<PERSON>, o vadovybei - juos patvirtinti arba atmesti. Visus veiksmus galima koreguoti.", "Letzeburgesch, Luxembourgish": "Letzeburgesch, liuksemburgiečių kalba", "Level": "Lygis", "Level of education": "Išsilavinimo lygis", "Libraries": "Bibliotekos", "Lighter": "<PERSON><PERSON><PERSON><PERSON>", "Limburgish, Limburgan, Limburger": "Limburgish, Limburgan, Limburger", "Lime and Gypsum Products Manufacturing": "Kalkių ir gipso produktų gamyba", "Limit interviews to start on ...": "Apribokite pokalbių pradžią nuo ...", "Limit:": "Limitas:", "Limited user": "Ribotas <PERSON>", "Limited users can see the same projects as regular users. Stages hidden from limited users stay hidden.": "Ribotą prieigą turintys naudotojai gali matyti tuos pačius projektus kaip ir įprasti naudotojai. Nuo ribotą prieigą turinčių naudotojų paslėpti etapai lieka paslėptais.", "Limits enforced": "Limitai p<PERSON>i", "Limits not enforced": "<PERSON><PERSON><PERSON>", "Lingala": "Lingala", "Link": "<PERSON><PERSON><PERSON><PERSON>", "Link URL": "Nuorodos URL", "Link to current project": "Nuoroda į dabartinį projektą", "Linked to": "<PERSON><PERSON> su", "LinkedIn": "LinkedIn", "LinkedIn Company ID": "\"LinkedIn\" įmonės ID", "LinkedIn URL": "\"LinkedIn\" URL adresas", "LinkedIn industry code": "\"LinkedIn\" industrija", "Links": "<PERSON><PERSON><PERSON><PERSON>", "Linux admin": "„Linux“ administratorius", "List": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "List all kinds of data you might be processing (e.g. CV, contact info, etc)": "nurodyktie visus duomenis, k<PERSON><PERSON><PERSON> gal<PERSON> t<PERSON> (pvz., <PERSON><PERSON><PERSON><PERSON>, kontaktinę informaciją ir pan.)", "List of Sub-processors": "Subprocesorių sąrašas", "List of your other job ads": "Kitų jū<PERSON>ų darbo skelbimų sąrašas", "Lithuania": "Lietuva", "Lithuanian": "Lietuvių kalba", "Load more": "Įkelti daugiau", "Loader": "Krovėjas", "Loading": "<PERSON><PERSON><PERSON><PERSON>", "Loading landing page preview": "Įkeliama pu<PERSON><PERSON>", "Loading...": "<PERSON><PERSON><PERSON><PERSON>...", "Loan Brokers": "Paskolų brokeriai", "Location": "Vieta", "Location (CVO)": "Vieta (CVO)", "Location (profession.hu)": "<PERSON><PERSON><PERSON> (profession.hu)", "Location address": "Vietov<PERSON><PERSON> ad<PERSON>", "Location data": "Duomenys a<PERSON> vietą", "Location name": "Vietos pavadinimas", "Location range": "Vietos ribos", "Location:": "Vieta:", "Locations": "Vietos", "Locksmith": "Š<PERSON><PERSON>kal<PERSON>", "Log": "Žurnalas", "Log in to CV online, make sure you have an administrator account": "Prisijunkite prie CV Online, įsitikinkite, kad turite administratoriaus paskyr<PERSON>", "Log out": "<PERSON>si<PERSON><PERSON><PERSON>", "Login failed. The user does not have an email address associated with their account.": "Prisijungti nepavyko. Naudotojas neturi su paskyra susieto el. pašto adreso.", "Logistician": "Logistas", "Logistics": "Logistika", "Logistics / Transport": "Logistika / Transportas", "Logo": "Logotipas", "Long term consent expiring soon": "Ilgalai<PERSON> suti<PERSON> netrukus bai<PERSON>is", "Long text": "Ilgas tekstas", "Longer in stage first": "Ilgesnis pir<PERSON>is etapas", "Loss Prevention": "Nuostolių prevencija", "Lower rated first": "Pirmiausia žemesnio reitingo", "Lower scorecard score first": "Pirmiausia žemesnis vertinimo kortelės rezultatas", "Luba-Katanga": "Luba-Katanga", "MB": "MB", "MS Office messages sync": "\"MS Office\" žinučių sinchronizavimas", "MTD": "MTD", "Macedonian": "<PERSON><PERSON><PERSON><PERSON>", "Machinery Manufacturing": "Technikos gamyba", "Magnetic and Optical Media Manufacturing": "Magnetinių ir optinių laikmenų gamyba", "Mail": "<PERSON><PERSON><PERSON>", "Mail folder ID": "Pašto aplanko ID", "Mail identities": "<PERSON><PERSON><PERSON>", "Mail settings": "<PERSON><PERSON><PERSON>", "Mail signature settings": "<PERSON><PERSON><PERSON> n<PERSON>", "Make default": "Nustatykite numatytąsias nuostatas", "Make import and application form submission comments visible to limited users": "Importo ir paraiškos formos pateikimo komentarus padaryti matomus ir ribotiems naudotojams", "Make longer": "Padaryti ilgesnį", "Make private": "Padaryti privatų", "Make public": "<PERSON><PERSON><PERSON><PERSON>", "Make sure this is a trusted address controlled by your organization.": "Įsitikinkite, kad tai yra pat<PERSON> ad<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON> organizacijai.", "Make this comment visible to limited users": "Padaryti šį komentarą matomą tik ribotam naudotojų skaičiui", "Make this entry visible to limited users": "Padaryti šį įrašą matomą tik ribotam naudotojų ratui", "Make your job ads accessible only from your company's internal network.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad darbo skelbimai būtų pasiekiami tik iš įmonės vidinio tinklo.", "Malagasy": "Malagasių", "Malay": "Malajų kalba", "Malayalam": "Malajalam<PERSON>", "Male": "<PERSON><PERSON><PERSON>", "Maltese": "Maltiečių", "Manage project access": "Tvarkyti projekto prieigą", "Manage stage actions": "<PERSON><PERSON><PERSON><PERSON> etapo ve<PERSON>", "Manage stage actions for {stageName}": "<PERSON><PERSON><PERSON><PERSON> {stageName} etapo veik<PERSON> ", "Manage teams hierarchy": "Tvarkykite komandų hierarchiją", "Manage users": "<PERSON><PERSON><PERSON>", "Management": "<PERSON><PERSON><PERSON>", "Management/quality management": "Valdymas / kokybė<PERSON> vald<PERSON>", "Manager": "Vadybininkas", "Manager by staff": "<PERSON><PERSON>", "Manager name": "<PERSON><PERSON><PERSON><PERSON><PERSON> var<PERSON> ir pava<PERSON>", "Manager or coworker quotes block": "Vadovo ar bendradarbio citatų blokas", "Manager:": "Vadybininkas:", "Managing recruiter": "Atrank<PERSON>ado<PERSON>", "Mandatory two-factor authentication": "Privalomas d<PERSON>jų veiksnių autentifikavimas", "Manicurist": "Man<PERSON>ūrini<PERSON><PERSON>", "Manually added to stage": "<PERSON><PERSON><PERSON> būdu įtrauktas į sceną", "Manufacturing": "Gamyba", "Manufacturing / Production": "Gamyba / Produkcija", "Manufacturing/Surveying": "Gamyba / Geodezija", "Manx": "Manx", "Maori": "<PERSON><PERSON><PERSON>", "Marathi": "Marathi", "March": "<PERSON><PERSON>", "Maritime Transportation": "Jūrų transportas", "Mark GDPR consent for imported candidates": "Pažymėkite importuojamų kandidatų BDAR sutikimą", "Mark as favourite": "Paž<PERSON>ė<PERSON> ka<PERSON>", "Mark finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad baigta", "Mark notifications as read": "Paž<PERSON>ėti p<PERSON> ka<PERSON>", "Market Research": "<PERSON><PERSON><PERSON>", "Marketing": "<PERSON><PERSON><PERSON><PERSON>", "Marketing / Advertising": "Rinkodara / reklama", "Marketing / Advertising / PR": "Rinkodara / Reklama / PR", "Marketing Services": "<PERSON><PERSON><PERSON><PERSON>", "Marketing/advertising": "Rinkodara ir (arba) reklama", "Marshallese": "<PERSON><PERSON>", "Mason": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Masseur": "Ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "Master's degree": "<PERSON><PERSON><PERSON>", "Match candidates with all the conditions above.": "Suderink<PERSON> kandidatus, atitinkančius visas pirmiau nurodytas sąlygas.", "Match candidates with at least one of the conditions above.": "Suderinkite kandidatus, atitinka<PERSON><PERSON><PERSON> bent vieną iš pirmiau nurodytų sąlygų.", "Mattress and Blinds Manufacturing": "Čiužinių ir žaliuzių gamyba", "Max attempt count: unlimited": "Maksimalus bandymų skaičius: neribotas", "Max attempt count: {count}": "Maks<PERSON>lus bandymų s<PERSON>čius: {count}", "Max attempt count: {maxAttemptCount}": "Maksimalus bandymų skaičius: {maxAttemptCount}", "Max candidate attempts": "<PERSON><PERSON><PERSON><PERSON> kandidato bandymų s<PERSON>", "Maximum value": "Didžiausia vertė", "May": "Gegužė", "Measuring and Control Instrument Manufacturing": "Matavimo ir kontrolės prietaisų gamyba", "Meat Products Manufacturing": "Mėsos produktų gamyba", "Mechanic": "<PERSON><PERSON><PERSON><PERSON>", "Mechanics / Engineering": "Mechanika / Inžinerija", "Media": "Žiniasklaida", "Media / New media / Creative": "Ž<PERSON><PERSON>laida / Naujoji <PERSON>di<PERSON> / K<PERSON><PERSON><PERSON>", "Media / PR": "Žiniasklaida / ryšiai su visuomene", "Media Production": "Žiniasklaidos produkcija", "Media and Telecommunications": "Žiniasklaida ir telekomunikacijos", "Media/communication": "Žiniasklaida ir komunikacija", "Medical Equipment Manufacturing": "Medicinos įrangos gamyba", "Medical Practices": "Medicinos praktika", "Medical and Diagnostic Laboratories": "Medicinos ir diagnostikos laboratorijos", "Medical/Pharmaceutical/Scientific": "Medicinos/Farmacijos/Mokslo", "Medicine/pharmacy": "Medicina / vaistinė", "Meet your candidates without scheduling a call. Candidates answer your questions on their own time.": "Susipažinkite su kandidatais neplanuodami skambučio. Kandidatai atsako į jūsų klausimus savo pasirinktu laiku.", "Meeting analysis is already in progress.": "Atliekama susitikimo analizė.", "Meeting details": "Susitikimo informacija", "Meeting location": "Susitikimo vieta", "Meeting location:": "Susitikimo vieta:", "Meeting room:": "Susi<PERSON><PERSON><PERSON> kambarys:", "Members": "<PERSON><PERSON><PERSON>", "Members to ping": "<PERSON><PERSON><PERSON>, kuriems reikia siųsti pranešimą", "Mental Health Care": "Psichikos sveikatos priežiūra", "Mentor:": "<PERSON><PERSON><PERSON>:", "Merchandising": "Prekybos", "Merge": "<PERSON><PERSON><PERSON><PERSON>", "Merge dropout reasons": "Sulieti iškritimo priežastis", "Merge duplicate candidates": "<PERSON><PERSON><PERSON><PERSON> besidu<PERSON><PERSON><PERSON><PERSON> kandidatus", "Merge tags": "<PERSON><PERSON><PERSON><PERSON>", "Merged": "<PERSON><PERSON><PERSON><PERSON>", "Message": "Žinutė", "Message Attachment": "<PERSON><PERSON><PERSON><PERSON>", "Message body": "Praneš<PERSON>", "Message details": "Išsami informacija apie žinutę", "Message not sent!": "Pranešimas neišsiųstas!", "Message project members": "Pranešimas projekto nariams", "Message sent!": "Žinutė išsiųsta!", "Message subject": "<PERSON><PERSON><PERSON><PERSON>", "Message templates": "Žinučių šablonai", "Message will be sent in": "Pranešimas bus išsiųstas", "Message will be sent in {delayRelative}": "Pranešimas bus išsiųstas už {delayRelative}", "Message:send": "Žinutė", "Messages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Meta description": "<PERSON><PERSON>", "Metal Ore Mining": "<PERSON><PERSON> r<PERSON>", "Metal Treatments": "<PERSON><PERSON>", "Metal Valve, Ball, and Roller Manufacturing": "Metalinių vožtuvų, rutuliukų ir ritinėlių gamyba", "Metalworking Machinery Manufacturing": "Metalo apdirbimo mašinų gamyba", "Microsoft Calendar schedule": "\"Microsoft\" kalendor<PERSON><PERSON> t<PERSON>", "Microsoft Teams calls": "\"Microsoft Teams\" skambučiai", "Mid-senior level": "Vidutinio ir vyresniojo lygio", "Middle manager": "Viduriniosios grandies vadovas", "Midlevel Manager / Senior Specialist": "Vidutinio ly<PERSON> / Vyresnysis specialistas", "Military and International Affairs": "Karin<PERSON>i ir tarptautiniai reikalai", "Military/Emergency/Government": "<PERSON><PERSON><PERSON><PERSON><PERSON> / sku<PERSON>s paga<PERSON>bos tarny<PERSON> / vyriausybė", "Milkmaid": "<PERSON><PERSON><PERSON><PERSON>", "Milling-machine operator": "Frezavimo staklių operatorius", "Minimize": "Sumažinkite", "Minimize column": "Minimizuoti stulpelį", "Minimum amount of users needed approval from": "<PERSON><PERSON><PERSON><PERSON><PERSON> na<PERSON>, kuriam reikia pat<PERSON><PERSON><PERSON> iš", "Minimum number of minutes between the interview and the next event": "Minimalus minučių skaičius nuo interviu iki kito įvykio", "Minimum number of minutes between the previous event and the interview": "<PERSON><PERSON><PERSON><PERSON><PERSON>čių skaičius tarp ankstesnio įvykio ir pokalbio", "Minimum value": "Mažiausia vertė", "Mining": "Kalnakasyba", "Minutes": "<PERSON><PERSON><PERSON><PERSON>", "Missing :missingCount :approval.": "Trūks<PERSON> :missingCount :approval.", "Missing email!": "Trūksta el. pašto!", "Missing or invalid phone!": "Trūksta telefono arba jis negalioja!", "Missing recorded video! Make sure you clicked the \"Confirm\" button after recording.": "Trūksta įrašyto vaizdo įrašo! Įsitikinkite, kad po įrašymo paspaudėte mygtuką \"Patvirtinti\".", "Missing related project.": "Trūksta susijusio projekto.", "Missing social media preview image. Please generate or upload it.": "Trūksta socialinės žiniasklaid<PERSON> perž<PERSON>ū<PERSON> vaizdo. Sukurkite arba įkelkite jį.", "Missing translation in {language}": "Trūks<PERSON> vertimo {language}", "Missing {missingCount} {approval}.": "<PERSON>r<PERSON><PERSON><PERSON> {missingCount} {approval} .", "Mixed": "<PERSON><PERSON><PERSON>", "Mobile Computing Software Products": "Mobiliosios kompiuterijos programinės įrangos produktai", "Mobile Food Services": "Mobiliosios ma<PERSON> p<PERSON>", "Mobile Gaming Apps": "Mobiliųjų žaidimų programos", "Mobile View": "<PERSON><PERSON><PERSON><PERSON> vai<PERSON>", "Model": "Modelis", "Moldovan, Moldavian, Romanian": "moldavų, moldavų, rumunų", "Monday": "Pirmadienis", "Mongolian": "<PERSON><PERSON><PERSON><PERSON>", "Month": "<PERSON><PERSON><PERSON><PERSON>", "Months": "Mėnesiai", "Months of GDPR consent": "BDAR sutikimo mėnesiai", "More": "Daugiau", "More info": "Daugiau informacijos", "Motor Vehicle Manufacturing": "Variklinių transporto priemonių gamyba", "Motor Vehicle Parts Manufacturing": "Variklinių transporto priemonių dalių gamyba", "Move down": "Perkelkite žemyn", "Move the candidate to {stage}": "<PERSON><PERSON><PERSON> kandi<PERSON> į {stage}", "Move this interview to another time and send event updates to all participants.": "Perkelkite šį pokalbį į kitą laiką ir visiems dalyviams išsiųskite įvykio atnaujinimus.", "Move to next stage": "Perėjimas į kitą etapą", "Move to stage": "Persikėlimas į etapą", "Move up": "<PERSON><PERSON><PERSON>", "Moved to stage": "Perkelta į etapą", "Moved to stage by action": "Perkelta į etapą automatiniu veiksmu", "Movie and Video Distribution": "Filmų ir vaizdo įrašų platinimas", "Movies, Videos and Sound Recording": "<PERSON><PERSON>, vaizdo ir garso įrašai", "Multimedia": "Daugialypė terpė", "Multiple files": "<PERSON><PERSON>", "Multiple stages": "<PERSON><PERSON> etapai", "Multiselect": "Daugiafunk<PERSON><PERSON> p<PERSON>", "Multiselect (checkboxes)": "Daugiafunk<PERSON><PERSON> (žymimieji langeliai)", "Museums": "Muziejai", "Museums, Historical Sites, and Zoos": "<PERSON><PERSON><PERSON><PERSON>, is<PERSON><PERSON><PERSON><PERSON> v<PERSON>ov<PERSON>s ir zoologijos sodai", "Musician": "Muzikantas", "Musicians": "Muzikantai", "Must accept - :attribute": "<PERSON><PERSON>valo sutikti - :attribute", "Must be accepted": "<PERSON><PERSON> b<PERSON><PERSON>", "Must be in lowercase a-z and underscore, at least 5 characters. This is used internally by the system.": "<PERSON><PERSON> b<PERSON>ti naudoja<PERSON> ma<PERSON> raidės a-z ir apatinis br<PERSON>, ne mažiau kaip 5 simboliai. Tai naudojama sistemos viduje.", "Mute e-mail notifications": "Nutildyti el. pa<PERSON>", "My account": "<PERSON><PERSON> p<PERSON>", "My form": "<PERSON>o forma", "Name": "Vardas", "Names of people": "Žmonių vardai ir pava<PERSON>ės", "Nanny": "Auklė", "Nanotechnology Research": "Nanotechnologijų tyrimai", "Natural Gas Distribution": "Gamtinių dujų paskirstymas", "Natural Gas Extraction": "Gamtinių dujų gavyba", "Nauru": "Nauru", "Navajo, Navaho": "Navajo, Navaho", "Ndonga": "Ndonga", "Needed for reach estimation": "<PERSON><PERSON><PERSON><PERSON> pasiek<PERSON>ui įvertinti", "Neighbourhood or parish (SS.lv)": "Kaimynyst<PERSON> arba <PERSON> (SS.lv)", "Nepali": "Nepalo", "Network administrator": "<PERSON><PERSON><PERSON>", "Neutral": "Neutral<PERSON>", "New Job Posted by :orgName: :position": "Paskelbtas naujas darbas :orgName: :position", "New Project": "Na<PERSON>jas projektas", "New SMS from :name": "Nauja SMS žinutė nuo :name", "New chat": "<PERSON><PERSON><PERSON> pokal<PERSON>", "New project": "Na<PERSON>jas projektas", "New project display name": "Naujas projekto rodoma<PERSON> pava<PERSON>s", "New project from scratch": "Naujas projektas nuo nulio", "New project name": "Naujas projekto pavadinimas", "New stage name": "<PERSON><PERSON><PERSON> etapo pava<PERSON>", "New user invite template": "<PERSON><PERSON><PERSON> naudo<PERSON>jo k<PERSON>ti<PERSON>", "Newer applications first": "Pirmiausia naujesnės kandi<PERSON>", "Newest first": "Pirmiausia naujesnės kandi<PERSON>", "Newspaper Publishing": "Laikraščių leidyba", "Next": "Kitas", "Next decade": "<PERSON><PERSON>", "Next file": "<PERSON><PERSON> failas", "Next month": "Kitą mėnesį", "Next question": "Kit<PERSON> k<PERSON>", "Next year": "<PERSON><PERSON> metais", "Nice": "Gražu", "No": "Ne", "No API keys added yet.": "API raktų dar nepridėta.", "No Event": "Nėra įvykio", "No actions yet": "Ko<PERSON> kas nėra jokių veiksmų", "No active links": "Nėra aktyvių nuorodų", "No activity recorded yet.": "<PERSON><PERSON> kas neužfiksuota joki<PERSON> ve<PERSON>.", "No application methods yet": "Dar nėra paraiškos teikimo būdų", "No candidate has confirmed this time slot yet. You'll get an update once it's confirmed.": "<PERSON><PERSON> laiko dar nepatvir<PERSON> nė vienas kandidatas. Kai jis bus pat<PERSON><PERSON>, gausite atnaujintą informaciją.", "No candidates": "Kandidatų nėra", "No candidates found": "Kandidatų nerasta", "No candidates found.": "Kandidatų nerasta.", "No candidates have been shared with you yet.": "Nė vienas kandidatas su jumis dar nebuvo pasidalintas.", "No comments yet": "Komentarų dar nėra", "No consent": "Sutikimo nėra", "No consent at all": "<PERSON><PERSON><PERSON>", "No data": "Duomen<PERSON> nėra", "No data available": "Duomen<PERSON> nėra", "No data processing consents": "Duomenų tvarkymo sutikimų nėra", "No date selected": "Data nepasirinkta", "No email": "Nėra el. pašto adreso", "No file chosen": "Nepasirinktas joks failas", "No file types added yet.": "Dar nepridėta jokių failo tipų.", "No forms": "Jokių formų", "No forms added yet.": "<PERSON><PERSON> kas nepridėta jokių formų.", "No forms found.": "Formų nerasta.", "No integrations yet": "Nėra jokių integracijų", "No landing pages yet": "Dar nėra nukreip<PERSON> pu<PERSON>", "No landings found": "Puslapių nerasta", "No landings found. Try adjusting the filters.": "Nerasta jokių puslapių. Pabandykite koreguoti filtrus.", "No lower than higher education": "Ne žemesnis nei aukštasis išsilavinimas", "No lower than secondary education": "Ne žemesnis nei vidurinis iš<PERSON>lavinimas", "No messages": "Jokių pranešimų", "No permissions": "Leidimų nėra", "No phone": "Nėra telefono numerio", "No project log entries": "Nėra projekto žurnalo įrašų", "No projects": "Projektų nėra", "No projects are included in this report. Change the filters to include some projects.": "Į šią ataskaitą neįtraukti jokie projektai. <PERSON><PERSON><PERSON><PERSON> filtrus, kad įtrauktumėte reikiamus projektus.", "No projects found": "Projektų nerasta", "No projects have been created from this template yet.": "Pagal šį šabloną dar nesukurta projektų.", "No ratings": "Nėra įvertinimų", "No reason specified": "Priežast<PERSON>", "No references": "Rekomendacijų nėra", "No results found": "Rezultatų nerasta", "No results text": "Rezultatų nėra", "No scheduled interviews found": "Suplanuotų interviu nerasta", "No scorecards added yet.": "Ko<PERSON> kas nepridėta jokių rezultatų kortelių.", "No scorecards found.": "Rezultatų kortelių nerasta.", "No sentence may be longer than 200 characters.": "Nė vienas sakinys negali būti ilgesnis nei 200 ženklų.", "No submissions yet": "Dar nėra pateiktų paraiškų", "No survey response yet": "Atsakymų į apklausą dar nėra", "No tasks found": "Užduočių nerasta", "No team": "<PERSON><PERSON><PERSON>", "No templates found": "Šablonų nerasta", "No templates yet": "Šablonų dar nėra", "No templates yet. Try changing the filters.": "Šablonų dar nėra. Pabandykite pakeisti filtrus.", "No transcripts found": "Nuorašų nerasta", "No transcripts found.": "Nuorašų nerasta.", "No upcoming interviews": "Jokių būsimų interviu", "No users found": "Naudotojų nerasta", "No users found. Try changing the filters.": "Naudotojų nerasta. Pabandykite pakeisti filtrus.", "No users will be notified.|1 user will be notified.|{count} users will be notified.": "Naudotojams nebus pranešta.|1 vartotojui bus pranešta.| {count} naudotojai bus informuoti.", "No video interviews": "Nėra vaizdo interviu", "No video interviews found": "Vaiz<PERSON> interviu nerasta", "No video interviews found.": "Vaizdo interviu nerasta.", "No, let me double-check": "<PERSON><PERSON>, leiskite man dar kartą patikrinti", "Non-profit Organizations": "Ne pelno siekiančios organizacijos", "None": "Nėra", "None selected": "<PERSON>ėra pasirin<PERSON>a", "Nonmetallic Mineral Mining": "Nemetalinių naudingųjų iškasenų gavyba", "Nonresidential Building Construction": "Negyvenamųjų pastatų statyba", "North & Western Europe": "Šiaurės ir Vakarų Europa", "Northern Ndebele": "Šiaurės Ndebele", "Northern Sami": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Norwegian": "Norve<PERSON><PERSON>", "Norwegian Bokmål": "Norvegų bokmål", "Norwegian Nynorsk": "Norvegų nynorsk", "Not Found": "<PERSON><PERSON><PERSON><PERSON>", "Not a valid Vimeo URL.": "Netinkamas \"Vimeo\" URL.", "Not a valid Youtube URL.": "Netinkamas \"Youtube\" URL.", "Not a valid video file URL.": "Netinkamas vaizdo failo URL.", "Not allowed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Not applicable": "<PERSON><PERSON><PERSON><PERSON>", "Not duplicate": "Nesidubliuoja", "Not included on any landing pages": "Neįtraukta į jokius nukre<PERSON><PERSON> pusla<PERSON>us", "Not scheduled yet": "<PERSON>", "Not specified": "Nenurody<PERSON>", "Not started": "Nepradėta", "Notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Notes (not visible to candidates):": "<PERSON><PERSON><PERSON><PERSON><PERSON> (kandidatams nematoma):", "Nothing here": "Čia nieko nėra", "Nothing to show": "Nėra k<PERSON> rodyti", "Notice: integration health check failed - :website": "Pranešimas: integracijos būklės patikrini<PERSON> ne<PERSON> – :website", "Notification email": "Pranešimo el. <PERSON>", "Notifications are currently turned on. E-mails are delivered on weekdays at 8:00, 13:00, 16:00. Click here to turn off.": "Šiuo metu pranešimai yra įjungti. Elektroniniai laiškai pristatomi darbo dienomis 8:00, 13:00, 16:00 val. <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON><PERSON>.", "November": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Now let's create your personal account.": "<PERSON>bar sukurkime asmeninę paskyrą.", "Nuclear Electric Power Generation": "Branduolinės elektros energijos gamyba", "Number of days": "<PERSON><PERSON><PERSON>", "Number of hours per week": "Val<PERSON><PERSON> skaič<PERSON> per savaitę", "Number of positions": "Pozici<PERSON><PERSON> s<PERSON>", "Numbers": "Skaičiai", "Numeric rating": "Skaitmeninis įvertinimas", "Numeric value": "Skaitmeninė vertė", "Nuosu, Sichuan Yi": "<PERSON><PERSON><PERSON>, Sičuanas Yi", "Nurse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Nursing": "<PERSON><PERSON><PERSON>", "Nursing Homes and Residential Care Facilities": "Slaugos namai ir stacionarios globos įstaigos", "OK": "GERAI", "OVP card": "OVP kortelė", "Obligatory specialized education": "Privalomas specializuotas išsilavinimas", "Occitan (post 1500)": "okitanų kalba (po 1500 m.)", "Occurrences": "Atvejai", "October": "<PERSON><PERSON>.", "Off": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Offer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Offer section title": "<PERSON><PERSON><PERSON><PERSON><PERSON>us pavadinimas", "Offers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Office": "Biuras", "Office Administration": "Biuro administravimas", "Office Furniture and Fixtures Manufacturing": "Biuro baldų ir įrangos gamyba", "Office Worker / Specialist": "Biuro da<PERSON> / Specialistas", "Offices": "Biurai", "Oh no": "O ne", "Oil Extraction": "Naftos gavyba", "Oil and Coal Product Manufacturing": "Naftos ir anglies produktų gamyba", "Oil and Gas": "<PERSON><PERSON><PERSON> ir dujos", "Oil, Gas, and Mining": "<PERSON><PERSON><PERSON>, dujos ir ka<PERSON>ba", "Ojibwa": "Ojibwa", "Older applications first": "Pirmiausia senesnės programos", "On the second Tuesday of every month all your candidates without a valid data processing consent get a \"consent renewal request\" email.": "Kiekvieno mėnesio antrąjį antradienį visi jūsų kandidatai, neturintys galiojančio sutikimo tvarkyti duomenis, gauna el. laišk<PERSON> su prašymu atnaujinti sutikimą.", "On the top menu, click your company name": "Viršutiniame meniu spustelėkite savo įmonės pavadinimą", "On-site": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Once logged in, go to the \"Manage app credentials\" here:": "Prisijungę eikite į \"Tvarkyti programėlės įgaliojimus\" čia:", "Once someone submits a form, their submission will appear here.": "Kai kas nors užpi<PERSON> formą, kandidatūra bus rodoma čia.", "Online Audio and Video Media": "Internetinė garso ir vaizdo medija", "Online and Mail Order Retail": "Mažmeninė prekyba internetu ir paštu", "Only": "Tik", "Only YouTube URLs. Used for job board exports.": "Tik \"YouTube\" URL adresai. Naudojama darbo skelbimų lentos eksportui.", "Only YouTube links are supported.": "Palaikomos tik \"YouTube\" nuorodos.", "Only active users can log in to the system.": "Prie sistemos gali prisijungti tik aktyvūs naudotojai.", "Only administrators can delete clients.": "Tik administratoriai gali ištrinti klientus.", "Only fill if you want to connect to a shared mailbox that your account has access to.": "Pi<PERSON>ma tik tada, jei norite prisijungti prie bendrinamos pa<PERSON><PERSON>, prie kurios prieigą turi jū<PERSON>ų paskyra.", "Only from service accounts": "Tik iš paslaugų sąskaitų", "Only my projects": "Tik mano projektai", "Only project managers": "Tik projektų vadovai", "Only run if": "<PERSON><PERSON><PERSON><PERSON> tik tada, jei", "Only show comments from current project": "Rodyti tik dabartinio projekto komentarus", "Only show logs for this project": "Rodyti tik šio projekto žurnalus", "Only summary": "Tik santrauka", "Only you can see this message.": "Šį pranešimą galite matyti tik jūs.", "Oops! Something went wrong.": "Ups! <PERSON><PERSON><PERSON> nutiko ne taip.", "Open": "<PERSON><PERSON><PERSON><PERSON>", "Open CV": "Atviras CV", "Open Teamdash": "Atidaryti „Teamdash“", "Open candidate": "At<PERSON><PERSON><PERSON> kandi<PERSON>", "Open candidate :initials profile": "<PERSON><PERSON><PERSON> kandidato profilis :initials", "Open form": "Atvira forma", "Open integration": "Atidaryti integraciją", "Open invite": "<PERSON><PERSON><PERSON>", "Open landing": "Atviras nusileidimas", "Open profile": "At<PERSON><PERSON><PERSON> profilį", "Open project": "Atidaryti projektą", "Open slot": "<PERSON><PERSON><PERSON> liz<PERSON>", "Open slots": "<PERSON><PERSON><PERSON> laiko tarpai", "Open the link in your phone.": "Atidarykite nuorodą telefone.", "OpenAI (US-based)": "OpenAI (JAV)", "Opened at": "Atidary<PERSON> ad<PERSON>", "Operations Consulting": "<PERSON><PERSON><PERSON><PERSON>", "Operator": "Operatorius", "Optional info": "Neprivaloma informacija", "Optionally choose a single form which triggers this action.": "Pasirinktinai vieną formą, kuri aktyvu<PERSON> šį veiksmą.", "Options": "Parinktys", "Optometrists": "Optometrai", "Or upload video": "Arba įkelkite vaizdo įrašą", "Org. location city": "Org. vieta miestas", "Org. name": "Organizacijos pavadinimas", "Org. registry code": "Organizacijos registro kodas", "Org. street address": "Org. gat<PERSON><PERSON><PERSON> ad<PERSON>", "Org. website URL": "Org. s<PERSON><PERSON>ės URL", "Org. website url": "Org. s<PERSON><PERSON> url", "Organisation management": "Organizacijos valdymas", "Organization Logo": "Organizacijos logotipas", "Organization favicon": "Organizacijos favicon", "Organization logo": "Organizacijos logotipas", "Organization name": "Organizacijos pavadinimas", "Organization name override": "Organizacijos pavadinimo pakeitimas", "Organization office address": "Organizacijos biuro adresas", "Organization settings": "Organizacijos nustatymai", "Organization settings under the GDPR tab": "Organizacijos nustatymai skirtuke BDAR", "Organization timezone": "Organizacijos laiko juosta", "Organization type": "Organizacijos tipas", "Original location:": "Originali vieta:", "Original value (you don't have access to it)": "Orgin<PERSON> vertė (neturite prieigos prie jos)", "Oriya": "Oriya", "Oromo": "Oromo", "Ossetian, Ossetic": "osetinų, osetinų", "Other": "<PERSON><PERSON>", "Other Candidate file": "Kitas kandidato failas", "Other Head Office Vacancies": "<PERSON><PERSON> pag<PERSON> buveinės laisvos darbo vietos", "Other Retailers": "Kiti mažmenininkai", "Other exports": "Kitas eksportas", "Other file": "<PERSON><PERSON> failas", "Other icons": "Kitos piktogram<PERSON>", "Other integrations": "Kitos integracijos", "Other languages": "Kitos ka<PERSON>", "Other professions": "Kitos profesijos", "Otherwise, we will not contact you about future vacancies at [organization_name].": "Priešingu atveju su jumis nesusisieksime dėl būsimų laisvų darbo vietų [organization_name].", "Outcome description": "Rezultatų aprašymas", "Outpatient Care Centers": "Ambulatorinės priežiūros centrai", "Outsourcing and Offshoring Consulting": "Užsakomosios ir perkeliamosios konsultacijos", "Overdue!": "Pavėluotai!", "Overlay": "Perdangos", "Overrides the organization name from settings. Can be used if you're managing multiple brands": "Pakeičia organizacijos pavadinimą iš nustatymų. <PERSON><PERSON><PERSON>, jei valdote kelis pre<PERSON>.", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "P.S. You can configure the question {here}.": "P.<PERSON><PERSON> galite konfigūruoti {here}.", "PB": "PB", "Packaging and Containers Manufacturing": "Pakuočių ir talpyklų gamyba", "Packer": "Pakuotojas", "Page Expired": "Puslapio galiojimo laikas pasi<PERSON>igę<PERSON>", "Page background": "P<PERSON><PERSON><PERSON> fonas", "Page background:": "Puslapio fonas:", "Page meta description": "Puslapio meta aprašymas", "Page meta title": "Puslapio meta pavadinimas", "Page saved!": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>!", "Page title": "Puslapio pavadinimas", "Paint, Coating, and Adhesive Manufacturing": "<PERSON><PERSON><PERSON>, dangų ir klijų gamyba", "Painter": "<PERSON><PERSON><PERSON><PERSON>", "Pali": "<PERSON><PERSON>", "Panjabi, Punjabi": "Pandž<PERSON>s, pandžabis", "Paper and Forest Product Manufacturing": "Popieriaus ir miško produktų gamyba", "Parlourmaid": "Kambarių tvarkytojas", "Part time": "Ne visą darbo dieną", "Participants": "Dalyviai", "Participants will be notified that they are being recorded.": "Dalyviams bus pranešta, kad da<PERSON>s įrašas.", "Participants:": "Dalyviai:", "Pashto, Pushto": "puštūn<PERSON> kalba, puštūnų kalba", "Password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Password must not be in top 10M passwords": "Slaptažodis negali būti tarp 10 mln. populiariausių slaptažodžių", "Paver": "Trinkelių klojėjas", "Pending": "<PERSON><PERSON><PERSON>", "Pending job requisition": "Neišnagrinėta darbo paraiš<PERSON>", "Pension Funds": "Pensijų fondai", "Perform an action": "Atlikti veiksmą", "Performing Arts": "<PERSON><PERSON><PERSON>", "Performing Arts and Spectator Sports": "Scenos menai ir žiūrovų sportas", "Periodical Publishing": "Periodinių leidinių leidyba", "Permalink": "Permalink", "Permanent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Permissions": "<PERSON><PERSON><PERSON><PERSON>", "Persian": "Persų kalba", "Person code": "<PERSON><PERSON><PERSON> kodas", "Person on duty": "<PERSON><PERSON><PERSON>", "Personal": "<PERSON><PERSON><PERSON><PERSON>", "Personal Care Product Manufacturing": "Asmens higienos produktų gamyba", "Personal Care Services": "As<PERSON><PERSON><PERSON><PERSON> p<PERSON> p<PERSON>", "Personal and Laundry Services": "As<PERSON><PERSON><PERSON><PERSON> ir skalbi<PERSON> p<PERSON>", "Personnel/Recruitment": "Personalas / įdarbinimas", "Pet Services": "Gyvūnų augintinių paslaugos", "Pharmaceutical Manufacturing": "Farmac<PERSON>jos <PERSON>", "Pharmacist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pharmacy": "Vaistinė", "Philanthropic Fundraising Services": "Labdaros lėšų <PERSON> p<PERSON>", "Philologist": "Filologas", "Phone": "Telefonas", "Phone number": "Telefono numeris", "Phone number with country code": "Telefono numeris su šalies kodu", "Phone with country code": "Telefonas su šalies kodu", "Photo": "<PERSON><PERSON><PERSON><PERSON>", "Photographer": "Fotografas", "Photography": "Fotografija", "Photomodel": "Fotomodel<PERSON>", "Physical, Occupational and Speech Therapists": "<PERSON><PERSON><PERSON><PERSON>, ergoterapijos ir logopedai", "Physicians": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Picker": "<PERSON><PERSON><PERSON><PERSON>", "Pin": "<PERSON><PERSON><PERSON>", "Ping user": "<PERSON>", "Pipeline Transportation": "<PERSON><PERSON><PERSON>", "Pipeline overview": "<PERSON><PERSON><PERSON>", "Placement": "Vieta", "Plain text block": "Paprastojo teksto blo<PERSON>", "Plasterer": "Tinkuotojas", "Plastics Manufacturing": "Plastikų gamyba", "Plastics and Rubber Product Manufacturing": "Plastiko ir gumos gaminių gamyba", "Platforms": "Platformos", "Play": "<PERSON><PERSON><PERSON><PERSON>", "Play question videos": "Paleisti klausimų vaizdo įrašus", "Please add a cNPS field to the form.": "Į formą įtraukite cNPS lauką.", "Please add the [:tag] merge tag!": "Pridėkite [:tag] bendrą žymą!", "Please add the [interview_url] merge tag!": "<PERSON><PERSON><PERSON><PERSON><PERSON> [interview_url] sujungi<PERSON>!", "Please add the [invite_url] merge tag to SMS!": "Į SMS žinutę pridėkite [invite_url] sujungimo žym<PERSON>!", "Please add the [invite_url] merge tag!": "<PERSON><PERSON><PERSON><PERSON><PERSON> [invite_url] sujungimo <PERSON>!", "Please add the [presentation_url] merge tag!": "Pridėkite [presentation_url] žymą!", "Please add the [video_call_details] merge tag!": "Pridėkite [video_call_details] sujungimo <PERSON>!", "Please allow access to your camera and microphone.": "Suteikite prieigą prie fotoaparato ir mikrofono.", "Please assign a category to every stage": "Kiekvienam etapui priskirkite kategoriją", "Please choose a form": "Pasirinkite formą", "Please choose a form:": "Pasirinkite formą:", "Please choose an image": "Pasirinkite paveikslėlį", "Please click the link below to verify your e-mail and create your Teamdash instance.": "Spustelėkite toliau pateiktą nuorodą, kad patvir<PERSON>te savo el. pašto ad<PERSON> ir susikurtumėte atskirą „Teamdash“ kopiją.", "Please confirm": "<PERSON><PERSON><PERSON><PERSON>", "Please confirm that you want to anonymize {anonCount} candidates.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad norite anonimizuoti {anonCount} kandidatus.", "Please confirm that you want to permanently delete {anonCount} candidates.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad norite visam laikui i<PERSON>trinti {anonCount} kandidatus.", "Please confirm your password before continuing.": "<PERSON><PERSON><PERSON> t<PERSON>dami patvir<PERSON> slaptažodį.", "Please connect to a WiFi network before starting.": "<PERSON><PERSON><PERSON>, prisijunkite prie „Wi-Fi“ tinklo.", "Please contact support at {mail} to enable SMS features": "Norėdami įjungti SMS funkcijas, k<PERSON>ipkitės į klientų aptarnavimo komandą {mail}.", "Please contact support for custom e-mail setups.": "Dėl pasirinktinių el. pašto nustatymų kreipkitės į klientų aptarnavimo komandą.", "Please contact your account manager to get started with cNPS reporting.": "Susisiekite su savo paskyros vadybininku, kad pradėtumėte teikti cNPS ataskaitas.", "Please enter a valid email address.": "Įveskite galiojantį el. pa<PERSON><PERSON> ad<PERSON>.", "Please enter your references": "Įveskite savo rekomendacijas", "Please insert your e-mail!": "Įrašykite savo el. pašto ad<PERSON>!", "Please make your decision": "Priimkite sprendimą", "Please note that Indeed expects the content of the published job ad to exactly match the details listed on the landing page.": "Atkreipki<PERSON> dėmesį, kad \"Indeed\" tiki<PERSON>, jog pask<PERSON><PERSON>o darbo skelbimo turinys tiksliai atitiks informaciją, nurodytą nukre<PERSON><PERSON> pu<PERSON>.", "Please note that ss.lv does not allow editing job ads once they have been published.": "Atkreipkite dėmesį, kad ss.lv negalima redaguoti paskelbtų darbo skelbimų.", "Please paste iframe URL below or click {here} to choose a form from a list.": "Toliau įklijuokite iframe URL arba spustelėkite {here} ir pasirinkite formą iš s<PERSON>.", "Please publish landing to export an image.": "Paskelbkite puslapį, kad eksportuotumėte vaizdą.", "Please re-connect your integration": "<PERSON><PERSON> naujo prijunkite integraciją", "Please resolve all conflicts before continuing.": "<PERSON><PERSON>š tęsdami darbą išspręskite visus neaiškumus.", "Please resolve conflicting data before merging.": "<PERSON><PERSON><PERSON>i pašalinkite duomenų nesuderinamumą.", "Please review the following:": "Peržiūrėkite toliau pateiktą informaciją:", "Please save the landing page to add a form.": "Norėdami p<PERSON> form<PERSON>, išsaugokite susijusį puslapį.", "Please select a response.": "Pasirinkite atsakymą.", "Please select a video.": "Pasirinkite vaizdo įrašą.", "Please select an interview time": "Prašome pasirinkti pokalbio laiką", "Please select at least one candidate to send a message.": "Nor<PERSON><PERSON><PERSON>, p<PERSON>rinki<PERSON> bent vieną kandidatą.", "Please select at least one consent type or give a general consent.": "Pasirinkite bent vieną sutikimo tipą arba duokite bendrą sutikimą.", "Please select at least one user to send a message.": "Norėdami si<PERSON>, p<PERSON>rin<PERSON><PERSON> bent vieną naudotoją.", "Please select time slots": "Pasirinkite laiko intervalus", "Please set a password here: [password_url]": "Čia nustatykite slaptažodį: [password_url]", "Please update {updateLink} or contact your system administrator.": "Atnaujinkite {updateLink} arba kreipkitės į sistemos administratorių.", "Please upload an attachment or remove the option": "Įkelkite priedą arba pašalinkite šią parinktį", "Please use SSO!": "Naudokite SSO!", "Please write briefly what you want them to do.": "Trumpai <PERSON>, ką norite, kad jie daryt<PERSON>.", "Please {updateYourBrowserHere} or contact your system administrator.": "Prašau {updateYourBrowserHere} arba susisiekite su sistemos administratoriumi.", "Polish": "Lenkų kalba", "Political Organizations": "Politinės organizacijos", "Poor": "<PERSON><PERSON><PERSON>", "Port": "Uostas", "Portuguese": "Portugalų kalba", "Position": "Pozicija", "Position details": "Informacija apie poziciją", "Position fit:": "Pozicija tinka:", "Position fit: {positionFit}%": "Padėties tinkamumas: {positionFit}%", "Position info": "Informacija apie poziciją", "Position name": "Pareigų pavadinimas", "Position name is required!": "<PERSON><PERSON><PERSON> nuro<PERSON> pareigybės pavadinimą!", "Position select": "Pasirinkite poziciją", "Position title": "Pareigų pavadinimas", "Position type": "Pozicijos <PERSON>", "Position type (cv.lt)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (cv.lt)", "Possible duplicates": "<PERSON><PERSON><PERSON>", "Possible duplicates found": "<PERSON><PERSON><PERSON>i <PERSON>", "Possibly irrelevant candidate": "Galimai nesvarbus kandidatas", "Post": "Raš<PERSON><PERSON>", "Postal Services": "<PERSON><PERSON><PERSON>", "Postcode": "<PERSON><PERSON><PERSON>", "Postman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Postmark": "<PERSON><PERSON><PERSON>", "Pre-scheduled": "<PERSON><PERSON>", "Prebooked Holidays": "<PERSON><PERSON>", "Precise location": "Tiksli vieta", "Prefer high quality": "Pirmenybė teikiama aukštai kokybei", "Prefer small file size": "Pirmenybė teikiama mažam failo dydžiui", "Preferences": "Nustatymai", "Preferred Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> vardas", "Premium campaign": "\"Premium\" kampanija", "Present candidates": "Dabartiniai kandidatai", "Present your why-work-here arguments with this block.": "Šiame bloke pateikite argumentus, kod<PERSON><PERSON> verta čia dirbti.", "Preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Preview font": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Preview font when hovering": "<PERSON><PERSON><PERSON> peržiūra užvedus pelę", "Preview form": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "Previous": "<PERSON><PERSON><PERSON><PERSON>", "Previous chats": "Ankstesni p<PERSON>i", "Previous decade": "<PERSON><PERSON><PERSON><PERSON>", "Previous file": "<PERSON><PERSON><PERSON><PERSON>", "Previous month": "<PERSON><PERSON><PERSON><PERSON>", "Previous week": "Ankstesnė savaitė", "Previous year": "Ankstesni metai", "Previously uploaded": "Anksčiau įkeltas", "Primary Metal Manufacturing": "Pirminė metalo gamyba", "Primary and Secondary Education": "<PERSON><PERSON><PERSON><PERSON> ir vidurinis u<PERSON>", "Primary color (hex)": "<PERSON><PERSON><PERSON><PERSON><PERSON> spalva (hex)", "Primary colour": "Pagrindinė spalva", "Primary colour:": "Pagrindinė spalva:", "Printing Services": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Printing worker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Privacy notice": "Privatumo p<PERSON>", "Privacy policy": "Privatumo politika", "Privacy policy URL": "Privatumo politikos URL adresas", "Private email": "Privatus el. pa<PERSON>", "Private integration": "Privati integracija", "Private job ads": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>", "Probation period (days)": "<PERSON><PERSON><PERSON> (dienos)", "Product Management": "Produktų valdymas", "Production": "Gamyba", "Production / Manufactoring": "Gamyba / gamyba", "Professional": "Profesionalus", "Professional Organizations": "Profesin<PERSON>s organizaci<PERSON>", "Professional Services": "Profesionalios <PERSON>lau<PERSON>", "Programme": "Programa", "Programmer": "Programuotojas", "Project": "Projektas", "Project (Teamdash)": "<PERSON>je<PERSON><PERSON> (Teamdash)", "Project / stage": "Projektas / etapas", "Project ID": "Projekto ID", "Project ID (Teamdash)": "Projekto ID (Teamdash)", "Project Log": "Projekto žurnalas", "Project Management": "Projektų valdymas", "Project action added!": "Pridėtas projekto veiksmas!", "Project actions": "Projekto veiksmai", "Project actions for {position}": "<PERSON><PERSON><PERSON><PERSON>, su<PERSON><PERSON><PERSON> su {position}", "Project actions will be automatically created based on the template you select.": "Projekto veiksmai bus sukurti automatiškai pagal pasirinktą šabloną.", "Project couldn't be converted to a template.": "Projekto nebuvo galima konvertuoti į šabloną.", "Project created": "Projektas sukurtas", "Project created!": "Projektas sukurtas!", "Project custom fields": "Projekto pasirinktiniai laukai", "Project end date": "Projekto pabaigos data", "Project entry date": "Projekto pradžios data", "Project failure reason saved!": "Projekto nesėkmės priežastis išsaugota!", "Project failure reasons": "Projekto nesėkmės priežastys", "Project finished!": "Projektas baigtas!", "Project log": "Projekto žurnalas", "Project log for {position}": "<PERSON><PERSON><PERSON><PERSON> {position}", "Project manager": "Projektų vadovas", "Project manager (Teamdash)": "<PERSON><PERSON><PERSON><PERSON> (Teamdash)", "Project stages": "Projekto etapai", "Project start date": "Projekto pradžios data", "Project statistics": "Projekto statistika", "Project status is": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>a yra", "Project status, deadlines, warranties and other dates can be found here now.": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>, terminus, garantijas ir kitas datas dabar galite rasti čia.", "Project template": "Projekto <PERSON>as", "Project updated": "<PERSON>jekt<PERSON>", "Project users and teams can now be added and removed in one convenient place.": "Projekto naudotojus ir komandas dabar galima pridėti ir pašalinti vienoje patogioje vietoje.", "Project visible only for members": "<PERSON><PERSON><PERSON><PERSON> matomas tik nariams", "Projects": "Projektai", "Projects count": "<PERSON>je<PERSON><PERSON> s<PERSON>", "Projects managed by": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> valdo", "Projects status": "Projektų statusas", "Projects using {templateName} as template": "<PERSON><PERSON><PERSON><PERSON>, kuriuose {templateName} naudo<PERSON><PERSON> ka<PERSON>", "Promotional content": "<PERSON><PERSON><PERSON><PERSON> turi<PERSON>", "Prompt for a reason when a candidate drops out": "<PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>dy<PERSON> priežastį, kai kandidata<PERSON> iškrenta iš <PERSON>rank<PERSON>", "Property Services": "Nekilnojamojo turto paslaugos", "Provider": "<PERSON><PERSON><PERSON><PERSON>", "Psychologist": "Psichologas", "Public / Governmental Service": "Viešosios / Vyriausybinės p<PERSON>", "Public Assistance Programs": "Viešosios pagalbos programos", "Public Health": "Visuo<PERSON><PERSON><PERSON>", "Public Policy Offices": "Viešosios politikos tarnybos", "Public Relations": "Viešieji ryšiai", "Public Relations and Communications Services": "Vieš<PERSON><PERSON><PERSON> ryšių ir komunikacijos paslaugos", "Public Safety": "Viešoji sauga", "Public Sector": "<PERSON><PERSON><PERSON><PERSON> sektorius", "Public administration/civil service": "Viešasis administravimas ir (arba) valstybės tarn<PERSON>ba", "Public domain": "Vieš<PERSON>ji <PERSON>", "Public servant": "Valstybės ta<PERSON>", "Publish": "Paskelbti", "Publish a new job ad": "Paskelbkite naują darbo skel<PERSON>ą", "Publish new": "Paskelbti naują", "Publish the landing page to download as image": "Paskelbkite nukreipimo puslapį, kad gal<PERSON>te atsisiųsti kaip paveikslėlį", "Publish to external channels": "Skelbti išoriniuose kanaluose", "Publish {landingName}": "Paskelbti {landingName}", "Published as": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaip", "Published job ads": "Paskelbti darbo s<PERSON>i", "Published on": "Paskelbta", "Published position": "Paskelbta pozicija", "Published until": "Paskelbta iki", "Publishing to this combination of channels is currently impossible.": "Šiuo metu neįmanoma skelbti tokio kanalų derinio.", "Purchasing": "Pirkimas", "Push candidates to BambooHR ATS": "Perkelkite kandidatus į BambooHR ATS", "Qualification type": "Kvalif<PERSON><PERSON><PERSON>", "Qualifications": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Quality assurance": "Ko<PERSON><PERSON><PERSON><PERSON>", "QualityAssurance": "Ko<PERSON><PERSON><PERSON><PERSON>", "Quechua": "<PERSON><PERSON><PERSON>", "Question": "<PERSON><PERSON><PERSON>", "Question {no}": "<PERSON><PERSON><PERSON> {no}", "Question {questionNumber}": "Klaus<PERSON>s {questionNumber}", "Questions": "<PERSON><PERSON><PERSON>", "Questions:": "<PERSON><PERSON><PERSON>:", "Quick call": "Greitas skambutis", "Quote": "Citata", "Quote author photo": "Citata autoriaus nuotrauka", "Quotes": "Citatos", "Racetracks": "Lenktynių trasos", "Radio and Television Broadcasting": "Radijo ir televizijos transliacijos", "Radio engineer": "<PERSON><PERSON><PERSON>", "Rail Transportation": "Geležinkelių transportas", "Railroad Equipment Manufacturing": "Geležinkelio įrangos gamyba", "Ranching": "Ranching", "Ranching and Fisheries": "Rankiojimas ir žu<PERSON>ė", "Range": "Diapazonas", "Rate the candidate:": "Įvertinti kandidatą:", "Rating": "Įvertinimas", "Rating saved.": "Įvertinimas i<PERSON><PERSON><PERSON><PERSON><PERSON>.", "Reach": "Pasiekite", "Read activities": "<PERSON><PERSON><PERSON><PERSON>", "Read audit logs": "Skaitykite audito žurnalus", "Read candidates": "<PERSON><PERSON><PERSON><PERSON> kandidatus", "Read database dumps": "Skaityti duomenų bazių išklotines", "Read feeds": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>us", "Read landing statistics": "Skaityti pu<PERSON>pio statistik<PERSON>", "Read more about AI-generated transcripts and summaries {asyncLink}.": "Daugiau informacijos apie dirbtinio intelekto generuojamus nuorašus ir santraukas rasite {asyncLink}.", "Read more about asynchronous video interviews {asyncLink}.": "Daugiau apie asinchroninius vaizdo interviu skaitykite {asyncLink}.", "Read more about cNPS": "Sužinokite daugiau apie cNPS", "Read more about cNPS.": "Sužinokite daugiau apie cNPS.", "Read more about internal forms": "Skaityti daugiau apie vidines formas", "Read project status reports": "Skaityti projekto būsen<PERSON> ataskaitas", "Read projects": "Skaityti projektus", "Read settings": "<PERSON><PERSON><PERSON><PERSON> nustatymus", "Reading candidate profile...": "<PERSON><PERSON><PERSON><PERSON> kandidato profilis...", "Ready to answer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Ready! Set! Hire!": "Pasiruošti! Dėmesio! Įdarbinti!", "Real Estate": "Nekilnoja<PERSON><PERSON> turtas", "Real Estate and Equipment Rental Services": "Nekilnojamojo turto ir įrangos nuomos paslaugos", "Real estate": "Nekilnoja<PERSON><PERSON> turtas", "Real estate agent": "Nekilnojamojo turto agentas", "Reason": "Priežastis", "Reason for candidate to drop out of applying": "<PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON> kurios kandidatas atsisakė teikti para<PERSON>š<PERSON>ą", "Reason for company to reject candidate": "<PERSON><PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON> kurios įmonė atmetė kandidatą", "Reason for consent revocation": "<PERSON><PERSON><PERSON><PERSON> atšaukimo priežastis", "Reason for hiring:": "Įdarbinimo priežastis:", "Reassign to...": "<PERSON><PERSON> na<PERSON> p<PERSON>...", "Received SMS": "Gautos SMS žinutės", "Received message via integration": "<PERSON><PERSON><PERSON> per integraciją", "Receptionist, doorman": "<PERSON><PERSON>, durininkas", "Recommended attempts exhausted": "Rekomenduojami bandymai išnaudoti", "Record": "Įrašas", "Record activity:": "Įrašykite veiklą:", "Record new activity": "Įrašyti naują veiklą", "Record new video": "Įrašykite naują vaizdo įrašą", "Record response": "Įrašyti atsakymą", "Record video": "Įrašyti vaizdo įrašą", "Recorded call placeholder": "Įrašyto skambu<PERSON><PERSON> viet<PERSON>", "Recording stopped automatically after time ran out.": "Įrašymas automatiškai sustojo pasibaigus laikui.", "Records all candidate data accesses and modifications. The logs are retained according to your internal policies.": "Registruoja visus kandidatų prieigos prie duomenų ir pakeitimus. Registras saugomas pagal jūsų vidaus politiką.", "Recreate project list dropdowns": "Atkurkite projektų sąrašo išskleidžiam<PERSON><PERSON> meniu", "Recreational Facilities": "Laisvalaikio įrenginiai", "Recruiter": "Įdarbintojas", "Recruiter contacts": "Įdarbintojo kontaktai", "Recruiters": "Įdarbintojai", "Recruitment": "Įdarbinimas", "Recruitment Performance": "Atrankų rezultatai", "Recruitment performance": "Atrankų statistika", "Recruitment software invite": "Kvietimas naudotis įdarbinimo programine įranga", "Redirect URL": "Peradresavimo URL", "Redo": "<PERSON><PERSON><PERSON><PERSON>", "Reference Checks": "Rekomendacijų patikrini<PERSON>", "Reference form": "Rekomendacijos forma", "Reference forms": "Rekomendacijos formos", "Reference request sent": "Išsiųstas prašymas pateikti rekomendaciją", "Reference submitted on {submissionTime}": "Rekomendacija pateikta {submissionTime}", "Reference submitted on {submissionTime} by {submissionUser}": "Rekomendacija pateikta {submissionTime}, o j<PERSON> patei<PERSON> {submissionUser}", "Reference type": "Rekomendacijos tipas", "References": "Rekomendacijos", "References pending first": "Pirmiausia laukiančios rekomen<PERSON>jos", "References submitted first": "Pirmiausia pateiktos rekomendacijos", "Referent": "Referentas", "Referral budget (€)": "Rekomendavimo biudžetas (€)", "Referral fee percentage": "Rekomendaci<PERSON> m<PERSON><PERSON> pro<PERSON> da<PERSON>", "Refresh this page.": "Atnaujinkite šį puslapį.", "Regards": "<PERSON><PERSON><PERSON>", "Regenerate": "<PERSON><PERSON><PERSON><PERSON>", "Regenerate summary": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Region": "Regionas", "Region (CV-Library)": "Regionas (CV-Library)", "Register": "<PERSON><PERSON><PERSON><PERSON>", "Register a new membership": "Užregistruoti naują narystę", "Regular user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reject": "<PERSON><PERSON><PERSON>", "Rejected": "Atmes<PERSON>", "Rejected at": "Atmes<PERSON>", "Relax!": "Atsipalaiduokite!", "Religious Institutions": "Religin<PERSON>s institucijos", "Reload": "<PERSON><PERSON><PERSON>", "Relocate from EU": "Persikėlimas iš ES", "Relocate from eu": "Persikelti iš ES", "Remember Me": "<PERSON><PERSON><PERSON><PERSON> mane", "Remember, that the candidate will not receive notification of new slots, so you may wish to send them a message under their profile.": "<PERSON><PERSON><PERSON><PERSON>, kad kandi<PERSON> ne<PERSON>s p<PERSON> a<PERSON> nauju<PERSON> la<PERSON>, to<PERSON><PERSON><PERSON> gal<PERSON> norė<PERSON> nusiųsti žinutę į jo profilį.", "Reminder: Job Ad Builder Tool by Teamdash": "Priminimas: „Teamdash“ darbo skelbimų kūrimo įrankis", "Remote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Remote links": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nuo<PERSON>", "Remote project": "<PERSON><PERSON><PERSON><PERSON><PERSON> projekt<PERSON>", "Remote project ID:": "Nuotolinio projekto ID:", "Remove": "<PERSON><PERSON><PERSON><PERSON>", "Remove candidate": "<PERSON><PERSON><PERSON><PERSON> kandi<PERSON>ą", "Remove filter": "<PERSON><PERSON><PERSON><PERSON> filtrą", "Remove from favourites": "Pašalint<PERSON> iš m<PERSON>ių", "Remove from project": "<PERSON><PERSON>lint<PERSON> iš projekto", "Remove image": "Pašalinti vaizdą", "Remove import": "<PERSON><PERSON>lint<PERSON> importavi<PERSON>ą", "Remove link": "Pašalinti nuorodą", "Remove quote": "Pašalinti citatą", "Remove slot": "<PERSON>šim<PERSON><PERSON> p<PERSON>kim<PERSON>", "Remove this candidate from the event.": "Pašalinkite šį kandidatą iš susitikimo.", "Removed dropout reason": "<PERSON><PERSON>lint<PERSON> išmetimo p<PERSON>", "Removed from project": "<PERSON><PERSON><PERSON><PERSON> iš projekto", "Render failed at": "Atvaizdavi<PERSON>", "Renew token": "Atnaujinti <PERSON>", "Renewable Energy Semiconductor Manufacturing": "Atsinaujinančiosios energijos puslaidininkių gamyba", "Renewal link is valid for": "Atnaujinimo nuoroda galioja", "Renewal text": "<PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Repair and Maintenance": "Remontas ir priežiūra", "Report generation started": "<PERSON><PERSON><PERSON><PERSON><PERSON> ataskaitų generavimas", "Require answers in strict order": "Reikalauti atsakymų nustatyta tvarka", "Require project failure reasons?": "Reikalauti projekto nesėkmės priežasčių?", "Required field": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Required languages": "Reikalingos kalbos", "Requirements": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Requirements section title": "Reikalavimų skyriaus pavadinimas", "Requisition Attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Requisition custom fields": "Užklausos pasirinktiniai laukai", "Requisition info field template": "Užklausos laukų šablonas", "Requisitions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reschedule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Reschedule time slot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Research": "Moksliniai tyrimai", "Research Services": "Tyrimų paslaugos", "Resend invite": "Persiųsti kvietimą", "Reset": "<PERSON><PERSON> n<PERSON>", "Reset Password": "<PERSON><PERSON> na<PERSON> nustat<PERSON>i slaptažodį", "Reset Password Notification": "Pranešimas apie slaptažodžio atstatymą", "Residential Building Construction": "Gyvenamųjų namų statyba", "Resolved task": "Išspręsta uždu<PERSON>", "Responded": "Atsakė", "Response": "Atsakymas", "Response rate": "Atsakymų dažnis", "Response time limit": "<PERSON><PERSON><PERSON><PERSON> laiko <PERSON>a", "Response time limit: {timeLimit}": "<PERSON><PERSON><PERSON><PERSON> laiko riba: {timeLimit}", "Responses": "Atsakymai", "Restaurants": "Restoranai", "Restore": "Atkurti", "Retail": "Mažmeninė <PERSON>", "Retail Apparel and Fashion": "Mažmeninė <PERSON> ir mada", "Retail Appliances, Electrical, and Electronic Equipment": "Maž<PERSON>inė <PERSON> p<PERSON>tai<PERSON>, elektros ir elektronine įranga", "Retail Art Dealers": "Mažmeninės prekybos meno kūriniais atstovai", "Retail Art Supplies": "Mažmeninė prekyba meno re<PERSON>", "Retail Books and Printed News": "Mažmeninė prekyba knygomis ir spausdintom<PERSON> nau<PERSON>", "Retail Building Materials and Garden Equipment": "Mažmeninė prekyba statybinėmis medžiagomis ir sodo įranga", "Retail Florists": "Mažmeninės prekybos gėlininkai", "Retail Furniture and Home Furnishings": "Mažmeninė prekyba baldais ir namų apyvokos reikmenimis", "Retail Gasoline": "Mažmeninė <PERSON>", "Retail Groceries": "Mažmeninė prekyba maisto produktais", "Retail Health and Personal Care Products": "Mažmeninė prekyba sveikatos ir asmens higienos produktais", "Retail Luxury Goods and Jewelry": "Mažmeninė prekyba prabangos prekėmis ir juvelyriniais dirbiniais", "Retail Motor Vehicles": "Mažmeninė prekyba variklinėmis transporto priemonėmis", "Retail Musical Instruments": "Mažmeninė prekyba muzikos instrumentais", "Retail Office Equipment": "Mažmeninė prekyba biuro įranga", "Retail Office Supplies and Gifts": "Mažmeninė prekyba biuro re<PERSON> ir do<PERSON>", "Retail Recyclable Materials & Used Merchandise": "Mažmeninė prekyba perdirbamomis medžiagomis ir naudotomis prek<PERSON>", "Retail/Purchasing": "Mažmeninė <PERSON> / pirkimas", "Retrieved full profile": "Gautas visas profilis", "Retry": "<PERSON><PERSON><PERSON><PERSON>", "Retry meeting analysis": "Pakartokite susitikimo analizę", "Retype password": "Pakartoti slaptažodį", "Reupholstery and Furniture Repair": "Baldų apmušimas ir remontas", "Review": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Review approvals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Review integrations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>te<PERSON>", "Review now": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Revoke": "<PERSON><PERSON><PERSON><PERSON>", "Revoke consent": "<PERSON><PERSON><PERSON><PERSON>", "Revoked by": "Atšaukė", "Right": "<PERSON><PERSON><PERSON><PERSON>", "Right button link override": "<PERSON><PERSON><PERSON><PERSON><PERSON> mygtuko nuorod<PERSON> p<PERSON>iti<PERSON>", "Role": "<PERSON><PERSON><PERSON><PERSON>", "Role for new users": "<PERSON><PERSON><PERSON><PERSON> naudotojų vaidmuo", "Romanian": "Rumunų kalba", "Romansh": "Retoromanų kalba", "Roofer": "Stogdengys", "Room address": "<PERSON><PERSON><PERSON>", "Room address:": "Ka<PERSON><PERSON> adres<PERSON>:", "Room assignment successful!": "Kambario priskyrimas pavyko!", "Rows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rubber Products Manufacturing": "Gumos gaminių gamyba", "Rules": "Taisykl<PERSON><PERSON>", "Run an AI-assisted social media campaign for attracting passive talent.": "Vyk<PERSON>ti dirbtinio intelekto palaikomą socialinės žiniasklaidos kampaniją pasyviems talentams pritraukti.", "Run check": "<PERSON><PERSON><PERSON> pat<PERSON>", "Run now": "<PERSON><PERSON><PERSON><PERSON>", "Run related imports": "Vykdyti susijusį importavimą", "Rundi": "<PERSON><PERSON> kalba", "Russian": "Rusų kalba", "SCIM group mappings": "SCIM grupės <PERSON>s", "SCIM group synchronisation follows these rules:": "SCIM grupės sinchronizavimas vadovaujasi šiomis taisyklėmis:", "SCIM groups and roles": "SCIM grupės ir v<PERSON>ys", "SES": "SES", "SMS": "SMS", "SMS body": "SMS tekstas", "SMS confirmation": "Patvirtinimas SMS žinute", "SMS confirmation message": "SMS pat<PERSON><PERSON><PERSON>", "SMS message": "SMS žinutė", "SMS sent!": "Išsiųsta SMS žinutė!", "SSO": "SSO", "SSO is enabled!": "SSO įjungtas!", "Sailor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Salary": "Atlyginimas", "Salary currency": "Darbo užmokesčio valiuta", "Salary from": "Atlyginimas nuo", "Salary from (gross)": "Darbo užmokestis nuo (bruto)", "Salary info": "Informacija apie darbo užmokestį", "Salary period": "<PERSON><PERSON>žmo<PERSON><PERSON>", "Salary range": "Darbo užmokesčio intervalas", "Salary rate": "Darbo užmokesčio d<PERSON>", "Salary to": "Atlyginimas iki", "Salary to (gross)": "Darbo užmokestis i<PERSON> (bruto)", "Sales": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sales / Retail": "Pardavimas / Mažmeninė <PERSON>kyba", "Sales Assistants": "Pardavimų asistentai", "Sales Manager, Head of Department, Chief Accountant, Marketing Manager, Construction Manager...": "Parda<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Statybos vado<PERSON>...", "Salesman, Cashier, Waiter, Hotel Administrator, Security Guard...": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...", "Samoan": "Samoa kalba", "Sample text": "<PERSON>ks<PERSON> p<PERSON>", "Sango": "Sangų kalba", "Sanitary technician": "Santechnikas", "Sanskrit": "Sanskritas", "Sardinian": "Sardų kalba", "Satellite Telecommunications": "Palydovinės telekomunikacijos", "Saturday": "Šeštadienis", "Save": "<PERSON>š<PERSON>ug<PERSON><PERSON>", "Save and close": "Išsaugoti ir uždaryti", "Save and next": "Išsaugoti ir ž<PERSON><PERSON><PERSON><PERSON><PERSON>", "Save as new": "Išsaugot<PERSON> kaip nauj<PERSON>", "Save as template": "Išsaugoti ka<PERSON>", "Save candidate and add to current project": "Išsaugoti kandidatą ir pridėti prie dabartinio projekto", "Save changes": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Save or update active filters for later use.": "Išsaugoti arba atnaujinti aktyvius filtrus vėlesniam naudojimui.", "Save project action": "Išsaugoti projekto veiksmą", "Save stage action": "Išsaugoti etapo veiksmą", "Saved": "Išsaugota", "Saved filters": "Išsaugoti filtrai", "Saved!": "Išsaugota!", "Saved.": "Išsaugota.", "Saving draft...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>..", "Saving...": "Išsaugoma...", "Savings Institutions": "Taupy<PERSON> įstaigos", "Sawyer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Scan the QR code.": "Nuskaitykite QR kodą.", "Scandinavia": "Skandinavija", "Schedule a Consultation": "Suplanuokite konsultaciją", "Schedule an interview or upload a video.": "Suplanuokite interviu arba įkelkite vaizdo įrašą.", "Schedule interview": "<PERSON><PERSON><PERSON><PERSON><PERSON> pokalbį", "Schedule interviews": "<PERSON><PERSON><PERSON><PERSON><PERSON> pokal<PERSON>", "Scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Scheduled meetings:": "<PERSON><PERSON><PERSON><PERSON><PERSON> susi<PERSON><PERSON>:", "Scheduling type": "<PERSON>avi<PERSON> tip<PERSON>", "School and Employee Bus Services": "Mokykinių ir darbuotojų autobusų paslaugos", "Science": "<PERSON><PERSON><PERSON>", "Scientific": "<PERSON><PERSON><PERSON><PERSON>", "Score": "Rezultatai", "Scorecard": "<PERSON><PERSON><PERSON><PERSON>", "Scorecard comments": "Vertinimo kortelės komentarai", "Scorecard summary": "Vertinimo kortelių suvestinė", "Scorecards": "<PERSON><PERSON><PERSON><PERSON>", "Scored candidate": "Balų surinkęs kandi<PERSON>s", "Screening questions": "<PERSON><PERSON><PERSON>", "Scroll to zoom in or out": "Slinkite, kad priartintumėte arba nutolintumėte", "Seafood Product Manufacturing": "Jūros gėrybių produktų gamyba", "Seaman": "J<PERSON>rini<PERSON><PERSON>", "Seamstress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search": "Ieškoti", "Search by image name...": "Paieška pagal paveiksliuko pavadinimą...", "Search by name, tags, file contents, etc...": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>, <PERSON><PERSON><PERSON>, failo turinį ir t. t.", "Search by video name...": "Paieška pagal vaizdo įrašo pavadinimą...", "Search candidate": "Ieškoti kandidato", "Search here.": "Ieškoti čia.", "Search user": "Ieškoti naudotojo", "Search...": "Pa<PERSON>š<PERSON>...", "Seasonal": "<PERSON><PERSON><PERSON><PERSON>", "Secondary Colour": "<PERSON><PERSON><PERSON><PERSON> spalva", "Secondary Colour:": "<PERSON><PERSON><PERSON><PERSON> spalva:", "Secretarial Schools": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Secretary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Section with links to your social media pages": "Skyrelis su nuorodomis į jūsų socialinių paskyrų puslapius", "Section with recruiter contacts": "Skyrius su įdarbintojų kontaktais", "Sector type": "Sekt<PERSON><PERSON> tipas", "Sectors (profession.hu)": "<PERSON><PERSON><PERSON><PERSON> (profession.hu)", "Securities and Commodity Exchanges": "Vertybinių popierių ir prekių biržos", "Security": "<PERSON><PERSON><PERSON><PERSON>", "Security & compliance": "<PERSON><PERSON><PERSON><PERSON> ir atitiktis", "Security / Rescue / Defence": "Saugumas / Gelbėjimas / Gynyba", "Security Guards and Patrol Services": "Apsaugos darbuotojų ir patruliavimo paslaugos", "Security Systems Services": "Apsaugos sistemų paslaugos", "Security and Investigations": "<PERSON><PERSON><PERSON><PERSON> ir tyr<PERSON>i", "Security guard": "Apsaugos <PERSON>", "Security services": "Apsau<PERSON> p<PERSON>", "See more...": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "See you soon!": "Iki pasimatymo!", "Select": "<PERSON><PERSON><PERSON><PERSON>", "Select a Block": "Pasirinkti blo<PERSON>ą", "Select a category": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Select a font": "Pasirinkite šriftą", "Select a reason...": "Pasirinkite priežastį...", "Select a reference form": "Pasirinkite rekomendacijos formą", "Select a room": "Pa<PERSON>ink<PERSON> kambarį", "Select a template": "Pasirink<PERSON>", "Select a thread to view messages.": "Pasirinkite temą, nor<PERSON><PERSON><PERSON>.", "Select a time": "<PERSON><PERSON><PERSON><PERSON>", "Select all": "<PERSON><PERSON><PERSON><PERSON> visus", "Select all {count} candidates": "Pasirinkite visus {count} kandidatus (-ų)", "Select an action from the left to add to the stage.": "Pasirinkite veiksmą iš <PERSON>, kurį norite įtraukti į etapą.", "Select an option from the left to start your new project.": "Norėdami pradėti naują projektą, pasirinkite parinktį iš ka<PERSON>.", "Select another profile to merge with {candidateName}": "Pasirinkite kitą profilį, su kuriuo norite sujungti {candidateName}", "Select candidates to merge": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>, kad būt<PERSON> galima juos sujungti", "Select interview": "Pa<PERSON>ink<PERSON> pokalbį", "Select interview to see preview.": "Pasirinkite pokalbį, kad pamaty<PERSON>te perž<PERSON>ūr<PERSON>.", "Select none": "Nesirinkti nė vieno", "Select users to add": "Pasirinkite naudotoju<PERSON>, k<PERSON>uos norite p<PERSON>", "Select video": "Pasirinkite vaizdo įrašą", "Selected candidate is already in the chosen project. | Selected candidates are already in the chosen project.": "Pasirinktas kandidatas jau yra pasirinktame projekte. | Pasirinkti kandidatai jau yra pasirinktame projekte.", "Selected date": "Pasirinkta data", "Selected fields": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "Selected time": "Pasirink<PERSON> laikas", "Self-Managed Backups": "Savarankiškai valdomos atsarginės kopijos", "Seller": "Pardavė<PERSON>", "Semiconductor Manufacturing": "Puslaidininkių gamyba", "Send": "Si<PERSON>sti", "Send Password Reset Link": "Siųsti slaptažodžio atstatymo nuorodą", "Send SMS": "Siųsti SMS", "Send a message to project members": "Siųsti pranešimą projekto nariams", "Send a message to start the conversation.": "Norėdami p<PERSON>ėti pokalbį, siųskite žinutę.", "Send a message to the candidate": "Siųsti žinutę kandidatui", "Send a message to {candidateName}": "<PERSON><PERSON><PERSON><PERSON> {candidateName}", "Send a message to {count} candidates": "<PERSON><PERSON><PERSON><PERSON> ž<PERSON> {count} kandidatams (-ų)", "Send a message to {count} users": "<PERSON><PERSON><PERSON><PERSON> {count} naudotojams", "Send a message to {userName}": "Sių<PERSON><PERSON> {userName}", "Send a reference check request to the candidate's referees": "Išsiųsti rekomendacijų patikrinimo prašymą kandidato rekomendacijos davėjams", "Send a video interview to the candidate": "Siųskite kandidatui vaizdo pokalbį", "Send a video message to the candidate": "Siųsti vaizdo žin<PERSON>ę kandidatui", "Send a video message to {candidateName}": "<PERSON>ų<PERSON><PERSON> v<PERSON> {candidateName}", "Send a video message to {count} candidates": "<PERSON>ų<PERSON><PERSON> vai<PERSON> {count} kandidatams (-ų)", "Send an SMS to {candidateName}": "Siųsti SMS žinutę {candidateName}", "Send an SMS to {count} candidates": "Siųsti SMS žinutę {count} kandidatams (-ų)", "Send an interview scheduling request to the candidate": "Siųsti kandidatui prašymą suplanuoti pokalbį.", "Send applicants to": "Siųsti pareiškėjus", "Send as": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Send candidates": "<PERSON><PERSON><PERSON><PERSON> kandidatus", "Send consent renewal messages": "Siųsti sutikimo pratęsimo p<PERSON>", "Send consent renewals": "Siųsti sutikimo pratęsimus", "Send homework assignments, screening questions, consent requests or ask for any additional data from candidates.": "Siųskite namų darbų už<PERSON>, atrank<PERSON> klausimus, pra<PERSON><PERSON>us duoti sutikimą arba prašykite bet kokios papildomos informacijos iš kandidatų.", "Send interview": "Siųsti pokalbį", "Send invitations": "Siųsti kvietimus", "Send invites": "Siųsti kvietimus", "Send message": "Siųsti pranešimą", "Send out cNPS surveys and ask candidates for feedback.": "Išsiųskite cNPS apklausas ir paprašykite kandidatų atsiliepimų.", "Send out message at": "Išsiųsti pranešimą", "Send out the invite link as an SMS too and be sure the invite is seen. Text messages longer than 160 characters are automatically split into segments and then re-assembled when they are received. This allows you to send long SMS messages, but this increases your per-message cost because SMS is billed per segment.": "Kvietimo nuorodą išsiųskite ir kaip SMS žinutę ir įsitikinkite, kad kvietimas yra matomas. Ilgesni nei 160 simbolių tekstiniai pranešimai yra automatiškai suskirstomi į segmentus, o juos gavus – vėl sujungiami. Tai leidžia siųsti ilgas SMS žinutes, ta<PERSON><PERSON><PERSON> d<PERSON> to padidėja vienos žinutės kaina, nes SMS kaina mokama už kiekvieną segmentą.", "Send tentative scheduler time slots to interviewers": "<PERSON><PERSON><PERSON><PERSON> preliminarius tvar<PERSON><PERSON><PERSON> sudarymo laikus pokalbį vykdantiems asmenims", "Send this candidate a video interview invitation to get started.": "<PERSON><PERSON><PERSON><PERSON>, nusiųskite šiam kandidatui vaizdo pokalbio kvietimą.", "Send this candidate an invite to get started.": "<PERSON><PERSON>, išsiųskite šiam kandidatui kvietimą.", "Send to :recipient": "<PERSON><PERSON><PERSON><PERSON> :recipient", "Send to BambooHR ATS": "Siųsti į BambooHR ATS", "Send to {hrisName}": "Siųsti į {hrisName}", "Send video interview": "Siųsti vaizdo pokalbį", "Send video interview invites": "Siųsti vaizdo pokalbio kvietimus", "Send video message": "Siųsti vaizdo žin<PERSON>ę", "Send webhook": "<PERSON>ųsti \"webhook\"", "Sending report": "Ataskaitos siuntimas", "Sending your message...": "Siunčiamas jū<PERSON>ų pranešimas...", "Sending...": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "Senior Manager / Director": "<PERSON><PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON>", "Sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sent SMS": "SMS žinutė išsiųsta", "Sent at": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Sent message": "Išsiųsta žinutė", "Sent to HRIS": "Išsiųsta į HRIS", "September": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Serbian": "Serbų kalba", "Server Error": "<PERSON><PERSON> k<PERSON>", "Service & Sales Representative": "Aptarna<PERSON><PERSON> ir pardavimo at<PERSON>", "Service account": "Paslaugų darbuotojas", "Service industry": "Paslaugų pramonė", "Service user": "Paslaugų naudotojas", "Service worker": "Paslaugų srities darbuotojas", "Services": "Paslaugos", "Services for the Elderly and Disabled": "Paslaugos pagyvenusiems žmonėms ir neįgaliesiems", "Set as new project manager": "Nustatyti kaip naują projekto vadovą", "Set automatic stage action": "Nustatyti automatinį etapo veiksmą", "Set dates": "Nustatyti datas", "Set type to {type}": "Nustatyti tipą į {type}", "Set up Azure AD SSO": "Nustatyti „Azure AD“ SSO", "Set up on": "<PERSON>ustat<PERSON><PERSON>", "Settings": "Nustatymai", "Setup": "Sąranka", "Setup instructions": "Sąrank<PERSON> instruk<PERSON>", "Share": "<PERSON><PERSON>", "Share on Facebook": "Dalytis „Facebook“", "Share on LinkedIn": "Dalytis „LinkedIn“", "Share to": "<PERSON><PERSON>", "Share with": "<PERSON><PERSON> su", "Share with Teamdash users?": "<PERSON><PERSON><PERSON> \"Team<PERSON>h\" naudo<PERSON><PERSON><PERSON>?", "Share with users": "Dalintis su naudotojais", "Shared by": "<PERSON><PERSON><PERSON><PERSON>", "Shared candidate": "<PERSON><PERSON> kandidatas", "Shared candidates": "<PERSON><PERSON> ka<PERSON>", "Shared mailbox address": "<PERSON><PERSON><PERSON> p<PERSON>", "Shared with": "<PERSON><PERSON><PERSON><PERSON> su", "Sharing preview photo": "<PERSON><PERSON><PERSON><PERSON>", "Sheet Music Publishing": "Muzikos natų leidyba", "Shift-click on a team and drag to another team to connect them with a directed edge": "<PERSON><PERSON><PERSON><PERSON><PERSON> „Shift“ klavi<PERSON>, spustelėkite komandą ir vilkite ją link kitos komandos, kad sujungtumėte jas kryptiniu kraštu", "Shift-click on team to change its label": "Spustelėkite Shift ir pasirinkite komandą, kad pakeistumėte jos ž<PERSON>", "Shift-click on whitespace to create a team": "<PERSON><PERSON><PERSON><PERSON><PERSON> „Shift“ klavi<PERSON>, spustelėkite tuščią vietą, norėdami sukurti komandą", "Shipbuilding": "Laivų statyba", "Shoe size": "Batų dydis", "Shoemaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shona": "Šonų kalba", "Short message": "<PERSON><PERSON>", "Short name": "<PERSON><PERSON> pavadin<PERSON>", "Short name for the consent type. Max 4 characters. We suggest an emoji.": "Trumpas sutikimo tipo pavadinimas. Ne daugiau kaip 4 simboliai. Si<PERSON>lome naudoti emoji.", "Show": "<PERSON><PERSON><PERSON>", "Show advanced filters": "<PERSON><PERSON><PERSON> išp<PERSON>ė<PERSON><PERSON> filtrus", "Show all personal data": "<PERSON><PERSON><PERSON> visus asmens duomenis", "Show all projects to limited users": "Rodyti visus projektus ribotą prieigą turintiems naudotojams", "Show archived": "<PERSON><PERSON><PERSON> archyvuotus", "Show candidate initials in blind hiring mode": "Rodyti kandidatų inicialus aklos atrank<PERSON> būdu", "Show candidate journeys": "Rodyti kandidatų keliones", "Show candidates with new activities": "<PERSON><PERSON><PERSON> kandidatus su naujomis veiklo<PERSON>", "Show comments indicator": "Rodyti komentarų indikatorių", "Show done tasks": "Rodyti atliktas užduotis", "Show email": "Rodyti el. paštą", "Show entire image": "Rodyti visą vaizdą", "Show filter": "<PERSON><PERSON><PERSON> filtr<PERSON>", "Show icon for location": "Rodyti vietos piktogramą", "Show if candidate is active in other projects": "<PERSON><PERSON><PERSON>, ar kandidatas yra aktyvus kituose projektuose", "Show images in grid": "<PERSON><PERSON><PERSON> vaiz<PERSON> tinkle<PERSON>", "Show inactive users": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON> var<PERSON>", "Show invites together": "Rod<PERSON>i kvietimus kartu", "Show location": "Rodyti vietą", "Show me the candidates": "<PERSON><PERSON><PERSON><PERSON> man kandidatus", "Show messages indicator": "Rodyti žinučių indikatorių", "Show non-public project logs to limited users": "Rodyt<PERSON> neviešus projekto žurnalus ribotą prieigą turintiems naudotojams", "Show on candidate card": "<PERSON><PERSON><PERSON> kandi<PERSON> k<PERSON>", "Show on job ad": "<PERSON><PERSON><PERSON> da<PERSON>o s<PERSON>", "Show only my tasks": "<PERSON><PERSON><PERSON> tik mano u<PERSON>", "Show other active candidacies": "Rodyti kitas aktyvias kandi<PERSON>", "Show pending/answered video interview indicator": "Rod<PERSON>i <PERSON>č<PERSON> / atsakyto vaizdo pokalbio indikatorių", "Show pending/scheduled interview indicator": "<PERSON><PERSON>i <PERSON>č<PERSON> / suplanuoto pokalbio indikatorių", "Show pending/submitted references indicator": "Rodyti laukiančių / pateiktų rekomendacijų indikatorių", "Show personal data alongside AI-generated summary": "<PERSON><PERSON><PERSON> asmeninius duomenis kartu su DI sugeneruota santrauka", "Show phone": "Rodyti telefoną", "Show question": "<PERSON><PERSON><PERSON>", "Show source": "<PERSON><PERSON><PERSON> kanal<PERSON>", "Show stage categories": "<PERSON><PERSON><PERSON> etap<PERSON> ka<PERSON>gor<PERSON>", "Show talent pool": "Rodyti talentų duombazę", "Show tasks from all projects": "Rodyti užduotis iš visų projektų", "Show weekends": "<PERSON><PERSON><PERSON>", "Shows how candidates are divided across stage categories at specific moments in time": "<PERSON><PERSON><PERSON>, kaip kandidatai pasiskirsto pagal etapo kategorijas tam tikrais laiko momentais.", "Shuttles and Special Needs Transportation Services": "Pervežimai ir specialiųjų poreikių transporto paslaugos", "Sick-nurse": "Ligonių slaugytojas", "Sightseeing Transportation": "Ekskursinis transportas", "Sign in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sign into your Indeed account": "Prisijungti prie „Indeed“ paskyros", "Signature accent color": "Parašo akcento spalva", "Signature additional text": "Pa<PERSON><PERSON><PERSON> paraš<PERSON> te<PERSON>", "Signature preview": "<PERSON><PERSON><PERSON>", "Silkscreen operator": "Šilkografas", "Similarity to selected": "Panašumas į pasirinktą", "Simplify language": "Supaprastinti kalbą", "Since this is an SMS, keep it short.": "Kadangi tai yra <PERSON> žinutė, raš<PERSON><PERSON> trumpai.", "Sindhi": "Sindų kalba", "Single Sign-On": "<PERSON><PERSON><PERSON><PERSON><PERSON> pris<PERSON>", "Single stage": "Vienas etapas", "Sinhala, Sinhalese": "Sinhalų kalba", "Size": "<PERSON><PERSON><PERSON>", "Skiing Facilities": "Slidinėjimo įranga", "Skilled Labor": "Kvalifikuota darbo j<PERSON>ga", "Skilled Worker / Professional": "Kvalifikuotas darbuotojas / Profesionalas", "Skilled worker": "Kvalifikuotas darbuotojas", "Skills": "Įgūdžiai", "Skip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Skip this month": "Praleisti šį mėnesį", "Slots": "<PERSON><PERSON><PERSON> laiko tarpai", "Slovak": "Slovakų", "Slovenian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Slug": "<PERSON><PERSON><PERSON>", "Sms sent!": "SMS žinutė išsiųsta!", "Soap and Cleaning Product Manufacturing": "Muilo ir valymo priemonių gamyba", "Social Care": "<PERSON>in<PERSON>", "Social Networking Platforms": "Socialinių tinklų platformos", "Social media": "<PERSON><PERSON><PERSON>", "Social media sharing image": "Socialinės ž<PERSON>laidos dalijimosi vaizdas", "Social worker": "<PERSON><PERSON><PERSON>", "Software Development": "Programinės įrangos kūrimas", "Solar Electric Power Generation": "<PERSON><PERSON><PERSON> elektros energijos gam<PERSON>", "Somali": "Somalių kalba", "Some candidates were not be copied.": "<PERSON> kurie kandidatai nebuvo nukopijuoti.", "Some inactive users are selected. Deselect them to hide inactive users.": "Pasirinkti kai kurie neaktyvūs vartotojai. Panaikinkite jų p<PERSON>, kad paslėptumėte neaktyvius vartotojus.", "Some invites can't be delivered via selected channels.": "Kai kurių kvietimų negalima pristatyti pasirinktais kanalais.", "Someone": "<PERSON><PERSON><PERSON>", "Someone applied via email?": "Ka<PERSON> nors k<PERSON>si el. paš<PERSON>?", "Someone has booked this time already": "<PERSON><PERSON><PERSON> jau rezervavo šį laiką", "Something went wrong!": "<PERSON><PERSON><PERSON> nutiko ne taip!", "Something went wrong! Please try again.": "<PERSON><PERSON><PERSON> nutiko ne taip! Bandykite dar kartą.", "Sort by": "Rūšiuoti pagal", "Sort by:": "Rūšiuoti pagal:", "Sort candidates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kandidatus", "Sort manually": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rankiniu būdu", "Sorter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sotho, Southern": "Pietų <PERSON> kalba", "Sound Recording": "Garso įrašymas", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Source tags": "<PERSON><PERSON><PERSON><PERSON>", "South Ndebele": "Pietų ndebelių kalba", "Space Research and Technology": "Kosmoso tyrimai ir technologijos", "Spanish": "Ispanų kalba", "Spanish, Castilian": "Ispanų arba kastilų kalba", "Specialist": "Specialistas", "Specialty Trade Contractors": "Specialiosios preky<PERSON>", "Specific form": "Specif<PERSON><PERSON> forma", "Specific height": "<PERSON><PERSON><PERSON><PERSON>", "Specific slots": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "Specifying the language may improve transcription accuracy. If left unspecified, language will be detected automatically.": "Kalbos nurodymas gali pagerinti nuorašo tikslumą. Jei nenurodyta, kalba bus aptikta automatiškai.", "Spectator Sports": "Stebimasis sportas", "Spoken language": "Šnekamoji kalba", "Sporting Goods Manufacturing": "Sporto prekių gamyba", "Sports Teams and Clubs": "Sporto komandos ir klubai", "Sports and Recreation Instruction": "Sporto ir poilsio mok<PERSON>", "Spring and Wire Product Manufacturing": "Spyruoklių ir vielos gaminių gamyba", "Staffing and Recruiting": "<PERSON>o p<PERSON>š<PERSON> ir atranka", "Stage": "Etapas", "Stage (Teamdash)": "Etapas (Teamdash)", "Stage ID": "Etapo ID", "Stage ID (Teamdash)": "Etapo <PERSON> (Teamdash)", "Stage action added!": "Pridėtas etapo veiksmas!", "Stage category": "<PERSON><PERSON><PERSON>", "Stage deleted successfully.": "Etapas iš<PERSON>.", "Stage header dropdown menu": "<PERSON><PERSON><PERSON> i<PERSON>ž<PERSON> meniu", "Stage hidden from limited users": "Etapas nerodomas ribotiems naudotojams ", "Stage name": "<PERSON><PERSON><PERSON> p<PERSON>", "Stage visibility settings": "<PERSON><PERSON><PERSON> matomumo nustat<PERSON>", "Stages": "Etapai", "Stages & actions": "Etapai ir ve<PERSON>i", "Stages and actions will be automatically created based on the template you select.": "Etapai ir veiksmai bus automatiškai sukurti pagal pasirinktą šabloną.", "Standard campaign": "\"Standard\" kampanija", "Standard fields": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>s", "Start": "Pradžia", "Start Date": "Pradžios data", "Start SSO setup": "SSO sąrankos paleidimas", "Start a new AI chat with this interview": "Pradėkite naują DI pokalbį apie šį interviu", "Start a project": "Pradėti projektą", "Start date": "Pradžios data", "Start interview": "Pradėti interviu", "Start month": "<PERSON><PERSON><PERSON><PERSON>", "Start recording": "Pradėkite įrašymą", "Start tracking your candidate experience using cNPS surveys.": "<PERSON><PERSON><PERSON><PERSON> stebėti kandidato patirtį naudojant cNPS apklausas.", "Start year": "Prad<PERSON><PERSON>", "Started: {date}": "Pradėta: {date}", "State": "<PERSON><PERSON><PERSON><PERSON>", "Static text": "Statinis teks<PERSON>", "Statistics": "Statistika", "Statistics for {landingName}": "{landingName} statistika", "Status": "<PERSON><PERSON><PERSON><PERSON>", "Status: Enabled": "Būsena: Įjungta", "Steam and Air-Conditioning Supply": "<PERSON><PERSON> ir oro kondicionavimo įrangos tiekimas", "Steward": "Stiuardas", "Stomatologist": "Odontologas", "Stop recording": "Sustabdyti įrašymą", "Store Management": "Parduotuvė<PERSON> vadyba", "Store the cleaned transcript segments. Should be called with an array in which each object represents a cleaned-up transcript segment.": "Saugokite išvalytus nuorašo segmentus. Reikėtų iškviesti su ma<PERSON>vu, kuriame kiekvienas objektas yra išvalytas nuorašo segmentas.", "Store the interview summary in a structured format. Should be called with an array in which each element is a string containing a summarization sentence.": "Išsaugokite interviu santrauką struktūrizuotu formatu. Turėtų būti iškviečiamas su masyvu, kuriame kiekvienas elementas yra eilutė su apibendrinimo sakiniu.", "Storekeeper": "Sand<PERSON>lininka<PERSON>", "Stovemaker": "Viryklių gamintojas", "Strategic Management Services": "Strateginio valdymo p<PERSON>", "Strategy / Planning": "Strategija / planavimas", "Strikethrough": "<PERSON><PERSON><PERSON><PERSON>", "Stripper": "<PERSON><PERSON><PERSON>", "Structured Ad Image": "Struktūrizuotas skelbimo vaizdas", "Structured job ads": "Strukt<PERSON><PERSON><PERSON><PERSON> da<PERSON>o s<PERSON>", "Student": "Studentas", "Styling": "<PERSON><PERSON><PERSON>", "Subdivision of Land": "<PERSON><PERSON><PERSON><PERSON>", "Subject": "<PERSON><PERSON>", "Submission": "<PERSON><PERSON><PERSON><PERSON>", "Submission for {formName}": "Pateikta {formName}", "Submissions": "Paraiškos", "Submissions for :form": "Pateiktos paraiškos :form", "Submissions: :form": "Pateikimai: :forma", "Submit": "Pat<PERSON><PERSON><PERSON>", "Submit answer": "Pateikti atsakymą", "Submit button label": "<PERSON><PERSON><PERSON><PERSON> \"Pateikt<PERSON>\" žyma", "Submit reference": "Pateikti rekomendaciją", "Submit reference from {referenceName}": "Pateikti rekomendaciją nuo {referenceName}", "Submitted a form": "Pateikta forma", "Submitted for {positionName} on {uploadTime}": "Pateikta {positionName} {uploadTime}", "Submitted referees": "Pateikti rekomendacijų davėjai", "Submitted reference": "Pateikta rekomendacija", "Submitting": "<PERSON><PERSON><PERSON><PERSON>", "Success": "Sėkmė", "Success text": "Pavykusio veiksmo tekstas", "Success!": "<PERSON><PERSON><PERSON>!", "Sugar and Confectionery Product Manufacturing": "Cukraus ir konditerijos gaminių gamyba", "Summarization failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sundanese": "Sundų kalba", "Sunday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Super campaign": "\"Super\" kampanija", "Supermarket/Food": "Prekybos centras / maisto produktai", "Supervisor": "<PERSON><PERSON><PERSON>", "Supply Chain": "<PERSON><PERSON><PERSON>", "Supports merge tags.": "<PERSON><PERSON><PERSON>.", "Surname": "Pa<PERSON><PERSON>", "Survey": "<PERSON><PERSON><PERSON><PERSON>", "Survey form": "<PERSON><PERSON><PERSON><PERSON><PERSON> forma", "Survey response": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Surveyor": "Geodezininkas", "Surveys sent": "Išsiųstos apklausos", "Swahili": "Svahilių kalba", "Swati": "Svazių kalba", "Swedish": "Švedų kalba", "Switch off asking for dropout reasons": "Išjungti klausimą apie iškritimo priežastis", "Switch to columns view": "Perjungti į stulpelių rodinį", "Switch to table view": "Perjungti į lentelę", "Sync all users": "Sinchronizuoti visus vartotojus", "T-shirt size": "Marškinėlių dydis", "TB": "TB", "TTF can only be calculated if your filters include finished projects.": "Laikas iki įdarbinimo galimas tik įtraukus baigtus projektus", "Table": "Lentelė", "Tag": "<PERSON><PERSON><PERSON>", "Tag name": "<PERSON><PERSON><PERSON>", "Tagalog": "Tagalų kalba", "Tags": "<PERSON><PERSON><PERSON>", "Tags to add": "<PERSON><PERSON><PERSON>", "Tahitian": "Taitiečių kalba", "Tailor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tajik": "Tadžik<PERSON> kalba", "Take fields from": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> i<PERSON>", "Talenme.com": "Talenme.com", "Talent Pool": "Kandidatų duombazė", "Talent.com Job category": "Talent.com darbų kategorija", "Tamil": "Tamilų kalba", "Target stage": "Tikslinis etapas", "Target tag": "Tikslinė žyma", "Task": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Task in project {project}": "Užduotis projekte {project}", "Task title": "Užduoties pavadinimas", "Task title:": "Užduoties pavadinimas:", "Tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tasks with deadline today:": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurių terminas baigiasi šiandien:", "Tatar": "Totorių kalba", "Taxi and Limousine Services": "Taksi ir limuzinų paslaugos", "Teacher": "Mokytojas", "Team": "<PERSON><PERSON><PERSON>", "Team settings:": "Komandos nustatymai:", "Team-Based Access Controls": "Komandos pagrindu veikianti prieigos kontrolė", "Team-based access controls make permission handling easier and facilitate managing multiple departments or brands under one account.": "Komandomis pagrįsta prieigos kontrolė supaprastina leidimų tvarkymą ir palengvina kelių skyrių ar prekės ženklų valdymą vienoje paskyroje.", "Teamdash Feed": "„Teamdash“ srautas", "Teamdash Report": "„Teamdash“ ataskaita", "Teamdash Social Media Tool distributes your job ad in social media. This helps you attract passive talent and reach a more diverse audience than just job board visitors.": "„Teamdash“ socialinės žiniasklaidos įrankis platina jūsų darbo skelbimą socialinėje žiniasklaidoje. Tai padės pritraukti pasyvius talentus ir pasiekti įvairesnę auditoriją nei tik darbo skelbimų lentos lankytojai.", "Teamdash Team": "„Teamdash“ komanda", "Teamdash is an easy-to-use recruitment software, a place you can manage all your recruitment projects with your team members.": "„Teamdash“ yra paprasta naudoti įdarbinimui skirta programinė įranga, kurioje galite valdyti visus įdarbinimo projektus kartu su savo komandos nariais.", "Teamdash will anonymize {candidateCount} {anonymizeDiff}.": "„Teamdash“ nuasmenins {candidateCount} {anonymizeDiff} .", "Teamdash will ask data processing consent renewals {candidateCount} {renewalDiff}.": "„Teamdash“ paprašys atnaujinti duomenų tvarkymo sutikimą {candidateCount} {renewalDiff}.", "Teamdash's GDPR automation helps you acquire, manage, document and monitor all your candidates' data processing consents. It works like this:": "„Teamdash“ BDAR automatizavimas padeda gauti, v<PERSON><PERSON><PERSON>, dokumentuoti ir stebėti visų kandidatų sutikimus dėl duomenų tvarkymo. Tai veikia taip:", "Teams": "<PERSON><PERSON><PERSON>", "Teams admins can modify the teams hierarchy and assign any user to any team.": "Komandų administratoriai gali keisti komandų hierarchiją ir priskirti bet kurį naudotoją bet kuriai komandai.", "Teams saved!": "Komand<PERSON>!", "Tech support": "Techninė pagalba", "Technical / Engineering": "Techninė / inžinerinė įranga", "Technical and Vocational Training": "Techninis ir profesinis mok<PERSON>", "Technical support": "Techninė pagalba", "Technician": "Technikas", "Technologist": "Technologas", "Technology": "Technologijos", "Technology and Information": "Technologijos ir informacija", "Technology, Information and Internet": "Technologijos, informacija ir internetas", "Technology, Information and Media": "Technologijos, informacija ir žiniasklaida", "Telecommunications": "Telekomunikacijos", "Telecommunications Carriers": "Telekomunikacijų operatoriai", "Telecoms": "Telekomunikacijos", "Telephone Call Centers": "Telefono skambučių centrai", "Telugu": "Telugų kalba", "Template": "Šablonas", "Template created": "Šablonas sukurtas", "Template created!": "Su<PERSON><PERSON>s <PERSON>!", "Template display name": "Šablon<PERSON> p<PERSON>", "Template log": "Šablono žurnalas", "Template name": "Šablono pavadinimas", "Template name should be descriptive eg. \"Interview invite\". This is not visible to the candidate.": "Šablono pavadinimas turėtų b<PERSON>, pvz., \"Kvietimas į pokalbį\". Kandidatui jis nėra matoma<PERSON>.", "Template name should be descriptive eg. \"Rejection letter\". This is not the subject.": "Šablono pavadinimas turėtų b<PERSON><PERSON>, pvz., „Atsisakymo laiškas“. Tai nėra tema.", "Template name should be descriptive eg. \"Request for feedback\". This is not the subject.": "Šablono pavadinimas turėtų b<PERSON>, pvz., „Prašymas pateikti atsiliepimus“. Tai nėra tema.", "Template owner": "Šablon<PERSON>", "Template visible for": "Šablon<PERSON>", "Templates": "Šablonai", "Temporary": "Laikinasis", "Temporary Help Services": "Laikinosios pagalbos paslau<PERSON>", "Tentative": "Preliminarus", "Terms": "<PERSON>ąly<PERSON>", "Terms of use": "<PERSON><PERSON><PERSON><PERSON>", "Test connection": "<PERSON><PERSON><PERSON> prisi<PERSON>", "Tester": "<PERSON><PERSON><PERSON><PERSON>", "Text": "Tekstas", "Text color": "Teks<PERSON> spalva", "Text color (hex)": "<PERSON><PERSON><PERSON> (šešioliktainiu formatu)", "Text of transcribed speech": "Nuorašo kalbos tekstas", "Text shadow": "<PERSON><PERSON><PERSON>", "Text signature override": "<PERSON><PERSON><PERSON>", "Text with background": "Tekstas su fonu", "Textarea": "<PERSON><PERSON><PERSON>", "Textile Manufacturing": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "Thai": "Taj<PERSON> kalba", "Thank you for reporting a problem. We will use your feedback to improve the location search functionality.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad p<PERSON>te apie problemą. Jūsų atsiliepimus panaudosime vietos paieškos funkcijai tobulinti.", "Thank you for using Teamdash!": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad na<PERSON><PERSON><PERSON><PERSON><PERSON> „Teamdash“!", "Thank you for your response!": "Dėkojame už atsakymą!", "Thank you!": "A<PERSON><PERSON><PERSON>!", "Thank you! We have saved your preferences!": "Ačiū! Išsaugojome jūsų pageidavimus!", "Thank you! Your details have been sent!": "Ačiū! Jūsų duomenys išsiųsti!", "Thank you! Your feedback was received!": "Ačiū! <PERSON><PERSON>sų atsiliepimas buvo gautas!", "Thank you! Your interview is scheduled at": "Ačiū! Jūsų interviu pokalbis suplanuotas", "Thank you! Your references have been saved.": "Ačiū! Jūsų rekomendacijos išsaugotos.", "The SMS body supports these merge tags: [recipient_full_name], [recipient_first_name], [recipient_last_name]. If sending from project context, [position_name] is also available. <br> Since this is an SMS, keep it short.": "SMS žinutė palaiko šias sujungimo žymas: [recipient_full_name], [recipient_first_name], [recipient_last_name]. <PERSON><PERSON> siun<PERSON> i<PERSON> atrank<PERSON>, taip pat galima naudoti [position_name]. <br> Kadangi tai yra SMS žinutė, rašykite ją trumpai.", "The URL must begin with HTTPS.": "URL turi prasidėti HTTPS.", "The action will be automatically executed after receiving a video interview response. It will be delayed for selected time.": "Veiksmas bus automatiškai įvykdytas gavus vaizdo pokalbio atsakymą. Jis bus atidėtas pasirinktam laikui.", "The action will be automatically executed after the project status has changed.": "Veiksmas bus automatiškai įvykdytas pakeitus projekto būseną.", "The action will be automatically executed when the candidate has been in stage longer than the selected time.": "Veiksmas bus atliktas automatiškai, kai kandidatas išbus etape ilgiau nei pasirinktas laikas.", "The action will be executed only when you click the button that appears in stage header.": "Veiksmas bus atliktas tik tada, kai spustelėsite antraštėje rodomą mygtuką.", "The array must contain between 3 and 10 sentences.": "Masyve turi būti nuo 3 iki 10 sakinių.", "The body colour may be hard to read on the background colour. Try increasing the contrast.": "Tokios spalvos tekstas gali būti sunkiai įskaitomas tokios spalvos fone. Pabandykite padidinti kontrastą.", "The candidate chose a time, but due to the error they did not get an email confirmation. Please contact support.": "Kandidatas p<PERSON><PERSON>, bet dėl klaidos negavo patvirtinimo el. paštu. Kreipkitės į palaikymo tarnybą.", "The candidate did not record any videos.": "Kandidatas neįrašė jokių vaizdo įrašų.", "The candidate gets a link where they can choose an interview time.": "Kandidatas gauna nuorodą, kurioje gali pasirinkti pokalbio <PERSON>.", "The candidate has has submitted the following preboarding information. It will be sent to {hrisName} together with the handover form.": "Kandidatas pateikė šią išankstinę informaciją. Ji bus išsiųsta į {hrisName} kartu su perdavimo forma.", "The candidate has not yet submitted the preboarding information.": "Kandidatas dar ne<PERSON><PERSON>s informacijos.", "The candidate has submitted preboarding information.": "Kandidatas pateikė išankstinę informaciją.", "The candidate has submitted the following preboarding information. It will be sent to {hrisName} together with the handover form.": "Kandidatas pateikė šią išankstinę informaciją. Ji bus išsiųsta į {hrisName} kartu su perdavimo forma.", "The candidate might not have a CV attached or the CV might be unreadable or too long.": "<PERSON><PERSON>, kad kandi<PERSON> ne<PERSON>rid<PERSON> gy<PERSON> a<PERSON> arba jis yra neįskaitomas ar per ilgas.", "The candidate recorded {count} videos, but did not submit any of them.": "Kandidatas įrašė {count} vaizdo įrašų, tačiau nė vieno iš jų nepateik<PERSON>.", "The candidates have no conflicting fields. Ready to merge!": "Kandidatai neturi konfliktuojančių sričių. Paruošta sujungti!", "The category field is required.": "<PERSON><PERSON><PERSON><PERSON> lauka<PERSON> yra privaloma<PERSON>.", "The cleaned-up text of a transcript segment in which spelling mistakes have been fixed.": "Išvalytas nuorašo segmento tekstas, k<PERSON>me i<PERSON>taisytos rašybos klaidos.", "The consent expires when this project is finished.": "Sutikimas nustos gal<PERSON>i, kai šis projektas bus baigtas.", "The deadline for review is in :deadline, so you better do it now.": "G<PERSON>utinis peržiūros terminas yra :deadline, todėl geriau tai padarykite dabar.", "The deadline for review is in :deadline. You will be notified if the job requisition succeeds.": "Galutinis peržiūros terminas yra :deadline. Jums bus pranešta, jei darbo paraiška bus sėkminga.", "The default date and time format for your organization.": "Numatytasis jūsų organizacijos datos ir laiko formatas.", "The default language for new users in your organization.": "Numaty<PERSON>ji kalba naujiems naudotojams jūsų įmonėje.", "The display name becomes visible to candidates when using the [position_display_name] merge tag.": "<PERSON><PERSON><PERSON> vardas tampa matomas kandi<PERSON>, kai naudo<PERSON><PERSON> sujungi<PERSON> [position_display_name].", "The file must contain name, email, and role columns.": "<PERSON><PERSON>e turi b<PERSON>ti <PERSON>, el. pa<PERSON><PERSON> ir vaidmens stu<PERSON>i.", "The final score is calculated: cNPS = % promoters - % detractors.": "Galutinis rezultatas apskaičiuojamas taip: cNPS = % teigiamai vertinančiųjų – % neigiamai vertinančiųjų.", "The following e-mail will be sent to recipient:": "Gavėjui bus išsiųstas šis el. laiškas:", "The font file extensions must be one of the following: :extensions.": "Šrifto failo plėtiniai turi būti vieni iš šių: :extensions.", "The function parameter must be an array in which each object represents a cleaned-up segment with the \"text\" field contains the cleaned-up text and \"time\" field remains unchanged.": "Funkcijos parametras turi b<PERSON><PERSON>, k<PERSON>me k<PERSON>vienas objektas apima išvalytą segmentą, kuri<PERSON> lauke<PERSON> „text“ yra išvalytas tekstas, o laukelis „time“ lieka nepakitęs.", "The heading colour may be hard to read on the background colour. Try increasing the contrast.": "Tokios spalvos antraštė gali būti sunk<PERSON>i įskaitoma tokios spalvos fone. Pabandykite padidinti kontrastą.", "The interview has multiple people being interviewed and their names are as follows:": "Interviu apklaus<PERSON> k<PERSON>, kurių vardai yra š<PERSON>:", "The interview is conducted by a person named :interviewer.": "Pokalbį veda asmuo, vardu :a<PERSON><PERSON><PERSON><PERSON><PERSON>.", "The interview is conducted by the following interviewers:": "Pokalbį veda šie da<PERSON>i:", "The interview is for a job position titled \":position\" in the company \":company\".": "Pokalbis vyksta dėl darbo vietos pavadinimu „:position“ įmonėje „:company“.", "The interview is for a job position titled \":position\" with involvement by the following companies: :companies.": "Pokalbis vyksta dėl darbo vietos pavadinimu „:position“, dalyvauja šios įmonės: :companies.", "The interview is for a job position titled \":position\".": "Pokalbis vyksta dėl darbo vietos pavadinimu \":pozicija\".", "The job ad can be accessed only by logged in users and from internal network.": "Darbo skelbimas pasiekiamas tik prisijungusiems naudotojams ir iš vidinio tin<PERSON>.", "The latest error was:": "Paskutinė klaida buvo:", "The maximum value must be greater than the minimum value.": "Didžiausia reikšmė turi būti didesnė už mažiausią reikšmę.", "The meeting recording is not yet available for analysis.": "Susitikimo įrašo dar negalima analizuoti.", "The person being interviewed is named :person.": "Apkla<PERSON><PERSON>as asmuo vadinamas :asmuo.", "The processing of the transcript has not been finished yet. Please check again later.": "Nuorašo tvarkymas dar nebaigtas. Vėliau patikrinkite dar kartą.", "The recruiter has prepared one question for you. | The recruiter has prepared {count} questions for you.": "Atrankų specialistas parengė jums vieną klausimą. | Atrankų specialistas jums parengė {count} klausimų.", "The same value as the original transcript segment had for the key \"time\". Should not be modified.": "Tokia pati re<PERSON>, kaip ir originalaus nuorašo segmento rakto \"laikas\" reik<PERSON><PERSON><PERSON>. Neturėtų būti keič<PERSON>a.", "The segments returned must be in the exact same order as input.": "Grąžinami segmentai turi būti tiksliai tokia pačia tvarka kaip ir įvesties.", "The segments returned must be in the exact same order as input. Give response in following format: {\"segments\": [...]}": "Grąžinami segmentai turi būti tokia pat tvarka kaip įvestis. Pateikite atsakymą tokiu formatu: {\"segments\": [...]}", "The selected candidate does not have a valid phone number.": "Pasirinktas kandidatas neturi galiojančio telefono numerio.", "The selected candidate does not have an email address.": "Atrinktas kandidatas neturi el. pašto adreso.", "The speech that was originally transcribed might have included the following proper names:": "<PERSON><PERSON> užrašytoje kalboje galėjo būti š<PERSON>menvardžiai:", "The summary should be useful for making good hiring decisions.": "Santrauka turėtų būti na<PERSON>a priimant gerus sprendimus dėl įdarbinimo.", "The survey responses are aggregated into three groups by their score.": "A<PERSON><PERSON><PERSON>os atsakymai suskirstyti į tris grupes pagal surinktus balus.", "The table must have the following columns: Name, Email, Phone, Tags. The file may include one or more comment columns named Comment1, Comment2 etc. Download sample file <a href=\"/files/import_sample.xlsx\" target=\"_blank\">here</a>.": "Lentelėje turi būti šie stulpeliai: Name, Email, Phone, Tags. Į failą gali būti įtrauktas vienas ar daugiau komentarų stulpelių, pavadintų Comment1, Comment2 ir t. t. Atsisiųskite pavyzdinį failą <a href=\"/files/import_sample.xlsx\" target=\"_blank\">čia</a>.", "The team with the biggest number of accessible teams is chosen.": "<PERSON><PERSON><PERSON><PERSON><PERSON> koman<PERSON>, turinti daugiausiai prieinamų komandų.", "The transcribed text will not be affected.": "Nuorašo tekstas nebus paveiktas.", "The uploaded file is too large.": "Įkeltas failas yra per didelis.", "The user will send you the transcript which will be a JSON array of transcript segments": "Vartotojas atsiųs jums nuorašą, kuris bus JSON nuorašo segmentų masyvas.", "The user will send you the transcript which will be a JSON array of transcript segments, each being a JSON object with two keys: \"text\" and \"time\".": "Naudotojas atsiųs jums nuorašą, kuris bus JSON nuorašo segmentų masyvas, kurių kiekvienas yra JSON objektas su dviem raktais: „tekstas“ ir „laikas“.", "The video recording failed unexpectedly. Please refresh the page.": "Vaizdo įrašymas netikėtai nutrūko. Atnaujinkite puslapį.", "The video recording {willBeChangedAutomatically} after the time has run out.": "Vaizdo įrašas {willBeChangedAutomatically} pasi<PERSON><PERSON><PERSON> laikui.", "The {anonymizedCv} option will also show an anonymized version of the candidate's CV.": "Pasirinkus {anonymizedCv} parinktį taip pat bus rodoma anoniminė kandidato CV versija.", "Theater Companies": "Teatro įmonės", "Theme": "<PERSON><PERSON>", "Then choose \"Message\" and include the [survey] tag inside the message.": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" ir į žinutę įtraukite žymą [survey].", "There are candidates, actions or statistics associated with this stage.": "Su šiuo etapu yra susiję kandi<PERSON>, veiksmai ar statistiniai duomenys.", "There are no actions defined for this stage.": "Šiame etape nenustatyta jokių veiksmų.", "There are no cNPS responses yet.": "Ko<PERSON> kas nėra cNPS atsakymų.", "There are no candidates in this slot.": "Kandidatų į šią vietą nėra.", "There are no candidates in this time slot.": "Kandidatų šiame laiko tarpe nėra.", "There are no comments yet.": "Komentarų dar nėra.", "There are no integrations yet.": "Integracij<PERSON> dar nėra.", "There are no messages yet.": "<PERSON><PERSON> kas nėra jokių žinučių.", "There are no more open slots in this interview.": "Šiame darbo pokalbyje daugiau laisvų vietų nėra.", "There are no other dropout reasons to merge with.": "Nėra kitų iškritimo priežasčių, kurias būtų galima sujungti.", "There are no project log entries yet.": "Projekto žurnalo įrašų dar n<PERSON>ra.", "There are no published job ads for this project yet.": "<PERSON><PERSON> kas nėra paskelbtų šio projekto darbo skelbimų.", "There are no upcoming interviews.": "Būsimų darbo pokalbių nėra.", "There are no upcoming interviews. You can try changing the filters above the calendar.": "Artimiausių interviu nėra. Galite paba<PERSON>ti pakeisti filtrus virš ka<PERSON>.", "There is no data available for the selected filters.": "Pasirinktų filtrų duomenų nėra.", "There is no data to display.": "Rodytinų duomenų nėra.", "There is not enough data to display this report. Try changing the selected filters and/or date range.": "Šiai ataskaitai rodyti nepakanka duomenų. Pabandykite pakeisti pasirinktus filtrus ir (arba) datos intervalą.", "There must be at least one category for each system category": "Kiekvienai sistemos kategorijai turi būti suteikta bent viena kategorija.", "There must be at least one custom category for every system category": "Kiekvienai sistemos kategorijai turi būti bent viena pasirinktinė kategorija.", "There was a problem loading the form options.": "<PERSON><PERSON> problema įkeliant formos parinktis.", "There was a problem with at least one of the video responses.": "<PERSON><PERSON> dėl bent vieno vaizdo įrašo atsakymo.", "There was a problem with sending the candidates.": "Kandidatų išsiuntimo problema.", "There was an error": "Įvyko klaida", "There was an error anonymizing the candidates. Please refresh this page.": "Kandidatų anonimiškumo nustatymo klaida. Prašome atnaujinti šį puslapį.", "There was an error assigning the room.": "Įvyko klaida p<PERSON>t kambarį.", "There was an error committing stage transitions. Please refresh the page.": "Įvyko klaida atliekant etapų keitimus. Prašome atnaujinti puslapį.", "There was an error loading this report.": "Įkeliant šią ataskaitą įvyko klaida.", "There was an error removing the action.": "Įvyko klaida šali<PERSON> ve<PERSON>.", "There was an error removing the stage.": "Įvyko klaida šali<PERSON> etapą.", "There was an error removing this stage action.": "Įvyko klaida šalinant šį etapo ve<PERSON>.", "There was an error sending your messages.": "Įvyko klaida siunč<PERSON> jūsų p<PERSON>šimus.", "There was an error submitting the form. Please try again.": "Įvyko klaida pateikiant formą. Prašome pabandyti dar kartą.", "There was an error updating the event.": "<PERSON><PERSON><PERSON><PERSON><PERSON> įvykį įvyko klaida.", "There was an error uploading the file.": "Įkeliant failą įvyko klaida.", "There was an error with the interview. Please contact support.": "Interviu metu įvyko klaida. Susisiekite su klientų aptarnavimo komanda.", "There was an error.": "Įvyko klaida.", "There was an error. Please try again.": "Įvyko klaida. Bandykite dar kartą.", "There were candidates for whom we could not determine some fields. You can review the information below and update it if necessary.": "Buvo kandidatų, kurių kai kurių sričių negalėjome nustatyti. <PERSON><PERSON>te per<PERSON>ti toliau pateiktą informaciją ir prireikus ją atnaujinti.", "There's a project associated with this requisition.": "Su šia paraiška susijęs projektas.", "These actions cannot be reassigned.": "Šių veiksmų negalima priskirti iš naujo.", "These are the videos the candidate submitted:": "Kandidatas pateikė šiuos vaizdo įrašus:", "These candidates have one or more overlapping fields. Click on a name to compare.": "Šie kandidatai turi vieną ar daugiau sutampančių sričių. Spustelėkite vardą, kad gal<PERSON>tumėte palyginti.", "These fields will be added to the handover form.": "Šie laukai bus įtraukti į perdavimo formą.", "These fields will be added to the job ad publication form.": "Šie laukai bus pridėti prie darbo skelbimo publikavimo formos.", "These fields will be shown in the job ad publication form.": "Šie laukai bus rodomi darbo skelbimo publikavimo formoje.", "These images will be added to the job posting. You can drag and drop to change the order.": "Šios nuotraukos bus pridėtos prie darbo skelbimo. Pele galite jas tempti ir keisti nuotraukų tvarką.", "These tags will be removed.": "Šios žymos bus pašalintos.", "These users are out of sync with their roles from SCIM": "<PERSON>ie vartotojai nesinchronizuojami su savo vaidmenimis iš SCIM", "These users will be added to every project you create from this template.": "Šie naudotojai bus įtraukti į kiekvieną projektą, sukurtą naudojant šį šabloną.", "These users will be asked to approve or reject the requisition.": "Šių naudotojų bus paprašyta patvirtinti arba atmesti paraišką.", "They will be notified when you post the comment.": "Jiems bus pranešta, kai paskelbsite komentarą.", "They will get a notification about the cancellation.": "Jie gaus praneš<PERSON>ą apie atšaukimą.", "They wrote:": "<PERSON><PERSON> ra<PERSON>:", "Think Tanks": "Idėj<PERSON> kalv<PERSON>s", "Third sector / NGO": "Trečiasis sektorius / NVO", "This action can only be triggered automatically.": "Šį veiksmą galima aktyvinti tik automatiškai.", "This action cannot be undone.": "Šio ve<PERSON> negalima at<PERSON>ti.", "This action is automatically executed if the candidate is in stage on or after {actionScheduledAtUser}. You can execute the action immediately by pressing this button.": "<PERSON><PERSON> veiksmas atliekamas automatiškai, jei kandidatas yra etape {actionScheduledAtUser} arba vėliau. Veiksmą galite atlikti iš karto paspausdami šį mygtuką.", "This action is automatically executed once the candidate has been in stage for {actionDelayValue} {actionDelayUnit}. You can execute the action immediately by pressing this button.": "Šis veiksmas atliekamas automatiškai, kai kandidatas etape yra {actionDelayValue} {actionDelayUnit}. Veiksmą galite atlikti iš karto paspausdami šį mygtuką.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after receiving a video interview response.": "Šis veiksmas atliekamas automatiškai {actionDelayValue} {actionDelayUnit} po vaizdo pokalbio atsakymo gavimo.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted a form.": "<PERSON>is veiksmas atliekamas automatiškai {actionDelayValue} {actionDelayUnit} po to, kai kandidatas pateikia formą.", "This action is automatically executed {actionDelayValue} {actionDelayUnit} after the candidate has submitted their references.": "<PERSON>is veiksmas atliekamas automatiškai {actionDelayValue} {actionDelayUnit} po to, kai kandidatas pateikia rekomendacijas.", "This action was sent to {candidateCount} candidates at {actionTime}.": "<PERSON><PERSON> ve<PERSON> buvo <PERSON> {candidateCount} kandidatams (-ų) {actionTime}.", "This allows you to send semi-automatic messages and video interviews": "Tai leidžia siųsti pusiau automatines žinutes ir vaizdo pokalbius", "This campaign is particularly recommended for roles that are relatively easy to fill, such as customer service representatives, warehouse workers, and other similar positions.": "Ši kampanija rekomendu<PERSON><PERSON> par<PERSON>, kuria<PERSON> gana le<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, klientų aptarnavimo specialistų, <PERSON><PERSON><PERSON> ir kitų panašių pareigų.", "This campaign is the go-to choice for critical yet moderately easy-to-fill positions such as restaurant workers and junior-level professionals.": "Ši kampanija yra tinkamas pasirinkim<PERSON> kritin<PERSON>, tačiau vidutiniškai lengvai užpildomoms pareigoms, pvz., restoranų darbuotojams ir jaunesniojo lygio profesionalams.", "This campaign is the ideal choice for filling challenging positions, such as healthcare professionals and experienced technical or commercial specialists.": "Ši kampanija yra idealus pasirinkimas norint samdyti sudėtingas pozici<PERSON>, to<PERSON>as kaip sveika<PERSON> prie<PERSON><PERSON><PERSON> specialistus ir patyrusius k<PERSON> specialistus.", "This campaign level is suitable for supporting individual, highly challenging recruitment eﬀorts, such as software developers or experienced professionals.": "Šis kampanija tinka individualioms, labai sudėtingoms atrankoms. Pvz., programuotojų ar patyrusių specialistų.", "This can help you make less biased decision for a more inclusive hiring process.": "Tai gali padėti priimti mažiau šališ<PERSON> sprendimus, kad atrankų procesas būtų objektyvesnis.", "This can not be undone.": "To negalima atšauk<PERSON>.", "This candidate does not have any data processing consents.": "Šis kandidatas neturi jokių sutikimų tvarkyti duomenis.", "This candidate does not have any references yet.": "<PERSON><PERSON> kandidatas dar neturi jokių rekomendacijų.", "This candidate has a pending consent renewal.": "<PERSON><PERSON> kandidato sutikimas dar nėra p<PERSON>ę<PERSON>.", "This candidate has been anonymized.": "<PERSON><PERSON> kandidatui suteiktas anonimiškumas.", "This candidate has not been sent any video interviews.": "Šiam kandidatui nebuvo išsiųsti jokie vaizdo interviu.", "This candidate is in {count} unfinished projects.": "<PERSON><PERSON> kandidatas rastas {count} nebaigtuose projektuose.", "This category contains candidates from ongoing recruitment projects.": "Į šią kategoriją įtraukti kandidatai iš vykdomų atrankų projektų.", "This category contains candidates from recently finished projects.": "Šioje kategorijoje pateikiami neseniai baigtų projektų kandidatai.", "This client does not have any offices.": "Šis klientas neturi biurų.", "This client does not have any projects.": "Šis klientas neturi projektų.", "This client is associated with 1 active project|This client is associated with {count} active projects": "Šis klientas susietas su 1 aktyviu projektu|Šis klientas susietas su {count} aktyvių projektų", "This client is associated with 1 finished project.|This client is associated with {count} finished projects.": "Šis klientas susietas su 1 užbaigtu projektu.|Šis klientas susietas su {count} užbaigtais projektais.", "This client is not associated with any projects.": "Šis klientas nesusietas su jokiais projektais.", "This consent covers only processing related to the position they applied to.": "<PERSON><PERSON> sutikimas apima tik su darbo vieta, į kurią pateikta paraiška, susijusį duomenų tvarkymą.", "This consent request has expired.": "Š<PERSON> sutikimo p<PERSON>š<PERSON> b<PERSON>.", "This data was extracted with the help of AI": "Šie duomenys buvo išgauti naudojant dirbtinį intelektą", "This distinction is critical to ensure that your most important messages do not go into Gmail promotions tab.": "<PERSON><PERSON> s<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kad svar<PERSON>usi pranešimai nepatektų į „Gmail“ reklamos skirtuką.", "This document is valid until :expiry.": "Šis dokumentas galioja iki :expiry paba<PERSON>s.", "This e-mail is sent when you add a new user to your organization.": "<PERSON><PERSON> el. la<PERSON><PERSON><PERSON>, kai į organizaciją įtraukiamas naujas naudotojas.", "This failure reason cannot be deleted as it has already been assigned to some projects.": "Šios ne<PERSON>ėkmės priežasties negalima ištrinti, nes ji jau priskirta kai kuriems projektams.", "This field MUST include the [presentation_url] tag.": "<PERSON><PERSON>e laukelyje TURI būti [presentation_url] žyma.", "This field is used internally and for reporting. It can contain lowercase a-z and underscores (e.g. if your activity name is \"Sent out free t-shirt\" then you'd put tshirt_sent here).": "<PERSON><PERSON> laukelis naudojamas sistemos viduje ir ataskaitoms teikti. Jame gali būti maž<PERSON> raidės a-z ir pabraukimo simboliai (pvz., jei jūsų veiklos pavadinimas yra „Siųsti nemokamus mar<PERSON>“, čia gal<PERSON> įrašyti „tshirt_sent“).", "This form cannot be accessed directly, because it does not contain an email field. Please include the form in an e-mail via the [form_url] merge tag.": "Prie šios formos negalima prisijungti <PERSON>, nes joje n<PERSON>ra el. pašto lauko. Prašome formą įtraukti į el. laišką naudojant [form_url] žymą.", "This form cannot be accessed directly. Please include the form in an e-mail via the [form_url] merge tag.": "Šios formos tiesiogiai pasiekti negalima. Prašome formą įtraukti į el. laišk<PERSON> naudojant [form_url] sujungimo žym<PERSON>.", "This form is automatic and uses the stage defined on the landing page": "Ši forma yra automatinė ir joje naudojamas nukre<PERSON>imo puslapyje nustatytas etapas.", "This form is directly linked to a stage, applicants will always be added to this stage": "Ši forma yra tiesiogiai susieta su etapu, pareiškėjai visada bus įtraukti į šį etapą.", "This form is used in one or more templates: {templates}": "Ši forma naudojama viename ar keliuose šablonuose: {templates}", "This generates automatic summaries of candidate CV-s.": "Taip sukuriamos automatinės kandidatų gyvenimo a<PERSON>šymų santraukos.", "This includes confidential projects. Do not turn on hastily.": "Tai apima ir konfidencialius projektus. Neįjunkite skubotai.", "This integration is currently not linked to any API keys.": "Šiuo metu ši integracija nėra susieta su jokiais API raktais.", "This integration is using the following API keys:": "Šiai integracijai naudojami šie API raktai:", "This integration publishes a feed at:": "Ši integracija skelbia informacijos santrauką adresu:", "This interview has been cancelled.": "Šis darbo pokalbis yra at<PERSON>.", "This interview slot was not yet booked.": "<PERSON>is darbo pokalbio laikas dar nebuvo rezervuotas.", "This interview was prescheduled.": "Šis darbo pokalbis buvo iš anksto suplanuotas.", "This is a manual action, only executed when you click this button.": "<PERSON> rankinis veik<PERSON>, atliekamas tik spustelėjus šį mygtuką.", "This is a preview of the form. Submissions will not be saved.": "Tai yra formos perž<PERSON>ū<PERSON>. Pateikta informacija nebus išsaugota.", "This is the url where shared candidates are listed.": "<PERSON> url ad<PERSON>, kuria<PERSON> pat<PERSON>ami bendrų kandidatų sąrašai.", "This is used for all time inputs and outputs. ": "Tai naudojama visoms laiko įvestims ir išvestims.", "This is used in internal menus. Candidates can not see this.": "Tai naudojama vidiniuose meniu. Kandidatai to nemato.", "This is used to determine how long before the expiry date is the file considered to be expiring soon.": "Tai naudojama siekiant nustat<PERSON>i, kiek laiko iki galiojimo p<PERSON>s da<PERSON> la<PERSON>, kad <PERSON>o galiojimo laikas netrukus bai<PERSON>.", "This is used to determine the expiry date if the file doesn't have an expiration date specified.": "Tai naudojama galiojimo pabaigos datai nustatyti, jei failas neturi nurodytos galiojimo pabaigos datos.", "This job ad is for internal access only. Please": "Šis darbo skelbimas skirtas tik vidinei prieigai. Prašome", "This job ad is for internal access only. Please :loginLink to see the ad": "Šis darbo skelbimas skirtas tik turintiems vidinę prieigą. Norėdami pamatyti skelbimą, prašome :loginLink", "This job requisition needs your approval to move forward.": "<PERSON><PERSON> <PERSON>i darbo paraiška būt<PERSON> tę<PERSON>, reikia jūs<PERSON> pat<PERSON>.", "This key is used by integration {integrationName}. Deleting this key will break the connection between the integration and your Teamdash instance.": "Šį raktą naudoja integracija {integrationName}. Ištrynus šį raktą bus nutrauktas ryšys tarp integracijos ir jūsų Teamdash paskyros.", "This location was detected automatically with high confidence based on data provided in the CV.": "Ši vieta buvo nustatyta automatiškai ir labai tik<PERSON>liai, re<PERSON><PERSON> gyvenimo apra<PERSON> pateiktais duomenimis.", "This location was detected automatically with low confidence based on data provided in the CV.": "Ši vieta buvo nustatyta automatiškai ir netiksliai, re<PERSON><PERSON> gy<PERSON> a<PERSON> pateiktais duomenimis.", "This location was detected automatically with medium confidence based on data provided in the CV.": "Ši vieta buvo nustatyta automatiškai ir vidutiniškai tiksliai, re<PERSON><PERSON> gyvenimo apra<PERSON>yme pateiktais duomenimis.", "This means that candidate imports or job postings might not work correctly.": "<PERSON>, kad kandidatų importavimas arba darbo vietų skelbimas gali veikti netinkamai.", "This means that if you are scheduling video interviews, Teamdash will use a fallback video calls provider, not :integration.": "<PERSON>, kad jei <PERSON> vaiz<PERSON> p<PERSON>, \"Teamdash\" naudos atsarginį vaizdo skambuči<PERSON> teikėj<PERSON>, o ne :integration.", "This means that incoming emails from candidates are not displayed in Teamdash.": "<PERSON>, kad <PERSON>Teamdash“ nerodomi iš kandidatų gaunami el. laiškai.", "This means that when you're scheduling interviews, you might not see your colleagues' calendars correctly.": "<PERSON>, kad planuodami darbo pokalbius galite neteisingai matyti savo kolegų kalendorius.", "This means that you cannot send candidates to your HRIS from their profiles.": "<PERSON>, kad kandidatų iš jų profilių negalite siųsti į savo HRIS.", "This message will be sent to :allCandidates in the interview :immediatelyAfterSaving": "Ši žinutė bus išsiųsta :allCandidates pokalbyje :immediatelyAfterSaving", "This message will be sent to the candidate :afterTimeSelected.": "Šis pranešimas bus išsiųstas kandidatui :afterTimeSelected.", "This might take up to an hour. Check again later.": "Tai gali užtrukti iki valandos. Patikrinkite dar kartą vėliau.", "This option allows you to add candidates without email. This will make some features fail.": "<PERSON><PERSON> parink<PERSON> leidžia pridėti kandidatus be el. pašto. D<PERSON>l to kai kurios funkcijos neveiks.", "This option allows you to require adding a location for each candidate.": "Š<PERSON> parinktis leidžia reikalauti pridėti kiekvieno kandidato vietą.", "This password reset link will expire in :count days.": "<PERSON><PERSON>odžio atstatymo nuoroda nustos galioti po :count dien<PERSON>.", "This person is marked as the sender.": "<PERSON><PERSON> asmuo p<PERSON> ka<PERSON>.", "This presentation was shared with :recipient.": "Šiuo pristatymu buvo pasidalinta su :recipient.", "This project and the candidates within are only visible to project members.": "Šis projektas ir jame esantys kandidatai matomi tik projekto nariams.", "This project does not accept candidates through any application methods yet.": "Į šį projektą kandidatai dar nepriimami jokiais paraiškų teikimo būdai<PERSON>.", "This project doesn't have a position display name set.": "Šiam projektui pozicijos atvaizdavimo pavadinimas nenustatytas.", "This project is finished and read-only.": "<PERSON><PERSON> projektas baigtas ir <PERSON>as tik skaitymui.", "This project is not associated with any landing pages yet.": "Šis projektas dar nesusijęs su jokiais nukreipimo pusla<PERSON>.", "This reason cannot be deleted as it has already been assigned to some candidates.": "Šios priežasties negalima ištrinti, nes ji jau buvo priskirta kai kuriems kandidatams.", "This requires users to choose a reason when changing a project's status to 'Failed'.": "Tai reikalauja naudotojų pasirinkti priežastį keičiant projekto statusą į \"Nepavyko\"", "This requisition was approved!": "Ši paraiška buvo patvirtinta!", "This setting applies to all users who view this stage. Limited users can not change this setting.": "Šis nustatymas taikomas visiems naudotojams, kurie per<PERSON> šį etapą. Riboti naudotojai negali keisti šio nustatymo.", "This setting is nondestructive - you will always be able to turn it off again, without losing any data.": "Šis nustatymas nė<PERSON> destruktyvu<PERSON> – visada galėsite jį vėl i<PERSON>, neprarasdami jokių duomenų.", "This tag will take the place of all source tags.": "Ši ž<PERSON>a pakeis visas š<PERSON><PERSON><PERSON>.", "This week": "<PERSON><PERSON><PERSON> sa<PERSON>", "This will allow candidates to apply with their resume in one click. The form details are prefilled from the resume. The candidate can still edit the details before submitting.": "Tai leis kandidatams vienu paspaudimu pateikti paraišką su savo gyvenimo aprašymu. Išsamios formos informacija iš anksto u<PERSON> iš gyvenimo aprašymo. Kandidatas vis tiek gali redaguoti informaciją prieš pateikdam<PERSON>.", "This will appear as the image when sharing on social media and IM platforms.": "Dalijantis socialinėje <PERSON>sklaidoje ir tiesioginių pokalbių platformose, tai bus rodoma kaip p<PERSON>.", "This will appear in list view.": "Tai bus rodoma sąrašo rodinyje.", "This will apply to all users. It can later be turned back on in Organization settings.": "Tai bus taikoma visiems naudotojams. Vėliau tai galima vėl įjungti organizacijos nustatymuose.", "This will be a select option for candidates in the consent renewal request.": "Kandidatas turė<PERSON> gal<PERSON> pažymėti sutikimo atnaujinimą", "This will be in your page URL.": "Tai bus įtraukta į jūsų puslapio URL.", "This will be sent to LinkedIn as the job description.": "Tai bus išsiųsta į \"LinkedIn\" kaip darbo a<PERSON>.", "This will be stored as the organization privacy policy URL.": "Jis bus išsaugotas kaip organizacijos privatumo politikos URL.", "This will not affect existing actions in projects previously created from this template.": "Tai neturės įtakos esamiems veiksmams projektuose, anksč<PERSON>u sukurtuose pagal šį šabloną.", "This will override the default signature.": "<PERSON> pakeis standartinį parašą.", "Thursday": "Ketvirtadienis", "Tibetan": "Tibetiečių kalba", "Tigrinya": "Tigrajų kalba", "Tiler": "Plytelių klojėjas", "Time": "<PERSON><PERSON>", "Time slot updated successfully!": "Laiko tarpas sėkmingai atnaujintas!", "Time to fill": "Projektų laikas", "Time to hire": "Įdarbinimo laikas", "Timestamp": "<PERSON><PERSON>", "Timestamp of speech occurrence": "<PERSON><PERSON><PERSON>sir<PERSON><PERSON>", "Timezone": "<PERSON><PERSON> j<PERSON>", "Timezone mismatch detected": "Aptiktas laiko juostos <PERSON>", "Time’s up! Try to finish soon.": "Laikas baigėsi! Pabandykite baigti kuo greičiau.", "Tinsmith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tip: If you want to run something when the candidate submits the form, create another action with the trigger set to \"after candidate has submitted a form\".": "Patarimas: jei norite ką nor<PERSON>, kai kandidatas pateikia formą, sukurkite kitą veiksmą, kurio veiksmo aktyvinimas nustatytas kaip „po to, kai kandidatas pateikia formą“.", "Tire worker": "Padangų meistras", "Title": "Pavadinimas", "To automate your feedback collection process, use the [survey] tag inside your automatic stage actions.": "Jei norite automatizuoti atsiliepimų rinkimo procesą, automatinių etapų veiksmuose naudokite žymą [survey].", "To find your employer ID:": "Norėdami rasti darbdavio ID:", "To find/generate your API key:": "Norėdami rasti arba sugeneruoti savo API raktą:", "To find/generate your Client ID and Secret:": "Norėdami rasti arba sugeneruoti savo kliento ID ir slaptą raktą:", "To get the best results:": "Norėdami pasiekti geriausių rezultatų:", "To get your credentials for integrating with RetailChoice, please contact RetailChoice over one of the methods available at": "Norėdami gauti integracijos su „RetailChoice“ įgaliojimus, kreipkitės į „RetailChoice“ vienu iš galimų būdų, pateiktų", "To get your credentials for integrating with Totaljobs, please contact Totaljobs over one of the methods available at": "Norėdami gauti integracijos su „Totaljobs“ įgaliojimus, kreipkitės į „Totaljobs“ vienu iš galimų būdų, pateiktų", "To know what your candidates really think about your recruitment process, you'll need to ask them for actual feedback.": "<PERSON><PERSON><PERSON><PERSON>, ką kandidatai iš tikrųjų mano apie jūsų atrankų procesą, turite paprašyti jų realių atsiliepimų.", "To provide you with better analytics, it will soon be required to categorize all stages before saving a project.": "Kad <PERSON>e pateikti geresnę analizę, netrukus prieš i<PERSON>ugant projektą reikės visus etapus suskirstyti į kategorijas.", "To show you project funnel statistics, all project stages need to be categorized.": "<PERSON><PERSON><PERSON>, kad būt<PERSON> rodoma projekto kanalo statistika, visi projekto etapai turi būti suskirstyti į kategorijas.", "To use Teamdash Platform You should accept": "Norėdami naudoti „Teamdash“ platformą, turėtumėte sutikti", "To use the Teamdash Platform you must accept the": "Norėdami naudotis „Teamdash“ platforma, turite sutikti su", "Tobacco Manufacturing": "Tabako gam<PERSON>", "Today": "Šiandien", "Today is the day to create your first job ad with Job Ad Tool by Teamdash!": "Šiandien yra ta diena, kai galite sukurti savo pirmąjį darbo skelbimą naudodamiesi „Teamdash“ darbo skelbimų įrankiu!", "Tomorrow": "<PERSON><PERSON><PERSON><PERSON>", "Tonga (Tonga Islands)": "Tonga (Tongos salos)", "Too Many Requests": "Per daug užklausų", "Total": "<PERSON><PERSON> viso", "Total average": "Bendras vidurkis", "Total candidates": "<PERSON><PERSON> viso kandidat<PERSON>", "Total of 1 comment|Total of {count} comments": "Iš viso 1 komentaras|<PERSON><PERSON> viso {count} komentarai (-ų)", "Total projects": "<PERSON><PERSON> viso projektų", "Tour operator": "Kelionių organizatorius", "Tourism / Hotels": "Turizmas / Viešbučiai", "Tourism / Hotels / Catering": "Turizmas / Viešbučiai / Mai<PERSON>imas", "Town": "Miestas", "Town or postcode": "Miestas arba pa<PERSON>to kodas", "Tractor driver": "Traktoriaus vair<PERSON>", "Trade": "Prekyba", "Trade/retail": "Prekyba / mažmeninė prekyba", "Train driver": "<PERSON><PERSON><PERSON><PERSON>", "Trainer": "<PERSON><PERSON><PERSON>", "Training": "<PERSON><PERSON><PERSON>", "Training description": "<PERSON><PERSON><PERSON>", "Training/education/culture": "<PERSON><PERSON><PERSON> / <PERSON><PERSON><PERSON><PERSON> / kultūra", "Transcribe the provided text.": "Perrašykite pateiktą tekstą.", "Transcript": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transcription segment": "Nuorašo segmentas", "Transcription segments": "Nuorašo segmentai", "Transcription will begin automatically after the recording is available.": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> automatiškai, kai įrašas bus pasiekiamas.", "Transcripts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Translation and Localization": "<PERSON><PERSON><PERSON><PERSON> ir lo<PERSON>", "Translator": "<PERSON><PERSON><PERSON><PERSON>", "Transport driving": "Transporto v<PERSON>", "Transport/logistics management": "Transporto ir (arba) logistikos valdymas", "Transportation": "Transportas", "Transportation / Logistics": "Transportas / Logistika", "Transportation Equipment Manufacturing": "Transporto įrangos gamyba", "Transportation Programs": "Transporto programos", "Transportation, Logistics, Supply Chain and Storage": "Transportas, logistika, tiekimo grandinė ir sand<PERSON>mas", "Travel": "<PERSON><PERSON><PERSON><PERSON>", "Travel Arrangements": "Kelionių organizavimas", "Trigger": "Veiksmo aktyvinimas", "Triin from Teamdash has built many job ads and recruitment campaigns.": "<PERSON><PERSON> <PERSON> \"Team<PERSON><PERSON>\" komandos sukūrė daugybę darbo skelbi<PERSON>ų ir atrankų kampanijų.", "Troubleshooting:": "Trikčių šalinimas:", "Trousers size": "Kelnių dydis", "Truck Transportation": "Sunkvežimių gabenimas", "Trusts and Estates": "Patikos fondai ir turtas", "Try again": "Pabandykite dar kartą", "Try changing your search criteria": "Pabandykite pakeisti paieškos kriterijus", "Try it out": "Išbandykite", "Tsonga": "Tsongų kalba", "Tswana": "Tsvanų kalba", "Tuesday": "<PERSON><PERSON><PERSON><PERSON>", "Turkish": "Turkų kalba", "Turkmen": "Turkmėnų kalba", "Turn on e-mail notifications": "Įjunkite el. pa<PERSON><PERSON>", "Turned Products and Fastener Manufacturing": "Tekintų produktų ir tvirtinimo de<PERSON> gamyba", "Turner": "<PERSON><PERSON><PERSON>jas", "Twi": "<PERSON><PERSON> kalba", "Two-Factor Authentication": "Dviejų veiksnių autentiškumo patvirtinimas", "Type": "Tipas", "Type (CV-Library)": "Tipas (CV-Library)", "Type @ to tag a colleague.": "Norėdami p<PERSON> kole<PERSON>, įrašykite @.", "Type of employment: full time, part time, permanent, temporary, etc.": "<PERSON><PERSON> pobūdis: visą darbo dien<PERSON>, ne visą darbo dieną, <PERSON><PERSON><PERSON><PERSON><PERSON>, la<PERSON><PERSON> ir t. t.", "Type question here...": "Įrašykite klausimą čia...", "Type to search or add...": "<PERSON><PERSON><PERSON><PERSON>, norėdami ieškoti ar pridėti...", "Type to search...": "Pa<PERSON>š<PERSON>...", "Typesetter": "Tipografas", "UK & Ireland": "<PERSON><PERSON><PERSON>", "UKPRN": "UKPRN", "UPDATED": "ATNAUJINTA", "URL": "URL", "Uighur, Uyghur": "Uigūrų kalba", "Ukrainian": "Ukrainiečių kalba", "Unable to extract data from this file.": "Nepavyko išgauti duomenų iš <PERSON>o.", "Unable to respond to interview": "<PERSON><PERSON><PERSON> į pokalbį", "Unassigned": "Nepriskirta", "Unauthorized": "Neįgaliotas", "Unavailable": "Nėra", "Uncategorized": "Neįtraukta į kate<PERSON><PERSON>s", "Uncategorized stages": "Nėra kategorizuotų etapų", "Undo": "At<PERSON>uk<PERSON> ve<PERSON>", "Unfortunately, it has a bug with uploading videos on a cellular connection.": "<PERSON><PERSON>, įvyko k<PERSON><PERSON>, susijusi su vaizdo įrašų įkėlimu naudojant mobilųjį ryšį.", "Unfortunately, there are no available interview times left. Please contact your recruiter.": "<PERSON><PERSON>, laisvų pokalbių laikų nebėra. Prašome susisiekti su personalo atrankos specialistu.", "Unfortunately, your browser is not supported.": "<PERSON><PERSON>, j<PERSON><PERSON><PERSON> naršyklė nepalaikoma.", "Unique candidates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Unknown": "Nežinoma", "Unlimited": "<PERSON><PERSON><PERSON><PERSON>", "Unlink": "<PERSON><PERSON><PERSON>", "Unlink file from its project": "<PERSON><PERSON><PERSON><PERSON>ą iš jo projekto", "Unpin": "Atsegti", "Unpublish": "<PERSON><PERSON><PERSON><PERSON>", "Unpublished at": "Nepaskelbta", "Unsaved changes": "Neišsaugoti p<PERSON>i", "Unseen notifications first": "Pirm<PERSON><PERSON><PERSON> rod<PERSON>i nematytus p<PERSON>", "Unselect all": "<PERSON><PERSON><PERSON><PERSON> visus", "Unselect video": "<PERSON><PERSON><PERSON><PERSON> vaizdo įrašą", "Unspecified": "Nenurody<PERSON>", "Until": "<PERSON><PERSON>", "Until date": "<PERSON><PERSON> da<PERSON>", "Until project end": "<PERSON><PERSON> projekto p<PERSON>", "Until specific date": "<PERSON><PERSON> k<PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Update SCIM group mappings": "Atnaujinkite SCIM grupės su<PERSON>", "Update comment": "<PERSON><PERSON><PERSON><PERSON><PERSON> komenta<PERSON>", "Update project failure reason": "Atnaujinkite projekto nesėkmės priežastį", "Update requisition": "Atnaujinti para<PERSON>šką", "Updated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Updates for :project": "Atnaujinimai :project", "Upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Upgrade Teamdash": "<PERSON><PERSON><PERSON><PERSON><PERSON> „Teamdash“", "Upgrade to access this feature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON> naudotis š<PERSON> funkcija", "Upload": "Įkelti", "Upload a file of the :version version of the font.": "Įkelkite :version versijos šrifto fail<PERSON>.", "Upload a video file": "Įkelti vaizdo įrašo failą", "Upload button text": "Įkėlimo mygtuko tekstas", "Upload photo": "Įkelti nuotrauką", "Upload video": "Įkelti vaizdo įrašą", "Uploaded video: {fileName}": "Įkeltas vaizdo įrašas: {fileName}", "Urban Transit Services": "<PERSON><PERSON><PERSON> tranzito p<PERSON>", "Urdu": "Urdu kalba", "Usages": "<PERSON><PERSON><PERSON><PERSON> bū<PERSON>", "Use AI language models to pre-fill the form from the file uploaded.": "Naudokite dirbtinio intelekto ka<PERSON><PERSON>, kad iš an<PERSON><PERSON> u<PERSON>ėte formą iš įkelto failo.", "Use AI to compare candidates. To avoid bias, you should select multiple candidates at a time.": "Naudokite dirbtinį intelektą kandidatams palyginti. Kad i<PERSON>, vienu metu turėtumėte pasirinkti kelis kandidatus.", "Use AI to generate a transcript and summary based on the video call recording.": "Naudokite dirbtinį intelektą, kad pagal vaizdo skambučio įrašą sukurtumėte nuorašą ir santrauką.", "Use AI to generate a transcript and summary based on the video call recording.)": "Naudokite dirbtinį intelektą, kad sukurtumėte nuorašą ir santrauką pagal vaizdo skambučio įrašą.)", "Use a [survey] merge tag like this:": "Naudokite to<PERSON> [survey] sujungi<PERSON>:", "Use a font which is easy to read": "Naudokite lengvai įskaitomą šriftą", "Use browser timezone": "Naudokite naršyklės laiko juost<PERSON>", "Use cursor keys to navigate calendar dates": "<PERSON><PERSON><PERSON><PERSON><PERSON> datoms naršyti naudokite žymeklio k<PERSON>", "Use custom description for LinkedIn": "Naudokite pasirinktinį \"LinkedIn\" apraš<PERSON>ą", "Use custom signature": "Naudoti unikalų parašą", "Use custom stage categories": "Naudoti skirtingas etapų kategorijas", "Use heading colour": "<PERSON><PERSON><PERSON>", "Use landing theme": "<PERSON><PERSON><PERSON> te<PERSON>", "Use multiple languages": "<PERSON><PERSON><PERSON> k<PERSON> ka<PERSON>", "Use photos of your team members or office, not stock images": "Naudokite savo komandos narių ar biuro nuotraukas, o ne nuotraukas iš <PERSON>abloninių šaltinių", "Use plain background": "<PERSON><PERSON><PERSON> p<PERSON> foną", "Use primary colour": "<PERSON><PERSON><PERSON> pagrin<PERSON>ę spalvą", "Use text signature": "Naudoti tekstinį parašą", "Use this field to link the form with a project. All the applicants will go to the selected stage.": "Šį laukelį naudokite norėdami susieti formą su projektu. Visi pareiškėjai pateks į pasirinktą etapą.", "Use this for confidentiality notices, disclaimers, etc.": "Naudokite tai konfidencialumo <PERSON>, atsakomybės atsisakymui ir pan.", "Use this form as template for creating new forms": "Naudoti šią formą kaip šabloną naujoms formoms kurti", "Use this value": "<PERSON><PERSON><PERSON> š<PERSON> re<PERSON>", "Use your company's identity provider to log in to Teamdash. Requires a company email address.": "Prisijun<PERSON><PERSON> prie \"Teamdash\" galite naudodami savo įmonės tapatybės teikėją. Reikalingas įmonės el. pa<PERSON><PERSON> adres<PERSON>.", "Used for GDPR compliance": "Naudojama siekiant užtikrinti atitiktį BDAR reikalavimams", "Used for job board exports.": "Naudojamas darbo skelbimų lentos eksportavimui.", "Used forbidden tag: [:tag] is not allowed.": "Naudojama uždrausta žyma: [:tag] neleidžiama.", "Used in email signatures": "Naudojama el. laiškų parašuose", "Used in email signatures, job board exports.": "Naudojama el. la<PERSON> para<PERSON>, darbo skelbimų lentos eksportavimui.", "Used in email signatures, job board exports. PNG format suggested.": "Naudojama el. laiškų para<PERSON>, darbo skelbimų lentos eksportavimui. Rekomenduojamas PNG formatas.", "Used in job board exports.": "Naudojama darbo skelbimų lentos eksportavimui.", "Useful for collaborating with external recruitment partners. After enabling this setting, an admin can switch on password access for specific user accounts.": "Naudinga bendradarbiaujant su išorės įdarbinimo partneriais. Įjungus šį nustatymą, administratorius gali suteikti prieigą su slaptažodžiu konkretiems naudotojams.", "User": "Vartotojas", "User Avatar": "<PERSON><PERSON><PERSON><PERSON> avatara<PERSON>", "User administration": "<PERSON><PERSON><PERSON><PERSON> administravimas", "User deactivated": "Naudo<PERSON>jas <PERSON>", "User deactivated!": "Naudotojas deaktyvuotas!", "User message": "Naudotojo <PERSON>", "User role": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "User roles": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "User-Agent": "Vartotojo agentas", "Username": "<PERSON><PERSON><PERSON><PERSON>", "Users": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Users & accounts": "Naudotoja<PERSON> ir p<PERSON>", "Users added!": "Pridėta naudotojų!", "Users count": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Users imported!": "Naudotojai importuoti!", "Usually email from Teamdash goes <NAME_EMAIL>. You still get all the replies to your inbox because Teamdash includes your email in the Reply-To header.": "Paprastai el. laiškai iš „Teamdash“ siunčiami iš <EMAIL>. Jūs vis tiek gausite visus atsakymus į savo pašto d<PERSON>, nes „Teamdash“ įtraukia jūsų el. pašto adresą į „Reply-To“ antraštę.", "Utilities": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Utilities Administration": "Komunalinių paslaugų administravimas", "Utility System Construction": "Komunalinių paslaugų sistemos statyba", "Uzbek": "Uzbekų kalba", "VP2 Feed": "VP2 srautas", "Valid": "<PERSON><PERSON><PERSON>", "Valid \"until date\" consent": "<PERSON><PERSON><PERSON><PERSON><PERSON> „iki datos“.", "Valid HEX color codes start with a # followed by 3 or 6 characters.": "Galiojantys HEX spalvų kodai prasideda ženklu #, po kurio seka 3 arba 6 simboliai.", "Valid data processing consent missing.": "Trūksta galiojančio sutikimo tvarkyti duomenis.", "Valid long term consent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Valid until": "<PERSON><PERSON><PERSON> iki", "Valid, but missing long term consent": "<PERSON><PERSON><PERSON>, bet tr<PERSON><PERSON><PERSON> il<PERSON>o su<PERSON>o", "Valid, but without \"until date\" consent": "<PERSON><PERSON><PERSON>, bet be su<PERSON><PERSON>o „iki datos“.", "Validity": "G<PERSON><PERSON><PERSON><PERSON>", "Value": "Reikš<PERSON><PERSON>", "Value is saved to database, label is visible to candidate.": "Re<PERSON><PERSON><PERSON>ė išsaugoma duomenų bazėje, <PERSON><PERSON><PERSON> matoma kandida<PERSON>.", "Value must be a number": "<PERSON><PERSON><PERSON><PERSON><PERSON> turi b<PERSON><PERSON>", "Value must not be a number.": "<PERSON><PERSON><PERSON><PERSON><PERSON> neturi b<PERSON><PERSON>.", "Values": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Varnisher": "Lakuotojas", "Vehicle Repair and Maintenance": "Transporto priemonių remontas ir priežiūra", "Venda": "Vendų kalba", "Venture Capital and Private Equity Principals": "Rizikos kapitalo ir privataus kapitalo vadovai", "Verified": "<PERSON><PERSON><PERSON><PERSON>", "Verify": "<PERSON><PERSON><PERSON><PERSON>", "Vertical position": "<PERSON><PERSON><PERSON><PERSON>", "Veterinary": "Veterinaras", "Veterinary Services": "Veterina<PERSON><PERSON> p<PERSON>", "Video": "Vaizdo įrašas", "Video call": "<PERSON><PERSON><PERSON><PERSON>", "Video call URL": "Vaizdo s<PERSON>mbučio URL", "Video call created!": "Su<PERSON><PERSON>s vaizdo ska<PERSON>!", "Video call password": "<PERSON><PERSON><PERSON><PERSON>", "Video call password:": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "Video interview": "<PERSON><PERSON><PERSON><PERSON> pokal<PERSON>", "Video interview title": "<PERSON><PERSON><PERSON><PERSON> poka<PERSON> pavadin<PERSON>s", "Video interviews": "<PERSON><PERSON><PERSON><PERSON>", "Video interviews sent!": "Išsiųsti vaizdo pokalbiai!", "Video message": "<PERSON><PERSON>z<PERSON>", "Video message sent!": "Išsiųstas vaizdo p<PERSON>š<PERSON>s!", "Video source": "Vaizdo įrašų šaltinis", "Vietnamese": "Vietnamiečių kalba", "View": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View Job": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View ad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View all shared candidates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visus bendrus kandidatus", "View all tasks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> visas užduotis", "View cNPS results": "<PERSON><PERSON><PERSON><PERSON><PERSON>ėti cNPS rezultatus", "View candidate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "View comments": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "View details": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ti informaciją", "View feed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View form": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View image ad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "View instructions.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ins<PERSON>uk<PERSON>.", "View invitation": "<PERSON>ž<PERSON>ūrė<PERSON> k<PERSON>timą", "View invites": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View landing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> puslapį", "View landing page": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> puslapį", "View messages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> projekt<PERSON>", "View public form": "Peržiūrėti viešą <PERSON>ą", "View requisition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View social media campaign results": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> socialinės žiniasklaidos kampanijos rezultatus", "View stages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et<PERSON>us", "View submission": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View submissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "View template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Viewed file preview": "<PERSON><PERSON><PERSON>", "Viewed profile": "Perž<PERSON>ūrėtas profilis", "Viewing file {cvIndex} of {cvsTotal}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {cvIndex} failas iš {cvsTotal}", "Vimeo": "\"Vimeo\"", "Visagiste": "Vizažistas", "Visibility": "<PERSON><PERSON><PERSON><PERSON>", "Visible for limited users": "Matoma ribotą prieigą turintiems naudotojams", "Visible to all": "Visiems matoma", "Visma Hop": "Visma Hop", "Visual Merchandising": "Vizualinė <PERSON>ky<PERSON>", "Vocalist": "Vokalistas", "Vocational Rehabilitation Services": "Profesin<PERSON><PERSON> reabilitac<PERSON> p<PERSON>", "Vocational education": "<PERSON><PERSON><PERSON>", "Vocational secondary education": "Profesinis viduri<PERSON><PERSON>", "Volap_k": "Volap_k", "Voluntary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Voluntary work": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>", "Volunteer": "<PERSON><PERSON><PERSON>", "Waiter": "Padavėjas", "Waiting for camera and microphone access.": "Laukiama prieigos prie kameros ir mikrofono.", "Waiting for recording to start.": "<PERSON><PERSON><PERSON>, kol prasidės įrašymas.", "Walloon": "Valonų kalba", "Warehouse": "<PERSON><PERSON><PERSON>", "Warehousing": "Sand<PERSON><PERSON><PERSON><PERSON>", "Warehousing and Storage": "Sandėliavimas ir saug<PERSON>s", "Warning period": "Įspėjimo laikotarpis", "Warning period unit": "Įspėjimo laiko<PERSON><PERSON> vienetas", "Warranty until": "Garantija iki", "Washerman": "Skalbėjas", "Waste Collection": "Atliekų surinkimas", "Waste Treatment and Disposal": "Atliekų apdorojimas ir šalinimas", "Water Supply and Irrigation Systems": "Vandens tiekimo ir dr<PERSON><PERSON><PERSON> siste<PERSON>", "Water, Waste, Steam, and Air Conditioning Services": "Vandens, atliekų, garų ir oro kondicionavimo paslaugos", "We are currently generating the transcription": "Šiuo metu rengiame nuoraš<PERSON>", "We are currently processing this candidate's profile. Please check again in a few minutes.": "Šiuo metu tvarkome šio kandidato anketą. Prašome patikrinti dar kartą po kelių minučių.", "We are glad to have you on board. If you ever forget your Teamdash login address, just find this email.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad prisi<PERSON>g<PERSON>te prie mūsų. Jei kada nors pamiršite savo „Teamdash“ prisijungimo adresą, tiesiog susiraskite šį el. laišką.", "We are informing you that [organization_name] is currently storing your personal data. The data might include your CV, contact information and other information you provided when applying to a position.": "Informuojame jus, kad [organization_name] š<PERSON>o metu saugo jūsų asmens duomenis. Šie duomenys gali apimti jūsų gyven<PERSON>, kontaktinę informaciją ir kitą informaciją, kurią pateikėte kreipdamiesi dėl darbo vietos.", "We are updating our": "Atnaujiname savo", "We could not parse the uploaded file.": "Nepavyko išanalizuoti įkelto failo.", "We have populated the form with default categories. Add or remove categories to match your hiring process. You can drag the items to re-order them.": "Formoje įrašėme standartines kategorijas. Pridėkite arba pašalinkite kategorijas, kad jos atitiktų jūsų atrankų procesą. <PERSON><PERSON><PERSON> perst<PERSON> element<PERSON>, kad pakeistumėte jų eiliškumą.", "We offer": "<PERSON><PERSON><PERSON><PERSON>", "We received the following error with your Google Calendar integration:": "<PERSON><PERSON><PERSON> „Google“ kalendoriaus integracijos klaidą:", "We will remind you in specified time!": "Priminsime jums nurodytu laiku!", "We will use different infrastructure to deliver the message.": "Praneš<PERSON>ui perduoti naudosime skirtingą infrastruktūrą.", "We won't ask for dropout reasons anymore. You can always turn this back on in Settings.": "Daugiau nebeklausime iškritimo priežasčių. Visada galite tai vėl įjungti nustatymuose.", "Web designer": "Interneto svetainių dizaineris", "Webhook endpoint URL": "\"Webhook\" galinio punkto URL", "Wednesday": "Trečiadienis", "Week": "Savaitė", "Weekdays": "<PERSON><PERSON> die<PERSON>", "Weeks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Weighting": "<PERSON><PERSON><PERSON><PERSON>", "Welcome to Teamdash": "Sveiki atvykę į „Teamdash“", "Welcome to Teamdash – please verify your e-mail": "Sveiki atvykę į „Teamdash“ – patvirtinkite savo el. paštą", "Welder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Wellness and Fitness Services": "Sveikatingumo ir fitneso paslau<PERSON>", "Welsh": "Valų kalba", "Western Frisian": "Vakarų fryzų kalba", "What happens when this action is triggered:": "<PERSON><PERSON>, kai yra akt<PERSON> veik<PERSON>:", "What is Teamdash?": "<PERSON><PERSON> <PERSON><PERSON> „Teamdash“?", "What is cNPS?": "Kas yra cNPS?", "What motivates the candidate? What are their likes and dislikes? Use specific quotes.": "Kas moty<PERSON> kandidatą? Kas jam patinka ir nepatinka? Naudokite konkrečias citatas.", "What next?": "<PERSON><PERSON>?", "When": "<PERSON><PERSON>", "When creating new projects, these stages are used by default.": "<PERSON><PERSON>t naujus projektus šie etapai naudojami standart<PERSON>.", "When publishing to LinkedIn, use City, Country format (e.g. \"London, United Kingdom\"). For remote positions, use only country.": "Skelbdami informaciją į „LinkedIn“, naudokite miesto, <PERSON><PERSON><PERSON> (pvz., „Londonas, <PERSON><PERSON>ė Karaly<PERSON>“). Nuotolinių pozicijų atveju naudokite tik šalį.", "When someone applies to a position, they implicitly consent to you processing their data.": "<PERSON><PERSON>o pat<PERSON>a para<PERSON> d<PERSON> da<PERSON> v<PERSON>, jis netiesiogia<PERSON> su<PERSON>, kad tvar<PERSON> jo duomen<PERSON>.", "When switched on, this comment will be visible when sharing the candidate with :client.": "Įjungus šią <PERSON>, š<PERSON> komenta<PERSON> bus matomas dali<PERSON> kandidatu su :client.", "When switched on, this entry will be visible when sharing the candidate with :client.": "Įjungus šį įrašą, jis bus matomas dali<PERSON> kandidatu su :client.", "When the project ends, you must not further process these candidates' data.": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> kandidatų duomenų toliau tvarkyti negalima.", "When there are candidates without email addresses or phone numbers, you can only schedule a fixed time for the interview.": "Kai kandidatai neturi el. pašto adresų ar telefono numerių, darbo pokalbiui galite paskirti tik konkretų laiką.", "When there are candidates without email addresses, you can only schedule a fixed time for the interview.": "Kai kandidatai neturi el. pa<PERSON>, darbo pokalbiui galite paskirti tik fiksuotą laiką.", "When this form is included in a landing page, colors will be taken from the landing page theme.": "Kai ši forma įtraukiama į nukreipimo puslapį, spalvos perimamos iš nukreipimo puslapio temos.", "When this is turned off, candidate files from all projects are visible to all users.": "Kai š<PERSON> funkci<PERSON>, visų projektų kandidatų failai matomi visiems naudotojams.", "When you apply, we may conduct a background check using public databases and websites and utilizing a web search engine. Your resume may be retained for a maximum period of one year.": "Kai pateikiate paraišką, galime patikrinti jūsų praeitį, naudodamiesi viešomis duomenų bazėmis ir svetainėmis bei interneto paieškos sistema. Jūsų gyvenimo aprašymas gali būti saugomas ne ilgiau kaip vienerius metus.", "When you choose :anyTime as the scheduling type, the candidates receive an e-mail with a link where they can freely choose a slot in the calendar that is open for all recruiters.": "Pasirinkus :anyT<PERSON> kaip <PERSON>, kandidatai gaus el. lai<PERSON> su nuoroda, kurioje galės laisvai pasirinkti visiems įdarbintojams tinkamą laiką kalendoriuje.", "When you link a resource (i.e. project, landing) with a team, it will be accessible to that team and all teams above that team.": "Kai susiejate išteklių (pvz., projektą, puslapį) su komanda, jis bus prieinamas tai komandai ir visoms aukščiau esančioms komandoms.", "When you mark a recruitment project as finished, your right to process the candidate's data ends.": "<PERSON>, kad p<PERSON> atrankų projektą, j<PERSON><PERSON><PERSON> teisė tvarkyti kandidato duomenis taip <PERSON> bai<PERSON>.", "Where do hires come from?": "Sėkmingiausi samdy<PERSON> kanalai", "Which features do you value the most?": "<PERSON><PERSON><PERSON> savybes vertinate labiausiai?", "Which form types should trigger this?": "Kokie formų tipai turėtų tai aktyvuoti?", "Which timezone would you like to use?": "<PERSON><PERSON><PERSON> laiko juostą norė<PERSON> naudoti?", "While the speakers are not identified in the transcript, you will do your best to differentiate the speakers.": "Nors nuorašė nėra nurodyti pokalbio <PERSON>i, jums reik<PERSON>s atlikti geriausia įmanomą darbą juos atskiriant ir identifikuojant.", "While you were away": "Kol jūsų nebuvo", "White collar, specialists": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "Who are you talking to?": "Su kuo kalbate?", "Wholesale": "Didmeninė <PERSON>", "Wholesale Alcoholic Beverages": "Didmeninė prekyba alkoholiniais g<PERSON>", "Wholesale Apparel and Sewing Supplies": "Didmeninė preky<PERSON> drabuž<PERSON> ir siuvimo re<PERSON>", "Wholesale Appliances, Electrical, and Electronics": "Did<PERSON><PERSON><PERSON> pre<PERSON> buitine technika, elektros ir elektronikos prietaisais", "Wholesale Building Materials": "Didmeninė prekyba statybinėmis medžiagomis", "Wholesale Chemical and Allied Products": "Didmeninė prekyba cheminėmis medžiagomis ir susijusiais produktais", "Wholesale Computer Equipment": "Didmeninė prekyba kompiuterine įranga", "Wholesale Drugs and Sundries": "Didmeninė prekyba vaistais ir kitais reikmenimis", "Wholesale Food and Beverage": "Didmeninė prekyba maisto produktais ir g<PERSON>rimais", "Wholesale Footwear": "Didmeninė prekyba avalyne", "Wholesale Furniture and Home Furnishings": "Didmeninė prekyba baldais ir namų apyvokos reikmenimis", "Wholesale Hardware, Plumbing, Heating Equipment": "Didmeninė prekyba aparatūra, sa<PERSON><PERSON><PERSON>a, šildymo įranga", "Wholesale Import and Export": "Didmeninis importas ir eksportas", "Wholesale Luxury Goods and Jewelry": "Didmeninė prekyba prabangos prekėmis ir juvelyriniais dirbiniais", "Wholesale Machinery": "Didmeninė prekyba technine įranga", "Wholesale Metals and Minerals": "Didmeninė prekyba metalais ir mineralais", "Wholesale Motor Vehicles and Parts": "Didmeninė prekyba variklinėmis transporto priemonėmis ir j<PERSON> dalimis", "Wholesale Paper Products": "Didmeninė prekyba popieriaus gaminiais", "Wholesale Petroleum and Petroleum Products": "Didmeninė prekyba nafta ir naftos produktais", "Wholesale Photography Equipment and Supplies": "Didmeninė prekyba fotografijos įranga ir reikmenimis", "Wholesale Raw Farm Products": "Didmeninė prekyba neapdorotais ūkio produktais", "Wholesale Recyclable Materials": "Didmeninė prekyba perdirbamomis medžiagomis", "Whoops!": "Oi!", "Why do candidates drop out?": "Kandidatūrų atmetimo priežastys", "Why not start with a real open vacancy. Who is missing from your team?": "<PERSON><PERSON><PERSON><PERSON> nepradėjus nuo tikros laisvos darbo vietos. Ko trūksta jūsų komandai?", "Why should I use cNPS?": "Kod<PERSON>l turėčiau naudoti cNPS?", "Why work here?": "Ko<PERSON><PERSON>l verta čia dirbti?", "Width": "<PERSON><PERSON><PERSON>", "Will They Be a Manager": "Ar jie bus vadovai", "Wind Electric Power Generation": "Vėjo elektros energijos gamyba", "Windows master": "Langų meistras", "Wineries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Wireless Services": "Belaidžio ryš<PERSON> p<PERSON>", "With active exports": "Su aktyviu eksportavimu", "With best": "Su geriausiais", "With descendant teams": "Su išplėstinėmis komandomis", "With summary": "Su santrauka", "Without active exports": "Be aktyvaus eksportavimo", "Wolof": "Volofų kalba", "Women's Handbag Manufacturing": "Moteriškų rankinių gamyba", "Wood Product Manufacturing": "Medienos gaminių gamyba", "Woodcutter": "<PERSON><PERSON><PERSON><PERSON>", "Work at sea": "<PERSON><PERSON>", "Work in shifts": "<PERSON><PERSON>", "Work schedule (profession.hu)": "<PERSON><PERSON> (profession.hu)", "Work time (cv.lt)": "<PERSON><PERSON> la<PERSON> (cv.lt)", "Work times": "<PERSON><PERSON> la<PERSON>", "Work times (CVK/CVM)": "<PERSON><PERSON> laikai (CVK/CVM)", "Worker": "Darbuotojas", "Working days": "<PERSON><PERSON> die<PERSON>", "Working days (SS.lv)": "<PERSON><PERSON> (SS.lv)", "Working hours": "Darbo valandos", "Working hours (SS.lv)": "Darbo valandos (SS.lv)", "Working schedule (profession.hu)": "<PERSON><PERSON> (profession.hu)", "Working week description": "<PERSON><PERSON> sa<PERSON>", "Workplace type": "Darbo vietos tipas", "Write a interview time confirmation SMS": "Parašyti darbo pokalbio laiko patvirtinimo <PERSON> žinutę", "Write a message for candidate to choose a time": "Paraš<PERSON><PERSON>, kad kandidatas pasirinktų laiką", "Write a message to send the video interview link": "Paraš<PERSON>i <PERSON>, kad būtų galima siųsti vaizdo pokalbio nuorodą", "Write a reference check message": "Parašyti rekomendacijų tikrinimo pranešimą", "Write a rejection letter": "Parašyti atsisakymo la<PERSON>šką", "Write activities": "Raš<PERSON><PERSON> ve<PERSON>", "Write candidates": "<PERSON><PERSON><PERSON><PERSON> kandidatus", "Write projects": "Rašyti projektus", "Writing / Editing": "Rašymas / redagavimas", "Writing and Editing": "<PERSON><PERSON><PERSON><PERSON> ir <PERSON>", "Xhosa": "Kosų kalba", "Xlsx file": "Xlsx failas", "YTD": "<PERSON><PERSON> metų pr. iki dabar", "Year": "Metai", "Years": "Metai", "Yes": "<PERSON><PERSON>", "Yes, anonymize the candidates!": "<PERSON><PERSON>, nuasmeninti kandidatus!", "Yes, delete the candidates permanently!": "<PERSON><PERSON>, i<PERSON><PERSON><PERSON> kandidatus visam laikui!", "Yiddish": "<PERSON><PERSON><PERSON>", "Yoruba": "Jorubų kalba", "You approved this requisition.": "<PERSON><PERSON><PERSON>.", "You are a summarization tool integrated within an applicant tracking system (ATS).": "Esate apibendrinimo įrankis, integruotas į kandidatų stebėjimo sistemą (ATS).", "You are a tool meant for cleaning up transcripts integrated within an applicant tracking system (ATS).": "Esate įrankis, skirtas nuorašams tvarkyti, integruotas į kandidatų stebėjimo sistemą (ATS).", "You are about to submit an empty form. Are you sure? Click Submit again to confirm.": "Ketinate pateikti tuščią formą. Ar esate tikri? Norėdami <PERSON>, dar kartą spustelėkite Pateikti.", "You are currently using :full_seats and :limited_seats.": "Šiuo metu naudojate :full_seats ir :limited_seats.", "You are currently using :seats.": "Šiuo metu naudojate :seats.", "You are editing a project template.": "Redaguojate projekto šabloną.", "You are editing an action in a template.": "Redaguojate veiksmą esantį šablone.", "You are not allowed to edit this comment.": "Jums <PERSON>idžiama redaguoti š<PERSON> komentaro.", "You are not authorized to perform this action.": "<PERSON><PERSON><PERSON> neturite teis<PERSON>s atlik<PERSON> š<PERSON> ve<PERSON>.", "You are receiving this email because we received a password reset request for your account.": "Šį el. lai<PERSON><PERSON> gaunate to<PERSON>, kad su<PERSON> jūsų paskyros <PERSON>žodžio keitimo p<PERSON>.", "You are using an outdated version of MS Edge. All features might not work as intended.": "<PERSON>udo<PERSON><PERSON> p<PERSON> „MS Edge“ versiją. Visos funkcijos gali veikti ne taip, kaip numa<PERSON>.", "You can add a new candidate below.": "Žemiau galite pridėti naują kandidatą.", "You can add a new form by clicking the button below.": "Galite pridėti naują formą spustelėdami žemiau esantį mygtuką.", "You can add a video interview by clicking the button below.": "Video interviu galite pridėti paspaudę žemiau esantį mygtuką.", "You can add an API key by clicking the button below.": "G<PERSON>te pridėti API raktą spustelėdami žemiau esantį mygtuką.", "You can add file types by clicking the button below.": "Failų tipus galite pridėti spustelėję toliau esantį mygtuką.", "You can add integrations to:": "Galite pridėti integraciją su:", "You can also refuse consent from the link. If you consent to data processing, you can withdraw your consent at any time in the future. If you ignore this message, we will assume you do not consent to further data processing.": "Taip pat galite atsisakyti duoti sutikimą naudodamiesi šia nuoroda. Jei sutinkate su duomenų tvarkymu, savo sutikimą galite bet kada ateityje atšaukti. Jei ignoruosite šį pranešimą, la<PERSON><PERSON><PERSON>, kad nesutinkate su tolesniu duomenų tvarkymu.", "You can also use [survey] to include a feedback survey with a 0-10 rating scale.": "<PERSON><PERSON> pat <PERSON>ite naudoti [survey], jei norite įtraukti atsiliepimų apklausą su 0-10 balų vertinimo skale.", "You can ask for data processing consent renewals from {candidateCount}.": "Duomenų tvarkymo sutikimo atnaujinimo galite prašyti iš {candidateCount}.", "You can choose whether to use the question from here or your custom form when sending a message to the candidate.": "Siųsdami pranešimą kandidatui galite pasirinkti, ar naudoti klaus<PERSON> i<PERSON>, ar pasirinkt<PERSON><PERSON> form<PERSON>.", "You can contact these candidates about relevant job offers.": "Su šiais kandidatais galite susisiekti dėl atitinkamų darbo p<PERSON>ūlymų.", "You can create an API key by clicking the BambooHR logo in top right corner and choosing \"API Keys\".": "API raktą galite susikurti paspaudę „BambooHR“ logotipą viršutiniame dešiniajame kampe ir pasirinkę „API raktai“.", "You can embed this form on your landing page.": "<PERSON><PERSON><PERSON> formą galite įterpti į savo nukreipimo puslapį.", "You can give additional instructions to the AI using the \"Add the tag if...\" field.": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>, jei...\" galite DI pateikti papildomas instrukcijas.", "You can leave additional feedback:": "Galite palikti papildomų komentarų:", "You can limit the time available to the candidate by setting the days, dates and times between which they must choose a slot.": "Galite apriboti kandidato pasirenkamą laiką nustatydami dienas, datas ir laik<PERSON>, i<PERSON> kuri<PERSON> jis turi pasirinkti tinkamą laiką.", "You can load saved SMS templates later.": "Išsaugotus SMS šablonus galite įkelti vėliau.", "You can load saved message templates later. Attachments are not saved.": "Išsaugotus žinučių šablonus galite įkelti vėliau. Priedai neišsaugomi.", "You can modify the filters to change this.": "Kad tai p<PERSON>, koregu<PERSON><PERSON> filtrus.", "You can modify which reasons are available in the Organization settings.": "Priežastis galima keisti organizacijos nustatymuose.", "You can not merge the same dropout reason.": "Negalite sujungti tų pačių iškritimo priežasčių.", "You can now close this window.": "<PERSON><PERSON><PERSON> uždaryti šį langą.", "You can now proceed to the next question.": "Galite pereiti prie kito klausimo.", "You can now send cNPS surveys with more questions!": "Dabar galite siųsti cNPS apklausas su daugiau klausimų!", "You can now track why candidates dropped out of the application process. If this candidate applies again, you will see this information on their candidate card.": "Dabar galite <PERSON>, kod<PERSON><PERSON> kandidatai iškrito iš atrank<PERSON>. Jei šis kandidatas kandidatuos dar kartą, šią informaciją matysite kandidato kortelėje.", "You can optionally associate the candidate presentation with a project.": "Galite pasirinktinai susieti kandidato pristatymą su projektu.", "You can optionally specify the spoken language. It may improve results.": "Galite pasirinktinai nurodyti šnekamąją kalbą. Tai gali pagerinti rezultatus.", "You can read about our privacy statement here.": "Apie mūsų privatumo politiką galite perskaityti čia.", "You can revoke an individual consent. This will not affect other consents.": "Galite atšaukti individualų sutikimą. Tai nepaveiks kitų sutikimų.", "You can revoke this consent any time by contacting {org} at {email}.": "Šį sutikimą galite bet kada atšaukti susisiekę su {org} adresu {email}.", "You can send cNPS feedback surveys with all types of messages.": "Galite siųsti cNPS grįžtamojo ryšio apklausas su visų tipų pranešimais.", "You can send this message to all references or only to personal or professional references.": "<PERSON><PERSON><PERSON> žinutę galite siųsti dėl visų rekomendacijų arba tik dėl asmeninių ar profesinių rekomendacijų.", "You can set the link to the privacy policy in {organizationSettings}.": "Nuorodą į privatumo politiką galite nustatyti {organizationSettings}.", "You can use the <i>Only summary</i> mode to hide candidate name, gender, ethnicity, and age.": "Norėdami paslėpti kandidato vard<PERSON>, pava<PERSON><PERSON>, lytį, etnin<PERSON> kilmę ir amžių, galite naudo<PERSON> <i>tik santraukos</i> re<PERSON><PERSON><PERSON>.", "You can use the {onlySummary} mode to hide candidate name, gender, ethnicity, and age.": "Naudodami {onlySummary} rež<PERSON>ą galite paslėpti kandidato vardą, lytį, etninę kilmę ir amžių.", "You can:": "Galite:", "You do not have permission to create new dropout reasons.": "Neturite leidimo kurti naujų iškritimo priežasčių.", "You do not have permission to disable asking for dropout reasons.": "Neturite leidimo išjungti klausimo d<PERSON>l iškritimo priežasčių.", "You don't have access to the submissions of this form.": "<PERSON><PERSON>s neturite prieigos prie šios formos duomenų.", "You don't have access to this requisition.": "<PERSON><PERSON><PERSON> neturite prieigos prie šios <PERSON>.", "You dont have permission to move candidates in project. Your changes will not be saved.": "Neturite leidimo per<PERSON>ti kandidatų į projektą. Jūsų pakeitimai nebus iš<PERSON>ugoti.", "You have 1 invite pending for this interview.|You have :count invites pending for this interview.": "Į šį darbo pokalbį laukiama 1 kvietimo.|Į šį darbo pokalbį laukiama :count kvietimų.", "You have :count incomplete tasks without a deadline.": "Turite :count ne<PERSON><PERSON><PERSON> be galutinio termino.", "You have :count tasks overdue.": "Turite :count <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "You have a asynchronous video interview in progress. You can continue your progress.": "<PERSON><PERSON><PERSON> vaizdo interviu. Galite tęsti pokalbį.", "You have a valid data processing consent for {consentScore}% of your database.": "Turite galiojantį sutikimą {consentScore}% jūsų duomenų bazės duomenims tvarkyti.", "You have active filters. Clear filters to hide this.": "Turite aktyvių filtrų. Išvalykite filtrus, kad tai paslėptumėte.", "You have already asked for reference from this person at {lastMessageTime}.": "<PERSON><PERSON> pap<PERSON>š<PERSON><PERSON> šio asmens rekomendacijos {lastMessageTime}.", "You have already finished this asynchronous video interview.": "<PERSON>au baigėte šį asinchroninį vaizdo interviu.", "You have been invited to a video interview.": "Buvote pakviestas į vaizdo pokalbį.", "You have exhausted your attempt limit. You can no longer answer this question.": "Išnaudojote savo bandymų limitą. Jūs nebegalite atsakyti į šį klausimą.", "You have no SCIM groups.": "Neturite SCIM grupių.", "You have no mail identities set up yet.": "Dar nesate nustatę pašto tapatybių.", "You have no requisitions yet.": "Dar neturite jokių paraiškų.", "You have no video interviews yet.": "Dar neturite vaizdo pokalbių.", "You have not created any consent subtypes yet.": "Dar <PERSON>sukūrėte jokių sutikimo tipų.", "You have not created any project failure reasons yet.": "Dar nesukūrėte jokių projekto nesėkmės priežasčių.", "You have not sent this presentation via e-mail yet, but you can share this url with anyone.": "<PERSON><PERSON> prezentacijos dar nesiuntėte el. p<PERSON>, ta<PERSON><PERSON><PERSON> galite dalytis šiuo url adresu su visais.", "You have reached the recommended attempt limit, but you can still submit responses.": "Pasiekėte rekomenduojamą bandymų limitą, bet vis tiek galite pateikti atsakymus.", "You have successfully finished the interview.": "Sėkmingai baigėte pokalbį.", "You have {candidateCount} and {slotCount}": "<PERSON><PERSON><PERSON> turite {candidateCount} ir {slotCount}", "You haven't added any custom fonts yet.": "Dar nepridėjote jokių pasirinktinių šriftų.", "You may not change the number of segments. The number of segments in the output must be the same as in the input.": "Negalite keisti segmentų skaičiaus. Segmentų skaičius išvestyje turi būti toks pat kaip ir įvestyje.", "You may not change the time (timestamp) data of the segments; you may only change the text.": "<PERSON><PERSON><PERSON> negalite keisti segmentų laiko (la<PERSON>) duomenų; galite pakeisti tik tekstą.", "You may only omit input segments from your output if you are very certain that they are noise and do not contain human speech.": "Neįtraukti įvesties turinio į išvesties turinį galima tik tuo atveju, jei esate visi<PERSON> tikri, kad jie yra nere<PERSON>šmingi ir juose nėra žmo<PERSON> kal<PERSON>.", "You must accept the privacy policy.": "Privalote sutikti su privatumo politika.", "You must be a Microsoft tenant administrator to continue.": "<PERSON><PERSON><PERSON><PERSON>, turi<PERSON> b<PERSON><PERSON> „Microsoft“ valdytojo administratorius.", "You must call the provided tool function with an array of strings in which each string is a single sentence that summarizes some aspect of the interview.": "Turite iškviesti pateiktą įrankio funkciją naudodami eilučių ma<PERSON>vą, kurioje kiekviena eilutė yra vienas sa<PERSON>, apibendrinantis tam tikrą pokalbio aspektą.", "You must call the provided tool function with the cleaned up transcript segments. ": "Pateiktą įrankio (eng. tool) funkciją turite i<PERSON> (eng. call) su išvalytais nuorašo segmentais.", "You must ensure that the timestamps of segments do not change during processing. The timestamp of when a certain segment happened must be the same in the output as it was in the input.": "<PERSON><PERSON>, kad a<PERSON><PERSON><PERSON><PERSON><PERSON> metu segmentų laiko žymos nesikeistų. <PERSON><PERSON>, kada įvyko tam tikras segmentas, i<PERSON><PERSON><PERSON><PERSON> turi būti to<PERSON>a pati, kaip ir įvestyje.", "You must get approval from :users": "Turite gauti pat<PERSON> iš :users", "You must include yourself as manager or member for confidential projects.": "Į konfidencialius projektus turite įtraukti save kaip v<PERSON> arba narį.", "You must select at least one slot in this scheduling type.": "Privalote pasirinkti bent vieną šio planavimo tipo laiką.", "You must try your best to fix spelling errors and improve readability without removing information or adding new content.": "Turite stengtis ištaisyti rašybos klaidas ir pagerinti skaitomumą nepašalindami informacijos ir nepridėdami naujo turinio.", "You must try your best to fix spelling errors and improve readability without removing information or adding new content. You may replace words if they seem out of context.": "Turite stengtis ištaisyti rašybos klaidas ir pagerinti skaitomumą nepašalindami informacijos ir nepridėdami naujo turinio. <PERSON><PERSON><PERSON> p<PERSON><PERSON>, jei jie atrodo iškrentantys i<PERSON> konte<PERSON>.", "You probably want to automate your GDPR compliance": "Tikriausiai norite automatizuoti atitiktį BDAR reikalavimams", "You rejected this requisition.": "<PERSON><PERSON><PERSON> parai<PERSON>.", "You will always have the option to skip a month. Same goes with purging the database, you always get a notification beforehand and an option to skip. You will always have the final say on any actions.": "Visada turėsite galimybę praleisti mėnesį. Tas pats pasakytina ir apie duomenų bazės valymą – prieš tai visada gausite pranešimą ir galimybę praleisti. Visuomet galėsite priimti galutinį sprendimą dėl bet kokių veiksmų.", "You will be notified once everybody has made their decision.": "Jums bus pranešta, kai visi priims sprendimą.", "You're almost there!": "Jau beveik baigta!", "You've been assigned project manager in the project :positionName": "<PERSON><PERSON><PERSON> esate paskirtas projekto vadovu projekte :positionName", "You've been removed as recruiter from a job requisition": "Buvote pašalintas iš darbo paraiškos kaip įdarbintojas", "Your Teamdash integration :integration has not been working correctly.": "Jū<PERSON>ų \"Teamdash\" integracija :integration neveikė tin<PERSON>.", "Your Teamdash login details": "Jū<PERSON>ų „Teamdash“ prisijungimo duomenys", "Your URL should be like \"/employer/12345/settings/...\"": "Jūsų URL turėtų būti toks: „/darbdavys/12345/nustatymai/...“", "Your account must be accessed via SSO": "Jūsų paskyra turi būti pasiek<PERSON>a per SSO", "Your account needs verification": "Jūsų paskyrą reikia patvirtinti", "Your approval is needed for :position": "Reikia jūsų patvirtinimo :pozicija", "Your approval is needed for a job requisition for :position.": "Reikalingas jū<PERSON>, kad būtų galima pateikti darbo paraišką dėl :pozicijos.", "Your approval is no longer needed for :position": "Jūsų patvirtinimo <PERSON> :pozicija", "Your browser timezone ({browserTimezone}) is different from your current timezone setting ({currentTimezone}).": "<PERSON><PERSON><PERSON><PERSON> na<PERSON>š<PERSON><PERSON> laiko juosta ({browserTimezone}) skiriasi nuo nustatytos laiko juostos ({currentTimezone}).", "Your daily agenda": "Jūsų kasdien<PERSON>", "Your decisions is missing.": "Trūksta jūsų sprendimų.", "Your e-mail has been verified and your organization's Teamdash instance has been created.": "Jūsų el. paštas buvo patvirtintas ir sukurta atskira jūsų organizacijos „Teamdash“ kopija.", "Your employer is special - prove it with interesting arguments": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> yra y<PERSON> – įrodykite tai įdomiais argumentais", "Your job ad is available at:": "Jūsų darbo skelbimą galima rasti:", "Your job ad is public!": "<PERSON><PERSON><PERSON><PERSON> darbo skelbimas yra vieša<PERSON>!", "Your meta description will appear when sharing on social media and IM platforms. Keep the length below 150 characters.": "Jūsų meta aprašymas bus rodomas dalijantis socialinėje žiniasklaidoje ir tiesioginių pokalbių platformose. Ilgis neturėtų viršyti 150 simbolių.", "Your only function is to clean up an auto-generated transcript segments that will be provided to you by the user.": "Vienintelė jūsų funkcija - išvalyti automatiškai sukurtus nuorašo segmentus, kuriuos pateiks naudotojas.", "Your only function is to clean up the text of auto-generated transcript segments that will be provided to you by the user.": "Vienintelė jūsų funkcija yra išvalyti automatiškai sugeneruotų nuorašo segmentų, kuriuos jums pateiks vartotojas, tekstą.", "Your page title will appear when sharing on social media and IM platforms. Keep the length below 55 characters.": "Jūsų puslapio pavadinimas bus rodomas dalijantis socialinėje žiniasklaidoje ir tiesioginių pokalbių platformose. Ilgis neturėtų viršyti 55 simbolių.", "Your primary function is to summarize a video interview using a transcript that is provided to you by the user.": "Pagrindinė jūsų funkcija - apibendrinti vaizdo interviu pagal naudotojo pateiktą nuorašą.", "Your question": "Jūsų klaus<PERSON>", "Your report will be emailed to you.": "Jūsų ataskaita bus išsiųsta el. paštu.", "Your requisition :position has been approved": "Jūsų paraiška :position patvir<PERSON><PERSON>", "Your requisition :position was rejected!": "<PERSON>ūsų paraiška :position buvo atmesta!", "Your response is uploading. Please wait until it is finished.": "<PERSON>ū<PERSON>ų atsakymas įkeliamas. <PERSON><PERSON><PERSON><PERSON>, kol jis bus baigtas.", "Your responses have been recorded": "<PERSON><PERSON><PERSON><PERSON> atsakymai įrašyti", "Your selection contains candidates without a valid phone number. Please uncheck them.": "Jūsų pasirinkime įtraukta kandidatų, neturinčių galiojančio telefono numerio. Panaikinkite jų žymėjimą.", "Your selection contains candidates without email addresses. Please uncheck them.": "Kai kurių jūsų pasirinktų kandidatų el. pašto adresas nenurodytas. Panaikinkite jų žymėjimą.", "Your selection contains candidates without email addresses. You can only create pre-scheduled interviews for this selection.": "Jūsų atrankoje yra kandidatų be el. pašto adresų. Šiai atrankai galite kurti tik iš anksto suplanuotus darbo pokalbius.", "Your session has expired and your last change was not saved. Sorry. We will refresh the page for you.": "Jūsų sesija baigėsi ir paskutinis pakeitimas nebuvo išsaugotas. Atsiprašome. Atnaujinsime puslapį.", "Your subscription includes :generic_seats.": "Jūsų prenumerata apima :generic_seats.", "Your subscription includes :included_full_seats and :included_limited_seats.": "Jūsų prenumerata apima :included_full_seats ir :included_limited_seats.", "Your username is the subdomain name you use for logging in to BambooHR.": "<PERSON><PERSON><PERSON><PERSON> naudotojo vardas yra subdomeno pavadinimas, kurį naudojate prisijungdami prie „BambooHR“.", "Youtube": "\"Youtube\"", "Zhuang, Chuang": "Džuangų kalba", "Zoom calls": "„Zoom“ skambučiai", "Zoos and Botanical Gardens": "Zoologijos sodai ir botanikos sodai", "Zulu": "Zulų kalba", "[current_user_name] has invited you to use Teamdash.": "[current_user_name] pakvietė jus naudotis \"Teamdash\".", "a project": "projektas", "after the candidate submits a form": "po to, kai kandidatas pateikia formą", "after the candidate submits a video response": "kandidatui pateikus vaizdo at<PERSON>kymą.", "after the candidate submits their referees": "po to, kai kandidatas pateikia savo rekomendacijos davėju<PERSON>.", "after they have selected their preferred time slot": "pasirin<PERSON><PERSON> laiko <PERSON>.", "after {delay} {unit} in this stage": "po {delay} {unit} šiame etape", "after {duration} in this stage": "po {duration} šiame etape", "agriculture / forestry / fishing": "<PERSON><PERSON><PERSON><PERSON> / mi<PERSON><PERSON><PERSON><PERSON><PERSON> / žu<PERSON><PERSON><PERSON><PERSON><PERSON>", "all": "visi", "all candidates": "visi kandidatai", "and": "ir", "annually": "kasmet", "anonymized candidate": "anoni<PERSON>s kandi<PERSON>s", "answer_saved": "Atsakymas <PERSON>!", "apprenticeships.gov.uk entity ID": "apprenticeships.gov.uk subjekto ID", "approval|approvals": "patvir<PERSON><PERSON>s|patvirtinimai", "assisting / administration": "asistavimas / administravimas", "attempted": "pabandyta", "attempts_exhausted": "Išnaudojote bandymų limitą. Vis dar galite pateikti atsakymą, tačiau samdymo komanda matys jūsų pakartotinių bandymų skaičių.", "attempts_left": "Lik<PERSON> bandymai: {0}", "automatically": "automatiškai", "automatically at {time}": "automati<PERSON><PERSON> {time}", "available_period": "<PERSON><PERSON><PERSON>", "banking": "bankininkystė", "between {start} and {end}": "tarp {start} ir {end}", "bold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bold-and-italic": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir k<PERSON>", "but not public": "bet ne viešai", "buttons.submit_text": "buttons.submit_text", "cNPS": "cNPS", "cNPS comment": "cNPS komentaras", "cNPS is an excellent indicator for aligning your recruitment team behind improving candidate experience.": "cNPS yra informatyvu<PERSON>, padedantis atrankų komandai gerinti kandidatų patirtį.", "cNPS or Candidate Net Promoter Score is a measure to gauge your candidate satisfaction.": "cNPS arba kandidatų grynasis populiarumo balas - tai priemon<PERSON>, kuria vertinamas kandidatų pasitenkinimas.", "cNPS or Candidate Net Promoter Score is a measure to gauge your candidates' satisfaction with the hiring process.": "cNPS (angl. Candidate Net Promoter Score) - ta<PERSON>, kuri<PERSON> vertinamas kandidatų pasitenkinimas atrankų procesu.", "cNPS responses": "cNPS atsakymai", "cNPS score": "cNPS balas", "cNPS survey": "cNPS apklausa", "cNPS survey form": "cNPS apklausos forma", "cNPS survey forms": "cNPS apklausos formos", "cNPS survey message for candidate": "cNPS apklausos pranešimas kandidatui", "cNPS survey message with merge tag": "cNPS apklausos pranešimas su sujungimo žyma", "cNPS.question.here": "čia", "candidate has tag {tag}": "kandidatas turi <PERSON> {tag}", "candidate | candidates": "kandidatas | kandidatai", "career page": "<PERSON><PERSON><PERSON><PERSON>", "catering": "maiti<PERSON><PERSON>", "check the integration credentials": "patikrinkite integracijos įgaliojimus", "click here to decline the invitation": "spustel<PERSON><PERSON><PERSON>, jei norite atsisakyti kvietimo", "click here to request another": "spustelė<PERSON><PERSON> č<PERSON>, jei norite užsisakyti kit<PERSON>", "click the link below and re-connect by clicking the Save button": "spustelėkite toliau pateiktą nuorodą ir vėl prisijunkite spustelėdami mygtuką <PERSON>i", "clicked": "paspaud<PERSON>", "clicked conversion": "paspausta: konversija", "clients": "klientai", "color picker": "spalvų rinkik<PERSON>", "completed": "paba<PERSON><PERSON>", "confidential": "konfidencialu", "confirm": "Patvirtinkite", "construction / real estate": "statyba / nekilnojamasis turtas", "contains": "yra", "contains all": "yra visi", "culture / entertainment /recreation": "kult<PERSON>ra / pramogos / poilsis", "current": "<PERSON><PERSON><PERSON><PERSON>", "customer service": "klientų aptarnavimas", "data_use": "Jūsų duomenys bus naudojami susisiekti su jumis dėl būsimų laisvų darbo vietų.", "day": "diena", "days": "dienos", "daysInStageShort": "d", "default": "numatytasis", "does not contain": "nėra", "draft": "j<PERSON><PERSON><PERSON><PERSON>", "due on {dueOnDate}": "iki {dueOnDate}", "due {dueInDiff}": "d<PERSON>l {dueInDiff}", "durationpicker.custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "e.g #007bff": "pvz., #007bff", "e.g. Feedback request": "pvz., atsiliepimų užklausa", "e.g. Send thx 4 appl. or Send rejection letter": "pvz., Siųsti ačiū už 4 paraiškas arba Siųsti atmetimo la<PERSON>šką.", "e.g. example.org": "pvz., pavyzdys.org", "e.g. {imap.gmail.com:993/imap/ssl}INBOX or {outlook.office365.com:993/imap/ssl}INBOX": "pvz., {imap.gmail.com:993/imap/ssl}INBOX arba {outlook.office365.com:993/imap/ssl}INBOX.", "edited": "<PERSON><PERSON><PERSON><PERSON>", "edited by {userName}": "redagavo {userName}", "education / science / research": "švietimas / mokslas / moksliniai tyrimai", "electronics / telecommunication": "elektronika / telekomunikacijos", "elements.file.dndDescription": "elements.file.dndDescription", "elements.file.dndTitle": "elements.file.dndTitle", "elements.file.uploadButton": "elements.file.uploadButton", "elements.gallery.dndDescription": "elements.gallery.dndDescription", "elements.gallery.dndTitle": "elements.gallery.dndTitle", "elements.gallery.uploadButton": "elements.gallery.uploadButton", "elements.list.remove": "elements.list.remove", "elements.multifile.dndDescription": "elements.multifile.dndDescription", "elements.multifile.dndTitle": "elements.multifile.dndTitle", "energetics / natural resources": "energetika / gamtiniai ištekliai", "every 10th minute (:00, :10, :20, :30, :40, :50)": "kas de<PERSON><PERSON><PERSON> minutę (:00, :10, :20, :30, :40, :50)", "every 15th minute (:00, :15, :30, :45)": "kas penkioliktą minutę (:00, :15, :30, :45)", "failed": "<PERSON><PERSON><PERSON><PERSON>", "feedback_title": "Galite palikti papildomų atsiliepimų:", "fill handover form": "užpildykite perdavimo formą", "finance": "finansai", "finished": "bai<PERSON>i", "form": "forma", "formbuilder.items.label": "<PERSON><PERSON><PERSON>", "from 1 candidate|from {count} candidates": "iš 1 kandidato|iš {count} kandidatų", "from global settings": "iš <PERSON>jų nustatymų", "from project": "i<PERSON> projekto", "from team :team": "<PERSON><PERSON> :team", "full hour (:00)": "visa valanda (:00)", "full seat|full seats": "vieta užpildyta|vietos užpildytos", "get insights": "gauti įžvalgų", "greater than": "<PERSON><PERSON><PERSON> nei", "has all of": "turi visas", "has already submitted reference": "jau pat<PERSON><PERSON> reko<PERSON>", "has none": "neturi nė vieno", "has none of": "neturi nė vieno iš", "has one of": "turi vieną iš", "health care / social work": "sve<PERSON><PERSON> p<PERSON> / socialinis darbas", "hello": "<PERSON><PERSON><PERSON>, {name}!", "here": "čia", "hourly": "valan<PERSON><PERSON>", "hours": "valandos", "human resources / training": "žmogiškieji ištekliai / mokymas", "i_consent": "<PERSON><PERSON><PERSON>, kad {org} tvarkytų mano duomenis ir susisiektų su manimi dėl laisvų darbo vietų iki {date}", "i_dont_consent": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad mano asmens duomenys būtų toliau tvarkomi", "if": "jei", "if this is a video call interview": "jei tai interviu vai<PERSON>.", "if you think this is a mistake, activate the integration again": "<PERSON><PERSON> man<PERSON>, kad tai k<PERSON>, dar kart<PERSON> aktyvinkite integraciją", "iframe URL": "iframe URL", "iframe from URL": "iframe iš URL adreso", "immediately after saving this form": "iš karto po šios formos išsaugojimo", "in progress": "<PERSON><PERSON><PERSON><PERSON>", "in project {project}": "projekte {project}", "in stage {stage}": "etape {stage}", "in {anonymizeTime}": "per {anonymizeTime}", "in {project}": "yra {project}", "in {renewalTime}": "s<PERSON><PERSON><PERSON><PERSON> {renewalTime}", "inactive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": "Į jūsų el. paštą išsiųstas pakvietimas. Galite uždaryti šį langą. Bet kuriuo metu galite grįžti į šį url adresą ir pamatyti savo pokalbio laiką.", "information technology / e-commerce": "informacinės technologijos / elektronin<PERSON>", "interview": "interviu", "interview_length": "Interviu t<PERSON>", "interview_scheduled": "Suplanuotas interviu", "interview_scheduling": "<PERSON><PERSON><PERSON>", "interview_slot_text": "Interviu", "intro": "{name} iš {org} norėtų gauti jūsų sutikimą toliau tvarkyti Jūsų asmens duomenis. Šie duomenys gali apimti Jūsų kontaktinę informaciją, gy<PERSON><PERSON> a<PERSON> ir kitus duomenis, kuri<PERSON>s pateikėte kreipdamiesi dėl darbo vietos įmonėje {org}.", "intro_1": "Buvote pakviestas į vaizdo pokalbį. Įdarbintojas Jums paruošė vieną klausimą. | Buvote pakviestas į vaizdo pokalbį. Įdarbintojas Jums parengė {count} klausimus.", "intro_2": "Kai spustelėsite toliau esantį pradžios mygtuką, jūsų bus paprašyta suteikti interneto kameros ir mikrofono leidimus.", "intro_3": "NB! Prie kiekvie<PERSON>, kurį norite pat<PERSON>i, b<PERSON><PERSON><PERSON> spustelėkite mygtuką Pateikti.", "invite": "<PERSON><PERSON><PERSON><PERSON>", "invited": "pakviesta", "invite|invites": "kvietimas|kvietimai", "is false": "yra k<PERSON>", "is near": "yra netoli", "is one of": "yra vienas iš", "is true": "yra tiesa", "italic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "job_ad_category_export": "Eksportuoti", "key": "raktas", "know more about {candidateName}": "<PERSON><PERSON><PERSON><PERSON> da<PERSON> a<PERSON> {candidate<PERSON>ame}", "last contacted :timeAgo": "paskutinį kartą susisiekta :timeAgo", "law": "<PERSON><PERSON><PERSON>", "less than": "ma<PERSON><PERSON><PERSON> nei", "limited seat|limited seats": "ribotas vietų skaičius|ribotas vietų skaičius", "log in": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mailbox": "p<PERSON><PERSON><PERSON>", "management": "v<PERSON><PERSON><PERSON>", "managers": "vadovai", "manufacturing / production": "gamyba / produkcija", "marketing / advertising / pr": "rinkodara / reklama / PR", "mechanics / engineering": "mechanika / inžinerija", "media / new media / creative": "žiniasklaida / naujoji ž<PERSON> / kūryba", "messages.cta_text": "messages.cta_text", "messages.cta_url": "messages.cta_url", "messages.success_text": "messages.success_text", "minutes": "<PERSON><PERSON><PERSON><PERSON>", "missing email address": "nėra el. pašto adreso", "missing phone number": "nėra telefono numerio", "mo": "mo", "month": "<PERSON><PERSON><PERSON><PERSON>", "monthly": "kas mėnesį", "no attempts left|1 attempt left|{count} attempts left": "nebeliko bandymų|liko 1 bandymas| liko bandymų: {count}", "no responses yet": "dar n<PERSON>ra at<PERSON>", "no_available_slots": "<PERSON><PERSON>, laisvų pokalbių laikų nebėra. Prašome susisiekti su personalo atrankos specialistu.", "no_camera": "<PERSON><PERSON> jū<PERSON> dabar<PERSON> įrenginyje nėra fotoaparato, naudokite išmanųjį telefoną.", "no_camera_step_1": "Įjunkite fotoaparatą", "no_camera_step_2": "Nuskaitykite šį QR kodą:", "none": "nėra", "normal": "įprastas", "not any of": "nei vienas iš", "not opened": "neat<PERSON><PERSON><PERSON>", "of type": "tipo", "ok": "ok", "on hold": "sustab<PERSON><PERSON>", "opened": "atidaryta", "opened conversion": "atidaryta: konversija", "optional": "pasirinktinai", "or": "arba", "per annum": "per metus", "per day": "per dieną", "per hour": "per valandą", "play_recording": "<PERSON><PERSON><PERSON><PERSON> įrašą", "policy_review": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mū<PERSON> <a href=\"{url}\" class=\"text-medium\" target=\"_blank\">privatumo politiką</a>, toliau nurodykite sprendimą:", "present": "<PERSON><PERSON><PERSON><PERSON>", "privacy policy": "privatumo politika", "private": "privatus", "project is in status {status}": "projekto b<PERSON>a {status}", "public": "<PERSON><PERSON><PERSON><PERSON>", "public / governmental service": "viešosios / vyriausybin<PERSON>s p<PERSON>", "publish your job ads in various job portals": "skelbti savo darbo skelbimus įvairiuose darbo portaluose", "quarter": "ketvir<PERSON>", "question_no": "Klausimas #{0}", "record": "Įrašas", "record_again": "Atmeskite vaizdo įrašą ir įrašykite iš naujo", "record_new": "Įrašykite naują vaizdo įrašą", "recorder.confirm": "Patvirtinkite", "recorder.ok": "<PERSON><PERSON><PERSON> vaizdo įrašas:", "recorder.play_recording": "<PERSON><PERSON><PERSON><PERSON> įrašą", "recorder.record": "Įrašas", "recorder.record_again": "Pašalinkite vaizdo įrašą ir įrašykite iš naujo", "recorder.record_new": "Įrašykite naują vaizdo įrašą", "recorder.stop": "Sustabdyti įrašymą", "recorder.time_left": "<PERSON><PERSON><PERSON><PERSON>", "recorder.warning_not_sent_user": "Vaizdo įrašas dar neiš<PERSON>. Spustelėkite <PERSON>, kad i<PERSON><PERSON>ugot<PERSON>ė<PERSON> klaus<PERSON>.", "reply to this message to contact support": "atsakykite į šią žinutę, norėdami susisiekti su palaikymo tarnyba", "revocation": "atšaukimas", "sales / retail": "pardavimai / maž<PERSON>inė <PERSON>", "schedule_button": "<PERSON><PERSON><PERSON><PERSON><PERSON> pokalbį", "seat|seats": "vieta|vietos", "seconds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "security": "<PERSON><PERSON><PERSON><PERSON>", "see incoming e-mails from candidates directly on the candidate profile": "matyti gaunamus kandidatų el. laiškus tiesiogiai kandidato profilyje", "see your existing calendar events when scheduling interviews": "peržiūrėkite esamus susitikimus kalendoriuje prieš planuodami interviu", "select_time": "Prašome pasirinkti pokalbio laiką", "send candidate information to your HR platform": "siųsti informaciją apie kandidatą į savo HR platformą", "slot_selector_help": "Spustelėkite ir pasirinkite pokalbio laik<PERSON>. Jei norite pakeisti laiką, pele galite vilkti laiko intervalą arba jį pašalinti ir laiką rinktis iš naujo.", "sourced": "LN priedėlis", "ss.lv images": "ss.lv nuo<PERSON>ukos", "stage": "etapas", "start": "Pradžia", "stop": "Sustabdyti įrašymą", "submit": "Pat<PERSON><PERSON><PERSON>", "talent pool": "Kandidatų duombazė", "thanks": "Ačiū! Gavome jūsų atsakymus!", "thanks_ok": "Ačiū! Jūsų pokalbis yra numatytas", "time_full": "<PERSON><PERSON><PERSON> jau rezervavo šį laiką", "time_left": "<PERSON><PERSON><PERSON><PERSON>", "time_limit": "<PERSON><PERSON><PERSON><PERSON> la<PERSON> limitas {0}", "title": "pavadinimas", "to_here": "čia", "tourism / hotels": "turizmas / viešbučiai", "transportation / logistics": "transportas / logistika", "trix.acceptedExtensions": "trix.acceptedExtensions", "trix.acceptedMimes": "trix.acceptedMimes", "undefined": "neapib<PERSON><PERSON><PERSON><PERSON>", "unknown": "<PERSON><PERSON><PERSON><PERSON>", "unlimited": "<PERSON><PERSON><PERSON><PERSON>", "update your browser here": "atnaujinkite savo naršyklę čia", "upgrade": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "video_info": "Prisijungti prie vaizdo pokalbio čia:", "video_info_password": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "video_ok": "Vaizdas tvar<PERSON>!", "view_question": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "voluntary work": "<PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>", "warning_not_sent": "Atsakymas dar nepateiktas. Spustelėkite Pateikti, kad išsiųstumėte atsakymą.", "week": "savaitė", "when a button is clicked": "kai paspa<PERSON><PERSON><PERSON> my<PERSON>", "when this template is used for video call interviews": "kai šis šablonas naudojamas vaizdo skambučių pokalbiams", "will be stopped automatically": "bus sustabdytas automatiškai", "will be submitted automatically": "bus pateikta automatiškai", "with status": "su statusu", "work at sea": "da<PERSON><PERSON> j<PERSON>", "you": "jūs", "{0} There are no more open slots in this interview.|{1} There is one open slot in this interview.|[2,*] There are :count open slots in this interview.": "{0} Daugiau laisvų vietų šiame darbo pokalbyje nėra.|{1} Šiame darbo pokalbyje yra viena laisva vieta.|[2,*] Šiame darbo pokalbyje yra :count laisvos (-ų) vietos (-ų).", "{0} are no more open slots|{1} is one open slot|[2,*] are :count open slots": "{0} nėra daugiau laisvų laiko|{1} yra vienas laisvas laiko tarpas|[2,*] yra :count laisvus laiko tarpus", "{1} :count project from this template|[2,*] :count projects from this template": "{1}:count projektas iš šio šablono|[2,*] :count projektai (-ų) iš šio šablono", "{count} actions in {status} projects": "{count} veiksmai {status} projektuose", "{count} candidate|{count} candidates": "{count} kandidatas| {count} kandidatai", "{count} project will be included in the report.|{count} projects will be included in the report.": "{count} projektas bus įtrauktas į ataskaitą.| {count} projektai bus įtraukti į ataskaitą.", "{count} received": "{count} gauta", "{count} settings to reassign or clear": "{count} nust<PERSON><PERSON><PERSON>, kuriuos reikia iš naujo priskirti arba išvalyti", "{count} stage actions to reassign": "{count} et<PERSON><PERSON>, k<PERSON><PERSON>s reikia priskirti iš naujo", "{count} stage actions will be deleted": "{count} etapo veiksmai bus ištrinti", "{count} stage|{count} stages": "{count} etapas|{count} etapai (-ų)", "{count} task|{count} tasks": "{count} už<PERSON>otis| {count} užduotys", "{count} user": "{count} naudoto<PERSON>", "{dataLength} of {dataTotal}": "{dataLength} iš {dataTotal}", "{delay} {unit} after the project status changes": "{delay} {unit} nuo projekto b<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "{duration} after the project status changes": "{duration} pasi<PERSON><PERSON><PERSON> projekto b<PERSON><PERSON>i", "{mergeTag} will be blank.": "{mergeTag} bus tuščias.", "{remainingAttempts}/{maxAttempts} attempt(s) left": "Liko {remainingAttempts} / {maxAttempts} bandymai", "{selected} of {total} candidates selected": "{selected} iš {total} atrinktų kandidatų", "{selected} of {total} selected": "pasirinkta {selected} iš {total}", "{total} of {total} selected": "pasirinkta {total} iš {total}", "{upgradeCTA} to get access to all of our video features.": "{upgradeCTA} kad gautumėte prieigą prie visų mūsų vaizdo funkcijų.", "{upgradeCTA} to get access to all of our video interview analysis tools.": "{upgradeCTA} kad gautumėte prieigą prie visų mūsų vaizdo pokalbių analizės įrankių.", "{userName} shared {candidateCount} candidates": "{userName} pasidalino {candidateCount} kandidatais", "{userName} shared {candidateCount} candidates for {projectName}": "{userName} pasidalijo {candidateCount} kandidatais (-ų) {projectName}", "{user} shared 1 candidate for {position} | {user} shared {count} candidates for {position}": "{user} pasidalino 1 kandidatu į {position} | {user} pasidalino {count} kandidatais į {position}", "{user} shared 1 candidate | {user} shared {count} candidates": "{user} pasidalino 1 kandidatu | {user} pasidalino {count} kandidatais"}