# RecruitLab

## Installation

### docker compose

Needs: node v16

```
# psql pg_dump pg_restore for wrangling databases
brew install libpq

cp .env.example .env
docker compose up -d pgbouncer pgsql-17 pgsql-test
php composer install
php artisan key:generate
# Following telescope:install command modifies config/app.php (revert changes manually)
php artisan telescope:install 
php artisan migrate
php artisan tenant:create rl.localhost
php artisan tenant:create get.localhost

# add this to your npmrc:
echo @laraform:registry=https://gitlab.com/api/v4/projects/16645025/packages/npm/ >> .npmrc
echo '@teamdash:registry=https://gitlab.com/api/v4/projects/16645025/packages/npm/' >> .npmrc
echo "//gitlab.com/api/v4/projects/16645025/packages/npm/:_authToken=${GITLAB_ACCESS_TOKEN}" >> .npmrc

yarn
# frontend needs to know the routes. re-run when adding new routes.
php artisan ziggy:routes
yarn dev
```

### native

1. install PHP
2. turn on opcache
3. `PHP_CLI_SERVER_WORKERS=4 php artisan serve --port=80`

### Working with GraphQL

You need `fswatch` to run these instructions. Install it beforehand (e.g. `brew install fswatch`).

```
php artisan lighthouse:ide-helper
chmod +x generate-graphql.sh
./generate-graphql.sh
fswatch -o graphql/schema.graphql | xargs -n1 -I{} ./generate-graphql.sh
```

## Development

### Background jobs

#### Running background jobs

```
php artisan queue:work --queue=broadcast,high,default
```

#### Clearing background job queue

```
php artisan queue:clear --queue=high
```

### A few notes about the staging environment

- If your branch name starts with _rec-_, it gets deployed to _rec-000-branch.feature.portaal24.ee_
- These branches don't run scheduled jobs as of now
- These branches get the test1 instance's database
- The _development_ branch gets deployed to \*.portaal24.ee
- system db-s migrations are never run for feature branches

### Testing

1. `./vendor/bin/xsd2php convert app/DataExchange/CvDotLt/config/xsd2php.yml app/DataExchange/CvDotLt/config/*.xsd`
1. `cp patches/HasMany.php vendor/laraform/laraform-laravel/src/Database/Relationships/HasMany.php`
1. `brew install libreoffice python3 pipx`
1. `pipx install extract-msg`
1. `yarn pub-watch`
1. make sure `pgsql-test` container is running
1. ensure `NODE_PATH` is correct in `.env`
1. ensure `PYTHON_PATH` points to the environment where `extract-msg` is installed (e.g. `/Users/<USER>/.local/pipx/venvs/extract-msg/bin/python3`)
1. run tests (`vendor/bin/phpunit` or `vendor/bin/paratest`)
1. P.S. Running paratest right after starting the container will have errors since every process tries to create the template databases and migrate them. So run at least one regular test with phpunit before running with paratest.

### Linux php setup

```
sudo add-apt-repository ppa:ondrej/php
sudo apt update

sudo apt install php8.3 php8.3-cli php8.3-fpm php8.3-{bz2,curl,mbstring,intl,ldap,zip,gd,bcmath,exif,opcache,excimer,mailparse,imap,xml,pgsql,redis,soap,imagick}

cp config/php/99-recruit.ini /etc/php/8.3/cli/conf.d/
cp config/php/99-recruit.ini /etc/php/8.3/fpm/conf.d/
```

### Production worker setup

Two queues/workers: high (8 processes), default (2 processes).

Please send only network-bound stuff to high queue.

```
[program:recruit-all-queues-worker]
process_name=%(program_name)s_%(process_num)02d
command=nice -n 12 php /var/www/recruit/artisan queue:work redis --sleep=10 --tries=1 --queue=high,default
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/recruit/storage/logs/worker-all.log
stopwaitsecs=3600
startsecs=0

[program:recruit-high-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=nice -n 12 php /var/www/recruit/artisan queue:work redis --sleep=10 --tries=1 --queue=high
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/var/www/recruit/storage/logs/worker-high.log
stopwaitsecs=3600
startsecs=0
```

### Unable to generate landing previews?

Check the logs, it is possible that Chrome is unable to write to some specific directory it's using for crash logs.

```bash
rm -rf /tmp/Crashpad
```

### Running e2e tests

The used tenant will be reset!

Install Chromium executable:

```
yarn run playwright install
```

Add this to your .env:

```
E2E_REF_SLUG=e2e # Tenant name without the random str.
E2E_URL=http://e2e.localhost # Access url
```

And run `yarn run playwright test`
