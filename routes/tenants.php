<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Http\Controllers\Tenant\AddOnController;
use App\Http\Controllers\Tenant\Api\ProfessionHuController;
use App\Http\Controllers\Tenant\ApiKeyController;
use App\Http\Controllers\Tenant\AuditController;
use App\Http\Controllers\Tenant\Auth\LoginController;
use App\Http\Controllers\Tenant\AuthConfigController;
use App\Http\Controllers\Tenant\CandidateSummaryController;
use App\Http\Controllers\Tenant\ConsentSubtypeController;
use App\Http\Controllers\Tenant\CustomStageCategoryController;
use App\Http\Controllers\Tenant\DropoutReasonController;
use App\Http\Controllers\Tenant\FileTypeController;
use App\Http\Controllers\Tenant\FormBuilderController;
use App\Http\Controllers\Tenant\IntegrationController;
use App\Http\Controllers\Tenant\InterviewController;
use App\Http\Controllers\Tenant\LocationController;
use App\Http\Controllers\Tenant\LocationSearchController;
use App\Http\Controllers\Tenant\MailIdentityController;
use App\Http\Controllers\Tenant\MeetingAnalysisController;
use App\Http\Controllers\Tenant\MessageController;
use App\Http\Controllers\Tenant\PresentationController;
use App\Http\Controllers\Tenant\ProjectActionController;
use App\Http\Controllers\Tenant\ProjectController;
use App\Http\Controllers\Tenant\ProjectFailureReasonController;
use App\Http\Controllers\Tenant\ProjectLogController;
use App\Http\Controllers\Tenant\ProjectRoleController;
use App\Http\Controllers\Tenant\ProjectUsersController;
use App\Http\Controllers\Tenant\Pub\AsyncVideoInterviewController;
use App\Http\Controllers\Tenant\Pub\GoogleCalendarController;
use App\Http\Controllers\Tenant\Pub\GoogleSearchConsoleVerificationController;
use App\Http\Controllers\Tenant\Pub\MicrosoftWebhookController;
use App\Http\Controllers\Tenant\Pub\OpenViduController;
use App\Http\Controllers\Tenant\Pub\SurveyController;
use App\Http\Controllers\Tenant\ReferenceFormController;
use App\Http\Controllers\Tenant\ScimSettingsController;
use App\Http\Controllers\Tenant\StageController;
use App\Http\Controllers\Tenant\StructuredJobAdController;
use App\Http\Controllers\Tenant\TeamsController;
use App\Http\Controllers\Tenant\VideoInterviewController;
use App\Http\Controllers\Tenant\VideoUploadController;
use App\Http\Middleware\ApiKeyMiddleware;
use App\Http\Middleware\ProfessionHuMiddleware;
use App\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Route;

Route::middleware(['tenancy.enforce'])
    ->namespace('App\\Http\\Controllers\\Tenant\\')
    ->group(function () {
        Route::middleware('web')->group(function () {
            Route::get('/', [LoginController::class, 'showLoginForm'])->name('root');
            Route::post('/', [LoginController::class, 'login']);

            Route::get('/auth/otp', 'Auth\LoginController@otpLogin')->name('auth.otp');

            // Saving time of future travellers: this ends up evaluating \Laravel\Ui\AuthRouteMethods::auth (part of "laravel/ui" package) and declaring about 14 auth-related routes (including /login).
            Auth::routes();

            Route::get('/auth/sso', 'Auth\LoginController@sso');
            Route::get('/auth/sso/callback', 'Auth\LoginController@ssoCallback');
            Route::get('/auth/ssoProvider/{config}', 'Auth\LoginController@ssoProvider')->name('auth.ssoProvider');
            Route::get('/auth/ssoProvider/{config}/callback', 'Auth\LoginController@ssoProviderCallback')->name('auth.ssoProvider.callback');

            Route::group(['middleware' => ['auth', '2fa', 'billing', 'late-redirects']], function () {
                Route::get('dashboard', 'DashboardController@index');

                Route::get('guide', 'GuideController@show');

                Route::get('resetted', 'DashboardController@resetted');

                Route::post('/projects/addUsers', 'ProjectController@addUsersToProject');
                Route::get('projects/getProjectsForAdding', 'ProjectController@getProjectsForAdding');
                Route::post('/projects/updateStageOrder', 'ProjectController@updateStageOrder');
                Route::get('/projects/totalReport', 'ProjectController@totalReport')->name('projects.totalReport');
                Route::get('/projects/projectStatusReport', 'ProjectController@projectStatusReport')->name('projects.projectStatusReport');
                Route::get('/projects/monthlyHiringReport', 'ProjectController@monthlyHiringReport')->name('projects.monthlyHiringReport');
                Route::any('projects/get', 'ProjectController@index');
                Route::any('projects/stats', 'ProjectController@stats');
                Route::any('projects/funnel', 'ProjectController@funnel');
                Route::post('projects/getReport', 'ProjectController@getReport')->name('projects.getReport');
                Route::post('projects/clone', 'ProjectController@clone');
                Route::post('projects/createFromTemplate', [ProjectController::class, 'createFromTemplate']);
                Route::post('projects/{project}/convertToTemplate', [ProjectController::class, 'convertToTemplate']);
                Route::get('projects/reports', 'ProjectController@index')->name('projects.reports');
                Route::get('projects/add-ons', 'ProjectController@index')->name('projects.add-ons');
                Route::get('projects/shared-candidates', 'ProjectController@index')->name('projects.shared-candidates');
                Route::get('projects/getStageGroups', 'ProjectController@getStageGroups');
                Route::post('projects/setCategoryForStages', 'ProjectController@setCategoryForStages');
                Route::post('projects/{project}/runScreener', 'ProjectController@runScreener');
                Route::post('projects/{project}/clearScreenerResults', 'ScreenerController@clearScreenerResults')->name('projects.clearScreenerResults');
                Route::get('add-ons', 'ProjectController@index');
                Route::get('add-ons/redirectToSchedule', [AddOnController::class, 'redirectToSchedule']);
                Route::get('projects/templates', 'ProjectController@index')->name('projects.templates');
                Route::post('projects/create_v2', [ProjectController::class, 'create_v2'])->name('projects.create_v2');
                Route::post('/projects/{project}/markFinished', 'ProjectController@markFinished');
                Route::get('projects/api/templates', [ProjectController::class, 'templates']);
                Route::resource('projects', 'ProjectController')->except(['edit']);
                Route::any('/projects', [ProjectController::class, 'index']);
                Route::post('/projects/{project}/users/inviteNewUser', [ProjectUsersController::class, 'inviteNewUser']);
                Route::put('/projects/{project}/users/{user}/setAsProjectManager', [ProjectUsersController::class, 'setAsProjectManager']);
                Route::put('/projects/{project}/users/setTeam', [ProjectUsersController::class, 'setTeam']);
                Route::resource('projects.users', ProjectUsersController::class);
                Route::get('projects/{project}/api/landings', [ProjectController::class, 'landings']);
                Route::get('projects/{project}/api/importexport', [ProjectController::class, 'importexport']);
                Route::get('projects/{project}/api/actions', [ProjectController::class, 'actions']);
                Route::get('/projects/{project}/children', [ProjectController::class, 'children']);
                Route::get('/projects/{project}/job-ads', [ProjectController::class, 'jobAds']);
                Route::get('/projects/{project}/statistics', [ProjectController::class, 'statistics']);
                Route::get('/projects/{project}/log', [ProjectLogController::class, 'index']);
                Route::get('/projects/{project}/edit', [ProjectController::class, 'edit3']);
                Route::get('/projects/{project}/actions', [ProjectController::class, 'actions']);
                Route::get('/projects/{project}/screening', [ProjectController::class, 'screening']);
                Route::get('/projects/{project}/edit_v2', [ProjectController::class, 'editV2']);
                Route::get('/projects/{project}/projectNpsResults', [ProjectController::class, 'projectNpsResults']);
                Route::get('/projects/{project}/loadStateForProjectView', 'ProjectController@loadStateForProjectView');
                Route::post('/projects/{project}/advancedSearch', 'ProjectController@advancedSearch');
                Route::any('/projects/{project}/getCandidateSimilarities', 'ProjectController@getCandidateSimilarities');
                Route::get('/projects/{project}/toXlsx', 'ProjectController@toXlsx')->name('projects.xlsx');
                Route::get('/projects/{project}/sankey', 'ProjectController@sankey')->name('projects.sankey');
                Route::post('/projects/{project}/togglePin', 'ProjectController@togglePin')->name('projects.togglePin');
                Route::post('/projects/{project}/toggleSubscription', 'ProjectController@toggleSubscription')
                    ->name('projects.subscribe');
                Route::put('/projects/{project}/stages', 'ProjectController@addStage');
                Route::post('/projects/{project}/ensureSummaries', 'CandidateSummaryController@ensureSummaries');
                Route::get('/candidate-summaries/{summary}', [CandidateSummaryController::class, 'get']);
                Route::post('/projects/{project}/template', 'ProjectController@template');
                Route::post('/candidates/{candidate}/retrySummary', 'CandidateSummaryController@retrySummary');
                Route::post('/stages/{stage}/syncSummaryMode', 'CandidateSummaryController@syncSummaryMode');
                Route::any('/projects/{project}/detailedStats', 'ProjectStatController@detailedStats');
                Route::get('/projects/{project}/raw', [ProjectController::class, 'raw']);
                Route::post('/projects/{project}/updateSourceBudget', 'ProjectStatController@updateSourceBudget');
                Route::post('projects/{project}/commitStageTransitions', 'ProjectController@commitStageTransitions');
                Route::get('projects/{project}/commentsGroupedByCandidate', [ProjectController::class, 'commentsGroupedByCandidate']);
                Route::post('/projects/{project}/generateAndPersistCriteria', 'ScreenerController@generateAndPersistCriteria')->name('projects.generateAndPersistCriteria');
                Route::post('/projects/{project}/screening-criteria', 'ScreenerController@addCriterion')->name('projects.addCriterion');
                Route::get('/projects/{project}/screening-responses', 'ScreenerController@getScreeningResponses')->name('projects.getScreeningResponses');
                Route::delete('/screening-criteria/{screeningCriterion}', 'ScreenerController@deleteCriterion')->name('screeningCriteria.delete');
                Route::put('/screening-criteria/{screeningCriterion}', 'ScreenerController@updateCriterion')->name('screeningCriteria.update');
                Route::post('/applications/{application}/runTempScreener', 'ScreenerController@runTempScreener')->name('applications.runTempScreener');
                Route::resource('projects.projectLogs', ProjectLogController::class)->shallow();
                Route::resource('clients', 'ClientController');
                Route::get('contacts/{contact}/edit', 'ClientController@editContact');
                Route::get('contacts/create', 'ClientController@createContact');
                Route::get('contacts/{contact}', 'ClientController@viewContact');
                Route::get('offices/{office}/edit', 'ClientController@editOffice')->name('offices.edit');
                Route::get('offices/create', 'ClientController@createOffice');
                Route::get('offices/{office}', 'ClientController@viewOffice');
                Route::get('activityTypes/all', 'ActivityTypeController@all');
                Route::get('activityTypes/report', 'ActivityTypeController@report');
                Route::get('activityTypes/report', 'ActivityTypeController@report');
                Route::resource('activityTypes', 'ActivityTypeController');
                Route::resource('dropoutReasons', 'DropoutReasonController');
                Route::post('dropoutReasons/{dropoutReason}/merge', [DropoutReasonController::class, 'merge'])->name('dropoutReasons.merge');
                Route::resource('requisitions', 'RequisitionController');
                Route::get('presentations', [PresentationController::class, 'index']);
                Route::post('/requisitions/{requisition}/createProject', 'RequisitionController@createProject')->name('requisitions.createProject');
                Route::put('/requisition-approvals/{approval}', 'RequisitionApprovalController@update');
                Route::get('/users/generateToken', 'UserController@generateToken')->name('user.generateToken');
                Route::post('/users/{user}/resendInvite', 'UserController@resendInvite')->name('user.resentInvite');
                Route::get('/users/{user}/isSmtpValid', 'UserController@isSmtpValid')->name('user.isSmtpValid');
                Route::post('/users/{user}/signature', 'UserController@signature')->name('user.signature');
                Route::get('/users/{user}/linkedItems', 'UserController@linkedItems')->name('user.linkedItems');
                Route::get('/users/{user}/history', 'UserController@history')->name('user.history');
                Route::post('/users/{user}/deactivate', 'UserController@deactivate')->name('user.deactivate');
                Route::get('/users/bulkImport', 'UserController@bulkImport')->name('users.bulkImport');
                Route::resource('users', 'UserController');
                Route::get('account', 'UserController@account')->name('user.me');
                Route::get('acceptTos', 'UserController@acceptTos');
                Route::get('acceptPrivacy', 'UserController@acceptPrivacy');
                Route::get('/me/getFutureTimeSlots', 'UserController@getFutureTimeSlots');
                Route::get('/me/getCalendarSchedules', 'UserController@getCalendarSchedules');
                Route::post('/me/setTimezone', 'UserController@setTimezone')->name('user.setTimezone');
                Route::post('actions/{action}/send', 'ActionController@sendAction');
                Route::get('/actions/{action}/getActionFormDataToLoad', 'ActionController@getActionFormDataToLoad');
                Route::resource('actions', 'ActionController');
                Route::get('/projectActions/{projectAction}/getActionFormDataToLoad', [ProjectActionController::class, 'getActionFormDataToLoad']);
                Route::resource('projectActions', ProjectActionController::class);
                Route::post('/stages/{stage}/addCandidatesToStage', 'StageController@addCandidatesToStage');
                Route::resource('stages', 'StageController');

                Route::group(['prefix' => 'messages'], function () {
                    Route::get('threads/candidate', [MessageController::class, 'candidateThreads']);
                    Route::get('thread/candidate/{candidate}', [MessageController::class, 'candidateThread']);
                    Route::post('thread/candidate/{candidate}/markAsRead', [MessageController::class, 'candidateThreadMarkAsRead']);
                    Route::get('threads/user', [MessageController::class, 'userThreads']);
                    Route::get('thread/user/{user}', [MessageController::class, 'userThread']);
                    Route::post('thread/user/{user}/markAsRead', [MessageController::class, 'userThreadMarkAsRead']);
                    Route::get('/', [MessageController::class, 'index'])->name('messages.index');
                    Route::fallback([MessageController::class, 'index']);
                });

                Route::get('/nps/getSurvey', 'NpsController@getSurvey');
                Route::put('/nps/{npsSurvey}', 'NpsController@update');

                Route::resource('structured-jobs', 'StructuredJobAdController');

                Route::get('structured-jobs/{structuredJobAd}/checkout', 'StructuredJobAdController@checkout');
                Route::get('structured-jobs/{structuredJobAd}/success', 'StructuredJobAdController@success');
                Route::get('structured-jobs/{structuredJobAd}/pay', 'StructuredJobAdController@createSession');
                Route::get('structured-jobs/{structuredJobAd}/edit-results', 'StructuredJobAdController@editResults'); // FIXME: dead code?
                Route::delete('structured-jobs/{structuredJobAd}/unpublish', [StructuredJobAdController::class, 'unpublish']);

                Route::get('structured-jobs/{structuredJobAd}/results', 'StructuredJobAdController@showSocialResults');

                Route::get('/stages/{stage}/get', [StageController::class, 'getStage']);
                Route::get('stages/{stage}/sync', 'StageController@sync');
                Route::post('stages/{stage}/uploadCandidate', 'CandidateController@uploadCandidate');
                Route::post('stages/{stage}/startSynchronousCvParse', 'CandidateController@startSynchronousCvParse');
                Route::post('stages/{stage}/updateVisibility', 'StageController@updateVisibility');
                Route::post('stages/{stage}/updateFairEvaluations', 'StageController@updateFairEvaluations');

                Route::get('/candidates/search', 'CandidateController@search');
                Route::any('/candidates/filterSearch', 'CandidateController@filterSearch')->name('candidates.filterSearch');
                Route::get('/candidates/nameSearch', 'CandidateController@nameSearch');
                Route::get('/candidates/loadFilterTerms', 'CandidateController@loadFilterTerms');
                Route::post('/candidates/anonymizeByIds', 'CandidateController@anonymizeByIds');
                Route::post('/candidates/anonymizeByFilters', 'CandidateController@anonymizeByFilters');
                Route::post('/candidates/createConsentRenewals', 'CandidateController@createConsentRenewals');
                Route::post('/candidates/{candidate}/anonymize', 'CandidateController@anonymize');
                Route::get('/candidates/getFilters', 'CandidateController@getFilters');
                Route::resource('candidates', 'CandidateController');
                Route::post('candidates/{id}/restore', 'CandidateController@restore');
                Route::post('/candidates/links/{possibleDuplicateCandidateLink}/markNotDuplicate', 'CandidateController@markNotDuplicate');
                Route::put('candidates/{candidate}/syncTags', 'CandidateController@syncTags');
                Route::post('candidates/{candidate}/uploadSingleFile', 'CandidateController@uploadSingleFile');
                Route::post('/candidates/addBulkTags', 'CandidateController@addBulkTags');
                Route::post('/candidates/createFilters', 'CandidateController@createFilters');
                Route::post('/candidates/{candidateFilter}/updateFilters', 'CandidateController@updateFilters');
                Route::delete('/candidates/{candidateFilter}/removeCandidateFilter', 'CandidateController@removeCandidateFilter');

                Route::delete('/files/{file}', 'FileController@destroy');
                Route::put('/files/{file}/changeType', 'FileController@changeType')->name('files.changeType');
                Route::put('/files/{file}/updateDate', 'FileController@updateDate')->name('files.updateDate');
                Route::put('/files/{file}/extractDetails', 'FileController@extractDetailsWithAI')->name('files.extractDetails');
                Route::put('/files/{file}/associateWithProject', 'FileController@associateWithProject');
                Route::put('/files/{file}/dissociateWithProject', 'FileController@dissociateWithProject');
                Route::put('/files/{file}/cloneToProject', 'FileController@cloneToProject');
                Route::post('/files/{file}/hideFromLibrary', 'FileController@hideFromLibrary');

                Route::get('/interviews/getAvailableRooms', 'InterviewController@getAvailableRooms');
                Route::post('/interviews/{timeSlot}/updateTimeSlotDetails', 'InterviewController@updateTimeSlotDetails');
                Route::get('/interviews/upcoming', 'InterviewController@upcoming');
                Route::delete('/interviews/{invite}/removeUserFromTimeSlot', 'InterviewController@removeUserFromTimeSlot');
                Route::any('/interviews/get', 'InterviewController@index');
                Route::post('/interviews/validateDeliveryChannels', [InterviewController::class, 'validateDeliveryChannels'])
                    ->name('interviews.validateDeliveryChannels');
                Route::resource('interviews', InterviewController::class);
                Route::post('/interviews', [InterviewController::class, 'index']);
                Route::resource('integrations', 'IntegrationController');
                Route::get('integrations/{integration}/test', 'IntegrationController@test')
                    ->name('integrations.test');
                Route::get('integrations/{integration}/debug', [IntegrationController::class, 'debug'])->name('integrations.debug');
                Route::get('integrations/{integration}/recreateCache', 'IntegrationController@recreateCache')->name('integrations.recreateCache');
                Route::get('integrations/{integration}/downloadEmail', 'IntegrationController@downloadEmail')->name('integrations.downloadEmail');
                Route::post('integrations/{integration}/generateNewFeedApiKey', 'IntegrationController@generateNewFeedApiKey')->name('integrations.generateNewFeedApiKey');
                Route::get('integrations/{integration}/runImports', 'IntegrationController@runImports')->name('integrations.runImports');
                Route::get('integrations/{integration}/ms', 'IntegrationController@ms');
                Route::get('integrations/api/userHasIntegrationOfType', [IntegrationController::class, 'userHasIntegrationOfType']);
                Route::get('candidates/{candidate}/full', 'CandidateController@full');
                Route::get('candidates/{candidate}/messages', 'CandidateController@messages');
                Route::patch('candidates/{candidate}/messages/{message}/cancelDelayedMessage', 'MessageController@cancelDelayedMessage');
                Route::get('candidates/{candidate}/comments', 'CandidateController@comments');
                Route::get('/candidates/{candidate}/tags', 'CandidateController@tags');
                Route::get('/candidates/{candidate}/sms', 'CandidateController@sms');
                Route::get('/candidates/{candidate}/interviews', 'CandidateController@interviews');
                Route::get('/candidates/{candidate}/meetingAnalyses', [MeetingAnalysisController::class, 'index']);
                Route::post('/candidates/{candidate}/meetingAnalyses/createFromVideoFile', [MeetingAnalysisController::class, 'createFromVideoFile']);
                Route::post('/meetingAnalyses/{meetingAnalysis}/retry', [MeetingAnalysisController::class, 'retry']);
                Route::post('/meetingAnalyses/{meetingAnalysis}/onlyFetchTeamsRecording', [MeetingAnalysisController::class, 'onlyFetchTeamsRecording']);
                Route::get('/meetingAnalyses/availableLanguages', [MeetingAnalysisController::class, 'availableLanguages']);
                Route::get('/meetingAnalyses/{meetingAnalysis}', [MeetingAnalysisController::class, 'show'])->whereNumber('meetingAnalysis');
                Route::delete('/meetingAnalyses/{meetingAnalysis}', [MeetingAnalysisController::class, 'destroy']);
                Route::delete('/meetingAnalyses/{meetingAnalysis}/video', [MeetingAnalysisController::class, 'destroyVideo']);
                Route::post('/meetingAnalyses/{meetingAnalysis}/startChat', [MeetingAnalysisController::class, 'startChat']);
                Route::post('/aiChats/{aiChatThread}/continue', [MeetingAnalysisController::class, 'continueChat']);
                Route::delete('/aiChats/{aiChatThread}', [MeetingAnalysisController::class, 'deleteChat']);
                Route::get('/candidates/{candidate}/references', 'CandidateController@references');
                Route::post('/candidates/{candidate}/references/generateSubmissionUrl', 'CandidateController@generateReferenceSubmissionUrl');
                Route::post('/candidates/{candidate}/sendSms', 'CandidateController@sendSms');
                Route::get('/candidates/{candidate}/history', 'CandidateController@history');
                Route::put('/candidates/{candidate}/updatePlace', 'CandidateController@updatePlace');
                Route::post('/candidates/import/xlsx', 'CandidateController@importFromXlsx');
                Route::post('/candidates/mergeTwoCandidates', 'CandidateController@mergeTwoCandidates');

                Route::get('/candidates/{candidate}/getHrisData', [\App\Http\Controllers\Tenant\HrisController::class, 'getHrisData']);
                Route::post('/candidates/{candidate}/sendToHris/{integration}', [\App\Http\Controllers\Tenant\HrisController::class, 'sendToHris']);
                Route::post('/candidates/{candidate}/sendToHrisAts', [\App\Http\Controllers\Tenant\HrisController::class, 'sendToHrisAts']);
                Route::delete('/candidates/{candidate}/removeLink/{integration}', [\App\Http\Controllers\Tenant\HrisController::class, 'removeLink']);

                Route::post('/candidates/snoozeAutoConsentRenewals', 'CandidateController@snoozeAutoConsentRenewals');
                Route::post('/candidates/snoozeAutoAnonymize', 'CandidateController@snoozeAutoAnonymize');
                Route::post('/candidates/runAutoConsentRenewalsNow', 'CandidateController@runAutoConsentRenewalsNow');
                Route::post('/candidates/runAutoAnonymizeNow', 'CandidateController@runAutoAnonymizeNow');

                Route::resource('/video-interviews', VideoInterviewController::class);
                Route::get('/video-interviews/{videoInterview}/duplicate', [VideoInterviewController::class, 'duplicate'])
                    ->name('video-interviews.duplicate');
                Route::post('/video-interviews/{videoInterview}/archive', [VideoInterviewController::class, 'archive'])
                    ->name('video-interviews.archive');
                Route::post('/video-interviews/{videoInterview}/restore', [VideoInterviewController::class, 'restore'])
                    ->name('video-interviews.restore');

                Route::get('/video-call/{call}/redirect', [VideoInterviewController::class, 'redirectToCallUrl']);
                Route::get('/videos/{video}', [VideoInterviewController::class, 'getVideo']);

                Route::get('forms', 'FormBuilderController@index')->name('forms.index');
                Route::get('forms/candidateFormsList', 'FormController@candidateFormsList')->name('forms.candidateFormsList');
                Route::any('laraform/unique', 'FormController@unique');
                Route::get('forms/{form}/edit', 'FormBuilderController@edit')->name('forms.edit');
                Route::get('forms/{form}/submissions', 'FormBuilderController@submissions')->name('forms.submissions');
                Route::get('forms/{form}/clone', 'FormBuilderController@cloneForm')->name('forms.clone');
                Route::post('forms/{form}/archive', 'FormBuilderController@archiveForm')->name('forms.archive');
                Route::post('forms/{form}/restore', 'FormBuilderController@restoreForm')->name('forms.restore');
                Route::get('forms/{form}/download', [FormBuilderController::class, 'downloadFormSubmissions'])->name('forms.download');
                Route::get('forms/editByUrl', 'FormBuilderController@editByUrl')->name('forms.editByUrl');
                Route::get('forms/getStages', 'FormBuilderController@getStages')->name('forms.getStages');
                Route::post(
                    'forms/createFromDefault',
                    'FormBuilderController@createFromDefault'
                )->name('forms.createFromDefault');
                Route::post('forms/updateFont', 'FormBuilderController@updateFont')->name('forms.updateFont');
                Route::get(
                    'forms/getDefaultFormData',
                    'FormBuilderController@getDefaultFormData'
                )->name('forms.getDefaultFormData');
                Route::get('forms/getProjects', 'FormBuilderController@getProjects');
                Route::get('forms/getStagesForProject/{project}', 'FormBuilderController@getStagesForProject');
                Route::post('forms/{form}', 'FormBuilderController@update')->name('forms.update');
                Route::get('forms/create', 'FormBuilderController@create')->name('forms.create');
                Route::post('forms', 'FormBuilderController@store')->name('forms.store');
                Route::get('forms/{form}/submissions/{submission}/', [FormBuilderController::class, 'showSingleSubmission'])->name('forms.submission');
                Route::get('forms/{form}/submissions/{submission}/pdf', [FormBuilderController::class, 'downloadSubmissionPdf'])->name('forms.submission.pdf');

                Route::get('form/{name}', 'FormController@get');
                Route::get('form/{key}/items/{field}', 'FormController@items');
                Route::post('form/{key}/aiFill/{field}', 'FormController@aiFill');
                Route::post('comments/handleAttachment', 'CommentController@handleAttachment');
                Route::post('/textGeneration/generate', [\App\Http\Controllers\Tenant\TextGenerationController::class, 'generate']);
                Route::delete('comments/{comment}', 'CommentController@delete');
                Route::post('comments/{comment}/makePublic', 'CommentController@makePublic');
                Route::post('comments/{comment}/makePrivate', 'CommentController@makePrivate');
                Route::post('comments/{comment}/addReact', 'CommentController@addReact');
                Route::post('comments', 'CommentController@store');
                Route::post('/comments/bulk', 'CommentController@addBulk');
                Route::put('comments/{comment}', 'CommentController@update');

                Route::get('organization/settings', 'OrganizationController@index')->name('organization.settings');
                Route::post('organization/settings', 'OrganizationController@update');
                Route::post('organization/migrateBrand', 'OrganizationController@migrateBrand');
                Route::post('organization/addDiversityFieldsAndForms', 'OrganizationController@addDiversityFieldsAndForms')->name('organization.addDiversityFieldsAndForms');

                Route::post('activity/receipt', 'ActivityController@receipt');

                Route::post(
                    'presentation/{presentation}/send',
                    'Pub\PresentationController@send'
                )->name('presentation.send');

                Route::get('landing/getRecentImages', 'LandingController@getRecentImages');
                Route::get('landing/getRecentVideos', 'LandingController@getRecentVideos');
                Route::get('landing/{landing}/clone', 'LandingController@cloneLanding')->name('landings.clone');
                Route::get('landing/{landing}/generate', 'LandingController@generate')->name('landings.generate');
                Route::get('landing/{landing}/publish', 'LandingController@publish')->name('landings.publish');
                Route::get('landing/{landing}/getStats', 'LandingController@getStats')->name('landings.getStats');
                Route::get('landing/{landing}/getSummaryStats', 'LandingController@getSummaryStats')->name('landings.getSummaryStats');
                Route::get('landing/{landing}/archive', 'LandingController@archive')->name('landings.archive');
                Route::get('b-landing/create', 'LandingController@createImage')->name('landings.createImage');
                Route::get('landings/getStatsForLandings', 'LandingController@getStatsForLandings')->name('landings.stats');
                Route::get('landings/getForms', 'LandingController@getForms');
                Route::get('landings/v2/create', 'LandingController@createV2')->name('landings.v2.create');
                Route::get('landings/v2/{landing}/edit', 'LandingController@editV2');
                Route::get('landings/v2/feeds/{slug}', 'LandingController@loadFeedItems');
                Route::resource('landings', 'LandingController');
                Route::get('landings/{landing}/downloadAsImage', 'LandingController@downloadAsImage');
                Route::get('landings/{landing}/getPreviewUrl', 'LandingController@getPreviewURL');
                Route::get('landings/{landing}/simple', 'LandingController@simple')->name('landings.simple');
                Route::get('landings/{landing}/socialMediaPreview', 'LandingController@socialMediaPreview')->name('landings.socialMediaPreview');
                Route::get('landings/{landing}/delete', 'LandingController@destroy')->name('landings.delete');
                Route::put('landings/{landing}/syncTags', 'LandingController@syncTags');
                Route::post('landings/{landing}/dei/check-block', 'LandingDEIController@checkBlock');
                Route::get('landings/{landing}/dei/block', 'LandingDEIController@getBlockSummary');
                Route::get('landing/{landing}/pin', 'LandingController@pin')->name('landings.pin');
                Route::get('landing/{landing}/unpin', 'LandingController@unpin')->name('landings.unpin');
                Route::get('landings/{landing}/history', 'LandingController@history')->name('landings.history');

                Route::post('tasks', 'TasksController@createMany');
                Route::put('tasks/{task}/setDone', 'TasksController@setDone')->name('tasks.setDone');
                Route::get('/tasks/{task}/redirectToCandidateWithTask', 'TasksController@redirectToCandidateWithTask');

                Route::get('templates', 'TemplateController@get');
                Route::resource('/settings/templates', 'TemplateController');

                Route::get('settings/scim', [ScimSettingsController::class, 'index'])->name('settings.scim.index');
                Route::patch('settings/scim', [ScimSettingsController::class, 'update'])->name('settings.scim.update');
                Route::post('settings/scim/syncAll', [ScimSettingsController::class, 'syncAll'])->name('settings.scim.syncAll');
                Route::post('settings/scim/disableAutoSync', [ScimSettingsController::class, 'disableAutoSync'])->name('settings.scim.disableAutoSync');
                Route::post('settings/scim/enableAutoSync', [ScimSettingsController::class, 'enableAutoSync'])->name('settings.scim.enableAutoSync');

                Route::get('/settings/tags/candidates/{tag}/edit', 'TagController@edit');
                Route::delete('/settings/tags/candidates/{tag}', 'TagController@destroy');
                Route::resource('/settings/tags/candidates', 'TagController', [
                    'as' => 'settings.tags',
                ]);
                Route::post('/settings/tags/candidates/merge', 'TagController@merge');

                Route::get('/settings/tags/landings/{landingTag}/edit', 'LandingTagController@edit');
                Route::delete('/settings/tags/landings/{landingTag}', 'LandingTagController@destroy');
                Route::resource('/settings/tags/landings', 'LandingTagController', [
                    'as' => 'settings.tags',
                ]);
                Route::post('/settings/tags/landings/merge', 'LandingTagController@merge');

                Route::resource('/settings/file-types', FileTypeController::class, [
                    'as' => 'settings',
                ]);

                Route::resource('/settings/project-roles', ProjectRoleController::class, [
                    'as' => 'settings',
                ]);

                Route::get('/project-roles/all', [ProjectRoleController::class, 'getAll']);
                Route::put('/project-roles/sync', [ProjectRoleController::class, 'sync']);
                Route::post('/project-roles/quick', [ProjectRoleController::class, 'addNew']);
                Route::post('/project-roles/{projectRole}/updateColor', [ProjectRoleController::class, 'updateColor']);

                Route::post('/settings/consent-subtypes/updateGeneral', [ConsentSubtypeController::class, 'updateGeneral']);
                Route::resource('/settings/consent-subtypes', ConsentSubtypeController::class);

                Route::get('/settings/project-failure-reasons', [ProjectFailureReasonController::class, 'index'])->name('projectFailureReasons.index');
                Route::delete('/settings/project-failure-reasons/{projectFailureReason}', [ProjectFailureReasonController::class, 'destroy'])->name('projectFailureReasons.destroy');
                Route::put('/settings/project-failure-reasons/require-project-failure-reasons', [ProjectFailureReasonController::class, 'updateRequireProjectFailureReasons'])->name('projectFailureReasons.updateRequireProjectFailureReasons');

                Route::get('/tags/all', 'TagController@all');
                Route::put('/tags/{tag}/updateColor', 'TagController@updateColor');
                Route::post('/tags/quick', 'TagController@addNew');

                Route::get('/landing-tags/all', 'LandingTagController@all');
                Route::put('/landing-tags/{landingTag}/updateColor', 'LandingTagController@updateColor');
                Route::post('/landing-tags/quick', 'LandingTagController@addNew');

                Route::get('subscription/portal', 'SubscriptionController@toPortal');
                Route::post('subscription', 'SubscriptionController@createSubscription');
                Route::post('settings/billing/update', 'SubscriptionController@updateSubscription');
                Route::get('settings/billing', 'SubscriptionController@index');
                Route::get('settings/billing/getSetupIntent', 'SubscriptionController@getSetupIntent');
                Route::get('settings/billing/setupIntentCallback', 'SubscriptionController@setupIntentCallback');
                Route::post('settings/billing/saveSubscriptionStateSession', 'SubscriptionController@saveSubscriptionStateSession');
                Route::post('settings/billing/saveBillingInfo', 'SubscriptionController@saveBillingInfo');
                Route::post('settings/billing/preview', 'SubscriptionController@preview');
                Route::get('billing-issue', 'SubscriptionController@billingIssue');

                Route::post('/customStageCategory/process', [CustomStageCategoryController::class, 'process']);
                Route::get('/customStageCategories', [CustomStageCategoryController::class, 'index']);
                Route::post('/customStageCategory/clearAllStageCategories', [CustomStageCategoryController::class, 'clearAllStageCategories'])->name('customStageCategory.clearAllStageCategories');
                Route::get('/customStageCategory/edit', [CustomStageCategoryController::class, 'edit'])
                    ->name('customStageCategory.edit');

                Route::get('audits', [AuditController::class, 'index'])->name('audits.index');
                Route::get('/candidates/{candidate}/audits', [AuditController::class, 'candidateAudits'])->name('audits.candidates');
                Route::post('/audits/recordCustomAuditEvent', [AuditController::class, 'recordCustomAuditEvent'])->name('audits.recordCustomAuditEvent');

                Route::resource('authconfigs', AuthConfigController::class);

                Route::resource('/settings/api/keys', ApiKeyController::class, [
                    'as' => 'settings.api',
                ]);

                Route::get('teams/editor', 'TeamsController@editor')->name('teams.editor');
                Route::post('teams/save', 'TeamsController@save');
                Route::get('teams/accessibleTeams', 'TeamsController@accessibleTeams')->name('teams.accessibleTeams');
                Route::resource('teams', TeamsController::class);
                Route::get('teams/{team}/settings', 'TeamsController@settings')->name('teams.settings');
                Route::post('teams/{team}/settings', 'TeamsController@updateSettings')->name('teams.update-settings');

                Route::get('scorecards/getProjectCandidateData', 'ScorecardController@getProjectCandidateData');
                Route::get('scorecards/{scorecard}/clone', 'ScorecardController@clone')->name('scorecards.clone');
                Route::get('/projects/{project}/candidates/{candidate}/getCandidateScorecardResponses', 'ScorecardController@getCandidateScorecardResponses');
                Route::post('/scorecards/upsertScorecardResponseSet', 'ScorecardController@upsertScorecardResponseSet');
                Route::post('/projects/{project}/syncProjectScoreCards', 'ScorecardController@syncProjectScoreCards');
                Route::resource('scorecards', 'ScorecardController');

                Route::middleware('feature-control:referenceForms')->group(function () {
                    Route::get('settings/references/{form}/edit', 'ReferenceFormController@edit')->name('references.edit');
                    Route::get('settings/references/{form}/submissions', 'ReferenceFormController@submissions')->name('references.submissions');
                    Route::post('settings/references/{form}', 'ReferenceFormController@update')->name('references.update');
                    Route::resource('settings/references', 'ReferenceFormController');
                    Route::get('/settings/references/{form}/download', [ReferenceFormController::class, 'downloadFormSubmissions'])->name('references.download');
                });

                Route::resource('settings/locations', LocationController::class);

                Route::resource('mailIdentities', MailIdentityController::class);
                Route::put('mailIdentities/{mailIdentity}/makeDefault', [MailIdentityController::class, 'makeDefault']);

                Route::get('/printer/jpg', 'PrinterController@jpg');
                Route::get('/printer/png', 'PrinterController@png');
                Route::get('/printer/pdf', 'PrinterController@pdf');

                Route::group(['prefix' => 'two-factor'], function () {
                    Route::get('set-up', 'Auth\TwoFactorController@showSetup');
                    Route::post('set-up', 'Auth\TwoFactorController@storeSetup');
                    Route::get('set-up/verify', 'Auth\TwoFactorController@showVerifySetup');
                    Route::post('set-up/verify', 'Auth\TwoFactorController@verifySetup');

                    Route::get('challenge', 'Auth\TwoFactorController@challenge');
                    Route::post('challenge', 'Auth\TwoFactorController@sendChallenge');
                    Route::get('challenge/verify', 'Auth\TwoFactorController@showVerifyChallenge');
                    Route::post('challenge/verify', 'Auth\TwoFactorController@verifyChallenge');
                });

                Route::post('/fields/update', 'SingleFieldController@update');

                Route::group(['prefix' => 'api/v2', 'as' => 'api.v2'], function () {
                    Route::get('/sourcing/auth', 'ApiV2\SourcingExtensionController@auth');
                });

                Route::any('/oauth2/generic/login', 'Auth\GenericOAuth2Controller@login');
                Route::any('/oauth2/generic/userCallback', 'Auth\GenericOAuth2Controller@userCallback');

                Broadcast::routes();
            });

            Route::group(['prefix' => 'api/v2', 'as' => 'api.v2'], function () {
                Route::get('/sourcing/findExistingCandidate', 'ApiV2\SourcingExtensionController@findExistingCandidate');
                Route::get('/sourcing/getProjects', 'ApiV2\SourcingExtensionController@getProjects');
                Route::any('/sourcing/stages/{stage}/addToStage', 'ApiV2\SourcingExtensionController@addToStage');
                Route::post('/sourcing/updateExistingCandidate', 'ApiV2\SourcingExtensionController@updateExistingCandidate');
            });

            Route::post('landing/uploadImage', 'LandingController@uploadImage');
            Route::post('landing/uploadVideo', 'LandingController@uploadVideo');
            Route::post('landing/crop', 'LandingController@crop');

            Route::get('/hls_concat/master.m3u8', 'HLSController@master');
            Route::get('/hls_concat/audio.m3u8', 'HLSController@audio');
            Route::get('/hls_concat/video.m3u8', 'HLSController@video');
            Route::get('/hls_concat/infos', 'HLSController@infos');

            Route::get('/settings/appearance', [\App\Http\Controllers\Tenant\Settings\AppearanceController::class, 'index'])
                ->name('settings.appearance.index');
            Route::get('/custom-fonts', [\App\Http\Controllers\Tenant\CustomFontController::class, 'index']);

            Route::name('videos.initialize')->post(
                '/videos/initialize',
                [VideoUploadController::class, 'initializeVideo']
            );
            Route::name('videos.upload')->post(
                '/videos/upload/{video:token}',
                [VideoUploadController::class, 'uploadVideo']
            );

            Route::group(['prefix' => 'public'], function () {
                Route::get('presentation/{token}', 'Pub\PresentationController@show');
                Route::get('invites/{token}', 'Pub\InviteController@show')->name('invite.show');
                Route::get('invites/{token}/busy', 'Pub\InviteController@getBusySlots')->name('invite.busy');
                Route::get('invites/{token}/call', 'Pub\InviteController@call')->name('invite.call');
                Route::get('calls/{token}', 'Pub\InviteController@quickCall')->name('call');
                Route::post('invites/{token}', 'Pub\InviteController@confirm')->name('invite.confirm');

                Route::get('timeslot/{payload}/decline', 'Pub\TimeslotController@decline')->name('timeslot.decline');
                Route::post('timeslot/{payload}/decline', 'Pub\TimeslotController@confirmDecline')->name('timeslot.confirmDecline');

                Route::post('forms/file/upload', 'Pub\FormController@uploadFile');
                Route::post('forms/file/process-ai', 'Pub\FormController@processUploadedFileUsingAI');
                Route::get('forms/{token}', 'Pub\FormController@show')->name('form.iframe');
                Route::post('forms/{token}/process', 'Pub\FormController@process');
                Route::get('landing/{landing}-{slug}', 'Pub\LandingController@showFromInstance')->name('landing.permalink');

                Route::post('/video/ov/createOvSession', [OpenViduController::class, 'createOvSession']);
                Route::post('/video/ov/startOvRecord', [OpenViduController::class, 'startOvRecord']);
                Route::post('/video/ov/stopOvRecord', [OpenViduController::class, 'stopOvRecord']);
                Route::get('/video/ov/getRecordingInfo/{recordingId}', [OpenViduController::class, 'getRecordingInfo']);
                Route::get('/video/ov/{recordingId}/getVideoFile', [OpenViduController::class, 'getVideoFile']);
                Route::post('/video/ov/{recordingId}/confirm', [OpenViduController::class, 'confirmOvResponse']);

                Route::name('asyncVideoInterviews.initializeResponse')->post(
                    '/async-video-interviews/{invite:token}/questions/{question}/responses',
                    [AsyncVideoInterviewController::class, 'initializeResponse']
                );
                Route::name('asyncVideoInterviews.uploadChunk')->post(
                    '/async-video-interviews/{invite:token}/responses/{response}/upload-chunk',
                    [AsyncVideoInterviewController::class, 'uploadChunk']
                );
                Route::name('asyncVideoInterviews.mergeChunks')->post(
                    '/async-video-interviews/{invite:token}/responses/{response}/merge-chunks',
                    [AsyncVideoInterviewController::class, 'mergeChunks']
                );
                Route::name('asyncVideoInterviews.confirmResponse')->post(
                    '/async-video-interviews/{invite:token}/responses/{response}/confirm',
                    [AsyncVideoInterviewController::class, 'confirmResponse']
                );
                Route::get('/video/{invite:token}', [AsyncVideoInterviewController::class, 'show']);

                Route::get(
                    '/video-message/{encryptedVideoId}',
                    'Pub\VideoMessageController@show'
                )->name('video-message.show');
                Route::get(
                    '/video-message/{encryptedVideoId}/mp4',
                    'Pub\VideoMessageController@getMp4Url'
                )->name('video-message.mp4');

                Route::get('/consents/{token}', 'Pub\ConsentRenewalController@show')->name('consent.show');
                Route::post('/consents/{token}', 'Pub\ConsentRenewalController@update')->name('consent.update');

                Route::name('survey.edit')->get(
                    '/survey/{survey:token}',
                    [SurveyController::class, 'edit']
                );

                Route::get('/references/{token}', 'Pub\ReferenceEntryController@show')->name('reference.show');
                Route::post('/references/{token}', 'Pub\ReferenceEntryController@process')->name('reference.process');

                Route::middleware(['throttle:locationSearch'])->group(function () {
                    Route::get('/location/search', [LocationSearchController::class, 'index']);
                });
            });

            Route::group(['prefix' => '/api/docs', 'as' => 'api.docs'], function () {
                Route::get('/', 'Api\DocsController@swagger');
                Route::get('/yaml', 'Api\DocsController@yaml')->name('yaml');
            });

            Route::group(['prefix' => 'i/{instance}'], function () {
                Route::get(
                    '/jobs/{anyLanding}/{slug?}',
                    [\App\Http\Controllers\Tenant\Pub\LandingController::class, 'showFromWebsite']
                )->name('landing.permalink_web');

                Route::get(
                    '/job/{urlToken}/{slug?}',
                    [\App\Http\Controllers\Tenant\Pub\LandingController::class, 'showWithUrlTokenFromWebsite']
                );
                Route::post(
                    '/jobs/{anyLanding}/{slug}/cvlApply',
                    [\App\Http\Controllers\Tenant\Pub\LandingController::class, 'cvlApply']
                );
                Route::get(
                    '/jobs/{anyLanding}/{slug}/cvlProxy',
                    [\App\Http\Controllers\Tenant\Pub\LandingController::class, 'cvlProxy']
                );
            });

            Route::group(['prefix' => 'p'], function () {
                Route::get('/job-builder', 'Pub\JobAdBuilderController@index')->name('job-ad-builder');
                Route::post('/job-builder/remindMe', 'Pub\JobAdBuilderController@remindMe');
                Route::post('/job-builder', 'Pub\JobAdBuilderController@store');
                Route::put('/job-builder', 'Pub\JobAdBuilderController@update');

                Route::put('/job-builder/{token}', 'Pub\JobAdBuilderController@update');
                Route::get('/job-builder/{token}/edit', 'Pub\JobAdBuilderController@edit')->name('job-ad-builder.edit');

                Route::get('/job-builder/{token}/{slug?}', 'Pub\JobAdBuilderController@show')->name('job-ad-builder.show');

                Route::post('/job/builder/{token}/sendJpg', 'Pub\JobAdBuilderController@sendJpg');
                Route::post('/job/builder/{token}/generateJpg', 'Pub\JobAdBuilderController@generateJpg');
                Route::post('/job/builder/{token}/generateFile', 'Pub\JobAdBuilderController@generateFile');

                Route::get('landing-builder', 'Pub\LandingBuilderController@create');

                Route::get('/social/start', 'Pub\SocialSignUpController@show');
                Route::post('/social/start', 'Pub\SocialSignUpController@store');
                Route::get('social/start/{adKey}/success', 'Pub\SocialSignUpController@success');

                Route::get(
                    '/jobs/{anyLanding}/{slug?}',
                    [\App\Http\Controllers\Tenant\Pub\LandingController::class, 'showFromInstance']
                );
                Route::get(
                    '/job/{urlToken}/{slug?}',
                    [\App\Http\Controllers\Tenant\Pub\LandingController::class, 'showWithUrlTokenFromInstance']
                );
            });

            Route::post('/twilio/inbound', 'Pub\TwilioController@inbound');
            Route::post('/messente/inbound', 'Pub\MessenteController@inbound');

            Route::post('/google/calendar/callback', [GoogleCalendarController::class, 'receive'])->name('google.calendar.callback');

            Route::withoutMiddleware(VerifyCsrfToken::class)
                ->controller(MicrosoftWebhookController::class)
                ->group(function () {
                    Route::post('/microsoft/webhook/recording', 'processCreatedRecording')->name('webhook.microsoft.recording');
                    Route::post('/microsoft/webhook/lifecycle', 'processLifecycleNotifications')->name('webhook.microsoft.lifecycle');
                });

            Route::get('/debug-sentry', function () {
                throw new Exception('My first Sentry error!');
            });

            Route::get('/google{token}.html', [GoogleSearchConsoleVerificationController::class, 'verify']);
        });

        Route::group(['middleware' => 'auth:api', 'prefix' => 'api'], function () {
            Route::post('/applications/submit', 'ApplicationController@submit');
        });

        Route::group([
            'middleware' => [
                ApiKeyMiddleware::class,
                SubstituteBindings::class,
            ],
            'prefix' => 'api/v1',
            'as' => 'api.v1.',
        ], function () {
            Route::get('/jobAdFeeds/{slug}', [
                'as' => 'feeds.show',
                'uses' => 'Api\JobAdFeedController@show',
                'middleware' => ['return-json'],
            ]);

            Route::get('/jobAdFeeds/{slug}/jobAds/{ad}', [
                'as' => 'feeds.showSingle',
                'uses' => 'Api\JobAdFeedController@showSinglePassive',
                'middleware' => ['return-json'],
            ]);

            Route::get('/jobAdFeeds/{slug}/dispositionData', [
                'uses' => 'Api\JobAdFeedController@dispositionData',
                'middleware' => ['return-json'],
            ]);

            Route::get('/candidates/findByEmail', [\App\Http\Controllers\Tenant\Api\CandidateController::class, 'findByEmail']);
            Route::get('/candidates/hires', [\App\Http\Controllers\Tenant\Api\CandidateController::class, 'hires']);
            Route::get('/candidates/{candidate}', [\App\Http\Controllers\Tenant\Api\CandidateController::class, 'show']);
            Route::patch('/candidates/{candidate}', [\App\Http\Controllers\Tenant\Api\CandidateController::class, 'update']);
            Route::post('/candidates/{candidate}/comments', [\App\Http\Controllers\Tenant\Api\CandidateController::class, 'addComment']);
            Route::post('/candidates/{candidate}/moveToStage', [\App\Http\Controllers\Tenant\Api\CandidateController::class, 'moveToStage']);

            Route::get('/projects/{project}', [\App\Http\Controllers\Tenant\Api\ProjectController::class, 'show']);
            Route::post('/projects', [\App\Http\Controllers\Tenant\Api\ProjectController::class, 'store']);
            Route::get('/project-templates', [\App\Http\Controllers\Tenant\Api\ProjectController::class, 'indexTemplates']);

            Route::get('/activityTypes', [\App\Http\Controllers\Tenant\Api\ActivityController::class, 'activityTypes']);
            Route::post('/activities', [\App\Http\Controllers\Tenant\Api\ActivityController::class, 'store']);

            Route::get('/jobAdFeeds/{slug}/cvKeskus', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showCvk');
            Route::get('/jobAdFeeds/{slug}/cvKeskusLanding', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showCvkLanding');
            Route::get('/jobAdFeeds/{slug}/linkedin', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showLinkedin');
            Route::get('/jobAdFeeds/{slug}/vp2', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showVp2');
            Route::get('/jobAdFeeds/{slug}/cvbankas', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showCvbankas');
            Route::get('/jobAdFeeds/{slug}/sslv', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showSsLv');
            Route::get('/jobAdFeeds/{slug}/professionhu', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showProfessionHu');
            Route::get('/jobAdFeeds/{slug}/talenme', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showTalenme');
            Route::get('/jobAdFeeds/{slug}/duunitori', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showDuunitori');

            Route::get('/jobAdFeeds/{slug}/indeed', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showIndeed');
            Route::get('/jobAdFeeds/{slug}/indeed/{ad}/screening-questions', 'Api\JobAdFeedController@screeningQuestions')
                ->name('feeds.showIndeed.screeningQuestions');
            Route::post('/apply', 'Api\ApplyController@apply');
            Route::post('/indeed-apply', 'Api\IndeedApplyController@apply')->name('apply.indeed');
            Route::post('/talentcom-apply', 'Api\TalentComApplyController@apply')->name('apply.talent');

            Route::get('/jobAdFeeds/{slug}/talentcom', 'Api\JobAdFeedController@showPassive')
                ->name('feeds.showTalentcom');
            Route::get('/jobAdFeeds/{slug}/talentcom/{ad}/screening-questions', 'Api\JobAdFeedController@screeningQuestions')
                ->name('feeds.showTalent.screeningQuestions');

            Route::get('/db/dump', [\App\Http\Controllers\Tenant\Api\DbDumpController::class, 'dump']);
            Route::get('/reports/projectStatusReport', [\App\Http\Controllers\Tenant\Api\ReportController::class, 'projectStatusReport']);
            Route::get('/reports/landingViewsReport', [\App\Http\Controllers\Tenant\Api\ReportController::class, 'landingViewsReport']);
            Route::get('/reports/landingSubmissionsReport', [\App\Http\Controllers\Tenant\Api\ReportController::class, 'landingSubmissionsReport']);

            Route::get('/settings/projectCustomFields', [\App\Http\Controllers\Tenant\Api\SettingsController::class, 'projectCustomFields']);
            Route::get('/settings/candidateCustomFields', [\App\Http\Controllers\Tenant\Api\SettingsController::class, 'candidateCustomFields']);
            Route::get('/settings/tags', [\App\Http\Controllers\Tenant\Api\SettingsController::class, 'tags']);

            Route::get('/auditLogs', [\App\Http\Controllers\Tenant\Api\AuditLogsController::class, 'index']);
        });

        Route::group([
            'middleware' => [
                SubstituteBindings::class,
                'api_key:' . \App\Models\ApiKey::PERMISSION_CANDIDATES_WRITE . ',password',
            ],
            'prefix' => 'api/v1/professionhu',
        ], function () {
            Route::post('/{integration}/authorize', [ProfessionHuController::class, 'authorizeProfession'])->name('professionhu.authorize');
        });

        Route::group([
            'middleware' => [
                SubstituteBindings::class,
                ProfessionHuMiddleware::class,
            ],
            'prefix' => 'api/v1/professionhu',
        ], function () {
            Route::get('/{integration}/check', [ProfessionHuController::class, 'check'])->name('professionhu.check');
            Route::post('/{integration}/apply', [ProfessionHuController::class, 'apply'])->name('professionhu.apply');
        });

        Route::group([
            'middleware' => ['api_key:' . \App\Models\ApiKey::PERMISSION_MANAGE_USERS],
        ], function () {
            \ArieTimmerman\Laravel\SCIMServer\RouteProvider::routes();
        });

    });
