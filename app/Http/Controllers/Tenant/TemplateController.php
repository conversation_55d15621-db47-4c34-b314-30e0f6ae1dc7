<?php


namespace App\Http\Controllers\Tenant;

use App\Classes\FeatureControls\FeatureControls;
use App\Forms\TemplateForm;
use App\Http\Controllers\Controller;
use App\Models\Template;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class TemplateController extends Controller
{
    public function get(Request $request)
    {
        $this->authorize('viewAny', Template::class);
        $query = Template::query();
        if ($type = $request->get('type')) {
            $query->where('template_type', $type);
        }
        $query->where(function (Builder $q) {
            $q->where('user_id', auth()->id())->orWhereNull('user_id');
        });
        return $query->orderBy('name')->get();
    }

    public function index()
    {
        $this->authorize('viewAny', Template::class);
        $templateType = request()->get('type');
        return view('templates.index', [
            'templates' => Template::query()
                ->when($templateType, function (Builder $q) use ($templateType) {
                    $q->where('template_type', $templateType);
                })
                ->when(!FeatureControls::get()->templates->update, function (Builder $q) {
                    $q->where('user_id', auth()->id());
                })
                ->with('user')
                ->orderByDesc('id')
                ->get(),
            'pageTitle' => __('Message templates'),
        ]);
    }

    public function create()
    {
        $this->authorize('create', Template::class);
        $form = app(TemplateForm::class);
        \JavaScript::put('form', app(TemplateForm::class));
        return view('form_settings', [
            'form' => $form,
            'pageTitle' => __('Create template'),
        ]);
    }

    public function edit(Template $template)
    {
        $this->authorize('update', $template);
        $template->append([
            'message_body',
            'event_set_body',
            'invite_body',
            'video_interview_body',
            'video_message_body',
            'consent_message_body',
            'reference_body',
            'sms_body',
            'user_message_body',
        ]);
        $form = app(TemplateForm::class);
        \JavaScript::put('form', $form);
        \JavaScript::put('model', $template);
        return view('form_settings', [
            'form' => $form,
            'pageTitle' => __('Edit template'),
        ]);
    }

    public function destroy(Template $template)
    {
        $this->authorize('delete', $template);
        $template->delete();
        return redirect()->back();
    }

    public function show(Template $template)
    {
        $this->authorize('view', $template);
        return redirect()->route('templates.index');
    }
}
