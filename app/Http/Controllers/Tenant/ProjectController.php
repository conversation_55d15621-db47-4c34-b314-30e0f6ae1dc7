<?php

namespace App\Http\Controllers\Tenant;

use App\Classes\CandidateSearch\Util;
use App\Classes\ProjectSankey\ProjectSankey;
use App\Classes\ProjectSearch\Search;
use App\Classes\Screening\Screener;
use App\Classes\Statistics\Funnel\FunnelGrouperInterface;
use App\Classes\Statistics\Funnel\ModelFieldGrouper;
use App\Classes\Statistics\Funnel\ProjectFunnelService;
use App\Classes\Statistics\Funnel\SourceGrouper;
use App\Classes\Statistics\Indicators\CNps;
use App\Classes\Statistics\Indicators\TimeToFill;
use App\Classes\Statistics\Indicators\TimeToHire;
use App\Classes\Statistics\SourceNormalizer;
use App\Events\ProjectBoardUpdated;
use App\Events\ProjectScreeningUpdated;
use App\Exports\MonthlyHiringExport;
use App\Exports\ProjectCandidateExport;
use App\Exports\ProjectStatExport;
use App\Exports\ProjectStatusReport;
use App\Forms\ProjectFormGrouped;
use App\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\Views\ProjectIndexListResource;
use App\Jobs\Exports\ProjectTotalReportExportJob;
use App\Models\Action;
use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Application;
use App\Models\Candidate;
use App\Models\Comment;
use App\Models\CrmOrganization;
use App\Models\File;
use App\Models\Form;
use App\Models\Import;
use App\Models\Landing;
use App\Models\Location;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Stage;
use App\Models\StructuredJobAd;
use App\Models\Tag;
use App\Models\User;
use App\Scopes\CandidateAccessScope;
use App\Scopes\SubmissionAccessScope;
use App\Services\LandingStats\LandingStats;
use Carbon\Carbon;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Laraform\Process\AutoProcess;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Exception;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;

class ProjectController extends Controller
{
    public function __construct()
    {
        //        debugbar()->disable();
    }

    public function index(Request $request)
    {
        /** @var User $user */
        $user = auth()->user();

        $search = new Search($request);
        $params = $search->getParams();

        $projects = null;

        return Inertia::render('project/pages/Index', [
            'projects' => function () use ($search, $user, &$projects) {
                start_measure('loading projects');

                $query = $search->getQuery();

                $query = $this->loadStateForListView($query);

                $projects = $query->paginate(16);

                $pinnedIds = data_get($user, 'pinned_project_ids', []);

                $projects->each(function (Project $project) use ($pinnedIds) {
                    $project->setAttribute('is_pinned', in_array($project->id, $pinnedIds));
                    $project->append('nps_score');
                });

                $projectsForSerialization = ProjectIndexListResource::collection($projects);
                $items = $projectsForSerialization->jsonSerialize();

                stop_measure('loading projects');

                return Helpers::clonePaginatorWithNewItems($projects, $items);
            },
            'stageSummaries' => function () use (&$projects) {
                start_measure('loading stage summaries');
                $summaries = array_values(Project::getStageSummaries($projects->pluck('id')->toArray()));
                stop_measure('loading stage summaries');

                return $summaries;
            },
            'teamsOptions' => fn () => Helpers::modelsToOptions($user->getAccessibleTeams(['id', 'name'])),
            'filters' => $params,
            'statusOptions' => fn () => Helpers::assocToOptions(Project::getStatuses()),
            'rawUsersOptions' => fn () => User::query()->toBase()->orderBy('name')->get(['id', 'name', 'active', 'role']),
            'orderByOptions' => fn () => Helpers::assocToOptions(Search::getOrderByTypes()),
            'clientsOptions' => fn () => Helpers::modelsToOptions(CrmOrganization::query()->toBase()->orderBy('name')->get(['id', 'name'])),
            'locationOptions' => fn () => Helpers::modelsToOptions(Location::query()->toBase()->orderBy('name')->get(['id', 'name'])),
            'customFields' => fn () => Setting::getProjectCustomFields()->values()->toArray(),
            'hasProjectsAtAll' => fn () => Project::query()->excludeTemplates()->count() > 0,
            'tab' => fn () => $request->segment(2, 'projects'),
            'templates' => function () {
                start_measure('loading templates');
                $templates = Project::query()->where('is_template', true);

                $pinnedIds = data_get(auth()->user(), 'pinned_project_ids', []);

                $ids = implode(',', $pinnedIds);
                if (!empty($pinnedIds)) {
                    $templates = $templates->orderByRaw("id IN ($ids) DESC");
                }

                $templates = $templates->orderBy('position_name')
                    ->with([
                        'manager',
                        'stages',
                        'stages.actions',
                        'locations',
                        'scorecards',
                        'projectActions',
                    ])
                    ->get();

                $templates->each(function (Project $template) use ($pinnedIds) {
                    $template->setAttribute('is_pinned', in_array($template->id, $pinnedIds));
                });
                stop_measure('loading templates');

                return $templates;
            },
            'termsInfo' => fn () => [
                'tosOk' => $user->tos_ok,
                'acceptedTos' => $user->accepted_tos,
                'privacyOk' => $user->privacy_ok,
                'acceptedPrivacy' => $user->accepted_privacy_policy,
            ],
            'pendingRequisitionApprovals' => fn () => $user->pendingRequisitionApprovals->load(['requisition']),
            'title' => __('Projects'),
        ]);
    }

    public function stats(Request $request)
    {
        $search = new Search($request);

        $query = $search->getQuery()->with([
            'stages' => fn ($q) => $q->withCount('candidates'),
        ]);

        $projects = $query->get();

        $candidateCount = $projects->map(function (Project $project) {
            return $project->stages->sum('candidates_count');
        })->sum();

        $projectCount = $projects->count();

        $projectIds = $query->pluck('id')->toArray();

        return [
            'project_count' => $projectCount,
            'candidate_count' => $candidateCount,
            'avg_candidate_count' => $projectCount === 0 ? 0 : round($candidateCount / $projectCount, 1),
            'time_to_hire' => (new TimeToHire($projectIds, $search))->get(),
            'time_to_fill' => (new TimeToFill($projectIds, $search))->get(),
            'cnps' => (new CNps($projectIds, $search))->get(),
        ];
    }

    public function funnel(Request $request)
    {
        $search = new Search($request);

        if (!$request->get('use_logged_in_user_filters', true)) {
            $search = $search->setUseLoggedInUserFilters(false);
        }

        $grouper = $this->getFunnelGrouper($request);

        return (new ProjectFunnelService($search, $grouper))->get();
    }

    private function getFunnelGrouper(Request $request): FunnelGrouperInterface
    {
        $parts = explode('.', $request->grouper);

        if ($parts[0] === 'candidate') {
            return new ModelFieldGrouper(...$parts);
        }

        return new SourceGrouper;
    }

    public function getReport(Request $request)
    {
        $ns = "App\Classes\Statistics\MultiProjectReports";
        $class = $ns . '\\' . $request->report;

        $search = new Search($request);

        if (!$request->get('use_logged_in_user_filters', true)) {
            $search = $search->setUseLoggedInUserFilters(false);
        }

        return (array) (new $class($search))->get();
    }

    public function create_v2()
    {
        $this->authorize('create', Project::class);

        $project = new Project;
        $project->position_name = __('New Project');
        $project->status = Project::STATUS_IN_PROGRESS;
        $project->start_date = Carbon::now()->format('Y-m-d');
        $project->project_manager_id = auth()->id();
        $project->is_perpetual = false;
        $project->save();

        $project->stages()->saveMany(
            collect(Setting::get(Setting::KEY_DEFAULT_STAGES))->map(function (array $stage, int $key) {
                return new Stage([
                    'name' => $stage['name'],
                    'category' => $stage['category'],
                    'sort_order' => $key,
                ]);
            })
        );

        return response()->json([
            'id' => $project->id,
        ]);
    }

    public function show(Project $project)
    {
        if (app()->environment('local', 'staging')) {
            DB::statement('SET jit=off');
        }
        session(['latest_project_id' => $project->id]);
        Setting::setTeamSettingsContext($project->team);

        return Inertia::render('project/pages/Show', $this->buildRawProjectProps($project));
    }

    public function raw(Project $project): JsonResponse
    {
        if (app()->environment('local', 'staging')) {
            DB::statement('SET jit=off');
        }
        session(['latest_project_id' => $project->id]);
        Setting::setTeamSettingsContext($project->team);

        $data = $this->buildRawProjectProps($project)
            ->map(fn (mixed $value) => is_a($value, Closure::class) ? $value() : $value);

        return response()->json($data);
    }

    public function jobAds(Project $project)
    {
        $project->loadSharedRelationsForProjectView();

        return Inertia::render('project/pages/ProjectJobAdsView', [
            'project' => $project,
            'bottomSectionClass' => 'bottom-section-transparent',
            'navbar_container_class' => 'container-fluid',
        ]);
    }

    public function statistics(Project $project)
    {
        $project->loadSharedRelationsForProjectView()
            ->load(['stages' => fn ($q) => $q->withCount('candidates')]);

        return Inertia::render('project/pages/ProjectStatisticsView', [
            'project' => $project,
            'bottomSectionClass' => 'bottom-section-transparent',
            'navbar_container_class' => 'container-fluid',
        ]);
    }

    public function edit3(Project $project)
    {
        $project->loadSharedRelationsForProjectView();
        $project->load(['scorecards', 'locations']);

        return Inertia::render('project/pages/ProjectEditView', [
            'project' => $project,
            'bottomSectionClass' => 'bottom-section-transparent',
            'navbar_container_class' => 'container-fluid',
        ]);
    }

    public function sankey(Request $request, Project $project)
    {
        $showIndividualJourneys = $request->get('show_individuals', 'true') !== 'false';
        $group = $request->get('group');

        return (new ProjectSankey($project, showIndividualJourneys: $showIndividualJourneys, group: $group))->run();
    }

    public function getProjectsForAdding()
    {
        return Project::query()
            ->excludeTemplates()
            ->whereIn('status', [
                Project::STATUS_IN_PROGRESS,
                Project::STATUS_ON_HOLD,
            ])
            ->orderByDesc('id')
            ->with(['stages'])
            ->get();
    }

    public function commitStageTransitions(Project $project, Request $request)
    {
        $transitions = $request->get('transitions', []);

        $validator = Validator::make($transitions, [
            '*.candidate_id' => 'required|integer|exists:candidates,id',
            '*.application_id' => 'nullable|integer|exists:applications,id',
            '*.to_stage_id' => 'nullable|integer|exists:stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'errors' => $validator->errors(),
            ], 422);
        }

        $updatedStageIds = collect($transitions)->pluck('to_stage_id')->unique()->filter()->toArray();

        /** @var Collection $stages */
        $stages = $project
            ->stages
            ->keyBy('id');
        $stageSortOrders = $request->get('stage_sort_orders') ?? [];
        $affectedApplications = [];
        DB::transaction(function () use ($transitions, $stages, $project, &$affectedApplications, $stageSortOrders, &$updatedStageIds) {
            foreach ($transitions as $transition) {
                $stageId = data_get($transition, 'to_stage_id');

                // If stage ID is not set, the candidate was moved to the talent pool
                /** @var Stage|null $stage */
                $stage = $stageId ? $stages->get($stageId) : null;

                $candidateId = data_get($transition, 'candidate_id');
                $applicationId = data_get($transition, 'application_id');

                // Stage and application - existing application was moved to another stage.
                // Stage and no application - new candidate was added to the stage.
                // No stage and application - candidate was moved to the talent pool -
                //                            TEMPORARILY we delete the application, because that's what the
                //                            old code did, and it needs to be refactored in a comprehensive way.
                // No stage and no application - should never happen, something went wrong.

                if ($stage && $applicationId) {
                    $application = Application::find($applicationId);
                    $updatedStageIds[] = $application->stage_id;
                    $application->stage_id = $stage->id;
                    $application->save();
                } elseif ($stage) {
                    $application = Application::firstOrNew([
                        'candidate_id' => $candidateId,
                        'project_id' => $project->id,
                    ]);

                    $application->stage_id = $stage->id;
                    $application->save();
                } elseif ($applicationId) {
                    // The candidate was moved to the talent pool. The application is soft-deleted.
                    Application::find($applicationId)->delete();
                }

                if (isset($application)) {
                    $affectedApplications[] = $application->makeHidden('project');
                }
            }

            $statements = [];
            foreach ($stageSortOrders as $stageSortOrder) {
                $stageId = (int) $stageSortOrder['stage_id'];

                foreach ($stageSortOrder['candidate_order'] as $idx => $candidateId) {
                    $idx = (int) $idx;
                    $candidateId = (int) $candidateId;
                    $statements[] = "UPDATE applications SET sort_order=$idx WHERE stage_id=$stageId and candidate_id=$candidateId;";
                }
            }
            if (count($statements)) {
                DB::connection('tenant')->unprepared(implode('', $statements));
            }
        }, 3);

        ProjectFunnelService::invalidateFunnelCacheFor($project);

        if (count($affectedApplications)) {
            start_measure('broadcasting');
            broadcast(new ProjectBoardUpdated($project->id))->toOthers();
            stop_measure('broadcasting');
        }

        $updatedStageIds = collect($updatedStageIds)->unique()->values()->toArray();

        return [
            'moved_applications' => $affectedApplications,
            'updated_stages' => Stage::with(['actions'])->whereIn('id', $updatedStageIds)->get(),
        ];
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        $project->delete();

        return [];
    }

    public function advancedSearch(Project $project, Request $request)
    {
        $project->load(['stages', 'stages.candidates']);
        $candidateIds = $project->stages->map(function (Stage $s) {
            return $s->candidates;
        })->flatten()->pluck('id');
        $query = Candidate::query()->whereIn('id', $candidateIds);
        $hasCommentsBy = collect($request->get('hasCommentsByUserIds'));
        if ($hasCommentsBy->count()) {
            $query->whereHas('comments', function (Builder $q) use ($hasCommentsBy) {
                $q->whereIn('user_id', $hasCommentsBy->toArray());
            });
        }
        if ($after = $request->get('submissionAfter')) {
            $query->whereHas('firstSourcingActivity', function (Builder $q) use ($project, $after) {
                $q->where('project_id', $project->id);
                $q->where('activities.created_at', '>=', $after);
            });
        }
        if ($request->get('hasNewActivities', false)) {
            $query->whereHas('newActivitySummaries', function (Builder $q) use ($project) {
                $q->where('project_id', $project->id);
            });
        }

        // Filter for underage candidates if the setting is enabled
        if ($request->get('showOnlyUnderage', false) && Setting::get(Setting::KEY_MARK_UNDERAGE_CANDIDATES)) {
            $ageThreshold = Setting::get(Setting::KEY_UNDERAGE_AGE_UNDER);
            $query->whereNotNull('birthday_at')
                ->where('birthday_at', '>', now()->subYears($ageThreshold));
        }

        return $query->get(['id'])->pluck('id');
    }

    public function toXlsx(Project $project)
    {
        return Excel::download(new ProjectCandidateExport($project), Str::slug($project->position_name) . '.xlsx');
    }

    public function toggleSubscription(Project $project, Request $request)
    {
        $project->current_user_is_subscribed = $request->input('subscribed', false);
        $project->save();

        return [
            'updates' => [
                'current_user_is_subscribed' => $project->current_user_is_subscribed,
            ],
        ];
    }

    public function projectStatusReport(Request $request)
    {
        $search = new Search($request);
        $report = new ProjectStatusReport(
            $search->getQuery()->get('id')->pluck('id')->all()
        );
        $now = (string) now();

        return Excel::download($report, "project-status-report-$now.xlsx");
    }

    /**
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function monthlyHiringReport(Request $request): BinaryFileResponse
    {
        $search = new Search($request);
        $month = Carbon::parse($request->get('month') ?: now());
        $projectIds = $search->getQuery()->pluck('id');
        $report = new MonthlyHiringExport($projectIds, $month);
        $monthFormatted = $month->format('Y-m');

        return Excel::download($report, "monthly-hiring-report-$monthFormatted.xlsx");
    }

    public function totalReport(Request $request)
    {
        $search = new Search($request);

        dispatch(new ProjectTotalReportExportJob(
            user: auth()->user(),
            params: $search->getParams()
        ));

        return new JsonResponse([
            'title' => __('Report generation started'),
            'success' => __('Your report will be emailed to you.'),
        ]);
    }

    public function updateStageOrder(Request $request)
    {
        foreach ($request->get('new_orders') as $order) {
            Stage::find($order['stage_id'])->update(['sort_order' => $order['new_sort_order']]);
        }
    }

    public function loadStateForProjectView(Project $project): Project
    {
        $project->load([
            'client',
            'manager',
            'manager.avatar',
            'users' => function (BelongsToMany $q) {
                return $q->select(['users.id AS id', 'name', DB::raw('1 as no_signature'), 'role', 'last_active_at']);
            },
            'users.avatar',
            'requisitions',
            'requisitions.author',
            'requisitions.files',
            'requisitions.recruiter',
            'requisitions.approvals',
            'requisitions.approvals.user',
            //            'scorecard',
            //            'scorecard.questions',
            'scorecards.questions',
            'locations' => function ($q) {
                $q->with('place');
            },
            'stages' => fn ($q) => $q->withCount('candidates'),
            'stages.forms',
            'stages.actions' => fn (HasMany $q) => $q
                ->addSelect('*')
                ->addSelect(DB::raw('array_to_json(array(select candidate_id from action_candidate where action_id = id)) done_candidate_ids')),
            'stages.forms' => function ($formQuery) {
                $formQuery->withCount(['submissions' => function (Builder $submissionQuery) {
                    $submissionQuery->withoutGlobalScope(SubmissionAccessScope::class);
                }]);
            },
            'stages.candidates' => function (BelongsToMany $q) use ($project) {
                Project::addCandidateRatingsQuery($project, $q);
                $addProjectAndLatestMsgJoin = function (Builder $q) use ($project) {
                    return $q->where('project_id', $project->id)
                        ->leftJoin('messageables as m2', function ($q) {
                            $q->on('m2.messageable_id', 'messageables.messageable_id')
                                ->on('messageables.sent_at', '<', 'm2.sent_at');
                        })->whereNull('m2.sent_at');
                };
                $q->withPivot(['sort_order']);
                $q->with([
                    'applications',
                    'photo',
                ]);
                $q->withCount([
                    'messages' => fn (Builder $q) => $q->where('project_id', $project->id),
                    'sentMessages' => fn (Builder $q) => $addProjectAndLatestMsgJoin($q),
                    'bouncedMessages' => fn (Builder $q) => $addProjectAndLatestMsgJoin($q),
                    'openedMessages' => fn (Builder $q) => $addProjectAndLatestMsgJoin($q),
                    'clickedMessages' => fn (Builder $q) => $addProjectAndLatestMsgJoin($q),
                    'sms' => fn (Builder $q) => $q->where('project_id', $project->id),
                    'receivedSms' => fn (Builder $q) => $q->where('created_at', '>', $project->created_at),
                    'acceptedInvites' => function (Builder $q) use ($project) {
                        $q->whereHas('eventSet', function (Builder $q) use ($project) {
                            $q->where('project_id', $project->id);
                        });
                        $q->whereHas('selectedTimeSlot', function (Builder $q) {
                            $q->where('start_time', '>', now());
                        });
                    },
                    'pendingInvites' => function (Builder $q) use ($project) {
                        $q->whereHas('eventSet', function (Builder $q) use ($project) {
                            $q->where('project_id', $project->id);
                        });
                    },
                    'comments' => function (Builder $q) use ($project) {
                        $q->where('project_id', $project->id)
                            ->where('is_quick', 0)
                            ->whereNotNull('user_id');
                    },
                    'answeredVideoInvites' => fn (Builder $q) => $q->where('project_id', $project->id),
                    'pendingVideoInvites' => fn (Builder $q) => $q->where('project_id', $project->id),
                    'unresolvedDuplicateLinks',
                    'references',
                    'references as pending_references' => fn (Builder $q) => $q->whereHas('messages')->whereDoesntHave('submissions', function (Builder $submissionQuery) {
                        $submissionQuery->withoutGlobalScope(SubmissionAccessScope::class);
                    }),
                    'references as submitted_references' => fn (Builder $q) => $q->whereHas('submissions', function (Builder $submissionQuery) {
                        $submissionQuery->withoutGlobalScope(SubmissionAccessScope::class);
                    }),
                ]);
                $q->leftJoinSub(
                    DB::table('activities')
                        ->selectRaw(
                            <<<'SELECT'
                                DISTINCT ON (activities.candidate_id)
                                "candidate_id",
                                DATE(created_at) as first_activity_date,
                                created_at as first_activity_datetime,
                                activities.id as first_activity_id
                                SELECT
                        )
                        ->where('project_id', $project->id)
                        ->whereIn('activity_type_id', ActivityType::query()
                            ->whereIn('name', Candidate::SOURCING_ACTIVITIES)
                            ->pluck('id'))
                        ->orderBy('activities.candidate_id')
                        ->orderBy('activities.created_at'),
                    'sourcing_activity',
                    function (JoinClause $join) {
                        $join->on('candidates.id', '=', 'sourcing_activity.candidate_id');
                    }
                );
                $q->addSelect('first_activity_date');
                $q->addSelect('first_activity_datetime');
                $q->addSelect('first_activity_id');
                $q->addSelect(DB::raw('array_to_json(array(select ct.tag_id from candidate_tag ct where candidate_id = candidates.id)) tag_ids'));
                $q->addSelect(DB::raw('array_to_json(array(select stage_id from applications cs1 where candidates.id=cs1.candidate_id)) stage_ids'));
            },
            'stages.candidates.quickComments' => function (HasMany $q) use ($project) {
                $q->where('project_id', $project->id)
                    ->whereNotNull('content')
                    ->whereNotNull('user_id');
            },
            'stages.candidates.summary' => fn (HasOne $q) => $q->forProject($project)->select([
                'candidate_summaries.id',
                'candidate_summaries.candidate_id',
                'candidate_summaries.project_id',
                'candidate_summaries.is_failed',
                'candidate_summaries.fail_reason',
                'candidate_summaries.summary_json',
                DB::raw('candidate_summaries.anonymized_cv_html IS NOT NULL as has_anonymized_cv'),
            ]),
            'stages.candidates.incompleteTasks' => function ($q) use ($project) {
                $q->with('project');
                if (!Setting::get(Setting::KEY_CC_TASKS_FROM_ALL_PROJECTS)) {
                    $q->where('project_id', $project->id);
                }
            },
            'stages.candidates.lastStageEntryActivity' => function (HasOne $q) use ($project) {
                $q->getOneOfManySubQuery()->where('project_id', $project->id);
                $q->select([
                    'activities.id',
                    'activities.candidate_id',
                    'activities.activity_type_id',
                    'activities.created_at',
                    DB::raw('DATE_PART(\'day\'::text, current_date - activities.created_at) as days_in_stage'),
                ]);
            },
            //            'stages.candidates.newActivitySummaries' => fn(HasMany $q) => $q->where(
            //                'activities.project_id',
            //                $project->id
            //            ),
            'stages.candidates.lastCv' => fn (MorphOne $q) => $q
                ->distinct('fileable_id')
                ->select([
                    'id',
                    'fileable_id',
                    'location',
                    'fileable_type',
                ])->reorder('fileable_id')->orderByDesc('id'),
            'stages.candidates.lastEmployment' => fn (HasOne $q) => $q
                ->select([
                    'employments.id',
                    'employments.candidate_id',
                    'employments.employer_name',
                    'employments.position_title',
                    // extracting the dates here because parsing it as Carbon was slow
                    DB::raw('EXTRACT(YEAR FROM employments.date_from) as start_year'),
                    DB::raw('LPAD(EXTRACT(MONTH FROM employments.date_from)::text, 2, \'0\') as start_month'),
                    DB::raw('EXTRACT(YEAR FROM employments.date_until) as end_year'),
                    DB::raw('LPAD(EXTRACT(MONTH FROM employments.date_until)::text, 2, \'0\') as end_month'),
                ]),
            'stages.candidates.educations',
            'stages.candidates.scorecardResponseSets' => function (HasMany $q) use ($project) {
                $q->whereIn('stage_id', $project->stages()->pluck('id'));
                $q->with(['responses']);
            },
            'stages.candidates.place' => function (BelongsTo $q) use ($project) {
                $places = $project->locations()->get()->pluck('place');
                if (count($places)) {
                    $q->withDistance($places->toArray());
                }
            },
        ]);

        $this->addAdditionalAttributesToCandidates($project);

        $project->users->each(function (User $user) {
            $user->pivot->load('roles');
        });

        $project->loadCount([
            'logs',
            'children',
            'structuredJobAds',
            'applicationsPendingScreening',
        ]);
        $project->append(['current_user_is_subscribed', 'first_requisition', 'is_empty', 'nps_score', 'has_requisition']);

        return $project;
    }

    public function loadStateForListView($query)
    {
        return $query->with([
            'stages' => fn ($q) => $q->withCount([
                // We don't need the access scope any more since if user can access
                // the stage, then they can access the candidates.
                'candidates' => fn (Builder $q) => $q->withoutGlobalScope(CandidateAccessScope::class),
            ]),
            'client',
            'team',
            'manager' => fn ($q) => $q->select(['id', 'name', DB::raw('1 as no_signature')]),
            'manager.avatar',
            'requisitions',
            'requisitions.author',
            'requisitions.files',
            'requisitions.recruiter',
            'requisitions.approvals',
        ])->withCount([
            'surveyPromoters',
            'surveyDetractors',
            'surveyNeutrals',
        ]);
    }

    public function togglePin(Project $project)
    {
        /** @var User $user */
        $user = auth()->user();
        if ($user->pinned_project_ids) {
            $pinnedIds = $user->pinned_project_ids;
        } else {
            $pinnedIds = [];
        }

        if (in_array($project->id, $pinnedIds)) {
            // this removes from array by value
            $pinnedIds = array_diff($pinnedIds, [$project->id]);
            $isPinned = false;
        } else {
            $pinnedIds[] = $project->id;
            $isPinned = true;
        }

        $user->pinned_project_ids = $pinnedIds;
        $user->save();

        return [
            'is_pinned' => $isPinned,
        ];
    }

    public function projectNpsResults(Project $project)
    {
        return $project->load([
            'surveys.candidate',
        ])->append([
            'nps_score',
            'nps_response_count',
            'nps_sent_count',
        ]);
    }

    public function addStage(Project $project, Request $request)
    {
        $stage = $project->stages()->save(new Stage([
            'name' => $request->get('stage_name'),
            'category' => $request->get('category'),
            'custom_stage_category_id' => $request->get('custom_stage_category_id'),
            'sort_order' => $project->stages->max('sort_order') + 1,
        ]));

        $changes = $stage->getChanges();

        $stage = $stage->refresh();

        $stage->load([
            'forms',
            'actions',
            'candidates',
        ]);

        return [
            'updates' => $changes,
            'model' => $stage,
        ];
    }

    public function clone(Request $request)
    {
        $projectId = $request->input('data.project_id');

        /** @var Project $oldProject */
        $oldProject = Project::find($projectId);

        $isFromTemplate = $oldProject->is_template;
        $newProjectManagerId = $request->input('data.project_manager_id') ?? auth()->id();
        $cloneAsTemplate = filter_var($request->input('data.clone_as_template'), FILTER_VALIDATE_BOOLEAN);

        /** @var Project $project */
        $project = $oldProject->replicate(except: [
            'end_date',
            'deadline_at',
            'status',
            'is_template',
        ]);

        $project->status = Project::STATUS_IN_PROGRESS;
        $project->start_date = $cloneAsTemplate ? null : now();
        $project->position_name = $request->input('data.position_name') ?? __('New Project');
        $project->display_name = $request->input('data.display_name') ?? '';
        $project->project_manager_id = $newProjectManagerId;
        $project->is_template = $cloneAsTemplate;

        $project->template_id = match (true) {
            $cloneAsTemplate => null,
            $isFromTemplate => $oldProject->id,
            default => $project->template_id
        };

        $project->save();

        $project->users()->sync($request->input('data.users', null));

        $stageMap = [];
        $actionPairs = [];

        foreach ($oldProject->stages as $oldStage) {
            [$newStage, $newActionPairs] = $oldStage->clone(
                newProject: $project,
                linkToParent: $isFromTemplate,
                cloneStageVisibility: $request->input('data.clone_stage_visibility') === 'true',
                cloneAutomaticActions: $request->input('data.clone_automatic_actions') === 'true',
                cloneAsTemplate: $cloneAsTemplate
            );

            $stageMap[$oldStage->id] = $newStage->id;
            $actionPairs = array_merge($actionPairs, $newActionPairs);
        }

        // This cannot be done during stage cloning because all new stages need to exist before we can
        // replace the target stage IDs in the actions
        if ($request->input('data.clone_automatic_actions') === 'true') {
            foreach ($actionPairs as $pair) {
                [$oldAction, $action] = $pair;

                // When cloning a moving action, replace the original target stage ID in the action
                // with the one from the new project
                if ($action->action_type === Action::TYPE_MOVE_TO_STAGE) {
                    $actionData = $action->data;
                    if (isset($actionData['target_stage_id'])) {
                        $originalTargetStageId = $actionData['target_stage_id'];

                        $newTargetStageId = $stageMap[$originalTargetStageId];
                        $actionData['target_stage_id'] = $newTargetStageId;
                        $action->data = $actionData;
                        $action->save();
                    }
                }

                // When cloning an action with a message, replace the original sender with the new project manager
                if (in_array($action->action_type, Action::ACTION_TYPES_WITH_MESSAGE)) {
                    $actionData = $action->data;
                    if (isset($actionData['user_id'])) {
                        $actionData['user_id'] = $newProjectManagerId;
                        $action->data = $actionData;
                        $action->save();
                    }
                }
            }
        }

        return response([
            'messages' => [],
            'payload' => [
                'updates' => [
                    'id' => $project->id,
                ],
            ],
            'status' => 'success',
        ]);
    }

    public function createFromTemplate(Request $request)
    {
        $autoProcessResponse = (new AutoProcess)->process($request);
        $autoProcessResponseBody = json_decode($autoProcessResponse->getContent(), true);

        if ($autoProcessResponseBody['status'] != 'success') {
            return $autoProcessResponse;
        }

        $project = Project::findOrFail($autoProcessResponseBody['payload']['updates']['id']);
        $project->processProjectAfterCloningFromTemplate();

        return $autoProcessResponse;
    }

    public function markFinished(Project $project)
    {
        if ($project->is_template) {
            return false;
        }

        $project->status = Project::STATUS_FINISHED;
        // end date is set in the Project's saving event handler
        $project->save();

        return [];
    }

    public function getProjectStatExport(Builder $query): ProjectStatExport
    {
        $query->with([
            'stages',
            'stages.candidates' => function (BelongsToMany $q) {
                $q->with([
                    'sourcingActivities',
                    'sourcingActivities.activityType',
                    'sourcingActivities.stage.imports.integration',
                    'sourcingActivities.form' => fn ($q) => $q->select(['id', 'title']),
                    'sourcingActivities.integration',
                    'comments',
                    'tags',
                    'employments',
                    'files',
                    'lastCv' => fn (MorphOne $q) => $q->select(['id', 'location', 'fileable_id', 'fileable_type']),
                ]);
                $q->withCount([
                    'comments',
                    'tags',
                ]);
            },
        ]);

        return new ProjectStatExport($query->get()->all());
    }

    public function getCandidateSimilarities(Project $project, Request $request)
    {
        $candidateIds = $project->getAllCandidateIds();
        $inputCandidateIds = array_map('intval', explode(',', $request->get('candidate_ids')));

        $avgVector = Util::getAverageVectorForCandidates($inputCandidateIds);

        if (!$avgVector) {
            return [];
        }

        $avgStr = implode(',', $avgVector);

        $similarities = File::query()->whereIn('fileable_id', $candidateIds)
            ->where('fileable_type', Candidate::class)
            ->where('type', File::TYPE_CV)
            ->addSelect('fileable_id as candidate_id')
            ->whereNotNull('embedding')
            ->addSelect(DB::raw(
                "embedding <=> '[{$avgStr}]' as distance"
            ))
            ->toBase()
            ->get()
            ->mapWithKeys(fn ($f) => [(int) $f->candidate_id => (1 - (float) $f->distance)])
            ->toArray();

        return $similarities;
    }

    public function editV2(Project $project)
    {
        $this->authorize('update', $project);
        $project->load([
            'stages' => function ($q) {
                $q->withCount('allCandidates');
            },
        ]);
        $userIds = $project->users->pluck('id');
        $scorecardIds = $project->scorecards()->pluck('id');
        $locationIds = $project->locations()->pluck('locations.id');
        $project = $project->toArray();
        if (!empty($project['project_manager_id'])) {
            $projectManagerKey = $userIds->search($project['project_manager_id']);
            if ($projectManagerKey) {
                $userIds = $userIds->forget($projectManagerKey)->values()->toArray();
            }
        }
        $project['users'] = $userIds;
        $project['scorecards'] = $scorecardIds;
        $project['locations'] = $locationIds;

        \JavaScript::put('model', $project);

        $form = app(ProjectFormGrouped::class)->load($project);

        return view('project.form_v2', [
            'form' => $form,
            'navbar_container_class' => 'container-fluid',
            'bottom_section_class' => 'bottom-section-transparent',
            'pageTitle' => __('Edit project'),
        ]);
    }

    public function templates()
    {
        $templates = Project::query()->where('is_template', true);

        $pinnedIds = data_get(auth()->user(), 'pinned_project_ids', []);

        $ids = implode(',', $pinnedIds);
        if (!empty($pinnedIds)) {
            $templates = $templates->orderByRaw("id IN ($ids) DESC");
        }

        $templates = $templates->orderBy('position_name')
            ->with([
                'manager',
                'stages',
                'stages.actions',
                'locations',
                'scorecards',
                'projectActions',
            ])
            ->get();

        $templates->each(function (Project $template) use ($pinnedIds) {
            $template->setAttribute('is_pinned', in_array($template->id, $pinnedIds));
        });

        return response()->json($templates);
    }

    public function children(Project $project)
    {
        $projects = $this->loadStateForListView($project->children())->get();

        return response()->json($projects);
    }

    public function landings(Project $project)
    {
        $landings = $project->landings()->get();

        if ($landings->isEmpty()) {
            return response()->json([]);
        }

        $publicIdMap = collect($landings)
            ->keyBy('id')
            ->map(function (Landing $landing) {
                return (string) ($landing->public_id);
            })->toArray();

        $landingStatsService = new LandingStats;

        $impressions = $landingStatsService->getBulkImpressions($publicIdMap);
        $submissions = $landingStatsService->getBulkSubmissions($publicIdMap);

        $landings->each(function (Landing $landing) use ($impressions, $submissions, $publicIdMap) {
            $landing->impressions = (int) ($impressions[$publicIdMap[$landing->id]] ?? 0);
            $landing->submissions = (int) ($submissions[$publicIdMap[$landing->id]] ?? 0);
            $landing->structuredAds->each(function (StructuredJobAd $ad) {
                $ad->append('links');
            });
        });

        return response()->json($landings);
    }

    // TODO: Find a more suitable place for this method
    // Find all possible ways for the candidate to be in the project
    public function importexport(Project $project)
    {
        // Find forms that are either:
        // - directly linked to a stage in the project
        // - included in a landing that is linked to a stage in the project
        $forms = Form::query()
            ->where(function ($q) use ($project) {
                $q->whereHas('stage', function (Builder $q2) use ($project) {
                    $q2->where('project_id', $project->id);
                });
                $q->orWhere(function (Builder $q3) use ($project) {
                    $q3->whereNull('stage_id')->whereExists(function ($q4) use ($project) {
                        $q4->select('landings.id')
                            ->from('landings')
                            ->leftJoin('stages', 'stages.id', '=', 'landings.stage_id')
                            ->whereRaw("data::text ilike '%' || forms.token || '%'")
                            ->where('stages.project_id', $project->id);
                    });
                });
            })
            ->with([
                'stage',
            ])
            ->get();

        $displayForms = $forms
            ->filter(function (Form $form) {
                return $form->hasIdentityField();
            })
            ->map(function (Form $form) use ($project) {
                return [
                    ...$form->toArray(),
                    'linked_landings' => $form->getLinkedLandingsForProject($project),
                    'view_url' => route('forms.submissions', $form->id),
                ];
            })
            ->values();

        $structuredJobAds = $project->structuredJobAds()->with(['integrations', 'targetStage', 'landing', 'emotionimages'])->get();

        $imports = Import::query()
            ->whereHas('stage', function (Builder $q) use ($project) {
                $q->where('project_id', $project->id);
            })
            ->with([
                'stage',
                'integration',
            ])
            ->get();

        return response()->json([
            'forms' => $displayForms,
            'structuredJobAds' => $structuredJobAds,
            'imports' => $imports,
        ]);
    }

    public function actions(Project $project)
    {
        $project->loadSharedRelationsForProjectView();

        return Inertia::render('project/pages/ProjectActionsView', [
            'project' => $project,
            'actions' => $project->projectActions()->get(),
            'bottomSectionClass' => 'bottom-section-transparent',
            'navbar_container_class' => 'container-fluid',
        ]);
    }

    public function screening(Project $project)
    {
        $project->loadSharedRelationsForProjectView();

        $project->load(['screeningCriteria']);

        return Inertia::render('project/pages/ProjectScreeningView', [
            'project' => $project,
            'applications' => $project->applications()
                ->orderByDesc('id')
                ->whereHas('stage', function (Builder $q) {
                    $q->where('category', Stage::CATEGORY_SUBMISSIONS);
                })->with(['candidate', 'candidate.lastCv', 'screeningCriterionResponses'])->get(),
            'bottomSectionClass' => 'bottom-section-transparent',
            'navbar_container_class' => 'container-fluid',
        ]);
    }

    public function runScreener(Project $project, Request $request)
    {
        // Check if override is requested
        $override = $request->input('override', false);

        // Only validate if not overriding
        if (!$override) {
            // First, validate the criteria for discriminatory content
            $validator = new \App\Classes\Screening\CriteriaValidator($project);
            $validationResults = $validator->validate();

            // Check if any criteria are discriminatory
            $unsuitableCriteria = collect($validationResults)->filter(function ($result) {
                return $result['is_unsuitable'];
            });

            // If there are discriminatory criteria, return them instead of running the screener
            if ($unsuitableCriteria->isNotEmpty()) {
                return response()->json([
                    'success' => false,
                    'validation_errors' => $validationResults,
                    'message' => 'Some screening criteria may be unsuitable. Please review and update them before running the screener.',
                ], 422);
            }
        }

        // If all criteria are valid or override is true, proceed with running the screener
        $applicationIds = collect($request->input('application_ids', []));

        $applicationIds->map(function ($applicationId) {
            dispatch(function () use ($applicationId) {
                /** @var Application $application */
                $application = Application::with('candidate.lastCv')->find($applicationId);

                $screener = new Screener($application);

                try {
                    $result = $screener->run();
                } catch (\Exception $e) {
                    $result = $screener->recordErrorState($e->getMessage());
                }

                broadcast(new ProjectScreeningUpdated($application->project_id, $application->id, $result));
            })->onQueue('high');
        });

        return response()->json(['success' => true]);
    }

    private function addAdditionalAttributesToCandidates(Project $project): void
    {
        $sourcingActivities = null;
        $addInitials = Setting::get(Setting::KEY_CC_SHOW_INITIALS_IN_BLIND_MODE);

        if (Setting::get(Setting::KEY_CC_SOURCE)) {
            $sourcingActivities = Activity::query()
                ->whereIn('id', $project->stages->pluck('candidates.*.first_activity_id')->flatten()->unique()->values())
                ->with([
                    'activityType',
                    'integration',
                ])
                ->get();
        }

        start_measure('plucking candidate ids');
        $candidateIds = $project->stages->pluck('candidates.*.id')->flatten()->unique()->values();
        stop_measure('plucking candidate ids');

        start_measure('fetching new activities');
        $newActivitySummaries = Activity::query()->toBase()
            ->where(function ($q) {
                $q->where('activities.user_id', '!=', auth()->id())
                    ->orWhereNull('activities.user_id');
            })
            ->whereNotExists(function ($q) {
                $q->selectRaw(1)
                    ->from('activity_receipts')
                    ->where('user_id', auth()->id())
                    ->whereColumn('activity_receipts.activity_id', 'activities.id');
            })->whereIn('candidate_id', $candidateIds)
            ->where('project_id', $project->id)
            ->groupBy('activities.candidate_id', 'activities.activity_type_id')
            ->select([
                'activities.candidate_id',
                'activities.activity_type_id',
                DB::raw('string_agg(activities.id::text, \',\') as activity_ids'),
                DB::raw('count(distinct activities.id) as count'),
            ])->get()
            ->each(fn (&$item) => $item->activity_ids = explode(',', $item->activity_ids))
            ->groupBy('candidate_id');
        stop_measure('fetching new activities');

        start_measure('adding additional attributes to candidates');
        $project->stages->each(function (Stage $stage) use ($sourcingActivities, $addInitials, $newActivitySummaries) {
            $stage->candidates->each(function (Candidate $candidate) use ($sourcingActivities, $addInitials, $newActivitySummaries) {
                if ($sourcingActivities !== null) {
                    $candidate->setAttribute(
                        'submission_source',
                        SourceNormalizer::normalize(
                            $sourcingActivities->firstWhere('candidate_id', $candidate->id)
                        )
                    );
                }
                if ($addInitials) {
                    $candidate->append('initials');
                }
                $summaries = $newActivitySummaries->get($candidate->id, collect([]));
                $candidate->setRelation('newActivitySummaries', $summaries);
            });
        });
        stop_measure('adding additional attributes to candidates');
    }

    public static function buildCandidateInProjectURL(Project $project, Candidate $candidate): string
    {
        return route('projects.show', ['project' => $project, 'candidate_id' => $candidate->id]);
    }

    public function convertToTemplate(Project $project): Response
    {
        abort_unless($project->is_empty, Response::HTTP_UNPROCESSABLE_ENTITY);
        abort_if($project->is_template, Response::HTTP_UNPROCESSABLE_ENTITY);

        Model::unguarded(function () use ($project) {
            DB::transaction(function () use ($project) {
                $project->update([
                    'is_template' => true,
                    'accessible_only_members' => false,
                    'project_manager_id' => $project->project_manager_id ?? auth()->id(),
                    'status' => Project::STATUS_IN_PROGRESS,
                    'start_date' => null,
                    'end_date' => null,
                    'deadline_at' => null,
                    'warranty_until' => null,
                    'template_id' => null,
                ]);

                $project->projectUsers()->delete();

                $project->stages()->update([
                    'is_template' => true,
                    'template_id' => null,
                ]);

                $project->stages()
                    ->withoutGlobalScopes()
                    ->get()
                    ->flatMap(fn (Stage $stage) => $stage->actions)
                    ->each(function (Action $action) use ($project) {
                        if ($action->scheduled_at || $action->trigger === Action::TRIGGER_SINGLE_AUTOMATIC) {
                            $action->delete();
                        } else {
                            $action->update([
                                'is_template' => true,
                                'template_id' => null,
                                'data' => in_array($action->action_type, Action::ACTION_TYPES_WITH_MESSAGE)
                                    ? [...$action->data, 'user_id' => $project->project_manager_id]
                                    : $action->data,
                            ]);
                        }
                    });
            });
        });

        return response(status: Response::HTTP_OK);
    }

    public function commentsGroupedByCandidate(Project $project): JsonResponse
    {
        $comments = Comment::query()
            ->whereRelation('candidate', function (Builder $candidateQuery) use ($project) {
                $candidateQuery->whereHas('stages', function (Builder $stageQuery) use ($project) {
                    $stageQuery->whereBelongsTo($project);
                });
            })
            ->with('user')
            ->get();

        $commentsGroupedByCandidate = $comments->groupBy(fn (Comment $comment) => $comment->candidate_id);

        return new JsonResponse($commentsGroupedByCandidate);
    }

    public function getStageGroups(Request $request)
    {
        $query = Stage::query();
        if (Setting::get(Setting::KEY_USE_CUSTOM_STAGE_CATEGORIES)) {
            $query->whereNull('custom_stage_category_id');
        } else {
            $query->whereNull('category');
        }

        return $query
            ->with(['project'])
            ->select('id', 'name', 'project_id')
            ->get()
            ->groupBy('name')
            ->sortKeys()
            ->map(function (Collection $group, $key) {
                $limit = 3;
                $count = $group->count();
                $others = $count - $limit;

                return [
                    'id' => $key,
                    'stage_name' => $key,
                    'stage_ids' => $group->pluck('id'),
                    'projects' => $group->pluck('project'),
                    'project_names' => $count > $limit
                        ? $group->take($limit)->pluck('project.position_name')->implode(', ') . " and $others others"
                        : $group->pluck('project.position_name')->implode(', '),
                    'category' => null,
                ];
            })->values();
    }

    public function setCategoryForStages(Request $request)
    {
        Stage::query()->whereIn('id', $request->stage_ids)
            ->get()
            ->each(fn (Stage $stage) => $stage->update([
                'category' => $request->category,
                'custom_stage_category_id' => $request->custom_stage_category_id,
            ]));
    }

    private function buildRawProjectProps(Project $project): Collection
    {
        return collect([
            'rawProject' => function () use ($project) {
                start_measure('load projects');
                $state = $this->loadStateForProjectView($project);
                stop_measure('load projects');
                start_measure('serialize project model');
                $arr = $state->toArray();
                stop_measure('serialize project model');

                return $arr;
            },
            'users' => fn () => $project->users,
            'user' => auth()->user(),
            'activityTypes' => fn () => ActivityType::all()->keyBy('id'),
            'usedTags' => function () use ($project) {
                start_measure('getting_used_tag_ids');
                $usedTagIds = $project->stages->pluck('candidates.*.tag_ids')->flatten()->unique()->values();
                $tags = Tag::query()->whereIn('id', $usedTagIds)
                    ->select(['id', 'color', 'name', 'light_color'])
                    ->get()
                    ->keyBy('id');
                stop_measure('getting_used_tag_ids');

                return $tags;
            },
            'usedStages' => function () use ($project) {
                start_measure('getting_used_stages');
                $usedStages = Stage::query()
                    ->whereIn('stages.id', $project->stages->pluck('candidates.*.stage_ids')->flatten()->unique()->values())
                    ->join('projects', 'projects.id', '=', 'stages.project_id')
                    ->select(['stages.id', 'stages.project_id', 'stages.name', 'stages.fair_evaluations', 'projects.position_name', 'projects.status as project_status'])
                    ->get()
                    ->keyBy('id');
                stop_measure('getting_used_stages');

                return $usedStages;
            },
            'bottomSectionClass' => 'bottom-section-transparent',
            'navbar_container_class' => 'container-fluid',
            'title' => $project->position_name,
        ]);
    }
}
