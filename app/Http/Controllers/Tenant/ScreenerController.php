<?php

namespace App\Http\Controllers\Tenant;

use App\Classes\Screening\CriteriaGenerator;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ScreeningCriterion;
use App\Models\ScreeningCriterionResponse;
use Illuminate\Http\Request;

class ScreenerController extends Controller
{
    public function generateAndPersistCriteria(Project $project)
    {
        // Delete existing criteria for this project
        $project->screeningCriteria()->delete();

        // Generate new criteria
        $criteria = (new CriteriaGenerator($project))->generateCriteria();

        if (!is_array($criteria) || !$criteria || (count($criteria) === 0)) {
            return response()->json(['error' => __('No criteria generated. There might be too little context available. Project landing pages and descriptions are used to generate the criteria. You can enter the criteria manually.')],
                400);
        }

        // Save the new criteria to the database
        $savedCriteria = [];
        foreach ($criteria as $criterion) {
            $savedCriteria[] = $project->screeningCriteria()->create([
                'criterion' => $criterion,
                'is_required' => false,
            ]);
        }

        return $savedCriteria;
    }

    public function addCriterion(Project $project, Request $request)
    {
        $validated = $request->validate([
            'criterion' => 'required|string',
            'is_required' => 'boolean',
        ]);

        $criterion = $project->screeningCriteria()->create($validated);

        return response()->json($criterion);
    }

    public function deleteCriterion(ScreeningCriterion $screeningCriterion)
    {
        $screeningCriterion->responses()->delete();
        $screeningCriterion->delete();

        return response()->json(['success' => true]);
    }

    public function updateCriterion(ScreeningCriterion $screeningCriterion, Request $request)
    {
        $validated = $request->validate([
            'criterion' => 'sometimes|string',
            'is_required' => 'sometimes|boolean',
        ]);

        $screeningCriterion->update($validated);

        return response()->json($screeningCriterion);
    }

    public function clearScreenerResults(Project $project)
    {
        // Get all applications for this project
        $applicationIds = $project->applications()->pluck('id');

        // Delete all screening responses for these applications
        $deleted = ScreeningCriterionResponse::whereIn('application_id', $applicationIds)->delete();

        return response()->json([
            'success' => true,
            'deleted_count' => $deleted,
        ]);
    }
}
