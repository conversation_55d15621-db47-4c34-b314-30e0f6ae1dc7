<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;

class AiUsage extends TenantModel
{
    protected $guarded = [];

    protected $casts = [
        'input' => 'array',
        'output' => 'array',
    ];

    // All prices in EUR
    const PRICES = [
        Setting::AI_PROVIDER_AZURE_EUR => [
            // price per 1M tokens (input, cached input, output)
            '/^gpt-4o/' => [
                'input_tokens' => 2.54677,
                'cached_input_tokens' => 1.2734,
                'output_tokens' => 10.1871,
            ],
            '/^gpt-4.1/' => [
                'input_tokens' => 1.76,
                'cached_input_tokens' => 0.44,
                'output_tokens' => 7.04,
            ],
            // transcriptions are priced per input minute
            '/^whisper-1/' => 0.00566,
        ],
        Setting::AI_PROVIDER_OPENAI => [
            // price per 1M tokens (input, cached input, output)
            '/^gpt-4o/' => [
                'input_tokens' => 2.20,
                'cached_input_tokens' => 1.10,
                'output_tokens' => 8.79,
            ],
            '/^gpt-4.1/' => [
                'input_tokens' => 1.78,
                'cached_input_tokens' => 0.45,
                'output_tokens' => 7.14,
            ],
            // transcriptions are priced per input minute
            '/^whisper-1/' => 0.0053,
        ],
    ];

    public static function getPrice(string $provider, string $model): array|float|null
    {
        $model = strtolower($model);

        if (!isset(self::PRICES[$provider])) {
            return null;
        }

        foreach (self::PRICES[$provider] as $pattern => $price) {
            if (preg_match($pattern, $model)) {
                return $price;
            }
        }

        return null;
    }

    public static function getUsageSelects()
    {
        return [
            'duration' => '(output->>\'duration\')::float',
            'cached_input_tokens' => '(output->\'usage\'->\'prompt_tokens_details\'->>\'cached_tokens\')::int',
            'input_tokens' => '(output->\'usage\'->>\'prompt_tokens\')::int - (output->\'usage\'->\'prompt_tokens_details\'->>\'cached_tokens\')::int',
            'output_tokens' => '(output->\'usage\'->>\'completion_tokens\')::int',
        ];
    }

    public static function addCostToRow(object &$row)
    {
        $price = self::getPrice($row->provider, $row->model);

        if (is_array($price)) {
            $row->cost = 0;
            foreach ($price as $key => $value) {
                if (isset($row->{$key})) {
                    $row->cost += $row->{$key} * $value / 1_000_000;
                }
            }
        } else {
            $row->cost = ((float) $row->duration ?? 0) / 60 * $price;
        }

        return $row;
    }

    public static function getWeeklyUsages()
    {
        $query = self::query()->toBase();

        $query->select([
            'provider',
            'model',
            DB::raw('date_trunc(\'week\', created_at) as week'),
        ]);

        foreach (self::getUsageSelects() as $alias => $select) {
            $query->addSelect(DB::raw("SUM($select)" . ' as ' . $alias));
        }

        $query->groupBy('provider', 'model', DB::raw('date_trunc(\'week\', created_at)'))
            ->orderBy(DB::raw('date_trunc(\'week\', created_at)'), 'desc');

        $usages = $query->get();

        return $usages->each(self::addCostToRow(...))->groupBy('week');
    }

    public static function getLast30DayUsage(): float
    {
        $query = self::query()->toBase();

        $query->select([
            'provider',
            'model',
        ]);

        foreach (self::getUsageSelects() as $alias => $select) {
            $query->addSelect(DB::raw("SUM($select)" . ' as ' . $alias));
        }

        $query->where('created_at', '>', now()->subDays(30))
            ->groupBy('provider', 'model');

        $usages = $query->get();

        return $usages->sum(function ($item) {
            return self::addCostToRow($item)->cost;
        });
    }

    public static function getLast30DayUsageByCaller(): array
    {
        $query = self::query()->toBase();

        $query->select([
            'provider',
            'model',
            'caller',
        ]);

        foreach (self::getUsageSelects() as $alias => $select) {
            $query->addSelect(DB::raw("SUM($select)" . ' as ' . $alias));
        }

        $query->where('created_at', '>', now()->subDays(30))
            ->groupBy('provider', 'model', 'caller');

        $usages = $query->get();

        return $usages->each(self::addCostToRow(...))
            ->groupBy('caller')
            ->map(function ($items) {
                return $items->sum(function ($item) {
                    return $item->cost;
                });
            })
            ->toArray();
    }

}
