<?php

namespace App\Models;

use Hyn\Tenancy\Traits\UsesTenantConnection;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class Template extends TenantModel
{
    protected $guarded = [];

    protected $casts = [
        'data' => 'array',
    ];

    const TYPE_CANDIDATE_MESSAGE = 1;
    const TYPE_EVENT_SET = 2;
    const TYPE_VIDEO_INTERVIEW = 3;
    const TYPE_VIDEO_MESSAGE = 4;
    const TYPE_CONSENT_RENEWAL = 5;
    const TYPE_INVITE = 6;
    const TYPE_REFERENCE = 7;
    const TYPE_SMS = 8;
    const TYPE_USER_MESSAGE = 9;

    public function __call($method, $parameters)
    {
        if (preg_match('/^get\w+BodyAttribute$/', $method)) {
            return $this->body;
        }

        if (preg_match('/^set\w+BodyAttribute$/', $method)) {
            $this->attributes['body'] = $parameters[0];
            return null;
        }

        return parent::__call($method, $parameters);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function getTypes()
    {
        return [
            self::TYPE_CANDIDATE_MESSAGE => __('Candidate message'),
            self::TYPE_EVENT_SET => __('Interview invite'),
            self::TYPE_VIDEO_INTERVIEW => __('Async video interview invite'),
            self::TYPE_VIDEO_MESSAGE => __('Video message'),
            self::TYPE_INVITE => __('Invite confirmation'),
            //self::TYPE_CONSENT_RENEWAL => __('Consent renewal'),
            self::TYPE_REFERENCE => __('Ask referee to submit evaluation'),
            self::TYPE_SMS => __('SMS'),
            self::TYPE_USER_MESSAGE => __('User message'),
        ];
    }

    public function getDataAttribute($value)
    {
        $value = $value ?? '{}';
        $data = json_decode($value, true);
        if (isset($data['form_id'])) {
            $data['user_form_id'] = $data['form_id'];
        }
        return $data;
    }
}
