<?php

namespace App\Models;

use App\Classes\CandidateHistory\Historizable;
use App\Classes\CandidateHistory\HistoryItemValue;
use App\Helpers;
use App\Scopes\TaskAccessScope;
use Hyn\Tenancy\Traits\UsesTenantConnection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Task extends TenantModel implements Historizable
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'done' => 'boolean',
    ];

    protected static function booted()
    {
        static::addGlobalScope(new TaskAccessScope());

        static::created(function (Task $task) {
            Activity::log(Activity::TASK_CREATED, [$task]);
        });

        static::updated(function (Task $task) {
            if (Helpers::didModelFieldChange($task, 'done', false, true)) {
                Activity::log(Activity::TASK_COMPLETED, [$task]);
            }
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function assignee()
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    public function candidate()
    {
        return $this->belongsTo(Candidate::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function toHistoryItemValue(): HistoryItemValue
    {
        $ass = optional($this->assignee);
        return new HistoryItemValue(
            "$this->title (Assignee: {$ass->name}, due: {$this->deadline_at})",
            $this->id,
            self::class,
            data_get($this, 'assignee.avatar_url'),
        );
    }

    public function titleMatches(Builder $query, ?string $name): Builder
    {
        if (!$name) {
            return $query;
        }
        return $query->where(function (Builder $q) use ($name) {
            $q->where('title', 'ilike', "%$name%")
                ->orWhereHas('project', function (Builder $q2) use ($name) {
                    $q2->where('position_name', 'ilike', "%$name%");
                })->orWhereHas('candidate', function (Builder $q3) use ($name) {
                    $q3->where('name', 'ilike', "%$name%")
                        ->orWhere('email', 'ilike', "%$name%");
                });
        });
    }

    public function assigneeCurrentUserMatches(Builder $query, ?bool $assigneeCurrentUser): Builder
    {
        if (!$assigneeCurrentUser) {
            return $query;
        }
        return $query->where('assignee_id', auth()->id());
    }

    public function assigneeUserMatches(Builder $query, ?array $userIds): Builder
    {
        if (!empty($userIds)) {
            return $query->whereIn('assignee_id', $userIds);
        }
        return $query;
    }

    public function withCandidate(Builder $query): Builder
    {
        return $query->whereHas('candidate')->whereHas('project');
    }

    public function scopeForProject($query, Project $project)
    {
        return $query->where('project_id', $project->id);
    }
}
