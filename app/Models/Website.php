<?php

namespace App\Models;

use App\Classes\Billing\Contracts\SubscriptionServiceContract;
use App\Classes\Billing\SubscriptionService;
use Hyn\Tenancy\Generators\Database\DefaultPasswordGenerator;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Stripe\Customer;

class Website extends \Hyn\Tenancy\Models\Website
{
    public static $bypassPgBouncer = false;

    protected $casts = [
        'features' => 'array',
        'table_data' => 'array',
        'table_data_at' => 'datetime',
    ];

    protected $fillable = ['minimum_spend_cents'];

    const FEATURE_KEY_VIDEO_INTERVIEWS = 'video_interviews';
    const FEATURE_KEY_REQUISITIONS = 'requisitions';

    const FEATURE_KEY_LT_MERGE_TAGS = 'lt_merge_tags';
    const FEATURE_KEY_LV_MERGE_TAGS = 'lv_merge_tags';
    const FEATURE_LANG_RU = 'lang_ru';

    const FEATURE_KEY_TEAMS = 'teams';
    const FEATURE_KEY_SSO = 'sso';

    const FEATURE_KEY_SCIM = 'scim';
    const FEATURE_AUDIT_LOG = 'audit_log';

    const FEATURE_JOB_ADS_IP_LIMIT = 'job_ads_ip_limit';
    const FEATURE_ENABLE_AI = 'enable_ai';
    const FEATURE_TRANSLATIONS = 'translations';
    const FEATURE_EDIT_ALL_COMMENTS = 'edit_all_comments';

    const FEATURE_SCORECARDS = 'scorecards';
    const FEATURE_CAREER_PAGES = 'career_pages';
    const FEATURE_REFERENCES = 'references';
    const FEATURE_CREDENTIAL_VALIDITY = 'credential_validity';
    const FEATURE_WEBHOOK_ACTIONS = 'webhook_actions';
    const FEATURE_FAVICON = 'favicon';

    const FEATURE_DUMP_DATABASE = 'dump_database';
    const FEATURE_AI_MEETING_ANALYSIS = 'ai_transcripts';

    const FEATURE_AI_CANDIDATE_SCREENING = 'ai_candidate_screening';

    const SMS_PROVIDER_TWILIO = 'twilio';
    const SMS_PROVIDER_MESSENTE = 'messente';

    const FEATURE_DEFAULTS = [
        self::FEATURE_KEY_VIDEO_INTERVIEWS => false,
        self::FEATURE_KEY_REQUISITIONS => false,
        self::FEATURE_KEY_LT_MERGE_TAGS => false,
        self::FEATURE_KEY_LV_MERGE_TAGS => false,
        self::FEATURE_KEY_SSO => false,
        self::FEATURE_KEY_SCIM => false,
        self::FEATURE_AUDIT_LOG => false,
        self::FEATURE_ENABLE_AI => true,
        self::FEATURE_KEY_TEAMS => false,
        self::FEATURE_DUMP_DATABASE => false,
        self::FEATURE_SCORECARDS => false,
        self::FEATURE_CAREER_PAGES => false,
        self::FEATURE_REFERENCES => false,
        self::FEATURE_JOB_ADS_IP_LIMIT => false,
        self::FEATURE_AI_MEETING_ANALYSIS => false,
        self::FEATURE_EDIT_ALL_COMMENTS => false,
        self::FEATURE_LANG_RU => false,
        self::FEATURE_CREDENTIAL_VALIDITY => false,
        self::FEATURE_WEBHOOK_ACTIONS => false,
        self::FEATURE_FAVICON => false,
        self::FEATURE_AI_CANDIDATE_SCREENING => false,
    ];

    const BRAND_VERSION_RECRUITLAB = 1;
    const BRAND_VERSION_TEAMDASH = 2;

    const SECTORS = [
        null => 'Not set',
        'Accountancy, banking and finance' => 'Accountancy, banking and finance',
        'Business, consulting and management' => 'Business, consulting and management',
        'Charity and voluntary work' => 'Charity and voluntary work',
        'Creative arts and design' => 'Creative arts and design',
        'Energy and utilities' => 'Energy and utilities',
        'Engineering and manufacturing' => 'Engineering and manufacturing',
        'Environment and agriculture' => 'Environment and agriculture',
        'Healthcare' => 'Healthcare',
        'Hospitality and events management' => 'Hospitality and events management',
        'Information technology' => 'Information technology',
        'Law' => 'Law',
        'Law enforcement and security' => 'Law enforcement and security',
        'Leisure, sport and tourism' => 'Leisure, sport and tourism',
        'Marketing, advertising and PR' => 'Marketing, advertising and PR',
        'Media and internet' => 'Media and internet',
        'Property and construction' => 'Property and construction',
        'Public services and administration' => 'Public services and administration',
        'Recruitment and HR' => 'Recruitment and HR',
        'Retail' => 'Retail',
        'Sales' => 'Sales',
        'Science and pharmaceuticals' => 'Science and pharmaceuticals',
        'Social care' => 'Social care',
        'Teacher training and education' => 'Teacher training and education',
        'Transport and logistics' => 'Transport and logistics',
        'Other' => 'Other',
    ];

    public function getFeaturesAttribute($o): array
    {
        if (!$o) {
            $features = [];
        } else {
            $features = json_decode($o, true);
        }

        $result = [];
        foreach (self::FEATURE_DEFAULTS as $key => $value) {
            $result[$key] = data_get($features, $key, self::FEATURE_DEFAULTS[$key]);
        }

        return $result;
    }

    public function getDisplayNameAttribute()
    {
        $parts = explode('.', $this->uuid);

        return $parts[0] ?? '';
    }

    public function getStripeCustomerId()
    {
        throw new \Exception('Deprecated!');
        if ($this->stripe_customer_id) {
            return $this->stripe_customer_id;
        }

        $res = Customer::create([
            'metadata' => [
                'unique_name' => $this->uuid,
            ],
        ]);

        $this->stripe_customer_id = $res->id;
        $this->save();

        return $this->stripe_customer_id;
    }

    public function getSubscriptionService(): SubscriptionServiceContract
    {
        return new SubscriptionService($this);
    }

    public function isSmsEnabled(): bool
    {
        return (bool) ($this->twilio_service_sid || $this->alphanumeric_sender_id) || $this->messente_sender_id;
    }

    public function hostnames(): HasMany
    {
        return parent::hostnames()->orderByDesc('is_default')->orderByDesc('id');
    }

    public function mailIdentities(): HasMany
    {
        return $this->hasMany(MailIdentity::class, 'website_id', 'id');
    }

    public function createPgBouncerDatabasesEntry(bool $restart = true)
    {
        info('creating pgb entry');
        $pass = (new DefaultPasswordGenerator(app()))->generate($this);

        $host = config()->get('database.connections.system.pg_host_docker') ?: config()->get('database.connections.system.pg_host');
        $port = config()->get('database.connections.system.pg_port');
        $entry = "$this->uuid = user=$this->uuid password=$pass host=$host port=$port";
        $fp = fopen(config('database.pg_bouncer_ini_path'), 'a');
        fwrite($fp, "$entry\n");
        fclose($fp);

        if ($restart) {
            self::reloadPgBouncerConfig();
        }
    }

    public static function reloadPgBouncerConfig(): void
    {
        touch(storage_path('lock_restarting_pgb'));
        // wait for web requests to finish
        sleep(1);

        try {
            $isDockerized = shell_exec('docker ps -q --filter "name=pgbouncer"');
            if ($isDockerized === null) {
                $isDockerized = shell_exec('sudo docker ps -q --filter "name=pgbouncer"');
            }

            // restart pgbouncer
            if ((bool) $isDockerized) {
                $output = shell_exec('docker compose restart pgbouncer');
                if ($output === null) {
                    shell_exec('sudo docker compose restart pgbouncer');
                }
            } else {
                // www-data must be able to do this; add to sudoers:
                // www-data ALL = NOPASSWD: /usr/sbin/service
                // https://askubuntu.com/questions/965420/how-can-i-start-stop-as-a-service-as-a-normal-user-without-sudo
                shell_exec('sudo service pgbouncer restart');
            }
        } catch (\Throwable $t) {
            throw $t;
        } finally {
            // although command finishes, it will not have restarted yet
            sleep(1);
            unlink(storage_path('lock_restarting_pgb'));
        }
    }
}
