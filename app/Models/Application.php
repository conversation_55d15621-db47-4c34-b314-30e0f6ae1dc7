<?php

namespace App\Models;

use App\Scopes\ApplicationAccessScope;
use Closure;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Application extends TenantModel
{
    use HasFactory, SoftDeletes;

    protected $table = 'applications';

    protected $fillable = [
        'candidate_id',
        'stage_id',
        'project_id',
        'dropout_reason_id',
    ];

    protected $guarded = [];

    /**
     * @var Closure(Application) | null
     *                                  This closure enables the ability to customize how the application movement is logged.
     *                                  If set, this closure will be called instead of the default logging mechanism.
     *
     * This is useful when you need to log a more specific activity type, e.g. when importing applications from a job portal.
     * See \App\DataExchange\ImportHelpers::addCandidateToStage() for an example of how to use this.
     *
     * The closure should accept an Application object as its only argument.
     */
    private ?Closure $movementActivityLogger = null;

    protected static function booted()
    {
        static::addGlobalScope(new ApplicationAccessScope);

        // Let's ensure that the project ID is always set
        static::creating(function (Application $application) {
            $application->project_id = $application->project_id ?? $application->stage->project_id;
        });

        static::saved(function (Application $application) {
            // If the application is being moved to a new stage, log that it was moved
            if (isset($application->movementActivityLogger)) {
                // If a closure is provided, use that to log how the application was moved.
                $application->getMovementActivityLogger()($application);
            } else {
                // Otherwise, use the default logging mechanism.
                if ($application->getOriginal('stage_id')) {
                    if ($application->wasChanged('stage_id')) {
                        Activity::log(Activity::MOVED_TO_STAGE, [
                            $application,
                        ]);
                    }
                } else {
                    Activity::log(Activity::ADDED_TO_STAGE, [
                        $application,
                    ]);
                }
            }

            // If the project is only accessible to members, make the candidate private
            if ($application->project->accessible_only_members) {
                $application->candidate->is_private = true;
                $application->candidate->save();
            }

            if ($application->wasChanged('dropout_reason_id')) {
                if ($application->dropoutReason) {
                    Activity::log(Activity::ADDED_DROPOUT_REASON, [
                        $application,
                        $application->dropoutReason,
                    ]);
                } else {
                    Activity::log(Activity::REMOVED_DROPOUT_REASON, [
                        $application,
                    ]);
                }
            }
        });

        static::softDeleted(function (Application $application) {
            Activity::log(Activity::REMOVED_FROM_PROJECT, [
                'application_id' => $application->id,
                'project_id' => $application->project_id,
                'candidate_id' => $application->candidate_id,
                'stage_id' => $application->stage_id,
            ]);
        });
    }

    public function candidate()
    {
        return $this->belongsTo(Candidate::class);
    }

    public function stage()
    {
        return $this->belongsTo(Stage::class);
    }

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function dropoutReason()
    {
        return $this->belongsTo(DropoutReason::class);
    }

    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    public function getMovementActivityLogger(): ?Closure
    {
        return $this->movementActivityLogger;
    }

    public function setMovementActivityLogger(Closure $logger): void
    {
        $this->movementActivityLogger = $logger;
    }

    /**
     * Get the screening criterion responses for this application.
     */
    public function screeningCriterionResponses(): HasMany
    {
        return $this->hasMany(ScreeningCriterionResponse::class);
    }
}
