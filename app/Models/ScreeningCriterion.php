<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ScreeningCriterion extends TenantModel
{
    use HasFactory;

    protected $table = 'screening_criteria';

    protected $guarded = [];

    protected $casts = [
        'is_required' => 'boolean',
    ];

    protected static function booted()
    {
        // When a criterion is updated, delete its responses
        static::updated(function (ScreeningCriterion $criterion) {
            if ($criterion->wasChanged('criterion')) {
                $criterion->responses()->delete();
            }
        });
    }

    public function responses(): HasMany
    {
        return $this->hasMany(ScreeningCriterionResponse::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}
