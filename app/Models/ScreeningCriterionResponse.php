<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScreeningCriterionResponse extends TenantModel
{
    use HasFactory;

    protected $guarded = [];

    public function criterion(): BelongsTo
    {
        return $this->belongsTo(ScreeningCriterion::class, 'screening_criterion_id');
    }

    public function application()
    {
        return $this->belongsTo(Application::class);
    }
}
