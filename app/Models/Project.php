<?php

namespace App\Models;

use App\Models\Traits\HasCustomFields;
use App\Scopes\ProjectAccessScope;
use App\Scopes\RequisitionAccessScope;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection as BaseCollection;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

/**
 * Class Project
 *
 * @property-read Stage[]|EloquentCollection $stages
 * @property-read User[]|EloquentCollection $users
 * @property-read User $manager
 * @property string $position_name
 * @property bool $accessible_only_members
 * @property bool $current_user_is_subscribed
 * @property int $status
 * @property Carbon|null $deadline_at
 */
class Project extends TenantModel
{
    use HasCustomFields;
    use HasFactory;
    use SoftDeletes;

    protected $appends = [
        'status_string',
        'status_class',
        'status_short',
        'duration_days',
        'due_in_days',
        'due_in_diff',
    ];

    protected $dates = ['deadline_at', 'warranty_until', 'start_date', 'end_date'];

    protected $casts = [
        'accessible_only_members' => 'boolean',
        'custom_fields' => 'array',
        'status' => 'integer',
    ];

    const STATUS_DRAFT = 0;
    const STATUS_ON_HOLD = 1;
    const STATUS_IN_PROGRESS = 2;
    const STATUS_FINISHED = 3;
    const STATUS_FAILED = 4;

    // Define translations for each status
    public static function getStatuses()
    {
        return [
            self::STATUS_IN_PROGRESS => __('in progress'),
            self::STATUS_FINISHED => __('finished'),
            self::STATUS_FAILED => __('failed'),
            self::STATUS_ON_HOLD => __('on hold'),
            self::STATUS_DRAFT => __('draft'),
        ];
    }

    const STATUSES = [
        self::STATUS_IN_PROGRESS => 'in progress',
        self::STATUS_FINISHED => 'finished',
        self::STATUS_FAILED => 'failed',
        self::STATUS_ON_HOLD => 'on hold',
        self::STATUS_DRAFT => 'draft',
    ];

    const FINISHED_STATUSES = [
        self::STATUS_FINISHED,
        self::STATUS_FAILED,
    ];

    const STATUS_CLASSES = [
        self::STATUS_DRAFT => 'secondary',
        self::STATUS_ON_HOLD => 'warning',
        self::STATUS_IN_PROGRESS => 'success',
        self::STATUS_FINISHED => 'primary',
        self::STATUS_FAILED => 'danger',
    ];

    const PERPETUAL_GDPR_CONSENT_LENGTH_1M = '1 month';
    const PERPETUAL_GDPR_CONSENT_LENGTH_2M = '2 months';
    const PERPETUAL_GDPR_CONSENT_LENGTH_3M = '3 months';
    const PERPETUAL_GDPR_CONSENT_LENGTH_6M = '6 months';
    const PERPETUAL_GDPR_CONSENT_LENGTH_1Y = '1 year';
    const PERPETUAL_GDPR_CONSENT_LENGTHS = [
        self::PERPETUAL_GDPR_CONSENT_LENGTH_1M => '1 month',
        self::PERPETUAL_GDPR_CONSENT_LENGTH_2M => '2 months',
        self::PERPETUAL_GDPR_CONSENT_LENGTH_3M => '3 months',
        self::PERPETUAL_GDPR_CONSENT_LENGTH_6M => '6 months',
        self::PERPETUAL_GDPR_CONSENT_LENGTH_1Y => '1 year',
    ];

    public static function getPerpetualGdprConsentLengths()
    {
        return [
            self::PERPETUAL_GDPR_CONSENT_LENGTH_1M => __('1 month'),
            self::PERPETUAL_GDPR_CONSENT_LENGTH_2M => __('2 months'),
            self::PERPETUAL_GDPR_CONSENT_LENGTH_3M => __('3 months'),
            self::PERPETUAL_GDPR_CONSENT_LENGTH_6M => __('6 months'),
            self::PERPETUAL_GDPR_CONSENT_LENGTH_1Y => __('1 year'),
        ];
    }

    protected static function booted()
    {
        static::addGlobalScope(new ProjectAccessScope);

    }

    public static function removeAccessScope()
    {
        ProjectAccessScope::$disabled = true;
    }

    public static function restoreAccessScope()
    {
        ProjectAccessScope::$disabled = false;
    }

    public function archiveAssociatedImports()
    {
        $this
            ->stages
            ->pluck('imports')
            ->flatten()
            ->each(function (Import $import) {
                if (!in_array($import->integration->remote_type, [
                    Integration::TYPE_CV_KESKUS,
                    Integration::TYPE_CV_KESKUS_GMAIL,
                    Integration::TYPE_CV_KESKUS_OFFICE,
                    Integration::TYPE_CV_MARKET,
                    Integration::TYPE_CV_MARKET_GMAIL,
                    Integration::TYPE_CV_MARKET_OFFICE,
                    Integration::TYPE_CV_MARKET_LT,
                    Integration::TYPE_CV_MARKET_LT_GMAIL,
                    Integration::TYPE_CV_MARKET_LT_OFFICE,
                ])) {
                    return;
                }
                if (!$import->integration->cache) {
                    return;
                }
                $cache = $import->integration->cache;
                foreach ($cache['projects'] as &$project) {
                    $project = new \App\DataExchange\RemoteData\Project($project);
                    /** @var \App\DataExchange\RemoteData\Project $project */
                    if (isset($project->id) && $project->id == $import->remote_project_id) {
                        $project->archived = true;
                    }
                }
                $import->integration->cache = $cache;
                $import->integration->save();
            });
    }

    public function getDurationDaysAttribute()
    {
        /** @var Carbon $start */
        /** @var Carbon $end */
        $start = ($this->start_date ?? $this->created_at);
        $end = $this->end_date ?? Carbon::now();

        return $start->diffInDays($end);
    }

    public function getDueInDaysAttribute()
    {
        if ($this->deadline_at) {
            return $this->deadline_at->diffInDays();
        } else {
            return null;
        }
    }

    public function getDueInDiffAttribute()
    {
        if ($this->deadline_at) {
            return $this->deadline_at->diffForHumans(options: CarbonInterface::ROUND);
        } else {
            return null;
        }
    }

    public function getStatusStringAttribute()
    {
        if ($this->status === self::STATUS_FINISHED && $this->warranty_until) {
            $suffix = ' (w)';
        } else {
            $suffix = '';
        }

        return self::getStatuses()[$this->status] . $suffix . " ({$this->duration_days}d)";
    }

    public function getStatusShortAttribute()
    {
        if ($this->status === self::STATUS_FINISHED && $this->warranty_until) {
            $suffix = ' (w)';
        } else {
            $suffix = '';
        }

        return self::getStatuses()[$this->status] . $suffix;
    }

    public function getNpsScoreAttribute(): ?int
    {
        if (
            isset($this->attributes['survey_promoters_count'])
            && isset($this->attributes['survey_detractors_count'])
            && isset($this->attributes['survey_neutrals_count'])
        ) {
            return Survey::getNpsScoreFromCounts(
                $this->attributes['survey_promoters_count'],
                $this->attributes['survey_neutrals_count'],
                $this->attributes['survey_detractors_count']
            );
        }

        return Survey::calculateNps(Survey::query()->where('project_id', $this->id));
    }

    public function getNpsSentCountAttribute(): int
    {
        return Survey::query()
            ->where('project_id', $this->id)
            ->count();
    }

    public function getNpsResponseCountAttribute(): int
    {
        return Survey::query()
            ->where('project_id', $this->id)
            ->whereNotNull('response')
            ->count();
    }

    public function getStatusClassAttribute()
    {
        if ($this->status === self::STATUS_FINISHED && $this->warranty_until) {
            return 'info';
        }

        return self::STATUS_CLASSES[$this->status];
    }

    public function stages()
    {
        return $this->hasMany(Stage::class)->orderBy('sort_order');
    }

    public function firstStage()
    {
        return $this->hasOne(Stage::class)->ofMany([
            'sort_order' => 'min',
        ]);
    }

    public function candidates()
    {
        return $this->belongsToMany(Candidate::class, 'applications')->withPivot(['id']);
    }

    public function applications()
    {
        return $this->hasMany(Application::class);
    }

    public function applicationsPendingScreening()
    {
        return $this->applications()->whereHas('stage', function (Builder $q) {
            $q->where('category', Stage::CATEGORY_SUBMISSIONS);
        });
    }

    public function screeningCriteria()
    {
        return $this->hasMany(ScreeningCriterion::class);
    }

    public function client()
    {
        return $this->belongsTo(CrmOrganization::class, 'crm_organization_id');
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'project_manager_id');
    }

    public function users()
    {
        return $this->belongsToMany(User::class)
            ->where('active', true)
            ->using(ProjectUser::class)
            ->withPivot(ProjectUser::pivots());
    }

    public function projectUsers(): HasMany
    {
        return $this->hasMany(ProjectUser::class)->orderBy('id');
    }

    public function surveys()
    {
        return $this->hasMany(Survey::class);
    }

    public function surveyPromoters()
    {
        return $this->surveys()->whereIn('response', [9, 10]);
    }

    public function surveyDetractors()
    {
        return $this->surveys()->whereIn('response', [0, 1, 2, 3, 4, 5, 6]);
    }

    public function surveyNeutrals()
    {
        return $this->surveys()->whereIn('response', [7, 8]);
    }

    public function surveyResponses()
    {
        return $this->surveys()->whereNotNull('response');
    }

    public function requisitions()
    {
        return $this->hasMany(Requisition::class);
    }

    public function activitySubscriptions()
    {
        return $this->hasMany(ActivitySubscription::class);
    }

    public function structuredJobAds()
    {
        return $this->hasMany(StructuredJobAd::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     *
     * @deprecated
     */
    public function scorecard()
    {
        return $this->belongsTo(Scorecard::class);
    }

    public function scorecards()
    {
        return $this->belongsToMany(Scorecard::class);
    }

    public function locations()
    {
        return $this->belongsToMany(Location::class);
    }

    public function children()
    {
        return $this->hasMany(self::class, 'template_id');
    }

    public function template()
    {
        return $this->belongsTo(self::class, 'template_id');
    }

    public function eventSets(): HasMany
    {
        return $this->hasMany(EventSet::class);
    }

    public function scopeExcludeTemplates($query)
    {
        return $query->where('is_template', false);
    }

    public function projectActions()
    {
        return $this->hasMany(ProjectAction::class)->orderBy('id');
    }

    /**
     * @param  int[]  $projectIds
     */
    public static function getStageSummaries(array $projectIds = [], ?int $userId = null): array
    {
        if (!$userId) {
            $userId = auth()->id();
        }

        $projectIds[] = 0;
        $userId = (int) $userId;
        $projectIdString = implode(',', array_map(fn ($id) => (int) $id, $projectIds));

        $activityCounts = DB::connection('tenant')->select(<<<SQL
WITH candidates_in_projects as (
    SELECT candidate_id, project_id, stage_id
    FROM applications
        LEFT JOIN candidates ON applications.candidate_id = candidates.id
    WHERE project_id in ($projectIdString)
        AND candidates.deleted_at is null
        AND applications.deleted_at is null
), unseen_activities_in_projects as (
    SELECT activities.id, project_id, candidate_id FROM activities
        left join activity_receipts on activities.id = activity_receipts.activity_id
                                           and activity_receipts.user_id = $userId
        where project_id in ($projectIdString)
        and activity_receipts.id is null
        and (activities.user_id != $userId or activities.user_id is null)
), unseen_activity_counts_per_candidate_per_project as (
    select candidates_in_projects.candidate_id,
           candidates_in_projects.project_id,
           candidates_in_projects.stage_id,
           count(unseen_activities_in_projects.id) as unseen_activity_count
    from candidates_in_projects
    left join unseen_activities_in_projects on candidates_in_projects.candidate_id = unseen_activities_in_projects.candidate_id
                                                    and candidates_in_projects.project_id = unseen_activities_in_projects.project_id
    group by candidates_in_projects.candidate_id, candidates_in_projects.stage_id, candidates_in_projects.project_id
)
select stage_id,
       max(project_id) as project_id,
       count(candidate_id) as candidate_count,
       count(candidate_id) FILTER (WHERE unseen_activity_count > 0) as with_activity_count
from unseen_activity_counts_per_candidate_per_project
group by stage_id
SQL
        );

        return collect($activityCounts)
            ->keyBy('stage_id')
            ->toArray();
    }

    public function getCurrentUserIsSubscribedAttribute(): bool
    {
        return $this->activitySubscriptions()->where('user_id', auth()->id())->count() > 0;
    }

    public function setCurrentUserIsSubscribedAttribute(bool $v): void
    {
        if ($v) {
            ActivitySubscription::firstOrCreate([
                'project_id' => $this->id,
                'user_id' => auth()->id(),
            ]);
        } else {
            ActivitySubscription::where([
                'project_id' => $this->id,
                'user_id' => auth()->id(),
            ])->delete();
        }
    }

    public static function addCandidateRatingsQuery(Project $project, BelongsToMany $q): void
    {
        $avgQuery = Comment::query()
            ->where('comments.project_id', $project->id)
            ->whereRaw('comments.candidate_id = candidates.id')
            ->whereNotNull('comments.rating')
            ->select([\DB::raw('AVG(rating) as rating_avg')]);
        $countQuery = Comment::query()
            ->where('comments.project_id', $project->id)
            ->whereRaw('comments.candidate_id = candidates.id')
            ->whereNotNull('comments.rating')
            ->select([\DB::raw('COUNT(rating) as rating_count')]);
        $q->addSelect(['candidates.*']);
        $q->selectSub($avgQuery, 'rating_avg');
        $q->selectSub($countQuery, 'rating_count');
    }

    public function getStatusNoAttribute()
    {
        $currentUserId = auth()->id();

        if ($this->status === 2 && $this->project_manager_id === $currentUserId) {
            return 0;
        } elseif ($this->status === 2) {
            return 1;
        } elseif ($this->status === 1) {
            return 2;
        } elseif ($this->status === 0) {
            return 3;
        } elseif ($this->status === 3) {
            return 4;
        } else {
            return 99;
        }
    }

    public function logs()
    {
        return $this->hasMany(ProjectLog::class);
    }

    /**
     * @return array|int[]
     */
    public function getAllCandidateIds(): array
    {
        return $this->stages
            ->map(function (Stage $stage) {
                return $stage->candidates;
            })->flatten()->pluck('id')->toArray();
    }

    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    public function projectFailureReason(): BelongsTo
    {
        return $this->belongsTo(ProjectFailureReason::class);
    }

    public function getDisplayCustomFieldsAttribute()
    {
        return $this->toDisplayCustomFields(collect(Setting::get(Setting::KEY_PROJECT_CUSTOM_FIELDS)))
            ->toArray();
    }

    public function scopeWithMembers($query, int|array $userIds)
    {
        if (!is_array($userIds)) {
            $userIds = [$userIds];
        }

        return $query->where(function ($q) use ($userIds) {
            $q->whereIn('project_manager_id', $userIds)
                ->orWhereHas('users', function ($q2) use ($userIds) {
                    $q2->whereIn('users.id', $userIds);
                });
        });
    }

    public function landings()
    {
        return Landing::query()
            ->where(function ($q) {
                $q->whereHas('stage', function ($q2) {
                    $q2->where('project_id', $this->id);
                });
                $q->orWhereHas('structuredAds', function ($q2) {
                    $q2->where('project_id', $this->id);
                });
            })
            ->with([
                'structuredAds' => function ($q) {
                    $q->where('project_id', $this->id);
                },
                'tags',
            ]);
    }

    public function firstRequisition(): Attribute
    {
        return Attribute::get(fn () => $this->requisitions->first());
    }

    public function hasRequisition(): Attribute
    {
        return Attribute::get(
            fn () => $this->requisitions()->withoutGlobalScope(RequisitionAccessScope::class)->exists()
        );
    }

    public function isEmpty(): Attribute
    {
        return Attribute::get(function () {
            return $this->stages->sum('candidates_count') === 0;
        });
    }

    public function loadSharedRelationsForProjectView(): self
    {
        $this->append('nps_score');

        $data = $this
            ->load([
                'users',
                'requisitions',
                'stages',
                'manager',
            ])->loadCount([
                'structuredJobAds',
                'logs',
                'requisitions',
                'applicationsPendingScreening',
            ]);
        $data->users->each(function (User $user) {
            $user->pivot->load('roles');
        });

        return $data;
    }

    public function createProjectFromTemplate(array $attributes): static
    {
        abort_unless($this->is_template, Response::HTTP_INTERNAL_SERVER_ERROR); // Should never happen

        $newProject = $this->replicate()->fill([
            'is_template' => false,
            'template_id' => $this->id,
            'start_date' => now(),
            'status' => Project::STATUS_IN_PROGRESS,
            ...$attributes,
        ]);
        $newProject->save();

        $newProject->locations()->sync($this->locations);
        $newProject->scorecards()->sync($this->scorecards);
        $newProject->processProjectAfterCloningFromTemplate();

        return $newProject;
    }

    public function processProjectAfterCloningFromTemplate(): void
    {
        // This method assumes it's called on a non-template that belongs to a template
        abort_if($this->is_template, Response::HTTP_INTERNAL_SERVER_ERROR);
        abort_unless($this->template, Response::HTTP_INTERNAL_SERVER_ERROR);

        $stageMap = [];
        $actionPairs = [];

        foreach ($this->template->stages as $oldStage) {
            [$newStage, $newActionPairs] = $oldStage->clone(
                newProject: $this,
                linkToParent: true,
                cloneStageVisibility: true,
                cloneAutomaticActions: true,
            );

            $stageMap[$oldStage->id] = $newStage->id;
            $actionPairs = array_merge($actionPairs, $newActionPairs);
        }

        foreach ($actionPairs as $pair) {
            [$oldAction, $action] = $pair;

            // When cloning a moving action, replace the original target stage ID in the action
            // with the one from the new project
            if ($action->action_type === Action::TYPE_MOVE_TO_STAGE) {
                $actionData = $action->data;
                if (isset($actionData['target_stage_id'])) {
                    $originalTargetStageId = $actionData['target_stage_id'];

                    $newTargetStageId = $stageMap[$originalTargetStageId];
                    $actionData['target_stage_id'] = $newTargetStageId;
                    $action->data = $actionData;
                    $action->save();
                }
            }

            // When cloning an action with a message, replace the original sender with the new project manager
            if (in_array($action->action_type, [Action::TYPE_MESSAGE, Action::TYPE_VIDEO_MESSAGE, Action::TYPE_VIDEO_INTERVIEW])) {
                $actionData = $action->data;
                if (isset($actionData['user_id'])) {
                    $actionData['user_id'] = $this->project_manager_id;
                    $action->data = $actionData;
                    $action->save();
                }
            }
        }

        $this->template->projectActions()->each(function (ProjectAction $projectAction) {
            $action = $projectAction->clone($this, linkToParent: true);

            // When cloning an action with a message, replace the original sender with the new project manager
            if (in_array($action->action_type, [ProjectAction::TYPE_USER_MESSAGE])) {
                $actionData = $action->data;
                if (isset($actionData['user_id'])) {
                    $actionData['user_id'] = $this->project_manager_id;
                    $action->data = $actionData;
                    $action->save();
                }
            }
        });
    }

    /** @return BaseCollection<string, string> All custom field keys mapped to values as text */
    public function getAllCustomFieldAsTexts(): BaseCollection
    {
        return Setting::getProjectCustomFields()
            ->mapWithKeys(function (array $field) {
                $key = $field['key'];
                $value = data_get($this->custom_fields, $key);

                $textValue = match ($field['type']) {
                    'select' => collect($field['items'])
                        ->filter(fn (array $item) => $item['value'] === $value)
                        ->map(fn (array $item) => $item['label'])
                        ->first() ?? '-',
                    'tags' => collect($field['items'])
                        ->filter(fn (array $item) => BaseCollection::wrap($value)->contains($item['value']))
                        ->map(fn (array $item) => $item['label'])
                        ->implode(', ') ?: '-',
                    'checkbox' => match ($value) {
                        true, 1, '1', 'true' => 'Yes',
                        false, 0, '0', 'false' => 'No',
                        default => '-',
                    },
                    default => $value ?? '-'
                };

                return [$key => "$textValue"];
            });
    }
}
