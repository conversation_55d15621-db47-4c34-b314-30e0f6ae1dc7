<?php

namespace App\Console\Commands;

use App\Models\Website;
use Hyn\Tenancy\Contracts\Repositories\WebsiteRepository;
use Illuminate\Console\Command;
use Hyn\Tenancy\Database\Connection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

abstract class TenantAwareCommand extends Command
{
    public $websites;

    public $connection;

    public $website;

    protected $signature = 'example:command {--website_id=}';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->websites = app(WebsiteRepository::class);
        $this->connection = app(Connection::class);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $website_id = $this->option('website_id');
        try {
            $website = $this->websites->query()->where('id', $website_id)->firstOrFail();
            $environment = app(\Hyn\Tenancy\Environment::class);
            $environment->tenant($website);
            $this->connection->set($website);
            $this->info('Running Command on website_id: ' . $website_id);
            $this->website = $website;

        } catch (ModelNotFoundException $e) {
            throw new \RuntimeException(
                sprintf(
                    'The tenancy website_id=%d does not exist.',
                    $website_id
                )
            );
        }
        $this->tenantHandle();
        $this->connection->purge();
    }

    abstract public function tenantHandle();
}
