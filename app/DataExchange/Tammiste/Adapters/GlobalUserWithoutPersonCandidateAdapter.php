<?php

namespace App\DataExchange\Tammiste\Adapters;

use App\DataExchange\Tammiste\CandidateConstants;
use App\DataExchange\Tammiste\Contracts\CandidateLike;
use App\DataExchange\Tammiste\Contracts\ConsentLike;
use App\DataExchange\Tammiste\Contracts\EducationLike;
use App\DataExchange\Tammiste\Contracts\EmploymentLike;
use App\DataExchange\Tammiste\Contracts\ReferenceLike;
use App\DataExchange\Tammiste\Models\Cv;
use App\DataExchange\Tammiste\Models\CvContest;
use App\DataExchange\Tammiste\Models\CvFile;
use App\DataExchange\Tammiste\Models\GlobalUser;
use App\DataExchange\Tammiste\Models\PotentialCandidate;
use App\DataExchange\Tammiste\Models\SimpleFile;
use App\DataExchange\Tammiste\TammisteFileManager;
use App\Models\Setting;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Closure;
use Illuminate\Support\Collection;

readonly class GlobalUserWithoutPersonCandidateAdapter implements CandidateLike
{
    private TammisteFileManager $fileManager;

    public function __construct(private GlobalUser $globalUser)
    {
        $this->fileManager = app(TammisteFileManager::class);
    }

    public function getName(): string
    {
        $sources = $this->globalUser->getCvs()->sortByDesc(['modified', 'created'])->prepend($this->globalUser);

        $fullNameFromSameSource = $sources
            ->filter(fn(Cv|GlobalUser $source) => $source->first_name && $source->last_name)
            ->map(fn(Cv|GlobalUser $source) => "$source->first_name $source->last_name")
            ->first();

        $firstNameFromAnySource = $sources->pluck('first_name')->first(filled(...));
        $lastNameFromAnySource = $sources->pluck('first_name')->first(filled(...));
        $combinedNameFromAnySource = $firstNameFromAnySource && $lastNameFromAnySource
            ? "$firstNameFromAnySource $lastNameFromAnySource"
            : null;

        return $fullNameFromSameSource
                ?: $combinedNameFromAnySource
                    ?: $this->firstFromPotentialCandidates(fn(PotentialCandidate $candidate) => $candidate->candidate_name)
                        ?: '';
    }

    public function getBirthday(): ?CarbonInterface
    {
        return $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getBirthday());
    }

    public function getPhone(): ?string
    {
        return $this->globalUser->phone
            ?: $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getPhone())
                    ?: $this->firstFromPotentialCandidates(fn(PotentialCandidate $candidate) => $candidate->phone);
    }

    public function getEmail(): ?string
    {
        return $this->globalUser->email
            ?: $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getEmail())
                ?: $this->firstFromPotentialCandidates(fn(PotentialCandidate $candidate) => $candidate->phone);
    }

    public function getSkype(): ?string
    {
        return $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getSkype());
    }

    public function getCountry(): ?string
    {
        return $this->globalUser->country?->getEstonianName()
            ?: $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getCountry());
    }

    public function getCity(): ?string
    {
        return $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getCity());
    }


    public function getCustomFields(): ?array
    {
        $fields = [
            CandidateConstants::IDENTITY_CODE => $this->globalUser->identity_code,
            CandidateConstants::FACEBOOK => $this->firstFromCvs(fn(Cv $cv) => $cv->facebook),
            CandidateConstants::HOMEPAGE => $this->firstFromCvs(fn(Cv $cv) => $cv->homepage),

            CandidateConstants::PURPOSE => $this->firstFromCvs(fn(Cv $cv) => $cv->purpose),
            CandidateConstants::NOTES => $this->firstFromCvs(fn(Cv $cv) => $cv->notes),
            CandidateConstants::SUMMARY => $this->firstFromCvs(fn(Cv $cv) => $cv->summary),
            CandidateConstants::POSITION => $this->firstFromCvs(fn(Cv $cv) => $cv->position_custom),
            CandidateConstants::BACKGROUND => $this->firstFromCvs(fn(Cv $cv) => $cv->taustauuringud),
            CandidateConstants::VIDEO => $this->firstFromCvs(fn(Cv $cv) => $cv->video_url),
            CandidateConstants::TARGET_JOB => $this->firstFromCvs(fn(Cv $cv) => $cv->sobiv_too),
            CandidateConstants::MOTHER_TONGUE => $this->firstSlugFromCvs(fn(Cv $cv) => $cv->motherTongueLanguage?->getEstonianName()),
            CandidateConstants::GENDER => $this->firstSlugFromCvs(fn(Cv $cv) => $cv->gender?->getEstonianName()),

            CandidateConstants::WANTS_TO_WORK_ABROAD => $this->firstFromCvs(fn(Cv $cv) => $cv->want_to_work_abroad),
            CandidateConstants::CAN_USE_CAR => $this->firstFromCvs(fn(Cv $cv) => $cv->possible_to_use_car),
            CandidateConstants::DRIVING_LICENSE_B => $this->firstFromCvs(fn(Cv $cv) => $cv->driving_license_b),

            CandidateConstants::SALARY => $this->firstFromCvs(fn(Cv $cv) => self::cvConverter($cv)->getSalaryString()),
            CandidateConstants::SALARY_NOTES => $this->firstFromCvs(fn(Cv $cv) => $cv->salary_notes),
            CandidateConstants::SKILL_NOTES => $this->firstFromCvs(fn(Cv $cv) => $cv->skill_notes),
            CandidateConstants::STATUS_NOTES => $this->firstFromCvs(fn(Cv $cv) => $cv->status_notes),
            CandidateConstants::TESTS => $this->firstFromCvs(fn(Cv $cv) => $cv->testid),

            CandidateConstants::DRIVING_EXPERIENCE_IN_YEARS => $this->firstFromCvs(fn(Cv $cv) => $cv->driving_experince_in_years),
            CandidateConstants::DRIVING_LICENSE_START_YEAR => $this->firstFromCvs(fn(Cv $cv) => $cv->driving_license_start_year),

            CandidateConstants::DOMAINS => $this->getDomainSlugs(),
            CandidateConstants::JOB_KINDS => $this->getJobKindSlugs(),
            CandidateConstants::COUNTRIES => $this->getCountrySlugs(),
            CandidateConstants::LANGUAGES => $this->getLanguageSlugs(),
        ];

        return collect($fields)
            ->mapWithKeys(fn(mixed $value, string $key) => [self::slug($key) => $value])
            ->all();
    }

    public function getDomainSlugs(): array
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getDomainSlugs())
            ->filter()
            ->unique()
            ->all();
    }

    public function getReferences(): Collection
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getReferences())
            ->unique(fn(ReferenceLike $reference) => $reference->getEmail() ?: $reference->getName() ?: $reference->getPhone())
            ->values();
    }

    public function getEducations(): Collection
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getEducations())
            ->unique(fn(EducationLike $education) => ($education->getDegree() ?: $education->getInstitution()) . $education->getStartYear())
            ->values();
    }

    public function getEmployments(): Collection
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getEmployments())
            ->unique(fn(EmploymentLike $employment) => ($employment->getPositionTitle() ?: $employment->getEmployerName()) . $employment->getDateFrom()?->year)
            ->values();
    }

    public function getCreatedAt(): CarbonInterface
    {
        return $this->globalUser->getCvs()
            ->map(fn(Cv $cv) => self::cvConverter($cv)->getCreatedAt())
            ->concat([$this->globalUser->account_activation_hash_create_time, $this->globalUser->account_activation_time])
            ->filter(filled(...))
            ->map(fn($value) => Carbon::parse($value))
            ->min() ?? now();
    }

    private static function slug(?string $label): string
    {
        return Setting::convertCustomFieldLabelToSlug($label);
    }

    /**
     * @template T
     * @param Closure(Cv): T $mapper
     * @return T|null
     */
    private function firstFromCvs(Closure $mapper): mixed
    {
        return $this->globalUser->getCvs()
            ->sortByDesc(['modified', 'created'])
            ->map($mapper)
            ->filter(filled(...))
            ->first();
    }

    /**
     * @param Closure(Cv): mixed $mapper
     */
    private function firstSlugFromCvs(Closure $mapper): ?string
    {
        $value = $this->firstFromCvs($mapper);
        return $value ? self::slug($value) : null;
    }

    /**
     * @template T
     * @param Closure(PotentialCandidate): T $mapper
     * @return T|null
     */
    private function firstFromPotentialCandidates(Closure $mapper): mixed
    {
        return $this->globalUser->potentialCandidates
            ->sortByDesc(fn(PotentialCandidate $potentialCandidate) => $potentialCandidate->created)
            ->map($mapper)
            ->filter(filled(...))
            ->first();
    }

    public function getJobKindSlugs(): Collection
    {
        return $this->globalUser->getCvs()->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getJobKindSlugs());
    }

    public function getCountrySlugs(): Collection
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getCountrySlugs())
            ->when(
                $this->globalUser->country?->getEstonianName(),
                function (Collection $collection, string $estonianName) {
                    $collection->push(self::slug($estonianName));
                }
            )
            ->map(self::slug(...))
            ->unique()
            ->values();
    }

    public function getLanguageSlugs(): Collection
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => self::cvConverter($cv)->getLanguageSlugs())
            ->filter()
            ->unique()
            ->values();
    }

    private static function cvConverter(Cv $cv): CvWithoutPersonOrUserCandidateAdapter
    {
        return new CvWithoutPersonOrUserCandidateAdapter($cv);
    }

    /** @return Collection<int, CandidateStageAdapter> */
    public function getStages(): Collection
    {
        return $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => $cv->cvContests)
            ->concat($this->globalUser->potentialCandidates)
            ->filter(fn (CvContest|PotentialCandidate $model) => $model->contest)
            ->mapInto(CandidateStageAdapter::class)
            ->pipe(CandidateStageAdapter::mergeStages(...));
    }

    public function getComments(): Collection
    {
        $potentialCandidateComments = $this->globalUser->potentialCandidates
            ->mapInto(PotentialCandidateCommentAdapter::class)
            ->values();

        $cvContestComments = $this->globalUser->getCvs()
            ->flatMap(fn(Cv $cv) => $cv->cvContests)
            ->union($this->globalUser->cvContests)
            ->unique(fn (CvContest $cvContest) => $cvContest->cv_contest_id)
            ->mapInto(CvContestCommentAdapter::class)
            ->values();

        $separateCvComments = $this->globalUser->getCvs()
            ->map(self::cvConverter(...))
            ->flatMap(fn(CvWithoutPersonOrUserCandidateAdapter $adapter) => [
                ...$adapter->getNegativeFeedbackComments(),
                ...$adapter->getRefereeComments(),
            ])
            ->values();

        return $potentialCandidateComments
            ->concat($separateCvComments)
            ->concat($cvContestComments);
    }

    public function getFiles(): Collection
    {
        $userFiles = $this->globalUser->cvFiles;
        $cvFiles = $this->globalUser->getCvs()->flatMap(fn(Cv $cv) => $cv->cvFiles);
        $photoSimpleFiles = $this->globalUser->getCvs()->flatMap(fn(Cv $cv) => $cv->photoFiles);
        $potentialCandidateSimpleFiles = $this->globalUser->potentialCandidates
            ->flatMap(fn(PotentialCandidate $potentialCandidate) => $potentialCandidate->pivotSimpleFiles);
        $potentialCandidateCvFiles = $this->globalUser->potentialCandidates
            ->map(fn(PotentialCandidate $potentialCandidate) => $potentialCandidate->cvSimpleFile)
            ->filter();

        return collect($userFiles)
            ->concat($cvFiles)
            ->concat($photoSimpleFiles)
            ->concat($potentialCandidateSimpleFiles)
            ->concat($potentialCandidateCvFiles)
            ->unique(fn(SimpleFile|CvFile $file) => match (get_class($file)) {
                SimpleFile::class => $file->file_id,
                CvFile::class => $file->simple_file_id,
            })
            ->filter($this->fileManager->isFileRealAndRecord(...))
            ->map(fn(SimpleFile|CvFile $file) => match (get_class($file)) {
                SimpleFile::class => new SimpleFileFileAdapter($file),
                CvFile::class => new CvFileFileAdapter($file)
            });
    }

    public function getConsent(): ?ConsentLike
    {
        return $this->globalUser->keep_personal_data ? new ConsentAdapter($this->globalUser) : null;
    }
}
