# App\DataExchange\BrowserUse\Api\lib\APIV10Api

All URIs are relative to https://api.browser-use.com, except if the operation defines another base path.

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**checkBalanceApiV1BalanceGet()**](APIV10Api.md#checkBalanceApiV1BalanceGet) | **GET** /api/v1/balance | Check Balance |
| [**deleteBrowserProfileForUserApiV1DeleteBrowserProfileForUserPost()**](APIV10Api.md#deleteBrowserProfileForUserApiV1DeleteBrowserProfileForUserPost) | **POST** /api/v1/delete-browser-profile-for-user | Delete Browser Profile For User |
| [**getTaskApiV1TaskTaskIdGet()**](APIV10Api.md#getTaskApiV1TaskTaskIdGet) | **GET** /api/v1/task/{task_id} | Get Task |
| [**getTaskGifApiV1TaskTaskIdGifGet()**](APIV10Api.md#getTaskGifApiV1TaskTaskIdGifGet) | **GET** /api/v1/task/{task_id}/gif | Get Task Gif |
| [**getTaskMediaApiV1TaskTaskIdMediaGet()**](APIV10Api.md#getTaskMediaApiV1TaskTaskIdMediaGet) | **GET** /api/v1/task/{task_id}/media | Get Task Media |
| [**getTaskScreenshotsApiV1TaskTaskIdScreenshotsGet()**](APIV10Api.md#getTaskScreenshotsApiV1TaskTaskIdScreenshotsGet) | **GET** /api/v1/task/{task_id}/screenshots | Get Task Screenshots |
| [**getTaskStatusApiV1TaskTaskIdStatusGet()**](APIV10Api.md#getTaskStatusApiV1TaskTaskIdStatusGet) | **GET** /api/v1/task/{task_id}/status | Get Task Status |
| [**listTasksApiV1TasksGet()**](APIV10Api.md#listTasksApiV1TasksGet) | **GET** /api/v1/tasks | List Tasks |
| [**meApiV1MeGet()**](APIV10Api.md#meApiV1MeGet) | **GET** /api/v1/me | Me |
| [**pauseTaskApiV1PauseTaskPut()**](APIV10Api.md#pauseTaskApiV1PauseTaskPut) | **PUT** /api/v1/pause-task | Pause Task |
| [**pingApiV1PingGet()**](APIV10Api.md#pingApiV1PingGet) | **GET** /api/v1/ping | Ping |
| [**resumeTaskApiV1ResumeTaskPut()**](APIV10Api.md#resumeTaskApiV1ResumeTaskPut) | **PUT** /api/v1/resume-task | Resume Task |
| [**runTaskApiV1RunTaskPost()**](APIV10Api.md#runTaskApiV1RunTaskPost) | **POST** /api/v1/run-task | Run Task |
| [**stopTaskApiV1StopTaskPut()**](APIV10Api.md#stopTaskApiV1StopTaskPut) | **PUT** /api/v1/stop-task | Stop Task |


## `checkBalanceApiV1BalanceGet()`

```php
checkBalanceApiV1BalanceGet(): \App\DataExchange\BrowserUse\Api\lib\Model\CheckUserBalanceResponse
```

Check Balance

Returns the user's current API credit balance, which includes both monthly subscription credits and any additional purchased credits. Required for monitoring usage and ensuring sufficient credits for task execution.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);

try {
    $result = $apiInstance->checkBalanceApiV1BalanceGet();
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->checkBalanceApiV1BalanceGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

This endpoint does not need any parameter.

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\CheckUserBalanceResponse**](../Model/CheckUserBalanceResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `deleteBrowserProfileForUserApiV1DeleteBrowserProfileForUserPost()`

```php
deleteBrowserProfileForUserApiV1DeleteBrowserProfileForUserPost(): mixed
```

Delete Browser Profile For User

Deletes the browser profile for the user.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);

try {
    $result = $apiInstance->deleteBrowserProfileForUserApiV1DeleteBrowserProfileForUserPost();
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->deleteBrowserProfileForUserApiV1DeleteBrowserProfileForUserPost: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

This endpoint does not need any parameter.

### Return type

**mixed**

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `getTaskApiV1TaskTaskIdGet()`

```php
getTaskApiV1TaskTaskIdGet($task_id): \App\DataExchange\BrowserUse\Api\lib\Model\TaskResponse
```

Get Task

Returns comprehensive information about a task, including its current status, steps completed, output (if finished), and other metadata.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->getTaskApiV1TaskTaskIdGet($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->getTaskApiV1TaskTaskIdGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\TaskResponse**](../Model/TaskResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `getTaskGifApiV1TaskTaskIdGifGet()`

```php
getTaskGifApiV1TaskTaskIdGifGet($task_id): \App\DataExchange\BrowserUse\Api\lib\Model\TaskGifResponse
```

Get Task Gif

Returns a gif url generated from the screenshots of the task execution. Only available for completed tasks that have screenshots.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->getTaskGifApiV1TaskTaskIdGifGet($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->getTaskGifApiV1TaskTaskIdGifGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\TaskGifResponse**](../Model/TaskGifResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `getTaskMediaApiV1TaskTaskIdMediaGet()`

```php
getTaskMediaApiV1TaskTaskIdMediaGet($task_id): \App\DataExchange\BrowserUse\Api\lib\Model\TaskMediaResponse
```

Get Task Media

Returns links to any recordings or media generated during task execution, such as browser session recordings. Only available for completed tasks.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->getTaskMediaApiV1TaskTaskIdMediaGet($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->getTaskMediaApiV1TaskTaskIdMediaGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\TaskMediaResponse**](../Model/TaskMediaResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `getTaskScreenshotsApiV1TaskTaskIdScreenshotsGet()`

```php
getTaskScreenshotsApiV1TaskTaskIdScreenshotsGet($task_id): \App\DataExchange\BrowserUse\Api\lib\Model\TaskScreenshotsResponse
```

Get Task Screenshots

Returns any screenshot urls generated during task execution.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->getTaskScreenshotsApiV1TaskTaskIdScreenshotsGet($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->getTaskScreenshotsApiV1TaskTaskIdScreenshotsGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\TaskScreenshotsResponse**](../Model/TaskScreenshotsResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `getTaskStatusApiV1TaskTaskIdStatusGet()`

```php
getTaskStatusApiV1TaskTaskIdStatusGet($task_id): \App\DataExchange\BrowserUse\Api\lib\Model\TaskStatusEnum
```

Get Task Status

Returns just the current status of a task (created, running, finished, stopped, or paused). More lightweight than the full task details endpoint.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->getTaskStatusApiV1TaskTaskIdStatusGet($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->getTaskStatusApiV1TaskTaskIdStatusGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\TaskStatusEnum**](../Model/TaskStatusEnum.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `listTasksApiV1TasksGet()`

```php
listTasksApiV1TasksGet($page, $limit): \App\DataExchange\BrowserUse\Api\lib\Model\ListTasksResponse
```

List Tasks

Returns a paginated list of all tasks belonging to the user, ordered by creation date. Each task includes basic information like status and creation time. For detailed task info, use the get task endpoint.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$page = 1; // int
$limit = 10; // int

try {
    $result = $apiInstance->listTasksApiV1TasksGet($page, $limit);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->listTasksApiV1TasksGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **page** | **int**|  | [optional] [default to 1] |
| **limit** | **int**|  | [optional] [default to 10] |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\ListTasksResponse**](../Model/ListTasksResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `meApiV1MeGet()`

```php
meApiV1MeGet(): bool
```

Me

Returns a boolean value indicating if the API key is valid and the user is authenticated.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);

try {
    $result = $apiInstance->meApiV1MeGet();
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->meApiV1MeGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

This endpoint does not need any parameter.

### Return type

**bool**

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `pauseTaskApiV1PauseTaskPut()`

```php
pauseTaskApiV1PauseTaskPut($task_id): mixed
```

Pause Task

Pauses execution of a running task. The task can be resumed later using the `/resume-task` endpoint. Useful for manual intervention or inspection.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->pauseTaskApiV1PauseTaskPut($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->pauseTaskApiV1PauseTaskPut: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

**mixed**

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `pingApiV1PingGet()`

```php
pingApiV1PingGet(): mixed
```

Ping

Use this endpoint to check if the server is running and responding.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');



$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client()
);

try {
    $result = $apiInstance->pingApiV1PingGet();
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->pingApiV1PingGet: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

This endpoint does not need any parameter.

### Return type

**mixed**

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `resumeTaskApiV1ResumeTaskPut()`

```php
resumeTaskApiV1ResumeTaskPut($task_id): mixed
```

Resume Task

Resumes execution of a previously paused task. The task will continue from where it was paused. You can't resume a stopped task.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->resumeTaskApiV1ResumeTaskPut($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->resumeTaskApiV1ResumeTaskPut: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

**mixed**

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `runTaskApiV1RunTaskPost()`

```php
runTaskApiV1RunTaskPost($run_task_request): \App\DataExchange\BrowserUse\Api\lib\Model\TaskCreatedResponse
```

Run Task

Requires an active subscription. Returns the task ID that can be used to track progress.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$run_task_request = new \App\DataExchange\BrowserUse\Api\lib\Model\RunTaskRequest(); // \App\DataExchange\BrowserUse\Api\lib\Model\RunTaskRequest

try {
    $result = $apiInstance->runTaskApiV1RunTaskPost($run_task_request);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->runTaskApiV1RunTaskPost: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **run_task_request** | [**\App\DataExchange\BrowserUse\Api\lib\Model\RunTaskRequest**](../Model/RunTaskRequest.md)|  | |

### Return type

[**\App\DataExchange\BrowserUse\Api\lib\Model\TaskCreatedResponse**](../Model/TaskCreatedResponse.md)

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: `application/json`
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)

## `stopTaskApiV1StopTaskPut()`

```php
stopTaskApiV1StopTaskPut($task_id): mixed
```

Stop Task

Stops a running browser automation task immediately. The task cannot be resumed after being stopped. Use `/pause-task` endpoint instead if you want to temporarily halt execution.

### Example

```php
<?php
require_once(__DIR__ . '/vendor/autoload.php');


// Configure Bearer authorization: HTTPBearer
$config = App\DataExchange\BrowserUse\Api\lib\Configuration::getDefaultConfiguration()->setAccessToken('YOUR_ACCESS_TOKEN');


$apiInstance = new App\DataExchange\BrowserUse\Api\lib\Api\APIV10Api(
    // If you want use custom http client, pass your client which implements `GuzzleHttp\ClientInterface`.
    // This is optional, `GuzzleHttp\Client` will be used as default.
    new GuzzleHttp\Client(),
    $config
);
$task_id = 'task_id_example'; // string

try {
    $result = $apiInstance->stopTaskApiV1StopTaskPut($task_id);
    print_r($result);
} catch (Exception $e) {
    echo 'Exception when calling APIV10Api->stopTaskApiV1StopTaskPut: ', $e->getMessage(), PHP_EOL;
}
```

### Parameters

| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **task_id** | **string**|  | |

### Return type

**mixed**

### Authorization

[HTTPBearer](../../README.md#HTTPBearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: `application/json`

[[Back to top]](#) [[Back to API list]](../../README.md#endpoints)
[[Back to Model list]](../../README.md#models)
[[Back to README]](../../README.md)
