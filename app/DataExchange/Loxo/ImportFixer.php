<?php

namespace App\DataExchange\Loxo;

use App\Classes\Files\JSONLReader;
use App\Classes\Files\ZipExtractor;
use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Candidate;
use App\Models\Comment;
use App\Models\CrmOrganization;
use App\Models\File;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Stage;
use App\Models\Tag;
use App\Models\User;
use App\Models\Website;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportFixer
{
    public Website $website;
    public string $rootDirPath;
    public bool $memoryLogging = true;
    public bool $timeLogging = true;
    public bool $alreadyImportedLogging = false;

    const string SYSTEM_USER_EMAIL = '<EMAIL>';

    const int CACHE_DURATION = 60 * 60 * 24 * 90; // 90 days

    const array FIX_CANDIDATE_IDS = [
        48750197,
        48750202,
        48750226,
        48750228,
        48750231,
        48750241,
        48750250,
        48750255,
        48750258,
        48750265,
        48750273,
        48750279,
        48750283,
        48750288,
        48750290,
        48750293,
        48750297,
        48750308,
        48750311,
        48750317,
        48750320,
        48750324,
        48750331,
        48750332,
        48750343,
        48750345,
        48750348,
        48750353,
        48750774,
        48750775,
        48750776,
        48750782,
        48750783,
        48750786,
        48750792,
        48750793,
        48750889,
        48751310,
        48751369,
        48751371,
        48751372,
        48751379,
        48751385,
        48751391,
        48751393,
        48751394,
        48752689,
        48753417,
        48753422,
        48753431,
        48753434,
        48753442,
        48753451,
        48753467,
        48753480,
        48753483,
        48753492,
        48753498,
        48753502,
        48753506,
        48753509,
        48753511,
        48753884,
        48753888,
        48753894,
        48753898,
        48753902,
        48753905,
        48753906,
        48753909,
        48753911,
        48753917,
        48753920,
        48753929,
        48753945,
        48753946,
        48753952,
        48753957,
        48753964,
        48753966,
        48753968,
        48753976,
        48753977,
        48753979,
        48754007,
        48754023,
        48754026,
        48754057,
        48754062,
        48754420,
        48754504,
        48754511,
        48754518,
        48754531,
        48754538,
        48754543,
        48754563,
        48754591,
        48754597,
        48754608,
        48754642,
        48754660,
        48754665,
        48754681,
        48754693,
        48754709,
        48754713,
        48754720,
        48754737,
        48754754,
        48754797,
        48754802,
        48755261,
        48755264,
        48755273,
        48755288,
        48755292,
        48755296,
        48755298,
        48755304,
        48755313,
        48755323,
        48755324,
        48755326,
        48755327,
        48755338,
        48755341,
        48755344,
        48755591,
        48755599,
        48755601,
        48755603,
        48755608,
        48755611,
        48755615,
        48755627,
        48755639,
        48755660,
        48755664,
        48755668,
        48755671,
        48755684,
        48755687,
        48755705,
        48755708,
        48755715,
        48755739,
        48755743,
        48757241,
        48757256,
        48757259,
        48757273,
        48757276,
        48757279,
        48757280,
        48757282,
        48757284,
        48758376,
        48758386,
        48758396,
        48758407,
        48758447,
        48758463,
        48758475,
        48758478,
        48758480,
        48758482,
        48758484,
        48758503,
        48758509,
        48758512,
        48758513,
        48759337,
        48798859,
        48798869,
        48798873,
        48798889,
        48798972,
        48799158,
        48799227,
        48799233,
        48799246,
        48799272,
        48799283,
        48799286,
        48799288,
        48799289,
        48799292,
        48799294,
        48799308,
        48799310,
        48799320,
        48799323,
        48799324,
        48799325,
        48799326,
        48799332,
        48799335,
        48799337,
        48799338,
        48799340,
        48799342,
        48799344,
        48799411,
        48799415,
        48799418,
        48799425,
        48799427,
        48799432,
        48799433,
        48799436,
        48799445,
        48799447,
        48799451,
        48799453,
        48799465,
        48799468,
        48799469,
        48799470,
        48799471,
        48799473,
        48799474,
        48799701,
        48799702,
        48799711,
        48799714,
        48799719,
        48799727,
        48799728,
        48799732,
        48799735,
        48799736,
        48799737,
        48799740,
        48799741,
        48799747,
        48799752,
        48799761,
        48799765,
        48799768,
        48799777,
        48799878,
        48799885,
        48799898,
        48799905,
        48799909,
        48799913,
        48799915,
        48799918,
        48799924,
        48799928,
        48799931,
        48799934,
        48799942,
        48799946,
        48799962,
        48799963,
        48799967,
        48799969,
        48799970,
        48799971,
        48799972,
        48799974,
        48799975,
        48799976,
        48799981,
        48799990,
        48799991,
        48799992,
        48799994,
        48799997,
        48799998,
        48800005,
        48800012,
        48800015,
        48800020,
        48800022,
        48800026,
        48800035,
        48800037,
        48800039,
        48800044,
        48800049,
        48800050,
        48800055,
        48800057,
        48800060,
        48800066,
        48800067,
        48800068,
        48800072,
        48800076,
        48800082,
        48800084,
        48800092,
        48800105,
        48800108,
        48800113,
        48800116,
        48800117,
        48805037,
        48806339,
        48808859,
        48809369,
        48809889,
        48810127,
        48810231,
        48817673,
        48819389,
        48820969,
        48822254,
        48913669,
        48913837,
        48913860,
        48914258,
        48914265,
        48914292,
        48914478,
        48914485,
        48914497,
        48914502,
        48914515,
        48914521,
        48914554,
        48914579,
        48915530,
        48915536,
        48915558,
        48915581,
        48915654,
        48915955,
        48915959,
        48915960,
        48915961,
        48915962,
        48915973,
        48915983,
        48915999,
        48916009,
        48916014,
        48916016,
        48916020,
        48916088,
        48916091,
        48916098,
        48916108,
        48916109,
        48916114,
        48916120,
        48916121,
        48916122,
        48916142,
        48916144,
        48916151,
        48916152,
        48916158,
        48916160,
        48916166,
        48916171,
        48916235,
        48916236,
        48916238,
        48916241,
        48916245,
        48916246,
        48916248,
        48916255,
        48916259,
        48916260,
        48916262,
        48916267,
        48916271,
        48916273,
        48916277,
        48916280,
        48916281,
        48916282,
        48916283,
        48916284,
        48916292,
        48916293,
        48916295,
        48916302,
        48916312,
        48916516,
        48916585,
        48916589,
        48916592,
        48916597,
        48916606,
        48916610,
        48916620,
        48916623,
        48916634,
        48916635,
        48916637,
        48916640,
        48916641,
        48916645,
        48916649,
        48916650,
        48916652,
        48916654,
        48916655,
        48916728,
        48916733,
        48916737,
        48916739,
        48916740,
        48916745,
        48916753,
        48916759,
        48916761,
        48916767,
        48916770,
        48916774,
        48916782,
        48916783,
        48916796,
        48916797,
        48916801,
        48916802,
        48916804,
        48916805,
        48916807,
        48916809,
        48916816,
        48916824,
        48916828,
        48916829,
        48916830,
        48916833,
        48916834,
        48916838,
        48916845,
        48916847,
        48916848,
        48916853,
        48917353,
        48917356,
        48917364,
        48917365,
        48917366,
        48917372,
        48917380,
        48917383,
        48917397,
        48917404,
        48917417,
        48917419,
        48917536,
        48917538,
        48917542,
        48917547,
        48917550,
        48917560,
        48917563,
        48917565,
        48917581,
        48917595,
        48917599,
        48917604,
        48917606,
        48917608,
        48917628,
        48917631,
        48917633,
        48917639,
        48917653,
        48917655,
        48917883,
        48917896,
        48917898,
        48917912,
        48917914,
        48917927,
        48917937,
        48918172,
        48918177,
        48918182,
        48918184,
        48918189,
        48918190,
        48918201,
        48918202,
        48918204,
        48918211,
        48918214,
        48918217,
        48918219,
        48918220,
        48918241,
        48918530,
        48918546,
        48918549,
        48918556,
        48918567,
        48918573,
        48918575,
        48918583,
        48918589,
        48918592,
        48918594,
        48918602,
        48918610,
        48918614,
        48918617,
        48918619,
        48918626,
        48918629,
        48918634,
        48918637,
        48918639,
        48918641,
        48918644,
        48918647,
        48918658,
        48918676,
        48918681,
        48918691,
        48918695,
        48918702,
        48918709,
        48918728,
        48918731,
        48918739,
        48918741,
        48918748,
        48918754,
        48918760,
        48918767,
        48918771,
        48918785,
        48918788,
        48918790,
        48918797,
        48918806,
        48918811,
        48918817,
        48918824,
        48919776,
        48919780,
        48919783,
        48919785,
        48919792,
        48919811,
        48919812,
        48919814,
        48919820,
        48919826,
        48919833,
        48919838,
        48919846,
        48919850,
        48919856,
        48919859,
        48919862,
        48919864,
        48919869,
        48919875,
        48919880,
        48919882,
        48919884,
        48919899,
        48919909,
        48919911,
        48919926,
        48919930,
        48919957,
        48919965,
        48919977,
        48920000,
        48920002,
        48920004,
        48920008,
        48920039,
        48920040,
        48920053,
        48920054,
        48920079,
        48920084,
        48920087,
        48920089,
        48920109,
        48920115,
        48920129,
        48920138,
        48920146,
        48920154,
        48920169,
        48920190,
        48920198,
        48920203,
        48920209,
        48920230,
        48920236,
        48920244,
        48920246,
        48920248,
        48920265,
        48920297,
        48920307,
        48920311,
        48920319,
        48920325,
        48920399,
        48920410,
        48920416,
        48920420,
        48920421,
        48920423,
        48920426,
        48920673,
        48920676,
        48920686,
        48920687,
        48920690,
        48920693,
        48920695,
        48920696,
        48920698,
        48920700,
        48920703,
        48920708,
        48920715,
        48920722,
        48920723,
        48924076,
        48924589,
        48924620,
        48930667,
        48933821,
        48939291,
        48940218,
        48942300,
        48944771,
        48997924,
        48998445,
        48998503,
        48998507,
        48998508,
        48998509,
        48998511,
        48998513,
        48998518,
        48998522,
        48998535,
        48998536,
        48998538,
        48998540,
        48998541,
        48998544,
        48998547,
        48998553,
        48998582,
        48998585,
        48998590,
        48998597,
        48998631,
        48998632,
        48998634,
        48998639,
        48998641,
        48998642,
        48998643,
        48998646,
        48998648,
        48998653,
        48998657,
        48998667,
        48998669,
        48998673,
        48998677,
        48998684,
        48998691,
        48998694,
        48998695,
        48998697,
        48998749,
        48998752,
        48998754,
        48998762,
        48998778,
        48998787,
        48998790,
        48998796,
        48998797,
        48998799,
        48998807,
        48998809,
        48998810,
        48998812,
        48998817,
        48998818,
        48998832,
        48998844,
        48998848,
        48998853,
        48998857,
        48998858,
        48998859,
        48998861,
        48998865,
        48998872,
        48998883,
        48998913,
        48998916,
        48998922,
        48998928,
        48998937,
        48999019,
        48999021,
        48999027,
        48999028,
        48999034,
        48999039,
        48999043,
        48999046,
        48999135,
        48999167,
        48999168,
        48999169,
        48999170,
        48999172,
        48999244,
        48999245,
        48999246,
        48999262,
        48999292,
        48999307,
        48999310,
        48999320,
        48999321,
        48999339,
        48999340,
        48999342,
        48999344,
        48999346,
        48999347,
        48999351,
        48999352,
        48999353,
        48999358,
        48999360,
        48999363,
        48999366,
        48999367,
        48999376,
        48999378,
        48999389,
        48999394,
        48999398,
        48999400,
        48999405,
        48999415,
        48999416,
        48999429,
        48999438,
        48999439,
        48999458,
        48999473,
        48999481,
        48999503,
        48999504,
        48999512,
        48999515,
        48999516,
        48999521,
        48999522,
        48999526,
        48999529,
        48999530,
        48999532,
        48999540,
        48999577,
        48999586,
        48999588,
        48999589,
        48999591,
        48999600,
        48999605,
        48999607,
        48999609,
        48999614,
        48999636,
        48999638,
        48999645,
        48999649,
        48999672,
        48999675,
        48999677,
        48999679,
        48999681,
        48999682,
        48999683,
        48999684,
        48999689,
        48999691,
        48999693,
        48999694,
        48999721,
        48999745,
        48999747,
        48999753,
        48999757,
        48999762,
        48999771,
        48999775,
        48999782,
        48999785,
        48999790,
        48999796,
        48999798,
        48999802,
        48999804,
        48999807,
        48999808,
        48999809,
        48999810,
        48999813,
        48999830,
        48999832,
        48999836,
        48999841,
        48999849,
        48999850,
        48999853,
        48999856,
        48999887,
        48999889,
        48999971,
        48999974,
        48999981,
        48999986,
        49000001,
        49000003,
        49000020,
        49000701,
        49000857,
        49001141,
        49001805,
        49001859,
        49001904,
        49002013,
        49002121,
        49002206,
        49002465,
        49002758,
        49002815,
        49002910,
        49003010,
        49003051,
        49003146,
        49003165,
        49003199,
        49003227,
        49003251,
        49003301,
        49003381,
        49003452,
        49003489,
        49003498,
        49003517,
        49003603,
        49003676,
        49003688,
        49003692,
        49003710,
        49003733,
        49003738,
        49003743,
        49003773,
        49003895,
        49004066,
        49004137,
        49004366,
        49004437,
        49004441,
        49004493,
        49004507,
        49004520,
        49004551,
        49004591,
        49004613,
        49004630,
        49004634,
        49004655,
        49004688,
        49004879,
        49005136,
        49005220,
        49005227,
        49005259,
        49005350,
        49005429,
        49005577,
        49020418,
        49020701,
        49021068,
        49021376,
        49021905,
        49022261,
        49022410,
        49022738,
        49023087,
        49023365,
        49025228,
        49025744,
        49026917,
        49026978,
        49042000,
        49042006,
        49042016,
        49042027,
        49042058,
        49042068,
        49043188,
        49043791,
        49044033,
        49044063,
        49044072,
        49044111,
        49044459,
        49044500,
        49044503,
        49044508,
        49044515,
        49044522,
        49044525,
        49044530,
        49044536,
        49044538,
        49044541,
        49044542,
        49044543,
        49044545,
        49044548,
        49044560,
        49044561,
        49044562,
        49044563,
        49044564,
        49044571,
        49044577,
        49044578,
        49044580,
        49044582,
        49044583,
        49044584,
        49044585,
        49044586,
        49044587,
        49044591,
        49044598,
        49044608,
        49044612,
        49044613,
        49044733,
        49044744,
        49044749,
        49044757,
        49044761,
        49044763,
        49044766,
        49044776,
        49044779,
        49044847,
        49044849,
        49044862,
        49044866,
        49044868,
        49044877,
        49044879,
        49044882,
        49044885,
        49044890,
        49044891,
        49044892,
        49044895,
        49044898,
        49044900,
        49044901,
        49044906,
        49044907,
        49044914,
        49044919,
        49044921,
        49044923,
        49044927,
        49044951,
        49044984,
        49045004,
        49045013,
        49045016,
        49045017,
        49045020,
        49045021,
        49045026,
        49045027,
        49045033,
        49045034,
        49045035,
        49045042,
        49045043,
        49045044,
        49045045,
        49045047,
        49045052,
        49045070,
        49045080,
        49045083,
        49045085,
        49045086,
        49045087,
        49045092,
        49045093,
        49045094,
        49045099,
        49045100,
        49045103,
        49045109,
        49045111,
        49045113,
        49045119,
        49045120,
        49045121,
        49045123,
        49045124,
        49045126,
        49045130,
        49045133,
        49045136,
        49045137,
        49045138,
        49045139,
        49045140,
        49045141,
        49045142,
        49045154,
        49045155,
        49045156,
        49045157,
        49045158,
        49045166,
        49045169,
        49045177,
        49045188,
        49045361,
        49045504,
        49045822,
        49045976,
        49046214,
        49046338,
        49046367,
        49046383,
        49046428,
        49046448,
        49046459,
        49046465,
        49046477,
        49046488,
        49046526,
        49046570,
        49046614,
        49046635,
        49046695,
        49046738,
        49046869,
        49046932,
        49047014,
        49047145,
        49047638,
        49047666,
        49047978,
        49048057,
        49052934,
        49053001,
        49053291,
        49053304,
        49076069,
        49076832,
        49076861,
        49077131,
        49077343,
        49077347,
        49081282,
        49081539,
        49081602,
        49087712,
        49087766,
        49087810,
        49087872,
        49087875,
        49087876,
        49087879,
        49087883,
        49087897,
        49087991,
        49087999,
        49088003,
        49088017,
        49088018,
        49088303,
        49088325,
        49088329,
        49089051,
        49089115,
        49089132,
        49089152,
        49089160,
        49089180,
        49089190,
        49089201,
        49089224,
        49089244,
        49089264,
        49091237,
        49091495,
        49091611,
        49091631,
        49091715,
        49091756,
        49091771,
        49091787,
        49092311,
        49092863,
        49093103,
        49093246,
        49093457,
        49093704,
        49094055,
        49094753,
        49096432,
        49097557,
        49098219,
        49098575,
        49098601,
        49098987,
        49100956,
        49120075,
        49120197,
        49120446,
        49120665,
        49122021,
        49122042,
        49122060,
        49122070,
        49122089,
        49122175,
        49122176,
        49122177,
        49122181,
        49122183,
        49122184,
        49122185,
        49122190,
        49122192,
        49122194,
        49122198,
        49122201,
        49122202,
        49122204,
        49122247,
        49122252,
        49122256,
        49122257,
        49122258,
        49122259,
        49122260,
        49122261,
        49122264,
        49122265,
        49122266,
        49122267,
        49122268,
        49122270,
        49122273,
        49122274,
        49122275,
        49122277,
        49122280,
        49122291,
        49122292,
        49122293,
        49122301,
        49122302,
        49122303,
        49122304,
        49122432,
        49122439,
        49122440,
        49122452,
        49122461,
        49122468,
        49122486,
        49122487,
        49122634,
        49122667,
        49123020,
        49123021,
        49123033,
        49123037,
        49123039,
        49123045,
        49123068,
        49123123,
        49123126,
        49123130,
        49123140,
        49123143,
        49123149,
        49123181,
        49123257,
        49123289,
        49124453,
        49124546,
        49124696,
        49124710,
        49124763,
        49124876,
        49125166,
        49127513,
        49127550,
        49127565,
        49127583,
        49127924,
        49127941,
        49128108,
        49128192,
        49128213,
        49128269,
        49128306,
        49128322,
        49174793,
        49188269,
        49188662,
        49188744,
        49188995,
        49189075,
        49189194,
        49189279,
        49189355,
        49189853,
        49190016,
        49196213,
        49196559,
        49198181,
        49198330,
        49199958,
        49200364,
        49201021,
        49201507,
        49202203,
        49202429,
        49202673,
        49204507,
        49206056,
        49207460,
        49208079,
        49208168,
        49208172,
        49208174,
        49208437,
        49208461,
        49208505,
        49208510,
        49208513,
        49208529,
        49208533,
        49208535,
        49208537,
        49208549,
        49208552,
        49208559,
        49208566,
        49208572,
        49208580,
        49273808,
        49278857,
        49279281,
        49279712,
        49279958,
        49280151,
        49280513,
        49293137,
        49295944,
        49296492,
        49296789,
        49315083,
        49315088,
        49315089,
        49315091,
        49337124,
        49337666,
        49337921,
        49338651,
        49339441,
        49365406,
        49521861,
        49524217,
        49525888,
        49528290,
        49529452,
        49530020,
        49577566,
        49579560,
        49579877,
        49579925,
        49580330,
        49862488,
        49863198,
        49863254,
        49863423,
        49869224,
        49869229,
        49869232,
        49869238,
        49869273,
        49869279,
        49869288,
        49869308,
        49869328,
        49869331,
        49869337,
        49870036,
        49872928,
        49878570,
        49896073,
        49896079,
        49896095,
        49896101,
        49896103,
        49896124,
        49896126,
        49896131,
        49897165,
        49897168,
        49897187,
        49897241,
        49898051,
        49943954,
        49943962,
        49945646,
        49945686,
        49945722,
        49945749,
        49954797,
        49961639,
        50025211,
        50025255,
        50025641,
        50026011,
        50027434,
        50140605,
        50140617,
        50140626,
        50140634,
        50140645,
        50140665,
        50210505,
        50210509,
        50210513,
        50210518,
        50210522,
        50210524,
        50210526,
        50210529,
        50210530,
        50210533,
        50210535,
        50213562,
        50213565,
        50213568,
        50213569,
        50231892,
        50232446,
        50258612,
        50258645,
        50258699,
        50258712,
        50258723,
        50258749,
        50258761,
        50258771,
        50258804,
        50258817,
        50258923,
        50258940,
        50258942,
        50258953,
        50258967,
        50258970,
        50258973,
        50258976,
        50258982,
        50258985,
        50258992,
        50259000,
        50259020,
        50259046,
        50259057,
        50259067,
        50259083,
        50259087,
        50259095,
        50259100,
        50259105,
        50259116,
        50259126,
        50260644,
        50260840,
        50262421,
        50262784,
        50262916,
        50263664,
        50263722,
        50283642,
        50283671,
        50286325,
        50286332,
        50286468,
        50286490,
        50286508,
        50286515,
        50286565,
        50286801,
        50286958,
        50286968,
        50287005,
        50287039,
        50287111,
        50287934,
        50288119,
        50288426,
        50288509,
        50349159,
        50349164,
        50349210,
        50349234,
        50349243,
        50349746,
        50349752,
        50349766,
        50349774,
        50349791,
        50349878,
        50349885,
        50349910,
        50349967,
        50349975,
        50349980,
        50350030,
        50350096,
        50350129,
        50350141,
        50350155,
        50350351,
        50357196,
        50357241,
        50357409,
        50357492,
        50357583,
        50357632,
        50357845,
        50357943,
        50373587,
        50373597,
        50374095,
        50374102,
        50374194,
        50374230,
        50374244,
        50374246,
        50374561,
        50374576,
        50374584,
        50374589,
        50374597,
        50374604,
        50374612,
        50374620,
        50374636,
        50451406,
        50451417,
        50451420,
        50451495,
        50451511,
        50451520,
        50451524,
        50451552,
        50451555,
        50460906,
        50464163,
        50464509,
        50464712,
        50464713,
        50464795,
        50464819,
        50464867,
        50464873,
        50464889,
        50464900,
        50465262,
        50465306,
        50473797,
        50474465,
        50474969,
        50475123,
        50475414,
        50475500,
        50475717,
        50484641,
        50485189,
        50485726,
        50485777,
        50486089,
        50489216,
        50489509,
        50489592,
        50489740,
        50521256,
        50521333,
        50521374,
        50522107,
        50522111,
        50522114,
        50522125,
        50522128,
        50522129,
        50522136,
        50522141,
        50522152,
        50523302,
        50549597,
        50579350,
        50579742,
        50579754,
        50579764,
        50579776,
        50579829,
        50579857,
        50580409,
        50583438,
        50583691,
        50585074,
        50588040,
        50588247,
        50588281,
        50588338,
        50588431,
        50588734,
        50588867,
        50588911,
        50597075,
        50597137,
        50623356,
        50623385,
        50623393,
        50623694,
        50623704,
        50623709,
        50623714,
        50623722,
        50623735,
        50623823,
        50623844,
        50623870,
        50623904,
        50623918,
        50623938,
        50623947,
        50623979,
        50624024,
        50624036,
        50624044,
        50658041,
        50671668,
        50671712,
        50671748,
        50671762,
        50673329,
        50673346,
        50674769,
        50674779,
        50674799,
        50674811,
        50674817,
        50674823,
        50674842,
        50674871,
        50674962,
        50675563,
        50675624,
        50675639,
        50675689,
        50677774,
        50677780,
        50677785,
        50677808,
        50677817,
        50677823,
        50733021,
        50733264,
        50770507,
        50770825,
        50770836,
        50771066,
        50771292,
        50771306,
        50771348,
        50771350,
        50771356,
        50771368,
        50771375,
        50771936,
        50772930,
        50773003,
        50773251,
        50773307,
        50773339,
        50773352,
        50774092,
        50774168,
        50774382,
        50774508,
        50774531,
        50774654,
        50774676,
        50774737,
        50774767,
        50774947,
        50774961,
        50922168,
        50925348,
        50925757,
        50925871,
        50926513,
        50927122,
        50932926,
        51043084,
        51047070,
        51047914,
        51049739,
        51050939,
        51051831,
        51051989,
        51054598,
        51054620,
        51054669,
        51060758,
        51060815,
        51061384,
        51061552,
        51082100,
        51083688,
        51085140,
        51085425,
        51086071,
        51096769,
        51096818,
        51096938,
        51099033,
        51117514,
        51118234,
        51118463,
        51118541,
        51118554,
        51118668,
        51118741,
        51118762,
        51119522,
        51119537,
        51119543,
        51119563,
        51119666,
        51120006,
        51124359,
        51306032,
        51306033,
        51306040,
        51306073,
        51306080,
        51306097,
        51306099,
        51306100,
        51306101,
        51306103,
        51306105,
        51306106,
        51306107,
        51306117,
        51306118,
        51306120,
        51306130,
        51306131,
        51306134,
        51306137,
        51306142,
        51306145,
        51306149,
        51306153,
        51306158,
        51306167,
        51306169,
        51306176,
        51306178,
        51306179,
        51306180,
        51306182,
        51306183,
        51306184,
        51306185,
        51306187,
        51306189,
        51306191,
        51306192,
        51306195,
        51306217,
        51306219,
        51306222,
        51306224,
        51306300,
        51306307,
        51306313,
        51306317,
        51306318,
        51306323,
        51306326,
        51306327,
        51306331,
        51306332,
        51306335,
        51306336,
        51306361,
        51306363,
        51306365,
        51306367,
        51306368,
        51306370,
        51306407,
        51306408,
        51306410,
        51306411,
        51306412,
        51306413,
        51306419,
        51306420,
        51306432,
        51306433,
        51306435,
        51306454,
        51306470,
        51306471,
        51306473,
        51306474,
        51306476,
        51306477,
        51306480,
        51306482,
        51306484,
        51306486,
        51306487,
        51306488,
        51306489,
        51306491,
        51306493,
        51306494,
        51306496,
        51306509,
        51306514,
        51306519,
        51306521,
        51306527,
        51306531,
        51306536,
        51306537,
        51306538,
        51306540,
        51306541,
        51306550,
        51306557,
        51306588,
        51306589,
        51306591,
        51306592,
        51306593,
        51306596,
        51306599,
        51306601,
        51306602,
        51306605,
        51306607,
        51306613,
        51306615,
        51306625,
        51306626,
        51306627,
        51306628,
        51306629,
        51306632,
        51306635,
        51306636,
        51306640,
        51306647,
        51306649,
        51306650,
        51306657,
        51306658,
        51306661,
        51306662,
        51306665,
        51306666,
        51306667,
        51306668,
        51306671,
        51306672,
        51306674,
        51306675,
        51306677,
        51306678,
        51306679,
        51306680,
        51306682,
        51306686,
        51306693,
        51306694,
        51306696,
        51306699,
        51306705,
        51306710,
        51306712,
        51306723,
        51306724,
        51306728,
        51306732,
        51306734,
        51306736,
        51306739,
        51306766,
        51306767,
        51306768,
        51306778,
        51306786,
        51306787,
        51306788,
        51306792,
        51306793,
        51306796,
        51306799,
        51306801,
        51306803,
        51306804,
        51306807,
        51306809,
        51306810,
        51306814,
        51306815,
        51306816,
        51306817,
        51306820,
        51306822,
        51306823,
        51306824,
        51306825,
        51306828,
        51306831,
        51306834,
        51306836,
        51306840,
        51306841,
        51306843,
        51306851,
        51306852,
        51306853,
        51306854,
        51306858,
        51306874,
        51306878,
        51306969,
        51306978,
        51306985,
        51306993,
        51307005,
        51307009,
        51307011,
        51307013,
        51307016,
        51307017,
        51307018,
        51307021,
        51307023,
        51307024,
        51307026,
        51307043,
        51307045,
        51307046,
        51307048,
        51307052,
        51307053,
        51307055,
        51307062,
        51307064,
        51307066,
        51307067,
        51307074,
        51307086,
        51307090,
        51307102,
        51307103,
        51307109,
        51307111,
        51307115,
        51307124,
        51307131,
        51307134,
        51307139,
        51307143,
        51307147,
        51307148,
        51307150,
        51307153,
        51307160,
        51307164,
        51307166,
        51307167,
        51307168,
        51307169,
        51307177,
        51307179,
        51307180,
        51307183,
        51307184,
        51307185,
        51307192,
        51307194,
        51307196,
        51307197,
        51315518,
        51315528,
        51315534,
        51315551,
        51315564,
        51315569,
        51317494,
        51322461,
        51322464,
        51322474,
        51322490,
        51322498,
        51322502,
        51322514,
        51322519,
        51322579,
        51322625,
        51322627,
        51322646,
        51322651,
        51322655,
        51322660,
        51322672,
        51322686,
        51322704,
        51322832,
        51322839,
        51322840,
        51322849,
        51324034,
        51324599,
        51324758,
        51324964,
        51327608,
        51327771,
        51327900,
        51331923,
        51332235,
        51335356,
        51437511,
        51437516,
        51441113,
        51443762,
        51443964,
        51444181,
        51444361,
        51481530,
        51521309,
        51524204,
        51524510,
        51525504,
        51528116,
        51528179,
        51528289,
        51542788,
        51542808,
        51542865,
        51543088,
        51554126,
        51554281,
        51554792,
        51555564,
        51555592,
        51555702,
        51555972,
        51628407,
        51628482,
        51628488,
        51628525,
        51628628,
        51628647,
        51628661,
        51628667,
        51628954,
        51628976,
        51681392,
        51681401,
        51681727,
        51681806,
        51682019,
        51682042,
        51682047,
        51682359,
        51682375,
        51682811,
        51682818,
        51682825,
        51682871,
        51682905,
        51682932,
        51683054,
        51683073,
        51683104,
        51683115,
        51684832,
        51695891,
        51715111,
        51716159,
        51716419,
        51716837,
        51716874,
        51716919,
        51716933,
        51717018,
        51717038,
        51717349,
        51717680,
        51717819,
        51717821,
        51717987,
        51718065,
        51718508,
        51719096,
        51719214,
        51719278,
        51719322,
        51719372,
        51719477,
        51719506,
        51719527,
        51719563,
        51719660,
        51748377,
        51748394,
        51748406,
        51748413,
        51748428,
        51748451,
        51748472,
        51748489,
        51748566,
        51748581,
        51748599,
        51748623,
        51748901,
        51748909,
        51748914,
        51748927,
        51748939,
        51748940,
        51748953,
        51749115,
        51749187,
        51749189,
        51749211,
        51749346,
        51749352,
        51749465,
        51749541,
        51749552,
        51751659,
        51751947,
        51760479,
        51800776,
        51800920,
        51801270,
        51801362,
        51801402,
        51801455,
        51801624,
        51806306,
        51806310,
        51806326,
        51806725,
        51807734,
        51807779,
        51807828,
        51807933,
        51808051,
        51808068,
        51808992,
        51809032,
        51844689,
        51845494,
        51845545,
        51845652,
        51845686,
        51845697,
        51845702,
        51845711,
        51845719,
        51845723,
        51845766,
        51845788,
        51845941,
        51846011,
        51846101,
        51846257,
        51846371,
        51846895,
        51846921,
        51848764,
        51848871,
        51849438,
        51849603,
        51886304,
        51887493,
        51887574,
        51887578,
        51887898,
        51887977,
        52145415,
        52147633,
        52155549,
        52158652,
        52158992,
        52159776,
        52160048,
        52160204,
        52161582,
        52162201,
        52163389,
        52181160,
        52181218,
        52188178,
        52191604,
        52191810,
        52192546,
        52192819,
        52193036,
        52193358,
        52193598,
        52193725,
        52194057,
        52194421,
        52194471,
        52196200,
        52200703,
        52201040,
        52201214,
        52234157,
        52235060,
        52329552,
        52334130,
        52334213,
        52334552,
        52334817,
        52336565,
        52336791,
        52337149,
        52342563,
        52343181,
        52345405,
        52345582,
        52345719,
        52346156,
        52346467,
        52346483,
        52372219,
        52372979,
        52373666,
        52373855,
        52383781,
        52384097,
        52384432,
        52384648,
        52385500,
        52386485,
        52400882,
        52401016,
        52401484,
        52401486,
        52401720,
        52401733,
        52401753,
        52401789,
        52401793,
        52401803,
        52401807,
        52401831,
        52401840,
        52401849,
        52401862,
        52401870,
        52401884,
        52401905,
        52401928,
        52401929,
        52402110,
        52402616,
        52402793,
        52403235,
        52403308,
        52403310,
        52403355,
        52403415,
        52403447,
        52403459,
        52403489,
        52403513,
        52403554,
        52403910,
        52404136,
        52404259,
        52404300,
        52404421,
        52404446,
        52404513,
        52404608,
        52404670,
        52472915,
        52472917,
        52472943,
        52473028,
        52473043,
        52473049,
        52473078,
        52473089,
        52473100,
        52473148,
        52473174,
        52473183,
        52473255,
        52473288,
        52473313,
        52473331,
        52473932,
        52473944,
        52473971,
        52473973,
        52473982,
        52474030,
        52474067,
        52474236,
        52474529,
        52474617,
        52474677,
        52474922,
        52474947,
        52510558,
        52513227,
        52513302,
        52513400,
        52513426,
        52513457,
        52513458,
        52513465,
        52513478,
        52513479,
        52513497,
        52513541,
        52513590,
        52513701,
        52513729,
        52513747,
        52513815,
        52513850,
        52513865,
        52513868,
        52513887,
        52513895,
        52514028,
        52514037,
        52514038,
        52514041,
        52514051,
        52531836,
        52558046,
        52558101,
        52558125,
        52558177,
        52558269,
        52558282,
        52558404,
        52558421,
        52558446,
        52558528,
        52558547,
        52558581,
        52558594,
        52558805,
        52568128,
        52599975,
        52600361,
        52600559,
        52600572,
        52601844,
        52601956,
        52601965,
        52601987,
        52603730,
        52611988,
        52639712,
        52639714,
        52639722,
        52639849,
        52639952,
        52640043,
        52640130,
        52640257,
        52640410,
        52640573,
        52640578,
        52640581,
        52640612,
        52640617,
        52640621,
        52640709,
        52640723,
        52640737,
        52641058,
        52641170,
        52641250,
        52641390,
        52641406,
        52641412,
        52641423,
        52644675,
        52674280,
        52676483,
        52676609,
        52702203,
        52705807,
        52706107,
        52706255,
        52706287,
        52706498,
        52706625,
        52706671,
        52706770,
        52706807,
        52706860,
        52706931,
        52707161,
        52710404,
        52710604,
        52710727,
        52722823,
        52722900,
        52722962,
        52723118,
        52723505,
        52723900,
        52724085,
        52734114,
        52735042,
        52735172,
        52735274,
        52735305,
        52735371,
        52735417,
        52748914,
        52749196,
        52751665,
        52752546,
        52753783,
        52776358,
        52776977,
        52777494,
        52777495,
        52777499,
        52777553,
        52777563,
        52777669,
        52777680,
        52777707,
        52777724,
        52777737,
        52777746,
        52777755,
        52778068,
        52778745,
        52778875,
        52779680,
        52779690,
        52779826,
        52781970,
        52782054,
        52782273,
        52782387,
        52796607,
        52799009,
        52800012,
        52849368,
        52849374,
        52849399,
        52849446,
        52849477,
        52849493,
        52849522,
        52849534,
        52850318,
        52850639,
        52850645,
        52850654,
        52850662,
        52850665,
        52850673,
        52850689,
        52850695,
        52850710,
        52850718,
        52850735,
        52850755,
        52850775,
        52850808,
        52850811,
        52850822,
        52850844,
        52850877,
        52850893,
        52850916,
        52850928,
        52850941,
        52851212,
        52851267,
        52851340,
        52851413,
        52851435,
        52851520,
        52851521,
        52851588,
        52851617,
        52851773,
        52851874,
        52852447,
        52852657,
        52852796,
        52852831,
        52853216,
        52853581,
        52853791,
        52854116,
        52855108,
        52882022,
        52882099,
        52882220,
        52882241,
        52882256,
        52882292,
        52882343,
        52882647,
        52882728,
        52882765,
        52882781,
        52882881,
        52890914,
        52891080,
        52891417,
        52891565,
        52891773,
        52891829,
        52892209,
        52892217,
        52892258,
        52892290,
        52892335,
        52892548,
        52892626,
        52894163,
        52894167,
        52894227,
        52894272,
        52894371,
        52894457,
        52894475,
        52898038,
        52898266,
        52898380,
        52898411,
        52899529,
        52918867,
        52918888,
        52918946,
        52919169,
        52919250,
        52919265,
        52919423,
        52919492,
        52919587,
        52919603,
        52919654,
        52919709,
        52920229,
        52920553,
        52926417,
        52930643,
        52931948,
        52932047,
        52932102,
        52932237,
        52932249,
        52932323,
        52932399,
        52932409,
        52932463,
        52932484,
        52932510,
        52932543,
        52932694,
        52933103,
        52933134,
        52933349,
        52933388,
        52933784,
        52933882,
        52933928,
        52935143,
        52935243,
        52935309,
        52935463,
        52935570,
        52935606,
        52935646,
        52935800,
        52945118,
        52947900,
        52956686,
        52956720,
        52957174,
        52957316,
        52962666,
        52962747,
        52963236,
        52963288,
        52963293,
        52963389,
        52963998,
        52964323,
        52965797,
        52965812,
        52965829,
        52965880,
        52966202,
        52966213,
        52970696,
        52971460,
        52990026,
        52993070,
        53044492,
        53046489,
        53048616,
        53048808,
        53048924,
        53049055,
        53049150,
        53049263,
        53049288,
        53050082,
        53130023,
        53158422,
        53160657,
        53162238,
        53163230,
        53163294,
        53174967,
        53174976,
        53174981,
        53175712,
        53175727,
        53175786,
        53175828,
        53175844,
        53175857,
        53175874,
        53175895,
        53176182,
        53176186,
        53176202,
        53177145,
        53177409,
        53177479,
        53177566,
        53177647,
        53177665,
        53177686,
        53177736,
        53177768,
        53177778,
        53177787,
        53177819,
        53177841,
        53178091,
        53178196,
        53178305,
        53178460,
        53180492,
        53180702,
        53180944,
        53183890,
        53184090,
        53184390,
        53184956,
        53200722,
        53226366,
        53226667,
        53227147,
        53228366,
        53228417,
        53228441,
        53229095,
        53229274,
        53229572,
        53231211,
        53234935,
        53235101,
        53235164,
        53259425,
        53259559,
        53259570,
        53259595,
        53259615,
        53259631,
        53302375,
        53302677,
        53302947,
        53302966,
        53303002,
        53303060,
        53303306,
        53303344,
        53303392,
        53303402,
        53303429,
        53303593,
        53304408,
        53304568,
        53304670,
        53304797,
        53344171,
        53450152,
        53450186,
        53450212,
        53450226,
        53450245,
        53450336,
        53450558,
        53450594,
        53450853,
        53450897,
        53451073,
        53451105,
        53451128,
        53451323,
        53451697,
        53451787,
        53452469,
        53452574,
        53452588,
        53452598,
        53452643,
        53452679,
        53452701,
        53452721,
        53452746,
        53452767,
        53452788,
        53452804,
        53469802,
        53527508,
        53527537,
        53527554,
        53528239,
        53528812,
        53530569,
        53532626,
        53532700,
        53534913,
        53534972,
        53534995,
        53535085,
        53535292,
        53535467,
        53536399,
        53813835,
        53827794,
        53829587,
        53832276,
        53832760,
        53833037,
        53833227,
        53833511,
        53834001,
        53834875,
        53836034,
        53836639,
        53843785,
        53863057,
        53867486,
        53869075,
        53870322,
        53870846,
        53875787,
        53875991,
        53876050,
        53876089,
        53877925,
        53880696,
        53882836,
        53883029,
        53883117,
        53883371,
        53914356,
        53914467,
        53914476,
        53916197,
        53916254,
        53916465,
        53917019,
        53917177,
        53917413,
        53917465,
        53917609,
        53917996,
        53918047,
        53918287,
        53918333,
        53918366,
        53918481,
        53918584,
        53918745,
        53918782,
        53919131,
        53919354,
        53919474,
        53919524,
        53967270,
        53967281,
        53967295,
        53967310,
        53967374,
        54015706,
        54015773,
        54015797,
        54015850,
        54016609,
        54016685,
        54016857,
        54126731,
        54127048,
        54163555,
        54165850,
        54166012,
        54214648,
        54214664,
        54214757,
        54214805,
        54214835,
        54214865,
        54214881,
        54214898,
        54218819,
        54219196,
        54219227,
        54220585,
        54262092,
        54263677,
        54263746,
        54264055,
        54264189,
        54264224,
        54264233,
        54264358,
        54266421,
        54267208,
        54268202,
        54268589,
        54268747,
        54269027,
        54269245,
        54269250,
        54269570,
        54272008,
        54272366,
        54272860,
        54274544,
        54275610,
        54291682,
        54349466,
        54352301,
        54352380,
        54352416,
        54352447,
        54352475,
        54352598,
        54355734,
        54355996,
        54356012,
        54356039,
        54356067,
        54356083,
        54356098,
        54356659,
        54356696,
        54356944,
        54357565,
        54357687,
        54357756,
        54501128,
        54509983,
        54511144,
        54511383,
        54512482,
        54512727,
        54512808,
        54512856,
        54515423,
        54516700,
        54519044,
        54519312,
        54521384,
        54521591,
        54703331,
        54746071,
        54746075,
        54746100,
        54746118,
        54746121,
        54746127,
        54746135,
        54746138,
        54746140,
        54746147,
        54746154,
        54746182,
        54746191,
        54746196,
        54746203,
        54746213,
        54746221,
        54746222,
        54746226,
        54746238,
        54746240,
        54748297,
        54748465,
        54748490,
        54748661,
        54748977,
        54749071,
        54815012,
        54818626,
        54819054,
        54819770,
        54819845,
        54822166,
        54822177,
        54822458,
        54822509,
        54822695,
        54822722,
        54828559,
        54829280,
        54841376,
        54842664,
        54843132,
        54854811,
        54928456,
        54928563,
        54928696,
        54928710,
        54928715,
        55064380,
        55065825,
        55065960,
        55066812,
        55066836,
        55071857,
        55079197,
        55208997,
        55209330,
        55209693,
        55210715,
        55212694,
        55212852,
        55212923,
        55213027,
        55213085,
        55213222,
        55217173,
        55217344,
        55217500,
        55218951,
        55219135,
        55219194,
        55219234,
        55219268,
        55219362,
        55219381,
        55219388,
        55219437,
        55219462,
        55219542,
        55219562,
        55473569,
        55473605,
        55473661,
        55473701,
        55473733,
        55473784,
        55473815,
        55473923,
        55474008,
        55474149,
        55474199,
        55475058,
        55481445,
        55481555,
        55481628,
        55482130,
        55482185,
        55482270,
        55482341,
        55482392,
        55483907,
        55483997,
        55496005,
        55579158,
        55580928,
        55581120,
        55581249,
        55582990,
        55585465,
        55588593,
        55588658,
        55588791,
        55588876,
        55588935,
        55589027,
        55589230,
        55589344,
        55590615,
        55590694,
        55590973,
        55592908,
        55604272,
        55607150,
        55607541,
        55607888,
        55608053,
        55608155,
        55608259,
        55608290,
        55608510,
        55608773,
        55609259,
        55609679,
        55610014,
        55711201,
        55711527,
        55711816,
        55717692,
        55717919,
        55718052,
        55719981,
        55720159,
        55721161,
        55721790,
        55722755,
        55723017,
        55723419,
        55724321,
        55724678,
        55726180,
        55726604,
        55726772,
        55726949,
        55777138,
        55779965,
        55780632,
        55780672,
        55780721,
        55780872,
        55780997,
        55781060,
        55785610,
        55853955,
        55937361,
        55937464,
        55941027,
        55941049,
        55941061,
        55941136,
        55941240,
        55942159,
        55942331,
        55942406,
        55942548,
        55946456,
        55946604,
        55947470,
        55947664,
        55947948,
        55948091,
        55948514,
        56012768,
        56014547,
        56015272,
        56015538,
        56015823,
        56064009,
        56064122,
        56064231,
        56064503,
        56064747,
        56064954,
        56065004,
        56065053,
        56065090,
        56065104,
        56065114,
        56065127,
        56065139,
        56065151,
        56065159,
        56065168,
        56065183,
        56065206,
        56065252,
        56066720,
        56066850,
        56066984,
        56067124,
        56067194,
        56163379,
        56163716,
        56163861,
        56163939,
        56164325,
        56164540,
        56164609,
        56164637,
        56164707,
        56164736,
        56164820,
        56164840,
        56164874,
        56164970,
        56165394,
        56165824,
        56165965,
        56253025,
        56257889,
        56381367,
        56381463,
        56389451,
        56389531,
        56389655,
        56390661,
        56391604,
        56391661,
        56391763,
        56391863,
        56399763,
        56402574,
        56534571,
        56534624,
        56534680,
        56534724,
        56534794,
        56534847,
        56534915,
        56534998,
        56535027,
        56535051,
        56535059,
        56535069,
        56535082,
        56535185,
        56535194,
        56535660,
        56541402,
        56541652,
        56541705,
        56542546,
        56542721,
        56542738,
        56543610,
        56543673,
        56543722,
        56543800,
        56543855,
        56603924,
        56604691,
        56604918,
        56605458,
        56673659,
        56674527,
        56675283,
        56675894,
        56676255,
        56678406,
        56679467,
        56679999,
        56684041,
        56684423,
        56685018,
        56685538,
        56685581,
        56685738,
        56687060,
        56687068,
        56687078,
        56687202,
        56687209,
        56687470,
        56687566,
        56696201,
        56696412,
        56696552,
        56697600,
        56698336,
        56698525,
        56698587,
        56698634,
        56698667,
        56698874,
        56698928,
        56698947,
        56699130,
        56699327,
        56699452,
        56700739,
        56700806,
        56700898,
        56700936,
        56701011,
        56701106,
        56701553,
        56703344,
        56720141,
        56744142,
        56744385,
        56744598,
        56744744,
        56778887,
        56778929,
        56781922,
        56782004,
        56782087,
        56782641,
        56785408,
        56786314,
        56786641,
        56788767,
        56791607,
        56792082,
        56795426,
        56795733,
        56795770,
        56796126,
        56796215,
        56796229,
        56796288,
        56796340,
        56796378,
        56796392,
        56796424,
        56796443,
        56799903,
        56800081,
        56800597,
        56800721,
        56801350,
        56802095,
        56892140,
        56892190,
        56892278,
        56892633,
        56892651,
        56892666,
        56892670,
        56892755,
        56892789,
        56892790,
        56892793,
        56892809,
        56894487,
        56895752,
        56895816,
        56895872,
        56896102,
        56896163,
        56896451,
        56896736,
        56896797,
        56901883,
        56902051,
        56902404,
        56963662,
        56963849,
        56964162,
        56964290,
        57117493,
        57117569,
        57117629,
        57117702,
        57117878,
        57117966,
        57118104,
        57118140,
        57118532,
        57145211,
        57145268,
        57145341,
        57145430,
        57145448,
        57145452,
        57145464,
        57145504,
        57145516,
        57145521,
        57145564,
        57147258,
        57147265,
        57147280,
        57147310,
        57147350,
        57261798,
        57262882,
        57264733,
        57265355,
        57272211,
        57272356,
        57272607,
        57272806,
        57273044,
        57273222,
        57273701,
        57273816,
        57274068,
        57274188,
        57274281,
        57274526,
        57274691,
        57274933,
        57274966,
        57275101,
        57275381,
        57276146,
        57276435,
        57276680,
        57277050,
        57277273,
        57277338,
        57277814,
        57280770,
        57281273,
        57281671,
        57281864,
        57282085,
        57293022,
        57293474,
        57293860,
        57294432,
        57294807,
        57294956,
        57295408,
        57295758,
        57295802,
        57299651,
        57299826,
        57301178,
        57301800,
        57316077,
        57316117,
        57316189,
        57316770,
        57317729,
        57319575,
        57319744,
        57319838,
        57320787,
        57321010,
        57321241,
        57321556,
        57321759,
        57322280,
        57322620,
        57322821,
        57325303,
        57325447,
        57326042,
        57326651,
        57326887,
        57327559,
        57327798,
        57330680,
        57330778,
        57330886,
        57331125,
        57331311,
        57331619,
        57331959,
        57334047,
        57345499,
        57381842,
        57382988,
        57384271,
        57384382,
        57384471,
        57384556,
        57385353,
        57385362,
        57385392,
        57385409,
        57385493,
        57385603,
        57385713,
        57385944,
        57387940,
        57388084,
        57388772,
        57388852,
        57390810,
        57391209,
        57391550,
        57392206,
        57393353,
        57393359,
        57393364,
        57393368,
        57393372,
        57393522,
        57393962,
        57439849,
        57439868,
        57439890,
        57439897,
        57439978,
        57440333,
        57440480,
        57440487,
        57440618,
        57440638,
        57440814,
        57442085,
        57442140,
        57442970,
        57465248,
        57465266,
        57465288,
        57465312,
        57465315,
        57465320,
        57465327,
        57465443,
        57465540,
        57465571,
        57465605,
        57465610,
        57465616,
        57465643,
        57465696,
        57465730,
        57465901,
        57465930,
        57465964,
        57466028,
        57466084,
        57466091,
        57466102,
        57466119,
        57466124,
        57466160,
        57466179,
        57466212,
        57466236,
        57466254,
        57469697,
        57510204,
        57510209,
        57510214,
        57510216,
        57510223,
        57510272,
        57510291,
        57510314,
        57510315,
        57510325,
        57510352,
        57510353,
        57510358,
        57510382,
        57510386,
        57510391,
        57510392,
        57510412,
        57510471,
        57510474,
        57510858,
        57510882,
        57510951,
        57511327,
        57511493,
        57511930,
        57513006,
        57513020,
        57513041,
        57513054,
        57513064,
        57513293,
        57513392,
        57513627,
        57513655,
        57513669,
        57513695,
        57513825,
        57513849,
        57513854,
        57513890,
        57513913,
        57516239,
        57516823,
        57527292,
        57527666,
        57527878,
        57528239,
        57528841,
        57529297,
        57530024,
        57530137,
        57530292,
        57559195,
        57559207,
        57559836,
        57559934,
        57559999,
        57560030,
        57560184,
        57560448,
        57560557,
        57560662,
        57560785,
        57560792,
        57561268,
        57561873,
        57562000,
        57562175,
        57562428,
        57563907,
        57564204,
        57564309,
        57564584,
        57564680,
        57565019,
        57565043,
        57565107,
        57565202,
        57565274,
        57565311,
        57565583,
        57566266,
        57566357,
        57566388,
        57566410,
        57566442,
        57566479,
        57566498,
        57566755,
        57566797,
        57566809,
        57566814,
        57566854,
        57566868,
        57567007,
        57567102,
        57567223,
        57567758,
        57568017,
        57568150,
        57568207,
        57568383,
        57568478,
        57568617,
        57568661,
        57569032,
        57569102,
        57569157,
        57571188,
        57571477,
        57572028,
        57572234,
        57572486,
        57573098,
        57576085,
        57576687,
        57677277,
        57868613,
        57868637,
        57868754,
        57869350,
        57870748,
        57872261,
        57964100,
        57964178,
        57964190,
        57964209,
        57964322,
        57966032,
        57967593,
        57968176,
        57968205,
        57968290,
        57968303,
        57968323,
        57968593,
        57968976,
        57970250,
        57970483,
        57970530,
        58035701,
        58037845,
        58039633,
        58041969,
        58059160,
        58059501,
        58059539,
        58059626,
        58083429,
        58083454,
        58083486,
        58086632,
        58186335,
        58186388,
        58186389,
        58186399,
        58186440,
        58186452,
        58186462,
        58186476,
        58186492,
        58186495,
        58235668,
        58236912,
        58237003,
        58237008,
        58237110,
        58237118,
        58237286,
        58237350,
        58237420,
        58237526,
        58237691,
        58237785,
        58237805,
        58237818,
        58237862,
        58238068,
        58238123,
        58238165,
        58238217,
        58238322,
        58238340,
        58238420,
        58238507,
        58238528,
        58239690,
        58241510,
        58241546,
        58243300,
        58244264,
        58244755,
        58247418,
        58247562,
        58262298,
        58262502,
        58262799,
        58263915,
        58266847,
        58287336,
        58291010,
        58291984,
        58292066,
        58292183,
        58292286,
        58292296,
        58292377,
        58292464,
        58292498,
        58292586,
        58292628,
        58292674,
        58292707,
        58292802,
        58292811,
        58292848,
        58292887,
        58293021,
        58293289,
        58293423,
        58293724,
        58293862,
        58293882,
        58293971,
        58294190,
        58294273,
        58300072,
        58340560,
        58340588,
        58340605,
        58340754,
        58340800,
        58340862,
        58340895,
        58341028,
        58341054,
        58341137,
        58341836,
        58341987,
        58342247,
        58364307,
        58364363,
        58364504,
        58364558,
        58364620,
        58383598,
        58383775,
        58384062,
        58384289,
        58384518,
        58385026,
        58385166,
        58385468,
        58385632,
        58385844,
        58386024,
        58386163,
        58386429,
        58386564,
        58386674,
        58388345,
        58389505,
        58389783,
        58461361,
        58461364,
        58461366,
        58461369,
        58461371,
        58461484,
        58461501,
        58462189,
        58462678,
        58462694,
        58462702,
        58462726,
        58462736,
        58462759,
        58462763,
        58462862,
        58462896,
        58462914,
        58462944,
        58462981,
        58463063,
        58463129,
        58463376,
        58463470,
        58463510,
        58464253,
        58464653,
        58464977,
        58465052,
        58465147,
        58466639,
        58469556,
        58469978,
        58471555,
        58471768,
        58472553,
        58523359,
        58543249,
        58543450,
        58543519,
        58543528,
        58543543,
        58543575,
        58546894,
        58595343,
        58596342,
        58596388,
        58596426,
        58596463,
        58596781,
        58596792,
        58596973,
        58597971,
        58598393,
        58598622,
        58598633,
        58598657,
        58598927,
        58598976,
        58599001,
        58599067,
        58599110,
        58601121,
        58601256,
        58601261,
        58601630,
        58601657,
        58610864,
        58611083,
        58611414,
        58656367,
        58656402,
        58656423,
        58656491,
        58656524,
        58656728,
        58656743,
        58656782,
        58657220,
        58657608,
        58658003,
        58658280,
        58658345,
        58658412,
        58658496,
        58658713,
        58658757,
        58658802,
        58658807,
        58658816,
        58658960,
        58658978,
        58659003,
        58659039,
        58659097,
        58659109,
        58659141,
        58659215,
        58659258,
        58659348,
        58659484,
        58659637,
        58659796,
        58661149,
        58661479,
        58661753,
        58661882,
        58662716,
        58669983,
        58671031,
        58673113,
        58673359,
        58673610,
        58722771,
        58723201,
        58880300,
        58899211,
        58910546,
        58911474,
        58911669,
        58911804,
        58911917,
        58912373,
        58912513,
        58912712,
        58912939,
        58913136,
        58949747,
        58951987,
        58954201,
        58954256,
        58954328,
        58954394,
        58960500,
        58960896,
        58960924,
        58960935,
        58960943,
        58960960,
        58960988,
        58961021,
        58961359,
        58968710,
        58968720,
        58968730,
        58968741,
        58968748,
        58968828,
        58969803,
        58970788,
        58970804,
        58970824,
        58970879,
        58971822,
        58972065,
        58972582,
        58972658,
        58972713,
        58972727,
        58972739,
        58972815,
        58972842,
        58973000,
        58973048,
        58973107,
        58973208,
        58973292,
        58973356,
        58973819,
        58973837,
        58973861,
        58973905,
        58978926,
        58980881,
        58981103,
        58981252,
        58988783,
        58991319,
        58991471,
        58991505,
        58991947,
        58992030,
        58992088,
        58992170,
        59005839,
        59005861,
        59005891,
        59005895,
        59005904,
        59005916,
        59005922,
        59005930,
        59005934,
        59005941,
        59006042,
        59006099,
        59006531,
        59007084,
        59009472,
        59009486,
        59009519,
        59009643,
        59009670,
        59009702,
        59009720,
        59009740,
        59009760,
        59009779,
        59009814,
        59009815,
        59009817,
        59009860,
        59009889,
        59009905,
        59009908,
        59010174,
        59010435,
        59010448,
        59010537,
        59010572,
        59010597,
        59010662,
        59010779,
        59010901,
        59010907,
        59010971,
        59011000,
        59011339,
        59011429,
        59011652,
        59011710,
        59012065,
        59012124,
        59012275,
        59013060,
        59016598,
        59016679,
        59046622,
        59046629,
        59046762,
        59051337,
        59051482,
        59051501,
        59051556,
        59098769,
        59098784,
        59098899,
        59098907,
        59098908,
        59099243,
        59099258,
        59099286,
        59099293,
        59102021,
        59180006,
        59180036,
        59180061,
        59180167,
        59180176,
        59180221,
        59180223,
        59180243,
        59180264,
        59180335,
        59180362,
        59180382,
        59180404,
        59180408,
        59180440,
        59180460,
        59180491,
        59180609,
        59180620,
        59180625,
        59180664,
        59180689,
        59180711,
        59180821,
        59181037,
        59181067,
        59181124,
        59181379,
        59181392,
        59181405,
        59181420,
        59181678,
        59181831,
        59182248,
        59182981,
        59182988,
        59236577,
        59236654,
        59236666,
        59236672,
        59236721,
        59236770,
        59236952,
        59238803,
        59238831,
        59239170,
        59241061,
        59241139,
        59243130,
        59250965,
        59251069,
        59251332,
        59251662,
        59251751,
        59287263,
        59287795,
        59287806,
        59287818,
        59287871,
        59287875,
        59287897,
        59287955,
        59289037,
        59292307,
        59293644,
        59293774,
        59293937,
        59294383,
        59295548,
        59295634,
        59295645,
        59295699,
        59295771,
        59295845,
        59295889,
        59295957,
        59296443,
        59296528,
        59296584,
        59296633,
        59296928,
        59297305,
        59297806,
        59300674,
        59332526,
        59333185,
        59333242,
        59333277,
        59334014,
        59334159,
        59334228,
        59334585,
        59334601,
        59334920,
        59334970,
        59334993,
        59335092,
        59335174,
        59335263,
        59335283,
        59335307,
        59335338,
        59335377,
        59335431,
        59335473,
        59335525,
        59335631,
        59335753,
        59336517,
        59337032,
        59338493,
        59339295,
        59377913,
        59377947,
        59377970,
        59378225,
        59378389,
        59378416,
        59378435,
        59378477,
        59379414,
        59379435,
        59379484,
        59379552,
        59379654,
        59380118,
        59383130,
        59432878,
        59433175,
        59433219,
        59433263,
        59433275,
        59433289,
        59433307,
        59433318,
        59433325,
        59433335,
        59433348,
        59433373,
        59433396,
        59433411,
        59433429,
        59433446,
        59433453,
        59433466,
        59433559,
        59433566,
        59433578,
        59433592,
        59433639,
        59433701,
        59435035,
        59435125,
        59435598,
        59436102,
        59479967,
        59480721,
        59480752,
        59480869,
        59480943,
        59481297,
        59481309,
        59481401,
        59481573,
        59481660,
        59481684,
        59481729,
        59481749,
        59481753,
        59481770,
        59482367,
        59482432,
        59482433,
        59482492,
        59482571,
        59482605,
        59482623,
        59482635,
        59482676,
        59482814,
        59482870,
        59482895,
        59483035,
        59483043,
        59483056,
        59483255,
        59483373,
        59523105,
        59523121,
        59523363,
        59523387,
        59523462,
        59523467,
        59523483,
        59523492,
        59523507,
        59523805,
        59523814,
        59524782,
        59524828,
        59525114,
        59525470,
        59525715,
        59525722,
        59525854,
        59525868,
        59526463,
        59526469,
        59526490,
        59527442,
        59527470,
        59527604,
        59527698,
        59527736,
        59527789,
        59527807,
        59527858,
        59528495,
        59528554,
        59529089,
        59529844,
        59614576,
        59614680,
        59614705,
        59614716,
        59614718,
        59614728,
        59614745,
        59614749,
        59614755,
        59614756,
        59614781,
        59614803,
        59614807,
        59614823,
        59614899,
        59615203,
        59615364,
        59617576,
        75033534,
        75033549,
        75033570,
        75033596,
        75033696,
        75033710,
        75033711,
        75033724,
        75033783,
        75033814,
        75033826,
        75033833,
        75033835,
        75033916,
        75033938,
        75034015,
        75034026,
        75034036,
        75034047,
        75034080,
        75034137,
        75034223,
        75035311,
        75035336,
        75035372,
        75035649,
        75035664,
        75035796,
        75035826,
        75035839,
        75036725,
        75036912,
        75037209,
        75037248,
        75037784,
        75037798,
        75039295,
        75039310,
        75039860,
        75039909,
        75040453,
        75042141,
        75042673,
        75042729,
        75043044,
        75043368,
        75043876,
        75043951,
        75044489,
        75051254,
        75051555,
        75051898,
        75052282,
        75052487,
        75149887,
        75150044,
        75153769,
        75154298,
        75154355,
        75154386,
        75156050,
        75156527,
        75156701,
        75157173,
        75157611,
        75157712,
        75157718,
        75157764,
        75157841,
        75157892,
        75158059,
        75158099,
        75160951,
        75161085,
        75161194,
        75161309,
        75163926,
        75164314,
        75164402,
        75164636,
        75166111,
        75166285,
        75166672,
        75166816,
        75170282,
        75170863,
        75184690,
        75201450,
        75201742,
        75202440,
        75202739,
        75202965,
        75346158,
        75347371,
        75716109,
        75719750,
        75719830,
        75719836,
        75719859,
        75719875,
        75719886,
        75719964,
        75719990,
        75720205,
        75720388,
        75720440,
        75720591,
        75720838,
        75724328,
        75726244,
        75727993,
        75755381,
        75755602,
        75755608,
        75755623,
        75755684,
        75755720,
        75755772,
        75756167,
        75756219,
        75756250,
        75756254,
        75756258,
        75756268,
        75756392,
        75756403,
        75756545,
        75756824,
        75758275,
        75758553,
        75758609,
        75758614,
        75758620,
        75758696,
        75758756,
        75760678,
        75760702,
        75760741,
        75760830,
        75760862,
        75760916,
        75760974,
        75761000,
        75902737,
        75903637,
        75905617,
        75906084,
        75906243,
        75906766,
        75928792,
        75980918,
        75981083,
        75981089,
        75981107,
        75981128,
        75981379,
        75981389,
        75982116,
        75982607,
        75982828,
        75982870,
        75982916,
        75983160,
        75983869,
        75985330,
        75985347,
        75985372,
        75985829,
        75986031,
        75986377,
        75986468,
        75986593,
        75986603,
        75986783,
        75986909,
        75986932,
        75987045,
        75987140,
        75989076,
        75989477,
        75990182,
        75990329,
        75990432,
        75990723,
        75990767,
        75990791,
        75991686,
        75992291,
        75992628,
        75992961,
        76488055,
        76488376,
        76490019,
        76492861,
        76508873,
        76511281,
        76511364,
        76514451,
        76522766,
        76522803,
        76522962,
        76523001,
        76523101,
        76523694,
        76524854,
        76534275,
        76534570,
        76534616,
        76534647,
        76603145,
        76603225,
        76603933,
        76603960,
        76604505,
        76604716,
        76604727,
        76604774,
        76605095,
        76607117,
        76607950,
        76608240,
        76608249,
        76652356,
        76652495,
        76653589,
        76653626,
        76653630,
        76653640,
        76653647,
        76653652,
        76653659,
        76653672,
        76653692,
        76653700,
        76655475,
        76655759,
        76656059,
        76656859,
        76657175,
        76664916,
        76667269,
        76718211,
        76720778,
        76721076,
        76721113,
        76721657,
        76721701,
        76721976,
        76722620,
        76723176,
        76723790,
        76724455,
        76729012,
        76730574,
        76730737,
        76730839,
        76731002,
        76731059,
        76731077,
        76731097,
        76731109,
        76731190,
        76731212,
        76782195,
        76783758,
        76783895,
        76786553,
        76787313,
        76787708,
        76788205,
        76788412,
        76790298,
        76790469,
        76845719,
        76845755,
        76845758,
        76845760,
        76845762,
        76845890,
        76846922,
        76847515,
        76847682,
        76847706,
        76848065,
        76848081,
        76848093,
        76848147,
        76848212,
        76848217,
        76848307,
        76848399,
        76848715,
        76848777,
        76848873,
        76848883,
        76848906,
        76848910,
        76848920,
        76848982,
        76848992,
        76849295,
        76849301,
        76849309,
        76849411,
        76849429,
        76849616,
        76849622,
        76849633,
        76849761,
        76849840,
        76849919,
        76849937,
        76849965,
        76849987,
        76850005,
        76850018,
        76850033,
        76850251,
        76850334,
        76851947,
        76852007,
        76852064,
        76852083,
        76852097,
        76852205,
        76852261,
        76852275,
        76853368,
        76854492,
        76920201,
        76920221,
        76920991,
        76922969,
        76923795,
        76923808,
        76923815,
        76923823,
        76923832,
        76923925,
        76924892,
        76925283,
        76925988,
        77394485,
        77394562,
        77394614,
        77394726,
        77395259,
        77395315,
        77395319,
        77395441,
        77395547,
        77395687,
        77396304,
        77396327,
        77396355,
        77396381,
        77396392,
        77396431,
        77396473,
        77396474,
        77396500,
        77396540,
        77396595,
        77396641,
        77396733,
        77396791,
        77396870,
        77396905,
        77396955,
        77397181,
        77397903,
        77398078,
        77398604,
        77398698,
        77398707,
        77398726,
        77409621,
        77410405,
        77416149,
        77416464,
        77416690,
        77416841,
        77416876,
        77416954,
        77417629,
        77418218,
        77418548,
        77418575,
        77418609,
        77418698,
        77419683,
        77422638,
        77422705,
        77422979,
        77579282,
        77579291,
        77579739,
        77579751,
        77579764,
        77579865,
        77579884,
        77579924,
        77581800,
        77583268,
        77583843,
        77583973,
        77584013,
        77584036,
        77584057,
        77584089,
        77584287,
        77584518,
        77584624,
        77584718,
        77584832,
        77585198,
        77585237,
        77585257,
        77585275,
        77585300,
        77585301,
        77585330,
        77585353,
        77585354,
        77585377,
        77585388,
        77585432,
        77585497,
        77585606,
        77586743,
        77587191,
        77588275,
        77589146,
        77589276,
        77589452,
        77589551,
        77589618,
        77589638,
        77590249,
        77592360,
        77593980,
        77594667,
        77594737,
        77594933,
        77595357,
        77595969,
        77674906,
        77675003,
        77675172,
        77675542,
        77675561,
        77675778,
        77676143,
        77676232,
        77676286,
        77676317,
        77676342,
        77676367,
        77676404,
        77676445,
        77676467,
        77676543,
        77676572,
        77676642,
        77676655,
        77676729,
        77676790,
        77676807,
        77676809,
        77676882,
        77676945,
        77677051,
        77677075,
        77677188,
        77677198,
        77677225,
        77677246,
        77677290,
        77677307,
        77677344,
        77677356,
        77677363,
        77677400,
        77677405,
        77677408,
        77677411,
        77677436,
        77677471,
        77677577,
        77677754,
        77677781,
        77677903,
        77677958,
        77677995,
        77678026,
        77678241,
        77678348,
        77679483,
        77679806,
        77680030,
        77681217,
        77682719,
        77682760,
        77688251,
        77689731,
        77689806,
        77689915,
        77690103,
        77690160,
        77690265,
        77690424,
        77690559,
        77690676,
        77690775,
        77691043,
        77691228,
        77691355,
        77691873,
        77691932,
        77692491,
        77692718,
        77692927,
        77762793,
        77762824,
        77762850,
        77762882,
        77762918,
        77764028,
        77764291,
        77764731,
        77764925,
        77765039,
        77765256,
        77765842,
        77775468,
        77776456,
        77777395,
        77778793,
        77783152,
        77785388,
        77847523,
        77847608,
        77847867,
        77849246,
        77849339,
        77850376,
        77853002,
        77853040,
        77853259,
        77853893,
        77854511,
        77854963,
        77855312,
        77855755,
        77856138,
        77856681,
        77884522,
        77884549,
        77884584,
        77886260,
        77886469,
        77886731,
        77886808,
        77886827,
        77886845,
        77886862,
        77886863,
        77886896,
        77887013,
        77887106,
        77887317,
        77888782,
        77922544,
        77923077,
        77923190,
        77923408,
        77923686,
        77923700,
        77923710,
        77924011,
        77924040,
        77924056,
        77924319,
        77924642,
        77925560,
        77925625,
        77925688,
        77925740,
        77925752,
        77926116,
        77926133,
        77926266,
        77926590,
        77926776,
        77927076,
        77927405,
        77927464,
        77927595,
        77927645,
        77932252,
        77979868,
        77980519,
        77983140,
        77983169,
        77983251,
        77983474,
        77983501,
        78178205,
        78182299,
        78182817,
        78182982,
        78183117,
        78183154,
        78183275,
        78183913,
        78185952,
        78186039,
        78186056,
        78187335,
        78187844,
        78188149,
        78188242,
        78188422,
        78188451,
        78240960,
        78244661,
        78244807,
        78244934,
        78277707,
        78277971,
        78278747,
        78278783,
        78278797,
        78278833,
        78278837,
        78278863,
        78278865,
        78278866,
        78279187,
        78279229,
        78279242,
        78279246,
        78279255,
        78279263,
        78279533,
        78279632,
        78279708,
        78279830,
        78279842,
        78279887,
        78279910,
        78279937,
        78279948,
        78279956,
        78279960,
        78279976,
        78279985,
        78280006,
        78280008,
        78280017,
        78280052,
        78280100,
        78280110,
        78280200,
        78280260,
        78280270,
        78280290,
        78280351,
        78280357,
        78280362,
        78280599,
        78280621,
        78280654,
        78280737,
        78280872,
        78280943,
        78281063,
        78281112,
        78281304,
        78281446,
        78281452,
        78281464,
        78281714,
        78281844,
        78281950,
        78282000,
        78282021,
        78282042,
        78282083,
        78282121,
        78282137,
        78282353,
        78282365,
        78282397,
        78282475,
        78282493,
        78282501,
        78282502,
        78282594,
        78282680,
        78282687,
        78282692,
        78282795,
        78282837,
        78282911,
        78283765,
        78284191,
        78284197,
        78284373,
        78284584,
        78284642,
        78284784,
        78284805,
        78285138,
        78285297,
        78286241,
        78286741,
        78286887,
        78288110,
        78288494,
        78289707,
        78289802,
        78289885,
        78290652,
        78292007,
        78292483,
        78293351,
        78293365,
        78372726,
        78372761,
        78372767,
        78372798,
        78372805,
        78372815,
        78372821,
        78372838,
        78372880,
        78372918,
        78372925,
        78372976,
        78372996,
        78373012,
        78373023,
        78373034,
        78373071,
        78373319,
        78373344,
        78373367,
        78373396,
        78374463,
        78374602,
        78374821,
        78374852,
        78374872,
        78374887,
        78375506,
        78376215,
        78376370,
        78376430,
        78376633,
        78376687,
        78377004,
        78377620,
        78377759,
        78377800,
        78377811,
        78377827,
        78377854,
        78377893,
        78378375,
        78378425,
        78380306,
        78380331,
        78380723,
        78380761,
        78380803,
        78380842,
        78380845,
        78381150,
        78381160,
        78381395,
        78382301,
        78382478,
        78382550,
        78383007,
        78384886,
        78385924,
        78386902,
        78400388,
        78400481,
        78400750,
        78401050,
        78401691,
        78402537,
        78439002,
        78440563,
        78440858,
        78441090,
        78442485,
        78442495,
        78443177,
        78444274,
        78523228,
        78644530,
        78644554,
        78644559,
        78644585,
        78646408,
        78646650,
        78646754,
        78647064,
        78652734,
        78653075,
        78653093,
        78653111,
        78653264,
        78653804,
        78653813,
        78653833,
        78654130,
        78654157,
        78654172,
        78654253,
        78654996,
        78669155,
        78669168,
        78669227,
        78669248,
        78669272,
        78670495,
        78671875,
        78672229,
        78672245,
        78672299,
        78672346,
        78672363,
        78672977,
        78673082,
        78673484,
        78674013,
        78696315,
        78696697,
        78696798,
        78736080,
        78736097,
        78736122,
        78736145,
        78737117,
        78737184,
        78742741,
        78742785,
        78743279,
        78743524,
        78743531,
        78743538,
        78747673,
        78747678,
        78747696,
        78747724,
        78747748,
        78747776,
        78747819,
        78747852,
        78747885,
        78796866,
        78797290,
        78798274,
        78803056,
        78803809,
        78806765,
        78808905,
        78809092,
        78809150,
        78809244,
        78809321,
        78809496,
        78809634,
        78809925,
        78845022,
        78846411,
        78846589,
        78846745,
        78847025,
        78847395,
        79083679,
        79083907,
        79083938,
        79084076,
        79085727,
        79085890,
        79088464,
        79088477,
        79088526,
        79088748,
        79088989,
        79099491,
        79099846,
        79100088,
        79100893,
        79101062,
        79101145,
        79101267,
        79101296,
        79101446,
        79101697,
        79102216,
        79102366,
        79185457,
        79185807,
        79186063,
        79186780,
        79187320,
        79187971,
        79188288,
        79188361,
        79188884,
        79192195,
        79192491,
        79193548,
        79194596,
        79195151,
        79195716,
        79196633,
        79197409,
        79211280,
        79219165,
        79219352,
        79287794,
        79288293,
        79288357,
        79288465,
        79288677,
        79288765,
        79288781,
        79288833,
        79288853,
        79288866,
        79288877,
        79288895,
        79288917,
        79288929,
        79288956,
        79288962,
        79288972,
        79289007,
        79289354,
        79289839,
        79289876,
        79289888,
        79290476,
        79291178,
        79374943,
        79375307,
        79375697,
        79375713,
        79375968,
        79375986,
        79376090,
        79376807,
        79377134,
        79377165,
        79377538,
        79377742,
        79378417,
        79378429,
        79378434,
        79378446,
        79378542,
        79379043,
        79379062,
        79379086,
        79379158,
        79379174,
        79379189,
        79379201,
        79379824,
        79379884,
        79380372,
        79380494,
        79380661,
        79380699,
        79380784,
        79380788,
        79381303,
        79381427,
        79381721,
        79382864,
        79525629,
        79525730,
        79526339,
        79526360,
        79526363,
        79526393,
        79526406,
        79527349,
        79680563,
        79684046,
        79686969,
        79691850,
        79696464,
        79696556,
        79696633,
        79697885,
        79837446,
        79837538,
        79837695,
        79838796,
        79839277,
        79839355,
        79843077,
        79843102,
        79843133,
        79843149,
        79843376,
        79843456,
        79845341,
        79846591,
        79847533,
        79847643,
        79848921,
        79849013,
        79849047,
        79849246,
        79849348,
        79849728,
        79915484,
        79915785,
        79916116,
        79916309,
        79917431,
        79979700,
        79981200,
        79981299,
        79981689,
        79989334,
        79997729,
        79998592,
        79998638,
        79998719,
        80000633,
        80000879,
        80001445,
        80002853,
        80009209,
        80152759,
        80153316,
        80153355,
        80153364,
        80153385,
        80153387,
        80153402,
        80153423,
        80153428,
        80153434,
        80153525,
        80153547,
        80153556,
        80153557,
        80153567,
        80153587,
        80153595,
        80153601,
        80153606,
        80153610,
        80153616,
        80153617,
        80153642,
        80153669,
        80153673,
        80153682,
        80153695,
        80153704,
        80153707,
        80153815,
        80154152,
        80154645,
        80154687,
        80154708,
        80154734,
        80155394,
        80155427,
        80155524,
        80155527,
        80155565,
        80156115,
        80156219,
        80156950,
        80156960,
        80156973,
        80157051,
        80157070,
        80158731,
        80159228,
        80164736,
        80164744,
        80164873,
        80165074,
        80165401,
        80230700,
        80231384,
        80231396,
        80231411,
        80231424,
        80232186,
        80232368,
        80232514,
        80232602,
        80233478,
        80233489,
        80233551,
        80233568,
        80233735,
        80233766,
        80233771,
        80233786,
        80233794,
        80233805,
        80233812,
        80234165,
        80234198,
        80235028,
        80236155,
        80236379,
        80236632,
        80236833,
        80236866,
        80236964,
        80237025,
        80238711,
        80239672,
        80239755,
        80239874,
        80240526,
        80240683,
        80240816,
        80241013,
        80241140,
        80241213,
        80241255,
        80241288,
        80241435,
        80241587,
        80241806,
        80241836,
        80241902,
        80242349,
        80242377,
        80242417,
        80242420,
        80242435,
        80242656,
        80242780,
        80243590,
        80243917,
        80243944,
        80244046,
        80244081,
        80244104,
        80244432,
        80244461,
        80244472,
        80244755,
        80244853,
        80246037,
        80246055,
        80246268,
        80246474,
        80246627,
        80247115,
        80248018,
        80248130,
        80248976,
        80249133,
        80249547,
        80249689,
        80251960,
        80282332,
        80284190,
        80284197,
        80284300,
        80284356,
        80284367,
        80284388,
        80284487,
        80284583,
        80284617,
        80284634,
        80284681,
        80284729,
        80284826,
        80284882,
        80286457,
        80286954,
        80287735,
        80287885,
        80289376,
        80289386,
        80289403,
        80291281,
        80291456,
        80291473,
        80291632,
        80291884,
        80291994,
        80292212,
        80292871,
        80293194,
        80330777,
        80330946,
        80330951,
        80330952,
        80330955,
        80331591,
        80335258,
        80450565,
        80450809,
        80452967,
        80454854,
        80582379,
        80582387,
        80582393,
        80582753,
        80582826,
        80583019,
        80583233,
        80583247,
        80586728,
        80586965,
        80588229,
        80588348,
        80590366,
        80591368,
        80592629,
        80744993,
        80748928,
        80749096,
        80749799,
        80750999,
        80752532,
        80753061,
        80755809,
        80756465,
        80759716,
        80760093,
        80760756,
        80761051,
        80761261,
        80761433,
        80761466,
        80763059,
        80763261,
        80764343,
        80764536,
        80764733,
        80765791,
        80766015,
        80766078,
        80766179,
        80766222,
        80766358,
        80766454,
        80766510,
        80766547,
        80766879,
        80766927,
        80767140,
        80767178,
        80767324,
        80767487,
        80767571,
        80771179,
        80772653,
        80773296,
        80773720,
        80777503,
        80778289,
        80782430,
        80782512,
        80782671,
        80783576,
        80784276,
        80784464,
        80784585,
        80784976,
        80785096,
        80923126,
        80924140,
        80924177,
        80924849,
        80930292,
        80930525,
        80930741,
        80931142,
        80931560,
        80931806,
        80932206,
        80933676,
        80936280,
        80936994,
        80937076,
        80937645,
        80937986,
        80938300,
        80938429,
        80938609,
        80938791,
        80938949,
        80944125,
        80948345,
        81057978,
        81059866,
        81060150,
        81062952,
        81063060,
        81063116,
        81063152,
        81063162,
        81064477,
        81064807,
        81064819,
        81064822,
        81064848,
        81064855,
        81064869,
        81064874,
        81064878,
        81064888,
        81064896,
        81166671,
        81183815,
        81184605,
        81184841,
        81184898,
        81185429,
        81185511,
        81185631,
        81185666,
        81185769,
        81186520,
        81186600,
        81186800,
        81188564,
        81188581,
        81190946,
        81231575,
        81232310,
        81233965,
        81234218,
        81234988,
        81235016,
        81235165,
        81236898,
        81236915,
        81237892,
        81242590,
        81262770,
        81262869,
        81262891,
        81262921,
        81323284,
        81323336,
        81323403,
        81323422,
        81323663,
        81323717,
        81323728,
        81323754,
        81323761,
        81323771,
        81323775,
        81323785,
        81323791,
        81323798,
        81323803,
        81323808,
        81323833,
        81323896,
        81323964,
        81324040,
        81324132,
        81324179,
        81324737,
        81324761,
        81324884,
        81325356,
        81325368,
        81326204,
        81326300,
        81328047,
        81328655,
        81331137,
        81338103,
        81413770,
        81413788,
        81413814,
        81413838,
        81414244,
        81415233,
        81415315,
        81415323,
        81415345,
        81416778,
        81417357,
        81417521,
        81483624,
        81483694,
        81483820,
        81483879,
        81495179,
        81495192,
        81495220,
        81495312,
        81495354,
        81498384,
        81560176,
        81560189,
        81560194,
        81560208,
        81560215,
        81560226,
        81560254,
        81560262,
        81560272,
        81560278,
        81560302,
        81560318,
        81560876,
        81561338,
        81561366,
        81561388,
        81561586,
        81561705,
        81561914,
        81562430,
        81562444,
        81562446,
        81562448,
        81562633,
        81563529,
        81563530,
        81563535,
        81563541,
        81563574,
        81563669,
        81563675,
        81564722,
        81565053,
        81565178,
        81565354,
        81565747,
        81566344,
        81566352,
        81566778,
        81566795,
        81567034,
        81567303,
        81567392,
        81567411,
        81567414,
        81567420,
        81567443,
        81567718,
        81568270,
        81568545,
        81568812,
        81569057,
        81569064,
        81569068,
        81569081,
        81569085,
        81569147,
        81569254,
        81569628,
        81569869,
        81570309,
        81570427,
        81570665,
        81570734,
        81574593,
        81574950,
        81681115,
        81681281,
        81681338,
        81681351,
        81681422,
        81681455,
        81687133,
        81692621,
        81863237,
        81863280,
        81863403,
        81863455,
        81863490,
        81863587,
        81863635,
        81866459,
        81890158,
        81891786,
        81894518,
        82072032,
        82133791,
        82195691,
        82200470,
        82249916,
        82249958,
        82249970,
        82250999,
        82251457,
        82255237,
        82255757,
        82256666,
        82256889,
        82257113,
        82257962,
        82258205,
        82271638,
        82272410,
        82330967,
        82332280,
        82332704,
        82332761,
        82332953,
        82334035,
        82334182,
        82335113,
        82335148,
        82335196,
        82335239,
        82335695,
        82335760,
        82335790,
        82335793,
        82335807,
        82336314,
        82336322,
        82337873,
        82753632,
        82753789,
        82753826,
        82753842,
        82754926,
        82755104,
        82755226,
        82755420,
        82825161,
        82825209,
        82825259,
        82825981,
        82826880,
        82905099,
        82905810,
        82905815,
        82905839,
        82905843,
        82905848,
        82905855,
        82905889,
        82907096,
        82907323,
        82907729,
        82907838,
        82907959,
        82908063,
        82908154,
        82909320,
        82950231,
        82950406,
        82950885,
        82950936,
        82950987,
        82951096,
        82951120,
        82951193,
        82951244,
        82951320,
        82951351,
        82955772,
        82955902,
        82956132,
        82957000,
        82957206,
        82957430,
        82957474,
        82957523,
        83044547,
        83048157,
        83050175,
        83056543,
        83056877,
        83057328,
        83108455,
        83108470,
        83109734,
        83109860,
        83110193,
        83110222,
        83110646,
        83110690,
        83111025,
        83111134,
        83113169,
        83113544,
        83113769,
        83114368,
        83193114,
        83193719,
        83193754,
        83193785,
        83193828,
        83193841,
        83193882,
        83193893,
        83193905,
        83194035,
        83195534,
        83195544,
        83195576,
        83195609,
        83195902,
        83195952,
        83196271,
        83196319,
        83196372,
        83196654,
        83196705,
        83196749,
        83196790,
        83196834,
        83196879,
        83197011,
        83197164,
        83197359,
        83201536,
        83201695,
        83202262,
        83202918,
        83226869,
        83227103,
        83227857,
        83228006,
        83228147,
        83574351,
        83574360,
        83580142,
        83783581,
        83785233,
        83789343,
        83791165,
        83799693,
        83799774,
        83800073,
        83800278,
        83800412,
        83801069,
        83802284,
        83803987,
        83804054,
        83805518,
        83805884,
        83806348,
        83806482,
        83810325,
        83810565,
        83812413,
        83813585,
        83813767,
        83813926,
        83814799,
        83815350,
        83815546,
        83816760,
        83817487,
        83817735,
        83819193,
        83819896,
        83820581,
        83823610,
        83824558,
        83952196,
        83952204,
        83952245,
        83952250,
        83952254,
        83953250,
        83953268,
        83953335,
        83953615,
        83953628,
        83953761,
        83953792,
        83953808,
        83953835,
        83953907,
        83954459,
        83954534,
        83954580,
        83955069,
        83955447,
        83955464,
        83955598,
        83955617,
        83955741,
        83955816,
        83956241,
        83956535,
        83956745,
        83956882,
        83957002,
        83957128,
        83957149,
        83957232,
        83957293,
        83957308,
        83957336,
        83957391,
        83957540,
        83957573,
        83957605,
        83957659,
        83957796,
        83958051,
        83958129,
        83958140,
        83958283,
        83958314,
        83958374,
        83958395,
        83958408,
        83958846,
        83959411,
        83959851,
        83961231,
        83964194,
        83965026,
        83967181,
        83970062,
        83981650,
        83981886,
        83983183,
        83985893,
        83987815,
        83995864,
        83996413,
        83996990,
        83997185,
        83997261,
        84078796,
        84079110,
        84079562,
        84080477,
        84080542,
        84080557,
        84081483,
        84081528,
        84083466,
        84083913,
        84084094,
        84084214,
        84204040,
        84208033,
        84283517,
        84283532,
        84283597,
        84283767,
        84284018,
        84284118,
        84284126,
        84284138,
        84284215,
        84284219,
        84284268,
        84284450,
        84284483,
        84284488,
        84284508,
        84284911,
        84284919,
        84284935,
        84286634,
        84286642,
        84286644,
        84286653,
        84286662,
        84286687,
        84286688,
        84286698,
        84286713,
        84286728,
        84286730,
        84286733,
        84286739,
        84286801,
        84286906,
        84287018,
        84287034,
        84287051,
        84287052,
        84287060,
        84287065,
        84287087,
        84287092,
        84287112,
        84287139,
        84287148,
        84287157,
        84287177,
        84287189,
        84287223,
        84287234,
        84287250,
        84287294,
        84287297,
        84287340,
        84287741,
        84289233,
        84294568,
        84366128,
        84366342,
        84368788,
        84369468,
        84369603,
        84369980,
        84370012,
        84370269,
        84370280,
        84370310,
        84370323,
        84370339,
        84370367,
        84370503,
        84370550,
        84373618,
        84373761,
        84373774,
        84375451,
        84375806,
        84375977,
        84571928,
        84571965,
        84576229,
        84576550,
        84576590,
        84576693,
        84577233,
        84577736,
        84578176,
        84582808,
        84583420,
        84583499,
        84584093,
        84586108,
        84586158,
        84586193,
        84586250,
        84587321,
        84587448,
        84587472,
        84587561,
        84657147,
        84657161,
        84657214,
        84662174,
        84662645,
        84662712,
        84662730,
        84662847,
        84663206,
        84665881,
        84665979,
        84844502,
        84844512,
        84844521,
        84844667,
        84846301,
        84848790,
        84849013,
        84849269,
        84849780,
        84849817,
        84849828,
        84850065,
        84850096,
        84850296,
        84850624,
        84850632,
        84850736,
        84850764,
        84850842,
        84850875,
        84851413,
        84851607,
        84853492,
        84854241,
        84855351,
        84856603,
        84922802,
        84922901,
        84923578,
        84924097,
        84932576,
        84939887,
        84939936,
        84940975,
        84941346,
        84941390,
        84941479,
        84941713,
        84941846,
        84948265,
        84952143,
        84960192,
        84960349,
        84960413,
        85062687,
        85063331,
        85069159,
        85069263,
        85069353,
        85072612,
        85072851,
        85073250,
        85074086,
        85076481,
        85077162,
        85077868,
        85078063,
        85079171,
        85079271,
        85079368,
        85079467,
        85079565,
        85079899,
        85080322,
        85080597,
        85080712,
        85080829,
        85082749,
        85082772,
        85082928,
        85082979,
        85082991,
        85083001,
        85083023,
        85083179,
        85083343,
        85083553,
        85083578,
        85083582,
        85083604,
        85083623,
        85083642,
        85083668,
        85083690,
        85083786,
        85083953,
        85083980,
        85084072,
        85084244,
        85084282,
        85084500,
        85084530,
        85091995,
        85094856,
        85098117,
        85101743,
        85102011,
        85103478,
        85103903,
        85104146,
        85112454,
        85119901,
        85119951,
        85119987,
        85120150,
        85120214,
        85120413,
        85120456,
        85120497,
        85120839,
        85120938,
        85316284,
        85316290,
        85316837,
        85316865,
        85316888,
        85317351,
        85318722,
        85319851,
        85320264,
        85321405,
        85321443,
        85321547,
        85321613,
        85321710,
        85324545,
        85327690,
        85327867,
        85328086,
        85328151,
        85396611,
        85396709,
        85396759,
        85396885,
        85396907,
        85397173,
        85397183,
        85397396,
        85398117,
        85398229,
        85398306,
        85405504,
        85407579,
        85409156,
        85409232,
        85412120,
        85417241,
        85418660,
        85421236,
        85421606,
        85422448,
        85422488,
        85423161,
        85424800,
        85425546,
        85519761,
        85530946,
        85532014,
        85532973,
        85533147,
        85533707,
        85536559,
        85536594,
        85536653,
        85537308,
        85537322,
        85537336,
        85537379,
        85537394,
        85659308,
        85662163,
        85662472,
        85662483,
        85662658,
        85662838,
        85663035,
        85663285,
        85663500,
        85663635,
        85663645,
        85663654,
        85663698,
        85663722,
        85663751,
        85663755,
        85663767,
        85663773,
        85663776,
        85663782,
        85663793,
        85663800,
        85795376,
        85795407,
        85795424,
        85795477,
        85795484,
        85923544,
        85923680,
        85923718,
        85927043,
        85927733,
        85939301,
        85943923,
        85944056,
        85944701,
        85944993,
        85945051,
        86048405,
        86048440,
        86050637,
        86050960,
        86050998,
        86051168,
        86051420,
        86051477,
        86051486,
        86051804,
        86051913,
        86052210,
        86052252,
        86052460,
        86052982,
        86052985,
        86052992,
        86052997,
        86053003,
        86053034,
        86053064,
        86053087,
        86053107,
        86053129,
        86053302,
        86053631,
        86053848,
        86053885,
        86055230,
        86055457,
        86055671,
        86062938,
        86063013,
        86063082,
        86063231,
        86063300,
        86068932,
        86070912,
        86072749,
        86074358,
        86080033,
        86094358,
        86120692,
        86120732,
        86120874,
        86120960,
        86122493,
        86122509,
        86122523,
        86122682,
        86123931,
        86124186,
        86124539,
        86124731,
        86124749,
        86124758,
        86124771,
        86124782,
        86124824,
        86126272,
        86126346,
        86126541,
        86126803,
        86126866,
        86126877,
        86126896,
        86126907,
        86126919,
        86126937,
        86126971,
        86127116,
        86129408,
        86136816,
        86136859,
        86137170,
        86179664,
        86180101,
        86180248,
        86180339,
        86181917,
        86182179,
        86182359,
        86186276,
        86186585,
        86186647,
        86186981,
        86198434,
        86199297,
        86209738,
        86217653,
        86218536,
        86243328,
        86292857,
        86295138,
        86333862,
        86335719,
        86336882,
        86337747,
        86337933,
        86339221,
        86340903,
        86341060,
        86341492,
        86351465,
        86499280,
        86499411,
        86499640,
        86500232,
        86502310,
        86502449,
        86504282,
        86504463,
        86504857,
        86506300,
        86506535,
        86515425,
        86516473,
        86516518,
        86517340,
        86517518,
        86520130,
        86520898,
        86521268,
        86521932,
        86522207,
        86522553,
        86523495,
        86568847,
        86568887,
        86568998,
        86569136,
        86569243,
        86569454,
        86569494,
        86569537,
        86570657,
        86573128,
        86574115,
        86575815,
        86578258,
        86580533,
        86581653,
        86582494,
        86584777,
        86584930,
        86585561,
        86585730,
        86585825,
        86585862,
        86586241,
        86586479,
        86586555,
        86586881,
        86587019,
        86587225,
        86589435,
        86591552,
        86592459,
        86593375,
        86594012,
        86595202,
        86595901,
        86596022,
        86619785,
        86619881,
        86619955,
        86620029,
        86620088,
        86655293,
        86657530,
        86657574,
        86659664,
        86663656,
        86663738,
        86663792,
        86664072,
        86664320,
        86664469,
        86664559,
        86664806,
        86664858,
        86664994,
        86665023,
        86665315,
        86665349,
        86665462,
        86666585,
        86679470,
        86680285,
        86687505,
        86688753,
        86689049,
        86711427,
        86711693,
        86711902,
        86712117,
        86712246,
        86712345,
        86712460,
        86712881,
        86713199,
        86713740,
        86713885,
        86713984,
        86714070,
        86714324,
        86714409,
        86846042,
        86846411,
        86847017,
        86848376,
        86851713,
        86851812,
        86853418,
        86854674,
        86866180,
        86866277,
        86866441,
        86866587,
        86866787,
        86867348,
        87081783,
        87083544,
        87083771,
        87083884,
        87084604,
        87087611,
        87088066,
        87088242,
        87088775,
        87089148,
        87089592,
        87089949,
        87098217,
        87098446,
        87099348,
        87099983,
        87101390,
        87101649,
        87101716,
        87102315,
        87198460,
        87198776,
        87199472,
        87200220,
        87204086,
        87212697,
        87215350,
        87216510,
        87216679,
        87216796,
        87217023,
        87217186,
        87217335,
        87219503,
        87230507,
        87232206,
        87234714,
        87261026,
        87261128,
        87292347,
        87292367,
        87292474,
        87292477,
        87292505,
        87292660,
        87292697,
        87293141,
        87293191,
        87293398,
        87293459,
        87293649,
        87293935,
        87293938,
        87293940,
        87293949,
        87293969,
        87293985,
        87293992,
        87293998,
        87294003,
        87294014,
        87294048,
        87294063,
        87294070,
        87294081,
        87294098,
        87294122,
        87294148,
        87294153,
        87294158,
        87294166,
        87294173,
        87294243,
        87294249,
        87294274,
        87294286,
        87294299,
        87294787,
        87294825,
        87294842,
        87294846,
        87294868,
        87294882,
        87294896,
        87294908,
        87294912,
        87294930,
        87294936,
        87294984,
        87294999,
        87295011,
        87295025,
        87295047,
        87295048,
        87295063,
        87295069,
        87295070,
        87295077,
        87295085,
        87295102,
        87295119,
        87295146,
        87295157,
        87295206,
        87295213,
        87295221,
        87295230,
        87295248,
        87295253,
        87295258,
        87295261,
        87295375,
        87295428,
        87295531,
        87295819,
        87295829,
        87298984,
        87299996,
        87300134,
        87300283,
        87300631,
        87300695,
        87300756,
        87300821,
        87300879,
        87302230,
        87310296,
        87310841,
        87310930,
        87323857,
        87324017,
        87325070,
        87325127,
        87326530,
        87347877,
        87351311,
        87355119,
        87355218,
        87355967,
        87356444,
        87356843,
        87357223,
        87357557,
        87376107,
        87377827,
        87378037,
        87378672,
        87379414,
        87411309,
        87411347,
        87411694,
        87411949,
        87513746,
        87570061,
        87570139,
        87570146,
        87570179,
        87570205,
        87570216,
        87570223,
        87570249,
        87570258,
        87570373,
        87570375,
        87570381,
        87570466,
        87570477,
        87570483,
        87570497,
        87570542,
        87570687,
        87570775,
        87570796,
        87570846,
        87570861,
        87570862,
        87570879,
        87570895,
        87570924,
        87570987,
        87571015,
        87572897,
        87573570,
        87575240,
        87575795,
        87577434,
        87637733,
        87638174,
        87638289,
        87639179,
        87639220,
        87639264,
        87639449,
        87639606,
        87639726,
        87639747,
        87640304,
        87640692,
        87641271,
        87643632,
        87643724,
        87683801,
        87683888,
        87684082,
        87710040,
        87731117,
        87731581,
        87731669,
        87731746,
        87846454,
        87846474,
        87846512,
        87846608,
        87846610,
        87846621,
        87846634,
        87846658,
        87846715,
        87846770,
        87846781,
        87846869,
        87846918,
        87846937,
        87846946,
        87846973,
        87846996,
        87847051,
        87847057,
        87847084,
        87847090,
        87847128,
        87847147,
        87847154,
        87847176,
        87847281,
        87847335,
        87847350,
        87847473,
        87847482,
        87847495,
        87847650,
        87848977,
        87851141,
        87851164,
        87851296,
        87851367,
        87851387,
        87851406,
        87851415,
        87886170,
        87887640,
        87888050,
        87889770,
        87889960,
        87968080,
        87968123,
        87968199,
        87968333,
        87968487,
        87968657,
        87968845,
        87968973,
        87969022,
        87969121,
        87969193,
        87969295,
        87969331,
        87969351,
        87969402,
        87969447,
        87969526,
        87969561,
        87969608,
        87969609,
        87969680,
        87969871,
        87970072,
        87970100,
        87970109,
        87970352,
        87970406,
        87970506,
        87970616,
        87970788,
        87970833,
        87970951,
        87970971,
        87970972,
        87971230,
        87985456,
        87987678,
        87992974,
        88020145,
        88065622,
        88066075,
        88109885,
        88109934,
        88110243,
        88110285,
        88136148,
        88136158,
        88136313,
        88138716,
        88138726,
        88138740,
        88138749,
        88138775,
        88138828,
        88139320,
        88139356,
        88139381,
        88139411,
        88144504,
        88148534,
        88158370,
        88160508,
        88160820,
        88162672,
        88232724,
        88349972,
        88353947,
        88353950,
        88353961,
        88353966,
        88353975,
        88353994,
        88354042,
        88354207,
        88354395,
        88354433,
        88358748,
        88358781,
        88454148,
        88454843,
        88454848,
        88454865,
        88458148,
        88458234,
        88464296,
        88466817,
        88487800,
        88487898,
        88488141,
        88488208,
        88488339,
        88488381,
        88488426,
        88488506,
        88488590,
        88511476,
        88512193,
        88512257,
        88512746,
        88512750,
        88512753,
        88513976,
        88513984,
        88514094,
        88514137,
        88514138,
        88514149,
        88514162,
        88514168,
        88514253,
        88514748,
        88516935,
        88517064,
        88517744,
        88519770,
        88521491,
        88521541,
        88524068,
        88547660,
        88871504,
        88871513,
        88871518,
        88871527,
        88871607,
        88871635,
        88871791,
        88872793,
        88874993,
        88875188,
        88875234,
        88875269,
        88875287,
        88875292,
        88875612,
        88875884,
        88876091,
        88876122,
        88876619,
        88876753,
        88876920,
        88877177,
        88877737,
        88877836,
        88880839,
        89124625,
        89125072,
        89125334,
        89125701,
        89127135,
        89127330,
        89127824,
        89128613,
        89128925,
        89129021,
        89129464,
        89130461,
        89130652,
        89131352,
        89131845,
        89132258,
        89132259,
        89132917,
        89133478,
        89133490,
        89133940,
        89134152,
        89134831,
        89134870,
        89135072,
        89135403,
        89135706,
        89135815,
        89136362,
        89136529,
        89136874,
        89137783,
        89138695,
        89139239,
        89139731,
        89139758,
        89140201,
        89140263,
        89140679,
        89142155,
        89143012,
        89143193,
        89143425,
        89148210,
        89153136,
        89154810,
        89160605,
        89175368,
        89180052,
        89180277,
        89180546,
        89180638,
        89181636,
        89183333,
        89184329,
        89184622,
        89189440,
        89193355,
        89195596,
        89195913,
        89196293,
        89196651,
        89196938,
        89197042,
        89197215,
        89197526,
        89198470,
        89198708,
        89199090,
        89199404,
        89200326,
        89200550,
        89201807,
        89224989,
        89225424,
        89225831,
        89270345,
        89354030,
        89354567,
        89362493,
        89366861,
        89367400,
        89517233,
        89517531,
        89518213,
        89518698,
        89518844,
        89519155,
        89521349,
        89526202,
        89526444,
        89526490,
        89527965,
        89528372,
        89529049,
        89529215,
        89529289,
        89530866,
        89530928,
        89531043,
        89531236,
        89531464,
        89531686,
        89532193,
        89532735,
        89532845,
        89532918,
        89532985,
        89539829,
        89548389,
        89596939,
        89597048,
        89597358,
        89597712,
        89597735,
        89598073,
        89598749,
        89598953,
        89599036,
        89599075,
        89599097,
        89599114,
        89599156,
        89599168,
        89599192,
        89599403,
        89601423,
        89601710,
        89601748,
        89601751,
        89601760,
        89601764,
        89601772,
        89601779,
        89601802,
        89601810,
        89601815,
        89601825,
        89601832,
        89602193,
        89603615,
        89603623,
        89603775,
        89605695,
        89608764,
        89609195,
        89610858,
        89611031,
        89611225,
        89611230,
        89611484,
        89611488,
        89611529,
        89611629,
        89611777,
        89611842,
        89612041,
        89612181,
        89612209,
        89612353,
        89612403,
        89612738,
        89613904,
        89614016,
        89614165,
        89614455,
        89614593,
        89614702,
        89614884,
        89615172,
        89616073,
        89616208,
        89616250,
        89616275,
        89616296,
        89616359,
        89616580,
        89616680,
        89616861,
        89616989,
        89617073,
        89617189,
        89617222,
        89617311,
        89617420,
        89617539,
        89617865,
        89687130,
        89687177,
        89687237,
        89687246,
        89687296,
        89687301,
        89687318,
        89687783,
        89687867,
        89688116,
        89688267,
        89688332,
        89688367,
        89689243,
        89689275,
        89689546,
        89689580,
        89689663,
        89689712,
        89689767,
        89689785,
        89689816,
        89689856,
        89690323,
        89690347,
        89690512,
        89690563,
        89691513,
        89691529,
        89691549,
        89691617,
        89691631,
        89692125,
        89692155,
        89692167,
        89692208,
        89692221,
        89693242,
        89695801,
        89695849,
        89696129,
        89696539,
        89696785,
        89696946,
        89697081,
        89697200,
        89697268,
        89697338,
        89697422,
        89697903,
        89736282,
        89736294,
        89736295,
        89736297,
        89736303,
        89736362,
        89752971,
        89753010,
        89753019,
        89753033,
        89753667,
        89753679,
        89753700,
        89754886,
        89754895,
        89754934,
        89754950,
        89754972,
        89755056,
        89756029,
        89756126,
        89756534,
        89756540,
        89756641,
        89756736,
        89756915,
        89756934,
        89756936,
        89756941,
        89756952,
        89756958,
        89756983,
        89756993,
        89757007,
        89757016,
        89757031,
        89757278,
        89757563,
        89757594,
        89757618,
        89757633,
        89757751,
        89758647,
        89758731,
        89759074,
        89759138,
        89759171,
        89759199,
        89759223,
        89759238,
        89759252,
        89849967,
        89853932,
        89853944,
        89853966,
        89854471,
        89854523,
        89854763,
        89854780,
        89854801,
        89854811,
        89854935,
        89855030,
        89855056,
        89855080,
        89855234,
        89855823,
        89858857,
        89858940,
        89858966,
        89859259,
        89859265,
        89859320,
        89859380,
        89859442,
        89859668,
        89859732,
        89859745,
        89859792,
        89860293,
        89861060,
        89861346,
        89861490,
        89861680,
        89861852,
        89862094,
        89862120,
        89862499,
        89862783,
        89862854,
        89862995,
        89863113,
        89863325,
        89863360,
        89863452,
        89863583,
        89863595,
        89863668,
        89863677,
        89863806,
        89863866,
        89864198,
        89864430,
        89865489,
        89865524,
        89865578,
        89865651,
        89865768,
        89866187,
        89866445,
        89866555,
        89866627,
        89866669,
        89866800,
        89868627,
        89868821,
        89869024,
        89918371,
        89918378,
        89918381,
        89918385,
        89918393,
        89918402,
        89918805,
        89919130,
        89919144,
        89919186,
        89919191,
        89919201,
        89919252,
        89919259,
        89919270,
        89919282,
        89920141,
        89920187,
        89920251,
        89920520,
        89920792,
        89920834,
        89922205,
        89925514,
        89925777,
        89925962,
        89926021,
        89926464,
        89926543,
        89926603,
        89926681,
        89926812,
        89926932,
        89927003,
        89927706,
        89928951,
        89929003,
        89941862,
        89942526,
        89942556,
        89943027,
        89943247,
        89943464,
        89947845,
        89948029,
        89948388,
        89948558,
        89948721,
        89977230,
        89977258,
        89977372,
        89977395,
        89977410,
        89977471,
        89977485,
        89977521,
        89977601,
        89977622,
        89977660,
        89977673,
        89977802,
        89977829,
        89977844,
        89977897,
        89977911,
        89977923,
        89978188,
        89978222,
        89978244,
        89978370,
        89978386,
        89979064,
        89979304,
        89985327,
        89986359,
        89986367,
        89986376,
        89986394,
        89986401,
        89986411,
        89986449,
        89988460,
        89988471,
        89988482,
        89988495,
        89988877,
        89989315,
        89989727,
        89995052,
        90014272,
        90016359,
        90063651,
        90063661,
        90063671,
        90063682,
        90063802,
        90063813,
        90063819,
        90063823,
        90063834,
        90063884,
        90064035,
        90065320,
        90067595,
        90067619,
        90068184,
        90070828,
        90071648,
        90071843,
        90072048,
        90072120,
        90072290,
        90072358,
        90072437,
        90072799,
        90076339,
        90082499,
        90082880,
        90084184,
        90092053,
        90097172,
        90100041,
        90150088,
        90156975,
        90158298,
        90160447,
        90160780,
        90162430,
        90232283,
        90232405,
        90232623,
        90235890,
        90235913,
        90236087,
        90236159,
        90236382,
        90236594,
        90236721,
        90236771,
        90238386,
        90238411,
        90238418,
        90238461,
        90238471,
        90238477,
        90238493,
        90238497,
        90238503,
        90238504,
        90238554,
        90238660,
        90238669,
        90238681,
        90238682,
        90238734,
        90238761,
        90238789,
        90238818,
        90238913,
        90239073,
        90239101,
        90239153,
        90239185,
        90239198,
        90239241,
        90239520,
        90239592,
        90239622,
        90239673,
        90239694,
        90239715,
        90239777,
        90239870,
        90239904,
        90239946,
        90242018,
        90242221,
        90243712,
        90246318,
        90247156,
        90248636,
        90249973,
        90256392,
        90257435,
        90257514,
        90259648,
        90278579,
        90291334,
        90298671,
        90300355,
        90300822,
        90301386,
        90303430,
        90304937,
        90306320,
        90310830,
        90311048,
        90313091,
        90313736,
        90314890,
        90316283,
        90319388,
        90407879,
        90408015,
        90408183,
        90408837,
        90409272,
        90409303,
        90410016,
        90410423,
        90411167,
        90411590,
        90413674,
        90413704,
        90415476,
        90415556,
        90415611,
        90416639,
        90416888,
        90417562,
        90419753,
        90419966,
        90430462,
        90430631,
        90438571,
        90468550,
        90472939,
        90472959,
        90472970,
        90472982,
        90473040,
        90473079,
        90473093,
        90473101,
        90473186,
        90473192,
        90473199,
        90552480,
        90552481,
        90552954,
        90553464,
        90553524,
        90553527,
        90553537,
        90553555,
        90553557,
        90553568,
        90553589,
        90553595,
        90553614,
        90553618,
        90553633,
        90553638,
        90553644,
        90553648,
        90553664,
        90553671,
        90553700,
        90553703,
        90553708,
        90553719,
        90553724,
        90564802,
        90564812,
        90564816,
        90565207,
        90565214,
        90565219,
        90565226,
        90565234,
        90565241,
        90565244,
        90565263,
        90565270,
        90565281,
        90565286,
        90565293,
        90565328,
        90565332,
        90565343,
        90567153,
        90567159,
        90567166,
        90567184,
        90567193,
        90570391,
        90570413,
        90570600,
        90570602,
        90570604,
        90570644,
        90570646,
        90570652,
        90570655,
        90571359,
        90571964,
        90572143,
        90572204,
        90573741,
        90576255,
        90610745,
        90610895,
        90611051,
        90611226,
        90611737,
        90611840,
        90612912,
        90612995,
        90646363,
        90646369,
        90646374,
        90646420,
        90646434,
        90646459,
        90646493,
        90646513,
        90646518,
        90646527,
        90646535,
        90646543,
        90646821,
        90646900,
        90646990,
        90647499,
        90648633,
        90651031,
        90651258,
        90654087,
        90654486,
        90654754,
        90654827,
        90654923,
        90655036,
        90655112,
        90655193,
        90655319,
        90655343,
        90655367,
        90655550,
        90655617,
        90655671,
        90655796,
        90655837,
        90655906,
        90655930,
        90656069,
        90656332,
        90656484,
        90657321,
        90658019,
        90659351,
        90659749,
        90661566,
        90667840,
        90668064,
        90668789,
        90669693,
        90669926,
        90670594,
        90670773,
        90672426,
        90689202,
        90729125,
        90729624,
        90729659,
        90729686,
        90729833,
        90729929,
        90730741,
        90731683,
        90731920,
        90732046,
        90732591,
        90732690,
        90733827,
        90734119,
        90734132,
        90734553,
        90734585,
        90735257,
        90735743,
        90736427,
        90736724,
        90736754,
        90737644,
        90737746,
        90742124,
        90742137,
        90742156,
        90742174,
        90742188,
        90742209,
        90742224,
        90742247,
        90742296,
        90742316,
        90742349,
        90743812,
        90743864,
        90743894,
        90743956,
        90743984,
        90744058,
        90744274,
        90744388,
        90745056,
        90747673,
        90747742,
        90747904,
        90748643,
        90750772,
        90752284,
        90765055,
        90921675,
        90922020,
        90922055,
        90922199,
        90922864,
        90922877,
        90926015,
        90926058,
        90926188,
        90926226,
        90926338,
        90926607,
        90926629,
        90926657,
        90932915,
        90933461,
        90933842,
        90934129,
        90934425,
        90934974,
        90935484,
        90935611,
        90935703,
        90936768,
        90937858,
        90938174,
        90938285,
        90938389,
        90938612,
        90938682,
        91053592,
        91053601,
        91053628,
        91054872,
        91054880,
        91054914,
        91054921,
        91055019,
        91055144,
        91055245,
        91055447,
        91055457,
        91055608,
        91055658,
        91055788,
        91055829,
        91056318,
        91056320,
        91056476,
        91056886,
        91056978,
        91057116,
        91057137,
        91057138,
        91057242,
        91057381,
        91057414,
        91057460,
        91057477,
        91057540,
        91057586,
        91057637,
        91057651,
        91057675,
        91058232,
        91058349,
        91058474,
        91058564,
        91058581,
        91058899,
        91060406,
        91060460,
        91061379,
        91061408,
        91061430,
        91061462,
        91061482,
        91063103,
        91063122,
        91063141,
        91063162,
        91063243,
        91063317,
        91063506,
        91064223,
        91064414,
        91163324,
        91163425,
        91163446,
        91163466,
        91163670,
        91164243,
        91164461,
        91164521,
        91164589,
        91164663,
        91164712,
        91164794,
        91165059,
        91165750,
        91166296,
        91166314,
        91166348,
        91166425,
        91166460,
        91166504,
        91166559,
        91166904,
        91166953,
        91166963,
        91166983,
        91167040,
        91169830,
        91169989,
        91170220,
        91170343,
        91170535,
        91171010,
        91178976,
        91179285,
        91179554,
        91193009,
        91193799,
        91194185,
        91194333,
        91194455,
        91195514,
        91196117,
        91251466,
        91251472,
        91253684,
        91253696,
        91254791,
        91255084,
        91257195,
        91257224,
        91257381,
        91257394,
        91257438,
        91257478,
        91257576,
        91257579,
        91257598,
        91257665,
        91257679,
        91257986,
        91258048,
        91258097,
        91258158,
        91258221,
        91258252,
        91258264,
        91258265,
        91258273,
        91258285,
        91258309,
        91258325,
        91258326,
        91258358,
        91259631,
        91260223,
        91261510,
        91261558,
        91261688,
        91261700,
        91261759,
        91261888,
        91261938,
        91263065,
        91263423,
        91263581,
        91263983,
        91264244,
        91264284,
        91264324,
        91264399,
        91266218,
        91267296,
        91267635,
        91267787,
        91407350,
        91407510,
        91407719,
        91423591,
        91428976,
        91430021,
        91431340,
        91434154,
        91434365,
        91436332,
        91437386,
        91437681,
        91437930,
        91437952,
        91438958,
        91439808,
        91440720,
        91441056,
        91441767,
        91444621,
        91447876,
        91620214,
        91639424,
        91822048,
        91822347,
        91822578,
        91822861,
        91822906,
        91824530,
        91824554,
        91824566,
        91824567,
        91824587,
        91824589,
        91824594,
        91824600,
        91824602,
        91824630,
        91824650,
        91824653,
        91824659,
        91824669,
        91824678,
        91824713,
        91824922,
        91824960,
        91825385,
        91825391,
        91825400,
        91825403,
        91825410,
        91825415,
        91825439,
        91825441,
        91825536,
        91825638,
        91825655,
        91825678,
        91825693,
        91825703,
        91825725,
        91825728,
        91825744,
        91825803,
        91825808,
        91829098,
        91829125,
        91829142,
        91829152,
        91829232,
        91829333,
        91829346,
        91829353,
        91829481,
        91829494,
        91829511,
        91829520,
        91829542,
        91829581,
        91830003,
        91830069,
        91830433,
        91830711,
        91830838,
        91832012,
        91832022,
        91832031,
        91832035,
        91891109,
        91891192,
        91891240,
        91891266,
        91891279,
        91891322,
        91891340,
        91891420,
        91891463,
        91891491,
        91891562,
        91891809,
        91891825,
        91892712,
        91893130,
        91893610,
        91893617,
        91893623,
        91893926,
        91893986,
        91894095,
        91894117,
        91894131,
        91894140,
        91894215,
        91894392,
        91894415,
        91894428,
        91894439,
        91894449,
        91894470,
        91894492,
        91894498,
        91894547,
        91894593,
        91894596,
        91894649,
        91894655,
        91894658,
        91894864,
        91894901,
        91894936,
        91894976,
        91895034,
        91895437,
        91896809,
        91898322,
        91899063,
        91899621,
        91900976,
        91901234,
        91901267,
        91901383,
        91901890,
        91901994,
        91902006,
        91902469,
        92023020,
        92023157,
        92023212,
        92023279,
        92023405,
        92023796,
        92024002,
        92025077,
        92025620,
        92026074,
        92026362,
        92026519,
        92027884,
        92027909,
        92027970,
        92028096,
        92028121,
        92028142,
        92028925,
        92029315,
        92034792,
        92034898,
        92035096,
        92035177,
        92044773,
        92045205,
        92045233,
        92045263,
        92045296,
        92045353,
        92045386,
        92045407,
        92045432,
        92045476,
        92045532,
        92045692,
        92045793,
        92045872,
        92049101,
        92049732,
        92049790,
        92050067,
        92053722,
        92058491,
        92060308,
        92171156,
        92171462,
        92171966,
        92172114,
        92172686,
        92172975,
        92173712,
        92173901,
        92177365,
        92178162,
        92178205,
        92178488,
        92178691,
        92178937,
        92178995,
        92179136,
        92179262,
        92179370,
        92179399,
        92179795,
        92179913,
        92180184,
        92180234,
        92180339,
        92180741,
        92180839,
        92180897,
        92181128,
        92181202,
        92181418,
        92181666,
        92183658,
        92184025,
        92187205,
        92193983,
        92194072,
        92194548,
        92194562,
        92196200,
        92196257,
        92196290,
        92196720,
        92197172,
        92197940,
        92199787,
        92205405,
        92205525,
        92205977,
        92206019,
        92206057,
        92206101,
        92206144,
        92301503,
        92343534,
        92344729,
        92344738,
        92344745,
        92344761,
        92344778,
        92344782,
        92344791,
        92344794,
        92344802,
        92344810,
        92344825,
        92344827,
        92344834,
        92344837,
        92344842,
        92344849,
        92344850,
        92344855,
        92344864,
        92344875,
        92344882,
        92344901,
        92344911,
        92344919,
        92344927,
        92344957,
        92344970,
        92344985,
        92344993,
        92345001,
        92345005,
        92345012,
        92345040,
        92345044,
        92345049,
        92345065,
        92345071,
        92345076,
        92345080,
        92345084,
        92345093,
        92345094,
        92345100,
        92345106,
        92345112,
        92345117,
        92345135,
        92345138,
        92345148,
        92345151,
        92345157,
        92345161,
        92345166,
        92345169,
        92345179,
        92345186,
        92345192,
        92345194,
        92345198,
        92345203,
        92345208,
        92345215,
        92345225,
        92345232,
        92345238,
        92345247,
        92345255,
        92345266,
        92345271,
        92345275,
        92345282,
        92345288,
        92345295,
        92345306,
        92345313,
        92345319,
        92345326,
        92345328,
        92345331,
        92345334,
        92345337,
        92345344,
        92345348,
        92345358,
        92345368,
        92345384,
        92345392,
        92345396,
        92345404,
        92345409,
        92345417,
        92345421,
        92345423,
        92345426
    ];

    const array FIX_FILE_NAMES = [
        'people/resumes/19068360.pdf',
        'people/resumes/19068677.pdf',
        'people/resumes/19068090.pdf',
        'people/resumes/19071186.pdf',
        'people/resumes/19071487.pdf',
        'people/resumes/19072178.pdf',
        'people/resumes/19072276.pdf',
        'people/resumes/19074939.pdf',
        'people/resumes/19075281.pdf',
        'people/resumes/19075517.pdf',
        'people/resumes/19070329.pdf',
        'people/resumes/18977448.pdf',
        'people/resumes/46371353.pdf',
        'people/resumes/19075997.pdf',
        'people/resumes/19136038.pdf',
        'people/resumes/19136078.pdf',
        'people/resumes/19136109.pdf',
        'people/resumes/19136187.pdf',
        'people/resumes/19136219.pdf',
        'people/resumes/19136253.pdf',
        'people/resumes/19136633.pdf',
        'people/resumes/19136708.pdf',
        'people/resumes/19136728.pdf',
        'people/resumes/19136975.pdf',
        'people/resumes/19139780.pdf',
        'people/resumes/19139795.pdf',
        'people/resumes/19139982.pdf',
        'people/resumes/19140170.pdf',
        'people/resumes/19140276.pdf',
        'people/resumes/44073693.pdf',
        'people/resumes/19140492.pdf',
        'people/resumes/19140506.pdf',
        'people/resumes/18286454.pdf',
        'people/resumes/19142698.pdf',
        'people/resumes/19143243.pdf',
        'people/resumes/19076001.pdf',
        'people/resumes/19143252.pdf',
        'people/resumes/19143508.pdf',
        'people/resumes/19197474.pdf',
        'people/resumes/19140550.pdf',
        'people/resumes/19200045.pdf',
        'people/resumes/18425983.pdf',
        'people/resumes/18425982.pdf',
        'people/resumes/18425973.pdf',
        'people/resumes/18730807.pdf',
        'people/resumes/19200143.pdf',
        'people/resumes/19200173.pdf',
        'people/resumes/19200166.pdf',
        'people/resumes/19200175.pdf',
        'people/resumes/19200180.pdf',
        'people/resumes/19200181.pdf',
        'people/resumes/18426006.pdf',
        'people/resumes/19200186.pdf',
        'people/resumes/18425996.pdf',
        'people/resumes/19200200.pdf',
        'people/resumes/36855648.pdf',
        'people/resumes/35307949.pdf',
        'people/resumes/37087901.pdf',
        'people/resumes/19218258.pdf',
        'people/resumes/19330390.pdf',
        'people/resumes/19218567.pdf',
        'people/resumes/19314043.pdf',
        'people/resumes/19315359.pdf',
        'people/resumes/19371648.pdf',
        'people/resumes/19371629.pdf',
        'people/resumes/19371670.pdf',
        'people/resumes/19371719.pdf',
        'people/resumes/19371692.pdf',
        'people/resumes/19371622.pdf',
        'people/resumes/19371683.pdf',
        'people/resumes/19371665.pdf',
        'people/resumes/19371662.pdf',
        'people/resumes/19371639.pdf',
        'people/resumes/19636308.pdf',
        'people/resumes/19636117.pdf',
        'people/resumes/19429705.pdf',
        'people/resumes/19429684.pdf',
        'people/resumes/19429670.pdf',
        'people/resumes/19429646.pdf',
        'people/resumes/19635647.pdf',
        'people/resumes/19429631.pdf',
        'people/resumes/19635491.pdf',
        'people/resumes/19635459.pdf',
        'people/resumes/19635049.pdf',
        'people/resumes/19429578.pdf',
        'people/resumes/19633656.pdf',
        'people/resumes/19633639.pdf',
        'people/resumes/19635217.pdf',
        'people/resumes/19632866.pdf',
        'people/resumes/19632831.pdf',
        'people/resumes/19634921.pdf',
        'people/resumes/19634901.pdf',
        'people/resumes/19634110.pdf',
        'people/resumes/19634090.pdf',
        'people/resumes/19382517.pdf',
        'people/resumes/19382513.pdf',
        'people/resumes/19429567.pdf',
        'people/resumes/19633314.pdf',
        'people/resumes/19633300.pdf',
        'people/resumes/19633450.pdf',
        'people/resumes/19634609.pdf',
        'people/resumes/19634595.pdf',
        'people/resumes/19632663.pdf',
        'people/resumes/19632646.pdf',
        'people/resumes/19632387.pdf',
        'people/resumes/19632506.pdf',
        'people/resumes/19632242.pdf',
        'people/resumes/19632075.pdf',
        'people/resumes/19429463.pdf',
        'people/resumes/19429440.pdf',
        'people/resumes/19631850.pdf',
        'people/resumes/19631693.pdf',
        'people/resumes/19638878.pdf',
        'people/resumes/19429420.pdf',
        'people/resumes/19429456.pdf',
        'people/resumes/19382499.pdf',
        'people/resumes/19382496.pdf',
        'people/resumes/19382555.pdf',
        'people/resumes/19632988.pdf',
        'people/resumes/19678161.pdf',
        'people/resumes/19678155.pdf',
        'people/resumes/19429519.pdf',
        'people/resumes/19429518.pdf',
        'people/resumes/19429405.pdf',
        'people/resumes/19633857.pdf',
        'people/resumes/57339177.pdf',
        'people/resumes/19636475.odt',
        'people/resumes/19636456.pdf',
        'people/resumes/19429737.pdf',
        'people/resumes/19636885.pdf',
        'people/resumes/19637371.pdf',
        'people/resumes/19637355.pdf',
        'people/resumes/19637650.pdf',
        'people/resumes/19640105.pdf',
        'people/resumes/19640091.pdf',
        'people/resumes/19636659.pdf',
        'people/resumes/19429788.pdf',
        'people/resumes/19429786.pdf',
        'people/resumes/19640300.pdf',
        'people/resumes/19640286.pdf',
        'people/resumes/19637492.pdf',
        'people/resumes/19639499.pdf',
        'people/resumes/19639486.pdf',
        'people/resumes/19640712.pdf',
        'people/resumes/19640689.pdf',
        'people/resumes/19429886.pdf',
        'people/resumes/19429884.pdf',
        'people/resumes/19429864.odt',
        'people/resumes/19429863.pdf',
        'people/resumes/19429835.pdf',
        'people/resumes/19640500.pdf',
        'people/resumes/19640486.pdf',
        'people/resumes/19429852.pdf',
        'people/resumes/19638148.pdf',
        'people/resumes/19638137.pdf',
        'people/resumes/19638388.pdf',
        'people/resumes/19638374.pdf',
        'people/resumes/19429802.pdf',
        'people/resumes/19638641.pdf',
        'people/resumes/19638624.pdf',
        'people/resumes/19639853.pdf',
        'people/resumes/19639730.pdf',
        'people/resumes/19430032.pdf',
        'people/resumes/19430506.pdf',
        'people/resumes/19430503.pdf',
        'people/resumes/19430534.pdf',
        'people/resumes/19678156.pdf',
        'people/resumes/19678157.pdf',
        'people/resumes/19678158.pdf',
        'people/resumes/19678159.pdf',
        'people/resumes/19678160.pdf',
        'people/resumes/19678162.pdf',
        'people/resumes/19678163.pdf',
        'people/resumes/19678164.pdf',
        'people/resumes/19678165.pdf',
        'people/resumes/19678166.pdf',
        'people/resumes/19678167.pdf',
        'people/resumes/19678168.pdf',
        'people/resumes/19678169.pdf',
        'people/resumes/19712949.pdf',
        'people/resumes/19712950.pdf',
        'people/resumes/19712951.pdf',
        'people/resumes/19712953.pdf',
        'people/resumes/19712954.pdf',
        'people/resumes/19793518.pdf',
        'people/resumes/19794235.pdf',
        'people/resumes/19794236.pdf',
        'people/resumes/19794237.pdf',
        'people/resumes/19794238.pdf',
        'people/resumes/19794239.pdf',
        'people/resumes/19794240.pdf',
        'people/resumes/19794241.pdf',
        'people/resumes/19794242.pdf',
        'people/resumes/19794243.pdf',
        'people/resumes/19794244.pdf',
        'people/resumes/19794246.pdf',
        'people/resumes/19794247.pdf',
        'people/resumes/20138746.pdf',
        'people/resumes/20138762.pdf',
        'people/resumes/20138770.pdf',
        'people/resumes/20138783.pdf',
        'people/resumes/20138796.pdf',
        'people/resumes/20138804.pdf',
        'people/resumes/20138823.pdf',
        'people/resumes/20138830.pdf',
        'people/resumes/20138838.pdf',
        'people/resumes/20138854.pdf',
        'people/resumes/20138867.pdf',
        'people/resumes/20138875.pdf',
        'people/resumes/20138881.pdf',
        'people/resumes/20142224.pdf',
        'people/resumes/20142245.pdf',
        'people/resumes/20142250.pdf',
        'people/resumes/20142261.pdf',
        'people/resumes/20202961.pdf',
        'people/resumes/20205210.pdf',
        'people/resumes/20205217.pdf',
        'people/resumes/20205221.pdf',
        'people/resumes/20205231.pdf',
        'people/resumes/20205238.pdf',
        'people/resumes/20205243.pdf',
        'people/resumes/20370815.pdf',
        'people/resumes/20370930.docx',
        'people/resumes/20435446.pdf',
        'people/resumes/20435453.pdf',
        'people/resumes/20435491.pdf',
        'people/resumes/20435492.pdf',
        'people/resumes/20435493.pdf',
        'people/resumes/20435494.pdf',
        'people/resumes/20435495.pdf',
        'people/resumes/20435496.pdf',
        'people/resumes/20435497.pdf',
        'people/resumes/20435498.pdf',
        'people/resumes/20435499.pdf',
        'people/resumes/20435500.pdf',
        'people/resumes/20435501.pdf',
        'people/resumes/20435502.pdf',
        'people/resumes/20435503.pdf',
        'people/resumes/20435505.pdf',
        'people/resumes/20435506.pdf',
        'people/resumes/20463625.pdf',
        'people/resumes/20436746.pdf',
        'people/resumes/20445679.pdf',
        'people/resumes/20445691.pdf',
        'people/resumes/20445707.pdf',
        'people/resumes/20463664.pdf',
        'people/resumes/20445716.pdf',
        'people/resumes/20445735.pdf',
        'people/resumes/20445750.pdf',
        'people/resumes/21079317.pdf',
        'people/resumes/20445773.pdf',
        'people/resumes/20445780.pdf',
        'people/resumes/25425838.pdf',
        'people/resumes/20445792.pdf',
        'people/resumes/20463741.pdf',
        'people/resumes/20445801.pdf',
        'people/resumes/20463755.pdf',
        'people/resumes/20445810.pdf',
        'people/resumes/20445824.pdf',
        'people/resumes/20445831.pdf',
        'people/resumes/20446156.pdf',
        'people/resumes/20463656.pdf',
        'people/resumes/20463658.pdf',
        'people/resumes/20463660.pdf',
        'people/resumes/20463713.pdf',
        'people/resumes/20464265.pdf',
        'people/resumes/20464286.pdf',
        'people/resumes/20464391.pdf',
        'people/resumes/20464392.pdf',
        'people/resumes/20464393.pdf',
        'people/resumes/20464394.pdf',
        'people/resumes/20464395.pdf',
        'people/resumes/20464396.pdf',
        'people/resumes/20464397.pdf',
        'people/resumes/20464398.pdf',
        'people/resumes/20464399.pdf',
        'people/resumes/20464400.pdf',
        'people/resumes/20464449.pdf',
        'people/resumes/20464474.pdf',
        'people/resumes/20464489.pdf',
        'people/resumes/20741039.pdf',
        'people/resumes/20464505.pdf',
        'people/resumes/20464523.pdf',
        'people/resumes/20741046.pdf',
        'people/resumes/20741053.pdf',
        'people/resumes/20741068.pdf',
        'people/resumes/20741071.pdf',
        'people/resumes/20741078.pdf',
        'people/resumes/20753153.pdf',
        'people/resumes/20753312.pdf',
        'people/resumes/20753331.pdf',
        'people/resumes/20753354.pdf',
        'people/resumes/20753409.pdf',
        'people/resumes/20753479.pdf',
        'people/resumes/20753523.pdf',
        'people/resumes/20753549.pdf',
        'people/resumes/20753565.pdf',
        'people/resumes/20753576.pdf',
        'people/resumes/20754369.pdf',
        'people/resumes/20754370.pdf',
        'people/resumes/20754371.pdf',
        'people/resumes/20754372.pdf',
        'people/resumes/20754373.pdf',
        'people/resumes/20754374.pdf',
        'people/resumes/20754375.pdf',
        'people/resumes/20754376.pdf',
        'people/resumes/20754377.pdf',
        'people/resumes/20754378.pdf',
        'people/resumes/20754379.pdf',
        'people/resumes/20754380.pdf',
        'people/resumes/20764470.pdf',
        'people/resumes/20764471.pdf',
        'people/resumes/20764472.pdf',
        'people/resumes/20764473.pdf',
        'people/resumes/20764474.pdf',
        'people/resumes/20764475.pdf',
        'people/resumes/20764476.pdf',
        'people/resumes/20764477.pdf',
        'people/resumes/20764478.pdf',
        'people/resumes/20764479.pdf',
        'people/resumes/20764555.pdf',
        'people/resumes/20764556.pdf',
        'people/resumes/20764557.pdf',
        'people/resumes/20764558.pdf',
        'people/resumes/20764560.pdf',
        'people/resumes/20764561.pdf',
        'people/resumes/20764562.pdf',
        'people/resumes/20764564.pdf',
        'people/resumes/20764565.pdf',
        'people/resumes/20764566.pdf',
        'people/resumes/20764568.pdf',
        'people/resumes/20775282.pdf',
        'people/resumes/20775283.pdf',
        'people/resumes/20775284.pdf',
        'people/resumes/20775285.pdf',
        'people/resumes/20775286.pdf',
        'people/resumes/20775287.pdf',
        'people/resumes/20775288.pdf',
        'people/resumes/20822759.pdf',
        'people/resumes/20865807.pdf',
        'people/resumes/20906592.pdf',
        'people/resumes/20919550.pdf',
        'people/resumes/47888160.pdf',
        'people/resumes/21077536.pdf',
        'people/resumes/21077656.pdf',
        'people/resumes/21079200.pdf',
        'people/resumes/21079400.pdf',
        'people/resumes/21079567.pdf',
        'people/resumes/25032711.pdf',
        'people/resumes/21094803.pdf',
        'people/resumes/21094984.pdf',
        'people/resumes/21095011.pdf',
        'people/resumes/21095034.pdf',
        'people/resumes/21157628.pdf',
        'people/resumes/21157631.pdf',
        'people/resumes/21157634.pdf',
        'people/resumes/21157874.pdf',
        'people/resumes/21157966.pdf',
        'people/resumes/21158062.pdf',
        'people/resumes/21158300.pdf',
        'people/resumes/21158353.pdf',
        'people/resumes/21158443.pdf',
        'people/resumes/21414515.pdf',
        'people/resumes/22441615.pdf',
        'people/resumes/22583460.pdf',
        'people/resumes/22583476.pdf',
        'people/resumes/22648091.pdf',
        'people/resumes/22687301.pdf',
        'people/resumes/22687302.pdf',
        'people/resumes/22687303.pdf',
        'people/resumes/22687304.pdf',
        'people/resumes/22687305.pdf',
        'people/resumes/22687306.pdf',
        'people/resumes/22687307.pdf',
        'people/resumes/22687308.pdf',
        'people/resumes/22687309.pdf',
        'people/resumes/22687310.pdf',
        'people/resumes/22768686.pdf',
        'people/resumes/22769915.pdf',
        'people/resumes/22771318.pdf',
        'people/resumes/22772009.pdf',
        'people/resumes/22775400.pdf',
        'people/resumes/22920028.pdf',
        'people/resumes/22929773.pdf',
        'people/resumes/22976169.pdf',
        'people/resumes/22976447.pdf',
        'people/resumes/22976620.pdf',
        'people/resumes/22976690.pdf',
        'people/resumes/22977141.pdf',
        'people/resumes/23081210.pdf',
        'people/resumes/23081239.pdf',
        'people/resumes/23083410.pdf',
        'people/resumes/23350944.pdf',
        'people/resumes/23351298.pdf',
        'people/resumes/23426808.pdf',
        'people/resumes/23426386.pdf',
        'people/resumes/23516017.pdf',
        'people/resumes/23622601.pdf',
        'people/resumes/23651202.pdf',
        'people/resumes/23652412.pdf',
        'people/resumes/23659426.pdf',
        'people/resumes/23666849.pdf',
        'people/resumes/23667183.pdf',
        'people/resumes/23669738.pdf',
        'people/resumes/23669797.pdf',
        'people/resumes/23749056.pdf',
        'people/resumes/23750364.pdf',
        'people/resumes/23879088.pdf',
        'people/resumes/23880254.pdf',
        'people/resumes/23892860.pdf',
        'people/resumes/23892978.pdf',
        'people/resumes/23893046.pdf',
        'people/resumes/23893545.pdf',
        'people/resumes/23990892.pdf',
        'people/resumes/23990902.pdf',
        'people/resumes/23993348.pdf',
        'people/resumes/23995869.pdf',
        'people/resumes/23996841.pdf',
        'people/resumes/23998656.pdf',
        'people/resumes/23999018.pdf',
        'people/resumes/23999439.pdf',
        'people/resumes/24011712.pdf',
        'people/resumes/24011980.pdf',
        'people/resumes/24050997.pdf',
        'people/resumes/24113010.pdf',
        'people/resumes/24113123.pdf',
        'people/resumes/24113350.pdf',
        'people/resumes/24129340.pdf',
        'people/resumes/24128654.pdf',
        'people/resumes/24160190.pdf',
        'people/resumes/24218335.pdf',
        'people/resumes/24218393.pdf',
        'people/resumes/24218483.pdf',
        'people/resumes/24357656.pdf',
        'people/resumes/18381376.pdf',
        'people/resumes/24619980.pdf',
        'people/resumes/18286437.pdf',
        'people/resumes/20908655.pdf',
        'people/resumes/20906593.pdf',
        'people/resumes/20604872.pdf',
        'people/resumes/32445997.pdf',
        'people/resumes/32411032.pdf',
        'people/resumes/32411074.pdf',
        'people/resumes/32411005.pdf',
        'people/resumes/32446341.pdf',
        'people/resumes/32464111.pdf',
        'people/resumes/32537254.pdf',
        'people/resumes/32537513.pdf',
        'people/resumes/43067694.pdf',
        'people/resumes/32545473.pdf',
        'people/resumes/32843280.pdf',
        'people/resumes/32545496.pdf',
        'people/resumes/32545492.pdf',
        'people/resumes/32546186.pdf',
        'people/resumes/32546655.pdf',
        'people/resumes/32590334.pdf',
        'people/resumes/32770384.pdf',
        'people/resumes/32826160.pdf',
        'people/resumes/32843226.pdf',
        'people/resumes/32843247.pdf',
        'people/resumes/32843263.png',
        'people/resumes/33058846.pdf',
        'people/resumes/50170618.pdf',
        'people/resumes/33407479.PDF',
        'people/resumes/58194309.pdf',
        'people/resumes/33509585.pdf',
        'people/resumes/33678033.pdf',
        'people/resumes/33678014.pdf',
        'people/resumes/33809460.pdf',
        'people/resumes/33855472.pdf',
        'people/resumes/33855590.pdf',
        'people/resumes/33883704.pdf',
        'people/resumes/33931866.pdf',
        'people/resumes/34007584.pdf',
        'people/resumes/34009715.pdf',
        'people/resumes/34096960.pdf',
        'people/resumes/34100586.pdf',
        'people/resumes/34165914.pdf',
        'people/resumes/34165941.pdf',
        'people/resumes/34625494.pdf',
        'people/resumes/23659481.pdf',
        'people/resumes/34815389.pdf',
        'people/resumes/18591451.pdf',
        'people/resumes/34937860.pdf',
        'people/resumes/34977310.pdf',
        'people/resumes/35036154.pdf',
        'people/resumes/34937993.pdf',
        'people/resumes/35103444.pdf',
        'people/resumes/35128125.pdf',
        'people/resumes/35128122.pdf',
        'people/resumes/35129746.pdf',
        'people/resumes/35208125.pdf',
        'people/resumes/35307516.pdf',
        'people/resumes/35205814.pdf',
        'people/resumes/35310200.pdf',
        'people/resumes/35334439.pdf',
        'people/resumes/35335835.pdf',
        'people/resumes/35472821.pdf',
        'people/resumes/35205842.pdf',
        'people/resumes/35475973.pdf',
        'people/resumes/35476518.pdf',
        'people/resumes/35663159.pdf',
        'people/resumes/35666265.pdf',
        'people/resumes/35666949.pdf',
        'people/resumes/35689168.pdf',
        'people/resumes/35895189.pdf',
        'people/resumes/35922446.pdf',
        'people/resumes/35949319.pdf',
        'people/resumes/35979247.pdf',
        'people/resumes/34904477.pdf',
        'people/resumes/36004583.pdf',
        'people/resumes/36039915.docx',
        'people/resumes/36032992.pdf',
        'people/resumes/36033376.pdf',
        'people/resumes/36034183.pdf',
        'people/resumes/36035219.pdf',
        'people/resumes/36070934.pdf',
        'people/resumes/36086996.pdf',
        'people/resumes/35986423.pdf',
        'people/resumes/35109587.pdf',
        'people/resumes/36214772.pdf',
        'people/resumes/36266290.pdf',
        'people/resumes/36266546.pdf',
        'people/resumes/36266927.pdf',
        'people/resumes/36267858.pdf',
        'people/resumes/36269486.pdf',
        'people/resumes/36438578.pdf',
        'people/resumes/36439744.pdf',
        'people/resumes/36556282.pdf',
        'people/resumes/36556397.pdf',
        'people/resumes/36556615.pdf',
        'people/resumes/36556624.pdf',
        'people/resumes/36673153.pdf',
        'people/resumes/36730786.pdf',
        'people/resumes/36856024.pdf',
        'people/resumes/24387027.pdf',
        'people/resumes/36949386.pdf',
        'people/resumes/36860451.pdf',
        'people/resumes/36951435.pdf',
        'people/resumes/36949573.pdf',
        'people/resumes/36791109.pdf',
        'people/resumes/36975434.pdf',
        'people/resumes/36975802.pdf',
        'people/resumes/36976192.pdf',
        'people/resumes/36979893.pdf',
        'people/resumes/37168050.pdf',
        'people/resumes/36980042.pdf',
        'people/resumes/36556232.pdf',
        'people/resumes/37080610.pdf',
        'people/resumes/22919930.pdf',
        'people/resumes/22919923.pdf',
        'people/resumes/37084868.pdf',
        'people/resumes/37084914.pdf',
        'people/resumes/37113318.pdf',
        'people/resumes/37084311.pdf',
        'people/resumes/37118380.pdf',
        'people/resumes/37121750.pdf',
        'people/resumes/37121740.pdf',
        'people/resumes/37142479.pdf',
        'people/resumes/37143935.pdf',
        'people/resumes/37170051.pdf',
        'people/resumes/37170511.pdf',
        'people/resumes/37194708.pdf',
        'people/resumes/37198230.pdf',
        'people/resumes/37201509.pdf',
        'people/resumes/37225742.pdf',
        'people/resumes/37225394.pdf',
        'people/resumes/37201530.pdf',
        'people/resumes/37234756.pdf',
        'people/resumes/37196646.pdf',
        'people/resumes/36979711.pdf',
        'people/resumes/36556097.pdf',
        'people/resumes/37342846.pdf',
        'people/resumes/37343115.pdf',
        'people/resumes/37348096.pdf',
        'people/resumes/37147601.pdf',
        'people/resumes/37443938.pdf',
        'people/resumes/37450624.pdf',
        'people/resumes/37479057.pdf',
        'people/resumes/37480980.pdf',
        'people/resumes/37481186.pdf',
        'people/resumes/37482531.pdf',
        'people/resumes/37482535.pdf',
        'people/resumes/37485789.pdf',
        'people/resumes/37533235.pdf',
        'people/resumes/37602812.pdf',
        'people/resumes/37604936.pdf',
        'people/resumes/37533417.pdf',
        'people/resumes/37607206.pdf',
        'people/resumes/37634898.pdf',
        'people/resumes/37661397.pdf',
        'people/resumes/37661493.pdf',
        'people/resumes/37661868.pdf',
        'people/resumes/37662099.pdf',
        'people/resumes/37662547.pdf',
        'people/resumes/37662552.pdf',
        'people/resumes/37662555.pdf',
        'people/resumes/37662561.pdf',
        'people/resumes/37662564.pdf',
        'people/resumes/37662568.pdf',
        'people/resumes/37662571.pdf',
        'people/resumes/37662574.docx',
        'people/resumes/37662581.pdf',
        'people/resumes/37662597.pdf',
        'people/resumes/37662601.pdf',
        'people/resumes/37662602.pdf',
        'people/resumes/37662609.pdf',
        'people/resumes/37662885.pdf',
        'people/resumes/37664145.pdf',
        'people/resumes/37664149.pdf',
        'people/resumes/37665238.pdf',
        'people/resumes/37702590.docx',
        'people/resumes/37702600.pdf',
        'people/resumes/37702617.docx',
        'people/resumes/37702630.pdf',
        'people/resumes/37702639.pdf',
        'people/resumes/37838654.pdf',
        'people/resumes/37838653.pdf',
        'people/resumes/37702771.pdf',
        'people/resumes/37702766.pdf',
        'people/resumes/37702765.pdf',
        'people/resumes/37709121.pdf',
        'people/resumes/37836440.pdf',
        'people/resumes/37836703.pdf',
        'people/resumes/37836845.pdf',
        'people/resumes/38048803.pdf',
        'people/resumes/38049207.docx',
        'people/resumes/38049211.docx',
        'people/resumes/38049217.pdf',
        'people/resumes/38049606.pdf',
        'people/resumes/38049721.pdf',
        'people/resumes/38049872.pdf',
        'people/resumes/38049973.pdf',
    ];

    public function __construct(protected string $loxoZipPath)
    {
        $this->website = app(\Hyn\Tenancy\Environment::class)->tenant();
    }

    public function logTime(string $message, $startTime = null): void
    {
        if (!$this->timeLogging) {
            return;
        }

        $startTime = $startTime ?? microtime(true);
        \Log::info('[Loxo/Timing] - [' . $message . '] ' . (microtime(true) - $startTime)) . 's';
    }

    public function logMemory(string $message, $startMemory = null): void
    {
        if (!$this->memoryLogging) {
            return;
        }

        $startMemory = $startMemory ?? memory_get_usage();
        $endMemory = memory_get_usage();
        \Log::info('[Loxo/Memory] - [' . $message . '] [' . $endMemory . ' bytes] ' . ($endMemory - $startMemory)) . ' bytes';
    }

    public function import()
    {
        DB::connection('tenant')->disableQueryLog();

        \Log::info('[Loxo Fixer] Import started');
        \Log::info('[Loxo Fixer] Extracting zip file');

        $zipExtractor = new ZipExtractor();
        $extractedPath = $zipExtractor->extract($this->loxoZipPath);
        \Log::info('[Loxo Fixer] Zip file extracted to ' . $extractedPath);

        cache()->driver('tenant_database')->set('loxo_import_extracted_path', $extractedPath, self::CACHE_DURATION);

        // The first directory in the extracted path is the root directory
        $rootDir = scandir($extractedPath)[2];
        $this->rootDirPath = $extractedPath . '/' . $rootDir;

        try {
            //$this->setupCandidateCustomFields();
            $this->importPeople();
//            $this->importClients();
//            $this->setupProjectCustomFields();
//            $this->importProjects();
//            $this->importActivities();
//            $this->moveCandidatesToCorrectStages();
//            $this->linkCommentsToJobs();
        } finally {
//            Do not clean up the extracted files for now. The queue jobs will fail if the files are cleaned up.
//            \Log::info('[Loxo Fixer] Cleaning up');
//            $zipExtractor->cleanUp();
        }
        \Log::info('[Loxo Fixer] Import finished');
    }

    protected function readLoxoFile(string $file): array
    {
        if (!$this->rootDirPath) {
            throw new \Exception('Root directory path is not set');
        }

        $filePath = $this->rootDirPath . '/' . $file;
        if (!file_exists($filePath)) {
            throw new \Exception('File does not exist: ' . $filePath);
        }

        $jsonlReader = new JSONLReader($filePath);
        return $jsonlReader->readToArray();
    }

    protected function getUser(string $email, ?array $data = []): User
    {
        static $userCache = [];
        $email = Str::lower($email);

        if (!isset($userCache[$email])) {
            $user = User::where('email', $email)->first();
            if (!$user) {
                $user = $this->getSystemUser();
            }

            $userCache[$email] = $user;
        }

        return $userCache[$email];
    }

    protected function getSystemUser(): User
    {
        $user = User::firstOrCreate([
            'email' => self::SYSTEM_USER_EMAIL,
        ], [
            'name' => 'Loxo',
        ]);

        $user->active = false;
        $user->role = User::ROLE_LIMITED;
        $user->save();

        return $user;
    }

    protected function importPeople()
    {
        if (cache()->driver('tenant_database')->get('loxo_fixer_people_imported')) {
            \Log::info('[Loxo Fixer] People already imported');
            return;
        }

        \Log::info('[Loxo Fixer] Importing people');
        $people = $this->readLoxoFile('people.jsonl');

        foreach ($people as $person) {
            $this->importPerson($person);
        }

        cache()->driver('tenant_database')->set('loxo_fixer_people_imported', time(), self::CACHE_DURATION);
        \Log::info('[Loxo Fixer] People imported');
    }

    protected function importPerson(array $personData)
    {
        $personTypes = $personData['types'] ?? ['Contact'];

        if (in_array('Candidate', $personTypes)) {
            try {
                $candidateId = data_get($personData, 'id');
                if (in_array(intval($candidateId), self::FIX_CANDIDATE_IDS)) {
                    $this->fixCandidate($personData);
                } else {
                    $this->fixFiles($personData);
                }
            } catch (\Throwable $e) {
                \Log::error('[Loxo Fixer] - Error importing candidate ' . data_get($personData, 'id') . ': ' . $e->getMessage());
            }
        }

        if (in_array('Contact', $personTypes)) {
            // Contacts are not imported for now
            //$this->importContact($personData);
        }
    }

    protected function fixCandidate(array $personData)
    {
        $startImportTime = microtime(true); // Start time for entire import function
        $initialMemoryUsage = memory_get_usage(); // Initial memory usage

        $loxoId = data_get($personData, 'id');

        // Check if the candidate has already been imported
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $tdCandidateId = cache()->driver('tenant_database')->get('loxo_candidate_id_' . $loxoId);
        if (!$tdCandidateId) {
            \Log::info('[Loxo Fixer] - Could not find candidate for Loxo ID ' . $loxoId);
            return;
        }
        $this->logTime('Cache check', $startTime);
        $this->logMemory('Cache check', $startMemory);

        $candidate = Candidate::find($tdCandidateId);

        if (!$candidate) {
            \Log::error('[Loxo Fixer] - Candidate with Loxo ID ' . $loxoId . ' returned TD candidate ID ' . $tdCandidateId . ' but candidate not found');
            return;
        }

        \Log::info('[Loxo Fixer] - Fixing candidate with Loxo ID ' . $loxoId);

        // Email processing
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $emails = collect(data_get($personData, 'emails', []))->sortByDesc(function ($email) {
            return $email['type'] === 'Main' ? 1 : 0;
        })->map(function ($email) {
            return $email['email'];
        })->values();

        $this->logTime('Email processing', $startTime);
        $this->logMemory('Email processing', $startMemory);

        $phones = collect(data_get($personData, 'phones', []))->sortByDesc(function ($phone) {
            return $phone['type'] === 'Main' ? 1 : 0;
        })->map(function ($phone) {
            return $phone['phone'];
        })->values();

        // Candidate data assignment
//        $startTime = microtime(true);
//        $startMemory = memory_get_usage();
//        $candidate->name = data_get($personData, 'name');
//        $candidate->email = $email;
//        $candidate->phone = $phone ?? null;
//        $candidate->linkedin_url = collect(data_get($personData, 'social', []))->filter(fn($social) => str_contains($social, 'linkedin'))->first() ?? null;
//        $candidate->city = data_get($personData, 'city');
//        $candidate->country = data_get($personData, 'country');
//        $candidate->created_at = data_get($personData, 'created');
//
//        $candidate->save();
//        $this->logTime('Candidate data assignment', $startTime);
//        $this->logMemory('Candidate data assignment', $startMemory);

        // Cache set operation
//        $startTime = microtime(true);
//        $startMemory = memory_get_usage();
//        cache()->driver('tenant_database')->set('loxo_candidate_id_' . data_get($personData, 'id'), $candidate->id, self::CACHE_DURATION);
//        $this->logTime('Cache set', $startTime);
//        $this->logMemory('Cache set', $startMemory);

        // Work Experiences
//        $startTime = microtime(true);
//        $startMemory = memory_get_usage();
//        $employments = collect(data_get($personData, 'experience', []))->map(function ($employment) {
//            $startDate = data_get($employment, 'from.year') ? data_get($employment, 'from.year') . '-' . data_get($employment, 'from.month', '01') . '-01' : null;
//            $endDate = data_get($employment, 'current') ? null : (data_get($employment, 'to.year') . '-' . data_get($employment, 'to.month', '01') . '-01');
//
//            return [
//                'position_title' => data_get($employment, 'title'),
//                'employer_name' => data_get($employment, 'company'),
//                'description' => data_get($employment, 'desc'),
//                'date_from' => $startDate,
//                'date_until' => $endDate,
//            ];
//        });
//
//        $candidate->employments()->createMany($employments);
//        $this->logTime('Work experiences', $startTime);
//        $this->logMemory('Work experiences', $startMemory);

        // Educations
//        $startTime = microtime(true);
//        $startMemory = memory_get_usage();
//        $educations = collect(data_get($personData, 'education', []))->map(function ($education) {
//            return [
//                'programme' => data_get($education, 'degree'),
//                'degree' => null,
//                'degree_title' => null,
//                'institution' => data_get($education, 'school'),
//                'description' => data_get($education, 'desc'),
//                'end_year' => data_get($education, 'year'),
//            ];
//        });
//
//        $candidate->educations()->createMany($educations);
//        $this->logTime('Educations', $startTime);
//        $this->logMemory('Educations', $startMemory);

        // Tags
//        $startTime = microtime(true);
//        $startMemory = memory_get_usage();
//        collect(data_get($personData, 'tags', []))->each(function ($tagName) use ($candidate) {
//            $tag = $this->getTag($tagName);
//
//            // Attach tag to candidate
//            $candidate->tags()->attach($tag->id);
//        });
//        $this->logTime('Tags', $startTime);
//        $this->logMemory('Tags', $startMemory);

        // Picture
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $picturePath = data_get($personData, 'pic.path');
        if ($picturePath) {
            if (!$candidate->photo) {
                // Only import the picture if the candidate does not already have a picture
                $uploadedFile = new UploadedFile($this->rootDirPath . '/' . $picturePath, data_get($personData, 'pic.filename') . '.jpg');
                $candidate->photo()->create([
                    'location' => File::store($uploadedFile),
                    'type' => File::TYPE_CANDIDATE_PHOTO,
                    'created_at' => data_get($personData, 'pic.upload_date'),
                ]);
            }
        }
        $this->logTime('Picture', $startTime);
        $this->logMemory('Picture', $startMemory);

        // Documents
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        collect(data_get($candidate, 'documents', []))->merge(collect(data_get($personData, 'resumes', [])))->each(function ($document) use ($candidate) {
            $filePath = data_get($document, 'path');
            if (!$filePath) {
                \Log::error('[Loxo Fixer] - No path present for document ' . data_get($document, 'filename', ''));
                return;
            }

            $fullFilePath = $this->rootDirPath . '/' . $filePath;

            if ($candidate->files()->where('type', File::TYPE_CV)->where('created_at', data_get($document, 'upload_date'))->exists()) {
                \Log::info('[Loxo Fixer] - File ' . $filePath . ' already imported, skipping');
                return;
            }

            dispatch(function () use ($fullFilePath, $candidate, $document, $filePath) {
                try {
                    retry(3, function () use ($fullFilePath, $candidate, $document, $filePath) {
                        $uploadedFile = new UploadedFile($fullFilePath, data_get($document, 'filename'));
                        $candidate->files()->create([
                            'type' => File::TYPE_CV,
                            'location' => File::store($uploadedFile),
                            'created_at' => data_get($document, 'upload_date'),
                        ]);
                    }, sleepMilliseconds: 1000);
                } catch (\Throwable $e) {
                    \Log::error('[Loxo Fixer]   - Error storing file ' . $filePath . ': ' . $e->getMessage());
                }
            });
        });
        $this->logTime('Documents', $startTime);
        $this->logMemory('Documents', $startMemory);

        $customFields = [
            'globalStatus' => 'Global status (Loxo)',
            'source' => 'Source (Loxo)',
        ];

        $candidateCustomFields = [];

        foreach ($customFields as $key => $field) {
            if (isset($personData[$key])) {
                $fieldSettings = collect(Setting::get(Setting::KEY_CANDIDATE_CUSTOM_FIELDS))->firstWhere('label', $field);

                $newFieldSlug = Str::slug($field, '_');
                if (in_array($fieldSettings['type'], ['select', 'tags'])) {
                    if (is_array($personData[$key])) {
                        $newFieldValue = array_map(function ($value) {
                            return Str::slug($value, separator: '_');
                        }, $personData[$key]);
                    } else {
                        $newFieldValue = Str::slug($personData[$key], separator: '_');
                    }
                } else {
                    $newFieldValue = $personData[$key];
                }

                $candidateCustomFields[$newFieldSlug] = $newFieldValue;
            }
        }

        $candidate->custom_fields = $candidateCustomFields;
        $candidate->save();

        // Comments
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $locationComponents = [
            data_get($personData, 'address'),
            data_get($personData, 'city'),
            data_get($personData, 'zip'),
            data_get($personData, 'state'),
            data_get($personData, 'country'),
        ];

        $locationComponents = array_filter($locationComponents);
        $locationComponentsString = implode(', ', $locationComponents);
        $locationString = data_get($personData, 'location');

        $commentContent = '<p><strong>Loxo ID:</strong> ' . data_get($personData, 'id') . '</p>';

        if ($locationString || $locationComponentsString) {
            $commentContent .= '<p><strong>Location:</strong> ' . $locationString . '<br>' . $locationComponentsString . '</p>';
        }

        if ($desc = data_get($personData, 'desc')) {
            $commentContent .= '<p><strong>Description:</strong><br>' . $desc . '</p>';
        }

        if ($comp = data_get($personData, 'comp')) {
            $commentContent .= '<p><strong>Compensation:</strong> ' . data_get($comp, 'comp', '') . ' ' . data_get($comp, 'notes', '') . '</p>';
        }

        if ($noticePeriod = data_get($personData, 'notice_period')) {
            $commentContent .= '<p><strong>Notice period:</strong> ' . $noticePeriod . '</p>';
        }

        if ($social = data_get($personData, 'social')) {
            $commentContent .= '<p><strong>Social:</strong> ' . implode(', ', $social) . '</p>';
        }

        if (count($emails) > 1) {
            $commentContent .= '<p><strong>Emails:</strong> ' . $emails->implode(', ') . '</p>';
        }

        if (count($phones) > 1) {
            $commentContent .= '<p><strong>Phones:</strong> ' . $phones->implode(', ') . '</p>';
        }

        // Add pretty-printed Loxo data to the comment in a collapsible div
        $commentContent .= '<details><summary><strong>Loxo data</strong></summary><pre>' . json_encode($personData, JSON_PRETTY_PRINT) . '</pre></details>';

        if ($candidate->comments()->where('content', 'like', '%Loxo ID:%')->exists()) {
            \Log::info('[Loxo Fixer] - Comment with Loxo ID already exists for candidate ' . data_get($personData, 'id') . ', skipping');
        } else {
            $candidate->comments()->create([
                'user_id' => $this->getUser(self::SYSTEM_USER_EMAIL)->id,
                'created_at' => now(),
                'content' => $commentContent,
            ]);
        }

        $assessments = collect(data_get($personData, 'assessments', []));

        if ($assessments->isNotEmpty()) {
            $assessments->each(function ($assessment) use ($candidate) {
                $assessmentUserEmail = data_get($assessment, 'user');
                $user = $assessmentUserEmail ? $this->getUser($assessmentUserEmail) : $this->getUser(self::SYSTEM_USER_EMAIL);

                $commentType = data_get($assessment, 'type');
                $commentContent = data_get($assessment, 'desc');
                $commentJobId = data_get($assessment, 'job');
                $commentRating = data_get($assessment, 'rating');

                $content = '';

                if ($commentType !== 'Comment') {
                    $content .= '<p><strong>' . $commentType . '</strong></p>';
                }

                $content .= '<p><strong>Submitted by:</strong> ' . $assessmentUserEmail . '</p>';

                if ($commentRating) {
                    $content .= '<p><strong>Rating:</strong> ' . $commentRating . '</p>';
                }

                $content .= '<p>' . $commentContent . '</p>';

                if ($candidate->comments()->where('content', $content)->exists()) {
                    \Log::info('[Loxo Fixer] - Comment for assessment already exists, skipping');
                } else {
                    $comment = $candidate->comments()->create([
                        'user_id' => $user->id,
                        'created_at' => data_get($assessment, 'created'),
                        'content' => $content,
                    ]);

                    if ($commentJobId) {
                        $projectId = cache()->driver('tenant_database')->get('loxo_project_id_' . $commentJobId);

                        if ($projectId) {
                            $comment->project_id = $projectId;
                            $comment->save();
                            \Log::info('[Loxo Fixer]   - Comment with ID ' . $comment->id . ' linked to project ' . $projectId . ' via Loxo ID ' . $commentJobId);
                        }
                        // cache()->driver('tenant_database')->set('loxo_comment_job_id_' . $comment->id, $commentJobId, self::CACHE_DURATION);
                    }
                }
            });
        }

        $scorecards = collect(data_get($personData, 'scorecards', []));

        if ($scorecards->isNotEmpty()) {
            $scorecards->each(function ($scorecard) use ($candidate) {
                $scorecardUserEmail = data_get($scorecard, 'user');
                $user = $scorecardUserEmail ? $this->getUser($scorecardUserEmail) : $this->getUser(self::SYSTEM_USER_EMAIL);

                $scorecardType = data_get($scorecard, 'type');
                $commentContent = data_get($scorecard, 'summary');
                $commentJobId = data_get($scorecard, 'job');
                $commentRecommendation = data_get($scorecard, 'recommendationType');

                $content = '<p><strong>Scorecard type:</strong> ' . $scorecardType . '</p>';
                $content .= '<p><strong>Submitted by:</strong> ' . $scorecardUserEmail . '</p>';

                if ($commentRecommendation) {
                    $content .= '<p><strong>Recommendation:</strong> ' . $commentRecommendation . '</p>';
                }

                $content .= '<p>' . $commentContent . '</p>';

                if ($candidate->comments()->where('content', $content)->exists()) {
                    \Log::info('[Loxo Fixer] - Comment for scorecard already exists, skipping');
                } else {
                    $comment = $candidate->comments()->create([
                        'user_id' => $user->id,
                        'created_at' => data_get($scorecard, 'created'),
                        'content' => $content,
                    ]);

                    if ($commentJobId) {
                        $projectId = cache()->driver('tenant_database')->get('loxo_project_id_' . $commentJobId);

                        if ($projectId) {
                            $comment->project_id = $projectId;
                            $comment->save();
                            \Log::info('[Loxo Fixer]   - Comment with ID ' . $comment->id . ' linked to project ' . $projectId . ' via Loxo ID ' . $commentJobId);
                        }
                        //cache()->driver('tenant_database')->set('loxo_comment_job_id_' . $comment->id, $commentJobId, self::CACHE_DURATION);
                    }
                }
            });
        }
        $this->logTime('Comments', $startTime);
        $this->logMemory('Comments', $startMemory);

        \Log::info('[Loxo Fixer]   - Candidate with Loxo ID ' . $loxoId . ' imported as candidate ID ' . $candidate->id);

        $this->logTime("Total import time $loxoId", $startImportTime);
        $this->logMemory("Total import memory $loxoId", $initialMemoryUsage);
    }

    protected function fixFiles(array $personData)
    {
        $loxoId = data_get($personData, 'id');

        $tdCandidateId = cache()->driver('tenant_database')->get('loxo_candidate_id_' . $loxoId);
        if (!$tdCandidateId) {
            \Log::info('[Loxo Fixer] - Could not find candidate for Loxo ID ' . $loxoId);
            return;
        }

        $candidate = Candidate::find($tdCandidateId);

        if (!$candidate) {
            \Log::error('[Loxo Fixer] - Candidate with Loxo ID ' . $loxoId . ' returned TD candidate ID ' . $tdCandidateId . ' but candidate not found');
            return;
        }

        \Log::info('[Loxo Fixer] - Fixing files for candidate with Loxo ID ' . $loxoId);

        collect(data_get($personData, 'documents', []))->merge(collect(data_get($personData, 'resumes', [])))->each(function ($document) use ($candidate) {
            $filePath = data_get($document, 'path');
            if (!$filePath) {
                \Log::error('[Loxo Fixer] - No path present for document ' . data_get($document, 'filename', ''));
                return;
            }

            $fullFilePath = $this->rootDirPath . '/' . $filePath;

            if ($candidate->files()->where('type', File::TYPE_CV)->where('created_at', data_get($document, 'upload_date'))->exists()) {
                \Log::info('[Loxo Fixer] - File ' . $filePath . ' already imported, skipping');
                return;
            }

            \Log::info('[Loxo Fixer] - Storing file ' . $filePath);

            dispatch(function () use ($fullFilePath, $candidate, $document, $filePath) {
                try {
                    retry(3, function () use ($fullFilePath, $candidate, $document, $filePath) {
                        $uploadedFile = new UploadedFile($fullFilePath, data_get($document, 'filename'));
                        $candidate->files()->create([
                            'type' => File::TYPE_CV,
                            'location' => File::store($uploadedFile),
                            'created_at' => data_get($document, 'upload_date'),
                        ]);
                    }, sleepMilliseconds: 1000);
                } catch (\Throwable $e) {
                    \Log::error('[Loxo Fixer]   - Error storing file ' . $filePath . ': ' . $e->getMessage());
                }
            });
        });
    }

    protected function importContact(array $personData)
    {
        $loxoId = data_get($personData, 'id');

        if ($tdContactId = cache()->driver('tenant_database')->get('loxo_contact_id_' . $loxoId)) {
            if ($this->alreadyImportedLogging) {
                \Log::info('[Loxo Fixer] - Contact with Loxo ID ' . $loxoId . ' already imported as contact ID ' . $tdContactId);
            }
            return;
        }

        \Log::info('[Loxo Fixer] - Importing contact with Loxo ID ' . $loxoId);

        // Order all emails by type so that type: Main emails are always first
        $email = collect(data_get($personData, 'emails', []))->sortByDesc(function ($email) {
            return $email['type'] === 'Main' ? 1 : 0;
        })->map(function ($email) {
            return $email['email'];
        })->values()->first();

        if (!$email) {
            $email = Str::random(10) . '@recruitlab.ee';
        }

        // Order all phones by type so that type: Main phones are always first
        $phone = collect(data_get($personData, 'phones', []))->sortByDesc(function ($phone) {
            return $phone['type'] === 'Main' ? 1 : 0;
        })->map(function ($phone) {
            return $phone['phone'];
        })->values()->first();

        $user = $this->getUser($email, [
            'name' => data_get($personData, 'name'),
            'phone' => $phone,
        ]);

        cache()->driver('tenant_database')->set('loxo_contact_id_' . $loxoId, $user->id, self::CACHE_DURATION);

        \Log::info('[Loxo Fixer]   - Contact with email ' . $email . ' imported as user ID ' . $user->id);
    }

    protected function importClients()
    {
        if (cache()->driver('tenant_database')->get('loxo_clients_imported')) {
            \Log::info('[Loxo Fixer] Clients already imported');
            return;
        }

        \Log::info('[Loxo Fixer] Importing clients');
        $clients = $this->readLoxoFile('companies.jsonl');

        foreach ($clients as $client) {
            $this->importClient($client);
        }

        cache()->driver('tenant_database')->set('loxo_clients_imported', time(), self::CACHE_DURATION);
        \Log::info('[Loxo Fixer] Clients imported');
    }

    protected function importClient(array $clientData)
    {
        $loxoId = data_get($clientData, 'id');

        if (data_get($clientData, 'hidden')) {
            \Log::info('[Loxo Fixer] - Client with Loxo ID ' . $loxoId . ' is hidden, skipping import');
            return;
        }

        if ($tdClientId = cache()->driver('tenant_database')->get('loxo_client_id_' . $loxoId)) {
            if ($this->alreadyImportedLogging) {
                \Log::info('[Loxo Fixer] - Client with Loxo ID ' . $loxoId . ' already imported as client ID ' . $tdClientId);
            }
            return;
        }

        \Log::info('[Loxo Fixer] - Importing client with Loxo ID ' . $loxoId);

        $client = new CrmOrganization();
        $client->name = data_get($clientData, 'name');
        $client->client_manager_id = $this->getUser(data_get($clientData, 'ownedBy'))->id;
        $client->created_at = data_get($clientData, 'created');
        $client->save();

        $addresses = data_get($clientData, 'addresses');
        if ($addresses) {
            collect($addresses)->each(function ($address) use ($client) {
                $client->offices()->create([
                    'name' => 'Main',
                    'location' => json_encode([
                        'address' => data_get($address, 'address'),
                        'city' => data_get($address, 'city'),
                        'state' => data_get($address, 'state'),
                        'country' => data_get($address, 'country'),
                        'zip' => data_get($address, 'zip'),
                    ]),
                ]);
            });
        }

        $emails = data_get($clientData, 'emails');
        if ($emails) {
            collect($emails)->each(function ($email) use ($client) {
                $client->contacts()->create([
                    'email' => data_get($email, 'email'),
                ]);
            });
        }

        $phones = data_get($clientData, 'phones');
        if ($phones) {
            collect($phones)->each(function ($phone) use ($client) {
                $client->contacts()->create([
                    'phone' => data_get($phone, 'phone'),
                ]);
            });
        }

        cache()->driver('tenant_database')->set('loxo_client_id_' . $loxoId, $client->id, self::CACHE_DURATION);

        \Log::info('[Loxo Fixer]   - Client with Loxo ID ' . $loxoId . ' imported as client ID ' . $client->id);
    }

    protected function setupCandidateCustomFields(): void
    {
        if (cache()->driver('tenant_database')->get('loxo_candidate_custom_fields_set_up')) {
            \Log::info('[Loxo Fixer] Candidate custom fields already set up');
            return;
        }

        \Log::info('[Loxo Fixer] Setting up candidate custom fields');

        $customFields = [
            [
                'label' => 'Source (Loxo)',
                'type' => 'text',
                'rules' => [],
                'visibility' => null,
                'show_on_candidate_card' => false
            ],
            [
                'label' => 'Global status (Loxo)',
                'type' => 'text',
                'rules' => [],
                'visibility' => null,
                'show_on_candidate_card' => false
            ],
        ];

        $existingCustomFields = Setting::get(Setting::KEY_CANDIDATE_CUSTOM_FIELDS);

        $mergedCustomFields = array_merge($customFields, $existingCustomFields);

        Setting::set(Setting::KEY_CANDIDATE_CUSTOM_FIELDS, $mergedCustomFields);

        cache()->driver('tenant_database')->set('loxo_candidate_custom_fields_set_up', time(), self::CACHE_DURATION);
    }

    protected function setupProjectCustomFields(): void
    {
        if (cache()->driver('tenant_database')->get('loxo_project_custom_fields_set_up')) {
            \Log::info('[Loxo Fixer] Project custom fields already set up');
            return;
        }

        \Log::info('[Loxo Fixer] Setting up project custom fields');

        $customFields = [
            [
                'label' => 'Type',
                'type' => 'select',
                'items' => [
                    'Contract',
                    'Full Time',
                ],
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Category',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Seniority levels',
                'type' => 'tags',
                'items' => [
                    'Director',
                    'VP',
                    'Senior',
                    'C Suite',
                    'Mid Level',
                    'Entry',
                ],
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Address',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'City',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'State',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Country',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Zip',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Remote allowed',
                'type' => 'checkbox',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Compensation',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Fee',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Fee type',
                'type' => 'select',
                'items' => [
                    'Flat',
                    'Percentage',
                ],
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Equity',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Bonus',
                'type' => 'text',
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Bill Rate Type',
                'type' => 'tags',
                'items' => [
                    'Yearly',
                ],
                'rules' => [],
                'original_slug' => null,
            ],
            [
                'label' => 'Pay Rate Type',
                'type' => 'tags',
                'items' => [
                    'Yearly',
                ],
                'rules' => [],
                'original_slug' => null,
            ],
        ];

        $existingCustomFields = Setting::get(Setting::KEY_PROJECT_CUSTOM_FIELDS);

        $mergedCustomFields = array_merge($customFields, $existingCustomFields);

        Setting::set(Setting::KEY_PROJECT_CUSTOM_FIELDS, $mergedCustomFields);

        cache()->driver('tenant_database')->set('loxo_project_custom_fields_set_up', time(), self::CACHE_DURATION);
        \Log::info('[Loxo Fixer] Project custom fields set up');
    }

    protected function importProjects()
    {
        if (cache()->driver('tenant_database')->get('loxo_projects_imported')) {
            \Log::info('[Loxo Fixer] Projects already imported');
            return;
        }

        \Log::info('[Loxo Fixer] Importing projects');
        $projects = $this->readLoxoFile('jobs.jsonl');

        foreach ($projects as $project) {
            $this->importProject($project);
        }

        cache()->driver('tenant_database')->set('loxo_projects_imported', time(), self::CACHE_DURATION);
        \Log::info('[Loxo Fixer] Projects imported');
    }

    protected function importProject(array $projectData)
    {
        $loxoId = data_get($projectData, 'id');

        $cacheKey = 'loxo_project_id_' . $loxoId;

        if ($tdProjectId = cache()->driver('tenant_database')->get($cacheKey)) {
            if ($this->alreadyImportedLogging) {
                \Log::info('[Loxo Fixer] - Project with Loxo ID ' . $loxoId . ' already imported as project ID ' . $tdProjectId);
            }
            return;
        }

        \Log::info('[Loxo Fixer] - Importing project with Loxo ID ' . $loxoId);

        $project = new Project();
        $project->position_name = data_get($projectData, 'title');
        $project->description = data_get($projectData, 'desc');
        $project->created_at = data_get($projectData, 'created');
        $project->start_date = data_get($projectData, 'opened');
        $project->end_date = data_get($projectData, 'filled') ?? now();

//        $projectDataStatus = data_get($projectData, 'status');
//        $project->status = $projectDataStatus === 'Active' ? Project::STATUS_IN_PROGRESS : Project::STATUS_FINISHED;

        // Set all projects to finished for now
        $project->status = Project::STATUS_FINISHED;

        $customFields = [
            'type' => 'Type',
            'category' => 'Category',
            'seniorityLevels' => 'Seniority levels',
            'address' => 'Address',
            'city' => 'City',
            'state' => 'State',
            'country' => 'Country',
            'zip' => 'Zip',
            'remoteAllowed' => 'Remote allowed',
            'comp' => 'Compensation',
            'fee' => 'Fee',
            'feeType' => 'Fee type',
            'equity' => 'Equity',
            'bonus' => 'Bonus',
            'billRateType' => 'Bill Rate Type',
            'payRateType' => 'Pay Rate Type',
        ];

        $projectCustomFields = [];

        foreach ($customFields as $key => $field) {
            if (isset($projectData[$key])) {
                $fieldSettings = collect(Setting::get(Setting::KEY_PROJECT_CUSTOM_FIELDS))->firstWhere('label', $field);

                $newFieldSlug = Str::slug($field, '_');
                if (in_array($fieldSettings['type'], ['select', 'tags'])) {
                    if (is_array($projectData[$key])) {
                        $newFieldValue = array_map(function ($value) {
                            return Str::slug($value, separator: '_');
                        }, $projectData[$key]);
                    } else {
                        $newFieldValue = Str::slug($projectData[$key], separator: '_');
                    }
                } else {
                    $newFieldValue = $projectData[$key];
                }

                $projectCustomFields[$newFieldSlug] = $newFieldValue;
            }
        }

        $project->custom_fields = $projectCustomFields;

        $loxoClientId = data_get($projectData, 'company');
        $clientId = cache()->driver('tenant_database')->get('loxo_client_id_' . $loxoClientId);

        if ($clientId) {
            $project->crm_organization_id = $clientId;
        }

        $project->save();

        cache()->driver('tenant_database')->set($cacheKey, $project->id, self::CACHE_DURATION);

        $loxoOwners = collect(data_get($projectData, 'owners', []))->filter(function ($email) {
            return $email !== self::SYSTEM_USER_EMAIL;
        });

        $firstOwner = $loxoOwners->shift() ?? self::SYSTEM_USER_EMAIL;

        $user = $this->getUser($firstOwner);
        $project->project_manager_id = $user->id;
        $project->save();

        $loxoOwners->each(function ($loxoOwner) use ($project) {
            $user = $this->getUser($loxoOwner);

            if ($user->email !== self::SYSTEM_USER_EMAIL) {
                $project->users()->attach($user->id);
            }
        });

        $stageImported = $project->stages()->create([
            'name' => 'Imported',
            'sort_order' => 0,
        ]);

        $loxoCandidateIds = collect(data_get($projectData, 'candidates', []));

        $candidateIds = collect($loxoCandidateIds)->map(function ($loxoCandidateId) {
            return cache()->driver('tenant_database')->get('loxo_candidate_id_' . $loxoCandidateId);
        })->filter()->unique();

        $stageImported->candidates()->attach($candidateIds);

        $documents = collect(data_get($projectData, 'documents', []));
        if ($documents->isNotEmpty()) {
            $documents->each(function ($document) use ($project) {
                $projectLog = $project->logs()->create([
                    'user_id' => $this->getUser(self::SYSTEM_USER_EMAIL)->id,
                    'content' => '',
                    'is_public' => false,
                    'created_at' => data_get($document, 'upload_date'),
                ]);
                $projectLog->files()->create([
                    'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), data_get($document, 'filename'))),
                    'type' => File::TYPE_PROJECT_LOG,
                    'created_at' => data_get($document, 'upload_date'),
                ]);
            });
        }

        \Log::info('[Loxo Fixer]   - Project with Loxo ID ' . $loxoId . ' imported as project ID ' . $project->id);
    }

    protected function getActivityType(string $activityTypeName)
    {
        static $activityTypeCache = [];

        if (!isset($activityTypeCache[$activityTypeName])) {
            $activityType = ActivityType::where('name', $activityTypeName)->first();
            if (!$activityType) {
                $activityType = new ActivityType();
                $activityType->name = $activityTypeName;
                $activityType->save();
            }

            $activityTypeCache[$activityTypeName] = $activityType;
        }

        return $activityTypeCache[$activityTypeName];
    }

    protected function importActivities()
    {
        if (cache()->driver('tenant_database')->get('loxo_activities_imported')) {
            \Log::info('[Loxo Fixer] Activities already imported');
            return;
        }

        \Log::info('[Loxo Fixer] Importing activities');
        $activities = $this->readLoxoFile('activities.jsonl');

        foreach ($activities as $activity) {
            $loxoId = data_get($activity, 'id');
            try {
                $this->importActivity($activity);
            } catch (\Throwable $e) {
                \Log::error('[Loxo Fixer] - Error importing activity ' . $loxoId . ': ' . $e->getMessage());
            }
        }

        cache()->driver('tenant_database')->set('loxo_activities_imported', time(), self::CACHE_DURATION);
        \Log::info('[Loxo Fixer] Activities imported');
    }

    protected function importActivity(array $activityData)
    {
        $loxoId = data_get($activityData, 'id');

        $cacheKey = 'loxo_activity_id_' . $loxoId;

        if ($tdActivityId = cache()->driver('tenant_database')->get($cacheKey)) {
            if ($this->alreadyImportedLogging) {
                \Log::info('[Loxo Fixer] - Activity with Loxo ID ' . $loxoId . ' already imported as activity ID ' . $tdActivityId);
            }
            return;
        }

        \Log::info('[Loxo Fixer] - Importing activity with Loxo ID ' . $loxoId);

        $loxoPersonId = data_get($activityData, 'person');
        if (!$loxoPersonId) {
            \Log::error('[Loxo Fixer]   - No person ID present for activity ' . $loxoId);
            return;
        }

        try {
            $candidateId = $this->getCandidateId($loxoPersonId);
            $candidate = Candidate::find($candidateId);
            $newActivity = $this->importCandidateActivity($activityData, $loxoId, $candidate);
        } catch (\Throwable $e) {
            try {
                $contact = $this->getContact($loxoPersonId);
                $newActivity = $this->importContactActivity($activityData, $loxoId, $contact);
            } catch (\Throwable $e) {
                \Log::error('[Loxo Fixer]   - Error importing activity ' . $loxoId . ': ' . $e->getMessage());
                return;
            }
        }

        cache()->driver('tenant_database')->set($cacheKey, $newActivity->id, self::CACHE_DURATION);

        \Log::info('[Loxo Fixer]   - Activity with Loxo ID ' . $loxoId . ' imported as activity ID ' . $newActivity->id);
    }

    protected function importCandidateActivity(array $activityData, int $loxoId, Candidate $candidate)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $loxoActivityType = data_get($activityData, 'type');
        [$activityTypeName, $extraData] = match ($loxoActivityType) {
            '2nd Interview' => [Activity::MOVED_TO_STAGE, null],
            'Applied' => [Activity::MOVED_TO_STAGE, null],
            'Deal In Progress' => [null, null],
            'Final Interview' => [Activity::MOVED_TO_STAGE, null],
            'For the Future' => [Activity::MOVED_TO_STAGE, null],
            'General Note/Update' => [Activity::COMMENT_ADDED, null],
            'General Note/Update / Interview Note' => [Activity::COMMENT_ADDED, null],
            'Hired' => [Activity::MOVED_TO_STAGE, null],
            'Hiring Manager Commented' => [Activity::COMMENT_ADDED, null],
            'In Progress' => [Activity::MOVED_TO_STAGE, null],
            'In-Progress' => [null, null],
            'In Progress / Incoming Phone Call' => [Activity::CALL, null],
            'In Progress / Left VoiceMail' => [Activity::CALL, null],
            'In Progress / Outgoing Phone Call' => [Activity::CALL, null],
            'In Progress / Outreach™ Email Sent' => [Activity::MESSAGE_ADDED, null],
            'In Progress / Outreach™ SMS Sent' => [Activity::SMS_OUTBOUND, null],
            'In Progress / Outreach™ Task Completed' => [Activity::COMMENT_ADDED, '<p><strong>Outreach™ Task Completed:</strong></p>'],
            'In Progress / Responded' => [Activity::MESSAGE_INBOUND, null],
            'In Progress / Sent Email' => [Activity::MESSAGE_ADDED, null],
            'In Progress / Sent InMail' => [Activity::COMMENT_ADDED, '<p><strong>Sent InMail:</strong></p>'],
            'In Progress / Sent SMS' => [Activity::SMS_OUTBOUND, null],
            'Interview' => [Activity::MOVED_TO_STAGE, null],
            'Interview / Face to Face' => [Activity::MOVED_TO_STAGE, null],
            'Interview / On-Site' => [Activity::MOVED_TO_STAGE, null],
            'Interview / Phone' => [Activity::MOVED_TO_STAGE, null],
            'Interview / Skype' => [Activity::MOVED_TO_STAGE, null],
            'Loxo AI Sourced' => [Activity::COMMENT_ADDED, '<p><strong>Loxo AI Sourced:</strong></p>'],
            'Marked as Maybe' => [Activity::COMMENT_ADDED, '<p><strong>Marked as Maybe</strong></p>'],
            'Marked as Yes' => [Activity::COMMENT_ADDED, '<p><strong>Marked as Yes</strong></p>'],
            'Moved to 2nd Interview' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Applied' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Final Interview' => [Activity::MOVED_TO_STAGE, null],
            'Moved to For the Future' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Hired' => [Activity::MOVED_TO_STAGE, null],
            'Moved to In Progress' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Interview' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Offer' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Rejected' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Replied' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Screening' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Sourced' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Submitted' => [Activity::MOVED_TO_STAGE, null],
            'Moved to Task' => [Activity::MOVED_TO_STAGE, null],
            'New Lead' => [null, null],
            'Offer' => [Activity::MOVED_TO_STAGE, null],
            'Rejected' => [Activity::MOVED_TO_STAGE, null],
            'Rejected / By Candidate' => [Activity::MOVED_TO_STAGE, null],
            'Rejected / By Client' => [Activity::MOVED_TO_STAGE, null],
            'Rejected / By Recruiter' => [Activity::MOVED_TO_STAGE, null],
            'Replied' => [Activity::MOVED_TO_STAGE, null],
            'Scorecard Submitted' => [Activity::COMMENT_ADDED, '<p><strong>Scorecard Submitted:</strong></p>'],
            'Screening' => [Activity::MOVED_TO_STAGE, null],
            'Sourced' => [Activity::MOVED_TO_STAGE, null],
            'Sourced / Unsourced' => [Activity::MOVED_TO_STAGE, null],
            'Submitted' => [Activity::MOVED_TO_STAGE, null],
            'Task' => [Activity::MOVED_TO_STAGE, null],
        };

        if (!$activityTypeName) {
            \Log::info('[Loxo Fixer] - Activity with Loxo ID ' . $loxoId . ' has no matching activity type');
            throw new \Exception('Activity type not found');
        }

        $this->logTime('Activity type matching', $startTime);
        $this->logMemory('Activity type matching', $startMemory);

        // Get project ID from cache
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $loxoJobId = data_get($activityData, 'job');
        $projectId = cache()->driver('tenant_database')->get('loxo_project_id_' . $loxoJobId);
        $this->logTime('Project ID from cache', $startTime);
        $this->logMemory('Project ID from cache', $startMemory);

        // Get acting user ID
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $actingUserEmail = data_get($activityData, 'createdBy');
        $userId = $actingUserEmail ? $this->getUser($actingUserEmail)->id : null;
        $this->logTime('Acting user ID', $startTime);
        $this->logMemory('Acting user ID', $startMemory);

        // Activity processing
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $documents = collect(data_get($activityData, 'documents', []));

        if ($activityTypeName === Activity::MOVED_TO_STAGE) {
            $stageName = Str::after($loxoActivityType, 'Moved to ');
            if ($projectId) {
                $stage = $this->getStage($loxoJobId, $stageName);

                $notes = data_get($activityData, 'notes');
                if ($notes) {
                    $commentAttachmentLinks = [];
                    $documents->each(function ($document) use (&$commentAttachmentLinks) {
                        $fileName = data_get($document, 'filename');
                        $file = File::create([
                            'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), $fileName)),
                            'type' => File::TYPE_COMMENT_ATTACHMENT,
                            'created_at' => data_get($document, 'upload_date'),
                        ]);

                        $fileUrl = $file->url;

                        $commentAttachmentLinks[] = "<p><a href=\"$fileUrl\"><span class=\"attachment__name\">$fileName</span></a></p>";
                    });

                    $commentLinksStr = implode('', $commentAttachmentLinks);

                    $commentCreatorStr = $actingUserEmail ? '<p><strong>Moved by:</strong> ' . $actingUserEmail . '</p>' : '';

                    $comment = Comment::create([
                        'content' => $commentCreatorStr . $extraData . ($notes ?? '') . $commentLinksStr,
                        'created_at' => data_get($activityData, 'created'),
                        'user_id' => $userId,
                        'project_id' => $projectId,
                        'candidate_id' => $candidate->id,
                    ]);
                }
            } else {
                $activityTypeName = Activity::COMMENT_ADDED;
                $str = '<p><strong>Moved to stage:</strong> ' . $stageName . '</p>';

                if ($actingUserEmail) {
                    $str .= '<p><strong>Moved by:</strong> ' . $actingUserEmail . '</p>';
                }

                $extraData = isset($extraData) ? $extraData . $str : $str;
            }
        }

        if ($activityTypeName === Activity::COMMENT_ADDED) {
            $notes = data_get($activityData, 'notes');
            if ($notes || $extraData) {
                $commentAttachmentLinks = [];
                $documents->each(function ($document) use (&$commentAttachmentLinks) {
                    $fileName = data_get($document, 'filename');
                    $file = File::create([
                        'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), $fileName)),
                        'type' => File::TYPE_COMMENT_ATTACHMENT,
                        'created_at' => data_get($document, 'upload_date'),
                    ]);

                    $fileUrl = $file->url;

                    $commentAttachmentLinks[] = "<p><a href=\"$fileUrl\"><span class=\"attachment__name\">$fileName</span></a></p>";
                });

                $commentLinksStr = implode('', $commentAttachmentLinks);

                $commentCreatorStr = $actingUserEmail ? '<p><strong>Commented by:</strong> ' . $actingUserEmail . '</p>' : '';

                $comment = Comment::create([
                    'content' => $commentCreatorStr . (isset($extraData) ? $extraData . $notes . $commentLinksStr : $notes . $commentLinksStr),
                    'created_at' => data_get($activityData, 'created'),
                    'user_id' => $userId,
                    'project_id' => $projectId,
                    'candidate_id' => $candidate->id,
                ]);
            }
        }

        if ($activityTypeName === Activity::MESSAGE_ADDED) {
            $messageSenderStr = $actingUserEmail ? '<p><strong>Sent by:</strong> ' . $actingUserEmail . '</p>' : '';

            $body = $messageSenderStr . data_get($activityData, 'notes');

            $message = $candidate->messages()->create([
                'subject' => null,
                'body' => $body,
                'created_at' => data_get($activityData, 'created'),
                'user_id' => $userId,
                'project_id' => $projectId,
            ]);

            $documents->each(function ($document) use ($message) {
                $message->files()->create([
                    'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), data_get($document, 'filename'))),
                    'type' => File::TYPE_MESSAGE_ATTACHMENT,
                    'created_at' => data_get($document, 'upload_date'),
                ]);
            });
        }

        if ($activityTypeName === Activity::MESSAGE_INBOUND) {
            $message = $candidate->messages()->create([
                'subject' => null,
                'body' => data_get($activityData, 'notes'),
                'created_at' => data_get($activityData, 'created'),
                'user_id' => null,
                'project_id' => $projectId,
            ]);

            $documents->each(function ($document) use ($message) {
                $message->files()->create([
                    'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), data_get($document, 'filename'))),
                    'type' => File::TYPE_MESSAGE_ATTACHMENT,
                    'created_at' => data_get($document, 'upload_date'),
                ]);
            });
        }

        if ($activityTypeName === Activity::SMS_OUTBOUND) {
            $sentByStr = $actingUserEmail ? '<p><strong>Sent by:</strong> ' . $actingUserEmail . '</p>' : '';

            $body = $sentByStr . data_get($activityData, 'notes');

            $sms = $candidate->sms()->create([
                'body' => $body,
                'created_at' => data_get($activityData, 'created'),
                'user_id' => $userId,
                'project_id' => $projectId,
            ]);
        }

        if ($activityTypeName === Activity::CALL) {
            $callByStr = $actingUserEmail ? '<p><strong>Call by:</strong> ' . $actingUserEmail . '</p>' : '';

            $body = $callByStr . data_get($activityData, 'notes');

            $call = $candidate->calls()->create([
                'content' => $body,
                'user_id' => $userId,
                'created_at' => data_get($activityData, 'created'),
                'start_at' => data_get($activityData, 'created'),
                'did_answer' => true,
                'project_id' => $projectId,
            ]);
        }

        $this->logTime('Activity processing', $startTime);
        $this->logMemory('Activity processing', $startMemory);

        // Create activity
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        $activityType = $this->getActivityType($activityTypeName);

        Activity::unguard();
        $newActivity = Activity::query()->create([
            'created_at' => data_get($activityData, 'created'),
            'activity_type_id' => $activityType->id,
            'stage_id' => isset($stage) ? $stage->id : null,
            'project_id' => $projectId,
            'candidate_id' => $candidate->id,
            'user_id' => $userId,
            'comment_id' => isset($comment) ? $comment->id : null,
            'message_id' => isset($message) ? $message->id : null,
            'sms_id' => isset($sms) ? $sms->id : null,
            'call_id' => isset($call) ? $call->id : null,
        ]);
        Activity::reguard();
        $this->logTime('Create activity', $startTime);
        $this->logMemory('Create activity', $startMemory);

        return $newActivity;
    }

    protected function importContactActivity(array $activityData, int $loxoId, User $targetUser)
    {
        $loxoActivityType = data_get($activityData, 'type');
        [$activityTypeName, $extraData] = match ($loxoActivityType) {
            'In Progress' => [Activity::MESSAGE_ADDED, null],
            'In Progress / Responded' => [Activity::MESSAGE_INBOUND, null],
            'In Progress / Sent Email' => [Activity::MESSAGE_ADDED, null],
            'Rejected' => [Activity::MESSAGE_ADDED, null],
            'Rejected / By Candidate' => [Activity::MESSAGE_ADDED, null],
            'Replied' => [Activity::MESSAGE_ADDED, null],
            'Sourced' => [Activity::MESSAGE_ADDED, null],
        };

        if (!$activityTypeName) {
            \Log::info('[Loxo Fixer] - Activity with Loxo ID ' . $loxoId . ' has no matching activity type');
            throw new \Exception('Activity type not found');
        }

        $loxoJobId = data_get($activityData, 'job');
        $projectId = cache()->driver('tenant_database')->get('loxo_project_id_' . $loxoJobId);

        $actingUserEmail = data_get($activityData, 'createdBy');
        $userId = $actingUserEmail ? $this->getUser($actingUserEmail)->id : null;

        $documents = collect(data_get($activityData, 'documents', []));

        if ($activityTypeName === Activity::MESSAGE_ADDED) {
            $message = $targetUser->messages()->create([
                'subject' => null,
                'body' => data_get($activityData, 'notes'),
                'created_at' => data_get($activityData, 'created'),
                'user_id' => $userId,
                'project_id' => $projectId,
            ]);

            $documents->each(function ($document) use ($message) {
                $message->files()->create([
                    'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), data_get($document, 'filename'))),
                    'type' => File::TYPE_MESSAGE_ATTACHMENT,
                    'created_at' => data_get($document, 'upload_date'),
                ]);
            });
        }

        if ($activityTypeName === Activity::MESSAGE_INBOUND) {
            $message = $targetUser->messages()->create([
                'subject' => null,
                'body' => data_get($activityData, 'notes'),
                'created_at' => data_get($activityData, 'created'),
                'user_id' => null,
                'project_id' => $projectId,
            ]);

            $documents->each(function ($document) use ($message) {
                $message->files()->create([
                    'location' => File::store(new UploadedFile($this->rootDirPath . '/' . data_get($document, 'path'), data_get($document, 'filename'))),
                    'type' => File::TYPE_MESSAGE_ATTACHMENT,
                    'created_at' => data_get($document, 'upload_date'),
                ]);
            });
        }

        $activityType = $this->getActivityType($activityTypeName);

        Activity::unguard();
        $newActivity = Activity::query()->create([
            'created_at' => data_get($activityData, 'created'),
            'activity_type_id' => $activityType->id,
            'project_id' => $projectId,
            'user_id' => $userId,
            'target_user_id' => $targetUser->id,
            'comment_id' => isset($comment) ? $comment->id : null,
            'message_id' => isset($message) ? $message->id : null,
        ]);
        Activity::reguard();

        return $newActivity;
    }

    protected function getStage(int $jobId, string $stageName): Stage
    {
        static $stageCache = [];

        if (!isset($stageCache[$jobId][$stageName])) {
            $projectId = cache()->driver('tenant_database')->get('loxo_project_id_' . $jobId);

            if (!$projectId) {
                throw new \Exception('Project ID not found for Loxo job ID ' . $jobId);
            }

            $stage = Stage::where('name', $stageName)->where('project_id', $projectId)->first();
            if (!$stage) {
                $stage = new Stage();
                $stage->name = $stageName;
                $stage->project_id = $projectId;
                $stage->sort_order = Stage::query()->where('project_id', $projectId)->max('sort_order') + 1;
                $stage->save();
            }

            $stageCache[$jobId][$stageName] = $stage;
        }

        return $stageCache[$jobId][$stageName];
    }

    protected function getCandidate(int $loxoCandidateId): Candidate
    {
        static $candidateCache = [];

        if (!isset($candidateCache[$loxoCandidateId])) {
            $tdCandidateId = cache()->driver('tenant_database')->get('loxo_candidate_id_' . $loxoCandidateId);
            if (!$tdCandidateId) {
                throw new \Exception('Candidate ID not found for Loxo ID ' . $loxoCandidateId);
            }
            $candidate = Candidate::find($tdCandidateId);

            $candidateCache[$loxoCandidateId] = $candidate;
        }

        return $candidateCache[$loxoCandidateId];
    }

    protected function getCandidateId(int $loxoCandidateId): int
    {
        $tdCandidateId = cache()->driver('tenant_database')->get('loxo_candidate_id_' . $loxoCandidateId);
        if (!$tdCandidateId) {
            throw new \Exception('Candidate ID not found for Loxo ID ' . $loxoCandidateId);
        }
        return $tdCandidateId;
    }

    protected function getTag(string $tagName): Tag
    {
        static $tagCache = [];

        if (!isset($tagCache[$tagName])) {
            $tag = Tag::where('name', $tagName)->first();
            if (!$tag) {
                $tag = new Tag();
                $tag->name = $tagName;
                $tag->save();
            }

            $tagCache[$tagName] = $tag;
        }

        return $tagCache[$tagName];
    }

    protected function getContact(int $loxoContactId): User
    {
        static $contactCache = [];

        if (!isset($contactCache[$loxoContactId])) {
            $tdContactId = cache()->driver('tenant_database')->get('loxo_contact_id_' . $loxoContactId);
            if (!$tdContactId) {
                throw new \Exception('Contact ID not found for Loxo ID ' . $loxoContactId);
            }
            $contact = User::find($tdContactId);

            $contactCache[$loxoContactId] = $contact;
        }

        return $contactCache[$loxoContactId];
    }

    protected function moveCandidatesToCorrectStages()
    {
        if (cache()->driver('tenant_database')->get('loxo_candidates_moved_to_correct_stages')) {
            \Log::info('[Loxo Fixer] Candidates already moved to correct stages');
            return;
        }
        \Log::info('[Loxo Fixer] Moving candidates to correct stages');

        $movedToStageActivityType = ActivityType::where('name', Activity::MOVED_TO_STAGE)->first();

        $latestActivities = DB::connection('tenant')
            ->select(<<<SQL
WITH ranked_activities AS (
    SELECT
        candidate_id,
        project_id,
        stage_id,
        created_at,
        ROW_NUMBER() OVER (PARTITION BY candidate_id, project_id ORDER BY created_at DESC) AS rn
    FROM
        activities
    WHERE
        activity_type_id = :activity_type_id
)
SELECT
    candidate_id,
    project_id,
    stage_id,
    created_at
FROM
    ranked_activities
WHERE
    rn = 1;
SQL, ['activity_type_id' => $movedToStageActivityType->id]);

        foreach ($latestActivities as $latestActivity) {
            $candidateId = $latestActivity->candidate_id;
            $projectId = $latestActivity->project_id;
            $stageId = $latestActivity->stage_id;

            $existingLink = DB::connection('tenant')
                ->table('applications')
                ->leftJoin('stages', 'applications.stage_id', '=', 'stages.id')
                ->where('candidate_id', $candidateId)
                ->where('stages.project_id', $projectId)
                ->first();

            if ($existingLink) {
                $existingStageId = $existingLink->stage_id;
                if ($existingStageId === $stageId) {
                    continue;
                }

                DB::connection('tenant')
                    ->table('applications')
                    ->where('candidate_id', $candidateId)
                    ->where('stage_id', $existingStageId)
                    ->delete();

                \Log::info('[Loxo Fixer]   - Candidate with ID ' . $candidateId . ' in project ' . $projectId . ' removed from stage ' . $existingStageId);
            }

            $stage = Stage::find($stageId);
            $stage->candidates()->attach($candidateId);

            \Log::info('[Loxo Fixer]   - Candidate with ID ' . $candidateId . ' in project ' . $projectId . ' moved to stage ' . $stageId);
        }

        \Log::info('[Loxo Fixer] Candidates moved to correct stages');
        cache()->driver('tenant_database')->set('loxo_candidates_moved_to_correct_stages', time(), self::CACHE_DURATION);
    }

    protected function linkCommentsToJobs(): void
    {
        if (cache()->driver('tenant_database')->get('loxo_comments_linked_to_jobs')) {
            \Log::info('[Loxo Fixer] Comments already linked to jobs');
            return;
        }

        \Log::info('[Loxo Fixer] Linking comments to jobs');

        $comments = Comment::all();

        foreach ($comments as $comment) {
            $loxoJobId = cache()->driver('tenant_database')->get('loxo_comment_job_id_' . $comment->id);

            if ($loxoJobId) {
                $projectId = cache()->driver('tenant_database')->get('loxo_project_id_' . $loxoJobId);
                if ($projectId) {
                    $comment->project_id = $projectId;
                    $comment->save();
                    \Log::info('[Loxo Fixer]   - Comment with ID ' . $comment->id . ' linked to project ' . $projectId . ' via Loxo ID ' . $loxoJobId);
                }
            }
        }

        \Log::info('[Loxo Fixer] Comments linked to jobs');
        cache()->driver('tenant_database')->set('loxo_comments_linked_to_jobs', time(), self::CACHE_DURATION);
    }
}
