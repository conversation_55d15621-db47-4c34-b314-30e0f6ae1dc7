<?php

namespace App\DataExchange\StructuredJobs\Outputs;

use App\DataExchange\StructuredJobs\BaseOutput;
use App\DataExchange\StructuredJobs\Interfaces\CombinableFeed;
use App\DataExchange\StructuredJobs\Interfaces\ControlsFields;
use App\DataExchange\StructuredJobs\Interfaces\PassiveOutput;
use App\DataExchange\StructuredJobs\Serializers\Handlers\CarbonHandlerISO8601;
use App\DataExchange\StructuredJobs\Serializers\StructuredJobAdXMLSerializer;
use App\DataExchange\TalentComXml\Types\Job;
use App\DataExchange\TalentComXml\Types\Salary;
use App\DataExchange\TalentComXml\Types\Source;
use App\Helpers;
use App\Models\ApiKey;
use App\Models\Setting;
use App\Models\StructuredJobAd;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Storage;
use Ramsey\Uuid\Uuid;

class TalentComXmlOutput extends BaseOutput implements CombinableFeed, ControlsFields, PassiveOutput
{
    protected $jobs = null;

    protected ?string $publisher = null;
    protected ?string $publisherurl = null;

    public function getOutput()
    {
        $serializer = new StructuredJobAdXMLSerializer([
            new CarbonHandlerISO8601,
        ]);

        $source = new Source;
        $source->publisher = $this->publisher ?? Setting::get(Setting::KEY_ORGANIZATION_NAME, $this->integration->team);
        $source->publisherurl = $this->publisherurl ?? Helpers::toValidUrl(Setting::get(Setting::KEY_ORGANIZATION_WEBSITE_URL, $this->integration->team));
        $source->lastbuilddate = now()->format('c');
        $source->jobs = $this->jobs ?? $this->getFeedJobs();

        return $serializer->serialize($source);
    }

    public function getFeedJobs(): array
    {
        return $this->integration
            ->structuredJobAds()
            ->active()
            ->get()->map(function (StructuredJobAd $ad) {
                return $this->structuredJobAdToXmlJob($ad);
            })->filter()
            ->toArray();
    }

    protected function structuredJobAdToXmlJob(StructuredJobAd $ad): ?Job
    {
        $job = new Job;

        // Required fields
        $job->referencenumber = $ad->external_id . '_' . $this->integration->id;
        $job->title = $ad->position_name;
        $job->company = Setting::get(Setting::KEY_ORGANIZATION_NAME, $ad->project?->team) ?? '';
        $job->city = $ad->indeed_city;
        $job->state = $ad->indeed_state;
        $job->country = $ad->indeed_country;
        $job->dateposted = $ad->created_at;
        if (!$ad->landing && !$ad->custom_job_ad_url) {
            return null;
        }
        $job->url = $ad->landing ? $this->addUtmTags($ad->landing->permalink) : $ad->custom_job_ad_url;
        $job->description = nl2br($ad->description);

        // Recommended fields
        if ($ad->deadline_at) {
            $job->expirationdate = $ad->deadline_at->endOfDay();
        }
        if ($ad->main_job_type) {
            $job->jobtype = Job::JOB_TYPES[$ad->main_job_type];
        }
        $job->isremote = $ad->is_remote ? 'yes' : 'no';
        if ($ad->talent_category) {
            $job->category = Job::JOB_CATEGORIES[$ad->talent_category];
        }
        $logoLocation = $ad->employer_logo_location ?? Setting::get(Setting::KEY_ORGANIZATION_LOGO, $ad->project?->team);
        if ($logoLocation) {
            $job->logo = $this->getFileLocationBase64($logoLocation);
        }
        if ($ad->salary_from || $ad->salary_to) {
            $salary = new Salary;
            if ($ad->salary_from) {
                $salary->salary_min = $ad->salary_from;
            }
            if ($ad->salary_to) {
                $salary->salary_max = $ad->salary_to;
            }
            if ($ad->salary_currency) {
                $salary->salary_currency = $ad->salary_currency;
            }
            if ($ad->salary_period) {
                $salary->period = match ($ad->salary_period) {
                    'HOURLY' => Job::SALARY_HOUR,
                    'MONTHLY' => Job::SALARY_MONTH,
                    'ANNUALLY' => Job::SALARY_YEAR,
                    default => Job::SALARY_YEAR,
                };
            }
            $salary->type = 'BASE_SALARY';
            $job->salary = $salary;
        }

        $job->talentApplyData = $this->buildTalentApply($ad);

        return $job;
    }

    /**
     * @return $this
     */
    public function setJobs(array $jobs): static
    {
        $this->jobs = $jobs;

        return $this;
    }

    /**
     * @return $this;
     */
    public function setPublisher(?string $publisher): static
    {
        $this->publisher = $publisher;

        return $this;
    }

    /**
     * @return $this;
     */
    public function setPublisherUrl(?string $publisherUrl): static
    {
        $this->publisherurl = $publisherUrl;

        return $this;
    }

    public static function getRequiredFields(): array
    {
        return [
            'position_name',
            'description',
            'target_stage_id',
            'indeed_country',
            'indeed_state',
            'indeed_city',
        ];
    }

    public static function getAcceptedFields(): array
    {
        $base = self::getRequiredFields();

        return array_merge($base, [
            'landing_id',
            'salary_from',
            'salary_to',
            'salary_period',
            'salary_currency',
            'screening_questions',
            'main_job_type',
            'employer_logo_location',
            'deadline_at',
            'is_remote',
            'talent_category',
        ]);
    }

    protected function addUtmTags(string $url): string
    {
        $queryString = http_build_query([
            'utm_source' => 'TalentCom',
            'utm_campaign' => 'rl_apply',
        ]);

        return $url . '?' . $queryString;
    }

    public static function getAcceptedCreatives(): array
    {
        return [
            StructuredJobAd::CREATIVE_LANDING,
        ];
    }

    protected function buildTalentApply(StructuredJobAd $ad): string
    {
        $talentApplyParameters = [
            'talent-apply-posturl' => Helpers::getCurrentTenantUri(
                '/api/v1/talentcom-apply',
                ['api_key' => $this->getApiKey()]
            ),
        ];

        if (count($ad->getScreeningQuestions())) {
            $talentApplyParameters['talent-apply-questions'] = Helpers::getCurrentTenantUri(
                '/api/v1/jobAdFeeds/' . $this->integration->feed_slug . '/talentcom/' . $ad->external_id . '/screening-questions',
                ['api_key' => $this->getApiKey()]
            );
        }

        return http_build_query($talentApplyParameters);
    }

    public function getApiKey(): string
    {
        $apiKey = ApiKey::query()->whereJsonContains('permissions', ApiKey::PERMISSION_FEEDS_READ)->first();

        if ($apiKey) {
            return $apiKey->api_key;
        }

        return ApiKey::query()->create([
            'name' => 'Talent.com',
            'api_key' => Uuid::uuid4(),
            'permissions' => [ApiKey::PERMISSION_FEEDS_READ],
        ])->api_key;
    }

    protected function getFileLocationBase64(string $location): string
    {
        $logoUrl = Storage::url($location);
        $client = new Client;
        $res = $client->get($logoUrl);

        return base64_encode($res->getBody()->getContents());
    }

    public function screeningQuestions(string $adId)
    {
        $parts = explode('_', $adId);

        /** @var StructuredJobAd $ad */
        $ad = $this->integration->structuredJobAds()->where('id', end($parts))->first();

        if (!$ad) {
            abort(404);
        }

        $questions = $ad->getScreeningQuestions();

        if (!$questions) {
            return [];
        }

        return $questions->map(function (string $question, $index) use ($ad) {
            return [
                'id' => "{$ad->external_id}_{$index}",
                'type' => 'text',
                'question' => $question,
                'required' => true,
            ];
        });
    }

    public function getContentType(): string
    {
        return 'application/xml';
    }
}
