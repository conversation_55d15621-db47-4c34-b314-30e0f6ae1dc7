<?php

namespace App\DataExchange\ApprenticeshipsGovUk;

use App\DataExchange\ApprenticeshipsGovUk\Api\lib\Api\AccountLegalEntitiesApi;
use App\DataExchange\ApprenticeshipsGovUk\Api\lib\Api\ReferenceDataApi;
use App\DataExchange\ApprenticeshipsGovUk\Api\lib\Configuration;
use App\DataExchange\ApprenticeshipsGovUk\Api\lib\Model\GetAccountLegalEntitiesItem;
use App\DataExchange\ApprenticeshipsGovUk\Api\lib\Model\GetTrainingCoursesListResponseItem;
use App\Models\Integration;

class ApiHelper
{
    public static function entities(): array
    {
        $entities = (new AccountLegalEntitiesApi(config: self::getConfig()))->getAccountlegalentities('1');
        return collect($entities->getAccountLegalEntities())->map(function (GetAccountLegalEntitiesItem $item) {
            return ['value' => $item->getAccountLegalEntityPublicHashedId(), 'label' => $item->getAccountLegalEntityName()];
        })->sortBy('label')->values()->toArray();
    }

    // TODO: responses actually cacheable below this line
    public static function qualifications(): array
    {
        $qualifications = (new ReferenceDataApi(config: self::getConfig()))->getReferencedataQualifications('1');
        return collect($qualifications->getQualifications())->map(function (string $qualification) {
            return ['value' => $qualification, 'label' => $qualification];
        })->sortBy('label')->values()->toArray();
    }

    public static function skills(): array
    {
        $qualifications = (new ReferenceDataApi(config: self::getConfig()))->getReferencedataSkills('1');
        return collect($qualifications->getCandidateSkills())->map(function (string $skill) {
            return ['value' => $skill, 'label' => $skill];
        })->sortBy('label')->values()->toArray();
    }

    public static function courses(): array
    {
        $qualifications = (new ReferenceDataApi(config: self::getConfig()))->getReferencedataCourses('1');
        return collect($qualifications->getTrainingCourses())->map(function (GetTrainingCoursesListResponseItem $item) {
            return ['value' => $item->getLarsCode(), 'label' => $item->getTitle()];
        })->sortBy('label')->values()->toArray();
    }

    private static function getIntegration(): Integration
    {
        return Integration::query()
            ->where('remote_type', Integration::TYPE_APPRENTICESHIPS_GOV_UK)
            ->active()
            ->first();
    }

    /**
     * @return Configuration
     */
    public static function getConfig(): Configuration
    {
        // TODO: enforce the single integration somehow?
        /** @var Integration $integration */
        $integration = self::getIntegration();

        $config = new Configuration();

        if (app()->environment('production')) {
            $config->setHost('https://api.apprenticeships.education.gov.uk/managevacancies');
        } else {
            $config->setHost('https://api-sandbox.apprenticeships.education.gov.uk/managevacancies');
        }

        $config->setApiKey('Ocp-Apim-Subscription-Key', $integration->api_key);

        return $config;
    }
}
