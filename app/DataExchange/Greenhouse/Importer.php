<?php

namespace App\DataExchange\Greenhouse;

use App\Models\Activity;
use App\Models\ActivityType;
use App\Models\Candidate;
use App\Models\Comment;
use App\Models\Consent;
use App\Models\DropoutReason;
use App\Models\File;
use App\Models\Project;
use App\Models\Stage;
use App\Models\User;
use App\Models\Website;
use Carbon\Carbon;
use Greenhouse\GreenhouseToolsPhp\Clients\Exceptions\GreenhouseAPIResponseException;
use Greenhouse\GreenhouseToolsPhp\GreenhouseService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;

class Importer
{
    protected ActivityType $sourceOverrideActivityType;
    public ?GreenhouseService $service = null;
    private Website $website;

    const string ADMIN_RECRUITER_EMAIL = '<EMAIL>';
    const string ADMIN_RECRUITER_NAME = 'Talent Admin';

    const int REFERRAL_SOURCE_ID = 4000031002;

    const int REJECTION_REASON_TYPE_COMPANY = 4000000002;
    const int REJECTION_REASON_TYPE_CANDIDATE = 4000001002;

    const GDPR_SOURCE_OK = [
        'CV.ee',
        'Facebook ad',
        'Facebook',
        'Internal Applicant',
        'Jobs page on your website',
        'Karjääristuudio',
        'LinkedIn (Prospecting)',
        'LinkedIn (Social Media)',
        'LinkedIn',
        'Praktikaleht',
        'Referral',
    ];

    const USER_EMAIL_MAPPING = [
        '<EMAIL>' => '<EMAIL>',
        '<EMAIL>' => '<EMAIL>',
        '<EMAIL>' => '<EMAIL>',
        '<EMAIL>' => '<EMAIL>',
        '<EMAIL>' => '<EMAIL>',
        '<EMAIL>' => '<EMAIL>',
        '<EMAIL>' => '<EMAIL>',
    ];

    const CACHE_DURATION = 60 * 60 * 24 * 90; // 90 days

    public function __construct()
    {
        $apiKey = config('services.greenhouse.api_key');

        if (!$apiKey) {
            throw new \Exception('Greenhouse API key is not set');
        }

        $this->service = new GreenhouseService([
            'apiKey' => $apiKey,
        ]);

        $this->website = app(\Hyn\Tenancy\Environment::class)->tenant();

        $this->sourceOverrideActivityType = ActivityType::query()->firstOrCreate(['name' => Activity::ADDED_TO_STAGE_MANUAL_SOURCE_OVERRIDE]);
    }

    public function getFallbackUser(): User
    {
        return User::firstOrCreate([
            'email' => self::ADMIN_RECRUITER_EMAIL,
        ], [
            'name' => self::ADMIN_RECRUITER_NAME,
            'active' => false,
            'role' => User::ROLE_REGULAR,
        ]);
    }

    public function requestAllPages($endpoint, $params = [])
    {
        \Log::info('[Greenhouse] - Requesting all pages for ' . $endpoint);
        $pagingParams = [];
        $lastParams = [];
        $results = [];
        $nextLink = null;
        do {
            $harvestService = $this->service->getHarvestService();
            $queryParams = array_merge($params, $pagingParams);
            if (!empty($lastParams)) {
                $queryParams['skip_count'] = 'true';
            }
            \Log::info('[Greenhouse]   - Requesting page ' . ($pagingParams['page'] ?? 1) . '/' . ($lastParams['page'] ?? '?'));
            try {
                $response = $harvestService->$endpoint($queryParams);
            } catch (GreenhouseAPIResponseException $e) {
                /**
                 * Harvest API requests for approved Greenhouse partners and customer-built custom integrations are limited to the amount specified in the returned X-RateLimit-Limit header, per 10 seconds. Unlisted vendors may be subject to additional rate limits.
                 *
                 * Exceeding the limit will return an HTTP 429 response. Status requests for approved integrations can be checked using the X-RateLimit-Remaining header. In the HTTP 429 response, the X-RateLimit-Reset header is the timestamp indicating when you can try again while the Retry-After header is the number of seconds indicating when you can try again.
                 */
                $guzzleException = $e->getPrevious();
                if ($guzzleException->getCode() === 429) {
                    $retryAfter = $guzzleException->getResponse()->getHeader('Retry-After')[0] ?? 0;
                    \Log::info('[Greenhouse]   - Rate limit exceeded. Retrying after ' . $retryAfter . ' seconds');
                    sleep($retryAfter);
                    continue;
                }
            } catch (\Exception $e) {
                \Log::error('[Greenhouse]   - Error requesting page ' . ($pagingParams['page'] ?? 1) . '/' . ($lastParams['page'] ?? '?') . ': ' . $e->getMessage());
                continue;
            }
            $results = array_merge($results, json_decode($response, true));

            $nextLink = $harvestService->nextLink();

            if ($nextLink) {
                // nextLink is something like: https://harvest.greenhouse.io/v1/candidates?page=2&per_page=100
                // the getCandidates method accepts an array of parameters to pass to the endpoint
                // We need to extract the query parameters from the URL and pass them to the method
                // nextLink is empty when there are no more pages
                $nextLink = parse_url($nextLink);
                parse_str($nextLink['query'], $pagingParams);
            }

            $lastLink = $harvestService->lastLink();
            if ($lastLink) {
                $lastLink = parse_url($lastLink);
                parse_str($lastLink['query'], $lastParams);
            }
        } while ($nextLink);

        \Log::info('[Greenhouse]   - Retrieved ' . count($results) . ' results from ' . $endpoint);

        // Create a temporary file with the results, create a UploadedFile out of it and store it in S3
        $tempFile = tempnam(sys_get_temp_dir(), 'greenhouse');
        file_put_contents($tempFile, json_encode($results, JSON_PRETTY_PRINT));
        $uploadedFile = new UploadedFile($tempFile, 'greenhouse.json', 'application/json', null, true);
        $parts = [
            app()->environment(),
            $this->website->uuid,
            'greenhouse_imports',
        ];
        $prefix = implode('/', $parts);
        $paramsStr = collect($params)->map(function ($value, $key) {
            return $key . '=' . $value;
        })->implode('&');
        if ($paramsStr) {
            $paramsStr = '_' . $paramsStr;
        }
        $storedFile = $uploadedFile->storeAs(
            $prefix,
            'greenhouse_' . $endpoint . $paramsStr . '_' . now()->toIso8601String() . '.json',
        );
        \Log::info('[Greenhouse]   - Stored ' . count($results) . ' results from ' . $endpoint . ' in ' . $storedFile);

        return $results;
    }

    public function import()
    {
        DB::disableQueryLog();

        // 1. Import users
        // 2. Import jobs
        // 3. Import candidates
        // 4. Import scorecards

        \Log::info('[Greenhouse] Starting Greenhouse import on ' . now()->toIso8601String());
        \Log::info('[Greenhouse] Importing users');
//        $this->importUsers();
        \Log::info('[Greenhouse] Finished importing users');

        \Log::info('[Greenhouse] Importing jobs');
//        $this->importJobs();
        \Log::info('[Greenhouse] Finished importing jobs');

        \Log::info('[Greenhouse] Importing candidates');
//        $this->importCandidates();
        \Log::info('[Greenhouse] Finished importing candidates');

        \Log::info('[Greenhouse] Importing scorecards');
        $this->importScorecards();
        \Log::info('[Greenhouse] Finished importing scorecards');

        \Log::info('[Greenhouse] Finished Greenhouse import on ' . now()->toIso8601String());

        DB::enableQueryLog();
    }

    public function getGreenhouseUserName(int $greenhouseUserId): ?string
    {
        $rawUser = cache()->driver('tenant_database')->get('greenhouse_user_id_' . $greenhouseUserId);
        if (!$rawUser) {
            return null;
        }

        return data_get($rawUser, 'name');
    }

    public function findOrCreateUser(int $greenhouseUserId): ?User
    {
        static $userCache = [];

        if (isset($userCache[$greenhouseUserId])) {
            return $userCache[$greenhouseUserId];
        }

        $user = cache()->driver('tenant_database')->get('greenhouse_user_id_' . $greenhouseUserId);
        if (!$user) {
            return null;
        }

        $email = data_get($user, 'primary_email_address');
        if (array_key_exists($email, self::USER_EMAIL_MAPPING)) {
            $email = self::USER_EMAIL_MAPPING[$email];
        }

        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            $userCache[$greenhouseUserId] = $existingUser;
            return $existingUser;
        }

        $userCache[$greenhouseUserId] = $this->getFallbackUser();

        return $userCache[$greenhouseUserId];
    }

    public function importUsers()
    {
        $users = $this->requestAllPages('getUsers');
        collect($users)->each(function ($user) {
            cache()->driver('tenant_database')->set('greenhouse_user_id_' . data_get($user, 'id'), $user, self::CACHE_DURATION);
        });
    }

    public function importJobs()
    {
        $jobs = $this->requestAllPages('getJobs');

        $jobStages = $this->requestAllPages('getJobStages', ['per_page' => 500]);
        $jobStagesPerJob = collect($jobStages)->groupBy('job_id');

        collect($jobs)->each(function ($job) use ($jobStagesPerJob) {
            $jobId = data_get($job, 'id');
            $jobStagesForJob = $jobStagesPerJob->get($jobId, []);
            $this->importJob($job, $jobStagesForJob);
        });
    }

    public function importJob(array $job, $jobStages)
    {
        $jobId = data_get($job, 'id');

        \Log::info('[Greenhouse]');
        \Log::info('[Greenhouse] Importing job ' . $jobId);

        // Check if we already have this job in our database
        $existingProject = cache()->driver('tenant_database')->get('greenhouse_job_id_' . $jobId);

        if ($existingProject) {
            \Log::info('[Greenhouse] - Job ' . $jobId . ' already exists in our database as project ' . $existingProject);
            return;
        }

        $project = new Project();
        $project->position_name = data_get($job, 'name');
        $project->description = data_get($job, 'notes');
        $project->accessible_only_members = data_get($job, 'confidential');

        $responsible = null;

        $recruiters = collect(data_get($job, 'hiring_team.recruiters'))->map(function ($recruiter) use (&$responsible) {
            $user = $this->findOrCreateUser(data_get($recruiter, 'id'));
            if (data_get($recruiter, 'responsible')) {
                $responsible = $user;
                return null;
            } else {
                return $user;
            }
        });

        $coordinators = collect(data_get($job, 'hiring_team.coordinators'))->map(function ($coordinator) use (&$responsible) {
            $user = $this->findOrCreateUser(data_get($coordinator, 'id'));
            if (data_get($coordinator, 'responsible') && !$responsible) {
                $responsible = $user;
                return null;
            } else {
                return $user;
            }
        });

        $allTeamMembers = $recruiters->merge($coordinators)->filter();

        if (!$responsible) {
            $responsible = $allTeamMembers->first();
        }

        if (!$responsible) {
            \Log::info('[Greenhouse] - Job ' . $jobId . ' has no team members. Assigning to default responsible.');
            $responsible = $this->getFallbackUser();
        }

        $teamMembers = $allTeamMembers->reject(function (User $user) use ($responsible) {
            return $user->is($responsible);
        })->unique('email');

        $project->project_manager_id = $responsible->id;

        \Log::info('[Greenhouse] - Responsible: ' . $responsible->name);
        \Log::info('[Greenhouse] - Team members: ' . $teamMembers->pluck('name')->implode(', ') . ' (' . $teamMembers->count() . ')');

        $project->status = match (data_get($job, 'status')) {
            'open' => Project::STATUS_IN_PROGRESS,
            'closed' => Project::STATUS_FINISHED,
            'draft' => Project::STATUS_DRAFT,
            default => Project::STATUS_IN_PROGRESS,
        };

        $project->custom_fields = data_get($job, 'custom_fields');

        $project->created_at = data_get($job, 'created_at');
        $project->start_date = data_get($job, 'opened_at');
        $project->end_date = data_get($job, 'closed_at');

        $project->save();

        $project->users()->sync($teamMembers->pluck('id'));

        if (empty($jobStages)) {
            $jobStages = $this->requestAllPages('getJobStagesForJob', ['id' => $jobId]);
            $jobStages = collect($jobStages)->sortBy('priority');
        }

        $jobStages->each(function ($stage) use ($project) {
            $newStage = $project->stages()->create([
                'name' => data_get($stage, 'name'),
                'sort_order' => data_get($stage, 'priority'),
            ]);

            cache()->driver('tenant_database')->set('greenhouse_stage_id_' . data_get($stage, 'id'), $newStage->id, self::CACHE_DURATION);
        });
        \Log::info('[Greenhouse] - Created ' . count($jobStages) . ' stages for job ' . $jobId);

        cache()->driver('tenant_database')->set('greenhouse_job_id_' . $jobId, $project->id, self::CACHE_DURATION);
        \Log::info('[Greenhouse] - Imported job ' . $jobId . ' (' . $project->position_name . ')');
    }

    public function importCandidates()
    {
        $candidates = $this->requestAllPages('getCandidates', ['per_page' => 500]);
//        $candidates = $this->harvestService->getCandidates();
//        return $candidates;
//        $candidates = json_decode(file_get_contents(base_path('greenhouse_getCandidates_per_page=500_2024-09-06T14:45:44+00:00.json')), true);
//        $candidates = json_decode(file_get_contents(base_path('greenhouse_candidates_test.json')), true);
        $failedCandidateIds = [];

        collect($candidates)->each(function ($candidate) use (&$failedCandidateIds) {
            try {
                $this->importCandidate($candidate);
            } catch (\Exception $e) {
                $failedCandidateIds[] = data_get($candidate, 'id');
                \Log::error('[Greenhouse] - Error importing candidate ' . data_get($candidate, 'id') . ': ' . $e->getMessage());
            }
        });

        \Log::info('[Greenhouse] - Failed to import ' . count($failedCandidateIds) . ' candidates: ' . implode(', ', $failedCandidateIds));
    }

    public function importCandidate(array $candidate)
    {
        $candidateId = data_get($candidate, 'id');
        \Log::info('[Greenhouse]');
        \Log::info('[Greenhouse] Importing candidate ' . $candidateId);

        // Check if we already have this candidate in our database
        $existingCandidate = cache()->driver('tenant_database')->get('greenhouse_candidate_id_' . $candidateId);

        if ($existingCandidate) {
            \Log::info('[Greenhouse] - Candidate ' . $candidateId . ' already exists in our database as candidate ' . $existingCandidate);
            return;
        }

        // E-mails
        // A candidate can have 0 or more e-mail addresses.
        // Each e-mail address is either of type 'personal', 'work' or 'other'.
        // We will prefer them in that order, but we will take the first one we find.
        $candidateEmails = collect($candidate['email_addresses'])->sortBy(function ($email) {
            return $email['type'] === 'personal' ? 1 : ($email['type'] === 'work' ? 2 : 3);
        });
        $candidateEmail = $candidateEmails->first();

        // Phone numbers
        // A candidate can have 0 or more phone numbers.
        // Each phone number is either of type 'home', 'work', 'mobile', 'skype' or 'other'.
        $candidatePhones = collect($candidate['phone_numbers'])->sortBy(function ($phone) {
            return match ($phone['type']) {
                'home' => 1,
                'work' => 5,
                'mobile' => 2,
                'skype' => 3,
                default => 4,
            };
        });
        $candidatePhone = $candidatePhones->first();

        // Social media profiles
        $candidateSocialMedia = collect($candidate['social_media_addresses']);
        $linkedinProfile = $candidateSocialMedia->first(function ($socialMedia) {
            return str_contains(data_get($socialMedia, 'value'), 'linkedin.com');
        });
        $linkedinProfile = $linkedinProfile ? $linkedinProfile['value'] : null;

        if ($linkedinProfile) {
            // Strip query parameters from linkedin url
            $linkedinProfile = explode('?', $linkedinProfile)[0];
        }

        $candidateIsPrivate = data_get($candidate, 'is_private');

        $candidateObject = Candidate::firstOrNew([
            'name' => $candidate['first_name'] . ' ' . $candidate['last_name'],
            'created_at' => $candidate['created_at'],
        ]);

        $newCandidate = $candidateObject->fill([
            'name' => $candidate['first_name'] . ' ' . $candidate['last_name'],
            'email' => $candidateEmail['value'] ?? null,
            'phone' => $candidatePhone['value'] ?? null,
            'linkedin_url' => $linkedinProfile,
            'is_private' => $candidateIsPrivate,
            'created_at' => $candidate['created_at'],
        ]);

        $newCandidate->save();

        // Educations
        $educations = collect($candidate['educations'])->map(function ($education) {
            $startDate = data_get($education, 'start_date');
            $startYear = $startDate ? date('Y', strtotime($startDate)) : null;

            $endDate = data_get($education, 'end_date');
            $endYear = $endDate ? date('Y', strtotime($endDate)) : null;

            return [
                'degree' => null,
                'degree_title' => data_get($education, 'degree'),
                'programme' => data_get($education, 'discipline'),
                'institution' => data_get($education, 'school_name'),
                'start_year' => $startYear,
                'end_year' => $endYear,
            ];
        });

        $newCandidate->educations()->createMany($educations);

        // Work Experiences
        $employments = collect($candidate['employments'])->map(function ($employment) {
            $startDateStr = data_get($employment, 'start_date');
            $startDate = $startDateStr ? date('Y-m-d', strtotime($startDateStr)) : null;

            $endDateStr = data_get($employment, 'end_date');
            $endDate = $endDateStr ? date('Y-m-d', strtotime($endDateStr)) : null;

            return [
                'position_title' => data_get($employment, 'title'),
                'employer_name' => data_get($employment, 'company_name'),
                'date_from' => $startDate,
                'date_until' => $endDate,
            ];
        });

        $newCandidate->employments()->createMany($employments);

        // Attachments
        $attachments = collect($candidate['attachments'])->mapWithKeys(function ($attachment) use ($newCandidate) {
            $fileUrl = data_get($attachment, 'url');
            if (!$fileUrl) {
                return [];
            }

            $fileType = match ($attachmentType = data_get($attachment, 'type')) {
                'resume' => File::TYPE_CV,
                'other' => File::TYPE_CANDIDATE_OTHER,
                default => $attachmentType,
            };

            $storedFile = File::create([
                'type' => $fileType,
//                'location' => File::store(File::getUploadedFileFromUrl($fileUrl, null, data_get($attachment, 'filename')), removeLocal: true),
                'fileable_type' => Candidate::class,
                'fileable_id' => $newCandidate->id,
                'created_at' => data_get($attachment, 'created_at'),
            ]);

            cache()->driver('tenant_database')->set('greenhouse_attachment_id_' . data_get($attachment, 'id'), $storedFile->id, self::CACHE_DURATION);

            dispatch(function () use ($attachment, $storedFile, $fileUrl) {
                try {
                    retry(3, function () use ($attachment, $storedFile, $fileUrl) {
                        $storedFile->location = File::store(File::getUploadedFileFromUrl($fileUrl, null, data_get($attachment, 'filename')), removeLocal: true);
                        $storedFile->save();
                    }, sleepMilliseconds: 1000);
                } catch (\Throwable $e) {
                    \Log::error('[Greenhouse] - Error storing file ' . $fileUrl . ': ' . $e->getMessage());
                }
            });

            $fileUrlWithoutQueryParams = explode('?', $fileUrl)[0];

            return [$fileUrlWithoutQueryParams => $storedFile];
        });

        // Applications
        collect($candidate['applications'])->each(function ($application) use ($attachments, $newCandidate) {
            $applicationJobId = data_get($application, 'jobs.0.id');
            if (!$applicationJobId) {
                \Log::info('[Greenhouse] - Application ' . data_get($application, 'id') . ' has no job ID');
                return;
            }

            $projectId = cache()->driver('tenant_database')->get('greenhouse_job_id_' . $applicationJobId);
            if (!$projectId) {
                \Log::info('[Greenhouse] - Application ' . data_get($application, 'id') . ' has no project ID');
                return;
            }

            // Find a project and stage
            $project = Project::find($projectId);

            $originalStage = null;
            $greenhouseCurrentStage = data_get($application, 'current_stage');
            if ($greenhouseCurrentStage) {
                $stageId = cache()->driver('tenant_database')->get('greenhouse_stage_id_' . $greenhouseCurrentStage['id']);
                if ($stageId) {
                    $originalStage = Stage::find($stageId);
                    $originalStage->addCandidate($newCandidate);
                }
            }

            $status = data_get($application, 'status');
            if ($status === 'active' && $originalStage) {
                // Candidate is still in the process
                $finalStage = $originalStage;
            } else {
                // Candidate has gone through the entire process and Greenhouse removes them from any stages.
                // We need a candidate to be in stage, so we will create them based on the "status" of the application.
                $capitalizedStatus = ucfirst($status);
                $finalStage = $project->stages()->firstOrCreate([
                    'name' => $capitalizedStatus,
                ], [
                    'sort_order' => $project->stages()->max('sort_order') + 1,
                ]);

                $finalStage->addCandidate($newCandidate);
            }

            // Link this application's attachments to the correct project
            collect(data_get($application, 'attachments'))->each(function ($attachment) use ($attachments, $project) {
                $fileUrl = data_get($attachment, 'url');
                if (!$fileUrl) {
                    return;
                }

                $fileUrl = explode('?', $fileUrl)[0];

                $file = $attachments->get($fileUrl);
                if (!$file) {
                    return;
                }

                $file->project_id = $project->id;
                $file->save();
            });

            // Set correct source
            $source = data_get($application, 'source');
            $source_public_name = data_get($source, 'public_name');

            $applicationDate = data_get($application, 'applied_at');

            Activity::unguard();
            $newActivity = Activity::query()->create([
                'created_at' => $applicationDate,
                'activity_type_id' => $this->sourceOverrideActivityType->id,
                'source' => $source_public_name,
                'stage_id' => $originalStage?->id ?? $finalStage->id,
                'project_id' => $project->id,
                'candidate_id' => $newCandidate->id,
                'user_id' => $project->project_manager_id,
            ]);
            Activity::reguard();

            // Track application and source and credits in a comment
            $commentContent = '<p>Applied to ' . $project->position_name . ' via ' . $source_public_name . '</p>';

            $creditedTo = data_get($application, 'credited_to');
            if ($creditedTo) {
                $commentContent .= '<p>Credited to ' . data_get($creditedTo, 'name') . '</p>';
            }

            $applicationAnswers = data_get($application, 'answers');

            if ($applicationAnswers) {
                $commentContent .= '<hr><p><strong>Application answers</strong></p>';
                collect($applicationAnswers)->each(function ($answer) use (&$commentContent) {
                    $commentContent .= '<p class="mb-1"><strong>' . data_get($answer, 'question') . '</strong></p>';
                    $commentContent .= '<p>' . data_get($answer, 'answer', '-') . '</p>';
                });
            }

            $newCandidate->comments()->create([
                'created_at' => data_get($application, 'applied_at'),
                'content' => $commentContent,
                'project_id' => $project->id,
            ]);

            // Track rejection
            $rejectedAt = data_get($application, 'rejected_at');
            if ($rejectedAt) {
                $greenhouseRejectionReason = data_get($application, 'rejection_reason');
                $greenhouseRejectionType = data_get($greenhouseRejectionReason, 'type.id');
                $greenhouseRejectionReasonName = data_get($greenhouseRejectionReason, 'name');

                $isCandidateReason = $greenhouseRejectionType === self::REJECTION_REASON_TYPE_CANDIDATE;
                $dropoutReason = DropoutReason::firstOrCreate([
                    'name' => '[Greenhouse] ' . $greenhouseRejectionReasonName,
                ], [
                    'candidate_reason' => $isCandidateReason ? DropoutReason::REASON_CANDIDATE : DropoutReason::REASON_COMPANY,
                ]);

                DB::connection('tenant')->table('applications')
                    ->where('candidate_id', $newCandidate->id)
                    ->where('stage_id', $finalStage->id)
                    ->update(['dropout_reason_id' => $dropoutReason->id]);
            }

            if (in_array($source_public_name, self::GDPR_SOURCE_OK)) {
                $newCandidate->consents()->save(new Consent([
                    'consent_type' => Consent::TYPE_UNTIL_DATE,
                    // We will set the consent to be active for 2 years from the application date
                    'active_until' => Carbon::parse($applicationDate)->addMonths(24),
                    'source' => "Imported from Greenhouse, based on application to " . $project->position_name . " on " . $applicationDate . " from " . $source_public_name,
                ]));
            } else {
                \Log::info('[Greenhouse] - Application ' . data_get($application, 'id') . ' skipped GDPR consent for application from ' . $source_public_name);
            }

            cache()->driver('tenant_database')->set('greenhouse_application_id_' . data_get($application, 'id'), $newActivity->id, self::CACHE_DURATION);

            \Log::info('[Greenhouse] - Application ' . data_get($application, 'id') . ' imported successfully');
        });

        // Candidate activity feed
        $activities = $this->requestAllPages('getActivityFeedForCandidate', ['id' => $candidateId]);

        // We will create a comment for each note in the activity feed
        collect(data_get($activities, 'notes'))->each(function ($note) use ($newCandidate) {
            $noteUserId = data_get($note, 'user.id');
            $commentUser = $noteUserId ? $this->findOrCreateUser($noteUserId) : $this->getFallbackUser();
            $originalUserName = $noteUserId ? $this->getGreenhouseUserName($noteUserId) : null;

            if ($originalUserName) {
                $commentContent = '<p>Note from <strong>' . $originalUserName . ':</strong></p>';
            } else {
                $commentContent = '';
            }
            $commentContent .= data_get($note, 'body');
            $newCandidate->comments()->create([
                'content' => $commentContent,
                'created_at' => data_get($note, 'created_at'),
                'user_id' => $commentUser->id,
                'is_public' => data_get($note, 'visibility') === 'public',
            ]);
        });

        // We will create a message for each email in the activity feed
        collect(data_get($activities, 'emails'))->each(function ($email) use ($candidateEmails, $newCandidate) {
            $messageFrom = data_get($email, 'from');

            if ($messageFrom && $candidateEmails->contains('value', $messageFrom)) {
                $messageUser = null;
                $originalUserName = null;
            } else {
                $messageUserId = data_get($email, 'user.id');
                $messageUser = $messageUserId ? $this->findOrCreateUser($messageUserId) : $this->getFallbackUser();
                $originalUserName = $messageUserId ? $this->getGreenhouseUserName($messageUserId) : null;
            }

            $messageBody = '';

            if ($originalUserName) {
                $messageBody .= '<p>Message from <strong>' . $originalUserName . ':</strong></p>';
            }

            $messageBody .= nl2br(data_get($email, 'body') ?? '');

            $newCandidate->messages()->create([
                'subject' => data_get($email, 'subject'),
                'body' => $messageBody,
                'created_at' => data_get($email, 'created_at'),
                'user_id' => $messageUser?->id,
                'to' => data_get($email, 'to'),
            ]);
        });

        // We will create a comment for each activity in the activity feed,
        // as their activity model does not match our activity model
        collect(data_get($activities, 'activities'))->each(function ($activity) use ($newCandidate) {
            $activitySubject = data_get($activity, 'subject');
            $activityBody = data_get($activity, 'body');
            $activityUserId = data_get($activity, 'user.id');
            $activityUser = $activityUserId ? $this->findOrCreateUser($activityUserId) : $this->getFallbackUser();
            $activityOriginalUserName = $activityUserId ? $this->getGreenhouseUserName($activityUserId) : null;

            $commentContent = '';
            if ($activityOriginalUserName) {
                $commentContent .= '<p>Activity by <strong>' . $activityOriginalUserName . ':</strong></p>';
            }
            if ($activitySubject) {
                $commentContent .= '<p><strong>' . $activitySubject . '</strong></p>';
            }
            if ($activityBody) {
                $commentContent .= '<p>' . $activityBody . '</p>';
            }
            $newCandidate->comments()->create([
                'content' => $commentContent,
                'created_at' => data_get($activity, 'created_at'),
                'user_id' => $activityUser->id,
            ]);
        });

        cache()->driver('tenant_database')->set('greenhouse_candidate_id_' . $candidateId, $newCandidate->id, self::CACHE_DURATION);
        \Log::info('[Greenhouse] - Imported candidate ' . $candidateId . ' (' . $newCandidate->name . ')');
    }

    public function importScorecards()
    {
        $scorecards = $this->requestAllPages('getScorecards', ['per_page' => 500]);
        $failedScorecardIds = [];
//        $scorecards = json_decode(file_get_contents(base_path('greenhouse_getScorecards_per_page=500_2024-09-19T16_27_44+00_00.json')), true);
        collect($scorecards)->each(function ($scorecard) use (&$failedScorecardIds) {
            try {
                $this->importScorecard($scorecard);
            } catch (\Exception $e) {
                $failedScorecardIds[] = data_get($scorecard, 'id');
                \Log::error('[Greenhouse] - Error importing scorecard ' . data_get($scorecard, 'id') . ': ' . $e->getMessage());
            }
        });

        \Log::info('[Greenhouse] - Failed to import ' . count($failedScorecardIds) . ' scorecards: ' . implode(', ', $failedScorecardIds));
    }

    public function importScorecard(array $scorecard)
    {
        $scorecardId = data_get($scorecard, 'id');
        if (cache()->driver('tenant_database')->get('greenhouse_scorecard_id_' . $scorecardId)) {
            \Log::info('[Greenhouse] - Scorecard ' . $scorecardId . ' already exists in our database');
            return;
        }

        $activityId = cache()->driver('tenant_database')->get('greenhouse_application_id_' . data_get($scorecard, 'application_id'));
        $candidateId = cache()->driver('tenant_database')->get('greenhouse_candidate_id_' . data_get($scorecard, 'candidate_id'));

        if (!$activityId || !$candidateId) {
            \Log::info('[Greenhouse] - Scorecard ' . $scorecardId . ' skipped, missing application or candidate');
            return;
        }

        $activity = Activity::find($activityId);
        $candidate = Candidate::find($candidateId);
        $project = $activity->project;

        $submittedById = data_get($scorecard, 'submitted_by.id');
        $submittedBy = $submittedById ? $this->findOrCreateUser($submittedById) : $this->getFallbackUser();

        $ratingTypes = [
            'definitely_not' => 'Definitely not',
            'no' => 'No',
            'mixed' => 'Mixed',
            'yes' => 'Yes',
            'strong_yes' => 'Strong yes',
            'no_decision' => '-',
        ];

        // Map all fields from the example scorecard to a comment
        $dateFormat = 'd.m.Y H:i:s';

        $commentContent = '<h2>' . data_get($scorecard, 'interview') . '</h2>';
        $commentContent .= '<table class="table-sm"><tbody>';
        $commentContent .= '<tr><td><strong>Interviewed by:</strong></td><td>' . data_get($scorecard, 'interviewer.name') . '</td></tr>';
        $commentContent .= '<tr><td><strong>Interviewed at:</strong></td><td>' . Carbon::parse(data_get($scorecard, 'interviewed_at'))->format($dateFormat) . '</td></tr>';
        $commentContent .= '<tr><td><strong>Submitted by:</strong></td><td>' . data_get($scorecard, 'submitted_by.name') . '</td></tr>';
        $commentContent .= '<tr><td><strong>Submitted at:</strong></td><td>' . Carbon::parse(data_get($scorecard, 'submitted_at'))->format($dateFormat) . '</td></tr>';
        $commentContent .= '</tbody></table>';

        $commentContent .= '<hr><h3>Questions</h3>';
        collect(data_get($scorecard, 'questions'))->each(function ($question) use (&$commentContent) {
            $answer = data_get($question, 'answer');
            if (empty($answer)) {
                $answer = '-';
            }
            $commentContent .= '<p class="mb-1"><strong>' . data_get($question, 'question') . '</strong></p><p> ' . nl2br($answer) . '</p>';
        });

        $overallRecommendation = data_get($scorecard, 'overall_recommendation');
        if (is_null($overallRecommendation)) {
            $overallRecommendation = 'no_decision';
        }

        $commentContent .= '<hr><p><strong>Overall recommendation</strong>: ' . data_get($ratingTypes, $overallRecommendation, '-') . '</p>';
        $commentContent .= '<details><summary>Details</summary>';
        $commentContent .= '<div class="row">';
        $commentContent .= '<div class="col-6">';
        $commentContent .= '<table class="table table-sm table-responsive"><tbody>';
        collect(data_get($scorecard, 'attributes'))->each(function ($attribute) use ($ratingTypes, &$commentContent) {
            $commentContent .= '<tr><td>' . data_get($attribute, 'name') . '</td><td>' . data_get($ratingTypes, data_get($attribute, 'rating')) . '</td></tr>';
        });
        $commentContent .= '</tbody></table>';
        $commentContent .= '</div>';

        $commentContent .= '<div class="col-6">';
        $commentContent .= '<table class="table table-sm table-responsive"><tbody>';
        collect(data_get($scorecard, 'ratings'))->each(function ($rating, $ratingType) use ($ratingTypes, &$commentContent) {
            if ($ratingType === 'no_decision') {
                return;
            }
            if (empty($rating)) {
                return;
            }
            $commentContent .= '<tr><td>' . data_get($ratingTypes, $ratingType) . '</td><td>' . implode(', ', $rating) . '</td></tr>';
        });
        $commentContent .= '</tbody></table>';
        $commentContent .= '</div>';
        $commentContent .= '</div>';
        $commentContent .= '</details>';

        $newComment = Comment::create([
            'content' => $commentContent,
            'created_at' => data_get($scorecard, 'created_at'),
            'user_id' => $submittedBy->id,
            'project_id' => $project->id,
            'candidate_id' => $candidate->id,
        ]);

        cache()->driver('tenant_database')->set('greenhouse_scorecard_id_' . $scorecardId, $newComment->id, self::CACHE_DURATION);
        \Log::info('[Greenhouse] - Imported scorecard ' . $scorecardId . ' for candidate ' . $candidate->name);
    }
}
