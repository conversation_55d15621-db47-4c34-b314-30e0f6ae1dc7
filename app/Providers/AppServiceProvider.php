<?php

namespace App\Providers;

use App\Classes\Billing\AccessService;
use App\Classes\CVParser\CVParserDoc;
use App\Classes\CVParser\CVParserEml;
use App\Classes\CVParser\CVParserMsgPy;
use App\Classes\CVParser\CVParserPDF;
use App\Classes\CVParser\CVParserUnsupported;
use App\Classes\CVParser\CVParserWord;
use App\Classes\FeatureControls\FeatureControls;
use App\Classes\TranslationExporter;
use App\DataExchange\CvEe\Api\CvEEExportClientInterface;
use App\DataExchange\CvLibrary\CvLibraryClientInterface;
use App\DataExchange\TotaljobsXml\TotaljobsClientInterface;
use App\Forms\Relationships\Factory;
use App\Forms\Strategies\StrategyBuilder;
use App\Helpers;
use App\Models\FileType;
use App\Models\Project;
use App\Models\Setting;
use App\Models\User;
use App\Models\Website;
use App\Services\AI\OpenAiRequestRunner;
use App\Services\LocationSearch\LocationSearch;
use App\Services\LocationSearch\MaaametLocationService;
use App\Services\LocationSearch\MapboxLocationService;
use App\Services\LocationSearch\PositionstackLocationService;
use App\Services\Teams\TeamGraphResolver;
use App\Services\TwoFactorAuth\TwilioTwoFactor;
use App\Services\TwoFactorAuth\TwoFactorAuth;
use Closure;
use FFMpeg\FFMpeg;
use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use Hyn\Tenancy\Environment;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Http\Request;
use Illuminate\Log\LogManager;
use Illuminate\Pagination\Paginator;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use JeroenNoten\LaravelAdminLte\Events\BuildingMenu;
use Segment\Segment;
use Stripe\Stripe;

class AppServiceProvider extends ServiceProvider
{
    public $singletons = [
        PositionstackLocationService::class => PositionstackLocationService::class,
        MapboxLocationService::class => MapboxLocationService::class,
        MaaametLocationService::class => MaaametLocationService::class,
        LocationSearch::class => LocationSearch::class,
        TwoFactorAuth::class => TwilioTwoFactor::class,
        OpenAIRequestRunner::class => OpenAIRequestRunner::class,
    ];

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->environment('local') && config('app.telescope')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            //            $this->app->register(TelescopeServiceProvider::class);
        }
        $this->app->singleton(CvEEExportClientInterface::class, function ($app) {
            return new Client([
                'verify' => false,
                'timeout' => 15,
            ]);
        });

        $this->app->bind(CvLibraryClientInterface::class, function () {
            return new Client;
        });

        $this->app->bind(TotaljobsClientInterface::class, function () {
            return new Client;
        });

        $this->app->bind(ClientInterface::class, function ($application, array $config = []) {
            return new Client($config);
        });

        if ($this->app->runningInConsole()) {
            // as workers are persistent processes, we cannot
            // use a singleton
            $this->app->bind(TeamGraphResolver::class, function () {
                return new TeamGraphResolver;
            });
        } else {
            // when it's running in a request, we can cache
            // the teams graph info in the resolver instance
            $this->app->singleton(TeamGraphResolver::class, function () {
                return new TeamGraphResolver;
            });
        }

        $this->app->bind(CVParserPDF::class, CVParserPDF::class);
        $this->app->bind(CVParserWord::class, CVParserWord::class);
        $this->app->bind(CVParserDoc::class, CVParserDoc::class);
        $this->app->bind(CVParserMsgPy::class, CVParserMsgPy::class);
        $this->app->bind(CVParserEml::class, CVParserEml::class);
        $this->app->bind(CVParserUnsupported::class, CVParserUnsupported::class);
        $this->app->singleton(\ArieTimmerman\Laravel\SCIMServer\SCIMConfig::class, \App\Classes\SCIM\Config::class);

        $this->app->singleton('translatable-string-exporter-exporter', function ($app) {
            return $app->make(TranslationExporter::class);
        });

        $this->app->bind(\Laraform\Database\Relationships\Factory::class, Factory::class);
        $this->app->bind(\Laraform\Database\StrategyBuilder::class, StrategyBuilder::class);

        $this->app->bind(\Hyn\Tenancy\Database\Connection::class, \App\Classes\Tenancy\Connection::class);
        $this->app->bind(\Hyn\Tenancy\Contracts\Repositories\HostnameRepository::class, \App\Classes\Tenancy\HostnameRepository::class);

        $this->app->singleton(FFMpeg::class, function () {
            return FFMpeg::create();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(Dispatcher $events)
    {
        $this->app->get('auth')->provider('eloquent-ci', function ($app, $config) {
            return new CaseInsensitiveUserProvider($app['hash'], $config['model']);
        });
        if ($this->app->environment('production')) {
            Segment::init(config('services.segment.key'));
        } else {
            Segment::init('test', [
                'consumer' => 'file',
                'filename' => base_path('storage/logs/segment.log'),
            ]);
        }
        Stripe::setApiKey(config('services.stripe.secret'));
        $website = app(\Hyn\Tenancy\Environment::class)->tenant();
        Paginator::useBootstrap();
        $events->listen(BuildingMenu::class, function (BuildingMenu $event) {
            // TODO: kill me
            $user = auth()->user();
            if ($user) {
                $userRole = $user->role ?? User::ROLE_LIMITED;
                $controls = FeatureControls::get($userRole);
                $event->menu->add(
                    [
                        'text' => __('Projects'),
                        'route' => 'projects.index',
                        'topnav' => true,
                        'icon' => false,
                        'key' => 'projects',
                        'inertia' => true,
                    ],
                    [
                        'text' => __('Job ads'),
                        'route' => 'landings.index',
                        'topnav' => true,
                        'icon' => false,
                        'extra_classes' => $controls->landings->view ?: 'disabled pointer-events-none',
                        'link_classes' => $controls->landings->view ?: 'disabled',
                    ],
                    [
                        'text' => __('Talent Pool'),
                        'route' => 'candidates.index',
                        'topnav' => true,
                        'icon' => false,
                        'key' => 'candidates',
                        'extra_classes' => $controls->candidates->view ?: 'disabled pointer-events-none',
                        'link_classes' => $controls->landings->view ?: 'disabled',
                    ]);

                if ($userRole === User::ROLE_LIMITED && $controls->requisitions->view) {
                    $event->menu->addAfter(
                        'projects',
                        [
                            'text' => __('Requisitions'),
                            'route' => 'requisitions.index',
                            'topnav' => true,
                            'icon' => false,
                            'extra_classes' => $controls->requisitions->view ?: 'disabled pointer-events-none',
                            'link_classes' => $controls->requisitions->view ?: 'disabled',
                        ]
                    );
                }

                if (Setting::isAgency()) {
                    $event->menu->addAfter(
                        'candidates',
                        [
                            'text' => __('Clients'),
                            'route' => 'clients.index',
                            'topnav' => true,
                            'icon' => false,
                            'key' => 'clients',
                            'extra_classes' => $controls->clients->view ?: 'disabled pointer-events-none',
                            'link_classes' => $controls->landings->view ?: 'disabled',
                        ]
                    );
                }

                $event->menu->addAfter((Setting::isAgency() ? 'clients' : 'candidates'), [
                    'text' => __('Calendar'),
                    'route' => 'interviews.index',
                    'topnav' => true,
                    'icon' => false,
                    'extra_classes' => $controls->interviews->view ?: 'disabled pointer-events-none',
                    'link_classes' => $controls->interviews->view ?: 'disabled',
                ]);

                $latestId = session('latest_project_id');
                if ($latestId
                    && ($project = Project::find($latestId))
                    && url()->current() !== route('projects.show', $project)
                ) {
                    $event->menu->add([
                        'text' => Str::limit($project->position_name, 20),
                        'url' => route('projects.show', $project),
                        'icon' => false,
                        'topnav' => true,
                        'inertia' => true,
                    ]);
                }

                /** @var Environment $tenancy */
                $tenancy = app(Environment::class);

                if (in_array($tenancy->hostname()->fqdn, config('app.admin_fqdns'))) {
                    $event->menu->add([
                        'text' => 'Admin',
                        'url' => '/admin',
                        'topnav' => true,
                        'icon' => false,
                    ]);
                }
            }
        });

        try {
            $hasConn = DB::connection('tenant');
        } catch (\Exception $e) {
            $hasConn = false;
        }

        if ($hasConn) {
            $settingsFn = function ($view) {
                /** @var User $user */
                $user = auth()->user();
                $settings = Arr::only(Setting::getAll($user?->team), [
                    Setting::KEY_CC_TAGS,
                    Setting::KEY_CC_LAST_EDUCATION,
                    Setting::KEY_CC_LAST_EMPLOYMENT,
                    Setting::KEY_CC_INDICATOR_MESSAGES,
                    Setting::KEY_CC_INDICATOR_COMMENTS,
                    Setting::KEY_CC_INDICATOR_INVITES,
                    Setting::KEY_CC_INDICATOR_VIDEO_INVITES,
                    Setting::KEY_CC_INDICATOR_REFERENCES,
                    Setting::KEY_CC_PHONE,
                    Setting::KEY_CC_EMAIL,
                    Setting::KEY_CC_OTHER_ACTIVE_CANDIDACIES,
                    Setting::KEY_CC_SOURCE,
                    Setting::KEY_CC_LOCATION,
                    Setting::KEY_CC_TASKS_FROM_ALL_PROJECTS,
                    Setting::KEY_CC_SHOW_INITIALS_IN_BLIND_MODE,
                    Setting::KEY_ORGANIZATION_TYPE,
                    Setting::KEY_ORGANIZATION_ADDRESS,
                    Setting::KEY_TIMEZONE,
                    Setting::KEY_CONSENT_AUTOMATION_ENABLED,
                    Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS,
                    Setting::KEY_SURVEY_QUESTION,
                    Setting::KEY_ENABLE_PERMANENT_DELETE,
                    Setting::KEY_ALLOW_REGULAR_USERS_CREATE_TAGS,
                    Setting::KEY_ASK_FOR_DROPOUT_REASON,
                    Setting::KEY_USE_CUSTOM_STAGE_CATEGORIES,
                    Setting::KEY_HIDE_FILES_FROM_LIMITED_USERS,
                    Setting::KEY_DEFAULT_STAGES,
                    Setting::KEY_PRIVACY_POLICY_URL,
                    Setting::KEY_ALLOW_WITHOUT_EMAIL,
                    Setting::KEY_SHOW_ALL_PROJECT_LOGS_TO_LIMITED,
                    Setting::KEY_REQUIRE_PROJECT_FAILURE_REASONS,
                    Setting::KEY_MARK_UNDERAGE_CANDIDATES,
                    Setting::KEY_UNDERAGE_AGE_UNDER,
                    Setting::KEY_PROJECTS_PRIVATE_BY_DEFAULT,
                ]);
                $website = Helpers::getCurrentWebsite();
                $settings['sms_enabled'] = $website->isSmsEnabled();
                $settings['enable_internal_landings'] = (bool) Setting::get(Setting::KEY_INTERNAL_NETWORK_ADDR_INFO);
                $settings['user'] = $user;
                if ($user) {
                    $settings['controls'] = FeatureControls::get($user->role);
                    $settings[Setting::KEY_CANDIDATE_CUSTOM_FIELDS] = collect(Setting::get(Setting::KEY_CANDIDATE_CUSTOM_FIELDS))
                        ->map(function ($field) {
                            return [
                                'label' => $field['label'],
                                'key' => Str::slug($field['label'], '_'),
                                'type' => $field['type'],
                                'visibility' => $field['visibility'] ?? null,
                                'items' => collect($field['items'] ?? [])->map(fn ($i) => [
                                    'text' => $i,
                                    'value' => Str::slug($i, '_'),
                                ])->toArray(),
                            ];
                        });
                    $settings['users'] = User::active()->orderBy('name')->get(['id', 'name', 'role'])->map(function (User $user) {
                        return [
                            'id' => $user->id,
                            'name' => $user->name,
                            'role' => $user->role,
                        ];
                    });
                    \JavaScript::put('settingsMenu', \App\Classes\SettingsMenu::items());
                }

                $settings['features'] = $website->features;
                if ($website->stripe_customer_id) {
                    $access = new AccessService;
                    $settings['features'][Website::FEATURE_KEY_VIDEO_INTERVIEWS] = $access->hasVideoInterviewsAccess();
                }

                $settings['uf_token'] = app()->environment('production') ? 'ct_3milapyrr5dvzcbectxggz7pze' : 'ct_pohr3gs5vnd4bgwaqlulzj57ru';
                $settings['stripe_key'] = config('services.stripe.key');
                [$instance, $_] = explode('.', Helpers::getCurrentWebsite()->uuid);
                $settings['instance_name'] = $instance;
                $settings['instance_uuid'] = Helpers::getCurrentWebsite()->uuid;
                $settings['pusher_key'] = config('broadcasting.connections.pusher.key');
                $settings['pusher_host'] = config('broadcasting.connections.pusher.options.host');
                $settings['environment'] = $this->app->environment();
                $settings['is_superadmin'] = in_array(request()->getHost(), config('app.admin_fqdns'));
                $settings['is_support_session'] = session('is_support_session', false);
                \JavaScript::put('settings', $settings);
                \JavaScript::put('stageCategories', Helpers::getStageCategories());

                $fileTypes = FileType::query()->get()->keyBy('id');
                \JavaScript::put('fileTypes', $fileTypes);

                $view->with('settings', $settings);
            };
            \View::composer('app_inertia', $settingsFn);
            \View::composer('adminlte::page', $settingsFn);
            \View::composer('public.layout', $settingsFn);
            \View::composer('layouts.settings', $settingsFn);
            // settings should also be available for users in landing edit
            \View::composer('landing_v2.index', $settingsFn);
        }

        Queue::before(function (JobProcessing $event) {
            Setting::$teamSettingsContext = null;
            Setting::$cache = [];
            \Sentry\configureScope(function (\Sentry\State\Scope $scope): void {
                $website = Helpers::getCurrentWebsiteIfSet();
                if (!$website) {
                    $scope->setTag('instance.name', null);

                    return;
                }
                $scope->setTag('instance.name', $website->display_name);
            });

            /** @var LogManager $logManager */
            $logManager = app('log');

            // File-per-tenant logging is used in production. As the log file directory
            // is set in the logger singleton, it needs to be reset when starting
            // a new job.
            $logManager->forgetChannel('tenant_persistent');
            $logManager->forgetChannel('stack');
        });

        static::defineMacros();
    }

    private static function defineMacros(): void
    {
        Collection::macro('maxBy', function (Closure|string|int $callback): mixed {
            /** @var Collection $this */
            $callback = $this->valueRetriever($callback);

            return $this->reduce(function ($result, $item) use ($callback) {
                if ($result === null) {
                    return $item;
                }

                return $callback($item) > $callback($result) ? $item : $result;
            });
        });

        Request::macro('validateKey', function (string $key, array|string $rules): mixed {
            /** @var Request $this */
            return data_get($this->validate([$key => $rules]), $key);
        });

        Request::macro('filterKey', function (string $key, array|string $rules, mixed $default = null): mixed {
            /** @var Request $this */
            try {
                return $this->validateKey($key, $rules);
            } catch (ValidationException) {
                return $default;
            }
        });
    }
}
