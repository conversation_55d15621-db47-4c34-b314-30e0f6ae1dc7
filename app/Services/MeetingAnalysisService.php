<?php

namespace App\Services;

use App\Helpers;
use App\Models\MeetingAnalysis;
use App\Models\Setting;
use App\Models\Video;
use App\Services\AI\HasOpenAIClient;
use Carbon\CarbonInterval;
use Closure;
use Exception;
use Generator;
use Illuminate\Support\Collection;
use Illuminate\Support\Enumerable;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\LazyCollection;
use Illuminate\Support\Str;
use Illuminate\Support\Stringable;
use OpenAI\Contracts\ClientContract;
use OpenAI\Exceptions\ErrorException;
use OpenAI\Factory;
use OpenAI\Responses\Audio\TranscriptionResponse;
use OpenAI\Responses\Audio\TranscriptionResponseSegment;
use Spatie\TemporaryDirectory\TemporaryDirectory;
use Symfony\Component\Intl\Languages;

/** @phpstan-type TranscriptionSegmentArray array{text: string, time: float} */
class MeetingAnalysisService
{
    use HasOpenAIClient;

    private const string ORIGINAL_AUDIO_FILE_NAME = 'original_audio.mp3';
    private const string LANG_DETECTION_AUDIO_FILE_NAME = 'lang_detection_audio.mp3';
    private const string TRIMMED_AUDIO_FILE_NAME = 'trimmed_audio.mp3';
    private const int MAX_AUDIO_FILE_SECONDS = 30 * 60;

    public function __construct(private readonly MicrosoftApiService $service) {}

    /** @throws Exception */
    public function analyze(MeetingAnalysis $meetingAnalysis, Video|string $video): void
    {
        Log::withContext([
            'meeting_analysis_id' => $meetingAnalysis->id,
            'video_type' => gettype($video),
            'video' => $video,
        ]);

        Log::debug('Starting downloaded video analysis process...');

        $promptHints = $this->computePromptHints($meetingAnalysis);
        Log::debug('Computed prompt hints.', $promptHints);

        $directory = TemporaryDirectory::make();
        try {
            $originalAudioFilePath = $this->convertVideoToAudio($directory, $video);
            $originalAudioLength = $this->computeMediaLengthInSeconds($originalAudioFilePath);
            $language = $this->resolveLanguage($directory, $meetingAnalysis, $originalAudioFilePath, $originalAudioLength);

            if (filled($language)) {
                Log::debug('Setting locale.', ['locale' => $language]);
                App::setLocale($language);
            } else {
                Helpers::toLogOrSentryMessage('Failed to detect language based on cropped audio transcript.');
            }

            $trimmedAudioPath = $this->trimLeadingSilence($directory);
            $trimmedAudioLength = $this->computeMediaLengthInSeconds($trimmedAudioPath);
            $removedAudioLength = $originalAudioLength->sub($trimmedAudioLength);
            $transcriptSegments = $this->transcribeIntoSegments($directory, $trimmedAudioPath, $language, $trimmedAudioLength);
            $cleanedTranscriptSegments = $this->cleanUpTranscriptSegments($transcriptSegments, $promptHints, $removedAudioLength);
            $summarySentences = $this->retryAfterServerException(fn () => $this->summarizeTranscript($cleanedTranscriptSegments, $promptHints));

            $meetingAnalysis->update([
                'transcript_by_openai' => $cleanedTranscriptSegments,
                'summary_by_openai' => $summarySentences,
                'recording_length_seconds' => $originalAudioLength->totalSeconds,
                'error_at' => null,
                'last_error' => null,
                'in_progress_since' => null,
            ]);
            Log::debug('Finished downloaded video analysis process.');
        } finally {
            $directory->delete();
        }
    }

    /** @throws Exception */
    private function resolveLanguage(TemporaryDirectory $directory, MeetingAnalysis $meetingAnalysis, string $audioPath, CarbonInterval $audioLength): string
    {
        if ($meetingAnalysis->spoken_language_override || $meetingAnalysis->timeSlot?->eventSet->spoken_language) {
            return $meetingAnalysis->spoken_language_override
                ?? $meetingAnalysis->timeSlot->eventSet->spoken_language;
        }

        $languageDetectionAudioFilePath = $audioLength->greaterThan(CarbonInterval::minutes(5))
            ? $this->createMediaExcerpt($directory, $audioPath, $audioLength, CarbonInterval::minutes(5))
            : $audioPath;

        $languageDetectionTranscriptionResponse = $this->transcribeForLanguageDetection($languageDetectionAudioFilePath);

        return $this->detectLanguage($languageDetectionTranscriptionResponse);
    }

    /**
     * @throws Exception
     */
    private function computeMediaLengthInSeconds(string $mediaPath): CarbonInterval
    {
        $command = "ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 $mediaPath";
        exec($command, $output, $resultCode);
        Log::debug('Executed media length command.', ['output' => $output]);

        if ($resultCode !== 0 || empty($output) || !is_numeric($output[0])) {
            $exception = new Exception('Failed to compute media length. Usually happens due to an empty file.');
            Helpers::toLogOrSentry($exception, [
                'command' => $command,
                'error_code' => $resultCode,
                'output' => $output,
            ]);
            throw $exception;
        }

        return CarbonInterval::seconds(intval($output[0]));
    }

    /**
     * @throws Exception
     */
    private function createMediaExcerpt(TemporaryDirectory $directory, string $mediaFilePath, CarbonInterval $fullLength, CarbonInterval $cropLength): string
    {
        info('Creating cropped audio excerpt...');
        $croppedAudioFilePath = Str::of($directory->path())
            ->finish(DIRECTORY_SEPARATOR)
            ->append(self::LANG_DETECTION_AUDIO_FILE_NAME)
            ->value();

        $cropLengthSeconds = $cropLength->totalSeconds;
        $cropStartSeconds = (int) $fullLength->copy()->divide(2)->sub($cropLength->copy()->divide(2))->totalSeconds;

        $command = "ffmpeg -ss $cropStartSeconds -i $mediaFilePath -t $cropLengthSeconds -codec:a libmp3lame -q:a 7 $croppedAudioFilePath";
        exec($command, $output, $resultCode);
        Log::debug('Executed media excerpt command.', ['output' => $output]);

        if ($resultCode !== 0) {
            $exception = new Exception('Failed to create cropped audio.');
            Helpers::toLogOrSentry($exception, [
                'command' => $command,
                'error_code' => $resultCode,
                'output' => $output,
            ]);
            throw $exception;
        }

        return $croppedAudioFilePath;
    }

    /**
     * @throws Exception
     */
    private function convertVideoToAudio(TemporaryDirectory $directory, Video|string $video): string
    {
        $audioFilePath = Str::of($directory->path())
            ->finish(DIRECTORY_SEPARATOR)
            ->append(self::ORIGINAL_AUDIO_FILE_NAME)
            ->value();

        $videoFilePath = $video instanceof Video ? $video->file->getLocalPath() : $video;
        if (!$videoFilePath) {
            $exception = new Exception('Can\'t analyze meeting no video file is specified.');
            Helpers::toLogOrSentry($exception);
            throw $exception;
        }

        $command = "ffmpeg -i $videoFilePath -vn -codec:a libmp3lame -q:a 7 $audioFilePath";
        exec($command, $output, $resultCode);
        Log::debug('Executed video-to-audio conversion command.', ['output' => $output]);

        if ($resultCode !== 0) {
            $exception = new Exception('Failed to extract audio from video file.');
            Helpers::toLogOrSentry($exception, [
                'command' => $command,
                'error_code' => $resultCode,
                'output' => $output,
            ]);
            throw $exception;
        }

        $audioSize = number_format(filesize($audioFilePath) / pow(1024, 2), 2);
        Log::debug('Converted video to audio.', ['audio_path' => $audioFilePath, 'size_in_mb' => $audioSize]);

        return $audioFilePath;
    }

    private function createSummarizationPrompt(array $hints): string
    {
        /** @var Collection<string> $companyNames */
        /** @var Collection<string> $candidateNames */
        /** @var Collection<string> $interviewerNames */
        [
            'company_names' => $companyNames,
            'position_name' => $positionName,
            'candidate_names' => $candidateNames,
            'interviewer_names' => $interviewerNames,
        ] = $hints;

        return (new Stringable)
            ->append(__('You are a summarization tool integrated within an applicant tracking system (ATS).'), ' ')
            ->append(__('Your primary function is to summarize a video interview using a transcript that is provided to you by the user.'), ' ')
            ->append(__('The summary should be useful for making good hiring decisions.'), " \n")
            ->append(match ($companyNames->count()) {
                0 => __(
                    'The interview is for a job position titled ":position".',
                    ['position' => $positionName]
                ),
                1 => __(
                    'The interview is for a job position titled ":position" in the company ":company".',
                    ['position' => $positionName, 'company' => $companyNames->first()]
                ),
                default => __(
                    'The interview is for a job position titled ":position" with involvement by the following companies: :companies.',
                    ['position' => $positionName, 'companies' => $companyNames->join(', ')]
                )
            })
            ->append(' ')
            ->append(match ($interviewerNames->count()) {
                0 => '',
                1 => __('The interview is conducted by a person named :interviewer.', ['interviewer' => $interviewerNames->first()]),
                default => __('The interview is conducted by the following interviewers:') . " {$interviewerNames->join(', ')}."
            })
            ->append(' ')
            ->append(match ($candidateNames->count()) {
                0 => '',
                1 => __('The person being interviewed is named :person.', ['person' => $candidateNames->first()]),
                2 => __('The interview has multiple people being interviewed and their names are as follows:') . " {$candidateNames->join(', ')}.",
            })
            ->append(" \n")
            ->append(__('You must call the provided tool function with an array of strings in which each string is a single sentence that summarizes some aspect of the interview.'), ' ')
            ->append(__('The array must contain between 3 and 10 sentences.'), ' ')
            ->append(__('No sentence may be longer than 200 characters.'), " \n")
            ->append(__('The user will send you the transcript which will be a JSON array of transcript segments, each being a JSON object with two keys: "text" and "time".'), " \n")
            ->append(__('While the speakers are not identified in the transcript, you will do your best to differentiate the speakers.'), " \n")
            ->toString();
    }

    private function createTranscriptionPrompt(array $hints): string
    {
        /** @var Collection<string> $companyNames */
        /** @var Collection<string> $candidateNames */
        /** @var Collection<string> $interviewerNames */
        [
            'company_names' => $companyNames,
            'position_name' => $positionName,
            'candidate_names' => $candidateNames,
            'interviewer_names' => $interviewerNames,
        ] = $hints;

        $allNames = collect([
            ...$candidateNames,
            ...$interviewerNames,
        ]);

        return (new Stringable)
            ->append(__('Transcribe the provided text.') . ' ')
            ->append(__('Position name') . ": $positionName. ")
            ->append(match ($companyNames->count()) {
                0 => '',
                1 => __('Company name') . ": {$companyNames->first()}. ",
                default => __('Company names') . ": {$companyNames->join(', ')}. "
            })
            ->append(match ($allNames->count()) {
                0 => '',
                default => __('Names of people') . ": {$allNames->join(', ')}."
            })
            ->toString();
    }

    /**
     * @return array{
     *     company_names: Collection<string>,
     *     position_name: string,
     *     candidate_names: Collection<string>,
     *     interviewer_names: Collection<string>
     * }
     */
    private function computePromptHints(MeetingAnalysis $meetingAnalysis): array
    {
        if (!$meetingAnalysis->timeSlot) {
            return [
                'company_names' => collect([]),
                'position_name' => '',
                'candidate_names' => collect([$meetingAnalysis->candidate->name]),
                'interviewer_names' => collect([]),
            ];
        }

        $project = $meetingAnalysis->timeSlot->eventSet->project;

        $candidateNames = $meetingAnalysis->timeSlot->invites
            ->pluck('candidate.name')
            ->values();

        $interviewerNames = $meetingAnalysis->timeSlot->eventSet->users
            ->pluck('name')
            ->concat($project->users->pluck('name')->values())
            ->push($project->author?->name)
            ->push($meetingAnalysis->timeSlot->eventSet->author?->name)
            ->unique()
            ->values();

        return [
            'company_names' => collect([$project->client?->name, Setting::get(Setting::KEY_ORGANIZATION_NAME)])->filter(),
            'position_name' => $project->position_name,
            'candidate_names' => $candidateNames,
            'interviewer_names' => $interviewerNames,
        ];
    }

    private function createLanguageDetectionPrompt(): string
    {
        return <<<'PROMPT'
            You are a summarization tool integrated within an applicant tracking system (ATS).
            Your only function is to detect the language of the provided text and return the ISO 639-1 language code.
            The user will provide you the text. Your response must be a ISO 639-1 language code (string of exactly two characters).
            PROMPT;
    }

    public function transcribeForLanguageDetection(string $languageDetectionAudioFilePath, array $options = []): TranscriptionResponse
    {
        Log::debug('Requesting transcript for language detection from OpenAI...');
        $transcriptionResponse = $this->getOpenAiClient('whisper-1')
            ->audio()
            ->transcribe([
                'model' => 'whisper-1',
                'file' => fopen($languageDetectionAudioFilePath, 'r'),
                'response_format' => 'text',
                ...$options,
            ]);
        Log::debug('Received transcript for language detection from OpenAI.', $transcriptionResponse->toArray());

        return $transcriptionResponse;
    }

    public function detectLanguage(TranscriptionResponse $transcriptionResponse): ?string
    {
        Log::debug('Requesting language detection using transcript from OpenAI');
        $prompt = $this->createLanguageDetectionPrompt();
        $languageDetectionResponse = $this->getOpenAiClient()
            ->chat()
            ->create([
                'model' => $this->getDefaultModel(),
                'messages' => [
                    ['role' => 'system', 'content' => $prompt],
                    ['role' => 'user', 'content' => $transcriptionResponse->text],
                ],
            ]);
        Log::debug('Received language detection response from OpenAI', [
            'response' => $languageDetectionResponse->toArray(),
            'prompt' => $prompt,
        ]);

        $language = $languageDetectionResponse->choices[0]->message->content;
        if (!Languages::exists($language)) {
            return null;
        }

        return $language;
    }

    /** @param TranscriptionSegmentArray $segments */
    public function summarizeTranscript(array $segments, array $promptHints): array
    {
        Log::debug('Requesting summarization from OpenAI...');
        $prompt = $this->createSummarizationPrompt($promptHints);
        $summarizationResponse = $this->getOpenAiClient()
            ->chat()
            ->create([
                'model' => $this->getDefaultModel(),
                'messages' => [
                    ['role' => 'system', 'content' => $prompt],
                    ['role' => 'user', 'content' => json_encode($segments)],
                ],
                'tools' => [
                    [
                        'type' => 'function',
                        'function' => [
                            'name' => 'store_interview_summary',
                            'description' => __('Store the interview summary in a structured format. Should be called with an array in which each element is a string containing a summarization sentence.'),
                            'parameters' => [
                                'type' => 'object',
                                'properties' => [
                                    'sentences' => [
                                        'type' => 'array',
                                        'description' => __('An array in which each element is a summarization sentence.'),
                                        'items' => [
                                            'type' => 'string',
                                            'description' => __('A single sentence summarizing a specific aspect of the interview.'),
                                            'maxLength' => 200,
                                        ],
                                        'minItems' => 3,
                                        'maxItems' => 10,
                                    ],
                                ],
                                'required' => ['sentences'],
                            ],
                        ],
                    ],
                ],
                'tool_choice' => 'required',
            ]);
        Log::debug('Received summarization response from OpenAI', [
            'response' => $summarizationResponse->toArray(),
            'prompt' => $prompt,
        ]);

        $arguments = json_decode($summarizationResponse->choices[0]->message->toolCalls[0]->function->arguments, true);

        return $arguments['sentences'];
    }

    private function getOpenAiClient(string $model = 'gpt-4.1'): ClientContract
    {
        $httpClient = new \GuzzleHttp\Client(['timeout' => CarbonInterval::minutes(30)->totalSeconds]);

        return $this->getClient(fn (Factory $factory) => $factory->withHttpClient($httpClient), model: $model);
    }

    /**
     * @return TranscriptionSegmentArray[]
     *
     * @throws Exception
     */
    private function transcribeIntoSegments(TemporaryDirectory $directory, string $audioFilePath, ?string $language, CarbonInterval $audioLength): array
    {
        /** @var Collection<int, TranscriptionSegmentArray> $segments */
        [$segments] = $this->splitAudioIfNecessary($directory, $audioFilePath, $audioLength)
            ->reduceSpread(function (Collection $previousSegments, CarbonInterval $elapsedTimeBeforeCurrentPart, string $audioPartPath) use ($language): array {
                $audioPartLength = $this->computeMediaLengthInSeconds($audioPartPath);
                $transcriptionResponse = $this->retryAfterServerException(fn () => $this->getTranscription($audioPartPath, $language));
                $newSegments = collect(($transcriptionResponse->segments))
                    ->map(fn (TranscriptionResponseSegment $segment) => [
                        'text' => $segment->text,
                        'time' => $segment->start + $elapsedTimeBeforeCurrentPart->totalSeconds,
                    ])
                    ->all();

                return [
                    $previousSegments->concat($newSegments), // $previousSegments
                    $elapsedTimeBeforeCurrentPart->copy()->add($audioPartLength), // $elapsedTimeBeforeCurrentPart
                ];
            }, Collection::empty(), CarbonInterval::seconds(0));

        return $segments->all();
    }

    private function getTranscription(string $audioFilePath, ?string $language): TranscriptionResponse
    {
        Log::debug('Requesting transcript from OpenAI...');
        $transcriptionResponse = $this->getOpenAiClient('whisper-1')
            ->audio()
            ->transcribe([
                'model' => 'whisper-1',
                'file' => fopen($audioFilePath, 'r'),
                'response_format' => 'verbose_json',
                'timestamp_granularities' => ['segment'],
                ...($language ? ['language' => $language] : []),
            ]);
        Log::debug('Received transcript from OpenAI.', [
            'response' => $transcriptionResponse->toArray(),
        ]);

        return $transcriptionResponse;

    }

    /**
     * @param  TranscriptionSegmentArray[]  $originalTranscriptSegments
     * @return TranscriptionSegmentArray[]
     */
    public function cleanUpTranscriptSegments(array $originalTranscriptSegments, array $promptHints, CarbonInterval $removedAudioLength): array
    {
        Log::debug('Requesting transcript cleanup from OpenAI...');

        return collect($originalTranscriptSegments)
            ->chunk(100)
            ->flatMap(fn (Collection $chunk) => $this->retryAfterServerException(fn () => $this->cleanUpChunk($chunk->values(), $promptHints)))
            ->map(fn (array $segment) => [
                ...$segment,
                'time' => $segment['time'] + $removedAudioLength->totalSeconds,
            ])
            ->sortBy(fn (array $segment) => $segment['time'])
            ->all();
    }

    private function getCleanUpPrompt(array $hints): string
    {
        $allNames = collect([
            $hints['position_name'],
            ...$hints['company_names'],
            ...$hints['candidate_names'],
            ...$hints['interviewer_names'],
        ]);

        return (new Stringable)
            ->append(__('You are a tool meant for cleaning up transcripts integrated within an applicant tracking system (ATS).') . ' ')
            ->append(__('Your only function is to clean up the text of auto-generated transcript segments that will be provided to you by the user.') . ' ')
            ->append(__('You may not change the time (timestamp) data of the segments; you may only change the text.') . ' ')
            ->append(__('As the transcript text has been auto-generated, it might include spelling errors.') . ' ')
            ->append(__('You must try your best to fix spelling errors and improve readability without removing information or adding new content. You may replace words if they seem out of context.') . ' ')
            ->append(__('You may not change the number of segments. The number of segments in the output must be the same as in the input.') . ' ')
            ->append(__('You must ensure that the timestamps of segments do not change during processing. The timestamp of when a certain segment happened must be the same in the output as it was in the input.') . ' ')
            ->append(__('Do not infer or assume any information that has not been explicitly said in the transcript.') . " \n")
            ->append(__('The speech that was originally transcribed might have included the following proper names:') . " {$allNames->join(', ')}. \n")
            ->append(__('The user will send you the transcript which will be a JSON array of transcript segments') . ' ')
            ->append(__('The segments returned must be in the exact same order as input.') . ' ')
            ->toString();
    }

    /** @return TranscriptionSegmentArray[] */
    public function cleanUpChunk(Collection $segments, array $promptHints): array
    {
        $prompt = $this->getCleanUpPrompt($promptHints);
        $segmentsJson = json_encode($segments->all());
        $schema = $this->getCleanUpSchema();

        Log::debug('Cleaning up transcript chunk', [
            'segments_hash' => md5($segments->pluck('text')->implode(',')),
            'prompt' => $prompt,
            'segmentsJson' => $segmentsJson,
            'schema' => $schema,
        ]);

        $response = $this->getOpenAiClient()
            ->chat()
            ->create([
                'model' => $this->getDefaultModel(),
                'messages' => [
                    ['role' => 'system', 'content' => $prompt],
                    ['role' => 'user', 'content' => $segmentsJson],
                ],
                'response_format' => ['type' => 'json_schema', 'json_schema' => $schema],
            ]);

        Log::debug('Got response from openai', ['response' => $response]);

        $responseContent = json_decode($response->choices[0]->message->content, true);

        return $responseContent['segments'];
    }

    private function getCleanUpSchema(): array
    {
        return [
            'name' => 'transcription_segments',
            'strict' => true,
            'schema' => [
                'type' => 'object',
                'required' => ['segments'],
                'additionalProperties' => false,
                'properties' => [
                    'segments' => [
                        'type' => 'array',
                        'title' => __('Transcription segments'),
                        'description' => __('An array containing segments of transcribed speech, ordered by when the speech occurred.'),
                        'items' => [
                            'type' => 'object',
                            'title' => __('Transcription segment'),
                            'description' => __('An object representing a single transcribed segment of speech.'),
                            'required' => ['text', 'time'],
                            'additionalProperties' => false,
                            'properties' => [
                                'text' => [
                                    'type' => 'string',
                                    'title' => __('Text of transcribed speech'),
                                    'description' => __('A text string that contains the text representing a transcribed segment of speech.'),
                                ],
                                'time' => [
                                    'type' => 'number',
                                    'title' => __('Timestamp of speech occurrence'),
                                    'description' => __('A timestamp that represents when the segment of speech occurred within the original recording.'),
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    /** @throws Exception */
    private function trimLeadingSilence(TemporaryDirectory $directory): string
    {
        $trimmedAudioFilePath = Str::of($directory->path())
            ->finish(DIRECTORY_SEPARATOR)
            ->append(self::TRIMMED_AUDIO_FILE_NAME)
            ->value();

        $originalAudioFilePath = Str::of($directory->path())
            ->finish(DIRECTORY_SEPARATOR)
            ->append(self::ORIGINAL_AUDIO_FILE_NAME)
            ->value();

        // Adapted from https://superuser.com/a/1364824
        $command = "ffmpeg -i $originalAudioFilePath -af \"silenceremove=start_periods=1:start_duration=1:start_threshold=-60dB:detection=peak\" -acodec libmp3lame -q:a 7 $trimmedAudioFilePath";
        exec($command, $output, $resultCode);
        Log::debug('Executed leading silence trimming command.', ['output' => $output]);

        if ($resultCode !== 0) {
            $exception = new Exception('Failed to trim leading silence from audio file.');
            Helpers::toLogOrSentry($exception, [
                'command' => $command,
                'error_code' => $resultCode,
                'output' => $output,
            ]);
            throw $exception;
        }

        $audioSize = number_format(filesize($trimmedAudioFilePath) / pow(1024, 2), 2);
        Log::debug('Trimmed leading silence from audio file.', ['trimmed_audio_path' => $trimmedAudioFilePath, 'size_in_mb' => $audioSize]);

        return $trimmedAudioFilePath;
    }

    /**
     * @return Collection<int, string> Collection of audio file paths.
     *
     * @throws Exception
     */
    private function splitAudioIfNecessary(TemporaryDirectory $directory, string $audioFilePath, CarbonInterval $audioLength): Collection
    {
        if ($audioLength->totalSeconds <= static::MAX_AUDIO_FILE_SECONDS) {
            Log::debug('No splitting file as its length is less than the maximum allowed.', ['audio_length' => $audioLength->totalSeconds, 'max_length' => static::MAX_AUDIO_FILE_SECONDS]);

            return collect([$audioFilePath]);
        }

        $silenceMoments = $this->detectSilenceMoments($audioFilePath);

        $halfHourGenerator = function () use ($audioLength): Generator {
            $current = CarbonInterval::seconds(static::MAX_AUDIO_FILE_SECONDS);
            while ($current->totalSeconds < $audioLength->totalSeconds) {
                yield $current;
                $current = $current->copy()->addSeconds(static::MAX_AUDIO_FILE_SECONDS);
            }
        };

        /** @var LazyCollection<int, CarbonInterval> $splitMoments */
        $splitMoments = LazyCollection::make($halfHourGenerator)
            ->map(fn (CarbonInterval $maxSplitMoment) => $silenceMoments
                ->filter(fn (CarbonInterval $silenceMoment) => $silenceMoment->betweenIncluded($maxSplitMoment->copy()->subMinutes(1), $maxSplitMoment))
                ->maxBy(fn (CarbonInterval $silenceMoment) => $silenceMoment->totalSeconds)
                ?? $maxSplitMoment
            );

        Log::debug('Resolved split moments for audio splitting', ['split_moments' => $splitMoments->pluck('totalSeconds')->all()]);

        return $this->splitAudioIntoParts($directory, $audioFilePath, $splitMoments);
    }

    /**
     * @param  Enumerable<int, CarbonInterval>  $splitMoments
     * @return Collection<int, string>
     *
     * @throws Exception
     */
    private function splitAudioIntoParts(TemporaryDirectory $directory, string $audioFilePath, Enumerable $splitMoments): Collection
    {
        $splitAudioPathFormat = Str::of($directory->path())
            ->finish(DIRECTORY_SEPARATOR)
            ->append('split_audio_part_%03d.mp3')
            ->value();
        $splitSecondsAsString = $splitMoments
            ->map(fn (CarbonInterval $splitMoment) => number_format($splitMoment->totalSeconds, 2, thousands_separator: ''))
            ->join(',');

        $splitCommand = "ffmpeg -i '$audioFilePath' -f segment -segment_times '$splitSecondsAsString' -c copy '$splitAudioPathFormat'";
        exec($splitCommand, $output, $resultCode);
        Log::debug('Executed audio splitting command.', ['output' => $output]);

        if ($resultCode !== 0) {
            $exception = new Exception('Failed to split audio file to parts.');
            Helpers::toLogOrSentry($exception, [
                'command' => $splitCommand,
                'error_code' => $resultCode,
                'output' => $output,
            ]);
            throw $exception;
        }

        $fileGlob = Str::of($directory->path())
            ->finish(DIRECTORY_SEPARATOR)
            ->append('split_audio_part_*.mp3')
            ->toString();

        $filenames = collect(glob($fileGlob, GLOB_ERR));

        Log::debug('Split file into parts.', ['parts_count' => $filenames->count(), 'filenames' => $filenames->all()]);

        return $filenames;
    }

    /**
     * @return Collection<int, CarbonInterval>
     *
     * @throws Exception
     */
    private function detectSilenceMoments(string $audioFilePath): Collection
    {
        $silenceCommand = "ffmpeg -i \"$audioFilePath\" -af silencedetect=noise=-30dB:d=1 -f null - 2>&1";
        $output = [];
        exec($silenceCommand, $output, $resultCode);
        Log::debug('Executed silence detection command.', ['output' => $output]);

        if ($resultCode !== 0) {
            $exception = new Exception('Failed to detect silences in audio file.');
            Helpers::toLogOrSentry($exception, [
                'command' => $silenceCommand,
                'error_code' => $resultCode,
                'output' => $output,
            ]);
            throw $exception;
        }

        return collect($output)
            ->map(function (string $line) {
                preg_match('/silence_end: (\d+(\.\d+)?) \| silence_duration: (\d+(\.\d+)?)/', $line, $matches);

                return filter_var(data_get($matches, 1), FILTER_VALIDATE_FLOAT) !== false
                && filter_var(data_get($matches, 2), FILTER_VALIDATE_FLOAT) !== false
                    ? [floatval($matches[1]), floatval($matches[2])]
                    : null;
            })
            ->filter()
            ->mapSpread(fn (float $silenceEnd, float $silenceDuration) => CarbonInterval::seconds($silenceEnd - ($silenceDuration / 2)));
    }

    /**
     * @template T
     *
     * @param  Closure(): T  $callback
     * @return T
     *
     * @throws Exception
     *
     * @see https://community.openai.com/t/gpt-4-is-producing-server-error-out-of-nowhere-recently/644972/7
     */
    private function retryAfterServerException(Closure $callback)
    {
        return retry(
            [1_000, 5_000, 15_000, 30_000, 60_000, 120_000, 300_000], // sleep milliseconds
            $callback,
            when: fn (Exception $exception) => $exception instanceof ErrorException && $exception->getStatusCode() >= 500
        );
    }
}
