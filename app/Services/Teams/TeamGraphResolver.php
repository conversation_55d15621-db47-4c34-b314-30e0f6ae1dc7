<?php

namespace App\Services\Teams;

use App\Models\Setting;
use App\Models\Team;
use App\Models\User;
use Graphp\Graph\Edge;
use Graphp\Graph\Graph;
use Graphp\Graph\Vertex;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class TeamGraphResolver
{
    protected ?Graph $graph = null;

    protected array $cache = [];

    protected ?Collection $settings = null;

    protected TeamGraphUtils $utils;

    public function __construct()
    {
        $this->utils = new TeamGraphUtils();
    }

    public function getGraph(?callable $teamQueryCallback = null): Graph
    {
        if ($this->graph) {
            return $this->graph;
        }
        $this->graph = $this->utils->buildGraph($teamQueryCallback);
        return $this->graph;
    }

    public function getAccessibleTeamIds(User $user): array
    {
        if (!$user->team_id) {
            return [];
        }
        if (isset($this->cache[$user->id])) {
            return $this->cache[$user->id];
        }
        $teams = $this->utils->getAccessibleTeams(
            $this->getGraph(),
            $user->team_id,
        );
        $teamIds = Arr::pluck($teams, 'id');
        $this->cache[$user->id] = $teamIds;
        return $teamIds;
    }

    public function getAccessibleTeamIdsFromTeamId($teamId): array
    {
        $teams = $this->utils->getAccessibleTeams(
            $this->getGraph(),
            $teamId,
        );
        return Arr::pluck($teams, 'id');
    }

    public function getTeamIdsWithAccessToTeamId(int $teamId): array
    {
        $teams = $this->utils->getTeamsWithAccessTo(
            $this->getGraph(),
            $teamId,
        );
        return Arr::pluck($teams, 'id');
    }

    public function getSettingsFor(Team $team, array $only = []): Collection
    {
        start_measure('getSettingsForTeam');
        $resolvedSettings = [];

        $query = Setting::query()->select(['id', 'key', 'value_json', 'team_id']);

        if ($only) {
            $settings = $query->whereIn('key', $only)->orderBy('team_id')->orderBy('id')->get();
        } elseif (!$this->settings) {
            $settings = $query->orderBy('team_id')->orderBy('id')->get();
            $this->settings = $settings;
        } else {
            $settings = $this->settings;
        }

        $teamVertex = $this->utils->getVertexByTeamId(
            $this->getGraph(),
            $team->id,
        );

        $verticesToProcess = [$teamVertex];

        start_measure('looping');
        while (count($verticesToProcess)) {
            /** @var Vertex $vertex */
            $vertex = array_shift($verticesToProcess);
            foreach ($vertex->getEdgesIn() as $incomingEdge) {
                $parent = $incomingEdge->getVertexFromTo($vertex);
                $verticesToProcess[] = $parent;
            }

            $settings
                ->where('team_id', $vertex->getAttribute('team')->id)
                ->each(function (Setting $setting) use (&$resolvedSettings) {
                    if (isset($resolvedSettings[$setting->key])) {
                        return;
                    }
                    $resolvedSettings[$setting->key] = $setting;
                });
        };
        stop_measure('looping');

        $mustHaveKeys = count($only) ? $only : array_keys(Setting::SETTING_DEFAULTS);

        $nullKeySettings = $settings->whereNull('team_id')->keyBy('key');

        foreach ($mustHaveKeys as $unresolvedKey) {
            if (!isset($resolvedSettings[$unresolvedKey])) {
                $nonTeamSettingModel = $nullKeySettings->get($unresolvedKey);
                if ($nonTeamSettingModel) {
                    $resolvedSettings[$unresolvedKey] = $nonTeamSettingModel;
                }
            }
        }

        stop_measure('getSettingsForTeam');
        return collect($resolvedSettings);
    }

    public function clearCache()
    {
        $this->cache = [];
        $this->settings = null;
        $this->graph = null;
    }
}
