<?php

namespace App\Services\AI;

use App\Models\Landing;
use App\Models\LandingDeiReport;

class OpenAILandingDEIReporter
{
    use HasOpenAIClient;

    const FAIL_BLOCK_DATA = 'no_block_data';

    public function __construct(public ?Landing $landing = null) {}

    public function generateDeiReport(array $block): LandingDeiReport
    {
        $blockData = $this->prepareBlockData($block);

        if (empty($blockData)) {
            return LandingDeiReport::create([
                'landing_id' => $this->landing->id,
                'raw_model_response' => null,
                'is_failed' => 1,
                'fail_reason' => self::FAIL_BLOCK_DATA,
            ]);
        }

        $systemPrompt = <<<'PROMPT'
                You are a diversity, equity, and inclusion (DEI) language detection service for
                an applicant tracking system job ads.

                Your task is to create a summary report of user contents,
                by detecting any use of language against DEI principles and make suggestions if needed.

                Instead of a full job ad you may get partial paragraphs.

                The user will send you the contents of a job ad to check.

                You must give your responses only in the following JSON format:
                {
                    "summary_report": "<summary_report>",
                    "suggestions": [<suggestions>]
                }
                PROMPT;

        info($systemPrompt);

        $blockContent = implode('<br>', $blockData);
        $blockContent = \Soundasleep\Html2Text::convert($blockContent);

        info($blockContent);

        $response = $this->getClient()->chat()->create([
            //            'model' => 'gpt-3.5-turbo',
            'model' => $this->getDefaultModel(),
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemPrompt,
                ],
                [
                    'role' => 'user',
                    'content' => $blockContent,
                ],
            ],
        ]);

        $respArray = $response->toArray();

        info($respArray);

        $content = $response->choices[0]->message->content;
        $content = $this->sanitizeOpenAIJsonResponse($content);

        return LandingDeiReport::create([
            'landing_id' => $this->landing->id,
            'block_id' => $block['_id'],
            'report_json' => json_decode($content),
            'usage_json' => $response->usage->toArray(),
            'raw_model_response' => $content,
        ]);
    }

    protected function prepareBlockData(array $block): array
    {
        $blockData = [];
        if (isset($block['heading'])) {
            $blockData[] = $block['heading'];
        }
        if (isset($block['subheading'])) {
            $blockData[] = $block['subheading'];
        }
        if (isset($block['content'])) {
            $blockData[] = $block['content'];
        }

        $titleContentData = ($block['quotes'] ?? $block['arguments'] ?? []);
        if (!empty($titleContentData)) {
            foreach ($titleContentData as $data) {
                if (!empty($data['title'])) {
                    $blockData[] = $data['title'];
                }
                if (!empty($data['content'])) {
                    $blockData[] = $data['content'];
                }
            }
        }

        return $blockData;
    }

}
