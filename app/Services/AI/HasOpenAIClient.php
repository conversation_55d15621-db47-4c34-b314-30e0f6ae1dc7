<?php

namespace App\Services\AI;

use App\Models\Candidate;
use App\Models\Setting;
use App\Services\AI\Proxies\OpenAiClientProxy;
use Closure;
use Illuminate\Support\Facades\Log;
use OpenAI;
use OpenAI\Factory;
use OpenAI\Responses\Chat\CreateResponse;

trait HasOpenAIClient
{
    /**
     * @param  null|Closure(Factory): Factory  $configure
     * @param  string  $model  Must be known for Azure implementation to work
     * @param  class-string  $caller
     */
    public function getClient(
        ?Closure $configure = null,
        string $model = 'gpt-4.1',
    ): OpenAI\Contracts\ClientContract {
        $provider = Setting::get(Setting::KEY_AI_PROVIDER);

        $factory = match ($provider) {
            Setting::AI_PROVIDER_OPENAI => $this->getOpenAiFactory(),
            Setting::AI_PROVIDER_AZURE_EUR => $this->getAzureOpenAiFactory($model),
            default => throw new \RuntimeException('Invalid AI provider'),
        };

        $callerItem = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, limit: 1);
        $caller = isset($callerItem[0]) ? ($callerItem[0]['class'] ?? null) : null;

        if ($configure) {
            $factory = $configure($factory);
        }

        return new OpenAiClientProxy($factory->make(), $provider, $caller);
    }

    private function getOpenAiFactory(): Factory
    {
        info('Using OpenAI');

        return OpenAI::factory()
            ->withApiKey(config('services.openai.secret'))
            ->withHttpHeader('Accept', 'application/json');
    }

    private function getAzureOpenAiFactory(string $model)
    {
        $msApiVersion = match ($model) {
            'gpt-4o' => '2025-01-01-preview',
            'gpt-4.1' => '2025-01-01-preview',
            'text-embedding-3-large' => '2023-05-15',
            'whisper-1' => '2024-06-01',
            default => throw new \RuntimeException('Invalid model for Azure'),
        };

        $msDeploymentName = match ($model) {
            'whisper-1' => 'whisper',
            default => $model,
        };
        // https://td-ai-2024.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview
        info("Using Azure OpenAI with model $model and version $msApiVersion");

        // https://td-ai-2024.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2025-01-01-preview
        return OpenAI::factory()
            ->withBaseUri(config('services.azure_eur_openai.base_uri') . '/openai/deployments/' . $msDeploymentName)
            ->withApiKey(config('services.azure_eur_openai.key'))
            ->withQueryParam('api-version', $msApiVersion)
            ->withHttpHeader('Accept', 'application/json');
    }

    public function sanitizeOpenAIJsonResponse(string $content): string
    {
        // Remove any comments that might be present in the response
        // Looks behind for a comma, then any amount of whitespace, then // and any amount of characters until the end of the line
        $content = preg_replace('/(?<=,)\s*\/\/.*/m', '', $content);

        // Remove any trailing commas that might be present in the response
        // Looks ahead for a comma, then any amount of whitespace, then a ] or }
        $content = preg_replace('/,(?=\s*[\]}])/m', '', $content);

        return $content;
    }

    protected function clearBasicPersonalDataFromInput(string $contents, Candidate $candidate): string
    {
        $contents = str_replace($candidate->name ?? '', '', $contents);
        $contents = str_replace($candidate->email ?? '', '', $contents);
        $contents = str_replace($candidate->linkedin_url ?? '', '', $contents);

        return $contents;
    }

    public function askJson(string $systemPrompt, string $userPrompt, int $temperature = 1): array
    {
        Log::debug('Asking openai for json response', [
            'systemPrompt' => $systemPrompt,
            'userPrompt' => $userPrompt,
        ]);
        $res = $this->getClient()
            ->chat()
            ->create([
                'model' => $this->getDefaultModel(),
                'temperature' => $temperature,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $userPrompt],
                ],
                'response_format' => ['type' => 'json_object'],
            ]);

        Log::debug('Got response from openai', [
            'response' => $res,
        ]);

        return json_decode($res->choices[0]->message->content, true);
    }

    public function askJsonSchema(
        string $systemPrompt,
        string $userPrompt,
        array $jsonSchema,
        int $temperature = 1
    ): array {
        Log::debug('Asking openai for json schema response', [
            'systemPrompt' => $systemPrompt,
            'userPrompt' => $userPrompt,
            'jsonSchema' => $jsonSchema,
        ]);
        $res = $this->getClient()
            ->chat()
            ->create([
                'model' => $this->getDefaultModel(),
                'temperature' => $temperature,
                'messages' => [
                    ['role' => 'system', 'content' => $systemPrompt],
                    ['role' => 'user', 'content' => $userPrompt],
                ],
                'response_format' => ['type' => 'json_schema', 'json_schema' => $jsonSchema],
            ]);

        Log::debug('Got response from openai', [
            'response' => $res,
        ]);

        return json_decode($res->choices[0]->message->content, true);
    }

    public function sanitizeFirstOption(CreateResponse $response): string
    {
        return $this->sanitizeOpenAIJsonResponse($this->getFirstOption($response));
    }

    public function getFirstOption(CreateResponse $response): string
    {
        return $response->choices[0]->message->content;
    }

    public function getDefaultModel(): string
    {
        return 'gpt-4.1';
    }
}
