<?php

namespace App\Services\AI;

use App\Models\AiChatMessage;
use App\Models\AiChatThread;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ChatService
{
    use HasOpenAIClient;

    public function __construct() {}

    /** @throws Exception */
    public function startThreadAbout(Model $chattable, string $systemPrompt, string $firstUserMessage): AiChatThread
    {
        $thread = $chattable->chats()->create([
            'system_prompt' => $systemPrompt,
            'user_id' => auth()->id(),
        ]);

        $this->continue($thread, $firstUserMessage);
        $this->setThreadTitle($thread);

        return $thread->load('messages.user');
    }

    public function delete(AiChatThread $thread): void
    {
        $thread->messages()->delete();
        $thread->delete();
    }

    /** @throws Exception */
    public function continue(AiChatThread $thread, string $userMessage): AiChatThread
    {
        $messages = [
            ['role' => 'system', 'content' => $thread->system_prompt],
        ];

        foreach ($thread->messages as $message) {
            $messages[] = ['role' => 'user', 'content' => $message->query];
            $messages[] = ['role' => 'assistant', 'content' => $message->response];
        }

        $messages[] = ['role' => 'user', 'content' => $userMessage];

        $parameters = [
            'model' => $this->getDefaultModel(),
            'messages' => $messages,
        ];

        Log::debug('ChatService::continue', $parameters);

        $res = retry(5, fn () => $this->getClient()->chat()->create($parameters), 100);

        $thread->messages()->create([
            'query' => $userMessage,
            'response' => $res->choices[0]->message->content,
            'user_id' => auth()->id(),
        ]);

        return $thread->load('messages.user');
    }

    public function setThreadTitle(AiChatThread $thread): void
    {
        /** @var AiChatMessage|null $message */
        $message = $thread->messages()->first();
        $title = $message?->query
            ? $this->generateShortSummary($message->query)
            : __('Chat thread');
        $thread->update(['title' => $title]);
    }

    /** @throws Exception */
    public function generateShortSummary(string $text): string
    {
        $queryLanguage = $this->detectLanguage($text);
        $systemPrompt = 'Generate a summary for this user message. ' .
            'The summary must not be longer than four words. ' .
            "The summary MUST be in the language identified by the ISO 639-1 code \"$queryLanguage\". " .
            'The response must be a JSON object with a single key "summary"';
        $schema = [
            'name' => 'short_summarization_response',
            'strict' => true,
            'schema' => [
                'type' => 'object',
                'properties' => [
                    'summary' => [
                        'type' => 'string',
                        'description' => "Short summary in the language of \"$queryLanguage\"",
                    ],
                ],
                'required' => ['summary'],
                'additionalProperties' => false,
            ],
        ];

        $response = retry(5, fn () => $this->askJsonSchema($systemPrompt, $text, $schema), 100);

        return $response['summary'];
    }

    /** @throws Exception */
    public function detectLanguage(string $text): string
    {
        $prompt = 'Detect the language of the user message and return it as a ISO 639-1 code. ' .
            'The response MUST be a JSON object with a single key "language"';
        $schema = [
            'name' => 'language_detection_response',
            'strict' => true,
            'schema' => [
                'type' => 'object',
                'properties' => [
                    'language' => [
                        'type' => 'string',
                        'description' => 'ISO 639-1 code',
                    ],
                ],
                'required' => ['language'],
                'additionalProperties' => false,
            ],
        ];

        $response = retry(5, fn () => $this->askJsonSchema($prompt, $text, $schema), 100);

        return $response['language'];
    }
}
