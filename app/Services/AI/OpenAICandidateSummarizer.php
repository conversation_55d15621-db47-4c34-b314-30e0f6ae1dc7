<?php

namespace App\Services\AI;

use App\Models\Candidate;
use App\Models\CandidateSummary;
use App\Models\File;
use App\Models\Project;
use App\Models\Setting;
use Carbon\CarbonInterval;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use OpenAI\Contracts\ClientContract;
use OpenAI\Factory as OpenAiFactory;
use OpenAI\Responses\Chat\CreateResponse;
use Soundasleep\Html2Text;

class OpenAICandidateSummarizer
{
    use HasOpenAIClient;

    public function __construct(
        private readonly Project $project,
        private readonly Candidate $candidate
    ) {}

    const string FAIL_INVALID_RESPONSE_FROM_AI_MODEL = 'invalid_response_from_ai_model';
    const string FAIL_NO_CV_CONTENTS = 'no_cv_contents';

    public function summarize(): void
    {
        logger('In OpenAICandidateSummarizer', ['candidate_id' => $this->candidate->id]);
        $language = Setting::get(Setting::KEY_LANGUAGE);

        $requirements = $this->project->description
            ? 'Requirements for this position are: ' . Html2Text::convert($this->project->description)
            : '';

        $systemPrompt = <<<PROMPT
                You are a summarization service for an applicant tracking system. Your main task is to
                summarize applicant data in a way that is useful for making good hiring decisions. You
                will do your best to not perpetuate any harmful biases. You are being used by companies
                who want to be more inclusive and equitable. You must never use the applicant's name in your response.
                You must never give the candidate's age. You must never use the persons gender - use
                "they" or "the candidate" for everyone. In the one sentence summary: try to give years of total experience,
                be as succinct as possible (15 words is good, 30 is maximum).
                In the five sentence summary: prefer bullet points and highlight specific projects and situations.
                When assessing position fit, be critical.
                The text you are generating must be in the language identified by the ISO-639-1 language code "$language".

                The position you are hiring for is "{$this->project->position_name}". $requirements

                You must give your responses in the following JSON format:

                {
                  "one_sentence_summary": "",
                  "five_sentence_summary_html": "",
                  "highest_level_of_education": "",
                  "fit_for_position_assessment": "",
                  "is_relevant_for_position": true,
                  "skills": [
                    {
                      "skill_name": "",
                      "skill_level": ""
                    }
                  ],
                  "work_experience": [
                    {
                      "company": "",
                      "role": "",
                      "start_date": "",
                      "end_date": ""
                    }
                  ],
                  "education": [
                    {
                      "institution": "",
                      "degree_or_level": "",
                      "start_date": "",
                      "end_date": ""
                    }
                  ]
                }

                All strings containing natural language in the JSON must be translated into the language identified by the ISO-639-1 language code "$language".

                The user will send you the contents of a parsed CV.
                PROMPT;

        $file = $this->candidate->lastCv;
        logger('candidate->lastCv?->id', ['cv_id' => $file?->id]);

        if (!$file || !$file->contents) {
            logger('Candidate summarization failed due to ' . $file ? 'their last CV having no content.' : 'them not having a CV.', [
                'file_id' => $file?->id,
            ]);
            $this->updateLatestOrCreateCandidateSummary([
                'raw_model_response' => null,
                'is_failed' => true,
                'fail_reason' => self::FAIL_NO_CV_CONTENTS,
                'summary_json' => null,
                'anonymized_cv_html' => null,
                'usage_json' => null,
            ]);

            return;
        }

        $userPrompt = $this->clearBasicPersonalDataFromInput($file->contents, $this->candidate);
        info('Asking OpenAI for summarization.', ['system_prompt' => $systemPrompt, 'user_prompt' => $userPrompt]);
        $summarizationResponse = $this->getOpenAiClient()->chat()->create([
            'model' => $this->getDefaultModel(),
            'response_format' => ['type' => 'json_object'],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemPrompt,
                ],
                [
                    'role' => 'user',
                    'content' => Str::limit($userPrompt, 7000, '...'),
                ],
            ],
        ]);

        info('Received summarization response from OpenAI.', $summarizationResponse->toArray());
        $summary = $this->sanitizeOpenAIJsonResponse($summarizationResponse->choices[0]->message->content);
        $anonymizedCvHtml = $this->getAnonymizedCvHtml($this->candidate->lastCv);

        if (json_decode($summary) && $anonymizedCvHtml) {
            $this->updateLatestOrCreateCandidateSummary([
                'summary_json' => json_decode($summary),
                'usage_json' => $summarizationResponse->usage->toArray(),
                'raw_model_response' => $summary,
                'anonymized_cv_html' => $anonymizedCvHtml,
                'is_failed' => false,
                'fail_reason' => null,
            ]);
        } else {
            \Log::error('Invalid response from AI model', [
                'response' => $summarizationResponse->toArray(),
                'summary' => $summary,
            ]);
            $this->updateLatestOrCreateCandidateSummary([
                'usage_json' => $summarizationResponse->usage->toArray(),
                'raw_model_response' => $summary,
                'is_failed' => true,
                'fail_reason' => self::FAIL_INVALID_RESPONSE_FROM_AI_MODEL,
                'summary_json' => null,
                'anonymized_cv_html' => null,
            ]);
        }
    }

    public function getAnonymizedCvHtml(File $cv): ?string
    {
        $response = $this->getFirstOption($this->askAnonymizationResponse($cv));

        Log::debug('Received response for CV anonymization', [
            'response' => $response,
            'cv' => $cv->only(['id', 'location']),
        ]);

        return collect(json_decode($response))->get('anonymized_cv_html');
    }

    public function askAnonymizationResponse(File $file): CreateResponse
    {
        $anonymizationPrompt = $this->getAnonymizationPrompt();
        $userPrompt = Str::limit($file->contents, 7000);
        info('Asking OpenAI for anonymized CV.', [
            'system_prompt' => $anonymizationPrompt,
            'user_prompt' => $userPrompt,
        ]);

        return $this->getOpenAiClient()->chat()->create([
            'model' => $this->getDefaultModel(),
            'response_format' => [
                'type' => 'json_schema',
                'json_schema' => [
                    'name' => 'cv_anonymization_response',
                    'description' => 'Response object for anonymization CV generation',
                    'strict' => true,
                    'schema' => [
                        'type' => 'object',
                        'properties' => [
                            'anonymized_cv_html' => [
                                'type' => 'string',
                                'description' => 'Valid HTML for the anonymized CV',
                            ],
                        ],
                        'required' => ['anonymized_cv_html'],
                        'additionalProperties' => false,
                    ],
                ],
            ],
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $anonymizationPrompt,
                ],
                [
                    'role' => 'user',
                    'content' => $userPrompt,
                ],
            ],
        ]);
    }

    public function getAnonymizationPrompt(): string
    {
        return <<<'PROMPT'
                You are a summarization service for an applicant tracking system.
                Your task is to generate an anonymized CV, encoded as HTML, using the user-provided contents of a parsed CV.
                You must remove any personal identifiable information from the user-provided text.
                Any references to the person's name, age, gender, and socio-economic background must be removed.
                If the user-provided text contains a "Contacts" section, then its contents may be removed.
                Any links and URLs in the CV must be removed.
                Do not infer or assume any information that has not been explicitly stated in the document.

                You must give your response in the following JSON format:

                {
                  "anonymized_cv_html": ""
                }

                The value of "anonymized_cv_html" must be a string containing the anonymized CV encoded as HTML.

                The user will send you the contents of a parsed CV.
                PROMPT;
    }

    private function getOpenAiClient(): ClientContract
    {
        $key = 'OpenAICandidateSummarizer.OpenAIClient';
        if (App::has($key)) {
            // For testing
            return App::get($key);
        }

        $httpClient = new GuzzleClient(['timeout' => CarbonInterval::minutes(3)->totalSeconds]);

        return $this->getClient(fn (OpenAiFactory $factory) => $factory->withHttpClient($httpClient));
    }

    private function updateLatestOrCreateCandidateSummary(array $attributes): void
    {
        /** @var CandidateSummary $latestOrNewSummary */
        $latestOrNewSummary = $this->candidate->summary()->forProject($this->project)->firstOrNew();
        $attributesWithForeignKeys = [
            ...$attributes,
            'candidate_id' => $this->candidate->id,
            'project_id' => $this->project->id,
        ];
        $latestOrNewSummary->fill($attributesWithForeignKeys)->save();
        logger('Saved candidate summary.', $attributesWithForeignKeys);
    }
}
