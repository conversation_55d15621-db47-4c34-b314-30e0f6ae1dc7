<?php

namespace App\Services\AI;

use App\Helpers;
use Carbon\Carbon;
use Carbon\Exceptions\InvalidFormatException;
use Illuminate\Support\Str;

class OpenAICVExtractor
{
    use HasOpenAIClient;

    const FAIL_AI_MODEL_DID_NOT_RETURN_ARGUMENTS = 'ai_model_did_not_return_arguments';
    const FAIL_AI_MODEL_DID_NOT_RETURN_VALID_JSON = 'ai_model_did_not_return_valid_json';

    public function extract(string $cv): OpenAICVExtractorResult
    {
        $systemPrompt = <<<'PROMPT'
            You are a summarization tool integrated within an applicant tracking system (ATS).
            Your primary function is to extract data from a candidate's CV and format this data according to a
            specific predefined structure. Follow these guidelines meticulously:

            Extract Data Accurately: Only extract information that pertains directly to the candidate whose CV you
            are processing. Do not infer or assume any information not explicitly stated in the CV.

            Error Handling: Transcribe data exactly as it appears in the document, including any typos or
            grammatical errors. Do not correct or modify the original text.

            Data Formatting: Format all dates in the ISO8601 standard. For any field value that is part of an
            enumeration, ensure that the value you provide matches one of the specified enumerator values or is
            set to null if no applicable data is present.

            Function Call: Call the provided function with the extracted data. When calling the function, ensure
            all fields are included. If a particular piece of information is missing, pass null for that field.
            Do not omit any fields from the function call.

            By adhering to these guidelines, ensure that the data is structured and the provided function is called
            precisely as required by the ATS system.

            The user will send you the text contents of a CV.
        PROMPT;

        info($systemPrompt);

        $response = $this->getClient()->chat()->create([
            'model' => $this->getDefaultModel(),
            'temperature' => 0.4,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemPrompt,
                ],
                [
                    'role' => 'user',
                    'content' => Str::limit("The CV contents are the following:\n\n" . $cv, 10_000, '...'),
                ],
            ],
            'tools' => [
                [
                    'type' => 'function',
                    'function' => json_decode(file_get_contents(base_path('app/Services/AI/PromptFixtures/small_cv_summarizer_function.json')), true),
                ],
            ],
        ]);

        info($response->toArray());

        $functionArguments = $response->choices[0]?->message->toolCalls[0]?->function?->arguments;
        if (!$functionArguments) {
            Helpers::toLogOrSentry(new \Exception('AI model did not return arguments'));

            // In case we did not receive function arguments, it is possible that the message content contains the arguments
            $functionArguments = $response->choices[0]->message->content;

            // The arguments may be wrapped as if they were in markdown, so we need to remove the wrapping
            $functionArguments = str_replace('```json', '', $functionArguments);
            $functionArguments = str_replace('```', '', $functionArguments);
        }

        $functionArguments = self::fixBadEncodings($functionArguments);

        try {
            $jsonContent = json_decode($functionArguments, true);
            if (empty($jsonContent)) {
                throw new \Exception('Unable to parse function arguments directly');
            }
        } catch (\Exception $e) {
            Helpers::toLogOrSentry($e);
            $sanitizedContent = $this->sanitizeOpenAIJsonResponse($functionArguments);
            $jsonContent = json_decode($sanitizedContent, true);
        }

        if (empty($jsonContent)) {
            return new OpenAICVExtractorResult(
                response: $response,
                fields: null,
                error: self::FAIL_AI_MODEL_DID_NOT_RETURN_VALID_JSON,
            );
        }
        $fixedContent = OpenAICVExtractor::sanitizeFields($jsonContent);

        return new OpenAICVExtractorResult(
            response: $response,
            fields: $fixedContent,
        );
    }

    /**
     * There's an issue with gpt-3.5-turbo-1106.
     *
     * https://community.openai.com/t/gpt-4-1106-preview-messes-up-function-call-parameters-encoding/478500/71
     */
    public static function fixBadEncodings(string $json): string
    {
        // fix double escaped unicode (\\u0000 -> \u0000)
        $json = preg_replace('/\\\\\\\u([0-9a-fA-F]{4})/', '\\u$1', $json);

        // fix unicode that was not escaped (u0000 -> \u0000)
        // $json = preg_replace_callback('/(?<!\\\)u[0-9a-fA-F]{4}/', fn($m) => '', $json);

        // unescape % encoded characters (e.g. %20 -> " ")
        $json = urldecode($json);

        // unescape html entities (e.g. &uuml; -> ü)
        $json = html_entity_decode($json);

        return $json;
    }

    public static function sanitizeFields(array $content): array
    {
        foreach ($content as $contentKey => $contentValue) {
            if (is_array($contentValue)) {
                $content[$contentKey] = OpenAICVExtractor::sanitizeFields($contentValue);
            } else {
                $value = self::fixNull($contentValue);
                $value = self::fixDates($contentKey, $value);

                $content[$contentKey] = $value;
            }
        }

        return $content;
    }

    private static function fixNull($fieldValue)
    {
        if ($fieldValue === 'null') {
            return null;
        }

        return $fieldValue;
    }

    private static function fixDates($fieldKey, $fieldValue)
    {
        if ($fieldValue === null) {
            return null;
        }

        // if $fieldKey contains the word "date" and $fieldValue is not null
        // check if the value is "present" or "current" and return null instead
        if (Str::contains($fieldKey, 'date')) {
            if (Str::contains($fieldValue, 'present') || Str::contains($fieldValue, 'current')) {
                return null;
            }

            try {
                return Carbon::parse($fieldValue);
            } catch (InvalidFormatException) {
                // If $fieldValue is in the format of 09/2019, parse it as a month and year
                return Carbon::createFromFormat('m/Y', $fieldValue);
            } catch (\Exception $e) {
                Helpers::toLogOrSentry($e);

                return null;
            }
        }

        return $fieldValue;
    }
}
