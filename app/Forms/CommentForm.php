<?php

namespace App\Forms;

use App\Forms\Traits\DefaultsHelper;
use App\Forms\Traits\HasMentions;
use App\Helpers;
use App\Models\Activity;
use App\Models\Comment;
use App\Models\Setting;
use App\Models\Website;
use Laraform\Laraform;

class CommentForm extends Laraform
{
    public $model = Comment::class;

    public $endpoint = '/laraform/process';

    use DefaultsHelper, HasMentions;

    public function schema()
    {
        $isAgency = Setting::isAgency();
        $clientStr = $isAgency ? __('clients') : __('managers');

        $contentAutoSaveKeyArr = [];
        $contentAutoSaveKey = '';
        if (request('candidate_id')) {
            $contentAutoSaveKeyArr[] = 'c' . request('candidate_id');
        }
        if (request('project_id')) {
            $contentAutoSaveKeyArr[] = 'p' . request('project_id');
        }
        if (!empty($contentAutoSaveKeyArr)) {
            array_unshift($contentAutoSaveKeyArr, 'comment_form_' . auth()->id());
            $contentAutoSaveKey = implode('_', $contentAutoSaveKeyArr);
        }

        return [
            'content' => [
                'type' => 'trix',
                'columns' => [
                    'field' => 12,
                    'label' => 0,
                ],
                'mentions' => true,
                'for_type' => 'Comment',
                'potentially_mentionable_users' => $this->getPotentiallyMentionableUsers(),
                'auto_save_key' => $contentAutoSaveKey,
                'before' => '<p class="text-xxs mb-1">' . __('Who are you talking to?') . ' <strong>' . __('Type @ to tag a colleague.') . '</strong> ' . __('They will be notified when you post the comment.') . '</p>',
                'writing_assistant' => [
                    'context' => 'The user is writing a comment about a candidate. Only teammates will see this comment.',
                    'ids' => [
                        'candidate_id' => request('candidate_id'),
                    ],
                ],
                'submit_on_ctrl_enter' => true,
                'autofocus' => true,
            ],
            'rating' => [
                'type' => 'sliderate',
                'default' => null,
                'columns' => [
                    'field' => 12,
                    'label' => 0,
                ],
                'prefix' => __('Position fit:'),
            ],
            'is_public' => [
                'type' => 'toggle',
                'label' => '',
                'tooltip' => __('When switched on, this comment will be visible when sharing the candidate with :client.', ['client' => $clientStr]),
                'default' => $this->getCommentIsPublicFieldDefaultValue(),
                'columns' => [
                    'field' => 5,
                    'label' => 0,
                ],
                'text' => __('Make this comment visible to limited users'),
                'dimensions' => [
                    'height' => 17,
                    'width' => 34,
                ],
            ],
            'id' => [
                'type' => 'key',
            ],
            'candidate_id' => [
                'type' => 'hidden',
            ],
            'user_id' => [
                'type' => 'meta',
                'default' => auth()->id(),
            ],
            'project_id' => [
                'type' => 'meta',
                'default' => null,
            ],
            'video_interview_invite_id' => [
                'type' => 'meta',
                'default' => null,
            ],
            'updated_by_id' => [
                'type' => 'meta',
                'default' => null,
            ],
        ];
    }

    public function before()
    {
        $data = $this->data;
        $currentUserId = auth()->id();

        $entity = $this->getEntity();

        if ($entity && $entity->user_id !== $currentUserId && !Helpers::getCurrentWebsite()->features[Website::FEATURE_EDIT_ALL_COMMENTS]) {
            return $this->fail(__('You are not allowed to edit this comment.'));
        }

        if (!isset($data['id'])) {
            // Automatically set the user_id for new comments
            $data['user_id'] = $currentUserId;
        } else {
            // Automatically set the updated_by for existing comments
            $data['updated_by_id'] = $currentUserId;
            $data['user_id'] = $entity->user_id;
        }

        $this->setData($data);
    }

    public function after()
    {
        if (isset($this->updates['id'])) {
            Activity::log(Activity::COMMENT_ADDED, [
                'comment_id' => $this->updates['id'],
            ]);
        }
        /** @var Comment $comment */
        $comment = Comment::find($this->data['id'] ?? $this->updates['id']);
        $comment->notifyTaggedUsers();
    }
}
