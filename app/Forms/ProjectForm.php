<?php

namespace App\Forms;

use App\Classes\FeatureControls\FeatureControls;
use App\Forms\Traits\ProjectFields;
use App\Forms\Traits\StageCategoryFields;
use App\Forms\Traits\TeamFields;
use App\Helpers;
use App\Models\CrmOrganization;
use App\Models\Location;
use App\Models\Place;
use App\Models\Project;
use App\Models\ProjectFailureReason;
use App\Models\Scorecard;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Laraform\Laraform;

class ProjectForm extends Laraform
{
    use ProjectFields, StageCategoryFields, TeamFields;

    public $model = Project::class;

    public $component = 'base-form ref="bform"';

    public $endpoint = '/laraform/process';

    public $columns = [
        'element' => 5,
        'label' => 12,
        'field' => 12,
    ];

    public $meta = [];

    public function schema()
    {
        $isAgency = Setting::isAgency();
        $schema = [
            'id' => [
                'type' => 'key',
            ],
            ...$this->getProjectNameFields(),
            'status' => [
                'type' => 'select',
                'label' => __('Status') . '<span class="text-danger">*</span>',
                'default' => Project::STATUS_IN_PROGRESS,
                'items' => array_map(
                    fn ($k, $v) => ['value' => $k, 'label' => $v],
                    array_keys(Project::getStatuses()),
                    Project::getStatuses()
                ),
            ],
            'end_date' => [
                'type' => 'date',
                'rules' => 'nullable',
                'label' => __('End date'),
                'conditions' => [
                    ['status', Project::FINISHED_STATUSES],
                ],
                'format' => 'Y-m-d',
                'default' => Carbon::now()->format('Y-m-d'),
            ],
            'project_failure_reason_id' => [
                'type' => 'select',
                'rules' => Setting::get(Setting::KEY_REQUIRE_PROJECT_FAILURE_REASONS) ? 'required' : [],
                'items' => Setting::get(Setting::KEY_REQUIRE_PROJECT_FAILURE_REASONS)
                    ? ProjectFailureReason::getFormItemsGrouped()
                    : [
                        ['value' => null, 'label' => __('Unspecified')],
                        ...ProjectFailureReason::getFormItemsGrouped(),
                    ],
                'label' => __('Failure reason'),
                'conditions' => [
                    ['status', Project::STATUS_FAILED],
                ],
                'columns' => [
                    'element' => 6,
                ],
            ],
            'warranty_until' => [
                'type' => 'date',
                'rules' => 'nullable',
                'label' => __('Warranty until'),
                'conditions' => [
                    ['status', Project::STATUS_FINISHED],
                ],
                'placeholder' => __('Enter warranty date if applicable'),
            ],
            'start_date' => [
                'type' => 'date',
                'rules' => 'nullable',
                'label' => __('Start date'),
                'default' => Carbon::now()->format('Y-m-d'),
            ],
            'deadline_at' => [
                'type' => 'date',
                'rules' => 'nullable',
                'label' => __('Deadline'),
                'conditions' => [
                    ['is_perpetual', 0],
                ],
                'class' => 'rightMost',
            ],
            'is_perpetual' => [
                'type' => 'toggle',
                'label' => __('Continuous project (always accepting candidates)'),
                'default' => false,
                'class' => 'reverse-field-label',
                'columns' => [
                    'element' => 12,
                    'field' => 1,
                    'label' => 11,
                ],
            ],
            'perpetual_gdpr_consent_length' => [
                'type' => 'select',
                'label' => __('GDPR consent length') . '<span class="text-danger">*</span>',
                'default' => Project::PERPETUAL_GDPR_CONSENT_LENGTH_6M,
                'items' => array_map(
                    fn ($k, $v) => ['value' => $k, 'label' => $v],
                    array_keys(Project::getPerpetualGdprConsentLengths()),
                    Project::getPerpetualGdprConsentLengths()
                ),
                'conditions' => [
                    ['is_perpetual', 1],
                ],
                'rules' => ['required'],
                'columns' => [
                    'element' => 12,
                    'field' => 5,
                    'label' => 12,
                ],
            ],
            'monthly_salary' => [
                'type' => 'text',
                'label' => __('Salary range'),
            ],
            'landing_page' => [
                'type' => 'text',
                'rules' => 'url|nullable',
                'label' => __('Landing page'),
            ],
            'scorecards' => [
                'type' => 'tags',
                'label' => __('Scorecards') . Helpers::getUpgradeBadge('scorecards'),
                'items' => Scorecard::orderBy('name')
                    ->get()->map(function ($scorecard) {
                        return [
                            'value' => $scorecard->id,
                            'label' => $scorecard->name,
                        ];
                    }),
                'disabled' => !FeatureControls::get()->scorecards->view,
            ],
            'locations' => [
                'type' => 'tags',
                'label' => __('Locations'),
                'items' => [
                    ...Helpers::modelsToOptions(Location::orderBy('name')->get()),
                    ['label' => __('Add new location'), 'value' => 'new'],
                ],
            ],
            'new_location' => [
                'type' => 'object',
                'persist' => false,
                'conditions' => [
                    ['locations', ['new']],
                ],
                'columns' => [
                    'element' => 12,
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => [
                    'name' => [
                        'type' => 'text',
                        'rules' => 'required',
                        'label' => __('Location name'),
                    ],
                    'place' => [
                        'type' => 'place',
                        'label' => __('Location'),
                    ],
                ],
            ],
            'description' => [
                'type' => 'trix',
                'label' => __('Description'),
                'columns' => ['element' => 12],
                'no_attachments' => true,
            ],
            ...$this->getTeamFields(),
            'project_manager_id' => [
                'type' => 'select',
                'items' => User::orderBy('name')
                    ->active()
                    ->get()->map(function ($user) {
                        return [
                            'value' => $user->id,
                            'label' => $user->name,
                        ];
                    }),
                'label' => __('Project manager') . '<span class="text-danger">*</span>',
                'placeholder' => __('Choose project manager'),
                'rules' => 'required',
                'trackBy' => 'name',
                'default' => auth()->id(),
                'persist' => false,
            ],
            ...($isAgency ? [
                'crm_organization_id' => [
                    'type' => 'select',
                    'items' => CrmOrganization::orderBy('name')
                        ->get()->map(function ($client) {
                            return [
                                'value' => $client->id,
                                'label' => $client->name,
                            ];
                        })->prepend(['label' => $isAgency ? __('Add new client') : __('Add new manager'), 'value' => 'new'])
                        ->prepend(['label' => $isAgency ? __('Choose client') : __('Choose manager'), 'value' => null]),
                    'label' => $isAgency ? __('Client') : __('Manager'),
                    'trackBy' => 'name',
                ],
                'crm_office_id' => [
                    'type' => 'select',
                    'label' => __('Office'),
                    'json_logic_items' => [
                        'if' => CrmOrganization::query()->with(['offices'])->get()
                            ->map(function (CrmOrganization $organization) {
                                return [
                                    ['==' => [['var' => 'crm_organization_id'], $organization->id]],
                                    [['value' => null, 'label' => '-'], ...Helpers::modelsToOptions($organization->offices)],
                                ];
                            })->flatten(1)->toArray(),
                    ],
                    'items' => [],
                    'conditions' => [
                        ['crm_organization_id', '!=', null],
                        ['crm_organization_id', '!=', 'new'],
                    ],
                ],
                'new_client' => [
                    'type' => 'object',
                    // 'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-3 rounded',
                    'conditions' => [
                        ['crm_organization_id', 'new'],
                    ],
                    'columns' => [
                        'element' => 12,
                        'label' => 0,
                        'field' => 12,
                    ],
                    'persist' => false,
                    'schema' => [
                        'name' => [
                            'type' => 'text',
                            'rules' => 'required',
                            'label' => $isAgency ? __('Client name') : __('Manager name'),
                        ],
                        'client_manager_id' => [
                            'type' => 'select',
                            'label' => __('Managing recruiter'),
                            'items' => User::orderBy('name')
                                ->active()
                                ->get()
                                ->map(function (User $user) {
                                    return [
                                        'value' => $user->id,
                                        'label' => $user->name,
                                    ];
                                }),
                        ],
                    ],
                ],
            ] : []),
            'users' => [
                'type' => 'tags',
                'search' => true,
                'label' => __('Members'),
                'items' => User::query()
                    ->active()
                    ->orderBy('name')
                    ->get()
                    ->map(function ($user) {
                        return [
                            'value' => $user->id,
                            'label' => $user->name,
                        ];
                    })->values()->toArray(),
                'columns' => [
                    'element' => 10,
                ],
                'persist' => false,
            ],
            'accessible_only_members' => [
                'type' => 'toggle',
                'label' => __('Project visible only for members'),
                'default' => Setting::get(Setting::KEY_PROJECTS_PRIVATE_BY_DEFAULT) ? 1 : 0,
                'trueValue' => 1,
                'falseValue' => 0,
                'class' => 'reverse-field-label',
                'columns' => [
                    'element' => 12,
                    'field' => 1,
                    'label' => 11,
                ],
            ],
            'stages' => [
                'type' => 'list',
                'label' => __('Stages'),
                'sort' => true,
                'orderBy' => 'sort_order',
                'storeOrder' => 'sort_order',
                'default' => Setting::get(Setting::KEY_DEFAULT_STAGES),
                'columns' => [
                    'element' => 12,
                    'field' => 12,
                ],
                'object' => [
                    'columns' => [
                        'element' => 12,
                        'label' => 0,
                        'field' => 12,
                    ],
                    //                    'class' => 'bg-gray-light border border-gray mb-2 pl-3 pr-3 pt-1 rounded',
                    'schema' => [
                        'id' => [
                            'type' => 'key',
                        ],
                        'name' => [
                            'label' => __('Stage name'),
                            'type' => 'text',
                            'rules' => 'required',
                        ],
                        ...$this->getStageCategoryFields(),
                        'sort_order' => [
                            'type' => 'meta',
                        ],
                    ],
                ],
            ],
            'custom_fields' => [
                'type' => 'object',
                'columns' => [
                    'element' => 10,
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => Setting::getProjectCustomFields()->toArray(),
            ],
        ];

        return $schema;
    }

    public function before()
    {
        $data = $this->data;
        $currentUserId = auth()->id();

        if ($data['accessible_only_members'] == 1 && $data['project_manager_id'] != $currentUserId && !in_array($currentUserId, $data['users'] ?? [])) {
            return response()->json([
                'status' => 'fail',
                'messages' => [__('You must include yourself as manager or member for confidential projects.')],
            ]);
        }

        $request = request()->all();
        if (data_get($request, 'data.crm_organization_id') === 'new') {
            $client = CrmOrganization::create(data_get($request, 'data.new_client'));
            $request['data']['crm_organization_id'] = $client->id;
            $this->setData($request['data']);
        }

        $locations = data_get($data, 'locations', []);
        if (in_array('new', $locations)) {
            unset($locations[array_search('new', $locations)]);
            $placeData = data_get($data, 'new_location.place');
            $place = Place::create($placeData);
            $location = Location::create(['name' => data_get($data, 'new_location.name'), 'place_id' => $place->id]);
            $locations = array_merge($locations, [$location->id]);
            unset($data['new_location']);
            unset($this->schema['new_location']);
            $data['locations'] = $locations;
            $this->setElements();
            $this->setData($data);
        }

        // scopes don't matter if it's new
        if (!isset($data['id'])) {
            Project::removeAccessScope();
        } elseif (isset($data['id'])) {
            if (Project::find($data['id'])->project_manager_id == $currentUserId && $data['project_manager_id'] != $currentUserId) {
                Project::removeAccessScope();
            }
        }
    }

    public function after()
    {
        $project = $this->getEntity();

        $formUserIds = collect([...$this->data['users'], $this->data['project_manager_id']])->unique();

        // Detach removed users
        $toDetach = $project->projectUsers->pluck('user_id')->diff($formUserIds);
        if ($toDetach->isNotEmpty()) {
            $project->projectUsers()->whereIn('user_id', $toDetach)->delete();
        }

        // Attach new users
        $toAttach = $formUserIds->diff($project->projectUsers->pluck('user_id'));
        if ($toAttach->isNotEmpty()) {
            $project->projectUsers()->createMany($toAttach->map(fn ($userId) => ['user_id' => $userId]));
        }

        // Update project manager (has to be last step as we're relying on a pgsql trigger)
        if ($project->project_manager_id != $this->data['project_manager_id']) {
            $project->project_manager_id = $this->data['project_manager_id'];
            $project->save();
        }

        Project::restoreAccessScope();

        if (!Project::find($this->data['id'])) {
            return response([
                'messages' => [],
                'payload' => [
                    'updates' => [],
                    'redirect_url' => url('projects'),
                ],
                'status' => 'success',
            ]);
        }
    }

}
