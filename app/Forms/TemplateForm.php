<?php

namespace App\Forms;

use App\Forms\Traits\MessageHelper;
use App\Helpers;
use App\Models\Form;
use App\Models\Template;
use App\Models\User;
use Laraform\Laraform;

class TemplateForm extends Laraform
{
    public $model = Template::class;

    public $component = 'base-form ref="bform"';

    public $endpoint = '/laraform/process';

    use MessageHelper;

    public function schema()
    {
        $userMessageFormField = $this->getFormIdField(
            'user_message_body',
            formTypes: [Form::TYPE_USER],
            fieldName: 'user_form_id',
            additionalJsonLogic: [
                '==' => [
                    ['var' => 'template_type'],
                    Template::TYPE_USER_MESSAGE,
                ],
            ],
            required: true,
        );

        $candidateMessageFormField = $this->getFormIdField(
            'message_body',
            formTypes: [Form::TYPE_CANDIDATE, Form::TYPE_ADDITIONAL_INFO],
            additionalJsonLogic: [
                '==' => [
                    ['var' => 'template_type'],
                    Template::TYPE_CANDIDATE_MESSAGE,
                ],
            ]
        );

        $candidateMessageSurveyField = $this->getFormIdField(
            'message_body',
            formTypes: [Form::TYPE_SURVEY],
            filter: fn ($forms) => $forms->withCnpsField(),
            fieldName: 'survey_form_id',
            additionalJsonLogic: [
                'in' => [
                    ['var' => 'template_type'],
                    [Template::TYPE_CANDIDATE_MESSAGE, Template::TYPE_VIDEO_MESSAGE, Template::TYPE_VIDEO_INTERVIEW],
                ],
            ],
            mergeTags: ['survey'],
            label: __('Survey'),
            emptyOption: ['value' => null, 'label' => __('Default survey')],
            required: false,
            after: __('Default survey will use the question defined under Organization settings. If you wish to send a form with more questions, create a new Survey form.')
        );

        return [
            'id' => [
                'type' => 'key',
            ],
            'name' => [
                'type' => 'text',
                'label' => __('Template name'),
                'rules' => 'required',
            ],
            'template_type' => [
                'type' => 'select',
                'label' => __('Type'),
                'items' => Helpers::assocToOptions(Template::getTypes()),
                'default' => Template::TYPE_CANDIDATE_MESSAGE,
            ],
            'subject' => [
                'type' => 'text',
                'label' => __('Subject'),
                'rules' => [
                    ['required' => ['template_type', '!=', Template::TYPE_INVITE]],
                ],
                'conditions' => [
                    ['template_type', '!=', Template::TYPE_SMS],
                ],
            ],
            'user_id' => [
                'type' => 'select',
                'items' => User::orderBy('name')
                    ->active()
                    ->get()
                    ->map(function (User $user) {
                        return [
                            'value' => $user->id,
                            'label' => $user->name,
                        ];
                    })->prepend(['value' => null, 'label' => __('All users')]),
                'label' => __('Template visible for'),
                'default' => null,
            ],
            'message_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'rules' => 'required',
                'after' => $this->getMergeTagInfo(other: ['referee_entry_form', 'form_url', 'position_display_name']),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_CANDIDATE_MESSAGE],
                ],
            ],
            'user_message_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'after' => $this->getMergeTagInfo(null, withSurvey: false, other: ['form_url'], exclude: ['consent_renewal_url']),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_USER_MESSAGE],
                ],
            ],
            'event_set_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'rules' => 'regex:/\[invite_url]/',
                'after' => $this->getMergeTagInfo('invite_url', withSurvey: false, other: ['event_title', 'position_display_name']),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_EVENT_SET],
                ],
            ],
            'invite_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'after' => $this->getMergeTagInfo(
                    'video_call_details:' . __('when this template is used for video call interviews'),
                    false,
                    [
                        'event_title',
                        'event_start',
                        'participant_list',
                        'participant_list_with_phones',
                        'participant_list_with_user_titles',
                        'meeting_room',
                        'position_display_name',
                    ]
                ),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_INVITE],
                ],
            ],
            'video_interview_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'rules' => 'regex:/\[interview_url]/',
                'after' => $this->getMergeTagInfo('interview_url', other: ['position_display_name']),
                'clickable_merge_tags' => true,
                'messages' => [
                    'regex' => $this->getRequiredMergeTagValidationMessage('interview_url'),
                ],
                'conditions' => [
                    ['template_type', '=', Template::TYPE_VIDEO_INTERVIEW],
                ],
            ],
            'video_message_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'rules' => 'regex:/\[video_url]/',
                'after' => $this->getMergeTagInfo('video_url', other: ['position_display_name']),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_VIDEO_MESSAGE],
                ],
                'messages' => [
                    'regex' => $this->getRequiredMergeTagValidationMessage('video_url'),
                ],
            ],
            'consent_message_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'rules' => 'regex:/\[consent_renewal_url]/',
                'after' => $this->getMergeTagInfo('consent_renewal_url', other: ['position_display_name']),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_CONSENT_RENEWAL],
                ],
            ],
            'reference_body' => [
                'type' => 'trix',
                'label' => __('Message body'),
                'rules' => 'regex:/\[reference_form_url]/',
                'after' => $this->getMergeTagInfo('reference_form_url', withSurvey: false, other: ['position_display_name']),
                'clickable_merge_tags' => true,
                'conditions' => [
                    ['template_type', '=', Template::TYPE_REFERENCE],
                ],
                'messages' => [
                    'regex' => $this->getRequiredMergeTagValidationMessage('reference_form_url'),
                ],
            ],
            'sms_body' => [
                'type' => 'customtextarea',
                'label' => __('SMS body'),
                'clickable_merge_tags' => true,
                'rules' => 'required',
                'conditions' => [
                    ['template_type', '=', Template::TYPE_SMS],
                ],
                'after' => $this->getMergeTagInfo(withSurvey: false, other: ['position_display_name'], exclude: ['consent_renewal_url']),
                'writing_assistant' => [
                    'context' => 'The user is writing an SMS to a candidate.',
                ],
            ],
            'body' => [
                'type' => 'meta',
            ],
            'data' => [
                'type' => 'object',
                'columns' => [
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => [
                    'video_id' => [
                        'label' => __('Video'),
                        'type' => 'video',
                        'show_file_upload' => true,
                        'rules' => 'required',
                        'conditions' => [
                            ['template_type', '=', Template::TYPE_VIDEO_MESSAGE],
                        ],
                    ],
                    'sms_body' => [
                        'type' => 'customtextarea',
                        'label' => __('SMS body'),
                        'clickable_merge_tags' => true,
                        //  'rules' => 'regex:/\[invite_url]/',
                        'conditions' => [
                            ['template_type', '=', Template::TYPE_EVENT_SET],
                        ],
                        'after' => $this->getMergeTagInfo(
                            withSurvey: false,
                            other: ['position_display_name'],
                            exclude: ['consent_renewal_url'],
                        ),
                        'description' => __('Since this is an SMS, keep it short.'),
                    ],
                    'sms_body__invite' => [
                        'type' => 'customtextarea',
                        'label' => __('SMS body'),
                        'clickable_merge_tags' => true,
                        'conditions' => [
                            ['template_type', '=', Template::TYPE_INVITE],
                        ],
                        'after' => $this->getMergeTagInfo(
                            'video_call_details:' . __('if this is a video call interview'),
                            false,
                            ['event_title', 'event_start', 'participant_list', 'participant_list_with_user_titles', 'participant_list_with_phones', 'meeting_room', 'position_display_name'],
                            ['consent_renewal_url'],
                        ),
                    ],
                    ...$candidateMessageFormField,
                    ...$userMessageFormField,
                    ...$candidateMessageSurveyField,
                ],
            ],
        ];
    }

    public function before()
    {
        $type = $this->data['template_type'];

        $bodyFields = [
            'message_body' => Template::TYPE_CANDIDATE_MESSAGE,
            'event_set_body' => Template::TYPE_EVENT_SET,
            'invite_body' => Template::TYPE_INVITE,
            'video_interview_body' => Template::TYPE_VIDEO_INTERVIEW,
            'video_message_body' => Template::TYPE_VIDEO_MESSAGE,
            'consent_message_body' => Template::TYPE_CONSENT_RENEWAL,
            'reference_body' => Template::TYPE_REFERENCE,
            'sms_body' => Template::TYPE_SMS,
            'user_message_body' => Template::TYPE_USER_MESSAGE,
        ];

        foreach ($bodyFields as $field => $templateType) {
            if ($type == $templateType) {
                $this->data['body'] = $this->data[$field];
            }
            unset($this->elements->children[$field]);
        }

        if ($type == Template::TYPE_USER_MESSAGE && !empty($this->data['data']['user_form_id'])) {
            $newData = [
                ...$this->data['data'],
                'form_id' => data_get($this->data, 'data.user_form_id'),
            ];
            unset($newData['user_form_id']);

            $this->data['data'] = $newData;
            unset($this->elements->children['data']->children['user_form_id']);
        }
    }
}
