<?php

namespace App\Forms\Admin;

use App\Helpers;
use App\Models\Website;
use Laraform\Laraform;

class FeaturesForm extends Laraform
{
    public $model = Website::class;

    public $component = 'base-form ref="bform"';

    public $endpoint = '/admin/website/updateFeatures';

    public function schema()
    {
        return [
            'id' => [
                'type' => 'key',
            ],
            'features' => [
                'type' => 'object',
                'columns' => [
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => [
                    'info_standard_features' => [
                        'type' => 'heading',
                        'content' => 'Standard features',
                        'heading' => 'h3',
                        'persist' => false,
                        'description' => 'These features are included in all plans',
                    ],
                    Website::FEATURE_ENABLE_AI => [
                        'type' => 'toggle',
                        'label' => 'Enable AI',
                        'description' => 'This adds subprocessor: OpenAI, L.L.C at 3180 18th Street, San Francisco, CA, United States.',
                    ],
                    Website::FEATURE_EDIT_ALL_COMMENTS => [
                        'type' => 'toggle',
                        'label' => 'Allow all users to edit all comments',
                    ],
                    Website::FEATURE_KEY_LT_MERGE_TAGS => [
                        'type' => 'toggle',
                        'label' => 'LT merge tags',
                    ],
                    Website::FEATURE_KEY_LV_MERGE_TAGS => [
                        'type' => 'toggle',
                        'label' => 'LV merge tags',
                    ],
                    Website::FEATURE_LANG_RU => [
                        'type' => 'toggle',
                        'label' => 'Russian language',
                    ],
                    'info_paid_addons' => [
                        'type' => 'heading',
                        'content' => 'Paid Add-ons',
                        'heading' => 'h3',
                        'persist' => false,
                    ],
                    Website::FEATURE_KEY_VIDEO_INTERVIEWS => [
                        'type' => 'toggle',
                        'label' => 'Async video interviews',
                    ],
                    Website::FEATURE_KEY_REQUISITIONS => [
                        'type' => 'toggle',
                        'label' => 'Requisitions',
                    ],
                    Website::FEATURE_AI_MEETING_ANALYSIS => [
                        'type' => 'toggle',
                        'label' => 'AI Transcriptions',
                        'description' => 'Allows users to get transcripts of video calls with AI-generated summaries.',
                    ],
                    Website::FEATURE_AI_CANDIDATE_SCREENING => [
                        'type' => 'toggle',
                        'label' => 'AI Candidate Screening',
                        'description' => 'Allows users to get AI-generated scores for candidates based on their resumes and application data.',
                    ],
                    Website::FEATURE_SCORECARDS => [
                        'type' => 'toggle',
                        'label' => 'Scorecards',
                    ],
                    Website::FEATURE_REFERENCES => [
                        'type' => 'toggle',
                        'label' => 'References',
                    ],
                    Website::FEATURE_CREDENTIAL_VALIDITY => [
                        'type' => 'toggle',
                        'label' => 'Credential validity',
                    ],
                    Website::FEATURE_KEY_TEAMS => [
                        'type' => 'toggle',
                        'label' => 'Enable team-based access controls',
                    ],
                    Website::FEATURE_CAREER_PAGES => [
                        'type' => 'toggle',
                        'label' => 'Enable career pages',
                    ],
                    Website::FEATURE_FAVICON => [
                        'type' => 'toggle',
                        'label' => 'Enable custom favicon',
                    ],
                    'info_enterprise_features' => [
                        'type' => 'heading',
                        'content' => 'Advanced features',
                        'heading' => 'h3',
                        'persist' => false,
                        'description' => 'These features are sometimes included in enterprise plans',
                    ],
                    Website::FEATURE_KEY_SSO => [
                        'type' => 'toggle',
                        'label' => 'SSO',
                    ],
                    Website::FEATURE_KEY_SCIM => [
                        'type' => 'toggle',
                        'label' => 'SCIM',
                    ],
                    Website::FEATURE_JOB_ADS_IP_LIMIT => [
                        'type' => 'toggle',
                        'label' => 'Internal network job ads',
                    ],
                    Website::FEATURE_AUDIT_LOG => [
                        'type' => 'toggle',
                        'label' => 'Audit log',
                    ],
                    Website::FEATURE_DUMP_DATABASE => [
                        'type' => 'toggle',
                        'label' => 'Database dump API',
                    ],
                    Website::FEATURE_WEBHOOK_ACTIONS => [
                        'type' => 'toggle',
                        'label' => 'Enable webhook-based stage actions',
                    ],
                ],
            ],
            'sector' => [
                'type' => 'select',
                'label' => 'Industry',
                'items' => Website::SECTORS,
            ],
            'country' => [
                'type' => 'select',
                'label' => 'Country',
                'items' => collect(Helpers::getCountries())->mapWithKeys(fn ($country) => [$country['alpha2'] => $country['name']])->toArray(),
            ],
        ];
    }
}
