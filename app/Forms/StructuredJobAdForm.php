<?php

namespace App\Forms;

use Alcohol\ISO4217;
use App\DataExchange\ApprenticeshipsGovUk\ApiHelper;
use App\DataExchange\CvKeskus\Export\Types\JobAd;
use App\DataExchange\LinkedinXml\Types\Job;
use App\DataExchange\StructuredJobs\Interfaces\ControlsFields;
use App\DataExchange\StructuredJobs\Outputs\CvBankasOutput;
use App\DataExchange\StructuredJobs\Outputs\CvDotLtOutput;
use App\DataExchange\StructuredJobs\Outputs\CvEeOutput;
use App\DataExchange\StructuredJobs\Outputs\CVLibraryOutput;
use App\DataExchange\StructuredJobs\Outputs\DuunitoriOutput;
use App\DataExchange\StructuredJobs\Outputs\SsLvOutput;
use App\DataExchange\TalentComXml\Types\Job as JobTalentCom;
use App\DataExchange\TotaljobsXml\Enums\JobType;
use App\DataExchange\TotaljobsXml\Enums\PositionType;
use App\DataExchange\TotaljobsXml\Enums\Region;
use App\DataExchange\TotaljobsXml\Enums\SalaryRangeAnnual;
use App\DataExchange\TotaljobsXml\Enums\SalaryRangeDaily;
use App\DataExchange\TotaljobsXml\Enums\SalaryRangeHourly;
use App\DataExchange\TotaljobsXml\Enums\SalaryRate;
use App\DataExchange\TotaljobsXml\Enums\SectorType;
use App\DataExchange\TotaljobsXml\Enums\Town;
use App\Helpers;
use App\Models\File;
use App\Models\Integration;
use App\Models\IntegrationStructuredJobAd;
use App\Models\Landing;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Stage;
use App\Models\StructuredJobAd;
use App\Models\Website;
use App\Services\AI\Forms\StructuredJobAdFiller;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Fluent;
use Laraform\Laraform;
use Laraform\Support\Arr;
use Segment\Segment;

class StructuredJobAdForm extends Laraform
{
    public $model = StructuredJobAd::class;

    public $endpoint = '/laraform/process';

    public $component = 'base-form ref="bform"';

    public $validateOn = 'step|submit';

    public array $previousFormStoreKeys = ['deadline_at'];
    public array $previousFormValues = [];

    // TODO: REFACTOR: use constants for field names - more usable in outputs.

    const SETTINGS_INPUTS = [
        Setting::KEY_ORGANIZATION_NAME,
        Setting::KEY_ORGANIZATION_CITY,
        Setting::KEY_ORGANIZATION_STREET_ADDRESS,
        Setting::KEY_ORGANIZATION_WEBSITE_URL,
    ];

    public function wizard()
    {
        $showSetup = (bool)Setting::get(Setting::KEY_ORGANIZATION_NAME);

        return [
            ...($showSetup ? [] : [
                'settings_setup' => [
                    'label' => __('Setup'),
                    'buttons' => [
                        'previous' => false,
                    ],
                    'elements' => self::SETTINGS_INPUTS,
                ],
            ]),
            'choose_outputs' => [
                'label' => __('Channels'),
                'buttons' => [
                    'previous' => !$showSetup,
                ],
                'elements' => [
                    'id',
                    'project_id',
                    StructuredJobAd::KEY_PUBLISH_SOCIAL_MEDIA,
                    StructuredJobAd::KEY_INTEGRATIONS,
                    StructuredJobAd::KEY_COMBINATION_ERROR,
                ],
                'labels' => [
                    'previous' => __('Back'),
                ],
            ],
            'social_media' => [
                'label' => __('Social media'),
                'elements' => [
                    StructuredJobAd::KEY_SOCIAL_MEDIA_PARAMS,
                ],
                'conditions' => [
                    [StructuredJobAd::KEY_PUBLISH_SOCIAL_MEDIA, 1],
                ],
            ],
            'creatives' => [
                'label' => __('Creatives'),
                'elements' => [
                    StructuredJobAd::KEY_CREATIVE_TYPE,
                    StructuredJobAd::KEY_LANDING_ID,
                    StructuredJobAd::KEY_CUSTOM_JOB_AD_URL,
                    StructuredJobAd::KEY_IMAGE,
                    StructuredJobAd::KEY_EMOTIONIMAGES,
                    StructuredJobAd::KEY_BANNERIMAGE,
                    StructuredJobAd::KEY_LANGUAGE,
                    StructuredJobAd::KEY_FILL_TEXT_FROM_LANDING,
                    StructuredJobAd::KEY_CVB_LABEL_DESCRIPTION,
                    StructuredJobAd::KEY_DESCRIPTION,
                    StructuredJobAd::KEY_CVB_LABEL_REQUIREMENTS,
                    StructuredJobAd::KEY_REQUIREMENTS,
                    StructuredJobAd::KEY_CVB_LABEL_OFFERS,
                    StructuredJobAd::KEY_WE_OFFER,
                    StructuredJobAd::KEY_OUTCOME_DESCRIPTION,
                    StructuredJobAd::KEY_TRAINING_DESCRIPTION,
                    StructuredJobAd::KEY_HTML_USE_OVERRIDE,
                    StructuredJobAd::KEY_HTML_PREVIEW,
                    StructuredJobAd::KEY_HTML_OVERRIDE,
                ],
            ],
            'job_info' => [
                'label' => __('Job info'),
                'labels' => [
                    'previous' => __('Back'),
                    'finish' => __('Publish'),
                ],
                'elements' => [
                    StructuredJobAd::KEY_DUUNITORI_CAMPAIGN_TYPE,
                    StructuredJobAd::KEY_A13N_ENTITY_ID,
                    StructuredJobAd::KEY_LARS_CODE,
                    StructuredJobAd::KEY_UKPRN,
                    StructuredJobAd::KEY_POSITION_NAME,
                    StructuredJobAd::KEY_START_AT,
                    StructuredJobAd::KEY_DEADLINE_AT,
                    StructuredJobAd::KEY_DURATION_MONTHS,
                    StructuredJobAd::KEY_A13S_WEEKLY_HOURS,
                    StructuredJobAd::KEY_A13S_WEEK_DESCRIPTION,
                    StructuredJobAd::KEY_CVO_USE_PORTAL_APPLY,
                    StructuredJobAd::KEY_TARGET_STAGE_ID,
                    StructuredJobAd::KEY_WORK_TIMES,
                    StructuredJobAd::KEY_DUUNITORI_EMPLOYMENT_TYPE,
                    StructuredJobAd::KEY_LOCATION,
                    StructuredJobAd::KEY_NUMBER_OF_POSITIONS,
                    StructuredJobAd::KEY_CVO_CATEGORIES,
                    StructuredJobAd::KEY_CONTRACT_TYPE,
                    StructuredJobAd::KEY_CVK_WORK_TIMES,
                    StructuredJobAd::KEY_CVK_CATEGORIES,
                    StructuredJobAd::KEY_CVK_LEVEL,
                    StructuredJobAd::KEY_CVK_COUNTRY,
                    StructuredJobAd::KEY_CVK_COUNTY,
                    StructuredJobAd::KEY_CVK_CITY,
                    StructuredJobAd::KEY_CVL_TYPES,
                    StructuredJobAd::KEY_CVL_INDUSTRY,
                    StructuredJobAd::KEY_CVL_ADDITIONAL_INDUSTRIES,
                    StructuredJobAd::KEY_CVL_REGION,
                    StructuredJobAd::KEY_CVL_PRECISE_LOCATION,
                    StructuredJobAd::KEY_CVL_DISPLAY_LOCATION,
                    StructuredJobAd::KEY_CVL_RELOCATE_FROM_EU,
                    StructuredJobAd::KEY_CV_LT_CITIES,
                    StructuredJobAd::KEY_CV_LT_DEPARTMENTS,
                    StructuredJobAd::KEY_CV_LT_POSITION_TYPE,
                    StructuredJobAd::KEY_CV_LT_WORK_TIME,
                    StructuredJobAd::KEY_CVB_LANGUAGE,
                    StructuredJobAd::KEY_CVB_CITY,
                    StructuredJobAd::KEY_CVB_CLOSE_CITY_1,
                    StructuredJobAd::KEY_CVB_CLOSE_CITY_2,
                    StructuredJobAd::KEY_CVB_CATEGORY,
                    StructuredJobAd::KEY_CVB_GROUP,
                    StructuredJobAd::KEY_CVB_ARE_UKRAINIANS_WELCOME,
                    StructuredJobAd::KEY_MAIN_JOB_TYPE,
                    StructuredJobAd::KEY_LI_WORKPLACE_TYPE,
                    StructuredJobAd::KEY_LI_INDUSTRY_CODES,
                    StructuredJobAd::KEY_LI_EXPERIENCE_LEVEL,
                    StructuredJobAd::KEY_LI_JOB_FUNCTIONS,
                    StructuredJobAd::KEY_CONTACT_INFO,
                    StructuredJobAd::KEY_CONTACT_NAME,
                    StructuredJobAd::KEY_CONTACT_EMAIL,
                    StructuredJobAd::KEY_CONTACT_PHONE,
                    StructuredJobAd::KEY_OPTIONAL_INFO,
                    StructuredJobAd::KEY_SCREENING_QUESTIONS,
                    StructuredJobAd::KEY_CVO_LOCATION,
                    StructuredJobAd::KEY_REQUIRED_LANGUAGES,
                    StructuredJobAd::KEY_SALARY_FROM,
                    StructuredJobAd::KEY_SALARY_TO,
                    StructuredJobAd::KEY_SALARY_CURRENCY,
                    StructuredJobAd::KEY_SALARY_PERIOD,
                    StructuredJobAd::KEY_SALARY_INFO,
                    StructuredJobAd::KEY_TOTALJOBS_REGION,
                    StructuredJobAd::KEY_TOTALJOBS_TOWN,
                    StructuredJobAd::KEY_POSTCODE,
                    StructuredJobAd::KEY_TOTALJOBS_JOB_TYPE,
                    StructuredJobAd::KEY_TOTALJOBS_POSITION_TYPE,
                    StructuredJobAd::KEY_TOTALJOBS_SECTOR_TYPE,
                    StructuredJobAd::KEY_TOTALJOBS_SALARY_RATE,
                    StructuredJobAd::KEY_TOTALJOBS_SALARY_RANGE,
                    StructuredJobAd::KEY_SS_LV_CATEGORY,
                    StructuredJobAd::KEY_SS_LV_EDUCATION,
                    StructuredJobAd::KEY_SS_LV_LANGUAGES,
                    StructuredJobAd::KEY_SS_LV_WORKING_DAYS,
                    StructuredJobAd::KEY_SS_LV_WORKING_HOURS,
                    StructuredJobAd::KEY_SS_LV_ADDRESS,
                    StructuredJobAd::KEY_SS_LV_LOCATION,
                    StructuredJobAd::KEY_SS_LV_CITY,
                    StructuredJobAd::KEY_PROF_HU_ADVERT_TYPE,
                    StructuredJobAd::KEY_PROF_HU_LOCATION,
                    StructuredJobAd::KEY_PROF_HU_SECTOR,
                    StructuredJobAd::KEY_PROF_HU_CLASSIFICATION,
                    StructuredJobAd::KEY_PROF_HU_EXPERIENCE,
                    StructuredJobAd::KEY_PROF_HU_WORKING_SCHEDULES,
                    StructuredJobAd::KEY_PROF_HU_WORK_SCHEDULE,
                    StructuredJobAd::KEY_PROF_HU_QUALIFICATION,
                    StructuredJobAd::KEY_PROF_HU_DRIVERS_LICENSE,
                    StructuredJobAd::KEY_PROF_HU_LANGS,
                    StructuredJobAd::KEY_IS_DISABILITY_CONFIDENT,
                    StructuredJobAd::KEY_A13S_SKILLS,
                    StructuredJobAd::KEY_A13S_QUALIFICATIONS,
                    StructuredJobAd::KEY_IS_REMOTE,
                    StructuredJobAd::KEY_CVL_HIDE_SALARY,
                    StructuredJobAd::KEY_BENEFITS,
                    StructuredJobAd::KEY_EMPLOYER_INFO,
                    StructuredJobAd::KEY_EMPLOYER_LOGO_LOCATION,
                    StructuredJobAd::KEY_EMPLOYER_VIDEO_URL,
                    StructuredJobAd::KEY_EMPLOYER_WEB_URL,
                    StructuredJobAd::KEY_EMPLOYER_ABOUT,
                    StructuredJobAd::KEY_INDEED_CITY,
                    StructuredJobAd::KEY_INDEED_STATE,
                    StructuredJobAd::KEY_INDEED_COUNTRY,
                    StructuredJobAd::KEY_TALENT_CATEGORY,
                    StructuredJobAd::KEY_INDEED_PUBLISHING_WARNING,
                    StructuredJobAd::KEY_SS_LV_PUBLISHING_WARNING,
                    StructuredJobAd::KEY_CUSTOM_FIELDS,
                    StructuredJobAd::KEY_PROBATION_PERIOD_DAYS,
                    StructuredJobAd::KEY_REFERRAL_BUDGET_PER_HIRE_EUR,
                    StructuredJobAd::KEY_TALENME_REFERRAL_FEE,
                ],
            ],
        ];
    }

    protected function getSystemFields()
    {
        [
            $showCreativeTypeChoice,
            $showImageUpload,
            $showLandingChoice,
            $showError,
        ] = $this->getCreativeFieldsDisplayLogics();

        $arrToItems = function (array $arr, $castKey = null) {
            return collect($arr)->map(function ($name, $key) use ($castKey) {
                if ($castKey) {
                    settype($key, $castKey);
                }

                return [
                    'value' => $key,
                    'label' => $name,
                ];
            })->values()->toArray();
        };

        $stageSelectItems = Stage::query()
            ->whereHas('project', function (Builder $projectQuery) {
                $projectQuery->excludeTemplates()->whereIn('status', [Project::STATUS_IN_PROGRESS, Project::STATUS_DRAFT]);
            })
            ->with('project')
            ->get()
            ->map(fn(Stage $stage) => [
                'value' => $stage->id,
                'label' => "{$stage->project->position_name}: $stage->name (ID: $stage->id)",
                'project_id' => $stage->project_id,
                'stage_name' => $stage->name,
            ])
            ->all();

        $hasDuunitoriIntegrationJsonSchema = [
            'some' => [
                ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                ['in' => [['var' => ''], $this->groupedIntegrationIds[Integration::TYPE_DUUNITORI] ?? []]],
            ],
        ];
        return [
            'id' => [
                'type' => 'key',
            ],
            'project_id' => [
                'type' => 'hidden',
            ],
            StructuredJobAd::KEY_PUBLISH_SOCIAL_MEDIA => [
                'label' => __('Social media'),
                'type' => 'toggle',
                'description' => __('Run an AI-assisted social media campaign for attracting passive talent.'),
            ],
            StructuredJobAd::KEY_SOCIAL_MEDIA_PARAMS => [
                'type' => 'object',
                'columns' => [
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => [
                    'region' => [
                        'type' => 'select',
                        'label' => __('Region'),
                        'items' => [
                            'uki' => __('UK & Ireland'),
                            'baltics' => __('Baltics'),
                            'scandinavia' => __('Scandinavia'),
                            'eu-west' => __('North & Western Europe'),
                            'eu-east' => __('Eastern Europe'),
                            'eu-central' => __('Central Europe'),
                        ],
                        'description' => __('Needed for reach estimation'),
                        'default' => 'uki',
                    ],
                    'budget' => [
                        'label' => __('Daily budget (€)'),
                        'type' => 'slider',
                        'default' => 50,
                        'min' => 30,
                        'max' => 80,
                        'options' => [
                            'interval' => 5,
                            'tooltipFormatter' => '{value}€',
                        ],
                    ],
                    'days' => [
                        'label' => __('Number of days'),
                        'type' => 'slider',
                        'default' => 15,
                        'min' => 13,
                        'max' => 21,
                        'options' => [
                            'interval' => 1,
                        ],
                    ],
                    'start_up_fee' => [
                        'type' => 'hidden',
                        'default' => 300,
                    ],
                    'instance_name' => [
                        'type' => 'hidden',
                        'default' => Helpers::getCurrentWebsite()->uuid,
                    ],
                    'user_email' => [
                        'type' => 'hidden',
                        'default' => auth()->user()->email,
                    ],
                    'referer' => [
                        'type' => 'hidden',
                        'default' => request()->headers->get('referer'),
                    ],
                    'estimate' => [
                        'label' => __('Reach'),
                        'type' => 'reach',
                    ],
                    'needs_invoice' => [
                        'label' => __('I need an invoice'),
                        'type' => 'toggle',
                    ],
                    'company_name' => [
                        'type' => 'text',
                        'label' => __('Company name'),
                        'conditions' => [
                            [StructuredJobAd::KEY_SOCIAL_MEDIA_PARAMS . '.needs_invoice', true],
                        ],
                    ],
                    'company_address' => [
                        'type' => 'text',
                        'label' => __('Billing address'),
                        'conditions' => [
                            [StructuredJobAd::KEY_SOCIAL_MEDIA_PARAMS . '.needs_invoice', true],
                        ],
                    ],
                    'info' => [
                        'type' => 'static',
                        'content' => Helpers::getStructuredJobAdSocialMediaDescription(),
                    ],
                ],
            ],
            StructuredJobAd::KEY_INTEGRATIONS => [
                'type' => 'togglegroup',
                'items' => Integration::query()
                    ->active()
                    ->whereIn('remote_type', array_keys(Integration::INTEGRATION_OUTPUT_MAP))
                    ->get()
                    ->mapWithKeys(function (Integration $integration) {
                        $type = Integration::getTypes()[$integration->remote_type];

                        return [$integration->id => $integration->name . ($integration->feed_name ? " ({$integration->feed_name})" : '') . " ($type)"];
                    }),
                'columns' => [
                    'label' => 0,
                    'field' => 12,
                ],
                'default' => [],
                'cast_values_int' => true,
            ],
            StructuredJobAd::KEY_COMBINATION_ERROR => [
                'type' => 'static',
                'content' => '<div class="alert alert-danger">' . __('Publishing to this combination of channels is currently impossible.') . '</div>',
                'json_logic' => $showError,
            ],
            StructuredJobAd::KEY_TARGET_STAGE_ID => [
                'label' => __('Applicants target stage'),
                'items' => $stageSelectItems,
                'type' => 'select',
                'rules' => 'required',
            ],
            StructuredJobAd::KEY_POSITION_NAME => [
                'type' => 'text',
                'label' => __('Position name'),
            ],
            StructuredJobAd::KEY_LOCATION => [
                'type' => 'text',
                'label' => __('Location'),
                'description' => __('Displayed on career page preview.') . (Integration::whereRemoteType(Integration::TYPE_LINKEDIN)->exists() ? ' ' . __('When publishing to LinkedIn, use City, Country format (e.g. "London, United Kingdom"). For remote positions, use only country.') : ''),
            ],
            StructuredJobAd::KEY_START_AT => [
                'type' => 'customdate',
                'label' => __('Start date'),
                'json_logic_max' => [
                    'if' => [
                        $hasDuunitoriIntegrationJsonSchema,
                        ['var' => StructuredJobAd::KEY_DEADLINE_AT],
                        null
                    ],
                ],
                'json_logic_min' => [
                    'if' => [
                        $hasDuunitoriIntegrationJsonSchema,
                        now()->format('Y-m-d'),
                        null
                    ],
                ],
                'rules' => [
                    'backend' => [
                        'nullable',
                        [
                            'after_or_equal:today' => fn (Fluent $data) => collect($data->get(StructuredJobAd::KEY_INTEGRATIONS))
                                ->intersect($this->groupedIntegrationIds[Integration::TYPE_DUUNITORI])
                                ->isNotEmpty(),
                            'before_or_equal:' . StructuredJobAd::KEY_DEADLINE_AT => function (Fluent $data) {
                                return $data->get(StructuredJobAd::KEY_DEADLINE_AT)
                                    && $data->get(StructuredJobAd::KEY_START_AT)
                                    && collect($data->get(StructuredJobAd::KEY_INTEGRATIONS))
                                        ->intersect($this->groupedIntegrationIds[Integration::TYPE_DUUNITORI])
                                        ->isNotEmpty();
                            },
                        ]],
                ],
            ],
            StructuredJobAd::KEY_DEADLINE_AT => [
                'id' => 'deadline_at',
                'type' => 'customdate',
                'label' => __('Application deadline'),
                'default_if_new' => now()->addDays(30)->format('Y-m-d'),
                'json_logic_min' => [
                    'if' => [
                        $hasDuunitoriIntegrationJsonSchema,
                        now()->format('Y-m-d'),
                        null
                    ]
                ],
                'json_logic_max' => [
                    'if' => [
                        // if Totaljobs or Relailchoice integration is chosen
                        ['some' => [
                            ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                            ['in' => [['var' => ''], array_merge(
                                $this->groupedIntegrationIds[Integration::TYPE_TOTALJOBS] ?? [],
                                $this->groupedIntegrationIds[Integration::TYPE_RETAILCHOICE] ?? [],
                            )]],
                        ]],
                        // then maximum date is 4 weeks from now
                        now()->addDays(28)->format('Y-m-d'),
                        // else if Duunitori integration is chosen and start_at is set
                        [
                            'and' => [
                                ['var' => StructuredJobAd::KEY_START_AT],
                                $hasDuunitoriIntegrationJsonSchema,
                            ],
                        ],
                        // then maximum date is start_at + 30 days
                        ['add_days' => [['var' => StructuredJobAd::KEY_START_AT], 30]],
                        // else maximum date is 30 days from now
                        now()->addDays(30)->format('Y-m-d'),
                    ],
                ],
                'rules' => [
                    'backend' => [
                        'nullable',
                        [
                            'after_or_equal:today' => function (Fluent $data) {
                                return collect($data->get(StructuredJobAd::KEY_INTEGRATIONS))
                                    ->intersect($this->groupedIntegrationIds[Integration::TYPE_DUUNITORI])
                                    ->isNotEmpty();
                            },
                            'after_or_equal:' . StructuredJobAd::KEY_START_AT => function (Fluent $data) {
                                return $data->get(StructuredJobAd::KEY_START_AT)
                                    && $data->get(StructuredJobAd::KEY_DEADLINE_AT)
                                    && collect($data->get(StructuredJobAd::KEY_INTEGRATIONS))
                                        ->intersect($this->groupedIntegrationIds[Integration::TYPE_DUUNITORI])
                                        ->isNotEmpty();
                            },
                        ]],
                ],
            ],
            'unpublished_at' => [
                'type' => 'meta',
                'default' => null,
            ],
            StructuredJobAd::KEY_CREATIVE_TYPE => [
                'persist' => false,
                'type' => 'radiogroup',
                'label' => __('Creative type'),
                'items' => [
                    'landing' => __('Landing page'),
                    'image' => __('Image'),
                ],
                'default' => 'landing',
                'json_logic' => $showCreativeTypeChoice,
            ],
            StructuredJobAd::KEY_LANDING_ID => [
                'type' => 'select',
                'label' => __('Landing page'),
                'items' => Landing::query()
                    ->whereIn('type',
                        [Landing::TYPE_V1, Landing::TYPE_TALLINK, Landing::TYPE_RADEMAR, Landing::TYPE_V3])
                    ->where('status', Landing::STATUS_ACTIVE)
                    ->get()
                    ->mapWithKeys(function (Landing $landing) {
                        return [$landing->id => ($landing->display_name) . " (ID: $landing->id)"];
                    })->prepend(__('Custom URL'), 'custom')->reverse()->toArray(),
                'rules' => 'required',
                'json_logic' => $showLandingChoice,
                'description' => __('If you don\'t have a landing page for this job yet, create it') . ' ' . '<a href="/landings/v2/create">' . __('here') . '</a>.',
            ],
            StructuredJobAd::KEY_IMAGE => [
                'type' => 'object',
                'json_logic' => $showImageUpload,
                'columns' => [
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => [
                    'type' => [
                        'type' => 'hidden',
                        'default' => File::STRUCTURED_AD_IMAGE,
                    ],
                    'location' => [
                        'type' => 'file',
                        'label' => __('Job ad image'),
                        'folder' => 'uploads',
                        'disk' => 'public',
                        'url' => preg_replace('/\/$/', '', Storage::url('')),
                        'store' => function (UploadedFile $file, File $entity) {
                            $location = File::store($file);
                            $entity->update(['location' => $location]);

                            return [
                                'location' => $location,
                            ];
                        },
                        'description' => __('If you don\'t have an ad for this job yet, create it') . ' ' . '<a href="/p/job-builder">' . __('here') . '</a>.',
                    ],
                ],
            ],
            StructuredJobAd::KEY_EMOTIONIMAGES => [
                'type' => 'gallery',
                'view' => 'list',
                'label' => __('Images'),
                'description' => __('These images will be added to the job posting. You can drag and drop to change the order.'),
                'drop' => true,
                'sort' => true,
                'folder' => 'uploads',
                'disk' => 'public',
                'url' => preg_replace('/\/$/', '', Storage::url('')),
                'store' => function (UploadedFile $file, File $entity) {
                    \Log::info('store file', ['uploadedfile' => $file, 'entity' => $entity]);
                    $location = File::store($file);
                    \Log::info('store file location', ['location' => $location]);
                    $entity->update(['location' => $location]);

                    return [
                        'location' => $location,
                        'type' => $entity->type,
                        'sort_order' => $entity->sort_order,
                    ];
                },
                'storeFile' => 'location',
                'storeOrder' => 'sort_order',
                'fields' => [
                    'type' => [
                        'type' => 'hidden',
                        'columns' => [
                            'field' => 12,
                            'label' => 0,
                        ],
                        'default' => File::TYPE_STRUCTURED_AD_EMOTION_IMAGE,
                    ],
                    'sort_order' => [
                        'type' => 'meta',
                    ],
                ],
                'autofill_on_load' => false,
                'autofill_url' => route('landings.socialMediaPreview', ['landing' => '---' . StructuredJobAd::KEY_LANDING_ID . '---']),
                'autofill_watch' => [StructuredJobAd::KEY_LANDING_ID, StructuredJobAd::KEY_INTEGRATIONS],
                'autofill_skip' => [StructuredJobAd::KEY_LANDING_ID => ['custom']],
            ],
            StructuredJobAd::KEY_BANNERIMAGE => [
                'type' => 'object',
                'columns' => [
                    'label' => 0,
                    'field' => 12,
                ],
                'schema' => [
                    'type' => [
                        'type' => 'hidden',
                        'default' => File::TYPE_STRUCTURED_AD_BANNER_IMAGE,
                    ],
                    'location' => [
                        'type' => 'file',
                        'label' => __('Banner image'),
                        'description' => __('Banner image for job ad'),
                        'folder' => 'uploads',
                        'disk' => 'public',
                        'url' => preg_replace('/\/$/', '', Storage::url('')),
                        'store' => function (UploadedFile $file, File $entity) {
                            $location = File::store($file);
                            $entity->update(['location' => $location]);

                            return [
                                'location' => $location,
                            ];
                        },
                    ],
                ],
            ],
            StructuredJobAd::KEY_LANGUAGE => [
                'type' => 'select',
                'label' => __('Language'),
                'default' => 'eng',
                'items' => [
                    'eng' => __('English'),
                    'est' => __('Estonian'),
                    'lav' => __('Latvian'),
                    'ltu' => __('Lithuanian'),
                    'rus' => __('Russian'),
                ],
            ],
            StructuredJobAd::KEY_DESCRIPTION => [
                'type' => 'textarea',
                'label' => __('Description'),
                'rules' => [
                    'required',
                    [
                        'not_regex:/@/i' => [StructuredJobAd::KEY_INTEGRATIONS, $this->groupedIntegrationIds[Integration::TYPE_CV_KESKUS_XML] ?? []],
                    ],
                ],
                'messages' => [
                    'not_regex' => __(':attribute cannot contain the @ symbol when publishing to CV Keskus.'),
                ],
            ],
            StructuredJobAd::KEY_REQUIREMENTS => [
                'type' => 'textarea',
                'label' => __('Requirements'),
                'rules' => [
                    'required',
                    [
                        'not_regex:/@/i' => [StructuredJobAd::KEY_INTEGRATIONS, $this->groupedIntegrationIds[Integration::TYPE_CV_KESKUS_XML] ?? []],
                    ],
                ],
                'messages' => [
                    'not_regex' => __(':attribute cannot contain the @ symbol when publishing to CV Keskus.'),
                ],
            ],
            StructuredJobAd::KEY_WE_OFFER => [
                'type' => 'textarea',
                'label' => __('We offer'),
                'rules' => [
                    'required',
                    [
                        'not_regex:/@/i' => [StructuredJobAd::KEY_INTEGRATIONS, $this->groupedIntegrationIds[Integration::TYPE_CV_KESKUS_XML] ?? []],
                    ],
                ],
                'messages' => [
                    'not_regex' => __(':attribute cannot contain the @ symbol when publishing to CV Keskus.'),
                ],
            ],
            StructuredJobAd::KEY_FILL_TEXT_FROM_LANDING => [
                'type' => 'aifiller',
                'persist' => false,
                'label' => __('Fill text fields from landing'),
                'fills' => [StructuredJobAd::KEY_REQUIREMENTS, StructuredJobAd::KEY_WE_OFFER, StructuredJobAd::KEY_DESCRIPTION],
                'filler' => [StructuredJobAdFiller::class, 'threeContentFields'],
            ],
            StructuredJobAd::KEY_CUSTOM_JOB_AD_URL => [
                'type' => 'text',
                'label' => __('Landing page URL'),
                'rules' => 'required|url',
                'conditions' => [
                    [StructuredJobAd::KEY_LANDING_ID, 'custom'],
                ],
            ],
            StructuredJobAd::KEY_WORK_TIMES => [
                'type' => 'tags',
                'label' => __('Work times'),
                'items' => [
                    'PART_TIME' => __('Part time'),
                    'FULL_TIME' => __('Full time'),
                    'PRACTICE' => __('Internship'),
                    'WORK_AFTER_CLASSES' => __('After school'),
                    'FREELANCE' => __('Freelance'),
                    'FIXED_TERM' => __('Fixed term'),
                    'FULL_TIME_WITH_SHIFTS' => __('Full time with shifts'),
                ],
                'rules' => 'min:1',
                'default' => ['FULL_TIME'],
            ],
            StructuredJobAd::KEY_CONTRACT_TYPE => [
                'type' => 'select',
                'label' => __('Contract type'),
                'items' => StructuredJobAd::getContractTypes(),
                'rules' => 'required',
                'default' => StructuredJobAd::CONTRACT_PERMANENT,
            ],
            StructuredJobAd::KEY_CVK_WORK_TIMES => [
                'type' => 'tags',
                'label' => __('Work times (CVK/CVM)'),
                'items' => [
                    'fulltime' => __('Full time'),
                    'part-time' => __('Part time'),
                    'workInShifts' => __('Work in shifts'),
                ],
                'rules' => [
                    'min:1',
                ],
            ],
            StructuredJobAd::KEY_CVK_CATEGORIES => [
                'type' => 'tags',
                'label' => __('Categories (CVK/CVM)'),
                'items' => $arrToItems(JobAd::getCategories()),
                'rules' => 'min:1',
            ],
            StructuredJobAd::KEY_CVK_LEVEL => [
                'type' => 'selectwithdescription',
                'label' => __('Job level (CVK/CVM)'),
                'items' => JobAd::getLevels(),
                'rules' => 'required',
            ],
            StructuredJobAd::KEY_CONTACT_INFO => [
                'type' => 'static',
                'content' => "<hr><div class='field-name-text'>" . __('Contact info') . '</div>',
                'persist' => false,
                'columns' => [
                    'field' => 12,
                    'label' => 0,
                ],
            ],
            StructuredJobAd::KEY_CONTACT_NAME => [
                'type' => 'text',
                'label' => __('Contact name'),
                'default' => auth()->user()->name,
                'rules' => 'required',
            ],
            StructuredJobAd::KEY_CONTACT_EMAIL => [
                'type' => 'text',
                'label' => __('Contact email'),
                'default' => auth()->user()->email,
                'rules' => 'required',
            ],
            StructuredJobAd::KEY_CONTACT_PHONE => [
                'type' => 'text',
                'label' => __('Contact phone'),
                'default' => auth()->user()->phone,
            ],
            StructuredJobAd::KEY_OPTIONAL_INFO => [
                'type' => 'static',
                'content' => "<hr><div class='field-name-text'>" . __('Optional info') . '</div>',
                'persist' => false,
                'columns' => [
                    'field' => 12,
                    'label' => 0,
                ],
            ],
            StructuredJobAd::KEY_CVO_LOCATION => [
                'type' => 'cvolocation',
                'label' => __('Location (CVO)'),
            ],
            StructuredJobAd::KEY_CVO_CATEGORIES => [
                'type' => 'tags',
                'label' => __('Categories (CVO)'),
                'items' => $arrToItems(CvEeOutput::getCVOCategories()),
                'rules' => 'min:1|max:3',
            ],
            StructuredJobAd::KEY_REQUIRED_LANGUAGES => [
                'label' => __('Required languages'),
                'type' => 'tags',
                'items' => collect(CvEeOutput::getISO6391Langs())->map(fn($name, $key) => [
                    'value' => $key,
                    'label' => $name,
                ])->values()->toArray(),
            ],
            StructuredJobAd::KEY_SALARY_FROM => [
                'type' => 'text',
                'rules' => 'numeric|nullable',
                'label' => __('Salary from (gross)'),
                'columns' => [
                    'field' => 4,
                ],
            ],
            StructuredJobAd::KEY_SALARY_TO => [
                'type' => 'text',
                'rules' => 'numeric|nullable',
                'label' => __('Salary to (gross)'),
                'columns' => [
                    'field' => 4,
                ],
            ],
            StructuredJobAd::KEY_SALARY_CURRENCY => [
                'type' => 'select',
                'label' => __('Salary currency'),
                'default' => 'EUR',
                'items' => self::getCurrencyOptions(),
                'columns' => [
                    'field' => 4,
                ],
            ],
            StructuredJobAd::KEY_SALARY_PERIOD => [
                'type' => 'select',
                'label' => __('Salary period'),
                'default' => 'MONTHLY',
                'items' => [
                    'HOURLY' => __('hourly'),
                    'MONTHLY' => __('monthly'),
                ],
                // Annual option is shown when ALL selected integrations belong to the listed integration types that support annual salaries.
                'json_logic_items' => [
                    'if' => [
                        [
                            'all' => [
                                ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                                [
                                    'in' => [
                                        ['var' => ''],
                                        array_merge(
                                            $this->groupedIntegrationIds[Integration::TYPE_INDEED] ?? [],
                                            $this->groupedIntegrationIds[Integration::TYPE_TALENT_COM] ?? [],
                                            $this->groupedIntegrationIds[Integration::TYPE_CV_LIBRARY] ?? [],
                                            $this->groupedIntegrationIds[Integration::TYPE_LINKEDIN] ?? [],
                                            $this->groupedIntegrationIds[Integration::TYPE_DEFAULT_FEED] ?? [],
                                        ),
                                    ],
                                ],
                            ],
                        ],
                        [
                            ['value' => 'HOURLY', 'label' => __('hourly')],
                            ['value' => 'MONTHLY', 'label' => __('monthly')],
                            ['value' => 'ANNUALLY', 'label' => __('annually')],
                        ],
                        [
                            ['value' => 'HOURLY', 'label' => __('hourly')],
                            ['value' => 'MONTHLY', 'label' => __('monthly')],
                        ],
                    ],
                ],
                'columns' => [
                    'field' => 4,
                ],
            ],
            StructuredJobAd::KEY_SALARY_INFO => [
                'type' => 'textarea',
                'label' => __('Salary info'),
            ],
            StructuredJobAd::KEY_TOTALJOBS_SALARY_RATE => [
                'type' => 'select',
                'label' => __('Salary rate'),
                'items' => SalaryRate::getFormItems(),
                'default' => SalaryRate::ANNUAL->value,
            ],
            StructuredJobAd::KEY_TOTALJOBS_SALARY_RANGE => [
                'type' => 'select',
                'label' => __('Salary range'),
                'items' => [],
                'json_logic_items' => [
                    'if' => [
                        ['==' => [['var' => StructuredJobAd::KEY_TOTALJOBS_SALARY_RATE], SalaryRate::ANNUAL->value]],
                        SalaryRangeAnnual::getFormItems(),
                        ['==' => [['var' => StructuredJobAd::KEY_TOTALJOBS_SALARY_RATE], SalaryRate::DAILY->value]],
                        SalaryRangeDaily::getFormItems(),
                        ['==' => [['var' => StructuredJobAd::KEY_TOTALJOBS_SALARY_RATE], SalaryRate::HOURLY->value]],
                        SalaryRangeHourly::getFormItems(),
                    ],
                ],
            ],
            StructuredJobAd::KEY_TOTALJOBS_REGION => [
                'type' => 'select',
                'label' => __('Region'),
                'items' => Region::getFormItems(),
            ],
            StructuredJobAd::KEY_TOTALJOBS_TOWN => [
                'type' => 'select',
                'label' => __('Town'),
                'items' => [],
                'json_logic_items' => [
                    'if' => collect(Region::getRegionTownMapping())->flatMap(function (array $towns, string $region) {
                        return [
                            ['==' => [['var' => StructuredJobAd::KEY_TOTALJOBS_REGION], $region]],
                            collect($towns)->map(function (Town $town) {
                                return ['value' => $town->value, 'label' => $town->value];
                            }),
                        ];
                    })->toArray(),
                ],
            ],
            StructuredJobAd::KEY_TOTALJOBS_JOB_TYPE => [
                'type' => 'select',
                'label' => __('Job type'),
                'items' => JobType::getFormItems(),
            ],
            StructuredJobAd::KEY_TOTALJOBS_POSITION_TYPE => [
                'type' => 'select',
                'label' => __('Position type'),
                'items' => PositionType::getFormItems(),
            ],
            StructuredJobAd::KEY_TOTALJOBS_SECTOR_TYPE => [
                'type' => 'select',
                'label' => __('Sector type'),
                'items' => SectorType::getFormItems(),
            ],
            StructuredJobAd::KEY_CVL_HIDE_SALARY => [
                'type' => 'toggle',
                'label' => __('Hide salary'),
                'trueValue' => 1,
                'falseValue' => 0,
            ],
            StructuredJobAd::KEY_IS_REMOTE => [
                'type' => 'toggle',
                'label' => __('Is remote?'),
            ],
            StructuredJobAd::KEY_BENEFITS => [
                'type' => 'textarea',
                'label' => __('Benefits (CVO)'),
                'rules' => 'max:100',
            ],
            StructuredJobAd::KEY_EMPLOYER_INFO => [
                'type' => 'static',
                'content' => "<hr><div class='field-name-text'>" . __('Employer info') . '</div>',
                'persist' => false,
                'columns' => [
                    'field' => 12,
                    'label' => 0,
                ],
            ],
            StructuredJobAd::KEY_EMPLOYER_LOGO_LOCATION => [
                'type' => 'file',
                'label' => __('Organization logo'),
                'folder' => 'uploads',
                'disk' => 'public',
                'url' => preg_replace('/\/$/', '', Storage::url('')),
                'store' => function (UploadedFile $file, $entity) {
                    $location = File::store($file);

                    return [
                        Setting::KEY_ORGANIZATION_LOGO => $location,
                    ];
                },
                'default' => Setting::get(Setting::KEY_ORGANIZATION_LOGO),
            ],
            StructuredJobAd::KEY_EMPLOYER_VIDEO_URL => [
                'type' => 'text',
                'rules' => 'url|nullable',
                'label' => __('Employer brand video URL'),
                'default' => Setting::get(Setting::KEY_BRAND_VIDEO_URL),
                'description' => __('Only YouTube links are supported.'),
            ],
            StructuredJobAd::KEY_EMPLOYER_WEB_URL => [
                'type' => 'text',
                'rules' => 'url|nullable',
                'label' => __('Employer web URL'),
                'default' => Helpers::toValidUrl(Setting::get(Setting::KEY_ORGANIZATION_WEBSITE_URL)),
            ],
            StructuredJobAd::KEY_EMPLOYER_ABOUT => [
                'type' => 'textarea',
                'rules' => 'max:5000',
                'label' => __('About employer'),
                'default' => Setting::get(Setting::KEY_BRAND_ABOUT),
            ],
            StructuredJobAd::KEY_CVK_COUNTRY => [
                'type' => 'select',
                'rules' => 'required',
                'label' => __('Country (CVK/CVM)'),
                'default' => 'Estonia',
                'items' => [
                    'Estonia' => __('Estonia'),
                    'Latvia' => __('Latvia'),
                    'Lithuania' => __('Lithuania'),
                ],
            ],
            StructuredJobAd::KEY_CVK_COUNTY => [
                'type' => 'datalisttext',
                'label' => __('County (CVK/CVM)'),
                'json_logic_items' => [
                    'if' => [
                        ['==' => [['var' => StructuredJobAd::KEY_CVK_COUNTRY], 'Estonia']],
                        Helpers::selfCombine([
                            'Harjumaa',
                            'Hiiumaa',
                            'Ida-Virumaa',
                            'Järvamaa',
                            'Jõgevamaa',
                            'Lääne-Virumaa',
                            'Läänemaa',
                            'Põlvamaa',
                            'Pärnumaa',
                            'Raplamaa',
                            'Saaremaa',
                            'Tartumaa',
                            'Valgamaa',
                            'Viljandimaa',
                            'Võrumaa',
                        ]),
                    ],
                ],
            ],
            StructuredJobAd::KEY_CVK_CITY => [
                'type' => 'datalisttext',
                'label' => __('City (CVK/CVM)'),
                'json_logic_items' => [
                    'if' => [
                        ['==' => [['var' => StructuredJobAd::KEY_CVK_COUNTRY], 'Estonia']],
                        Helpers::selfCombine([
                            'Tallinn',
                            'Tartu',
                            'Pärnu',
                            'Narva',
                            'Rakvere',
                            'Kohtla Järve / Jõhvi',
                            'Jõgeva',
                            'Haapsalu',
                            'Kodukontor',
                        ]),
                        ['==' => [['var' => StructuredJobAd::KEY_CVK_COUNTRY], 'Latvia']],
                        Helpers::selfCombine(['Rīga', 'Daugavpils', 'Jelgava', 'Liepāja', 'Rīgas rajons', 'Aizkraukle', 'Bauska', 'Cēsis', 'Dobele', 'Gulbene', 'Jēkabpils', 'Jūrmala', 'Kuldīga', 'Ogre', 'Preiļi', 'Rēzekne', 'Sigulda', 'Saldus', 'Talsi', 'Tukums', 'Valka', 'Valmiera', 'Kurzeme', 'Latgale', 'Zemgale', 'Ādažu novads', 'Alojas novads', 'Bauskas novads', 'Dobeles novads', 'Grobiņas novads', 'Gulbenes novads', 'Jelgavas novads', 'Kuldīgas novads', 'Ķekavas novads', 'Lielvārdes novads', 'Līvānu novads', 'Madonas novads', 'Mārupes novads', 'Ogres novads', 'Olaines novads', 'Ropažu novads', 'Salaspils novads', 'Saldus novads', 'Saulkrastu novads', 'Siguldas novads', 'Stopiņu novads', 'Tukuma novads', 'Latvija', 'Darbs no mājām']),
                        ['==' => [['var' => StructuredJobAd::KEY_CVK_COUNTRY], 'Lithuania']],
                        Helpers::selfCombine(['Vilnius', 'Kaunas', 'Klaipėda', 'Šiauliai', 'Panevėžys', 'Alytus', 'Akmenė', 'Anykščiai', 'Ariogala', 'Birštonas', 'Biržai', 'Druskininkai', 'Elektrėnai', 'Gargždai', 'Garliava', 'Grigiškės', 'Ignalina', 'Jonava', 'Joniškis', 'Jurbarkas', 'Kaišiadorys', 'Kalvarija', 'Karmėlava', 'Kazlų Rūda', 'Kėdainiai', 'Kelmė', 'Kernavė', 'Krekenava', 'Kretinga', 'Kupiškis', 'Kuršėnai', 'Kybartai', 'Lazdijai', 'Lentvaris', 'Maišiagala', 'Marijampolė', 'Mažeikiai', 'Molėtai', 'Naujoji Akmenė', 'Nemenčinė', 'Nemėžis', 'Neringa', 'Nida', 'Pabradė', 'Pagėgiai', 'Pakruojis', 'Palanga', 'Pasvalys', 'Plungė', 'Prienai', 'Radviliškis', 'Raseiniai', 'Raudondvaris', 'Riešė', 'Rietavas', 'Rokiškis', 'Rudamina', 'Rumšiškės', 'Skuodas', 'Šakiai', 'Šalčininkai', 'Šilalė', 'Šilutė', 'Širvintos', 'Švenčionėliai', 'Švenčionys', 'Tauragė', 'Telšiai', 'Trakai', 'Trakų Vokė', 'Ukmergė', 'Utena', 'Varėna', 'Vievis', 'Vilkaviškis', 'Visaginas', 'Zarasai', 'Žiežmariai', 'Kitur Lietuvoje', 'Visa Lietuva', 'Iš namų']),
                    ],
                ],
            ],

            StructuredJobAd::KEY_CVO_USE_PORTAL_APPLY => [
                'label' => __('Allow direct applications from job portals'),
                'type' => 'toggle',
                'trueValue' => 1,
                'falseValue' => 0,
                'json_logic' => [
                    'and' => [
                        [
                            '!!' => [
                                'filter' => [
                                    ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                                    [
                                        'in' => [
                                            ['var' => ''],
                                            array_merge(
                                                $this->groupedIntegrationIds[Integration::TYPE_CV_EE] ?? [],
                                                $this->groupedIntegrationIds[Integration::TYPE_CV_LV] ?? [],
                                                $this->groupedIntegrationIds[Integration::TYPE_CV_LT] ?? [],
                                                $this->groupedIntegrationIds[Integration::TYPE_INDEED] ?? [],
                                            ),
                                        ],
                                    ],
                                ],
                            ],
                        ],
                        [
                            '!' => [
                                'filter' => [
                                    ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                                    [
                                        'in' => [
                                            ['var' => ''],
                                            $this->groupedIntegrationIds[Integration::TYPE_CV_KESKUS_XML] ?? [],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                        ['==' => [['var' => StructuredJobAd::KEY_CREATIVE_TYPE], 'landing']],
                    ],
                ],
            ],
            StructuredJobAd::KEY_CVL_TYPES => [
                'label' => __('Type (CV-Library)'),
                'type' => 'tags',
                'items' => Helpers::assocToOptions(CVLibraryOutput::getJobTypes()),
            ],
            StructuredJobAd::KEY_CVL_INDUSTRY => [
                'label' => __('Industry (CV-Library)'),
                'type' => 'select',
                'items' => Helpers::assocToOptions(CVLibraryOutput::getIndustries()),
            ],
            StructuredJobAd::KEY_CVL_ADDITIONAL_INDUSTRIES => [
                'label' => __('Additional industries (CV-Library)'),
                'type' => 'tags',
                'items' => Helpers::assocToOptions(CVLibraryOutput::getIndustries()),
            ],
            StructuredJobAd::KEY_CVL_REGION => [
                'label' => __('Region (CV-Library)'),
                'type' => 'select',
                'items' => Helpers::assocToOptions(CVLibraryOutput::REGIONS),
            ],
            StructuredJobAd::KEY_CVL_PRECISE_LOCATION => [
                'label' => __('Precise location'),
                'type' => 'text',
                'description' => __('Town or postcode'),
            ],
            StructuredJobAd::KEY_CVL_DISPLAY_LOCATION => [
                'label' => __('Job Location'),
                'type' => 'text',
            ],
            StructuredJobAd::KEY_CVL_RELOCATE_FROM_EU => [
                'type' => 'toggle',
                'label' => __('Relocate from EU'),
            ],
            StructuredJobAd::KEY_SCREENING_QUESTIONS => [
                'type' => 'list',
                'label' => __('Screening questions'),
                'element' => [
                    'type' => 'text',
                    'placeholder' => __('Type question here...'),
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                ],
                'rules' => 'max:5',
                'default' => [],
            ],
            StructuredJobAd::KEY_CVB_LANGUAGE => [
                'type' => 'select',
                'items' => CvBankasOutput::getLanguages(),
                'label' => __('Language (CVbankas)'),
            ],
            StructuredJobAd::KEY_CV_LT_CITIES => [
                'type' => 'tags',
                'items' => CvDotLtOutput::getSelectOptions('place'),
                'label' => __('Cities (cv.lt)'),
            ],
            StructuredJobAd::KEY_CV_LT_DEPARTMENTS => [
                'type' => 'tags',
                'items' => CvDotLtOutput::getSelectOptions('department'),
                'label' => __('Departments (cv.lt)'),
            ],
            StructuredJobAd::KEY_CV_LT_POSITION_TYPE => [
                'type' => 'select',
                'search' => true,
                'items' => CvDotLtOutput::getSelectOptions('position'),
                'label' => __('Position type (cv.lt)'),
            ],
            StructuredJobAd::KEY_CV_LT_WORK_TIME => [
                'type' => 'select',
                'search' => true,
                'items' => CvDotLtOutput::getSelectOptions('work.time'),
                'label' => __('Work time (cv.lt)'),
            ],
            StructuredJobAd::KEY_CVB_CITY => [
                'type' => 'select',
                'items' => collect(CvBankasOutput::MAIN_CITIES_TO_CLOSE_CITIES_MAP)
                    ->keys()
                    ->mapWithKeys(fn(string $mainCity) => [$mainCity => $mainCity])
                    ->all(),
                'label' => __('City (CVbankas)'),
            ],
            StructuredJobAd::KEY_CVB_CLOSE_CITY_1 => [
                'type' => 'select',
                'items' => [],
                'json_logic_items' => CvBankasOutput::buildCloseCityJsonLogicItems(StructuredJobAd::KEY_CVB_CITY),
                'label' => __('Additional city (CVbankas)'),
                'conditions' => [
                    [StructuredJobAd::KEY_CVB_CITY, '!=', null],
                ],
            ],
            StructuredJobAd::KEY_CVB_CLOSE_CITY_2 => [
                'type' => 'select',
                'items' => [],
                'json_logic_items' => CvBankasOutput::buildCloseCityJsonLogicItems(StructuredJobAd::KEY_CVB_CITY),
                'label' => __('Additional city 2 (CVbankas)'),
                'conditions' => [
                    [StructuredJobAd::KEY_CVB_CITY, '!=', null],
                    [StructuredJobAd::KEY_CVB_CLOSE_CITY_1, '!=', null],
                ],
            ],
            StructuredJobAd::KEY_CVB_CATEGORY => [
                'type' => 'select',
                'items' => CvBankasOutput::getCategories(),
                'label' => __('Category (CVbankas)'),
            ],
            StructuredJobAd::KEY_CVB_GROUP => [
                'type' => 'select',
                'items' => CvBankasOutput::getGroups(),
                'label' => __('Group (CVbankas)'),
            ],
            StructuredJobAd::KEY_CVB_ARE_UKRAINIANS_WELCOME => [
                'type' => 'toggle',
                'label' => __('Are Ukrainians welcome? (CVBankas)'),
            ],
            StructuredJobAd::KEY_CVB_LABEL_DESCRIPTION => [
                'type' => 'text',
                'label' => __('Description section title'),
                'description' => __('Leave empty for default'),
            ],
            StructuredJobAd::KEY_CVB_LABEL_REQUIREMENTS => [
                'type' => 'text',
                'label' => __('Requirements section title'),
                'description' => __('Leave empty for default'),
            ],
            StructuredJobAd::KEY_CVB_LABEL_OFFERS => [
                'type' => 'text',
                'label' => __('Offer section title'),
                'description' => __('Leave empty for default'),
            ],
            StructuredJobAd::KEY_MAIN_JOB_TYPE => [
                'type' => 'select',
                'label' => __('Job type'),
                'items' => [
                    ['value' => null, 'label' => __('Not specified')],
                    ...Helpers::assocToOptions(Job::getJobTypes()),
                ],
            ],
            StructuredJobAd::KEY_LI_WORKPLACE_TYPE => [
                'type' => 'select',
                'label' => __('Workplace type'),
                'items' => Helpers::assocToOptions(Job::getWorkplaceTypes()),
            ],
            StructuredJobAd::KEY_LI_INDUSTRY_CODES => [
                'label' => __('LinkedIn industry code'),
                'type' => 'tags',
                'items' => Helpers::assocToOptions(Job::getIndustryCodes()),
                'search' => true,
            ],
            StructuredJobAd::KEY_LI_EXPERIENCE_LEVEL => [
                'type' => 'select',
                'label' => __('Experience level'),
                'items' => Helpers::assocToOptions(Job::getExperiences()),
            ],
            StructuredJobAd::KEY_LI_JOB_FUNCTIONS => [
                'type' => 'tags',
                'label' => __('Job functions'),
                'items' => Helpers::assocToOptions(Job::getJobFunctions()),
                'rules' => 'max:3',
            ],
            StructuredJobAd::KEY_INDEED_PUBLISHING_WARNING => [
                'type' => 'static',
                'persist' => false,
                'content' => Helpers::getIndeedPublishingWarning(),
            ],
            StructuredJobAd::KEY_SS_LV_PUBLISHING_WARNING => [
                'type' => 'static',
                'persist' => false,
                'content' => Helpers::getSsLvPublishingWarning(),
            ],
            StructuredJobAd::KEY_INDEED_CITY => [
                'type' => 'text',
                'label' => __('City'),
            ],
            StructuredJobAd::KEY_INDEED_STATE => [
                'type' => 'text',
                'label' => __('State'),
            ],
            StructuredJobAd::KEY_INDEED_COUNTRY => [
                'type' => 'text',
                'label' => __('Country'),
            ],
            StructuredJobAd::KEY_POSTCODE => [
                'type' => 'text',
                'label' => __('Postcode'),
            ],
            StructuredJobAd::KEY_TALENT_CATEGORY => [
                'type' => 'select',
                'label' => __('Talent.com Job category'),
                'items' => Helpers::assocToOptions(JobTalentCom::getJobCategories()),
            ],
            StructuredJobAd::KEY_A13N_ENTITY_ID => [
                'type' => 'select',
                'label' => __('apprenticeships.gov.uk entity ID'),
                'items' => [],
                'async_items' => [ApiHelper::class, 'entities'],
            ],
            StructuredJobAd::KEY_LARS_CODE => [
                'type' => 'select',
                'label' => __('LARS code'),
                'items' => [],
                'search' => true,
                'async_items' => [ApiHelper::class, 'courses'],
            ],
            StructuredJobAd::KEY_A13S_SKILLS => [
                'type' => 'tags',
                'label' => __('Skills'),
                'items' => [],
                'search' => true,
                'async_items' => [ApiHelper::class, 'skills'],
                'default' => [],
            ],
            StructuredJobAd::KEY_UKPRN => [
                'type' => 'text',
                'label' => __('UKPRN'),
                'rules' => 'numeric',
            ],
            StructuredJobAd::KEY_NUMBER_OF_POSITIONS => [
                'type' => 'text',
                'label' => __('Number of positions'),
                'rules' => 'numeric',
            ],
            StructuredJobAd::KEY_OUTCOME_DESCRIPTION => [
                'type' => 'textarea',
                'label' => __('Outcome description'),
            ],
            StructuredJobAd::KEY_TRAINING_DESCRIPTION => [
                'type' => 'textarea',
                'label' => __('Training description'),
            ],
            StructuredJobAd::KEY_DURATION_MONTHS => [
                'type' => 'text',
                'label' => __('Duration in months'),
                'rules' => 'numeric',
            ],
            StructuredJobAd::KEY_A13S_WEEKLY_HOURS => [
                'type' => 'text',
                'label' => __('Number of hours per week'),
                'rules' => 'numeric',
            ],
            StructuredJobAd::KEY_A13S_WEEK_DESCRIPTION => [
                'type' => 'textarea',
                'label' => __('Working week description'),
            ],
            StructuredJobAd::KEY_IS_DISABILITY_CONFIDENT => [
                'type' => 'toggle',
                'label' => __('Disability Confident employer'),
            ],
            StructuredJobAd::KEY_A13S_QUALIFICATIONS => [
                'type' => 'list',
                'label' => __('Qualifications'),
                'default' => [],
                'initial' => 0,
                'object' => [
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                    'schema' => [
                        'qualificationType' => [
                            'type' => 'select',
                            'label' => __('Qualification type'),
                            'items' => [],
                            'search' => true,
                            'async_items' => [ApiHelper::class, 'qualifications'],
                        ],
                        'subject' => [
                            'type' => 'text',
                            'label' => __('Subject'),
                        ],
                        'grade' => [
                            'type' => 'text',
                            'label' => __('Grade'),
                        ],
                        'weighting' => [
                            'type' => 'select',
                            'label' => __('Weighting'),
                            'items' => [
                                ['value' => 'Essential', 'label' => __('Essential')],
                                ['value' => 'Desired', 'label' => __('Desired')],
                            ],
                        ],
                    ],
                ],
            ],
            StructuredJobAd::KEY_SS_LV_CATEGORY => [
                'type' => 'select',
                'search' => true,
                'label' => __('Category (SS.lv)'),
                'items' => Helpers::assocToOptions(SsLvOutput::getCategories(), orderByLabel: true),
            ],
            StructuredJobAd::KEY_SS_LV_EDUCATION => [
                'type' => 'select',
                'label' => __('Education (SS.lv)'),
                'items' => Helpers::assocToOptions(SsLvOutput::getEducationLevels()),
            ],
            StructuredJobAd::KEY_SS_LV_LANGUAGES => [
                'label' => __('Required languages'),
                'type' => 'tags',
                'items' => collect(SsLvOutput::getLanguages())->map(fn($name, $key) => [
                    'value' => $key,
                    'label' => $name,
                ])->values()->toArray(),
            ],
            StructuredJobAd::KEY_SS_LV_WORKING_DAYS => [
                'type' => 'select',
                'label' => __('Working days'),
                'items' => Helpers::assocToOptions(SsLvOutput::getWorkingDays()),
            ],
            StructuredJobAd::KEY_SS_LV_WORKING_HOURS => [
                'type' => 'text',
                'label' => __('Working hours'),
            ],
            StructuredJobAd::KEY_SS_LV_ADDRESS => [
                'type' => 'text',
                'label' => __('Address (SS.lv)'),
            ],
            StructuredJobAd::KEY_SS_LV_LOCATION => [
                'type' => 'select',
                'label' => __('City or county (SS.lv)'),
                'items' => Helpers::assocToOptions(SsLvOutput::getLocations()),
            ],
            StructuredJobAd::KEY_SS_LV_CITY => [
                'type' => 'select',
                'label' => __('Neighbourhood or parish (SS.lv)'),
                'items' => [],
                'json_logic_items' => [
                    'if' => collect(SsLvOutput::getLocationToRegionMapping())->flatMap(function (array $regions, int $location) {
                        return [
                            ['==' => [['var' => StructuredJobAd::KEY_SS_LV_LOCATION], $location]],
                            collect($regions)->map(function (int $region) {
                                return ['value' => $region, 'label' => SsLvOutput::getRegions()[$region] ?? ''];
                            }),
                        ];
                    })->toArray(),
                ],
            ],
            StructuredJobAd::KEY_PROF_HU_ADVERT_TYPE => [
                'type' => 'select',
                'label' => __('Advert type (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'advert_type'],
            ],
            StructuredJobAd::KEY_PROF_HU_LOCATION => [
                'type' => 'select',
                'label' => __('Location (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'location'],
            ],
            StructuredJobAd::KEY_PROF_HU_SECTOR => [
                'type' => 'tags',
                'label' => __('Sectors (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'sector'],
            ],
            StructuredJobAd::KEY_PROF_HU_CLASSIFICATION => [
                'type' => 'select',
                'label' => __('Classification (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'classification_type'],
            ],
            StructuredJobAd::KEY_PROF_HU_EXPERIENCE => [
                'type' => 'select',
                'label' => __('Experience (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'experience'],
            ],
            StructuredJobAd::KEY_PROF_HU_WORKING_SCHEDULES => [
                'type' => 'tags',
                'label' => __('Working schedule (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'working_schedule.type'],
            ],
            StructuredJobAd::KEY_PROF_HU_WORK_SCHEDULE => [
                'type' => 'select',
                'label' => __('Work schedule (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'work_schedule.type'],
            ],
            StructuredJobAd::KEY_PROF_HU_QUALIFICATION => [
                'type' => 'select',
                'label' => __('Education requirement (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'qualification'],
            ],
            StructuredJobAd::KEY_PROF_HU_DRIVERS_LICENSE => [
                'type' => 'select',
                'label' => __('Driver\'s license (profession.hu)'),
                'items' => [],
                'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'drive_license_id'],
            ],
            StructuredJobAd::KEY_PROF_HU_LANGS => [
                'type' => 'list',
                'label' => __('Language skills (profession.hu)'),
                'initial' => 0,
                'object' => [
                    'columns' => [
                        'label' => 0,
                        'field' => 12,
                    ],
                    'schema' => [
                        'lang_id' => [
                            'type' => 'select',
                            'label' => __('Language'),
                            'items' => [],
                            'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'lang_id'],
                        ],
                        'lang_level' => [
                            'type' => 'select',
                            'label' => __('Level'),
                            'items' => [],
                            'async_items' => [\App\DataExchange\ProfessionHu\Export\ApiHelper::class, 'lang_level'],
                        ],
                    ],
                ],
            ],
            StructuredJobAd::KEY_HTML_USE_OVERRIDE => [
                'type' => 'toggle',
                'label' => __('Use custom description for LinkedIn'),
                'description' => __(''),
                'default' => 0,
                'trueValue' => 1,
                'falseValue' => 0,
            ],
            StructuredJobAd::KEY_HTML_PREVIEW => [
                'type' => 'trix',
                'label' => __('Job description preview'),
                'description' => __('This will be sent to LinkedIn as the job description.'),
                'disabled' => true,
                'conditions' => [
                    [StructuredJobAd::KEY_HTML_USE_OVERRIDE, 0],
                    [StructuredJobAd::KEY_LANDING_ID, '!=', null],
                ],
                'autofill_on_load' => true,
                'autofill_url' => route('landings.simple', ['landing' => '---' . StructuredJobAd::KEY_LANDING_ID . '---', 'exclude_blocks' => 'LandingSocialMediaBlock']),
                'autofill_watch' => [StructuredJobAd::KEY_LANDING_ID],
                'autofill_skip' => [StructuredJobAd::KEY_LANDING_ID => ['custom']],
                'persist' => false,
            ],
            StructuredJobAd::KEY_HTML_OVERRIDE => [
                'type' => 'trix',
                'label' => __('Custom description'),
                'description' => __('This will be sent to LinkedIn as the job description.'),
                'conditions' => [
                    [StructuredJobAd::KEY_HTML_USE_OVERRIDE, 1],
                ],
                'no_attachments' => true,
                'autofill_url' => route('landings.simple', ['landing' => '---landing_id---', 'exclude_blocks' => 'LandingSocialMediaBlock']),
                'autofill_watch' => ['landing_id', 'html_use_override'],
                'autofill_skip' => ['landing_id' => ['custom']],
            ],
            StructuredJobAd::KEY_PROBATION_PERIOD_DAYS => [
                'type' => 'text',
                'label' => __('Probation period (days)'),
                'rules' => 'nullable|numeric',
            ],
            StructuredJobAd::KEY_TALENME_REFERRAL_FEE => [
                'label' => __('Referral fee percentage'),
                'type' => 'text',
                'default' => 70,
                'rules' => ['min:0', 'max:90', 'integer'],
            ],
            StructuredJobAd::KEY_DUUNITORI_CAMPAIGN_TYPE => [
                'label' => __('Campaign type'),
                'type' => 'selectwithdescription',
                'items' => DuunitoriOutput::getCampaignTypes(),
                'rules' => 'required',
            ],
            StructuredJobAd::KEY_DUUNITORI_EMPLOYMENT_TYPE => [
                'label' => __('Employment type'),
                'description' => __('Type of employment: full time, part time, permanent, temporary, etc.'),
                'type' => 'text',
            ],
            StructuredJobAd::KEY_REFERRAL_BUDGET_PER_HIRE_EUR => [
                'type' => 'text',
                'label' => __('Referral budget (€)'),
                'rules' => 'nullable|numeric',
            ],
            Setting::KEY_ORGANIZATION_NAME => [
                'type' => 'text',
                'label' => __('Org. name'),
                'rules' => 'required',
                'persist' => false,
                'json_logic' => [],
                'description' => __('Used in job board exports.'),
                'default' => Setting::get(Setting::KEY_ORGANIZATION_NAME),
            ],
            Setting::KEY_ORGANIZATION_CITY => [
                'type' => 'text',
                'label' => __('Org. location city'),
                'rules' => 'nullable',
                'persist' => false,
                'json_logic' => [],
                'description' => __('Used in job board exports.'),
                'default' => Setting::get(Setting::KEY_ORGANIZATION_CITY),
            ],
            Setting::KEY_ORGANIZATION_STREET_ADDRESS => [
                'type' => 'text',
                'label' => __('Org. street address'),
                'rules' => 'nullable',
                'persist' => false,
                'json_logic' => [],
                'description' => __('Used in job board exports.'),
                'default' => Setting::get(Setting::KEY_ORGANIZATION_STREET_ADDRESS),
            ],
            Setting::KEY_ORGANIZATION_WEBSITE_URL => [
                'type' => 'text',
                'label' => __('Org. website URL'),
                'rules' => 'nullable|url',
                'persist' => false,
                'json_logic' => [],
                'description' => __('Used in email signatures, job board exports.'),
                'default' => Helpers::toValidUrl(Setting::get(Setting::KEY_ORGANIZATION_WEBSITE_URL)),
            ],
        ];
    }

    protected function getIntegrationFields(): array
    {
        $integrations = $this->getIntegrationsWithCustomFields();

        return $integrations->mapWithKeys(function (Integration $integration) {
            return [
                $integration->getCustomFieldsKey() => [
                    'type' => 'object',
                    'label' => $integration->name,
                    'schema' => Setting::getCustomFields($integration->job_ad_custom_field_configuration)->toArray(),
                    'json_logic' => [
                        '!!' => [
                            'filter' => [
                                ['var' => 'integrations'],
                                // TODO: Check if this can be simplified.
                                ['in' => [['var' => ''], [$integration->id]]],
                            ],
                        ],
                    ],
                ],
            ];
        })->toArray();
    }

    private array $groupedIntegrationIds = [];
    private array $integrationJobAdFields = [];

    public function schema()
    {
        $integrations = Integration::query()->get();

        // The end result is a dictionary with the following structure:
        // [<integration_type> => [
        //      [
        //          <field> => [<array of individual integrations requiring this field>]
        //      ]
        // ]
        $this->integrationJobAdFields = $integrations
            ->filter(fn(Integration $i) => $i->job_ad_fields)
            ->map(function (Integration $integration) {
                return [$integration->remote_type => collect($integration->job_ad_fields)->mapWithKeys(function ($field) use ($integration) {
                    return [$field => [$integration->id]];
                })->toArray()];
            })->toArray();

        $this->integrationJobAdFields = array_merge_recursive(...$this->integrationJobAdFields);

        $this->groupedIntegrationIds = collect(Integration::getTypes())
            ->map(fn ($ignored, string $type) => $integrations->where('remote_type', $type)->pluck('id')->all())
            ->all();

        $fields = $this->getSystemFields();

        $fields = array_merge($fields, [
            StructuredJobAd::KEY_CUSTOM_FIELDS => [
                'type' => 'object',
                'persist' => false, // Persisted manually in #after(). Reason: bug in Laraform source code.
                'schema' => $this->getIntegrationFields(),
                'conditions' => [],
                'columns' => [
                    'element' => 12,
                    'label' => 0,
                    'field' => 12,
                ],
            ],
        ]);

        foreach ($fields as $name => &$field) {
            if (isset($field['json_logic']) || isset($field['conditions'])) {
                continue;
            }
            $field['json_logic'] = $this->getShowLogic($name);
        }

        foreach ($fields as $name => &$field) {
            $this->addRequiredRule($name, $field);
            if ($field['type'] === 'object') {
                foreach ($field['schema'] as $objectKey => &$objectField) {
                    $this->addRequiredRule("$name.$objectKey", $objectField);
                }
            }
        }

        return $fields;
    }

    public function getCreativeFieldsDisplayLogics(): array
    {
        $integrationsByCreativeAcceptance = collect($this->groupedIntegrationIds)
            ->groupBy(function ($integrationIds, $integrationRemoteType) {
                $integrationToOutput = Integration::INTEGRATION_OUTPUT_MAP;
                if (!isset($integrationToOutput[$integrationRemoteType])) {
                    return 'other';
                } else {
                    /** @var ControlsFields $output */
                    $output = $integrationToOutput[$integrationRemoteType];
                    $acceptedCreatives = $output::getAcceptedCreatives();
                    if (count($acceptedCreatives) === 2) {
                        return 'both';
                    } elseif (count($acceptedCreatives) === 1) {
                        return $acceptedCreatives[0];
                    } else {
                        return 'none';
                    }
                }
            })->mapWithKeys(function ($v, $k) {
                return [$k => $v->flatten()];
            })->toArray();

        $product = [];

        $keys = array_keys($integrationsByCreativeAcceptance);

        foreach ($keys as $key) {
            $product[$key] = $integrationsByCreativeAcceptance[$key] ?? [];
        }

        if (isset($product['landing'])) {
            $product['landing'][] = 'social';
        }

        $selectedOutputsFragment = [
            'filter' => [
                [
                    'merge' => [
                        ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                        [
                            'if' => [
                                ['var' => StructuredJobAd::KEY_PUBLISH_SOCIAL_MEDIA],
                                ['social'],
                                [],
                            ],
                        ],
                    ],
                ],
                // all except those which need no creative
                [
                    '!' => [
                        'in' => [
                            ['var' => ''],
                            $product['none'] ?? [],
                        ],
                    ],
                ],
            ],
        ];

        $showCreativeTypeChoice = [
            'all' => [
                $selectedOutputsFragment,
                ['in' => [['var' => ''], $product['both'] ?? []]],
            ],
        ];

        $integrationsIdsThatCanGenerateAnImage = Integration::query()
            ->whereNotNull('image_generation_url')
            ->active()
            ->pluck('id')
            ->all();

        $oneOfChosenOutputsCanGenerateAnImage = [
            'some' => [
                $selectedOutputsFragment,
                ['in' => [['var' => ''], $integrationsIdsThatCanGenerateAnImage]],
            ],
        ];

        $showImageUpload = [
            'or' => [
                [
                    // show if choice available and chosen
                    'and' => [
                        $showCreativeTypeChoice,
                        ['==' => [['var' => StructuredJobAd::KEY_CREATIVE_TYPE], 'image']],
                        ['!' => $oneOfChosenOutputsCanGenerateAnImage],
                    ],
                ],
                [
                    // show if all feeds accept image but creative choice is not available
                    'and' => [
                        [
                            '!' => [
                                'and' => [
                                    $showCreativeTypeChoice,
                                    ['!=' => [['var' => StructuredJobAd::KEY_CREATIVE_TYPE], 'image']],
                                ],
                            ],
                        ],
                        [
                            'all' => [
                                $selectedOutputsFragment,
                                ['in' => [['var' => ''], array_merge($product['both'] ?? [], $product['image'] ?? [])]],
                            ],
                        ],
                        ['!' => $oneOfChosenOutputsCanGenerateAnImage],
                    ],
                ],
            ],
        ];

        //        dd(json_encode($showImageUpload));

        $showLandingChoice = [
            'or' => [
                [
                    // show if choice avaiable and chosen
                    'and' => [
                        $showCreativeTypeChoice,
                        ['==' => [['var' => StructuredJobAd::KEY_CREATIVE_TYPE], 'landing']],
                    ],
                ],
                [
                    // show if all feeds accept landing
                    'and' => [
                        [
                            '!' => [
                                'and' => [
                                    $showCreativeTypeChoice,
                                    ['!=' => [['var' => StructuredJobAd::KEY_CREATIVE_TYPE], 'landing']],
                                ],
                            ],
                        ],
                        [
                            'all' => [
                                $selectedOutputsFragment,
                                [
                                    'in' => [
                                        ['var' => ''],
                                        array_merge($product['both'] ?? [], $product['landing'] ?? [], ['social']),
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $nothingSelected = [
            '!' => $selectedOutputsFragment,
        ];

        $showError = [
            'or' => [
                [
                    // if there are no creative selects to show
                    '!' => [
                        'or' => [
                            $showCreativeTypeChoice,
                            $showImageUpload,
                            $showLandingChoice,
                            $nothingSelected,
                        ],
                    ],
                ],
            ],
        ];

        return [$showCreativeTypeChoice, $showImageUpload, $showLandingChoice, $showError];
    }

    /**
     * @return array[]
     */
    public function getCvoTargetStageDisplayLogic(): array
    {
        return [
            'and' => [
                [
                    '!!' => [
                        'filter' => [
                            ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                            [
                                'in' => [
                                    ['var' => ''],
                                    array_merge(
                                        $this->groupedIntegrationIds[Integration::TYPE_CV_EE] ?? [],
                                        $this->groupedIntegrationIds[Integration::TYPE_CV_LV] ?? [],
                                        $this->groupedIntegrationIds[Integration::TYPE_CV_LT] ?? [],
                                        $this->groupedIntegrationIds[Integration::TYPE_INDEED] ?? [],
                                    ),
                                ],
                            ],
                        ],
                    ],
                ],
                [
                    'or' => [
                        ['==' => [['var' => StructuredJobAd::KEY_CREATIVE_TYPE], 'image']],
                        ['!!' => ['var' => StructuredJobAd::KEY_IMAGE . '.location']],
                        ['==' => [['var' => StructuredJobAd::KEY_CVO_USE_PORTAL_APPLY], true]],
                    ],
                ],
            ],

        ];
    }

    public function getIntegrationsWithCustomFields(): Collection
    {
        return Integration::query()
            ->whereNotNull('job_ad_custom_field_configuration')
            ->get();
    }

    private function castArrToInts($dotKey)
    {
        $data = $this->data;
        if ($values = data_get($data, $dotKey, [])) {
            $values = array_map('intval', $values);
            data_set($data, $dotKey, $values);
        }
        $this->setData($data);
    }

    /**
     * If we're updating an existing job ad, then before saving the form with new values,
     * we store the previous values of specific keys so that we can react when those values
     * have changed.
     *
     * @return void
     */
    public function saving()
    {
        if (count($this->previousFormStoreKeys) && $this->hasKey()) {
            $item = $this->getEntity();
            foreach ($this->previousFormStoreKeys as $key) {
                if (isset($item->{$key})) {
                    $this->previousFormValues[$key] = $item->{$key};
                }
            }
        }
    }

    public function before()
    {
        $data = $this->data;

        $landingId = data_get($data, StructuredJobAd::KEY_LANDING_ID);

        if (isset($landingId) && !is_numeric($landingId)) {
            $data[StructuredJobAd::KEY_LANDING_ID] = null;
            $this->schema[StructuredJobAd::KEY_LANDING_ID]['rules'] = '';
            $this->setElements();
            $this->setData($data);
        }
        if (isset($data['id'])) {
            // must not overwrite cvo_remote_source_id
            $jobAd = StructuredJobAd::find($data['id']);
            if ($jobAd->cvo_remote_source_id && !isset($data['cvo_remote_source_id'])) {
                $data['cvo_remote_source_id'] = $jobAd->cvo_remote_source_id;
                $this->setData($data);
            }
        }
        $file = request()->file('data.employer_logo_location');
        if ($file) {
            $value = File::store($file);
            $data['employer_logo_location'] = $value;
            $this->setData($data);
        }
        // todo these look similar, refactor
        if (!isset($data[StructuredJobAd::KEY_CVO_USE_PORTAL_APPLY])) {
            $data[StructuredJobAd::KEY_CVO_USE_PORTAL_APPLY] = 0;
            $this->setData($data);
        }
        if (!isset($data[StructuredJobAd::KEY_CVL_HIDE_SALARY])) {
            $data[StructuredJobAd::KEY_CVL_HIDE_SALARY] = 0;
            $this->setData($data);
        }
        if (!isset($data[StructuredJobAd::KEY_IS_REMOTE])) {
            $data[StructuredJobAd::KEY_IS_REMOTE] = 0;
            $this->setData($data);
        }
        if (!isset($data[StructuredJobAd::KEY_IS_DISABILITY_CONFIDENT])) {
            $data[StructuredJobAd::KEY_IS_DISABILITY_CONFIDENT] = false;
            $this->setData($data);
        }
        if (isset($data[StructuredJobAd::KEY_TOTALJOBS_SALARY_RANGE])) {
            $salaryRate = $data[StructuredJobAd::KEY_TOTALJOBS_SALARY_RATE];
            $salaryRangeRaw = $data[StructuredJobAd::KEY_TOTALJOBS_SALARY_RANGE];
            [$salaryFrom, $salaryTo] = match ($salaryRate) {
                SalaryRate::ANNUAL->value => SalaryRangeAnnual::from($salaryRangeRaw)->getNumericRange(),
                SalaryRate::DAILY->value => SalaryRangeDaily::from($salaryRangeRaw)->getNumericRange(),
                SalaryRate::HOURLY->value => SalaryRangeHourly::from($salaryRangeRaw)->getNumericRange(),
            };
            $data['salary_from'] = $salaryFrom;
            $data['salary_to'] = $salaryTo;
            $this->setElements();
            $this->setData($data);
        }
        if (!isset($data[StructuredJobAd::KEY_HTML_USE_OVERRIDE])) {
            $data[StructuredJobAd::KEY_HTML_USE_OVERRIDE] = false;
            $this->setData($data);
        }
        if (data_get($data, StructuredJobAd::KEY_PROF_HU_WORKING_SCHEDULES)) {
            // Ensure that working schedules are saved as an array of integers (not strings)
            $data[StructuredJobAd::KEY_PROF_HU_WORKING_SCHEDULES] = Arr::map(
                $data[StructuredJobAd::KEY_PROF_HU_WORKING_SCHEDULES],
                fn($type) => intval($type)
            );
            $this->setData($data);
        }

    }

    public function after()
    {
        /** @var StructuredJobAd $ad */
        $ad = StructuredJobAd::find($this->data['id']);

        // Update custom fields manually to avoid Laraform bug when custom fields are not present.
        $ad->update(Arr::only($this->data, StructuredJobAd::KEY_CUSTOM_FIELDS));

        // An output might generate an image for the job ad
        $ad->generateAnImageIfPossible();

        // Create imports for passive integrations
        $ad->createImportsForFeedIntegrations();

        // Push active integrations to their remote endpoints
        $ad->pushIntegrations($this->previousFormValues);

        $errored = $ad->integrations->where('pivot.response.status', IntegrationStructuredJobAd::STATUS_ERROR);

        if ($errored->count()) {
            return response()->json([
                'status' => 'fail',
                'updates' => $this->getUpdates(),
                'messages' => $errored->map(function (Integration $integration) {
                    return "$integration->name: \n" . implode("\n\n", $integration->pivot->response->errors);
                })->values()->toArray(),
                'no_auto_hide' => true,
            ]);
        }

        if ($ad->publish_social_media && !$ad->payment) {
            $url = url("/structured-jobs/$ad->id/checkout");
        } else {
            if ($ad->image) {
                $url = url('/structured-jobs');
            } elseif ($ad->landing) {
                $url = url('/landings');
            } else {
                $url = url('/projects/' . session('latest_project_id'));
            }
        }

        if ($ad->landing) {
            Landing::clearSsrCache();
        }

        $data = $this->data;

        // TODO: figure this out
        if (!Helpers::getCurrentWebsite()->features[Website::FEATURE_KEY_TEAMS]) {
            foreach (self::SETTINGS_INPUTS as $input) {
                if (isset($data[$input])) {
                    Setting::set($input, $data[$input]);
                }
            }
        }

        Segment::track([
            'userId' => optional(auth()->user())->id,
            'event' => 'Exported job ad',
        ]);

        return response([
            'messages' => [],
            'payload' => [
                'updates' => $this->updates,
                'redirect_url' => $url,
                'full_page_reload' => true,
            ],
            'status' => 'success',
        ]);

    }

    protected function addRequiredRule(string $fieldName, array &$field): void
    {
        if (!isset($field['rules'])) {
            $rules = [];
        } elseif (isset($field['rules']) && is_string($field['rules'])) {
            $rules = explode('|', $field['rules']);
        } else {
            $rules = $field['rules'];
        }

        if (in_array('required', $rules)) {
            return;
        }

        $integrationOutputLogic = Integration::INTEGRATION_OUTPUT_MAP;
        $requiredForIntegrations = [];
        foreach ($integrationOutputLogic as $integrationRemoteType => $outputClass) {
            $ids = $this->groupedIntegrationIds[$integrationRemoteType] ?? [];
            if (in_array($fieldName, [$outputClass, 'getRequiredFields']())) {
                $requiredForIntegrations = array_merge($requiredForIntegrations, $ids);
            }
        }

        if (count($requiredForIntegrations)) {
            $rules[] = ['required' => [StructuredJobAd::KEY_INTEGRATIONS, $requiredForIntegrations]];
        }

        if (!str_contains($fieldName, StructuredJobAd::KEY_SOCIAL_MEDIA_PARAMS)) {
            $field['rules'] = $rules;
        }
    }

    protected function getShowLogic(string $fieldName)
    {
        $commonFields = [
            'id',
            'project_id',
            StructuredJobAd::KEY_DEADLINE_AT,
            StructuredJobAd::KEY_PUBLISH_SOCIAL_MEDIA,
            StructuredJobAd::KEY_SOCIAL_MEDIA_PARAMS,
            StructuredJobAd::KEY_INTEGRATIONS,
            StructuredJobAd::KEY_POSITION_NAME,
        ];

        if (in_array($fieldName, $commonFields)) {
            return null;
        }
        $orClauses = [];

        $integrationOutputLogic = Integration::INTEGRATION_OUTPUT_MAP;
        foreach ($integrationOutputLogic as $integrationRemoteType => $outputClass) {
            $ids = $this->groupedIntegrationIds[$integrationRemoteType] ?? [];
            $acceptedFields = [$outputClass, 'getAcceptedFields']();
            $acceptedKeys = array_merge(
                array_filter(array_keys($acceptedFields), function ($k) {
                    return !is_int($k);
                }),
                array_filter(array_values($acceptedFields), function ($k) {
                    return is_string($k);
                })
            );

            $requiredFields = [$outputClass, 'getRequiredFields']();
            $fieldsWithCustomRules = array_filter(array_keys($requiredFields), function ($k) {
                return !is_int($k);
            });

            $integrationBasedFields = $this->integrationJobAdFields[$integrationRemoteType] ?? [];

            if ($ids && in_array($fieldName, $fieldsWithCustomRules)) {
                // * There exist integrations of this type
                // * This field is required for this type of integration
                // * The integration defines custom rules for this field
                $orClauses[] = [
                    'and' => [
                        $requiredFields[$fieldName],
                        [
                            '!!' => [
                                'filter' => [
                                    ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                                    ['in' => [['var' => ''], $ids]],
                                ],
                            ],
                        ],
                    ],
                ];
            } elseif ($ids && in_array($fieldName, $acceptedKeys)) {
                // * There exist integrations of this type
                // * This field is accepted for this type of integration
                $orClauses[] = [
                    '!!' => [
                        'filter' => [
                            ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                            ['in' => [['var' => ''], $ids]],
                        ],
                    ],
                ];
            } elseif (data_get($integrationBasedFields, $fieldName)) {
                // * There exist individual integrations that require this field
                $orClauses[] = [
                    '!!' => [
                        'filter' => [
                            ['var' => StructuredJobAd::KEY_INTEGRATIONS],
                            ['in' => [['var' => ''], $integrationBasedFields[$fieldName]]],
                        ],
                    ],
                ];
            }
        }

        if (count($orClauses) === 0) {
            return ['!=' => [['var' => 'a'], ['var' => 'b']]];
        } elseif (count($orClauses) === 1) {
            return $orClauses[0];
        }

        return [
            'or' => $orClauses,
        ];
    }

    protected static function getCurrencyOptions()
    {
        $iso4217 = new ISO4217;
        $topChoices = ['EUR', 'GBP', 'USD'];

        $sortedCurrencies = collect($iso4217->getAll())->map(function ($currency) use ($topChoices) {
            return [
                'isTopCurrency' => in_array($currency['alpha3'], $topChoices),
                'value' => $currency['alpha3'],
                'label' => "{$currency['alpha3']} {$currency['name']}",
            ];
        })->sortBy([
            ['isTopCurrency', 'desc'],
            ['value', 'asc'],
        ])->values();

        $sortedCurrencies->splice(count($topChoices), 0, [['value' => '', 'label' => '-------------------', 'disabled' => true]]);

        return $sortedCurrencies->all();
    }
}
