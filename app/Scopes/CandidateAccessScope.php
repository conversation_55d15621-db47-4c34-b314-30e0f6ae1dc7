<?php

namespace App\Scopes;

use App\Helpers;
use App\Models\Candidate;
use App\Models\Presentation;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class CandidateAccessScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        $user = Helpers::getScopeUser();

        if ($user && $user->role === User::ROLE_ADMIN && $user->can_see_all_projects) {
            return;
        }

        $builder->where(function (Builder $q1) use ($user) {
            if (Setting::get(Setting::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT)) {
                $q1->where(function (Builder $q2) {
                    $q2->whereHas('stages')
                        ->orWhereIn('candidates.id', session()->get('temp_candidate_access', []));
                });
            } elseif ($user && $user->role === User::ROLE_LIMITED && !User::$noScope) {
                $q1->where(function (Builder $q2) {
                    $q2->whereHas('stages')
                        ->orWhereIn('candidates.id', session()->get('temp_candidate_access', []));
                });
            } else {
                // TODO: Is this a bug? This conditional branch is executed even if Helpers::getScopeUser() returns null (e.g. console, scheduled jobs, etc). Been here since 2021.
                $q1->where(function (Builder $q2) {
                    $q2->where('is_private', 0)
                        ->orWhereHas('stages');
                });
            }

            if ($user) {
                $q1->orWhereHas('presentations', function (Builder $presentationQuery) use ($user) {
                    $presentationQuery->where('is_internal', true)->whereRelation('users', 'id', $user->id);
                });
            }
        });
    }

    public static function hasAccessFromTemporaryAccess(Candidate $candidate): bool
    {
        $user = Helpers::getScopeUser();

        return $user
            && $user->role === User::ROLE_LIMITED
            && !User::$noScope
            && in_array($candidate->id, session()->get('temp_candidate_access', []));
    }

    public static function hasAccessAsItsPublicCandidate(Candidate $candidate): bool
    {
        $user = Helpers::getScopeUser();

        return !$candidate->is_private && (
            !$user
            || $user->role !== User::ROLE_LIMITED
            || User::$noScope
        );
    }

    public static function hasAccessFromPresentation(Candidate $candidate): bool
    {
        $user = Helpers::getScopeUser();

        return $user && Presentation::whereRelation('candidates', 'id', $candidate->id)->exists();
    }

    public static function hasAccessAsSuperAdmin(Candidate $candidate): bool
    {
        $user = Helpers::getScopeUser();

        return $user
            && $user->role === User::ROLE_ADMIN
            && $user->can_see_all_projects;
    }

    public static function hasAccessFromStage(Candidate $candidate): bool
    {
        return $candidate->stages->isNotEmpty();
    }
}
