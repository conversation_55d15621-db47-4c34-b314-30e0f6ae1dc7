<?php

namespace App\Classes\Screening;

use App\Models\Landing;
use App\Models\Project;
use App\Services\AI\HasOpenAIClient;

class CriteriaGenerator
{
    use HasOpenAIClient;

    public function __construct(
        protected Project $project
    ) {}

    public function generateCriteria(): array
    {
        /** @var Landing $landing */
        $landing = $this->project->landings()->first();
        $landingHtml = $landing?->toSimpleHTML();

        $res = $this->getClient()->chat()->create([
            'model' => $this->getDefaultModel(),
            'messages' => [
                [
                    'role' => 'system',
                    'content' => <<<'PROMPT'
                    You are an AI assistant that helps generate CV screening criteria for recruitment projects.
                    You will be provided with a project name and description and the job ad, and you will generate a list of
                    screening criteria based on that information.
                    You will only generate objective criteria that can be answered based on a typical CV.
                    You will not add any criteria that are not mentioned in the job ad.
                    You will create the criteria in the same language as the job ad.
                    Avoid subjective criteria like "good communication skills" or "team player".
                    You will not mix multiple criteria into a single criterion.

                    Respond with the specified json.
                    PROMPT,
                ],
                [
                    'role' => 'user',
                    'content' => <<<INFO
                    Project name: {$this->project->name}
                    Project description: {$this->project->description}

                    Project job ad:
                    {$landingHtml}
                    INFO
                ],
            ],
            'response_format' => [
                'type' => 'json_schema',
                'json_schema' => [
                    'name' => 'criteria_schema',
                    'schema' => [
                        'type' => 'object',
                        'properties' => [
                            'criteria' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'string',
                                ],
                            ],
                        ],
                        'required' => ['criteria'],
                    ],
                ]],
        ]);

        $res = json_decode($res->choices[0]->message->content, true);

        return $res['criteria'] ?? [];
    }

    public function getCriteriaMatchesForCv() {}

}
