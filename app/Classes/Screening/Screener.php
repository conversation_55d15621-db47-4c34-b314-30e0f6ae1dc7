<?php

namespace App\Classes\Screening;

use App\Models\Application;
use App\Models\ScreeningCriterionResponse;
use App\Services\AI\HasOpenAIClient;
use Illuminate\Support\Facades\DB;

class Screener
{
    use HasOpenAIClient;

    private $enableCache = false;

    public function __construct(
        protected Application $application,
    ) {}

    public function run()
    {
        $candidate = $this->application->candidate;
        $lastCvContents = $candidate->lastCv?->contents;

        if (!$lastCvContents) {
            throw new \Exception(__('Failed to run screener - CV contents empty or missing.'));
        }

        $criteria = $this->application->project->screeningCriteria()->pluck('criterion')->toArray();

        $criteriaStr = implode("\n", $criteria);

        // hash on criteria and cv contents to avoid calling the API multiple times for the same input
        $cacheKey = md5($criteriaStr . $lastCvContents);
        $fromCache = cache()->get($cacheKey) && $this->enableCache;

        if (!$fromCache) {
            $res = $this->getClient()->chat()->create([
                'model' => $this->getDefaultModel(),
                'temperature' => 0.4,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => <<<'PROMPT'
                        You are an AI assistant that assesses if candidate CVs meet the screening criteria for recruitment projects.
                        You will be provided a with a list of criteria and information about a candidate.
                        You will respond with a list of boolean values, one for each criteria.
                        You will give the match_details in the same language as the criteria is.
                        PROMPT,
                    ],
                    [
                        'role' => 'user',
                        'content' => <<<INFO
                        Screening criteria:
                        {$criteriaStr}
                        INFO
                    ],
                    [
                        'role' => 'user',
                        'content' => <<<"INFO"
                        Candidate CV:
                        {$lastCvContents}
                        INFO
                    ],
                ],
                'response_format' => [
                    'type' => 'json_schema',
                    'json_schema' => [
                        'name' => 'criteria_schema',
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'criteria_matches' => [
                                    'type' => 'array',
                                    'items' => [
                                        'type' => 'object',
                                        'properties' => [
                                            'criterion' => [
                                                'type' => 'string',
                                                'description' => 'The criterion that was matched',
                                            ],
                                            'match' => [
                                                'type' => 'string',
                                                'enum' => ['match', 'partial_match', 'no_match'],
                                            ],
                                            'match_details' => [
                                                'type' => 'string',
                                                'description' => 'A short description of why the criterion was matched or not',
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ]);

            // put to cache and return
            $fromCache = json_decode($res->choices[0]->message->content, true);
            cache()->put($cacheKey, $fromCache, 60 * 60 * 24);
        }

        // Persist the results to the database
        return $this->persistResults($fromCache);
    }

    /**
     * Persist the screening results to the database
     */
    protected function persistResults(array $results): array
    {
        $created = [];

        // Start a transaction to ensure all operations are atomic
        DB::transaction(function () use ($results, &$created) {
            // Delete existing responses for this application
            ScreeningCriterionResponse::where('application_id', $this->application->id)->get()->each->delete();

            // Get all criteria for this project
            $projectCriteria = $this->application->project->screeningCriteria()->get()->keyBy('criterion');

            // Create new responses
            foreach ($results['criteria_matches'] ?? [] as $match) {
                $criterion = $projectCriteria[$match['criterion']] ?? null;

                // Skip if criterion doesn't exist in the database
                if (!$criterion) {
                    continue;
                }

                // Create the response
                $created[] = ScreeningCriterionResponse::create([
                    'application_id' => $this->application->id,
                    'screening_criterion_id' => $criterion->id,
                    'result' => $match['match'],
                    'details' => $match['match_details'] ?? null,
                ]);
            }
        });

        return $created;
    }

    public function recordErrorState(string $message): array
    {
        $results = [];

        // just create a response for each criterion with an error message
        DB::transaction(function () use ($message, &$results) {
            // Delete existing responses for this application
            ScreeningCriterionResponse::where('application_id', $this->application->id)->get()->each->delete();

            // Get all criteria for this project
            $projectCriteria = $this->application->project->screeningCriteria()->get();

            // Create new responses
            foreach ($projectCriteria as $criterion) {
                $results[] = ScreeningCriterionResponse::create([
                    'application_id' => $this->application->id,
                    'screening_criterion_id' => $criterion->id,
                    'error_message' => $message,
                    'error_at' => now(),
                ]);
            }
        });

        return $results;
    }
}
