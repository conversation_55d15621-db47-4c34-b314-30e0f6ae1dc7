<?php

namespace App\Classes\Screening;

use App\Models\Project;
use App\Services\AI\HasOpenAIClient;
use Illuminate\Support\Collection;

class CriteriaValidator
{
    use HasOpenAIClient;

    protected Project $project;
    protected Collection $criteria;

    public function __construct(Project $project)
    {
        $this->project = $project;
        $this->criteria = $project->screeningCriteria;
    }

    /**
     * Validate the criteria for unsuitable content
     *
     * @return array An array of validation results with 'criterion_id', 'is_unsuitable', and 'reason'
     */
    public function validate(): array
    {
        if ($this->criteria->isEmpty()) {
            return [];
        }

        $criteriaText = $this->criteria->pluck('criterion')->implode("\n");

        $ctx = [];

        if ($this->project->description) {
            $ctx[] = [
                'role' => 'user',
                'content' => 'The job ad for the position is: ' . $this->project->description,
            ];
        }

        if ($this->project->landings()->get()->first()) {
            $ctx[] = [
                'role' => 'user',
                'content' => 'The job ad for the position is: ' . $this->project->landings()->get()->first()->toSimpleHTML(),
            ];
        }

        $res = $this->getClient()->chat()->create([
            'model' => $this->getDefaultModel(),
            'temperature' => 0.2,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => <<<'PROMPT'
                    You are an AI assistant that helps identify unsuitable, discriminatory or biased screening criteria in recruitment.

                    You will be provided with a list of screening criteria for a job position. Your task is to identify any criteria that might be:
                    1. Directly unsuitable based on protected characteristics (age, gender, race, religion, disability, etc.)
                    2. Irrelevant to job performance and potentially biased
                    3. The information is very unlikely to be included in a typical CV
                    4. Subjective in as a self-assessment ("team-player", "good communication skills", etc.)

                    For each criterion, determine if it's unsuitable. If it is, explain why.
                    Respond in the same language that the criterion is in.

                    Respond with the specified JSON format.
                    PROMPT,
                ],
                ...$ctx,
                [
                    'role' => 'user',
                    'content' => <<<INFO
                    Screening criteria:
                    {$criteriaText}
                    INFO
                ],
            ],
            'response_format' => [
                'type' => 'json_schema',
                'json_schema' => [
                    'name' => 'criteria_validation_schema',
                    'schema' => [
                        'type' => 'object',
                        'properties' => [
                            'validations' => [
                                'type' => 'array',
                                'items' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'criterion' => [
                                            'type' => 'string',
                                            'description' => 'The criterion text being validated',
                                        ],
                                        'is_unsuitable' => [
                                            'type' => 'boolean',
                                            'description' => 'Whether the criterion is unsuitable',
                                        ],
                                        'reason' => [
                                            'type' => 'string',
                                            'description' => 'Explanation of why the criterion is unsuitable, if applicable',
                                        ],
                                    ],
                                    'required' => ['criterion', 'is_unsuitable'],
                                ],
                            ],
                        ],
                        'required' => ['validations'],
                    ],
                ],
            ],
        ]);

        $response = json_decode($res->choices[0]->message->content, true);

        // Map the response to include criterion IDs
        $results = [];
        foreach ($response['validations'] ?? [] as $validation) {
            $criterion = $this->criteria->first(function ($item) use ($validation) {
                return $item->criterion === $validation['criterion'];
            });

            if ($criterion) {
                $results[] = [
                    'criterion_id' => $criterion->id,
                    'criterion_text' => $validation['criterion'],
                    'is_unsuitable' => $validation['is_unsuitable'],
                    'reason' => $validation['reason'] ?? null,
                ];
            }
        }

        return $results;
    }
}
