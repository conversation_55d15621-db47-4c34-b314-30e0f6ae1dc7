<?php

namespace App\Classes\Education;

use App\Models\Education;

class EducationHelper
{
    // Lists retrieved from https://abbreviations.yourdictionary.com/articles/degree-abbreviations.html
    const ASSOCIATE_DEGREES = [
        'A.A.' => 'Associate of Arts',
        'A.A.-T.' => 'Associate of Arts for Transfer',
        'A.A.A.' => 'Associate of Applied Arts',
        'A.A.B.' => 'Associate of Applied Business',
        'A.A.S.' => 'Associate of Applied Science',
        'A.A.T.' => ['Associate of Applied Technology', 'Associate of Applied Teaching'],
        'A.B.A.' => 'Associate of Business Administration',
        'A.B.S.' => 'Associate of Baccalaureate Studies',
        'A.E.E.T.' => 'Associate of Electrical Engineering Technology',
        'A.E.' => ['Associate of Electronics', 'Associate of Engineering'],
        'A.Eng.' => 'Associate of Engineering',
        'A.E.S.' => 'Associate of Engineering Science',
        'A.E.T.' => 'Associate of Engineering Technology',
        'A.Eng.T.' => 'Associate of Engineering Technology',
        'A.F.' => 'Associate of Forestry',
        'A.F.A.' => 'Associate of Fine Arts',
        'A.G.' => 'Associate of General Studies',
        'A.I.T.' => 'Associate of Industrial Technology',
        'A.O.S.' => 'Associate of Occupational Studies',
        'A.O.T.' => 'Associate of Occupational Technology',
        'A.P.E.' => 'Associate of Pre-Engineering',
        'A.P.S.' => ['Associate of Political Science', 'Associate of Public Service'],
        'A.S.' => 'Associate of Science',
        'A.S.-T.' => 'Associate of Science for Transfer',
        'A.S.-C.A.D' => 'Associate of Science in Computer Assisted Design',
        'A.S.P.T.' => 'Associate in Physical Therapy',
        'A.P.T' => 'Associate in Physical Therapy',
        'A.T.' => 'Associate of Technology',
        'A.N.' => 'Associate of Nursing',
        'A.D.N.' => 'Associate Degree in Nursing',
        'A.S.N.' => 'Associate of Science in Nursing',
    ];

    const BACHELOR_DEGREES = [
        'A.B.' => 'Bachelor of Arts',
        'B.A.' => 'Bachelor of Arts',
        'B.A.A.' => 'Bachelor of Applied Arts',
        'B.A.B.A.' => 'Bachelor of Arts of Business Administration',
        'B.A.Com.' => 'Bachelor of Arts in Communication',
        'B.Acc.Sci.' => 'Bachelor of Comptrolling',
        'B.Compt.' => 'Bachelor of Comptrolling',
        'B.Acy.' => 'Bachelor of Accountancy',
        'B.Acc.' => 'Bachelor of Accountancy',
        'B.A.E.' => ['Bachelor of Arts in Education', 'Bachelor of Science in Aerospace Engineering'],
        'B.A(Econ)' => 'Bachelor of Arts in Economics',
        'B.A.J.' => 'Bachelor of Arts in Journalism',
        'B.J.' => 'Bachelor of Arts in Journalism',
        'B.A.M' => 'Bachelor of Arts in Music',
        'B.A.Mus.' => 'Bachelor of Arts in Music',
        'B.A.O.M.' => 'Bachelor of Arts in Organizational Management',
        'B.A.P.S.Y.' => 'Bachelor of Arts in Psychology',
        'B.A.S.' => ['Bachelor of Administrative Studies', 'Bachelor of Applied Studies'],
        'B.A.Sc.' => 'Bachelor of Applied Science',
        'B.A.S.W.' => 'Bachelor of Arts in Social Work',
        'B.A.T.' => 'Bachelor of Arts for Teaching',
        'B.Ag' => 'Bachelor of Agriculture',
        'B.App.Sc(IT)' => 'Bachelor of Applied Science in Information Technology',
        'B.Arch.' => 'Bachelor of Architecture',
        'B.Avn.' => 'Bachelor of Aviation',
        'B.B.A.' => 'Bachelor of Business Administration',
        'B.B.I.S.' => 'Bachelor of Business Information Systems',
        'B.Bus.' => 'Bachelor of Business',
        'B.Bus.Sc.' => 'Bachelor of Business Science',
        'B.Ch.E.' => 'Bachelor of Chemical Engineering',
        'B.Com.' => 'Bachelor of Commerce',
        'B.Comm.' => 'Bachelor of Commerce',
        'B.Comp.' => 'Bachelor of Computing',
        'B.Comp.Sc.' => 'Bachelor of Computer Science',
        'B.Crim.' => 'Bachelor of Criminology',
        'B.C.A.' => 'Bachelor of Computer Applications',
        'B.C.E.' => 'Bachelor of Civil Engineering',
        'B.C.J.' => 'Bachelor of Criminal Justice',
        'B.Des.' => 'Bachelor of Design',
        'B.E.' => ['Bachelor of Education', 'Bachelor of Engineering'],
        'B.Ec.' => 'Bachelor of Economics',
        'B.Econ.' => 'Bachelor of Economics',
        'B.E.E.' => 'Bachelor of Electrical Engineering',
        'B.Eng.' => 'Bachelor of Engineering',
        'B.E.Sc.' => 'Bachelor of Engineering Science',
        'B.F.A.' => 'Bachelor of Fine Arts',
        'B.F&TV.' => 'Bachelor of Film and Television',
        'B.G.S.' => 'Bachelor of General Studies',
        'B.S.G.S.' => 'Bachelor of General Studies',
        'B.H.S.' => 'Bachelor of Health Science',
        'B.H.Sc.' => 'Bachelor of Health Science',
        'B.I.B.E.' => 'Bachelor of International Business Economics',
        'B.In.Dsn.' => 'Bachelor of Industrial Design',
        'B.I.S.' => 'Bachelor of Integrated Studies',
        'B.Kin.' => 'Bachelor of Kinesiology',
        'B.Sc.Kin.' => 'Bachelor of Science in Kinesiology',
        'B.L.A.' => 'Bachelor of Liberal Arts',
        'A.L.B.' => 'Bachelor of Liberal Arts',
        'B.L.Arch.' => 'Bachelor of Landscape Architecture',
        'B.L.S.' => ['Bachelor of Liberal Studies', 'Bachelor of Library Science'],
        'B.L.I.S.' => 'Bachelor of Library and Information Science',
        'B.Lib.' => 'Bachelor of Library Science',
        'B.M.' => 'Bachelor of Music',
        'B.Mus.' => 'Bachelor of Music',
        'B.M.E' => 'Bachelor of Music Education',
        'B.M.Ed.' => 'Bachelor of Music Education',
        'B.M.O.S.' => 'Bachelor of Management and Organizational Studies',
        'B.M.S.' => ['Bachelor of Management Studies', 'Bachelor of Mortuary Studies'],
        'B.Math' => 'Bachelor of Mathematics',
        'B.Math.Sc.' => 'Bachelor of Mathematical Science',
        'B.P.A.P.M.' => 'Bachelor of Public Affairs and Policy Management',
        'B.P.S.' => 'Bachelor of Professional Studies',
        'B.Phil' => 'Bachelor of Philosophy',
        'Ph.B.' => 'Bachelor of Philosophy',
        'B.S.' => 'Bachelor of Science',
        'S.B.' => 'Bachelor of Science',
        'B.S.A.E.' => 'Bachelor of Science in Aerospace Engineering',
        'B.S.B.A.' => 'Bachelor of Science in Business Administration',
        'B.S.C.S.' => 'Bachelor of Science in Computer Science',
        'B.S.Chem.' => 'Bachelor of Science in Chemistry',
        'B.S.E.' => ['Bachelor of Science in Engineering', 'Bachelor of Science in Education'],
        'B.S.Eng.' => 'Bachelor of Science in Engineering',
        'B.S.Ed.' => 'Bachelor of Science in Education',
        'B.S.E.T.' => 'Bachelor of Science of Engineering Technology',
        'B.S.F.' => 'Bachelor of Science in Forestry',
        'B.S.M.E.' => 'Bachelor of Science in Mechanical Engineering',
        'B.S.Micr.' => 'Bachelor of Science in Microbiology',
        'B.S.P.H.' => 'Bachelor of Science in Public Health',
        'B.S.S.W.' => 'Bachelor of Science in Social Work',
        'B.Sc.' => 'Intercalculated Bachelor of Science',
        'B.Sc(Econ)' => 'Bachelor of Science in Economics',
        'B.Sc(IT)' => 'Bachelor of Science in Information Technology',
        'B.Sc(Psych)' => 'Bachelor of Science in Psychology',
        'B.Soc.Sc.' => 'Bachelor of Social Science',
        'B.T.S.' => 'Bachelor of Tourist Studies',
        'B.Tech.' => 'Bachelor of Technology',
        'B.U.R.P.' => 'Bachelor of Urban and Regional Planning',
        'B.Plan.' => 'Bachelor of Urban and Regional Planning',
        'B.Med.Sci.' => 'Bachelor of Medical Science',
        'B.Med.Biol.' => 'Bachelor of Medical Biology',
        'B.N.' => 'Bachelor of Nursing',
        'B.Nurs.' => 'Bachelor of Nursing',
        'B.Pharm.' => 'Bachelor of Pharmacy',
        'B.S.N.' => 'Bachelor of Science in Nursing',
        'B.Sc.N.' => 'Bachelor of Science in Nursing',
        'B.S.L.' => 'Bachelor of Science in Law',
        'B.D.' => 'Bachelor of Divinity',
        'B.Div.' => 'Bachelor of Divinity',
        'B.R.E.' => 'Bachelor of Religious Education',
        'B.R.S.' => 'Bachelor of Religious Studies',
        'B.Th.' => 'Bachelor of Theology',
        'B.Theol.' => 'Bachelor of Theology',
        'B.T.L.' => 'Bachelor of Talmudic Law',
    ];


    const MASTER_DEGREES = [
        'M.A.' => 'Master of Arts',
        'A.M.' => 'Master of Arts',
        'M.Acc.' => ['Master of Accounting', 'Master of Accountancy'],
        'M.Acy.' => ['Master of Accounting', 'Master of Accountancy'],
        'M.Arch.' => 'Master of Architecture',
        'M.Aqua.' => 'Master of Aquaculture',
        'M.A.Ed.' => 'Master of Arts in Education',
        'M.A.L.S.' => 'Master of Arts in Liberal Studies',
        'M.L.S.' => 'Master of Arts in Liberal Studies',
        'M.A.S.' => ['Master of Advanced Study', 'Master of Applied Science'],
        'M.A.Sc.' => 'Master of Applied Science',
        'M.A.T.' => 'Master of Arts in Teaching',
        'M.Bus.' => 'Master of Business',
        'M.B.A.' => 'Master of Business Administration',
        'M.B.I.' => 'Master of Business Informatics',
        'M.Chem.' => 'Master of Chemistry',
        'M.Com.' => 'Master of Commerce',
        'M.Comm.' => 'Master of Commerce',
        'M.Crim.' => 'Master of Criminology',
        'M.C.A.' => 'Master of Computer Applications',
        'M.C.D.' => 'Master of Communication Disorders',
        'M.C.F.' => 'Master of Computational Finance',
        'M.C.J.' => 'Master of Criminal Justice',
        'M.C.P.' => 'Master of City Planning',
        'M.C.S.' => 'Master of Computer Science',
        'M.C.T.' => 'Master of Creative Technologies',
        'M.Des.' => 'Master of Design',
        'M.Design' => 'Master of Design',
        'M.E.' => 'Master of Engineering',
        'M.Econ.' => 'Master of Economics',
        'M.Ed.' => 'Master of Education',
        'Ed.M.' => 'Master of Education',
        'M.Ent.' => 'Master of Enterprise',
        'M.E.M.' => 'Master of Engineering Management',
        'M.Fin.' => 'Master of Finance',
        'M.Fstry.' => 'Master of Forestry',
        'M.F.A.' => 'Master of Fine Arts',
        'M.F.E.' => 'Master of Financial Economics',
        'M.H.' => 'Master of Humanities',
        'M.H.A.' => 'Master of Health Administration',
        'M.H.S.' => 'Master of Health Science',
        'M.I.Aff.' => 'Master of International Affairs',
        'M.I.B.' => 'Master of International Business',
        'M.I.L.R.' => 'Master of Industrial and Labor Relations',
        'M.I.S.' => 'Master of International Studies',
        'M.I.S.M.' => 'Master of Information System Management',
        'M.S.I.M' => 'Master of Information System Management',
        'M.I.T.' => 'Master of Information Technology',
        'M.L.A.' => 'Master of Liberal Arts',
        'M.L.Arch.' => 'Master of Landscape Architecture',
        'M.L.I.S.' => 'Master of Library and Information Studies',
        'M.Litt.' => ['Master of Letters', 'Magister Litterarum'],
        'M.M.' => ['Master of Management', 'Master of Music', 'Master of Medicine'],
        'M.Math.' => 'Master of Mathematics',
        'M.Mus.' => 'Master of Music',
        'M.M.F.' => 'Master of Mathematical Finance',
        'M.O.T.' => 'Master of Occupational Therapy',
        'M.P.S.' => ['Master of Political Science', 'Master of Professional Studies'],
        'M.Phil.' => 'Master of Philosophy',
        'M.Phys.' => 'Master of Physics',
        'M.P.A.' => 'Master of Public Administration',
        'M.P.Aff.' => 'Master of Public Affairs',
        'M.P.H.' => 'Master of Public Health',
        'M.P.M.' => 'Master of Public Management',
        'M.P.P.' => 'Master of Public Policy',
        'M.Poli.Sci.' => 'Master of Political Science',
        'M.Q.F.' => 'Master of Quantitative Finance',
        'M.R.' => 'Master of Research',
        'M.Sc.R.' => 'Master of Research',
        'M.R.E.D.' => 'Master of Real Estate Development',
        'M.S.' => ['Master of Science', 'Master of Surgery'],
        'M.Sc.' => 'Master of Science',
        'M.S.C.J.' => 'Master of Science in Criminal Justice',
        'M.S.C.S.' => 'Master of Science in Computer Science',
        'M.C.M.' => 'Master of Clinical Medicine',
        'M.M.S.' => 'Master of Medical Science',
        'M.Med.Sc.' => 'Master of Medical Science',
        'M.N.' => 'Master of Nursing',
        'M.N.A.' => 'Master of Nurse Anesthesia',
        'M.Pharm.' => 'Master of Pharmacy',
        'M.P.A.S.' => 'Master of Physician Assistant Studies',
        'Ch.M.' => 'Master of Surgery',
        'M.S.M.' => ['Master of Science in Medicine', 'Master of Sacred Music'],
        'M.S.N.' => 'Master of Science in Nursing',
        'M.V.S.C.' => 'Master of Veterinary Science',
        'M.V.Sc.' => 'Master of Veterinary Science',
        'M.J.' => 'Master of Jurisprudence',
        'M.Jur.' => 'Master of Jurisprudence',
        'M.S.L.' => 'Master of Studies in Law',
        'M.Div.' => 'Master of Divinity',
        'M.Rb.' => 'Master of Rabbinic Studies',
        'M.R.E.' => 'Master of Religious Education',
        'M.S.T.' => 'Master of Sacred Theology',
        'S.T.M.' => 'Master of Sacred Theology',
        'M.Th.' => 'Master of Theology',
        'Th.M.' => 'Master of Theology',
        'M.T.S.' => 'Master of Theological Studies',
    ];

    const DOCTORATE_DEGREES = [
        'Au.D.' => 'Doctor of Audiology',
        'Art.D.' => 'Doctor of Arts',
        'D.A.' => 'Doctor of Arts',
        'D.Arch.' => 'Doctor of Architecture',
        'D.A.T.' => 'Doctor of Arts in Teaching',
        'D.A.S.' => 'Doctor of Applied Science',
        'D.B.A.' => 'Doctor of Business Administration',
        'D.C.' => 'Doctor of Chiropractic',
        'D.Chem.' => 'Doctor of Chemistry',
        'D.Crim.' => 'Doctor of Criminology',
        'D.C.J.' => 'Doctor of Criminal Justice',
        'D.Des.' => 'Doctor of Design',
        'D.Ed.' => 'Doctor of Education',
        'Ed.D' => 'Doctor of Education',
        'D.Eng.' => 'Doctor of Engineering',
        'D.Env.' => 'Doctor of Environment',
        'D.F.' => 'Doctor of Forestry',
        'D.F.A.' => 'Doctor of Fine Arts',
        'D.G.S.' => 'Doctor of Geological Science',
        'D.H.S.' => ['Doctor of Health and Safety', 'Doctor of Hebrew Studies'],
        'D.I.T.' => 'Doctor of Industrial Technology',
        'D.L.S.' => 'Doctor of Library Science',
        'D.M.' => 'Doctor of Music',
        'D.M.A.' => 'Doctor of Musical Arts',
        'D.M.L.' => 'Doctor of Modern Languages',
        'D.P.A.' => 'Doctor of Public Administration',
        'D.P.E.' => 'Doctor of Physical Education',
        'D.P.H.' => 'Doctor of Public Health',
        'D.P.S.' => 'Doctor of Professional Studies',
        'D.R.' => 'Doctor of Recreation',
        'D.Rec.' => 'Doctor of Recreation',
        'D.Sc.' => 'Doctor of Science',
        'Sc.D.' => 'Doctor of Science',
        'D.Sc.H.' => 'Doctor of Science and Hygiene',
        'D.S.W.' => ['Doctor of Social Welfare', 'Doctor of Social Work'],
        'L.H.D.' => 'Doctor of Humane Letters',
        'Mus.D.' => 'Doctor of Music',
        'Ph.D.' => 'Doctor of Philosophy',
        'S.D' => 'Doctor of Science',
        'S.Sc.D.' => 'Doctor of Social Science',
        'D.C.M.' => ['Doctor of Clinical Medicine', 'Doctor of Church Music'],
        'D.Clin.Surg.' => 'Doctor of Clinical Surgery',
        'D.D.S.' => 'Doctor of Dental Surgery',
        'D.M.D.' => ['Doctor of Dental Medicine', 'Doctor of Medical Dentistry'],
        'D.M.Sc.' => 'Doctor of Medical Science',
        'D.Med.Sc.' => 'Doctor of Medical Science',
        'D.N.Sc.' => 'Doctor of Nursing Science',
        'D.S.' => 'Doctor of Surgery',
        'D.Surg.' => 'Doctor of Surgery',
        'D.Sc.D.' => 'Doctor of Science in Dentistry',
        'D.Sc.V.M.' => 'Doctor of Science in Veterinary Medicine',
        'D.O.' => 'Doctor of Osteopathic Medicine',
        'D.P.T.' => 'Doctor of Physical Therapy',
        'D.S.N.' => 'Doctor of Science in Nursing',
        'D.Sc.PT' => 'Doctor of Science in Physical Therapy',
        'D.S.Sc.' => 'Doctor of Social Science',
        'D.V.M' => 'Doctor of Veterinary Medicine',
        'D.C.L.' => 'Doctor of Civil Law',
        'J.C.D.' => 'Doctor of Canon Law',
        'J.D.' => ['Doctor of Jurisprudence', 'Doctor of Laws'],
        'J.S.D.' => 'Doctor of Juridical Science',
        'S.J.D.' => 'Doctor of Juridical Science',
        'LL.D.' => 'Doctor of Laws',
        'L.Sc.D.' => 'Doctor of Science in Law',
        'D.D.' => 'Doctor of Divinity',
        'D.H.L.' => 'Doctor of Hebrew Literature (or Letters)',
        'D.Th.' => 'Doctor of Theology',
        'Th.D.' => 'Doctor of Theology',
        'D.M.M.' => 'Doctor of Music Ministry',
        'D.Miss.' => 'Doctor of Missology',
        'D.R.E.' => 'Doctor of Religious Education',
        'D.S.M.' => 'Doctor of Sacred Music',
        'D.S.T.' => 'Doctor of Sacred Theology',
        'S.T.D.' => 'Doctor of Sacred Theology',
    ];

    const DEGREE_MAPPINGS = [
        Education::DEGREE_ASSOCIATE => self::ASSOCIATE_DEGREES,
        Education::DEGREE_BA => self::BACHELOR_DEGREES,
        Education::DEGREE_MA => self::MASTER_DEGREES,
        Education::DEGREE_PHD => self::DOCTORATE_DEGREES,
    ];

    public static function findDegreeType(string $name)
    {
        $result = null;
        foreach (self::DEGREE_MAPPINGS as $degreeType => $degreeList) {
            $degree = collect($degreeList)->first(function (string|array $degreeName, string $degreeAbbr) use ($name) {
                if ($degreeAbbr === $name) {
                    return true;
                }
                if (is_array($degreeName)) {
                    return in_array($name, $degreeName);
                }
                return $degreeName === $name;
            });

            if ($degree) {
                $result = $degreeType;
                break;
            }
        }

        if ($result === null) {
            if (stripos($name, 'high school') !== false) {
                $result = Education::DEGREE_SECONDARY;
            } elseif (stripos($name, 'associate') !== false) {
                $result = Education::DEGREE_ASSOCIATE;
            } elseif (stripos($name, 'bachelor') !== false) {
                $result = Education::DEGREE_BA;
            } elseif (stripos($name, 'master') !== false) {
                $result = Education::DEGREE_MA;
            } elseif (stripos($name, 'doctor') !== false) {
                $result = Education::DEGREE_PHD;
            }
        }

        return $result;
    }
}
