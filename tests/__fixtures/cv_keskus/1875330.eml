Received: by dn-68-95.tll01.zoneas.eu (Postfix, from userid 31967)
	id 1482AB41099; Mon, 14 Sep 2020 17:42:54 +0000 (UTC)
Received: from mh3.elkdata.ee (localhost [127.0.0.1])
	by mh3.elkdata.ee (Postfix) with ESMTP id 4AAEA112537
	for <<EMAIL>>; Mon, 14 Sep 2020 20:42:58 +0300 (EEST)
Received: from mh3.elkdata.ee ([127.0.0.1])
	by mh3.elkdata.ee (mh3.elkdata.ee [127.0.0.1]) (amavisd-new, port 10024)
	with ESMTP id xmRHDA1twfH4 for <<EMAIL>>;
	Mon, 14 Sep 2020 20:42:55 +0300 (EEST)
Received: from dn-68-95.tll01.zoneas.eu (dn95.zone.eu [*************])
	by mh3.elkdata.ee (Postfix) with ESMTPS id 2D28111252D
	for <<EMAIL>>; Mon, 14 Sep 2020 20:42:54 +0300 (EEST)
Received: from mh3.elkdata.ee
	by mh3 with LMTP
	id aHGJFaKrX1+3WQEAYOMG6w
	(envelope-from <<EMAIL>>)
	for <<EMAIL>>; Mon, 14 Sep 2020 20:42:58 +0300
Reply-To: "Allen Russ" <<EMAIL>>
From: "CV Keskus" <<EMAIL>>
To: <<EMAIL>>
Subject: =?UTF-8?Q?CV_Keskus:_Kandidaat_Allen_Russ_?=
	=?UTF-8?Q?kandideeris_Teie_t=C3=B6=C3=B6pakkumisele:?=
	=?UTF-8?Q?_KASUTAJATOE_SPETSIALIST?=
Date: Mon, 14 Sep 2020 20:42:54 +0300
Message-ID: <<EMAIL>>
MIME-Version: 1.0
Content-Type: multipart/mixed;
	boundary="----=_NextPart_000_00E6_01D69024.DD605A30"
X-Mailer: Microsoft Outlook 16.0
X-Virus-Scanned: amavisd-new at mh3.elkdata.ee
X-Spam-Flag: NO
X-Spam-Score: 1.521
X-Spam-Level: *
X-Spam-Status: No, score=1.521 tagged_above=-9999 required=5
	tests=[BAYES_50=1.5, HTML_MESSAGE=0.01, SPF_HELO_NONE=0.001,
	T_REMOTE_IMAGE=0.01] autolearn=no autolearn_force=no
Thread-Index: AQI6b6j6BlNWQfLYpJ8eVF7kwwseqw==
X-PHP-Originating-Script: 31967:Mailing.php

This is a multipart message in MIME format.

------=_NextPart_000_00E6_01D69024.DD605A30
Content-Type: multipart/alternative;
	boundary="----=_NextPart_001_00E7_01D69024.DD608140"


------=_NextPart_001_00E7_01D69024.DD608140
Content-Type: text/plain;
	charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

Tere p=C3=A4evast!

Allj=C3=A4rgnev CV on Teile saadetud l=C3=A4bi CV Keskuse =
t=C3=B6=C3=B6portaali.
CV omanik kandideerib Teie ettev=C3=B5tte t=C3=B6=C3=B6kohale =
KASUTAJATOE SPETSIALIST =
<https://www.cvkeskus.ee/view_jobad.php?job_id=3D667151> .



KAASKIRI	=20


Tere

Minu nimi on Allen Russ ja ma soovin kandideerida kasutajatoe =
spetsialisti ametikohale. =C3=9Cpris pikka aega olen tegelenud =
veokorralduse valdkonnas (maantee veod) ja p=C3=B5hiliselt Soome suunal. =
Eks selle t=C3=B6=C3=B6 juures on ka palju suhtlemist klientidega ehk =
kauba saatjate ja saajatega nii, et kvaliteetne klienditeenindus ei ole =
mulle v=C3=B5=C3=B5ras. Inglise suuline keel on minul hetkel n=C3=B6 =
"roostes" v=C3=A4hese kasutuse t=C3=B5ttu aga usun, et  suheldes =
keelekandjaga tuleb see ruttu taas meelde. Kandideerin sellep=C3=A4rast, =
et otsin uusi ning huvitavaid v=C3=A4ljakutseid ja vajan oma =
t=C3=B6=C3=B6s "aeg-ajalt " kodukontori v=C3=B5imalust.=20

Juhul, kui Teil tekib t=C3=A4iendavaid k=C3=BCsimusi, olen meeleldi =
n=C3=B5us nendele vastama CV-es n=C3=A4idatud kontaktidel.

 =20

Lugupidamisega=20

Allen Russ

56887971


Miks Sa soovid RecruitLabi kandideerida?
Kandideerin sellep=C3=A4rast, et otsin uusi ning huvitavaid =
v=C3=A4ljakutseid ja vajan oma t=C3=B6=C3=B6s "aeg-ajalt " kodukontori =
v=C3=B5imalust.=20
Millised on Sinu ootused oma tulevasele t=C3=B6=C3=B6andjale?
Usaldusv=C3=A4=C3=A4rne, koost=C3=B6=C3=B6aldis, toetav.



 <https://www.cvkeskus.ee>  	=09


CV number: 1875330
https://www.cvkeskus.ee/cv/1875330

=09

Allen Russ=20
Otsin t=C3=B6=C3=B6d lao ja/v=C3=B5i veokorralduse valdkonnas.Koheselt =
t=C3=B6=C3=B6leasumise v=C3=B5imalus.
Vanus:	 48a	=20
Sugu:	 mees	=20
Aadress:	Tallinn=09
Telefon:=09
+37256887971
E-post:=09
<EMAIL>
CV uuendamisaeg:	 13. sept 2020	=20
  <https://www.cvkeskus.ee/snap_cv_photos/2020/38/2964067-b3c4a216.jpg>=20

EESM=C3=84RK
Otsin t=C3=B6=C3=B6d lao ja/v=C3=B5i veokorralduse valdkonnas aga olen =
avatud ka k=C3=B5ikidele muu valdkonna pakkumistele( v.a =
m=C3=BC=C3=BCk). V=C3=B5ib ka pakkuda t=C3=B6=C3=B6d kodukontori =
v=C3=B5imalusega. Oman pikaajalist t=C3=B6=C3=B6kogemust =
transpordikorralduses Eesti ja Soome vahel. Arvutiga keskmisel tasemel =
"sina" peal.

SOOVITUD AMETIKOHT
Peamine valik	=20
Kategooria: Transport / Logistika=20
Ametinimetus: laohoidja, kauba vastuv=C3=B5tja, kaubak=C3=A4sitleja, =
logistik, veokorraldaja, ostuassistent=20
T=C3=B6=C3=B6 liik:	 palgat=C3=B6=C3=B6taja, lepinguline, ajutine	=20
T=C3=B6=C3=B6 aeg:	 t=C3=A4ist=C3=B6=C3=B6aeg, osaline t=C3=B6=C3=B6aeg, =
vabakutseline	=20
Soovitud asukoht:	 Tallinn, Kodukontor	=20

T=C3=96=C3=96KOGEMUS
06.2011 - ... 	Hansatraffic O=C3=9C (Eesti) - veokorraldaja Kategooria: =
Transport / Logistika T=C3=B6=C3=B6 kirjeldus: K=C3=B5ik =
=C3=BClessanded, mis on vajalikud, et kaubad liiguks st suhtlemine =
vedajatega, klientidega ning andmesisestus. =09
08.2014 - 01.2017 	Onry (Eesti) - laohoidja Kategooria: Transport / =
Logistika T=C3=B6=C3=B6 kirjeldus: Kauba ladustamine ning =
komplekteerimine. T=C3=B5stukiga kaupade peale ja mahalaadimine =
vastavalt FIFO reeglile. Inventuurides osalemine. Lao korrashoid. =09
02.2007 - 03.2011 	Ferroline O=C3=9C (Eesti) - veokorraldaja Kategooria: =
Transport / Logistika T=C3=B6=C3=B6 kirjeldus: Suhtlemine klientidega, =
vedajatega ja laevaettev=C3=B5tetega korraldamaks kaupade vedu Soome ja =
tagasi. Andmesisestus programmi, arvete v=C3=A4ljastamine ja laekumiste =
kontroll. =09
08.2003 - 01.2007 	AS Ehituslux (Eesti) - Logistik-ostujuht Kategooria: =
M=C3=BC=C3=BCk T=C3=B6=C3=B6 kirjeldus: M=C3=B6=C3=B6bli kaupluste kett. =
M=C3=B6=C3=B6bli import. Suhtlemine tarniatega ja vedajatega, et tagada =
kauba =C3=B5igeaegne j=C3=B5udmine kliendini. P=C3=B5hilised tarniad: =
Soome, Rootsi, Norra, Itaalia ja Saksamaa. =09

HARIDUS
Kutseharidus: 	 	=20
1987 - 1990 	Tallinna Ehituskool, (Eesti) - eriala: ehitustisleri eriala =

=09
1990 - ... 	EV Politseiakadeemia (Eesti) - eriala: =C3=95iguskaitse =
l=C3=B5petamata=20
=09
Keskharidus: 	 	=20
1978 - 1987 	Tallinna 24. Keskkool (Eesti)=20
=09

T=C3=84IENDKOOLITUS
2001 (2 p=C3=A4eva) 	MARU Konsultatsioonikeskus (Eesti) - =
M=C3=BC=C3=BCgioskuste arendamine =09

KEELED
Suhtluskeel:	 eesti	=20
Keel:	 M=C3=B5istmine	 R=C3=A4=C3=A4kimine	 Kirjutamine	=20
Kuulamine	 Lugemine	 Suuline suhtlus	 Suuline esitus	=20
vene	 C2 ? 	C2 ? 	C2 ? 	C2 ? 	C1 ? =09
inglise	 C1 ? 	C1 ? 	B1 ? 	B1 ? 	B1 ? =09
soome	 C1 ? 	B1 ? 	B2 ? 	B1 ? 	B1 ? =09
Tasemed: A1/A2: algtasemel keelekasutaja - B1/B2: iseseisev =
keelekasutaja - C1/C2: vilunud keelekasutaja
Euroopa N=C3=B5ukogu keeleoskuss=C3=BCsteemi enesehindamisskaala =
<http://www.cvkeskus.ee/gfx/est/Europass_eesti_v.pdf> 	=20

ARVUTIOSKUSED
Oskus	 Tase	=20
Kontoritarkvara: =09
MS Excel 	Tavakasutaja	=20
MS Outlook 	Kesktase	=20
MS Word 	Kesktase	=20
Operatsioonis=C3=BCsteemid: =09
Windows 10 	Tavakasutaja	=20
Windows 7 	Tavakasutaja	=20
Veebilehitseja: =09
Google Chrome 	Tavakasutaja	=20
Internet Explorer 	Tavakasutaja	=20
Mozilla Firefox 	Tavakasutaja	=20

AUTOS=C3=95IDUOSKUSED
Juhiload:	 B (1990) =09

LISAINFO
Isikuomadused:=20
Tugevad - Hea suhtleja. Sihikindel. Lojaalne. Kiire =C3=B5ppija. Austan =
v=C3=A4ga head huumorit.
N=C3=B5rgad - n=C3=B6 eluliselt t=C3=A4htsate otsuste puhul kipun vahest =
otsuse vastuv=C3=B5tmisega
venitama st ei suuda teha valikut. Kindlasti on veel midagi aga ju ei =
taha endale tunnistada:)=20
CV Keskus kontakt <mailto:<EMAIL>> 	  	=20


S=C3=B5bralike tervitustega
_____________________________________________

CVKeskus.ee | Eesti populaarseim t=C3=B6=C3=B6portaal
Tel: +************ | E-post: <EMAIL>
_____________________________________________

  =
<https://www.cvkeskus.ee/cvs.php?op=3Dlog_open&cv_id=3Dc6b96bf0baa5da2133=
d84194045debbc&app_id=3Dc0RPZ1FaN3dCUVltMU1Ha05rZ2dQZz09>=20

------=_NextPart_001_00E7_01D69024.DD608140
Content-Type: text/html;
	boundary="--BOUNDARY-6dcebedcd7c43e3798ca1fe99976f1ca--";
	charset="UTF-8"
Content-Transfer-Encoding: quoted-printable

<p>Tere p=C3=A4evast!<br />
<br />
Allj=C3=A4rgnev CV on Teile saadetud l=C3=A4bi CV Keskuse =
t=C3=B6=C3=B6portaali.<br />
CV omanik kandideerib Teie ettev=C3=B5tte t=C3=B6=C3=B6kohale <a =
href=3D"https://www.cvkeskus.ee/view_jobad.php?job_id=3D667151" =
target=3D"_blank">KASUTAJATOE SPETSIALIST</a>.<br />
<br />
<link rel=3D"stylesheet" =
href=3D"https://www.cvkeskus.ee/js/ckeditor/contents.css?1599743731" =
type=3D"text/css" />
<style type=3D"text/css">
body {
    background-color: white !important;
    background-image: none !important;
    font-family: Arial;
    font-size: 11px;
}

ul {
    list-style: disc;
}

ol {
    list-style: decimal;
}
</style>

<table width=3D"100%" border=3D"0" cellspacing=3D"5" cellpadding=3D"1">
  <tr>
    <td class=3D"content"><b>KAASKIRI</b></td>
        <tr>
    <td class=3D"content" style=3D"padding-top:0px"><p>Tere</p>

<p>Minu nimi on Allen Russ ja ma soovin kandideerida kasutajatoe =
spetsialisti ametikohale. =C3=9Cpris pikka aega olen tegelenud =
veokorralduse&nbsp;valdkonnas (maantee veod) ja p=C3=B5hiliselt Soome =
suunal. Eks selle t=C3=B6=C3=B6 juures on ka palju suhtlemist =
klientidega ehk&nbsp;kauba saatjate ja saajatega nii, et kvaliteetne =
klienditeenindus ei ole mulle v=C3=B5=C3=B5ras. Inglise suuline keel on =
minul hetkel&nbsp;n=C3=B6 "roostes" v=C3=A4hese kasutuse t=C3=B5ttu aga =
usun, et
 &nbsp;suheldes&nbsp;keelekandjaga tuleb see ruttu taas =
meelde.&nbsp;Kandideerin sellep=C3=A4rast, et otsin uusi =
ning&nbsp;huvitavaid v=C3=A4ljakutseid ja vajan oma =
t=C3=B6=C3=B6s&nbsp;"aeg-ajalt "&nbsp;kodukontori =
v=C3=B5imalust.&nbsp;</p>

<p>Juhul, kui Teil tekib t=C3=A4iendavaid k=C3=BCsimusi, olen meeleldi =
n=C3=B5us nendele vastama CV-es n=C3=A4idatud kontaktidel.</p>

<p>&nbsp;&nbsp;</p>

<p>Lugupidamisega&nbsp;</p>

<p>Allen Russ</p>

<p>56887971</p></td>
  </tr>
  </table>
<br />
            <dl>
                    <dt>Miks Sa soovid RecruitLabi kandideerida?</dt>
            <dd>Kandideerin sellep=C3=A4rast, et otsin uusi =
ning=C2=A0huvitavaid v=C3=A4ljakutseid ja vajan oma =
t=C3=B6=C3=B6s=C2=A0&quot;aeg-ajalt &quot;=C2=A0kodukontori =
v=C3=B5imalust.=C2=A0</dd>
                                <dt>Millised on Sinu ootused oma =
tulevasele t=C3=B6=C3=B6andjale?</dt>
            <dd>Usaldusv=C3=A4=C3=A4rne, koost=C3=B6=C3=B6aldis, =
toetav.</dd>
                            </dl>
        <br />
<style type=3D"text/css">
    .thumbnails a {
        opacity: 0.8;
        filter: alpha(opacity=3D80);
        display: block;
        float: left;
        padding: 0px 5px 5px 0px;
    }
    .thumbnails a:hover { opacity: 1; filter: alpha(opacity=3D100) }
    .thumbnails a:hover img { border-color: #3f3f3f; }
    .top_border td { border-top: 1px solid #f3f3f3; }
    .ep_skill { text-align: center; padding: 6px; }
    .ep_exp { padding: 6px; }
    .spaced td { border: 1px solid #fff; border-width: 0 1px 1px 0; }
    .spaced td:last-child { border-right: 0; }
    .no-bottom-border { border-bottom: 0 !important; }
    .questionmark { color: #ccc !important; }
    .questionmark:hover { color: #f60 !important; }
    .cluetip_long{ text-align: left; }
</style>
<table cellpadding=3D"10" cellspacing=3D"0" width=3D"630" =
align=3D"center" border=3D"0" class=3D"outer-table">
    <tr>
        <td>
                            <table cellpadding=3D"20" cellspacing=3D"0" =
width=3D"100%" border=3D"0" class=3D"cv-header-table">
                    <tr>
                        <td style=3D"width: 230px;">
                                                            <a =
href=3D"https://www.cvkeskus.ee" target=3D"_blank"><img height=3D"23" =
width=3D"179" border=3D"0" =
src=3D"https://www.cvkeskus.ee/gfx/transl/ee/gfx530.png" /></a>
                           =20
                                                    </td>
                        <td class=3D"links">
                                                    </td>
                    </tr>
                </table>
               =20
            <table cellpadding=3D"20" cellspacing=3D"1" width=3D"100%" =
border=3D"0" class=3D"orange-border-table">
                <tr>
                    <td >
                        <!-- base personal info {{{ -->
                        <table cellpadding=3D"0" cellspacing=3D"0" =
width=3D"100%" border=3D"0">
                            <tr>
                                <td width=3D"100%" valign=3D"top">
                                    <div class=3D"cv-number">CV number: =
1875330<br /><a class=3D"cv-number" =
href=3D"https://www.cvkeskus.ee/cv/1875330">https://www.cvkeskus.ee/cv/18=
75330</a></div>
                                                                        =
<br />
                                </td>
                                                            </tr>
                        </table>
                        <table cellpadding=3D"0" cellspacing=3D"0" =
width=3D"100%" border=3D"0">
                            <tr>
                                <td align=3D"center" colspan=3D"2">
                                                                    =
</td>
                            </tr>
                                                        <tr>
                                <td valign=3D"top" >
                                                                         =
                                   <div class=3D"label" =
style=3D"font-size: 18px;">
                                        Allen Russ                       =
             </div>
                                                                         =
                                       <div>Otsin t=C3=B6=C3=B6d lao =
ja/v=C3=B5i veokorralduse valdkonnas.Koheselt t=C3=B6=C3=B6leasumise =
v=C3=B5imalus.</div>
                                                                        =
<div id=3D"cv_part_21_0">
                                        <table cellpadding=3D"3" =
cellspacing=3D"0" width=3D"100%" border=3D"0" class=3D"list">
                                            <tr>
                                                                         =
                       <td style=3D"border-bottom: 1px solid white" =
class=3D"label" width=3D"140" nowrap=3D"nowrap">Vanus:</td>
                                                <td>48a</td>
                                                                         =
                                                                   </tr>
                                                                         =
               <tr>
                                                <td =
style=3D"border-bottom: 1px solid white" class=3D"label">Sugu:</td>
                                                <td>mees</td>
                                            </tr>
                                                                         =
           </table>
                                        <table cellpadding=3D"3" =
cellspacing=3D"0" width=3D"100%" border=3D"0" class=3D"list"><tr><td =
width=3D"140" style=3D"border-bottom: 1px solid white" class=3D"label" =
valign=3D"top">Aadress:</td><td>Tallinn</td></tr><tr><td =
style=3D"border-bottom: 1px solid white" width=3D"140" =
class=3D"label">Telefon:</td><td><div>+37256887971</div></td></tr><tr><td=
 style=3D"border-bottom: 1px solid white" width=3D"140"
 =
class=3D"label">E-post:</td><td><div><EMAIL></div></td></tr=
></table>                                                                =
                    <table cellpadding=3D"3" cellspacing=3D"0" =
width=3D"100%" border=3D"0" class=3D"list" id=3D"contactsTable">
                                                <tr>
                                                    <td =
style=3D"border-bottom: 1px solid white" width=3D"140" class=3D"label" =
nowrap=3D"nowrap">CV uuendamisaeg:</td>
                                                    <td>13. sept  =
2020</td>
                                                </tr>
                                            </table>
                                                                         =
       </div>
                                </td>
                                                                <td =
width=3D"200" nowrap=3D"nowrap" align=3D"center" valign=3D"top">
                                    <div id=3D"cv_part_22_0">
                                                                         =
       <!-- cv photo {{{ -->
                                                                         =
       <table border=3D"0" style=3D"width: 150px;">
                                            <tr>
                                                <td align=3D"center">
                                                    <img height=3D"150" =
src=3D"https://www.cvkeskus.ee/snap_cv_photos/2020/38/2964067-b3c4a216.jp=
g" style=3D"max-width: 200px" /></div>                                   =
             </td>
                                            </tr>
                                        </table>
                                                                         =
       <!-- }}} -->
                                    </div>
                                </td>
                                                            </tr>
                        </table>
                                                <!-- }}} -->
                    </td>
                </tr>
                <tr id=3D"cv_part_33_0">
                    <td >
                        <!-- target {{{ -->
                                                    <div =
class=3D"label">EESM=C3=84RK</div>
                                                                        =
<div id=3D"cv_part_31_0">Otsin t=C3=B6=C3=B6d lao ja/v=C3=B5i =
veokorralduse valdkonnas aga olen avatud ka k=C3=B5ikidele muu valdkonna =
pakkumistele( v.a m=C3=BC=C3=BCk).  V=C3=B5ib ka pakkuda t=C3=B6=C3=B6d =
kodukontori v=C3=B5imalusega. Oman pikaajalist t=C3=B6=C3=B6kogemust =
transpordikorralduses Eesti ja Soome vahel. Arvutiga keskmisel tasemel =
"sina" peal.</div>
                        <!-- }}} -->
                    </td>
                </tr>
                <tr id=3D"cv_part_43_0">
                    <td >
                        <!-- desired position {{{ -->
                                                    <div =
class=3D"label">SOOVITUD AMETIKOHT</div>
                                                <div =
id=3D"cv_part_41_0">
                            <table cellpadding=3D"3" cellspacing=3D"1" =
width=3D"100%" border=3D"0" class=3D"list">
                                                                    <tr>
                                        <td class=3D"label" =
valign=3D"top" width=3D"140" nowrap=3D"nowrap">Peamine valik</td>
                                        <td width=3D"100%">
                                            <div>
                                                Kategooria: Transport / =
Logistika                                            </div>
                                            <div>
                                                Ametinimetus:
                                                laohoidja, kauba =
vastuv=C3=B5tja, kaubak=C3=A4sitleja, logistik, veokorraldaja, =
ostuassistent                                            </div>
                                        </td>
                                    </tr>
                                                            </table>
                        </div>

                        <div id=3D"cv_part_51_0">
                            <table cellpadding=3D"3" cellspacing=3D"1" =
width=3D"100%" border=3D"0" class=3D"list">
                                                                    <tr>
                                        <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap">T=C3=B6=C3=B6 liik:</td>
                                        <td =
width=3D"100%">palgat=C3=B6=C3=B6taja, lepinguline, ajutine</td>
                                    </tr>
                               =20
                                                                    <tr>
                                        <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap">T=C3=B6=C3=B6 aeg:</td>
                                                                         =
       <td width=3D"100%">t=C3=A4ist=C3=B6=C3=B6aeg, osaline =
t=C3=B6=C3=B6aeg, vabakutseline</td>
                                    </tr>
                               =20
                               =20
                                                                    <tr>
                                        <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap">Soovitud asukoht:</td>
                                        <td width=3D"100%">Tallinn, =
Kodukontor</td>
                                    </tr>
                               =20
                                                            </table>
                        </div>
                    </td>
                </tr>

                                    <tr id=3D"cv_part_83_0">
                        <td >
                                                            <div =
class=3D"label">T=C3=96=C3=96KOGEMUS</div>
                           =20
                            <div id=3D"cv_part_81_0">
                                <table cellpadding=3D"3" =
cellspacing=3D"1" width=3D"100%" border=3D"0" class=3D"list">
                                                                         =
   <tr>
                                            <td class=3D"label" =
nowrap=3D"nowrap" valign=3D"top" width=3D"140">
                                                06.2011 - ...            =
                                </td>
                                            <td width=3D"100%">

                                                                         =
                                                                         =
          <strong>
                                                            Hansatraffic =
O=C3=9C                                                            =
(Eesti)                                                        </strong>
                                                        -
                                                                         =
                               veokorraldaja                             =
                      =20
                                                    <em =
style=3D"display: block;">
                                                        Kategooria:
                                                        Transport / =
Logistika                                                    </em>
                                                                         =
                                                                         =
                                                  <em =
style=3D"display:block">T=C3=B6=C3=B6 kirjeldus: K=C3=B5ik =
=C3=BClessanded, mis on vajalikud, et kaubad liiguks st suhtlemine =
vedajatega, klientidega ning andmesisestus. </em>
                                                                         =
                                                                         =
                                          </td>
                                        </tr>
                                                                         =
   <tr>
                                            <td class=3D"label" =
nowrap=3D"nowrap" valign=3D"top" width=3D"140">
                                                08.2014 - 01.2017        =
                                    </td>
                                            <td width=3D"100%">

                                                                         =
                                                                         =
          <strong>
                                                            Onry         =
                                                   (Eesti)               =
                                         </strong>
                                                        -
                                                                         =
                               laohoidja                                 =
                  =20
                                                    <em =
style=3D"display: block;">
                                                        Kategooria:
                                                        Transport / =
Logistika                                                    </em>
                                                                         =
                                                                         =
                                                  <em =
style=3D"display:block">T=C3=B6=C3=B6 kirjeldus: Kauba ladustamine ning =
komplekteerimine. T=C3=B5stukiga kaupade peale ja mahalaadimine =
vastavalt FIFO reeglile. Inventuurides osalemine. Lao korrashoid. </em>
                                                                         =
                                                                         =
                                          </td>
                                        </tr>
                                                                         =
   <tr>
                                            <td class=3D"label" =
nowrap=3D"nowrap" valign=3D"top" width=3D"140">
                                                02.2007 - 03.2011        =
                                    </td>
                                            <td width=3D"100%">

                                                                         =
                                                                         =
          <strong>
                                                            Ferroline =
O=C3=9C                                                            =
(Eesti)                                                        </strong>
                                                        -
                                                                         =
                               veokorraldaja                             =
                      =20
                                                    <em =
style=3D"display: block;">
                                                        Kategooria:
                                                        Transport / =
Logistika                                                    </em>
                                                                         =
                                                                         =
                                                  <em =
style=3D"display:block">T=C3=B6=C3=B6 kirjeldus: Suhtlemine  =
klientidega, vedajatega ja laevaettev=C3=B5tetega korraldamaks kaupade =
vedu Soome ja tagasi. Andmesisestus programmi, arvete v=C3=A4ljastamine =
ja laekumiste kontroll. </em>
                                                                         =
                                                                         =
                                          </td>
                                        </tr>
                                                                         =
   <tr>
                                            <td class=3D"label" =
nowrap=3D"nowrap" valign=3D"top" width=3D"140">
                                                08.2003 - 01.2007        =
                                    </td>
                                            <td width=3D"100%">

                                                                         =
                                                                         =
          <strong>
                                                            AS Ehituslux =
                                                           (Eesti)       =
                                                 </strong>
                                                        -
                                                                         =
                               Logistik-ostujuht                         =
                          =20
                                                    <em =
style=3D"display: block;">
                                                        Kategooria:
                                                        M=C3=BC=C3=BCk   =
                                                 </em>
                                                                         =
                                                                         =
                                                  <em =
style=3D"display:block">T=C3=B6=C3=B6 kirjeldus: M=C3=B6=C3=B6bli =
kaupluste kett. M=C3=B6=C3=B6bli import. Suhtlemine tarniatega ja =
vedajatega, et tagada kauba =C3=B5igeaegne j=C3=B5udmine kliendini. =
P=C3=B5hilised tarniad: Soome, Rootsi, Norra, Itaalia ja Saksamaa. </em>
                                                                         =
                                                                         =
                                          </td>
                                        </tr>
                                                                    =
</table>
                            </div>
                        </td>
                    </tr>
               =20
                <tr id=3D"cv_part_63_0">
                    <td >
                                                    <div =
class=3D"label">HARIDUS</div>
                                                <div =
id=3D"cv_part_61_0">
                            <table cellpadding=3D"3" cellspacing=3D"1" =
width=3D"100%" border=3D"0" class=3D"list">
                                                                         =
                                                                         =
                          <tr>
                                        <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap" valign=3D"top">
                                            Kutseharidus:
                                        </td>
                                        <td width=3D"100%">&nbsp;</td>
                                    </tr>

                                                                         =
   <tr>
                                            <td class=3D"label" =
valign=3D"top">
                                                1987 - 1990              =
                              </td>
                                            <td>
                                                <strong>
                                                    Tallinna Ehituskool, =
                                                   (Eesti)               =
                                 </strong>

                                               =20
                                                                         =
                                                                         =
                                                  -
                                                                         =
                                   eriala:
                                                        ehitustisleri =
eriala                                                                   =
                                                                         =
                                                           =20
                                               =20
                                               =20
                                                <br />

                                                                         =
                   </td>
                                        </tr>
                                                                         =
   <tr>
                                            <td class=3D"label" =
valign=3D"top">
                                                1990 - ...               =
                             </td>
                                            <td>
                                                <strong>
                                                    EV Politseiakadeemia =
                                                   (Eesti)               =
                                 </strong>

                                               =20
                                                                         =
                                                                         =
                                                  -
                                                                         =
                                   eriala:
                                                        =C3=95iguskaitse =
                                                                         =
                                                                         =
                                                     l=C3=B5petamata
                                               =20
                                               =20
                                                <br />

                                                                         =
                   </td>
                                        </tr>
                                                                         =
                                                                   <tr>
                                        <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap" valign=3D"top">
                                            Keskharidus:
                                        </td>
                                        <td width=3D"100%">&nbsp;</td>
                                    </tr>

                                                                         =
   <tr>
                                            <td class=3D"label" =
valign=3D"top">
                                                1978 - 1987              =
                              </td>
                                            <td>
                                                <strong>
                                                    Tallinna 24. =
Keskkool                                                    (Eesti)      =
                                          </strong>

                                               =20
                                                                         =
                                                                         =
                                             =20
                                               =20
                                               =20
                                                <br />

                                                                         =
                   </td>
                                        </tr>
                                                                         =
                                                           </table>
                        </div>
                    </td>
                </tr>

                                    <tr id=3D"cv_part_73_0">
                        <td >
                            <!-- trainings {{{ -->
                                                            <div =
class=3D"label">T=C3=84IENDKOOLITUS</div>
                                                        <div =
id=3D"cv_part_71_0">
                                <table cellpadding=3D"3" =
cellspacing=3D"1" width=3D"100%" border=3D"0" class=3D"list">
                                                                         =
   <tr>
                                            <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap" valign=3D"top">
                                                2001                     =
                           (2 p=C3=A4eva)                                =
            </td>
                                            <td width=3D"100%" =
style=3D"max-width: 740px;overflow-x: hidden;word-wrap: break-word;">
                                                <strong>
                                                    MARU =
Konsultatsioonikeskus                                                    =
(Eesti)                                                </strong> -
                                                M=C3=BC=C3=BCgioskuste =
arendamine                                                               =
                             </td>
                                        </tr>
                                                                    =
</table>
                            </div>
                        </td>
                    </tr>
               =20
               =20
               =20
               =20
                                    <tr id=3D"cv_part_93_0">
                        <td >
                            <!-- languages {{{ -->
                                                            <div =
class=3D"label">KEELED</div>
                                                        <div =
id=3D"cv_part_91_0">
                                <table cellpadding=3D"3" =
cellspacing=3D"0" width=3D"100%" border=3D"0" class=3D"list">
                                                                         =
   <tr class=3D"spaced">
                                            <td class=3D"label" =
style=3D"min-width:90px">Suhtluskeel:</td>
                                            <td colspan=3D"5">eesti</td>
                                        </tr>
                                                                         =
                                   <tr class=3D"spaced">
                                        <td class=3D"label =
no-bottom-border" rowspan=3D"2">Keel:</td>
                                        <td class=3D"label" =
colspan=3D"2" style=3D"text-align:center">M=C3=B5istmine</td>
                                        <td class=3D"label" =
colspan=3D"2" style=3D"text-align:center">R=C3=A4=C3=A4kimine</td>
                                        <td class=3D"label =
no-bottom-border" style=3D"text-align:center" =
rowspan=3D"2">Kirjutamine</td>
                                    </tr>
                                    <tr class=3D"spaced">
                                        <td class=3D"label =
no-bottom-border" =
style=3D"font-weight:normal;text-align:center;">Kuulamine</td>
                                        <td class=3D"label =
no-bottom-border" =
style=3D"font-weight:normal;text-align:center;">Lugemine</td>
                                        <td class=3D"label =
no-bottom-border" =
style=3D"font-weight:normal;text-align:center;">Suuline suhtlus</td>
                                        <td class=3D"label =
no-bottom-border" =
style=3D"font-weight:normal;text-align:center;">Suuline esitus</td>
                                    </tr>

                                                                         =
   <tr class=3D"top_border">
                                            <td><b>vene</b></td>
                                            <td class=3D"ep_skill">
                                                C2                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Kuulamine - C2|Saan vaevata aru igasugusest k=C3=B5nest, =
olenemata sellest, kus seda esitatakse. Saan aru ka kiirk=C3=B5nest, kui =
mulle antakse pisut aega h=C3=A4=C3=A4ldusviisiga harjumiseks.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                C2                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Lugemine - C2|Saan vaevata aru k=C3=B5igist kirjaliku teksti =
liikidest, sealhulgas abstraktsetest, struktuurilt ja/v=C3=B5i =
keeleliselt keerulistest tekstidest, n=C3=A4iteks k=C3=A4siraamatutest, =
erialastest artiklitest ja ilukirjandusest.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                C2                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Suuline suhtlus - C2|Saan vaevata osaleda igas vestluses ja =
diskussioonis ning oskan kujundlikke ja k=C3=B5nekeelseid =
v=C3=A4ljendeid. Oskan t=C3=A4pselt edasi anda t=C3=A4hendus-varjundeid. =
Vajadusel oskan lausungi =C3=BCmber s=C3=B5nastada, nii et vestluses
 osalejad seda vaevalt m=C3=A4rkavad.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                C2                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Suuline esitus - C2|Oskan esitada selge ja ladusa, kontekstile =
vastavas stiilis kirjelduse v=C3=B5i p=C3=B5hjenduse, millel on =
loogiline =C3=BClesehitus, mis aitab kuulajal m=C3=A4rgata ja meelde =
j=C3=A4tta k=C3=B5ige olulisemat.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                C1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Kirjutamine - C1|Oskan ennast v=C3=A4ljendada selge, h=C3=A4sti =
liigendatud tekstiga, avaldades oma arvamust vajaliku =
p=C3=B5hjalikkusega. Oskan kirjutada kirja, esseed v=C3=B5i aruannet =
keerukal teemal ja esile t=C3=B5sta olulisemat. Oskan lugejast
 l=C3=A4htuvalt kohandada oma stiili.">?</a>
                                                                         =
                   </td>
                                        </tr>

                                                                         =
                                           <tr class=3D"top_border">
                                            <td><b>inglise</b></td>
                                            <td class=3D"ep_skill">
                                                C1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Kuulamine - C1|Saan aru tekstist ka siis, kui see ei ole =
selgelt ja loogiliselt =C3=BCles ehitatud. M=C3=B5istan telesaateid ning =
filme ilma liigse pingutuseta.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                C1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Lugemine - C1|Saan aru pikkadest ja keerulistest tekstidest, =
nii olustikulistest kui ka kirjanduslikest, tajudes nende stilistilist =
erip=C3=A4ra. Saan aru erialastest artiklitest ja pikematest =
tehnilistest juhenditest isegi siis, kui need
 vahetult ei puuduta minu eriala.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Suuline suhtlus - B1|Saan enamasti keelega hakkama riigis, kus =
see on kasutusel. Oskan ettevalmistuseta vestelda tuttaval, huvitaval =
v=C3=B5i olulisel teemal: pere, hobid, t=C3=B6=C3=B6, reisimine ja =
p=C3=A4evas=C3=BCndmused.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Suuline esitus - B1|Oskan lihtsate seostatud lausetega =
kirjeldada kogemusi, s=C3=BCndmusi, unistusi ja kavatsusi. Oskan =
l=C3=BChidalt p=C3=B5hjendada ning selgitada oma seisukohti ja plaane. =
Oskan edasi anda jutu, raamatu ja filmi sisu ning
 kirjeldada muljeid.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Kirjutamine - B1|Oskan koostada lihtsat seostatud teksti =
tuttaval v=C3=B5i mulle huvi pakkuval teemal. Oskan kirjutada isiklikku =
kirja, milles kirjeldan oma kogemusi ja muljeid.">?</a>
                                                                         =
                   </td>
                                        </tr>

                                                                         =
                                           <tr class=3D"top_border">
                                            <td><b>soome</b></td>
                                            <td class=3D"ep_skill">
                                                C1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Kuulamine - C1|Saan aru tekstist ka siis, kui see ei ole =
selgelt ja loogiliselt =C3=BCles ehitatud. M=C3=B5istan telesaateid ning =
filme ilma liigse pingutuseta.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Lugemine - B1|Saan aru tekstidest, mis koosnevad sagedamini =
esinevatest v=C3=B5i minu t=C3=B6=C3=B6ga seotud s=C3=B5nadest. Saan aru =
s=C3=BCndmuste, m=C3=B5tete ja soovide kirjeldusest isiklikes =
kirjades.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B2                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Suuline suhtlus - B2|Oskan vestelda piisavalt spontaanselt ja =
ladusalt, nii et suhtlemine keelt emakeelena k=C3=B5nelevate inimestega =
on t=C3=A4iesti v=C3=B5imalik. Saan aktiivselt osaleda aruteludes =
tuttaval teemal, oskan oma seisukohti
 v=C3=A4ljendada ja p=C3=B5hjendada.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Suuline esitus - B1|Oskan lihtsate seostatud lausetega =
kirjeldada kogemusi, s=C3=BCndmusi, unistusi ja kavatsusi. Oskan =
l=C3=BChidalt p=C3=B5hjendada ning selgitada oma seisukohti ja plaane. =
Oskan edasi anda jutu, raamatu ja filmi sisu ning
 kirjeldada muljeid.">?</a>
                                                                         =
                   </td>
                                            <td class=3D"ep_skill">
                                                B1                       =
                                                                         =
    <a href=3D"#" class=3D"desc desc_long questionmark noprint" =
title=3D"Kirjutamine - B1|Oskan koostada lihtsat seostatud teksti =
tuttaval v=C3=B5i mulle huvi pakkuval teemal. Oskan kirjutada isiklikku =
kirja, milles kirjeldan oma kogemusi ja muljeid.">?</a>
                                                                         =
                   </td>
                                        </tr>

                                                                         =
  =20
                                    <tr>
                                        <td colspan=3D"6" =
style=3D"padding-top:10px;font-size:11px;">Tasemed: A1/A2: algtasemel =
keelekasutaja - B1/B2: iseseisev keelekasutaja - C1/C2: vilunud =
keelekasutaja<br /><A =
href=3D"http://www.cvkeskus.ee/gfx/est/Europass_eesti_v.pdf" =
target=3D"_blank">Euroopa N=C3=B5ukogu keeleoskuss=C3=BCsteemi =
enesehindamisskaala</a></td>
                                    </tr>
                                   =20
                                </table>
                            </div>
                        </td>
                    </tr>
               =20
                                    <tr id=3D"cv_part_103_0">
                        <td >
                            <!-- comp skills {{{ -->
                                                            <div =
class=3D"label">ARVUTIOSKUSED</div>
                                                        <div =
id=3D"cv_part_101_0">
                                <table cellpadding=3D"3" =
cellspacing=3D"1" width=3D"100%" border=3D"0" class=3D"list">
                                    <tr>
                                        <td class=3D"label">Oskus</td>
                                        <td class=3D"label">Tase</td>
                                    </tr>
                                                                         =
                                                                         =
                                                                         =
                                                     <tr>
                                            <td colspan=3D"2">
                                                Kontoritarkvara:
                                            </td>
                                        </tr>
                                                                         =
           <tr>
                                                <td>
                                                    MS Excel             =
                                   </td>
                                                <td>Tavakasutaja</td>
                                            </tr>
                                                                         =
           <tr>
                                                <td>
                                                    MS Outlook           =
                                     </td>
                                                <td>Kesktase</td>
                                            </tr>
                                                                         =
           <tr>
                                                <td>
                                                    MS Word              =
                                  </td>
                                                <td>Kesktase</td>
                                            </tr>
                                                                         =
                                                                         =
                                                                         =
                 <tr>
                                            <td colspan=3D"2">
                                                =
Operatsioonis=C3=BCsteemid:
                                            </td>
                                        </tr>
                                                                         =
           <tr>
                                                <td>
                                                    Windows 10           =
                                     </td>
                                                <td>Tavakasutaja</td>
                                            </tr>
                                                                         =
           <tr>
                                                <td>
                                                    Windows 7            =
                                    </td>
                                                <td>Tavakasutaja</td>
                                            </tr>
                                                                         =
                                                                         =
                                                                         =
                                                                         =
                                                                <tr>
                                            <td colspan=3D"2">
                                                Veebilehitseja:
                                            </td>
                                        </tr>
                                                                         =
           <tr>
                                                <td>
                                                    Google Chrome        =
                                        </td>
                                                <td>Tavakasutaja</td>
                                            </tr>
                                                                         =
           <tr>
                                                <td>
                                                    Internet Explorer    =
                                            </td>
                                                <td>Tavakasutaja</td>
                                            </tr>
                                                                         =
           <tr>
                                                <td>
                                                    Mozilla Firefox      =
                                          </td>
                                                <td>Tavakasutaja</td>
                                            </tr>
                                                                         =
                                                                         =
                                          </table>
                            </div>
                        </td>
                    </tr>
               =20
                                    <tr id=3D"cv_part_113_0">
                        <td >
                            <!-- driving skills {{{ -->
                                                            <div =
class=3D"label">AUTOS=C3=95IDUOSKUSED</div>
                                                        <div =
id=3D"cv_part_111_0">
                                <table cellpadding=3D"3" =
cellspacing=3D"1" width=3D"100%" border=3D"0" class=3D"list">
                                                                         =
                                           <tr>
                                            <td class=3D"label" =
width=3D"140" nowrap=3D"nowrap">Juhiload:</td>
                                            <td width=3D"100%">
                                                B                        =
                                                                         =
                                                                         =
                              (1990)
                                                                         =
                   </td>
                                        </tr>
                                                                         =
                                                                       =
</table>
                            </div>
                        </td>
                    </tr>
               =20
               =20
                                    <tr id=3D"cv_part_163_0">
                        <td >
                            <!-- additional info {{{ -->
                                                            <div =
class=3D"label">LISAINFO</div>
                                                        <div =
id=3D"cv_part_161_0">
                                                                =
Isikuomadused: <br />
Tugevad - Hea suhtleja. Sihikindel. Lojaalne. Kiire =C3=B5ppija. Austan =
v=C3=A4ga head huumorit.<br />
N=C3=B5rgad -  n=C3=B6 eluliselt t=C3=A4htsate otsuste puhul kipun =
vahest otsuse vastuv=C3=B5tmisega<br />
venitama st ei suuda teha valikut. Kindlasti on veel midagi aga ju ei =
taha endale tunnistada:)                            </div>
                        </td>
                    </tr>
               =20
                            </table>

                            <table cellpadding=3D"10" cellspacing=3D"0" =
width=3D"100%" border=3D"0" class=3D"bottom-menu noprint">
                    <tr>
                                                    <td =
nowrap=3D"nowrap"><a href=3D"mailto:<EMAIL>">CV Keskus =
kontakt</a></td>
                       =20
                        <td width=3D"100%">&nbsp;</td>

                                            </tr>
                </table>

                    </td>
    </tr>
</table>

<link rel=3D"stylesheet" type=3D"text/css" =
href=3D"https://www.cvkeskus.ee/static/jquery.cluetip.css?1599743732" />
<script type=3D"text/javascript" =
src=3D"https://www.cvkeskus.ee/js/jquery/jquery.cluetip.min.js?1599743732=
"></script>
<script type=3D"text/javascript" =
src=3D"https://www.cvkeskus.ee/js/jquery/yoxview/yoxview-init.js?15997437=
32"></script>
<script type=3D"text/javascript" =
src=3D"https://www.cvkeskus.ee/js/jquery/yoxview/jquery.yoxview-2.21.min.=
js?1599743732"></script>
<script type=3D"text/javascript" =
src=3D"https://www.cvkeskus.ee/js/jquery/yoxview/jquery.yoxthumbs.js?1599=
743732"></script>
<script type=3D"text/javascript">
<!--

$().ready(function() {
    if ($(".yoxview").length) {
        $(".yoxview").yoxview();
    }
    $('a.desc').cluetip({
        splitTitle: '|',
        width: 270,
        tracking: true
    });
    $('a.desc_long').cluetip({
        splitTitle: '|',
        width: 500,
        cluetipClass: 'default cluetip_long',
        tracking: true
    });
});

function changePhoto(id,cv_id,snap) {
    var id_sel =3D $(".media_link span").attr('id');
    var num_sel =3D $(".media_link span").html();
    id_sel =3D id_sel.substring(3);
    $("#photo_page_"+id_sel).css('background-color','#FFFFFF');
    $("#photo_page_"+id_sel).html('<a href=3D"" =
onclick=3D"changePhoto('+id_sel+','+cv_id+','+snap+'); return =
false;">'+num_sel+'</a>');
    var num_new =3D $("#photo_page_"+id+" a").html();
    $("#photo_page_"+id).css('background-color','#DDDDDD');
    $("#photo_page_"+id).html('<span =
id=3D"sp_'+id+'">'+num_new+'</span>');
    =
$.get('/lib/ajax.php?op=3Dget_cv_photo&photo_id=3D'+id+'&cv_id=3D'+cv_id+=
'&is_snap=3D'+snap, function(data) {
        $('#media_place').html(data);
        if ($(".yoxview").length) {
            $(".yoxview").yoxview();
        }
    });
    return false;
}


//-->
</script>



   =20

<br />
<br />
S=C3=B5bralike tervitustega<br />
_____________________________________________<br />
<br />
CVKeskus.ee | Eesti populaarseim t=C3=B6=C3=B6portaal<br />
Tel: +************ |&nbsp;E-post: <EMAIL><br />
_____________________________________________</p>
<img =
src=3D"https://www.cvkeskus.ee/cvs.php?op=3Dlog_open&cv_id=3Dc6b96bf0baa5=
da2133d84194045debbc&app_id=3Dc0RPZ1FaN3dCUVltMU1Ha05rZ2dQZz09" =
width=3D"0" height=3D"0" />

------=_NextPart_001_00E7_01D69024.DD608140--

------=_NextPart_000_00E6_01D69024.DD605A30
Content-Type: application/pdf;
	name="cv_1875330.pdf"
Content-Transfer-Encoding: base64
Content-Disposition: attachment;
	filename="cv_1875330.pdf"
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------=_NextPart_000_00E6_01D69024.DD605A30--
