<?php

namespace Tests\Feature;

use App\Helpers;
use App\Models\Candidate;
use App\Models\Comment;
use App\Models\Consent;
use App\Models\File;
use App\Models\Team;
use App\Models\Website;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Closure;
use Illuminate\Support\Facades\Artisan;
use Tests\TenantAwareTestCase;

class ImportCsvCandidatesFromRecrurTest extends TenantAwareTestCase
{
    private ?string $fixturePath;
    private ?Team $team;
    private ?Website $website;

    protected function setUp(): void
    {
        parent::setUp();
        $this->fixturePath = base_path('tests/__fixtures/recrur/import-csv');
        $this->website = Helpers::getCurrentWebsite();
        $this->team = Team::factory()->create();
    }

    public function test_data_imported(): void
    {
        Artisan::call('recrur:import-csv-candidates', [
            'directory' => $this->fixturePath,
            '--website_id' => $this->website->id,
            '--no-interaction' => true
        ]);
        Helpers::switchToWebsiteId($this->website->id); // necessitated by TenantAwareCommand

        $this->assertCount(3, Candidate::all());
        $this->assertCount(2, File::all());
        $this->assertCount(1, Comment::all());
        $this->assertCount(2, Consent::all());

        $this->assertAlvyraImported($this->assertDefaultConsentDuration(...));
        $this->assertJolantaImported($this->assertDefaultConsentDuration(...));
        $this->assertGiedreImported($this->assertDefaultConsentDuration(...));
    }

    public function test_data_imported_to_team(): void
    {
        Artisan::call('recrur:import-csv-candidates', [
            'directory' => $this->fixturePath,
            '--website_id' => $this->website->id,
            '--team_id' => $this->team->id,
            '--no-interaction' => true
        ]);
        Helpers::switchToWebsiteId($this->website->id); // necessitated by TenantAwareCommand

        $this->assertCount(3, Candidate::all());
        $this->assertCount(2, File::all());
        $this->assertCount(1, Comment::all());
        $this->assertCount(2, Consent::all());

        $this->assertAlvyraImported($this->assertTeam(...), $this->assertDefaultConsentDuration(...));
        $this->assertJolantaImported($this->assertTeam(...), $this->assertDefaultConsentDuration(...));
        $this->assertGiedreImported($this->assertTeam(...), $this->assertDefaultConsentDuration(...));
    }

    public function test_data_imported_since_date(): void
    {
        Artisan::call('recrur:import-csv-candidates', [
            'directory' => $this->fixturePath,
            '--website_id' => $this->website->id,
            '--candidates_since' => '2023-01-01',
            '--no-interaction' => true
        ]);
        Helpers::switchToWebsiteId($this->website->id); // necessitated by TenantAwareCommand

        $this->assertCount(2, Candidate::all());
        $this->assertCount(2, Consent::all());
        $this->assertCount(1, File::all());
        $this->assertEmpty(Comment::all());

        $this->assertAlvyraImported($this->assertDefaultConsentDuration(...));
        $this->assertJolantaImported($this->assertDefaultConsentDuration(...));
    }

    public function test_data_imported_with_custom_consent_duration(): void
    {
        Artisan::call('recrur:import-csv-candidates', [
            'directory' => $this->fixturePath,
            '--website_id' => $this->website->id,
            '--no-interaction' => true,
            '--consent_duration' => '3 months'
        ]);
        Helpers::switchToWebsiteId($this->website->id); // necessitated by TenantAwareCommand

        $this->assertCount(3, Candidate::all());
        $this->assertCount(2, File::all());
        $this->assertCount(1, Comment::all());
        $this->assertCount(2, Consent::all());

        $assert3MonthConsentDuration = function (Candidate $candidate) {
            $this->assertConsentDuration($candidate, CarbonInterval::months(3));
        };
        $this->assertAlvyraImported($assert3MonthConsentDuration);
        $this->assertJolantaImported($assert3MonthConsentDuration);
        $this->assertGiedreImported($assert3MonthConsentDuration);
    }

    public function assertAlvyraImported(Closure...$extraAssertions): void
    {
        $candidate = Candidate::firstWhere('email', '<EMAIL>');
        $this->assertNotNull($candidate);
        $this->assertEquals('Alvyra Cooper', $candidate->name);
        $this->assertEquals('+372 123456', $candidate->phone);
        $this->assertNull($candidate->country);
        $this->assertNull($candidate->city);
        $this->assertEquals(
            $candidate->created_at->toIsoString(),
            Carbon::parse('2023-01-03 10:42:02.000+00')->toIsoString()
        );

        $this->assertEmpty($candidate->employments);
        $this->assertEmpty($candidate->comments);

        $this->assertCount(1, $candidate->consents);
        $consent = $candidate->consents->first();
        $this->assertEquals(Consent::TYPE_UNTIL_DATE, $consent->consent_type);
        $this->assertTrue($consent->active_until->lte(now()->addYears(3)));
        $this->assertStringStartsWith('Imported from Recrur', $consent->source);
        $this->assertEquals(
            $consent->created_at->toIsoString(),
            Carbon::parse('2022-01-25 09:56:55.000000 +00:00')->toIsoString()
        );

        $this->assertCount(1, $candidate->files);
        $file = $candidate->files->first();
        $this->assertEquals(File::TYPE_CANDIDATE_PHOTO, $file->type);
        $this->assertStringContainsString('document-of-alvyra-cooper.jpeg', $file->display_name);
        $this->assertNull($file->project);
        $this->assertEquals(
            $file->created_at->toIsoString(),
            Carbon::parse('2024-01-24 17:42:04.000000+00')->toIsoString()
        );

        foreach ($extraAssertions as $assertion) {
            $assertion($candidate);
        }
    }

    public function assertJolantaImported(Closure...$extraAssertions): void
    {
        $candidate = Candidate::firstWhere('email', '<EMAIL>');
        $this->assertNotNull($candidate);
        $this->assertEquals('Jolanta Baker', $candidate->name);
        $this->assertEquals('000000000', $candidate->phone);
        $this->assertNull($candidate->country);
        $this->assertNull($candidate->city);
        $this->assertEquals(
            $candidate->created_at->toIsoString(),
            Carbon::parse('2024-01-03 10:42:04.000+00')->toIsoString()
        );

        $this->assertEmpty($candidate->files);
        $this->assertEmpty($candidate->comments);
        $this->assertEmpty($candidate->employments);

        $this->assertCount(1, $candidate->consents);
        $consent = $candidate->consents->first();
        $this->assertEquals(Consent::TYPE_UNTIL_DATE, $consent->consent_type);
        $this->assertTrue($consent->active_until->lte(now()->addYears(3)));
        $this->assertStringStartsWith('Imported from Recrur', $consent->source);
        $this->assertEquals(
            $consent->created_at->toIsoString(),
            Carbon::parse('2022-02-07 11:10:13.000000 +00:00')->toIsoString()
        );

        foreach ($extraAssertions as $assertion) {
            $assertion($candidate);
        }
    }

    public function assertGiedreImported(Closure...$extraAssertions): void
    {
        $candidate = Candidate::firstWhere('email', '<EMAIL>');
        $this->assertEquals('Giedrė Smith', $candidate->name);
        $this->assertEquals('123 456 7', $candidate->phone);
        $this->assertEquals('Zambia', $candidate->country);
        $this->assertEquals('Lusaka', $candidate->city);
        $this->assertEquals(
            $candidate->created_at->toIsoString(),
            Carbon::parse('2022-01-03 10:22:03.000+00')->toIsoString()
        );

        $this->assertCount(1, $candidate->files);
        $file = $candidate->files->first();
        $this->assertEquals(File::TYPE_CV, $file->type);
        $this->assertStringContainsString('document-of-giedre-smith.docx', $file->display_name);
        $this->assertNull($file->project);
        $this->assertEquals(
            $file->created_at->toIsoString(),
            Carbon::parse('2024-01-24 17:42:04.000000+00')->toIsoString()
        );

        $this->assertCount(1, $candidate->employments);
        $employment = $candidate->employments->first();
        $this->assertEquals('Developer', $employment->position_title);
        $this->assertEquals('Amazon', $employment->employer_name);
        $this->assertEquals(
            $employment->created_at->toIsoString(),
            Carbon::parse('2022-01-03 10:22:03.000+00')->toIsoString()
        );

        $this->assertCount(1, $candidate->comments);
        $comment = $candidate->comments->first();
        $this->assertEquals('this is a comment on Giedrė Smith', $comment->content);
        $this->assertTrue($comment->is_public);
        $this->assertNull($comment->user);
        $this->assertNull($comment->project);
        $this->assertEquals(
            $comment->created_at->toIsoString(),
            Carbon::parse('2022-02-08 13:02:17.000000 +00:00')->toIsoString()
        );

        $this->assertEmpty($candidate->consents);

        foreach ($extraAssertions as $assertion) {
            $assertion($candidate);
        }
    }

    private function assertTeam(Candidate $candidate): void
    {
        $this->assertEquals($this->team->id, $candidate->team_id);
        $candidate->comments->each(function (Comment $comment) {
            $this->assertEquals($this->team->id, $comment->team_id);
        });
    }

    private function assertConsentDuration(Candidate $candidate, CarbonInterval $duration): void
    {
        $candidate->consents->each(function (Consent $consent) use ($duration) {
            $expectedRange = CarbonPeriod::create(
                $consent->created_at->add($duration->subMinutes(1)),
                $consent->created_at->add($duration->addMinutes(1)),
            );
            $this->assertNotNull($consent->active_until);
            $this->assertTrue($expectedRange->contains($consent->active_until));
        });
    }

    private function assertDefaultConsentDuration(Candidate $candidate): void
    {
        $this->assertConsentDuration($candidate, CarbonInterval::years(2));
    }
}

