<?php

namespace Tests\Feature;

use App\Helpers;
use App\Models\Candidate;
use App\Models\File;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Team;
use App\Models\User;
use App\Models\Website;
use App\Services\Teams\TeamGraphUtils;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Tests\FakeMailer;
use Tests\TenantAwareTestCase;

class AccessControlTest extends TenantAwareTestCase
{
    use FakeMailer;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake(); // Disable notifications from going out
    }

    /**
     * @test
     */
    public function user_can_access_private_self_managed_project()
    {
        $allowed = User::factory()->create();
        $forbidden = User::factory()->create();

        $project = Project::factory()->create([
            'accessible_only_members' => true,
            'project_manager_id' => $allowed->id,
        ]);
        $this->projectAccessAsserter($project, $allowed, $forbidden);
    }

    /**
     * @test
     *
     * @return void
     */
    public function user_can_access_public_project()
    {
        $allowed = User::factory()->create();
        $forbidden = User::factory()->create(['role' => User::ROLE_LIMITED]);

        $project = Project::factory()->create();
        $this->projectAccessAsserter($project, $allowed, $forbidden);
    }

    /**
     * @test
     *
     * @return void
     */
    public function user_can_access_private_project_with_membership()
    {
        $allowed = User::factory()->create();
        $forbidden = User::factory()->create();

        $project = Project::factory()->create([
            'accessible_only_members' => true,
        ]);
        $project->users()->save($allowed);
        $this->projectAccessAsserter($project, $allowed, $forbidden);
    }

    /**
     * @test
     *
     * @return void
     */
    public function user_can_access_through_own_team()
    {
        $this->enableTeamsFeature();
        $a = Team::create(['name' => 'A']);
        $b = Team::create(['name' => 'B']);

        DB::connection('tenant')
            ->table('team_edges')
            ->insert(['source_team_id' => $a->id, 'target_team_id' => $b->id]);

        $allowed = User::factory()->create([
            'team_id' => $a->id,
        ]);
        $forbidden = User::factory()->create([
            'team_id' => $b->id,
        ]);

        $project = Project::factory()->create([
            'team_id' => $a->id,
        ]);

        $this->projectAccessAsserter($project, $allowed, $forbidden);
    }

    /**
     * @test
     *
     * @return void
     */
    public function user_can_access_through_descendant_team()
    {
        $this->enableTeamsFeature();
        $a = Team::create(['name' => 'A']);
        $b = Team::create(['name' => 'B']);

        DB::connection('tenant')
            ->table('team_edges')
            ->insert(['source_team_id' => $a->id, 'target_team_id' => $b->id]);

        $allowed = User::factory()->create([
            'team_id' => $a->id,
        ]);
        $allowed2 = User::factory()->create([
            'team_id' => $b->id,
        ]);

        $project = Project::factory()->create([
            'team_id' => $b->id,
        ]);

        $this->projectAccessAsserter($project, $allowed);
        $this->projectAccessAsserter($project, $allowed2);
    }

    /**
     * @test
     *
     * @return void
     */
    public function user_can_access_project_despite_teams_if_given_explicit_access()
    {
        $this->enableTeamsFeature();
        $a = Team::create(['name' => 'A']);
        $b = Team::create(['name' => 'B']);
        $c = Team::create(['name' => 'B']);

        DB::connection('tenant')
            ->table('team_edges')
            ->insert(['source_team_id' => $a->id, 'target_team_id' => $b->id]);

        DB::connection('tenant')
            ->table('team_edges')
            ->insert(['source_team_id' => $b->id, 'target_team_id' => $c->id]);

        $allowed = User::factory()->create([
            'team_id' => $a->id,
        ]);

        $hasExplicitAccessUser = User::factory()->create([
            'team_id' => $c->id,
        ]);

        /** @var Project $project */
        $project = Project::factory()->create([
            'team_id' => $b->id,
            'project_manager_id' => $allowed->id,
        ]);
        $project->users()->save($hasExplicitAccessUser);

        $this->projectAccessAsserter($project, $allowed);
        $this->projectAccessAsserter($project, $hasExplicitAccessUser);
    }

    /**
     * @test
     *
     * @return void
     */
    public function anyone_can_access_non_team_project()
    {
        $this->enableTeamsFeature();
        $a = Team::create(['name' => 'A']);
        $b = Team::create(['name' => 'B']);

        DB::connection('tenant')
            ->table('team_edges')
            ->insert(['source_team_id' => $a->id, 'target_team_id' => $b->id]);

        $allowed = User::factory()->create([
            'team_id' => $a->id,
        ]);
        $allowed2 = User::factory()->create([
            'team_id' => $b->id,
        ]);

        $project = Project::factory()->create();

        $this->projectAccessAsserter($project, $allowed);
        $this->projectAccessAsserter($project, $allowed2);
    }

    /**
     * @return void
     *
     * @test
     */
    public function non_teamed_users_dont_access_team_projects()
    {
        $this->enableTeamsFeature();
        $a = Team::create(['name' => 'A']);
        $b = Team::create(['name' => 'B']);

        DB::connection('tenant')
            ->table('team_edges')
            ->insert(['source_team_id' => $a->id, 'target_team_id' => $b->id]);

        $allowed = User::factory()->create([
            'team_id' => $a->id,
        ]);
        $forbidden = User::factory()->create();

        $project = Project::factory()->create([
            'team_id' => $b->id,
        ]);

        $this->projectAccessAsserter($project, $allowed, $forbidden);
    }

    private function projectAccessAsserter(Project $project, ?User $allowedUser = null, ?User $forbiddenUser = null)
    {
        if ($allowedUser) {
            Helpers::runAsUser(function () use ($project) {
                $p = Project::find($project->id);
                $this->assertNotNull($p);
            }, $allowedUser);
        }

        if ($forbiddenUser) {
            Helpers::runAsUser(function () use ($project) {
                $p = Project::find($project->id);
                $this->assertNull($p);
            }, $forbiddenUser);
        }
    }

    private function candidateAccessAsserter(User $user, Candidate $candidate, bool $shouldHaveAccess)
    {
        Helpers::runAsUser(function () use ($candidate, $shouldHaveAccess) {
            if ($shouldHaveAccess) {
                $c = Candidate::find($candidate->id);
                $this->assertNotNull($c);
            } else {
                $c = Candidate::find($candidate->id);
                $this->assertNull($c);
            }
        }, $user);
    }

    /**
     * @test
     */
    public function candidate_added_to_private_project_inherits_access_controls()
    {
        $userA = User::factory()->create();
        $userB = User::factory()->create();

        $privateCandidate = Candidate::factory()->create();
        $publicCandidate = Candidate::factory()->create();

        /** @var Project $project */
        $project = Project::factory()->create([
            'accessible_only_members' => true,
            'project_manager_id' => $userA->id,
        ]);

        $project->stages[0]->addCandidate($privateCandidate);

        $this->actingAs($userA)
            ->get(route('candidates.show', $privateCandidate))
            ->assertOk();

        $this->actingAs($userA)
            ->get(route('candidates.show', $publicCandidate))
            ->assertOk();

        $this->actingAs($userB)
            ->get(route('candidates.show', $privateCandidate))
            ->assertStatus(404);

        /** @var Project $project2 */
        $project2 = Project::factory()->create(['project_manager_id' => $userA->id]);

        $project2->stages[0]->addCandidate($privateCandidate);

        $this->actingAs($userA)
            ->get(route('candidates.show', $privateCandidate))
            ->assertOk();

        $this->actingAs($userB)
            ->get(route('candidates.show', $privateCandidate))
            ->assertOk();
    }

    /**
     * @test
     */
    public function it_can_detect_cycles()
    {
        $this->enableTeamsFeature();

        $user = User::factory()->create();

        $aTeam = Team::query()->create(['name' => 'A']);
        $bTeam = Team::query()->create(['name' => 'B']);
        $cTeam = Team::query()->create(['name' => 'C']);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => 1,
            'target_team_id' => 2,
        ]);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => 1,
            'target_team_id' => 3,
        ]);

        $svc = new TeamGraphUtils;

        $graph = $svc->buildGraph();

        $this->assertFalse($svc->hasCycles($graph));

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => 3,
            'target_team_id' => 1,
        ]);

        $graph = $svc->buildGraph();
        $this->assertTrue($svc->hasCycles($graph));
    }

    /**
     * @test
     */
    public function it_can_get_teams()
    {
        $this->enableTeamsFeature();

        /** @var Team $A */
        $A = Team::query()->create(['name' => 'A']);
        $B = Team::query()->create(['name' => 'B']);
        $C = Team::query()->create(['name' => 'C']);
        $D = Team::query()->create(['name' => 'D']);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $A->id,
            'target_team_id' => $B->id,
        ]);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $B->id,
            'target_team_id' => $C->id,
        ]);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $C->id,
            'target_team_id' => $D->id,
        ]);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $A->id,
            'target_team_id' => $D->id,
        ]);

        $user = User::factory()->create(['team_id' => 1]);

        $svc = new TeamGraphUtils;

        $graph = $svc->buildGraph();
        $accessibleTeams = $svc->getAccessibleTeams($graph, $A->id);
        $this->assertCount(4, $accessibleTeams);
    }

    /**
     * @test
     */
    public function user_can_add_users_to_visible_project()
    {
        $this->useFakeMailer();
        $user = User::factory()->create();
        $project = Project::factory()->create();

        $res = $this->actingAs($user)->get('/form/AddUsersToProjectForm')->getContent();
        $key = json_decode($res)->key;

        $this->actingAs($user)
            ->post('/laraform/process', [
                'key' => $key,
                'data' => [
                    'project_id' => $project->id,
                    'role' => User::ROLE_LIMITED,
                    'emails' => ['<EMAIL>'],
                ],
                'ctx' => ['project_id' => $project->id],
            ])
            ->assertOk();

        $user = User::query()->whereEmail('<EMAIL>')->first();

        $this->assertNotNull($user);
        $this->assertEquals(User::ROLE_LIMITED, $user->role);
    }

    /**
     * @test
     */
    public function user_can_not_add_users_to_private_project()
    {
        $allowed = User::factory()->create();
        $forbidden = User::factory()->create();

        $project = Project::factory()->create([
            'accessible_only_members' => true,
            'project_manager_id' => $allowed->id,
        ]);

        $res = $this->actingAs($forbidden)->get('/form/AddUsersToProjectForm')->getContent();
        $key = json_decode($res)->key;

        $this->actingAs($forbidden)
            ->post('/laraform/process', [
                'key' => $key,
                'data' => [
                    'project_id' => $project->id,
                    'role' => User::ROLE_LIMITED,
                    'emails' => ['<EMAIL>'],
                ],
                'ctx' => ['project_id' => $project->id],
            ])
            ->assertNotFound();

        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    /**
     * @test
     */
    public function user_can_add_users_to_private_project_with_membership()
    {
        $this->useFakeMailer();

        $allowed = User::factory()->create();

        $project = Project::factory()->create([
            'accessible_only_members' => true,
            'project_manager_id' => $allowed->id,
        ]);

        $res = $this->actingAs($allowed)->get('/form/AddUsersToProjectForm')->getContent();
        $key = json_decode($res)->key;

        $this->actingAs($allowed)
            ->post('/laraform/process', [
                'key' => $key,
                'data' => [
                    'project_id' => $project->id,
                    'role' => User::ROLE_LIMITED,
                    'emails' => ['<EMAIL>'],
                ],
                'ctx' => ['project_id' => $project->id],
            ])
            ->assertOk();

        $user = User::query()->whereEmail('<EMAIL>')->first();

        $this->assertNotNull($user);
        $this->assertEquals(User::ROLE_LIMITED, $user->role);
    }

    /**
     * @test
     */
    public function user_can_not_add_admin_users_to_project()
    {
        $user = User::factory()->create();
        $project = Project::factory()->create();

        $res = $this->actingAs($user)->get('/form/AddUsersToProjectForm')->getContent();
        $key = json_decode($res)->key;

        $res = $this->actingAs($user)
            ->post('/laraform/process', [
                'key' => $key,
                'data' => [
                    'project_id' => $project->id,
                    'role' => User::ROLE_ADMIN,
                    'emails' => ['<EMAIL>'],
                ],
                'ctx' => ['project_id' => $project->id],
            ]);
        $res->assertOk();
        $responseJson = $res->json();
        $this->assertEquals('fail', $responseJson['status']);
        $this->assertEquals('The selected role is invalid.', $responseJson['messages'][0]);

        $this->assertDatabaseMissing('users', ['email' => '<EMAIL>']);
    }

    public function enableTeamsFeature(): void
    {
        $website = Helpers::getCurrentWebsite();
        $features = $website->features;
        $features[Website::FEATURE_KEY_TEAMS] = true;
        $website->features = $features;
        $website->save();
    }

    /**
     * @test
     */
    public function limited_user_can_access_candidate_files_only_related_to_project()
    {
        $admin = User::factory()->create();
        $limitedUser = User::factory()->create(['role' => User::ROLE_LIMITED]);
        $limitedUserWithProject1 = User::factory()->create(['role' => User::ROLE_LIMITED]);
        $limitedUserWithProject2 = User::factory()->create(['role' => User::ROLE_LIMITED]);
        $limitedUserWithProject1And2 = User::factory()->create(['role' => User::ROLE_LIMITED]);
        $limitedUserWithProject3 = User::factory()->create(['role' => User::ROLE_LIMITED]);

        $project1 = Project::factory()->create([
            'project_manager_id' => $admin->id,
        ]);
        $project1->users()->saveMany([$limitedUserWithProject1, $limitedUserWithProject1And2]);

        $project2 = Project::factory()->create([
            'project_manager_id' => $admin->id,
        ]);
        $project2->users()->saveMany([$limitedUserWithProject2, $limitedUserWithProject1And2]);

        $project3 = Project::factory()->create([
            'project_manager_id' => $admin->id,
        ]);
        $project3->users()->save($limitedUserWithProject3);

        $candidate = Candidate::factory()->create();

        $project1->stages[0]->addCandidate($candidate);
        $project1->save();
        $this->assertEquals(1, $project1->stages[0]->candidates()->count());

        $project2->stages[0]->addCandidate($candidate);
        $project2->save();
        $this->assertEquals(1, $project2->stages[0]->candidates()->count());

        $project3->stages[0]->addCandidate($candidate);
        $project3->save();
        $this->assertEquals(1, $project3->stages[0]->candidates()->count());

        $file1 = File::factory()->cv()->create(['fileable_id' => $candidate->id]);
        $file2 = File::factory()->cv()->create(['fileable_id' => $candidate->id]);
        $file3 = File::factory()->cv()->create(['fileable_id' => $candidate->id, 'project_id' => $project1->id]);
        $file4 = File::factory()->cv()->create(['fileable_id' => $candidate->id, 'project_id' => $project2->id]);

        $candidate->files()->save($file1);
        $candidate->files()->save($file2);
        $candidate->files()->save($file3);
        $candidate->files()->save($file4);
        $this->assertEquals(4, $candidate->files()->count());

        $res = $this->actingAs($admin)
            ->get("candidates/{$candidate->id}/full?project_id{$project1->id}")->getContent();
        $parsed = json_decode($res);
        $this->assertCount(4, $parsed->files);

        $res = $this->actingAs($limitedUser)
            ->get("candidates/{$candidate->id}/full");
        $res->assertNotFound();

        $res = $this->actingAs($limitedUserWithProject1)
            ->get("candidates/{$candidate->id}/full?project_id{$project1->id}")->getContent();
        $parsed = json_decode($res);
        $this->assertCount(1, $parsed->files);

        $res = $this->actingAs($limitedUserWithProject2)
            ->get("candidates/{$candidate->id}/full?project_id{$project2->id}")->getContent();
        $parsed = json_decode($res);
        $this->assertCount(1, $parsed->files);

        $res = $this->actingAs($limitedUserWithProject1And2)
            ->get("candidates/{$candidate->id}/full?project_id{$project1->id}")->getContent();
        $parsed = json_decode($res);
        $this->assertCount(2, $parsed->files);

        $res = $this->actingAs($limitedUserWithProject3)
            ->get("candidates/{$candidate->id}/full?project_id{$project3->id}")->getContent();
        $parsed = json_decode($res);
        $this->assertCount(0, $parsed->files);
    }

    public function test_orphan_candidates_are_inaccessible_with_inherited_access()
    {
        Setting::set(Setting::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT, true);

        $user = User::factory()->create([
            'role' => User::ROLE_REGULAR,
        ]);

        $candidate = Candidate::factory()->create();

        $this->candidateAccessAsserter($user, $candidate, false);
    }

    public function test_orphan_candidates_are_accessible_without_inherited_access()
    {
        Setting::set(Setting::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT, false);

        $user = User::factory()->create([
            'role' => User::ROLE_REGULAR,
        ]);

        $candidate = Candidate::factory()->create();

        $this->candidateAccessAsserter($user, $candidate, true);
    }

    public function test_inherited_access_works()
    {
        $this->enableTeamsFeature();
        Setting::set(Setting::KEY_ALWAYS_INHERIT_CANDIDATE_ACCESS_FROM_PROJECT, true);

        $teamA = Team::factory()->create();
        $teamB = Team::factory()->create();

        $user = User::factory()->create([
            'role' => User::ROLE_REGULAR,
            'team_id' => $teamA->id,
        ]);

        $user2 = User::factory()->create([
            'role' => User::ROLE_REGULAR,
            'team_id' => $teamB->id,
        ]);

        $project = Project::factory()->create([
            'team_id' => $teamA->id,
        ]);

        $candidate = Candidate::factory()->create();
        $project->stages[0]->addCandidate($candidate);

        $this->candidateAccessAsserter($user, $candidate, true);
        $this->candidateAccessAsserter($user2, $candidate, false);
    }
}
