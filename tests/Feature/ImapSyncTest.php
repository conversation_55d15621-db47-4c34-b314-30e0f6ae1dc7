<?php

namespace Tests\Feature;

use App\DataExchange\ImapMessage\Client;
use App\DataExchange\ImapMessage\Importer;
use App\DataExchange\Mailbox\MailboxSearchParams;
use App\Models\Candidate;
use App\Models\Integration;
use Illuminate\Support\Facades\Log;
use PhpMimeMailParser\Parser;
use Tests\TenantAwareTestCase;

class ImapSyncTest extends TenantAwareTestCase
{
    /**
     * @test
     */
    public function it_skips_when_unknown_message()
    {
        $mock = $this->getMockClient();

        $mock->shouldReceive('getMessageIds')->andReturn([13]);

        $mock->shouldReceive('getMessage')->andReturn(
            (new Parser)->setText(file_get_contents(base_path('tests/__fixtures/imap_messages/simas_13.eml')))
        );

        $importer = new Importer($mock);

        Log::shouldReceive('info')->with('Started mailbox sync')->once();
        Log::shouldReceive('info')->with('ImapImport: could not find candidate!')->once();
        Log::shouldReceive('info')->with('Finished mailbox sync')->once();
        Log::shouldReceive('info')->times(3); // Temporary logging fix for memory debug

        $importer->import();
    }

    /**
     * @test
     */
    public function it_saves_message_if_candidate_exists()
    {
        $mock = $this->getMockClient();

        $candidate = Candidate::firstOrCreateFromEmail('<EMAIL>');

        $this->assertEquals(0, $candidate->messages()->count());

        $mock->shouldReceive('getMessageIds')->andReturn([13]);

        $mock->shouldReceive('getMessage')->andReturn(
            (new Parser)->setText(file_get_contents(base_path('tests/__fixtures/imap_messages/simas_13.eml')))
        );

        $importer = new Importer($mock);
        $importer->import();

        $this->assertEquals(1, $candidate->messages()->count());

        $importer->import();

        $this->assertEquals(1, $candidate->messages()->count());

        $message = $candidate->messages()->first();
    }

    /**
     * @return \Mockery\Mock
     */
    private function getMockClient()
    {
        $integration = Integration::query()->create([
            'name' => 'Imap test',
            'remote_type' => Integration::TYPE_IMAP_MESSAGES,
            'imap_path' => 'valid.path',
        ]);

        //        /** @var Client $mock */
        $mock = \Mockery::mock(
            Client::class,
            [$integration]
        )->makePartial();

        return $mock;
    }

    /**
     * @test
     */
    public function it_builds_ms_graph_urls()
    {
        $integration = Integration::query()->create([
            'name' => 'Imap test',
            'remote_type' => Integration::TYPE_CV_KESKUS_OFFICE,
            'imap_path' => 'valid.path',
        ]);

        $client = new \App\DataExchange\MSGraphMessageSync\Client($integration);

        $this->assertEquals(
            "includeHiddenFolders=true&\$filter=from/emailAddress/address eq '<EMAIL>'&\$top=30&\$skip=0",
            $client->buildQueryString(
                (new MailboxSearchParams)
//                ->setSince(now()->subWeek())
                    ->setFrom('<EMAIL>'),
                0,
                30,
            )
        );
    }
}
