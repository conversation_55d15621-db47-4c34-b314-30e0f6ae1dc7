<?php

namespace Tests\Feature;

use App\Classes\CVParser\CVParserMsgPy;
use App\Classes\CVParser\CVParserWord;
use App\Helpers;
use App\Models\Candidate;
use App\Models\File;
use App\Models\User;
use App\Models\Website;
use App\Services\AI\HasOpenAIClient;
use App\Services\AI\OpenAICVExtractor;
use App\Models\Project;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Queue;
use Tests\TenantAwareTestCase;

class CandidateUploadTest extends TenantAwareTestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        $website = Helpers::getCurrentWebsite();
        $features = $website->features;
        $features[Website::FEATURE_ENABLE_AI] = false;
        $website->features = $features;
        $website->save();
    }

    /**
     * @test
     */
    public function it_handles_uploaded_eml_files()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
            'status' => Project::STATUS_IN_PROGRESS,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/cv_keskus/1521279.eml'), '1521279.eml');

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals(1, $candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->count());
        $this->assertEquals(0, $candidate->cvs()->count());
        $this->assertEquals('Aleksei Nikiforov', $candidate->name);
        $this->assertEquals('<EMAIL>', $candidate->email);

        $this->assertEquals($candidate->id, $res->json()['candidate']['id']);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $res->json()['file']['id']);
        $this->assertNotNull($candidate->fresh()->getActiveConsent());

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();

        $this->assertEquals(1, $candidate->messages()->count());
        $this->assertStringContainsString('Alljärgnev CV on Teile saadetud läbi CV Keskuse tööportaali.', $candidate->messages()->first()->body);

        $this->assertDatabaseCount('activities', 1);
    }

    /**
     * @test
     */
    public function it_does_not_create_duplicates_when_candidate_exists_as_private()
    {
        //<EMAIL>
        Queue::fake();
        Candidate::create(['email' => '<EMAIL>', 'is_private' => 1]);

        $this->assertEquals(1, Candidate::query()->withoutGlobalScopes()->count());

        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
            'status' => Project::STATUS_IN_PROGRESS,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/cv_keskus/1521279.eml'), '1521279.eml');

        $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::query()->withoutGlobalScopes()->withTrashed()->count());
    }

    /**
     * @test
     */
    public function it_handles_uploaded_msg_files()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/Projektijuht.msg'), 'Projektijuht.msg');

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();
        $this->assertEquals('<EMAIL>', $candidate->email);
        $this->assertNull($candidate->name);
        $this->assertEquals(1, $candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->count());
        $this->assertEquals(0, $candidate->cvs()->count());

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $this->assertEquals($candidate->id, $candidateId);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $fileId);

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();
        $this->assertEquals(1, $candidate->messages()->count());
        $this->assertStringContainsString('Palun võtke', $candidate->messages()->first()->body);
    }

    /**
     * @test
     */
    public function it_handles_uploaded_msg_files_from_cvk()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/cv_keskus/KRL.msg'), 'KRL.msg');

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();
        $this->assertEquals('Kai-Riin Laurits', $candidate->name);
        $this->assertEquals('<EMAIL>', $candidate->email);

        $this->assertEquals(1, $candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->count());
        $this->assertEquals(0, $candidate->cvs()->count());

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $this->assertEquals($candidate->id, $candidateId);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $fileId);

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();

        $this->assertEquals(1, $candidate->messages()->count());
        $this->assertStringContainsString('Kai-Riin', $candidate->messages()->first()->body);
    }

    /**
     * @test
     */
    public function it_handles_uploaded_msg_files_from_mail_threads()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/re-applying-for-visual-art-teacher-position-at-tes.msg'),
            're-applying-for-visual-art-teacher-position-at-tes.msg');

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();
        $this->assertEquals('Eva Vezi', $candidate->name);
        $this->assertEquals('<EMAIL>', $candidate->email);

        $this->assertEquals($candidate->id, $res->json()['candidate']['id']);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $res->json()['file']['id']);
    }

    /**
     * @test
     */
    public function it_handles_uploaded_cyrillic_msg_files()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/Работа.msg'), 'Работа.msg');

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();
        $this->assertEquals('Andrei Kolõbanov', $candidate->name);
        $this->assertEquals('<EMAIL>', $candidate->email);

        $this->assertEquals(1, $candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->count());
        $this->assertEquals(0, $candidate->cvs()->count());

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $this->assertEquals($candidate->id, $candidateId);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $fileId);

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();

        $this->assertEquals(1, $candidate->messages()->count());
        $this->assertStringContainsString('3дравствуйте', $candidate->messages()->first()->body);
    }

    /**
     * @test
     */
    public function it_handles_msg_names()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/administraator.msg'), 'administraator.msg');

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        /** @var Candidate $candidate */
        $candidate = Candidate::first();
        $this->assertEquals('Kärt Viiberg', $candidate->name);

        $this->assertEquals($candidate->id, $res->json()['candidate']['id']);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $res->json()['file']['id']);
    }

    /**
     * @test
     */
    public function it_handles_uploaded_msg_files_when_first_parser_fails()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $msgFile = new UploadedFile(base_path('tests/__fixtures/denise_gray.msg'), 'denise_gray.msg');

        $CVParserMsgPyMock = \Mockery::mock(CVParserMsgPy::class . "[parseMsgPy]", array($msgFile));
        $CVParserMsgPyMock
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('parseMsgPy')
            ->once()
            ->andThrow(new \Exception("Nothing was extracted")
            );

        $this->app->bind(CVParserMsgPy::class, function () use ($CVParserMsgPyMock) {
            return $CVParserMsgPyMock;
        });

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $msgFile,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();
        $this->assertEquals('<EMAIL>', $candidate->email);

        $this->assertEquals(1, $candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->count());
        $this->assertEquals(0, $candidate->cvs()->count());

        $this->assertEquals($candidate->id, $res->json()['candidate']['id']);
        $this->assertEquals($candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->first()->id, $res->json()['file']['id']);
    }

    /**
     * @test
     */
    public function it_can_parse_phone_numbers()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
            'status' => Project::STATUS_IN_PROGRESS,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/cv_keskus/1521279.eml'), '1521279.eml');

        $this->assertEquals(0, Candidate::count());

        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals(1, $candidate->files()->where('type', File::TYPE_EMAIL_MESSAGE)->count());
        $this->assertEquals(0, $candidate->cvs()->count());
        $this->assertNotNull($candidate->fresh()->getActiveConsent());

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();

        $this->assertEquals(1, $candidate->cvs()->count());

        /** @var File $cv */
        $cv = $candidate->cvs[0];

        $cv->update(['contents' => <<<TEXT
Kaisa Katrine Mägi
CV
Naine • 12. märts 2000 53946622 <EMAIL> Tallinn
KOKKUVÕTE
Eesmärgiks on ennast arendada ja pidevalt midagi uut õppida.
TÖÖKOGEMUS
Klienditeenindaja
Deichmann kingad OÜ  Eesti
deichmann.com
08.2019 - 10.2019  •  3 k
Klienditeenindus
Klientidega suhtlemine, lao too, kassaga too ja kauba vastu votmine.
Klienditeenindaja
Olerex  Eesti
Olerex.ee
09.2018 - 01.2019  •  5 k
Klienditeenindus
Klientide viisakas suhtlemine, söögi tegemine ja kassaga toimetamine.
HARIDUS
Tallinna vanalinna gümnaasium (Eesti, Keskharidus)
2015 - ...
Hetkel on pooleli 12 klass ja ei plaani veel aastaks õppima minna, arendan ennast tööd tehes ja uusi asju avastades
KEELED
Kuulamine Lugemine Suuline suhtlus Suuline esitus Kirjutamine
Eesti (Suhtluskeel)
Inglise A1 A2 A1 A2 A2
http://www.cvkeskus.ee/cv/1835236 CV uuendamisaeg 12. august 2021
1/2
Kuulamine Lugemine Suuline suhtlus Suuline esitus Kirjutamine
Suhtlen vabalt.
Tasemed: A1/A2: algtasemel keelekasutaja - B1/B2: iseseisev keelekasutaja - C1/C2: vilunud keelekasutaja
SOOVITUD AMETIKOHT
Klienditeenindus
klienditeenindaja
TÖÖ LIIK:
palgatöötaja
TÖÖ AEG:
Täistööaeg
ASUKOHAD:
Tallinn
MUU
LISAINFO
Ma olen kohusetundlik, olen eelnevalt väga paljudes erinevates ettevõttetes/töö kohtadel tööd teinud, kuid kuna on lühike tööaeg
olnud ja olen aru saanud, et pole minu jaoks siis ei ole kõike kirja pandud, aga olen valmis alati rääkima kogemustest.
http://www.cvkeskus.ee/cv/1835236 CV uuendamisaeg 12. august 2021
2/2
TEXT
        ]);

        $cv->setPhoneNoFromContents();
        $candidate = $candidate->refresh();
        $this->assertNotNull($candidate->getAttributes()['phone_e164']);
    }

    /**
     * @test
     */
    public function it_can_handle_uploaded_pdf()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/felicia_wallace.pdf'), 'felicia_wallace.pdf');

        $this->assertEquals(0, Candidate::count());

        $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals('<EMAIL>', $candidate->email);
        $this->assertEquals(1, $candidate->cvs()->count());
    }

    /**
     * @test
     */
    public function it_can_handle_uploaded_docx()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $file = new UploadedFile(base_path('tests/__fixtures/anthony_davies.docx'), 'anthony_davies.docx');

        $this->assertEquals(0, Candidate::count());

        $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals('<EMAIL>', $candidate->email);
        $this->assertEquals(1, $candidate->cvs()->count());
    }

    /**
     * @test
     */
    public function it_uses_ai_parser_when_turned_on()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $website = Helpers::getCurrentWebsite();
        $website->features = [
            ...$website->features,
            'enable_ai' => true,
        ];
        $website->save();

        $this->assertEquals(0, Candidate::count());

        $file = new UploadedFile(base_path('tests/__fixtures/anthony_davies.docx'), 'anthony_davies.docx');
        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        // Check that candidate was created with an e-mail after the initial upload
        $candidate = Candidate::first();
        $this->assertEquals("<EMAIL>", $candidate->email);
        $this->assertNull($candidate->name);

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $fileModel = File::find($fileId);

        $jsonContent = json_decode(file_get_contents('tests/__fixtures/anthony_davies_fields.json'), true);
        $sanitizedJsonContent = OpenAICVExtractor::sanitizeFields($jsonContent);
        $CVParserMock = \Mockery::mock(CVParserWord::class . "[extractFieldsWithAI]", [null, $fileModel]);
        $CVParserMock
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('extractFieldsWithAI')
            ->once()
            ->andReturn($sanitizedJsonContent);

        $this->app->bind(CVParserWord::class, function () use ($CVParserMock) {
            return $CVParserMock;
        });

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals("<EMAIL>", $candidate->email);
        $this->assertEquals("ANTHONY DAVIES", $candidate->name);
        $this->assertEquals("+****************", $candidate->phone);
        $this->assertEquals("Luna Web Design", $candidate->employments()->first()->employer_name);
        $this->assertEquals(1, $candidate->cvs()->count());
    }

    /**
     * @test
     */
    public function it_does_not_use_ai_parser_when_turned_off()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $website = Helpers::getCurrentWebsite();
        $website->features = [
            ...$website->features,
            'enable_ai' => false,
        ];
        $website->save();

        $this->assertEquals(0, Candidate::count());

        $file = new UploadedFile(base_path('tests/__fixtures/anthony_davies.docx'), 'anthony_davies.docx');
        $res = $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        // Check that candidate was created with an e-mail after the initial upload
        $candidate = Candidate::first();
        $this->assertEquals("<EMAIL>", $candidate->email);
        $this->assertNull($candidate->name);

        $candidateId = $res->json()['candidate']['id'];
        $fileId = $res->json()['file']['id'];

        $fileModel = File::find($fileId);

        $CVParserMock = \Mockery::mock(CVParserWord::class . "[extractFieldsWithAI,extractFieldsWithRegex]", [null, $fileModel]);
        $CVParserMock
            ->shouldAllowMockingProtectedMethods()
            ->shouldNotReceive('extractFieldsWithAI');
        $CVParserMock
            ->shouldReceive('extractFieldsWithRegex')
            ->once()
            ->andReturn(['email' => '<EMAIL>', 'phone' => '+****************']);

        $this->app->bind(CVParserWord::class, function () use ($CVParserMock) {
            return $CVParserMock;
        });

        $this->actingAs($user)->post("stages/$stage->id/startSynchronousCvParse", [
            'candidate_id' => $candidateId,
            'file_id' => $fileId,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals('<EMAIL>', $candidate->email);
        $this->assertEquals(null, $candidate->name);
        $this->assertEquals('+****************', $candidate->phone);
        $this->assertCount(0, $candidate->employments()->get());
    }

    /**
     * @test
     */
    public function it_uses_regex_parser_on_initial_upload()
    {
        Queue::fake();
        $user = User::factory()->create();
        /** @var Project $project */
        $project = Project::factory()->create([
            'project_manager_id' => $user->id,
        ]);
        $stage = $project->stages[0];

        $website = Helpers::getCurrentWebsite();
        $website->features = [
            ...$website->features,
            'enable_ai' => true,
        ];
        $website->save();

        $file = new UploadedFile(base_path('tests/__fixtures/anthony_davies.docx'), 'anthony_davies.docx');
        $CVParserMock = \Mockery::mock(CVParserWord::class . "[extractFieldsWithAI,extractFieldsWithRegex]", array($file));

        $CVParserMock
            ->shouldAllowMockingProtectedMethods()
            ->shouldReceive('extractFieldsWithRegex')
            ->once()
            ->andReturn(["email" => "<EMAIL>"]);

        $this->app->bind(CVParserWord::class, function () use ($CVParserMock) {
            return $CVParserMock;
        });

        $this->assertEquals(0, Candidate::count());

        $this->actingAs($user)->post("stages/$stage->id/uploadCandidate", [
            'file' => $file,
        ])->assertOk();

        $this->assertEquals(1, Candidate::count());

        /** @var Candidate $candidate */
        $candidate = Candidate::first();

        $this->assertEquals("<EMAIL>", $candidate->email);
        $this->assertEquals(null, $candidate->name);
        $this->assertEquals(null, $candidate->phone);
        $this->assertCount(0, $candidate->employments()->get());
    }
    use HasOpenAIClient;
    /**
     * @test
     */
    public function it_fixes_openai_bad_encodings()
    {
        $initial = <<<JSON
        {"name":"Ar%C5%ABnas Pavliukevi%C4%8Dius","email":"<EMAIL>","phone":"+***********","work_experience":[{"company":"UAB Saul\\\\u0117s gr\\\\u0101\\\\u017ea","position":"Darb\\\\u0173 vadovas","start_date":"2022-04-01","end_date":"2023-06-01"},{"company":"UAB Empower-Fidelitas","position":"Instaliavimo darb\\\\u0173 vadovas","start_date":"2011-10-01","end_date":"2021-10-01"},{"company":"UAB ELTEL NETWORKS","position":"Vyr. in\\\\u017einierius","start_date":"2010-03-01","end_date":"2011-10-01"},{"company":"UAB NTService","position":"Telekomonukacij\\\\u0173 in\\\\u017einierius","start_date":"2008-01-01","end_date":"2010-03-01"},{"company":"ELTEL NETWORKS","position":"Vyr. In\\\\u017einierius","start_date":"2006-12-01","end_date":"2007-11-01"},{"company":"VRM V\\\\u012a Infostrukt\\\\u016bra","position":"Telekomunikacij\\\\u0173 in\\\\u017einierius","start_date":"2003-01-01","end_date":"2006-12-01"}],"education":[{"institution":"Vilniaus technikos kolegija","programme":"Telekomunikacij\\\\u0173 in\\\\u017einierius","degree_type":"Auk\\\\u0161tasis neuniversitetinis (profesinis bakalauras)","start_date":"2004-01-01","end_date":"2005-01-01"},{"institution":"Vilniaus auk\\\\u0161tesnioji technikos mokykla","programme":"Telekomunikacij\\\\u0173 in\\\\u017einierius","degree_type":"Auk\\\\u0161tesnysis","start_date":"2000-01-01","end_date":"2003-01-01"},{"institution":"Vilniaus rajono Mai\\\\u0161iagalos LDK Algirdo vidurin\\\\u0117 mokykla","programme":"Vidurinis","degree_type":null,"start_date":"1987-01-01","end_date":"2000-01-01"}],"references":[{"name":"UAB Tuvlita","email":null,"phone":null,"company":null,"position":"K\\\\u0117limo kran\\\\u0173 darb\\\\u0173 vadovas"},{"name":"UAB SDG","email":null,"phone":null,"company":null,"position":"\\\\u012emon\\\\u0117s darbuotoj\\\\u0173 saugos ir sveikatos specialistas"},{"name":"UAB SDG","email":null,"phone":null,"company":null,"position":"Auk\\\\u0161talipi\\\\u0173 darb\\\\u0173 vadovo"},{"name":"Nokia Siemens Networks (Vokietija)","email":null,"phone":null,"company":null,"position":"Flexi WCDMA BTS Commissioning and Integration"}],"birthday_at":"1981-08-31","country":"LT","city":"Vilnius"}
        JSON;

        $json = OpenAICVExtractor::fixBadEncodings($initial);

//        dd($initial, $json);

        $data = json_decode($json, true);
//        dd($data);

        $this->assertEquals('Arūnas Pavliukevičius', $data['name']);
        $this->assertStringStartsWith('UAB Saulės', $data['work_experience'][0]['company']);

    }
}
