<?php

namespace Tests\Feature\HRIS;

use App\Models\Candidate;
use App\Models\Integration;
use App\Models\Project;
use App\Models\User;
use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Handler\MockHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Notification;
use Tests\TenantAwareTestCase;

class SelectHRTest extends TenantAwareTestCase
{
    protected Integration $integration;
    protected Candidate $candidate;
    protected Project $project;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();

        // Create and authenticate as user
        $this->user = User::factory()->create();
        $this->actingAs($this->user);

        // Create test data
        $this->project = Project::factory()->create([
            'position_name' => 'Software Engineer',
            'status' => Project::STATUS_IN_PROGRESS,
        ]);

        $this->integration = Integration::factory()->create([
            'remote_type' => Integration::TYPE_SELECT_HR,
            'access_url' => 'https://api.selecthr.test',
            'job_ad_custom_field_configuration' => [
                [
                    'type' => 'select',
                    'items' => [
                        'Yes',
                        'No',
                    ],
                    'label' => 'Will they be manager? (2)',
                    'rules' => [
                        'required',
                    ],
                    'original_slug' => null,
                ],
            ],
        ]);

        $this->candidate = Candidate::factory()->create();
    }

    public function test_it_gets_candidate_hris_status_when_not_in_select_hr()
    {
        $response = $this->getJson("/candidates/{$this->candidate->id}/getHrisData");

        $response->assertOk()
            ->assertJsonPath('0.candidate_hris_data.status', 'not_in_hris')
            ->assertJsonPath('0.candidate_hris_data.id', $this->candidate->id);
    }

    public function test_it_sends_candidate_to_select_hr()
    {
        // Create mock response
        $mock = new MockHandler([
            new Response(200, [], json_encode(['Items' => [['id' => 123]]])),
        ]);

        $handlerStack = HandlerStack::create($mock);

        $this->app->bind(ClientInterface::class, function ($app, array $config) use ($handlerStack) {
            return new Client([
                ...$config,
                'handler' => $handlerStack,
            ]);
        });

        $formData = [
            'startdate' => '2024-03-20',
            'supervisor' => 'Jane Smith',
            'preferredname' => 'Johnny',
            'salary' => '50000',
            'hours' => '40',
            'holidayallowance' => '25',
            'prebookedholidays' => 'None',
            'driving' => 'Yes',
            'fte' => '1.0',
            'willtheybeamanager' => true,
            'will_the_be_a_manager_2' => 'Yes',
        ];

        $response = $this->post(
            "/candidates/{$this->candidate->id}/sendToHris/{$this->integration->id}",
            ['data' => $formData]
        );

        $response->assertOk();

        $response = $this->getJson("/candidates/{$this->candidate->id}/getHrisData");

        $response->assertOk()
            ->assertJsonPath('0.candidate_hris_data.status', 'exists_in_hris')
            ->assertJsonPath('0.candidate_hris_data.id', $this->candidate->id);
    }
}
