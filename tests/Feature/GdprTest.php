<?php

namespace Tests\Feature;

use App\Classes\CandidateSearch\Search;
use App\Classes\ConsentManagement\ConsentService;
use App\Jobs\PrepareConsentRenewals;
use App\Jobs\SendAutoConsentRenewals;
use App\Jobs\SendMessage;
use App\Models\Candidate;
use App\Models\Consent;
use App\Models\ConsentRenewal;
use App\Models\Project;
use App\Models\Setting;
use App\Models\Team;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Queue;
use Tests\FakeMailer;
use Tests\TenantAwareTestCase;

class GdprTest extends TenantAwareTestCase
{
    private function fakeSendConsentRenewalMessage(Candidate $candidate, int $validDays = 10): void
    {
        $this->useFakeMailer();
        $user = User::factory()->create();
        (new PrepareConsentRenewals([
            'candidates' => [$candidate->id],
            'body' => '[consent_renewal_url]',
            'subject' => '',
            'valid_days' => $validDays,
            'request_consent_until' => now()->addYears(3),
        ], $user))->handle();
    }

    /**
     * @test
     */
    public function api_submissions_create_consents()
    {
        $user = User::factory()->create();

        $email = '<EMAIL>';
        $this->post('/api/applications/submit?api_token=' . $user->api_token, [
            'name' => 'Test-Sander Erss',
            'email' => $email,
        ])->assertOk();

        /** @var Candidate $candidate */
        $candidate = Candidate::where('email', $email)->first();
        $this->assertNull($candidate->getActiveConsent());

        $candidate->anonymize();

        $this->assertNull(Candidate::where('email', $email)->first());

        $candidate = Candidate::where('id', $candidate->id)->withTrashed()->first();
        $this->assertNotEquals($email, $candidate->email);

        $this->post('/api/applications/submit?api_token=' . $user->api_token, [
            'name' => 'Test-Sander Erss',
            'email' => $email,
            'gdpr_months' => 36,
        ])->assertOk();
        $candidate->refresh();
        $this->assertEquals($email, $candidate->email);
        $this->assertNotNull($candidate->getActiveConsent());
        $this->assertEquals(Consent::TYPE_UNTIL_DATE, $candidate->getActiveConsent()->consent_type);
    }

    /**
     * @test
     */
    public function it_prepares_consent_renewals_for_specific_candidates()
    {
        $user = User::factory()->create();

        $candidatesA = Candidate::factory()->count(5)->create();

        Queue::fake();

        $this->actingAs($user)->post('/candidates/createConsentRenewals', [
            'data' => [
                'body' => <<<'HTML'
<p>Renew consent here: [consent_renewal_url]</p>
HTML,
                'subject' => 'Please renew consent',
                'candidates' => $candidatesA->pluck('id'),
                'request_consent_until' => now()->addYear(),
            ],
        ])->assertOk();

        Queue::assertPushed(PrepareConsentRenewals::class);
    }

    /**
     * @test
     */
    public function consent_renewal_preparation_can_resolve_filters()
    {
        Queue::fake();

        $user = User::factory()->create();

        $candidatesA = Candidate::factory()->count(5)->create(['name' => 'Härrad']);
        $candidatesB = Candidate::factory()->count(5)->create(['name' => 'Prouad']);

        /** @var Candidate $privateCandidate */
        $privateCandidate = Candidate::find(6);
        /** @var Project $project */
        $project = Project::factory()->create(['accessible_only_members' => 1]);

        $project->stages[0]->addCandidate($privateCandidate);

        $prepareConsentRenewals = new PrepareConsentRenewals([
            'body' => <<<'HTML'
<p>Renew consent here: [consent_renewal_url]</p>
HTML,
            'subject' => 'Please renew consent',
            'filters' => json_encode(['query' => 'Prouad']),
            'valid_days' => 5,
            'request_consent_until' => now()->addYear(),
        ], $user);

        $resolvedCandidateIds = ($prepareConsentRenewals)->resolveCandidateIds();

        $this->assertEquals(
            $candidatesB->whereNotIn('id', 6)->pluck('id')->toArray(),
            $resolvedCandidateIds,
        );

        $prepareConsentRenewals->handle();

        Queue::assertPushed(SendMessage::class);
        $this->assertEquals(4, ConsentRenewal::query()->count());

        $first = $candidatesB->whereNotIn('id', 6)->first();
        $this->assertMatchesRegularExpression(
            '/test\.test\.test\/public\/consents/',
            $first->messages[0]->getFullBodyText($first)
        );
    }

    /**
     * @test
     */
    public function anonymizer_does_not_anonymize_recently_added_candidates()
    {
        Candidate::factory()->count(3)->create(['name' => 'Härrad', 'created_at' => now()]);
        Candidate::factory()->count(6)->create(['name' => 'Prouad', 'created_at' => now()->subMonth()]);

        $this->assertEquals(6, (new ConsentService)->getAnonymizeCandidatesQuery()->count());
    }

    /**
     * @test
     */
    public function auto_consent_renewals_respect_automation_settings_queued()
    {
        Queue::fake();

        $user = User::factory()->create();

        Setting::set(Setting::KEY_CONSENT_AUTOMATION_ENABLED, true);
        Setting::set(Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS, true);
        Setting::set(Setting::KEY_CONSENT_ADMIN_USER_ID, $user->id);

        $service = (new ConsentService);

        $service->setNextRenewalsDate(now());
        $service->sendRenewals();
        Queue::assertPushed(PrepareConsentRenewals::class, 1);

        $service->setNextRenewalsDate(now());
        $service->sendRenewals();
        Queue::assertPushed(PrepareConsentRenewals::class, 2);

        dispatch_sync(new SendAutoConsentRenewals);
        Queue::assertPushed(SendAutoConsentRenewals::class, 1);
    }

    /**
     * @test
     */
    public function auto_consent_renewals_respect_automation_settings_not_queued()
    {
        Queue::fake();

        $user = User::factory()->create();

        Setting::set(Setting::KEY_CONSENT_AUTOMATION_ENABLED, false);
        Setting::set(Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS, true);
        Setting::set(Setting::KEY_CONSENT_ADMIN_USER_ID, $user->id);

        $service = (new ConsentService);
        $service->setNextRenewalsDate(now());

        $service->sendRenewals();
        Queue::assertPushed(PrepareConsentRenewals::class, 0);

        Setting::set(Setting::KEY_CONSENT_AUTOMATION_ENABLED, true);
        Setting::set(Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS, false);

        $service->setNextRenewalsDate(now());
        $service->sendRenewals();
        Queue::assertPushed(PrepareConsentRenewals::class, 0);
    }

    public function untilDateDataProvider(): array
    {
        return [
            [Project::STATUS_FINISHED, 1, 1],
            [Project::STATUS_FINISHED, 13, 0],
            [Project::STATUS_IN_PROGRESS, null, 0],
        ];
    }

    /**
     * @test
     *
     * @dataProvider untilDateDataProvider
     */
    public function dispute_only_consents_make_sense(int $status, ?int $endedMonthsAgo, int $expectCount)
    {
        /** @var Candidate $candidate */
        $candidate = Candidate::factory()->create([]);

        /** @var Project $project */
        $project = Project::factory()->create([
            'status' => $status,
            'end_date' => $endedMonthsAgo ? now()->subMonths($endedMonthsAgo) : null,
        ]);

        $candidate->consents()->save(new Consent([
            'consent_type' => Consent::TYPE_UNTIL_PROJECT_END,
            'project_id' => $project->id,
        ]));

        $this->assertEquals($expectCount, $candidate
            ->disputeOnlyConsents()
            ->count());
    }

    public function teamConsentDataProvider()
    {
        return [
            [Project::STATUS_FINISHED, 1, [1, 1, 1]],
            [Project::STATUS_FINISHED, 13, [0, 0, 0]],
            [Project::STATUS_FINISHED, 11, [1, 0, 0]],
            [Project::STATUS_FINISHED, 4, [1, 1, 0]],
            [Project::STATUS_FINISHED, 25, [0, 0, 0]],
            [Project::STATUS_IN_PROGRESS, 4, [0, 0, 0]],
        ];
    }

    /**
     * @test
     *
     * @dataProvider teamConsentDataProvider
     */
    public function dispute_only_consents_can_be_team_specific(int $status, int $projectEndedMonthsAgo, array $expectCounts)
    {
        Setting::set(Setting::KEY_CONSENT_DISPUTE_MONTHS, 24);
        $teams = [
            'Estonia' => 12,
            'Latvia' => 5,
            'Lithuania' => 3,
        ];

        foreach ($teams as $teamName => $months) {
            $team = Team::factory()->create(['name' => $teamName]);
            Setting::set(Setting::KEY_CONSENT_DISPUTE_MONTHS, $months, $team);
        }

        $k = 0;
        foreach ($teams as $teamName => $monthsCount) {
            /** @var Candidate $candidate */
            $candidate = Candidate::factory()->create([]);

            /** @var Project $project */
            $project = Project::factory()->create([
                'status' => $status,
                'end_date' => $projectEndedMonthsAgo ? now()->subMonths($projectEndedMonthsAgo) : null,
                'team_id' => Team::where('name', $teamName)->first()->id,
            ]);

            $candidate->consents()->save(new Consent([
                'consent_type' => Consent::TYPE_UNTIL_PROJECT_END,
                'project_id' => $project->id,
            ]));

            $this->assertEquals($expectCounts[$k], $candidate
                ->disputeOnlyConsents()
                ->count());
            $k++;
        }
    }

    /**
     * @test
     */
    public function dispute_months_get_fallbacks_without_team()
    {
        $team1 = Team::factory()->create();
        $team2 = Team::factory()->create();

        Setting::set(Setting::KEY_CONSENT_DISPUTE_MONTHS, 24, $team1);
        Setting::set(Setting::KEY_CONSENT_DISPUTE_MONTHS, 15, $team2);

        $candidate = Candidate::factory()->create();

        /** @var Project $projectWithoutTeam */
        $projectWithoutTeam = Project::factory()->create([
            'team_id' => null,
            'status' => Project::STATUS_FINISHED,
            'end_date' => now()->subMonths(4),
        ]);

        $projectWithoutTeam->stages[0]->addCandidate($candidate);
        $consent = new Consent([
            'consent_type' => Consent::TYPE_UNTIL_PROJECT_END,
            'project_id' => $projectWithoutTeam->id,
            'candidate_id' => $candidate->id,
        ]);
        $consent->save();

        $this->assertCount(1, $candidate->disputeOnlyConsents);
    }

    public function consentRelationDataProvider()
    {
        return [
            [
                [
                    'consent_type' => Consent::TYPE_UNTIL_DATE,
                    'active_until' => now()->addMonths(12),
                ],
                ['activeConsents', 'consents', 'activeLongTermConsents', 'notRevokedConsents'],
                ['disputeOnlyConsents', 'activeProjectBasedConsents', 'longTermConsentsExpiringSoon'],
            ],
            [
                [
                    'consent_type' => Consent::TYPE_UNTIL_DATE,
                    'active_until' => now()->addDays(5),
                ],
                ['activeConsents', 'consents', 'activeLongTermConsents', 'longTermConsentsExpiringSoon'],
                ['disputeOnlyConsents', 'activeProjectBasedConsents'],
            ],
            [
                [
                    'consent_type' => Consent::TYPE_UNTIL_DATE,
                    'active_until' => now()->subDays(5),
                ],
                ['consents'],
                ['activeConsents', 'disputeOnlyConsents', 'activeLongTermConsents', 'longTermConsentsExpiringSoon', 'activeProjectBasedConsents'],
            ],
            [
                [
                    'consent_type' => Consent::TYPE_UNTIL_PROJECT_END,
                    'project_id' => fn () => Project::factory()->create()->id,
                ],
                ['consents', 'activeConsents', 'activeProjectBasedConsents'],
                ['disputeOnlyConsents', 'activeLongTermConsents', 'longTermConsentsExpiringSoon'],
            ],
            [
                [
                    'consent_type' => Consent::TYPE_UNTIL_PROJECT_END,
                    'project_id' => fn () => Project::factory()->create()->id,
                    'revoked_at' => now(),
                ],
                ['consents'],
                ['disputeOnlyConsents', 'activeLongTermConsents', 'longTermConsentsExpiringSoon', 'activeConsents', 'activeProjectBasedConsents'],
            ],
            [
                [
                    'consent_type' => Consent::TYPE_UNTIL_PROJECT_END,
                    'project_id' => fn () => Project::factory()->create([
                        'status' => Project::STATUS_FINISHED,
                        'end_date' => now()->subMonths(1),
                    ])->id,
                ],
                ['consents', 'disputeOnlyConsents'],
                ['activeLongTermConsents', 'longTermConsentsExpiringSoon', 'activeConsents', 'activeProjectBasedConsents'],
            ],
        ];
    }

    /**
     * @test
     *
     * @dataProvider consentRelationDataProvider
     */
    public function candidate_consent_relations_work(array $consentParams, array $expectExists, array $expectNotExists)
    {
        $candidate = Candidate::factory()->create();

        if (isset($consentParams['project_id'])) {
            $consentParams['project_id'] = $consentParams['project_id']();
        }

        Consent::create([
            'candidate_id' => $candidate->id,
            ...$consentParams,
        ]);

        foreach ($expectExists as $relation) {
            $this->assertEquals(
                1,
                $candidate->$relation()->count(),
                "Expected $relation to have 1 item"
            );
        }

        foreach ($expectNotExists as $relation) {
            $this->assertEquals(
                0,
                $candidate->$relation()->count(),
                "Expected $relation to have 0 items"
            );
        }
    }

    use FakeMailer;

    /**
     * @test
     */
    public function renewal_candidates_are_filtered()
    {
        Carbon::setTestNow(now()->subDays(20));

        $candidateWithActiveRenewal = Candidate::factory()->create();
        $candidateWithIgnoredRenewal = Candidate::factory()->create();
        $candidateWithGivenConsent = Candidate::factory()->create();
        $candidateWithoutRenewal = Candidate::factory()->create();

        Carbon::setTestNow(now()->addDays(20));

        $this->fakeSendConsentRenewalMessage($candidateWithActiveRenewal);
        $this->fakeSendConsentRenewalMessage($candidateWithIgnoredRenewal, -20);

        $this->fakeSendConsentRenewalMessage($candidateWithGivenConsent);
        $candidateWithGivenConsent->consentRenewals[0]->update(['gave_consent' => true, 'response_at' => now()->subMinutes(5)]);
        $candidateWithGivenConsent->consents()->save(new Consent([
            'consent_type' => Consent::TYPE_UNTIL_DATE,
            'active_until' => now()->addYears(3),
        ]));

        $this->assertEquals(3, ConsentRenewal::query()->count());

        $willSendRenewalsToCandidates = (new ConsentService)->getRenewalsCandidatesQuery()->get();
        $anonymizeCandidates = (new ConsentService)->getAnonymizeCandidatesQuery()->get();

        $this->assertContains(
            $candidateWithoutRenewal->id,
            $willSendRenewalsToCandidates->pluck('id')->toArray()
        );

        $this->assertNotContains(
            $candidateWithIgnoredRenewal->id,
            $willSendRenewalsToCandidates->pluck('id')->toArray()
        );

        $this->assertNotContains(
            $candidateWithActiveRenewal->id,
            $willSendRenewalsToCandidates->pluck('id')->toArray()
        );

        $this->assertNotContains(
            $candidateWithActiveRenewal->id,
            $anonymizeCandidates->pluck('id')->toArray()
        );

        $this->assertContains(
            $candidateWithIgnoredRenewal->id,
            $anonymizeCandidates->pluck('id')->toArray()
        );

        $this->assertContains(
            $candidateWithoutRenewal->id,
            $anonymizeCandidates->pluck('id')->toArray()
        );

        $this->assertEquals(1, $willSendRenewalsToCandidates->count());
        $this->assertEquals(2, $anonymizeCandidates->count());

        $this->assertEquals(2, ConsentRenewal::query()->whereNull('gave_consent')->count());
        $this->assertEquals(1, ConsentRenewal::query()->where('gave_consent', true)->count());
    }

    public function test_exclude_expired_renewal_filter_but_accepted_consent()
    {
        $candidate = Candidate::factory()->create();
        $this->fakeSendConsentRenewalMessage($candidate, -10);
        $candidate->consentRenewals[0]->update(['gave_consent' => true, 'response_at' => now()->subMinutes(5)]);
        $candidate->consents()->save(new Consent([
            'consent_type' => Consent::TYPE_UNTIL_DATE,
            'active_until' => now()->addYears(3),
        ]));

        $cnt = (new Search([
            'ignored_consent_renewal' => 'exclude',
        ]))->buildQuery()->count();

        $this->assertEquals(1, $cnt);
    }

    public function test_consent_expiry_after_renewals_is_not_anonymized()
    {
        // Candidates created in the past 9 days (see ConsentService::ANONYMIZATION_OFFSET_DAYS) are also excluded.
        Carbon::setTestNow(now()->subDays(20));

        $candidate = Candidate::factory()->create();

        Carbon::setTestNow(now()->addDays(20));

        $consent = $candidate->consents()->create([
            'consent_type' => Consent::TYPE_UNTIL_DATE,
            'active_until' => now()->subDays(5),
        ]);

        $anonymizationCandidatesCount = (new ConsentService)->getAnonymizeCandidatesQuery()->count();

        $this->assertEquals(0, $anonymizationCandidatesCount);

        $consent->active_until = now()->subDays(10);
        $consent->save();

        $anonymizationCandidatesCount = (new ConsentService)->getAnonymizeCandidatesQuery()->count();

        $this->assertEquals(1, $anonymizationCandidatesCount);
    }

    public function test_it_sets_next_renewal_date()
    {
        Carbon::setTestNow('2025-05-21 13:02:25.000000');

        $user = User::factory()->create([
            'role' => User::ROLE_ADMIN,
        ]);

        $key = encrypt('OrganizationSettingsForm');

        $res = $this->actingAs($user)->get('/form/OrganizationSettingsForm')->getContent();
        $form = json_decode($res);
        $defaults = collect($form->schema)->pluck('default', 'name');

        Setting::set(Setting::KEY_CONSENT_AUTOMATION_ENABLED, true);
        Setting::set(Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS, false);
        Setting::set(Setting::KEY_CONSENT_NEXT_RENEWALS_DATE, Carbon::parse('2024-07-14'));

        $this->actingAs($user)->post('/organization/settings', [
            'key' => $key,
            'data' => [
                ...$defaults,
                Setting::KEY_CONSENT_AUTOMATION_SEND_RENEWALS => 'true',
            ],
        ])->assertOk();

        $this->assertStringNotContainsString(
            '2024-07-14',
            Setting::get(Setting::KEY_CONSENT_NEXT_RENEWALS_DATE)
        );
        $this->assertStringContainsString(
            '2025-06-10',
            Setting::get(Setting::KEY_CONSENT_NEXT_RENEWALS_DATE)
        );
    }
}
