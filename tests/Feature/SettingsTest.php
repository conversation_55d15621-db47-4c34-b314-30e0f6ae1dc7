<?php

namespace Tests\Feature;

use App\Helpers;
use App\Models\Candidate;
use App\Models\Landing;
use App\Models\LandingTag;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\Team;
use App\Models\User;
use App\Models\Website;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Tests\Assertions;
use Tests\FakeMailer;
use Tests\TenantAwareTestCase;

class SettingsTest extends TenantAwareTestCase
{
    use FakeMailer;

    /**
     * @test
     */
    public function it_resolves_settings_for_team()
    {
        $this->enableTeamsFeature();

        $grandparent = Team::query()->create(['name' => 'grandparent']);

        $parent1 = Team::query()->create(['name' => 'parent1']);
        $parent2 = Team::query()->create(['name' => 'parent2']);

        $child = Team::query()->create(['name' => 'child']);

        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $grandparent->id,
            'target_team_id' => $parent1->id,
        ]);
        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $grandparent->id,
            'target_team_id' => $parent2->id,
        ]);
        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $parent1->id,
            'target_team_id' => $child->id,
        ]);
        DB::connection('tenant')->table('team_edges')->insert([
            'source_team_id' => $parent2->id,
            'target_team_id' => $child->id,
        ]);

        /**
         *    grandparent
         *     /      \
         * parent1   parent2
         *     \     /
         *      child
         */
        Setting::set(Setting::KEY_BRAND_ABOUT, 'It is what it is');
        Setting::set(Setting::KEY_ORGANIZATION_NAME, 'Business Group AS');
        Setting::set(Setting::KEY_ORGANIZATION_NAME, 'Other Group AS', $grandparent);

        Setting::set(Setting::KEY_ORGANIZATION_NAME, 'Parent 1', $parent1);
        Setting::set(Setting::KEY_ORGANIZATION_NAME, 'Parent 2', $parent2);
        Setting::set(Setting::KEY_ORGANIZATION_ADDRESS, 'Kuuse tn 1-11', $parent2);

        Setting::set(Setting::KEY_ORGANIZATION_CITY, 'Tallinn', $grandparent);

        Setting::set(Setting::KEY_ORGANIZATION_STREET_ADDRESS, 'Lelle 22', $child);

        $this->assertEquals('Lelle 22', Setting::get(Setting::KEY_ORGANIZATION_STREET_ADDRESS, $child));
        $this->assertEquals('It is what it is', Setting::get(Setting::KEY_BRAND_ABOUT, $child));
        $this->assertEquals('Parent 1', Setting::get(Setting::KEY_ORGANIZATION_NAME, $child));
        $this->assertEquals('Kuuse tn 1-11', Setting::get(Setting::KEY_ORGANIZATION_ADDRESS, $child));
        $this->assertEquals('Tallinn', Setting::get(Setting::KEY_ORGANIZATION_CITY, $child));

        $allSettings = Setting::getAll($child);
        $this->assertEquals('Lelle 22', $allSettings[Setting::KEY_ORGANIZATION_STREET_ADDRESS]);
        $this->assertEquals('It is what it is', $allSettings[Setting::KEY_BRAND_ABOUT]);
        $this->assertEquals('Parent 1', $allSettings[Setting::KEY_ORGANIZATION_NAME]);
        $this->assertEquals('Kuuse tn 1-11', $allSettings[Setting::KEY_ORGANIZATION_ADDRESS]);
        $this->assertEquals('Tallinn', $allSettings[Setting::KEY_ORGANIZATION_CITY]);
    }

    public function enableTeamsFeature(): void
    {
        $website = Helpers::getCurrentWebsite();
        $features = $website->features;
        $features[Website::FEATURE_KEY_TEAMS] = true;
        $website->features = $features;
        $website->save();
    }

    public function test_candidate_tag_merge(): void
    {
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);

        $candidate1 = Candidate::factory()->create();
        $candidate2 = Candidate::factory()->create();
        $candidate3 = Candidate::factory()->create();

        $tag1 = Tag::factory()->create();
        $tag2 = Tag::factory()->create();
        $tag3 = Tag::factory()->create();
        $tag4 = Tag::factory()->create();

        $candidate1->tags()->attach([$tag1->id, $tag4->id]);
        $candidate2->tags()->attach([$tag1->id, $tag2->id]);

        $this->actingAs($admin)
            ->postJson(
                '/settings/tags/candidates/merge',
                ['data' => ['target_tag' => $tag2->id, 'source_tags' => [$tag1->id, $tag3->id]]]
            )
            ->assertOk();

        Assertions::assertArraysEqualIgnoringOrder(
            [$tag2->id, $tag4->id],
            static::getTagIds($candidate1)
        );
        $this->assertEquals([$tag2->id], static::getTagIds($candidate2));
        $this->assertEmpty(static::getTagIds($candidate3));
        $this->assertDatabaseCount('candidate_tag', 3);
        $this->assertDatabaseMissing('tags', ['id' => $tag1->id]);
        $this->assertDatabaseHas('tags', ['id' => $tag2->id]);
        $this->assertDatabaseMissing('tags', ['id' => $tag3->id]);
        $this->assertDatabaseHas('tags', ['id' => $tag4->id]);
    }

    public function test_landing_tag_merge(): void
    {
        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);

        $landing1 = Landing::factory()->create();
        $landing2 = Landing::factory()->create();
        $landing3 = Landing::factory()->create();

        $tag1 = LandingTag::factory()->create();
        $tag2 = LandingTag::factory()->create();
        $tag3 = LandingTag::factory()->create();
        $tag4 = LandingTag::factory()->create();

        $landing1->tags()->attach([$tag1->id, $tag4->id]);
        $landing2->tags()->attach([$tag1->id, $tag2->id]);

        $this->actingAs($admin)
            ->postJson(
                '/settings/tags/landings/merge',
                ['data' => ['target_tag' => $tag2->id, 'source_tags' => [$tag1->id, $tag3->id]]]
            )
            ->assertOk();

        Assertions::assertArraysEqualIgnoringOrder(
            [$tag2->id, $tag4->id],
            static::getTagIds($landing1)
        );
        $this->assertEquals([$tag2->id], static::getTagIds($landing2));
        $this->assertEmpty(static::getTagIds($landing3));
        $this->assertDatabaseCount('landing_tag', 3);
        $this->assertDatabaseMissing('landing_tags', ['id' => $tag1->id]);
        $this->assertDatabaseHas('landing_tags', ['id' => $tag2->id]);
        $this->assertDatabaseMissing('landing_tags', ['id' => $tag3->id]);
        $this->assertDatabaseHas('landing_tags', ['id' => $tag4->id]);
    }

    public function test_tag_merge_bad_requests(): void
    {
        $createBadData = fn (Collection $ids) => [
            ['target_tag' => $ids->first(), 'source_tags' => $ids->all()],
            ['target_tag' => $ids->first(), 'source_tags' => []],
            ['target_tag' => null, 'source_tags' => $ids->all()],
            ['target_tag' => 98765, 'source_tags' => $ids->all()],
            ['target_tag' => $ids->first(), 'source_tags' => [98765]],
        ];

        $admin = User::factory()->create(['role' => User::ROLE_ADMIN]);

        $candidateTagIds = Tag::factory()->count(5)->create()->pluck('id')->values();
        collect($createBadData($candidateTagIds))->each(function (array $data) use ($admin) {
            $this->actingAs($admin)->postJson('/settings/tags/candidates/merge', ['data' => $data])->assertBadRequest();
            $this->assertDatabaseEmpty('candidate_tag');
            $this->assertDatabaseCount('tags', 5);
        });

        $landingTagIds = LandingTag::factory()->count(5)->create()->pluck('id')->values();
        collect($createBadData($landingTagIds))->each(function (array $data) use ($admin) {
            $this->actingAs($admin)->postJson('/settings/tags/landings/merge', ['data' => $data])->assertBadRequest();
            $this->assertDatabaseEmpty('landing_tag');
            $this->assertDatabaseCount('landing_tags', 5);
        });
    }

    private static function getTagIds(Candidate|Landing $taggable): array
    {
        return $taggable->refresh()->tags->pluck('id')->values()->all();
    }

    public function test_it_clears_cache_for_settings(): void
    {
        $this->enableTeamsFeature();

        $team = Team::factory()->create();
        $user = User::factory()->create(['team_id' => $team->id, 'role' => User::ROLE_ADMIN]);

        $user2 = User::factory()->create();

        // Load projects page. This runs through the request stack and populates the settings cache.
        $this->actingAs($user)->get(route('projects.index'))->assertOk();

        // Assert that settings caches are populated correctly.
        $this->assertEquals([], data_get(cache()->get(Helpers::prependTenantName(Setting::$settingsCacheKeyPrefix . 'global')), Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS));
        $teamCacheKey = Helpers::prependTenantName(Setting::$settingsCacheKeyPrefix . $team->id);
        $this->assertEquals([], data_get(cache()->get($teamCacheKey), Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS));

        // Load and submit the settings form. Set the requisition approval setting.
        $res = $this->actingAs($user)->get('/form/OrganizationSettingsForm')->getContent();
        $form = json_decode($res);
        $defaults = collect($form->schema)->pluck('default', 'name');
        $key = json_decode($res)->key;
        \Log::info('Saving organization settings');
        $this->actingAs($user)->post('/organization/settings', [
            'key' => $key,
            'data' => [
                ...$defaults,
                Setting::KEY_TIMEZONE => 'Europe/Tallinn',
                Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS => [$user2->id],
            ],
        ])->assertOk();
        \Log::info('Saved organization settings');

        // Assert that the settings were saved correctly in the database.
        \Log::info('Asserting database has settings');
        $this->assertDatabaseHas('settings', [
            'key' => Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS,
            'value_json' => json_encode([$user2->id]),
            'team_id' => null,
        ]);
        \Log::info('Asserted database has settings');

        $this->assertEquals(
            [$user2->id],
            data_get(cache()->get($teamCacheKey), Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS));

        $this->actingAs($user)->post('/organization/settings', [
            'key' => $key,
            'data' => [
                ...$defaults,
                Setting::KEY_TIMEZONE => 'Europe/Tallinn',
                Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS => [],
            ],
        ])->assertOk();
        \Log::info('Saved organization settings');

        // Assert that the settings were saved correctly in the database.
        \Log::info('Asserting database has settings');
        $this->assertDatabaseHas('settings', [
            'key' => Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS,
            'value_json' => json_encode([]),
            'team_id' => null,
        ]);
        \Log::info('Asserted database has settings');

        info("Getting settings from cache: $teamCacheKey");
        $this->assertEquals([], data_get(cache()->get($teamCacheKey), Setting::KEY_REQUISITION_ALWAYS_ASK_APPROVAL_FROM_USER_IDS));
    }

}
