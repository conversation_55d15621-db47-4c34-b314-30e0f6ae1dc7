/node_modules
/public/hot
/public/build
/public/storage
/storage/*.key
/storage/*.pdf
/storage/*.msg
/storage/*.docx
/storage/cvk_tmp
/storage/msg-*/
/vendor
.env
.env.backup
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.loggit
yarn-error.log
.idea
.npmrc
.DS_Store
docker-compose.override.yml

/public/js/*
!/public/js/iframeResizer.contentWindow.min.js
!/public/js/iframeResizer.min.js

/public/fonts/
/public/css/
/public/mix-manifest.json
_ide_helper_models.php
_lighthouse_ide_helper.php
programmatic-types.graphql
schema-directives.graphql
graphql/introspected.graphql
coverage.txt

/bootstrap/ssr

# Playwright
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
