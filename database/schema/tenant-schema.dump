PGDMP                         {           jes.87kXz4RuQk    15.4 (Debian 15.4-1.pgdg120+1)     15.4 (Ubuntu 15.4-2.pgdg20.04+1) b   �           0    0    ENCODING    ENCODING        SET client_encoding = 'UTF8';
                      false            �           0    0 
   STDSTRINGS 
   STDSTRINGS     (   SET standard_conforming_strings = 'on';
                      false            �           0    0 
   SEARCHPATH 
   SEARCHPATH     8   SELECT pg_catalog.set_config('search_path', '', false);
                      false            �           1262    146971    jes.87kXz4RuQk    DATABASE     {   CREATE DATABASE "jes.87kXz4RuQk" WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.utf8';
     DROP DATABASE "jes.87kXz4RuQk";
                jes.87kXz4RuQk    false            �            1259    146983    action_candidate    TABLE     j   CREATE TABLE public.action_candidate (
    action_id bigint NOT NULL,
    candidate_id bigint NOT NULL
);
 $   DROP TABLE public.action_candidate;
       public         heap    jes.87kXz4RuQk    false            �            1259    147066    actions    TABLE       CREATE TABLE public.actions (
    id bigint NOT NULL,
    action_type character varying(255) DEFAULT 'message'::character varying NOT NULL,
    stage_id bigint NOT NULL,
    name character varying(255) NOT NULL,
    trigger character varying(255) DEFAULT 'manual'::character varying NOT NULL,
    data jsonb,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    delay jsonb,
    scheduled_at timestamp without time zone,
    filters jsonb,
    filters_condition smallint DEFAULT 0 NOT NULL
);
    DROP TABLE public.actions;
       public         heap    jes.87kXz4RuQk    false            .           1259    147364    actions_id_seq    SEQUENCE     �   ALTER TABLE public.actions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.actions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    238            �            1259    147020 
   activities    TABLE     �  CREATE TABLE public.activities (
    id bigint NOT NULL,
    activity_type_id bigint NOT NULL,
    candidate_id bigint,
    project_id bigint,
    stage_id bigint,
    comment_id bigint,
    message_id bigint,
    scorecard_id bigint,
    user_id bigint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    form_id bigint,
    submission_id bigint,
    task_id bigint,
    sms_id bigint,
    video_interview_invite_id bigint,
    integration_id bigint,
    call_id bigint,
    source character varying(255),
    medium character varying(255),
    referrer character varying(255),
    analytics_event_id character varying(255),
    custom_fields jsonb,
    team_id bigint,
    dropout_reason_id bigint
);
    DROP TABLE public.activities;
       public         heap    jes.87kXz4RuQk    false            e           1259    147716    activities_id_seq    SEQUENCE     �   ALTER TABLE public.activities ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.activities_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    223            �            1259    147023    activity_receipts    TABLE     �   CREATE TABLE public.activity_receipts (
    id bigint NOT NULL,
    activity_id bigint NOT NULL,
    user_id bigint NOT NULL,
    updated_at timestamp without time zone,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);
 %   DROP TABLE public.activity_receipts;
       public         heap    jes.87kXz4RuQk    false            g           1259    147744    activity_receipts_id_seq    SEQUENCE     �   ALTER TABLE public.activity_receipts ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.activity_receipts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    224            �            1259    147053    activity_subscriptions    TABLE     
  CREATE TABLE public.activity_subscriptions (
    id bigint NOT NULL,
    project_id bigint NOT NULL,
    user_id bigint NOT NULL,
    last_sent_at timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 *   DROP TABLE public.activity_subscriptions;
       public         heap    jes.87kXz4RuQk    false            f           1259    147736    activity_subscriptions_id_seq    SEQUENCE     �   ALTER TABLE public.activity_subscriptions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.activity_subscriptions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    233            �            1259    147034    activity_types    TABLE     ^  CREATE TABLE public.activity_types (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    icon_class character varying(255),
    display_name character varying(255),
    custom_fields_settings jsonb,
    is_custom smallint DEFAULT 0 NOT NULL
);
 "   DROP TABLE public.activity_types;
       public         heap    jes.87kXz4RuQk    false            Z           1259    147656    activity_types_id_seq    SEQUENCE     �   ALTER TABLE public.activity_types ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.activity_types_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    227            �            1259    147041    api_keys    TABLE     �   CREATE TABLE public.api_keys (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    api_key character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.api_keys;
       public         heap    jes.87kXz4RuQk    false            R           1259    147586    api_keys_id_seq    SEQUENCE     �   ALTER TABLE public.api_keys ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.api_keys_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    229            �            1259    147026    audits    TABLE     �  CREATE TABLE public.audits (
    id bigint NOT NULL,
    user_type character varying(255),
    user_id bigint,
    event character varying(255) NOT NULL,
    auditable_type character varying(255) NOT NULL,
    auditable_id bigint NOT NULL,
    old_values text,
    new_values text,
    url text,
    ip_address character varying(45),
    user_agent character varying(1023),
    tags character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.audits;
       public         heap    jes.87kXz4RuQk    false            `           1259    147689 
   audits_id_seq    SEQUENCE     �   ALTER TABLE public.audits ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.audits_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    225            �            1259    147038    auth_tokens    TABLE     �   CREATE TABLE public.auth_tokens (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    token character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.auth_tokens;
       public         heap    jes.87kXz4RuQk    false            d           1259    147712    auth_tokens_id_seq    SEQUENCE     �   ALTER TABLE public.auth_tokens ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.auth_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    228            �            1259    147046    cache    TABLE     �   CREATE TABLE public.cache (
    key character varying(255) NOT NULL,
    value text NOT NULL,
    expiration integer NOT NULL
);
    DROP TABLE public.cache;
       public         heap    jes.87kXz4RuQk    false            �            1259    147076    calls    TABLE     s  CREATE TABLE public.calls (
    id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    user_id bigint NOT NULL,
    project_id bigint,
    start_at timestamp without time zone NOT NULL,
    end_at timestamp without time zone,
    did_answer smallint NOT NULL,
    content text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.calls;
       public         heap    jes.87kXz4RuQk    false            >           1259    147467    calls_id_seq    SEQUENCE     �   ALTER TABLE public.calls ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.calls_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    242            �            1259    147114    candidate_cv_ai_summary    TABLE     r  CREATE TABLE public.candidate_cv_ai_summary (
    id bigint NOT NULL,
    candidate_id bigint,
    cv_id bigint NOT NULL,
    summary_json jsonb,
    usage_json jsonb,
    is_failed smallint DEFAULT 0 NOT NULL,
    fail_reason character varying(255),
    raw_model_response text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 +   DROP TABLE public.candidate_cv_ai_summary;
       public         heap    jes.87kXz4RuQk    false            c           1259    147707    candidate_cv_ai_summary_id_seq    SEQUENCE     �   ALTER TABLE public.candidate_cv_ai_summary ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.candidate_cv_ai_summary_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    249            �            1259    147052    candidate_filters    TABLE       CREATE TABLE public.candidate_filters (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    is_public smallint DEFAULT 0 NOT NULL,
    user_id bigint,
    data jsonb,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 %   DROP TABLE public.candidate_filters;
       public         heap    jes.87kXz4RuQk    false            [           1259    147660    candidate_filters_id_seq    SEQUENCE     �   ALTER TABLE public.candidate_filters ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.candidate_filters_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    232            �            1259    147084    candidate_integration    TABLE     �   CREATE TABLE public.candidate_integration (
    candidate_id bigint NOT NULL,
    integration_id bigint NOT NULL,
    remote_id character varying(255)
);
 )   DROP TABLE public.candidate_integration;
       public         heap    jes.87kXz4RuQk    false            �            1259    147017    candidate_presentation    TABLE     v   CREATE TABLE public.candidate_presentation (
    candidate_id bigint NOT NULL,
    presentation_id bigint NOT NULL
);
 *   DROP TABLE public.candidate_presentation;
       public         heap    jes.87kXz4RuQk    false            �            1259    147063 
   candidate_sms    TABLE       CREATE TABLE public.candidate_sms (
    candidate_id bigint NOT NULL,
    sms_id bigint NOT NULL,
    sent_at timestamp without time zone,
    delivered_at timestamp without time zone,
    undelivered_at timestamp without time zone,
    failed_at timestamp without time zone
);
 !   DROP TABLE public.candidate_sms;
       public         heap    jes.87kXz4RuQk    false            �            1259    147113    candidate_stage    TABLE     �   CREATE TABLE public.candidate_stage (
    candidate_id bigint NOT NULL,
    stage_id bigint NOT NULL,
    sort_order integer DEFAULT 0 NOT NULL,
    dropout_reason_id bigint
);
 #   DROP TABLE public.candidate_stage;
       public         heap    jes.87kXz4RuQk    false            �            1259    147065    candidate_summaries    TABLE     �  CREATE TABLE public.candidate_summaries (
    id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    project_id bigint,
    version character varying(255) DEFAULT '1.0.0'::character varying NOT NULL,
    summary_json jsonb,
    usage_json jsonb,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    is_failed smallint DEFAULT 0 NOT NULL,
    fail_reason character varying(255),
    raw_model_response text
);
 '   DROP TABLE public.candidate_summaries;
       public         heap    jes.87kXz4RuQk    false            3           1259    147399    candidate_summaries_id_seq    SEQUENCE     �   ALTER TABLE public.candidate_summaries ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.candidate_summaries_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    237            �            1259    147071 
   candidate_tag    TABLE     d   CREATE TABLE public.candidate_tag (
    candidate_id bigint NOT NULL,
    tag_id bigint NOT NULL
);
 !   DROP TABLE public.candidate_tag;
       public         heap    jes.87kXz4RuQk    false            �            1259    147069    candidate_video_call    TABLE     r   CREATE TABLE public.candidate_video_call (
    candidate_id bigint NOT NULL,
    video_call_id bigint NOT NULL
);
 (   DROP TABLE public.candidate_video_call;
       public         heap    jes.87kXz4RuQk    false            �            1259    146986 
   candidates    TABLE     �  CREATE TABLE public.candidates (
    id bigint NOT NULL,
    name character varying(255),
    email character varying(255),
    skype character varying(255),
    phone character varying(255),
    linkedin_url character varying(255),
    country character varying(255),
    city character varying(255),
    description text,
    entered_by_id bigint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    remote_source_id bigint,
    remote_id character varying(255),
    deleted_at timestamp without time zone,
    birthday_at date,
    is_private smallint DEFAULT 0 NOT NULL,
    phone_e164 character varying(255),
    custom_fields jsonb,
    team_id bigint
);
    DROP TABLE public.candidates;
       public         heap    jes.87kXz4RuQk    false            m           1259    147772    candidates_id_seq    SEQUENCE     �   ALTER TABLE public.candidates ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.candidates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    215            �            1259    146991    comments    TABLE     �  CREATE TABLE public.comments (
    id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    user_id bigint,
    content text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    is_public smallint DEFAULT 0 NOT NULL,
    project_id bigint,
    rating smallint,
    is_quick smallint DEFAULT 0 NOT NULL,
    video_interview_invite_id bigint,
    team_id bigint
);
    DROP TABLE public.comments;
       public         heap    jes.87kXz4RuQk    false            n           1259    147780    comments_id_seq    SEQUENCE     �   ALTER TABLE public.comments ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.comments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    216            �            1259    147059    consent_renewals    TABLE     �  CREATE TABLE public.consent_renewals (
    id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    message_id bigint,
    token character varying(255) NOT NULL,
    expires_at timestamp without time zone NOT NULL,
    request_consent_until timestamp without time zone NOT NULL,
    response_at timestamp without time zone,
    gave_consent smallint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 $   DROP TABLE public.consent_renewals;
       public         heap    jes.87kXz4RuQk    false            b           1259    147701    consent_renewals_id_seq    SEQUENCE     �   ALTER TABLE public.consent_renewals ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.consent_renewals_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    235            �            1259    147119    consents    TABLE     �  CREATE TABLE public.consents (
    id bigint NOT NULL,
    consent_type character varying(255) NOT NULL,
    candidate_id bigint NOT NULL,
    active_until timestamp without time zone,
    project_id bigint,
    user_id bigint,
    ip character varying(255),
    source character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    is_from_public_source smallint DEFAULT 0 NOT NULL
);
    DROP TABLE public.consents;
       public         heap    jes.87kXz4RuQk    false            a           1259    147695    consents_id_seq    SEQUENCE     �   ALTER TABLE public.consents ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.consents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    250            �            1259    147122    crm_contacts    TABLE     I  CREATE TABLE public.crm_contacts (
    id bigint NOT NULL,
    name character varying(255),
    email character varying(255),
    phone character varying(255),
    job_title character varying(255),
    crm_organization_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
     DROP TABLE public.crm_contacts;
       public         heap    jes.87kXz4RuQk    false            P           1259    147579    crm_contacts_id_seq    SEQUENCE     �   ALTER TABLE public.crm_contacts ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.crm_contacts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    251            �            1259    146996    crm_offices    TABLE     �   CREATE TABLE public.crm_offices (
    id bigint NOT NULL,
    name character varying(255),
    location jsonb,
    crm_organization_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.crm_offices;
       public         heap    jes.87kXz4RuQk    false            l           1259    147765    crm_offices_id_seq    SEQUENCE     �   ALTER TABLE public.crm_offices ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.crm_offices_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    217            �            1259    147001    crm_organizations    TABLE     V  CREATE TABLE public.crm_organizations (
    id bigint NOT NULL,
    name character varying(255),
    contact_phone character varying(255),
    contact_email character varying(255),
    contact_name character varying(255),
    client_manager_id bigint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 %   DROP TABLE public.crm_organizations;
       public         heap    jes.87kXz4RuQk    false            k           1259    147761    crm_organizations_id_seq    SEQUENCE     �   ALTER TABLE public.crm_organizations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.crm_organizations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    218            �            1259    147006    custom_stage_categories    TABLE       CREATE TABLE public.custom_stage_categories (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    system_category integer NOT NULL,
    sort_order smallint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 +   DROP TABLE public.custom_stage_categories;
       public         heap    jes.87kXz4RuQk    false            j           1259    147758    custom_stage_categories_id_seq    SEQUENCE     �   ALTER TABLE public.custom_stage_categories ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.custom_stage_categories_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    219            �            1259    147009    dropout_reasons    TABLE     �   CREATE TABLE public.dropout_reasons (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    candidate_reason smallint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 #   DROP TABLE public.dropout_reasons;
       public         heap    jes.87kXz4RuQk    false            i           1259    147755    dropout_reasons_id_seq    SEQUENCE     �   ALTER TABLE public.dropout_reasons ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.dropout_reasons_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    220            �            1259    147012 
   educations    TABLE     o  CREATE TABLE public.educations (
    id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    degree smallint,
    programme character varying(255),
    institution character varying(255),
    start_year integer,
    end_year integer,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    degree_title character varying(255)
);
    DROP TABLE public.educations;
       public         heap    jes.87kXz4RuQk    false            h           1259    147751    educations_id_seq    SEQUENCE     �   ALTER TABLE public.educations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.educations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    221            �            1259    147030    employments    TABLE     4  CREATE TABLE public.employments (
    id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    position_title character varying(255),
    employer_name character varying(255),
    date_from date,
    date_until date,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.employments;
       public         heap    jes.87kXz4RuQk    false            \           1259    147666    employments_id_seq    SEQUENCE     �   ALTER TABLE public.employments ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.employments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    226            �            1259    147045    event_set_user    TABLE     f   CREATE TABLE public.event_set_user (
    event_set_id bigint NOT NULL,
    user_id bigint NOT NULL
);
 "   DROP TABLE public.event_set_user;
       public         heap    jes.87kXz4RuQk    false            �            1259    147054 
   event_sets    TABLE     L  CREATE TABLE public.event_sets (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    project_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    subject character varying(255),
    body text,
    sms_body text,
    location character varying(255),
    is_video_call smallint DEFAULT 0 NOT NULL,
    author_id bigint,
    send_sms smallint DEFAULT 0 NOT NULL,
    is_prescheduled smallint DEFAULT 0 NOT NULL,
    is_dynamic smallint DEFAULT 0 NOT NULL,
    available_days character varying(255),
    available_period_start date,
    available_period_end date,
    available_time_start time without time zone,
    available_time_end time without time zone,
    available_time_zone character varying(255),
    interview_length smallint,
    interview_snap smallint
);
    DROP TABLE public.event_sets;
       public         heap    jes.87kXz4RuQk    false            S           1259    147593    event_sets_id_seq    SEQUENCE     �   ALTER TABLE public.event_sets ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.event_sets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    234            �            1259    147075    events    TABLE     �  CREATE TABLE public.events (
    id bigint NOT NULL,
    message_id bigint NOT NULL,
    name character varying(255),
    start_time timestamp without time zone,
    end_time timestamp without time zone,
    address character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    uid character varying(255),
    sequence bigint DEFAULT 0 NOT NULL,
    is_cancelled integer DEFAULT 0 NOT NULL,
    room_email character varying(255),
    room_name character varying(255),
    room_address character varying(255),
    status character varying(255) DEFAULT 'CONFIRMED'::character varying NOT NULL
);
    DROP TABLE public.events;
       public         heap    jes.87kXz4RuQk    false            ?           1259    147476 
   events_id_seq    SEQUENCE     �   ALTER TABLE public.events ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.events_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    241            �            1259    147083    failed_jobs    TABLE     �   CREATE TABLE public.failed_jobs (
    id bigint NOT NULL,
    connection text NOT NULL,
    queue text NOT NULL,
    payload text NOT NULL,
    exception text NOT NULL,
    failed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
    DROP TABLE public.failed_jobs;
       public         heap    jes.87kXz4RuQk    false            6           1259    147416    failed_jobs_id_seq    SEQUENCE     �   ALTER TABLE public.failed_jobs ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.failed_jobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    243            �            1259    147098    files    TABLE     I  CREATE TABLE public.files (
    id bigint NOT NULL,
    type character varying(255) NOT NULL,
    location character varying(255),
    fileable_id bigint,
    fileable_type character varying(255),
    contents text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    project_id bigint
);
    DROP TABLE public.files;
       public         heap    jes.87kXz4RuQk    false            F           1259    147517    files_id_seq    SEQUENCE     �   ALTER TABLE public.files ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.files_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    245                       1259    147209    forms    TABLE     n  CREATE TABLE public.forms (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    stage_id bigint,
    token character varying(255) NOT NULL,
    form_data jsonb NOT NULL,
    schema jsonb NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    status smallint DEFAULT 1 NOT NULL,
    team_id bigint
);
    DROP TABLE public.forms;
       public         heap    jes.87kXz4RuQk    false            <           1259    147453    forms_id_seq    SEQUENCE     �   ALTER TABLE public.forms ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.forms_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    275            �            1259    147103    imports    TABLE     E  CREATE TABLE public.imports (
    id bigint NOT NULL,
    integration_id bigint NOT NULL,
    stage_id bigint NOT NULL,
    remote_project_id character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    imported_ids jsonb,
    last_run_at timestamp without time zone
);
    DROP TABLE public.imports;
       public         heap    jes.87kXz4RuQk    false            Q           1259    147583    imports_id_seq    SEQUENCE     �   ALTER TABLE public.imports ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.imports_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    246            �            1259    147105    integration_message    TABLE     �   CREATE TABLE public.integration_message (
    integration_id bigint NOT NULL,
    message_id bigint NOT NULL,
    external_id character varying(255) NOT NULL
);
 '   DROP TABLE public.integration_message;
       public         heap    jes.87kXz4RuQk    false                       1259    147184    integration_structured_job_ad    TABLE     2  CREATE TABLE public.integration_structured_job_ad (
    integration_id bigint NOT NULL,
    structured_job_ad_id bigint NOT NULL,
    destination_stage_id bigint,
    updated_at timestamp without time zone,
    remote_id character varying(255),
    remote_url character varying(255),
    response jsonb
);
 1   DROP TABLE public.integration_structured_job_ad;
       public         heap    jes.87kXz4RuQk    false                       1259    147196    integrations    TABLE     �  CREATE TABLE public.integrations (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    remote_type character varying(255) NOT NULL,
    username character varying(255),
    password text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    imap_path character varying(255),
    cache jsonb,
    employer_id_override character varying(255),
    api_key character varying(255),
    feed_url text,
    user_id bigint,
    oauth_token jsonb,
    access_url text,
    access_key text,
    shared_mailbox_address character varying(255),
    is_active smallint DEFAULT 1 NOT NULL,
    failed_count bigint DEFAULT 0 NOT NULL,
    last_fail_at timestamp without time zone,
    last_error_message text,
    mail_folder_name character varying(255),
    team_id bigint,
    email character varying(255),
    client_id character varying(255),
    client_secret character varying(255),
    failing_since timestamp without time zone,
    flags jsonb,
    li_company_id character varying(255),
    feed_name character varying(255),
    feed_slug character varying(255),
    feed_conversion_type character varying(255),
    feed_conversion_original_id bigint
);
     DROP TABLE public.integrations;
       public         heap    jes.87kXz4RuQk    false            I           1259    147531    integrations_id_seq    SEQUENCE     �   ALTER TABLE public.integrations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.integrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    271            �            1259    147137    invites    TABLE       CREATE TABLE public.invites (
    id bigint NOT NULL,
    token character(40),
    event_set_id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    selected_time_slot_id bigint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.invites;
       public         heap    jes.87kXz4RuQk    false            ^           1259    147679    invites_id_seq    SEQUENCE     �   ALTER TABLE public.invites ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.invites_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    252            �            1259    147140    job_ad_feed_structured_job_ad    TABLE     �   CREATE TABLE public.job_ad_feed_structured_job_ad (
    job_ad_feed_id bigint NOT NULL,
    structured_job_ad_id bigint NOT NULL,
    remote_url character varying(255)
);
 1   DROP TABLE public.job_ad_feed_structured_job_ad;
       public         heap    jes.87kXz4RuQk    false            �            1259    147149    job_ad_feeds    TABLE     �  CREATE TABLE public.job_ad_feeds (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    slug character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    output_type character varying(255) DEFAULT 'default'::character varying NOT NULL,
    li_company_id character varying(255),
    integration_id bigint
);
     DROP TABLE public.job_ad_feeds;
       public         heap    jes.87kXz4RuQk    false            ;           1259    147447    job_ad_feeds_id_seq    SEQUENCE     �   ALTER TABLE public.job_ad_feeds ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.job_ad_feeds_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    254            �            1259    147153    landing_dei_reports    TABLE       CREATE TABLE public.landing_dei_reports (
    id bigint NOT NULL,
    landing_id bigint NOT NULL,
    block_id character varying(10) NOT NULL,
    version character varying(255) DEFAULT '1.0.0'::character varying NOT NULL,
    report_json jsonb,
    usage_json jsonb,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone,
    is_failed smallint DEFAULT 0 NOT NULL,
    fail_reason character varying(255),
    raw_model_response text
);
 '   DROP TABLE public.landing_dei_reports;
       public         heap    jes.87kXz4RuQk    false            O           1259    147574    landing_dei_reports_id_seq    SEQUENCE     �   ALTER TABLE public.landing_dei_reports ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.landing_dei_reports_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    255                        1259    147154    landing_tag    TABLE     h   CREATE TABLE public.landing_tag (
    landing_id bigint NOT NULL,
    landing_tag_id bigint NOT NULL
);
    DROP TABLE public.landing_tag;
       public         heap    jes.87kXz4RuQk    false                       1259    147158    landing_tags    TABLE       CREATE TABLE public.landing_tags (
    id bigint NOT NULL,
    color character varying(255) DEFAULT '#007bff'::character varying NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
     DROP TABLE public.landing_tags;
       public         heap    jes.87kXz4RuQk    false            _           1259    147686    landing_tags_id_seq    SEQUENCE     �   ALTER TABLE public.landing_tags ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.landing_tags_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    257                       1259    147159    landings    TABLE     b  CREATE TABLE public.landings (
    id bigint NOT NULL,
    slug character varying(255),
    data jsonb NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    image_url character varying(2500),
    status character varying(255) DEFAULT 'active'::character varying NOT NULL,
    type character varying(255) DEFAULT 'v1'::character varying NOT NULL,
    public_token character varying(255),
    private_token character varying(255),
    email character varying(255),
    template character varying(255) DEFAULT 'default'::character varying NOT NULL,
    published_data jsonb,
    deleted_at timestamp without time zone,
    url_type character varying(255) DEFAULT 'id'::character varying NOT NULL,
    url_token character varying(255),
    is_pinned smallint DEFAULT 0 NOT NULL,
    team_id bigint,
    stage_id bigint
);
    DROP TABLE public.landings;
       public         heap    jes.87kXz4RuQk    false            1           1259    147387    landings_id_seq    SEQUENCE     �   ALTER TABLE public.landings ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.landings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    258                       1259    147164    messageables    TABLE     F  CREATE TABLE public.messageables (
    message_id bigint NOT NULL,
    messageable_id bigint NOT NULL,
    messageable_type character varying(255) NOT NULL,
    bounced_at timestamp without time zone,
    clicked_at timestamp without time zone,
    complaint_at timestamp without time zone,
    delivery_at timestamp without time zone,
    open_at timestamp without time zone,
    reject_at timestamp without time zone,
    render_fail_at timestamp without time zone,
    sent_at timestamp without time zone,
    merge_fields jsonb,
    survey_id bigint,
    delivery_error text
);
     DROP TABLE public.messageables;
       public         heap    jes.87kXz4RuQk    false                       1259    147167    messages    TABLE     �  CREATE TABLE public.messages (
    id bigint NOT NULL,
    user_id bigint,
    project_id bigint,
    subject character varying(255),
    body text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    "to" jsonb,
    is_sent smallint DEFAULT 0 NOT NULL,
    merge_fields jsonb,
    is_promotional smallint DEFAULT 0 NOT NULL,
    video_id bigint,
    delay_until timestamp without time zone,
    team_id bigint
);
    DROP TABLE public.messages;
       public         heap    jes.87kXz4RuQk    false            D           1259    147505    messages_id_seq    SEQUENCE     �   ALTER TABLE public.messages ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.messages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    260                       1259    147169 
   migrations    TABLE     �   CREATE TABLE public.migrations (
    id bigint NOT NULL,
    migration character varying(255) NOT NULL,
    batch integer NOT NULL
);
    DROP TABLE public.migrations;
       public         heap    jes.87kXz4RuQk    false            ,           1259    147350    migrations_id_seq    SEQUENCE     �   ALTER TABLE public.migrations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.migrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    261                       1259    147179    nps_surveys    TABLE     �   CREATE TABLE public.nps_surveys (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    rating bigint,
    comment text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.nps_surveys;
       public         heap    jes.87kXz4RuQk    false            0           1259    147378    nps_surveys_id_seq    SEQUENCE     �   ALTER TABLE public.nps_surveys ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.nps_surveys_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    264                       1259    147176    password_resets    TABLE     �   CREATE TABLE public.password_resets (
    email character varying(255) NOT NULL,
    token character varying(255) NOT NULL,
    created_at timestamp without time zone
);
 #   DROP TABLE public.password_resets;
       public         heap    jes.87kXz4RuQk    false                       1259    147177 
   presentations    TABLE     -  CREATE TABLE public.presentations (
    id bigint NOT NULL,
    token character(80) NOT NULL,
    project_id bigint NOT NULL,
    message_id bigint NOT NULL,
    expires_at timestamp without time zone NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 !   DROP TABLE public.presentations;
       public         heap    jes.87kXz4RuQk    false            N           1259    147567    presentations_id_seq    SEQUENCE     �   ALTER TABLE public.presentations ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.presentations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    263                       1259    147205    project_logs    TABLE       CREATE TABLE public.project_logs (
    id bigint NOT NULL,
    project_id bigint NOT NULL,
    user_id bigint NOT NULL,
    content text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    is_public smallint DEFAULT 0 NOT NULL
);
     DROP TABLE public.project_logs;
       public         heap    jes.87kXz4RuQk    false            B           1259    147495    project_logs_id_seq    SEQUENCE     �   ALTER TABLE public.project_logs ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.project_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    274            	           1259    147180    project_user    TABLE     b   CREATE TABLE public.project_user (
    project_id bigint NOT NULL,
    user_id bigint NOT NULL
);
     DROP TABLE public.project_user;
       public         heap    jes.87kXz4RuQk    false                       1259    147267    projects    TABLE     z  CREATE TABLE public.projects (
    id bigint NOT NULL,
    crm_organization_id bigint,
    position_name character varying(255) NOT NULL,
    deadline_at date,
    application_email character varying(255),
    country character varying(255),
    city character varying(255),
    landing_page character varying(255),
    description text,
    monthly_salary text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    status smallint DEFAULT 0 NOT NULL,
    warranty_until date,
    project_manager_id bigint,
    deleted_at timestamp without time zone,
    start_date date,
    end_date date,
    accessible_only_members smallint DEFAULT 0 NOT NULL,
    custom_fields jsonb,
    crm_office_id bigint,
    scorecard_id bigint,
    team_id bigint,
    is_perpetual smallint DEFAULT 0 NOT NULL,
    perpetual_gdpr_consent_length character varying(255)
);
    DROP TABLE public.projects;
       public         heap    jes.87kXz4RuQk    false            K           1259    147546    projects_id_seq    SEQUENCE     �   ALTER TABLE public.projects ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.projects_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    283                       1259    147263    reacts    TABLE     �   CREATE TABLE public.reacts (
    id bigint NOT NULL,
    comment_id bigint NOT NULL,
    user_id bigint NOT NULL,
    emoji character varying(4) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.reacts;
       public         heap    jes.87kXz4RuQk    false            W           1259    147634 
   reacts_id_seq    SEQUENCE     �   ALTER TABLE public.reacts ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.reacts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    282            
           1259    147183    requisition_approvals    TABLE     K  CREATE TABLE public.requisition_approvals (
    id bigint NOT NULL,
    requisition_id bigint NOT NULL,
    user_id bigint NOT NULL,
    approved_at timestamp without time zone,
    rejected_at timestamp without time zone,
    comment text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 )   DROP TABLE public.requisition_approvals;
       public         heap    jes.87kXz4RuQk    false            J           1259    147536    requisition_approvals_id_seq    SEQUENCE     �   ALTER TABLE public.requisition_approvals ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.requisition_approvals_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    266            
           1259    147190    requisitions    TABLE       CREATE TABLE public.requisitions (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    project_id bigint,
    deadline_at timestamp without time zone,
    position_name character varying(255),
    info text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    recruiter_id bigint,
    org_chart_position jsonb,
    custom_fields jsonb
);
     DROP TABLE public.requisitions;
       public         heap    jes.87kXz4RuQk    false            =           1259    147458    requisitions_id_seq    SEQUENCE     �   ALTER TABLE public.requisitions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.requisitions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    269                       1259    147185    scorecard_comments    TABLE       CREATE TABLE public.scorecard_comments (
    id bigint NOT NULL,
    user_id bigint NOT NULL,
    project_id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    comment text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 &   DROP TABLE public.scorecard_comments;
       public         heap    jes.87kXz4RuQk    false            :           1259    147438    scorecard_comments_id_seq    SEQUENCE     �   ALTER TABLE public.scorecard_comments ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.scorecard_comments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    268                       1259    147195    scorecard_questions    TABLE       CREATE TABLE public.scorecard_questions (
    id bigint NOT NULL,
    scorecard_id bigint NOT NULL,
    question text NOT NULL,
    sort_order integer DEFAULT 0 NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 '   DROP TABLE public.scorecard_questions;
       public         heap    jes.87kXz4RuQk    false            5           1259    147411    scorecard_questions_id_seq    SEQUENCE     �   ALTER TABLE public.scorecard_questions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.scorecard_questions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    270                       1259    147197    scorecard_responses    TABLE     :  CREATE TABLE public.scorecard_responses (
    id bigint NOT NULL,
    scorecard_question_id bigint NOT NULL,
    user_id bigint NOT NULL,
    project_id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    score smallint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 '   DROP TABLE public.scorecard_responses;
       public         heap    jes.87kXz4RuQk    false            ]           1259    147672    scorecard_responses_id_seq    SEQUENCE     �   ALTER TABLE public.scorecard_responses ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.scorecard_responses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    272                       1259    147201 
   scorecards    TABLE     �   CREATE TABLE public.scorecards (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.scorecards;
       public         heap    jes.87kXz4RuQk    false            E           1259    147514    scorecards_id_seq    SEQUENCE     �   ALTER TABLE public.scorecards ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.scorecards_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    273                       1259    147232    sessions    TABLE     �   CREATE TABLE public.sessions (
    id character varying(255) NOT NULL,
    user_id bigint,
    ip_address character varying(45),
    user_agent text,
    payload text NOT NULL,
    last_activity integer NOT NULL
);
    DROP TABLE public.sessions;
       public         heap    jes.87kXz4RuQk    false                       1259    147236    settings    TABLE     �   CREATE TABLE public.settings (
    id bigint NOT NULL,
    key character varying(255) NOT NULL,
    value_json jsonb NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.settings;
       public         heap    jes.87kXz4RuQk    false            M           1259    147563    settings_id_seq    SEQUENCE     �   ALTER TABLE public.settings ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.settings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    277                       1259    147241    sms    TABLE     �   CREATE TABLE public.sms (
    id bigint NOT NULL,
    user_id bigint,
    project_id bigint,
    body text,
    merge_fields jsonb,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.sms;
       public         heap    jes.87kXz4RuQk    false            -           1259    147356 
   sms_id_seq    SEQUENCE     �   ALTER TABLE public.sms ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.sms_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    278                       1259    147252    source_budgets    TABLE       CREATE TABLE public.source_budgets (
    id bigint NOT NULL,
    source_name character varying(255) NOT NULL,
    project_id bigint NOT NULL,
    budget double precision NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 "   DROP TABLE public.source_budgets;
       public         heap    jes.87kXz4RuQk    false            4           1259    147406    source_budgets_id_seq    SEQUENCE     �   ALTER TABLE public.source_budgets ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.source_budgets_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    279                       1259    147257    stages    TABLE     �  CREATE TABLE public.stages (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    project_id bigint NOT NULL,
    sort_order smallint DEFAULT 0 NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    visible_for_limited smallint DEFAULT 1 NOT NULL,
    category smallint,
    summary_mode character varying(255) DEFAULT 'off'::character varying NOT NULL,
    custom_stage_category_id bigint,
    fair_evaluations smallint DEFAULT 0 NOT NULL
);
    DROP TABLE public.stages;
       public         heap    jes.87kXz4RuQk    false            /           1259    147373 
   stages_id_seq    SEQUENCE     �   ALTER TABLE public.stages ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.stages_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    280            +           1259    147339    structured_job_ads    TABLE       CREATE TABLE public.structured_job_ads (
    id bigint NOT NULL,
    position_name character varying(255),
    deadline_at date,
    publish_cvo smallint,
    contact_email character varying(255),
    contact_name character varying(255),
    contact_phone character varying(255),
    work_times jsonb,
    landing_id bigint,
    project_id bigint,
    custom_job_ad_url character varying(255),
    cvo_remote_source_id bigint,
    cvo_target_stage_id bigint,
    cvo_id integer,
    cvo_status text,
    required_languages jsonb,
    cvo_location jsonb,
    cvo_categories jsonb,
    benefits character varying(255),
    salary_from numeric(8,2),
    salary_to numeric(8,2),
    salary_period character varying(255),
    totaljobs_salary_rate character varying(255),
    totaljobs_salary_range character varying(255),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    employer_logo_location character varying(255),
    employer_video_url character varying(255),
    employer_web_url character varying(255),
    employer_about text,
    language character varying(255),
    cvk_city character varying(255),
    cvk_categories jsonb,
    description text,
    requirements text,
    we_offer text,
    contract_type smallint,
    cvk_work_times jsonb,
    cvk_level smallint,
    screening_questions jsonb,
    cvk_target_stage_id bigint,
    cvk_county character varying(255),
    cvo_use_portal_apply smallint DEFAULT 0 NOT NULL,
    location character varying(255),
    publish_social_media smallint DEFAULT 0 NOT NULL,
    social_media_params jsonb,
    payment jsonb,
    social_results jsonb,
    cvl_types jsonb,
    cvl_industry integer,
    cvl_additional_industries jsonb,
    cvl_region integer,
    cvl_precise_location character varying(255),
    cvl_display_location character varying(255),
    cvl_relocate_from_eu character varying(255),
    is_remote smallint DEFAULT 0 NOT NULL,
    cvl_hide_salary smallint DEFAULT 0 NOT NULL,
    cvb_city character varying(255),
    cvb_category integer,
    cvb_group integer,
    salary_info text,
    cvb_language character varying(255),
    cvb_target_stage_id bigint,
    li_workplace_type character varying(255),
    li_industry_codes jsonb,
    li_experience_level character varying(255),
    li_job_functions jsonb,
    main_job_type character varying(255),
    cvk_country character varying(255),
    cv_lt_cities jsonb,
    cv_lt_position_type character varying(255),
    cv_lt_departments jsonb,
    target_stage_id bigint,
    cv_lt_work_time character varying(255),
    indeed_city character varying(255),
    indeed_state character varying(255),
    indeed_country character varying(255),
    totaljobs_region character varying(255),
    totaljobs_town character varying(255),
    postcode character varying(255),
    salary_currency character varying(255) DEFAULT 'EUR'::character varying,
    talent_category character varying(255),
    totaljobs_job_type character varying(255),
    totaljobs_position_type character varying(255),
    totaljobs_sector_type character varying(255)
);
 &   DROP TABLE public.structured_job_ads;
       public         heap    jes.87kXz4RuQk    false            U           1259    147617    structured_job_ads_id_seq    SEQUENCE     �   ALTER TABLE public.structured_job_ads ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.structured_job_ads_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    299            &           1259    147323    submissions    TABLE     �   CREATE TABLE public.submissions (
    id bigint NOT NULL,
    form_id bigint NOT NULL,
    candidate_id bigint,
    data jsonb NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    user_data jsonb
);
    DROP TABLE public.submissions;
       public         heap    jes.87kXz4RuQk    false            9           1259    147433    submissions_id_seq    SEQUENCE     �   ALTER TABLE public.submissions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.submissions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    294                       1259    147272    surveys    TABLE       CREATE TABLE public.surveys (
    id bigint NOT NULL,
    token character varying(255) NOT NULL,
    project_id bigint,
    candidate_id bigint NOT NULL,
    response smallint,
    comment text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.surveys;
       public         heap    jes.87kXz4RuQk    false            X           1259    147639    surveys_id_seq    SEQUENCE     �   ALTER TABLE public.surveys ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.surveys_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    284                       1259    147262    tags    TABLE       CREATE TABLE public.tags (
    id bigint NOT NULL,
    color character varying(255) DEFAULT '#28a745'::character varying NOT NULL,
    name character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.tags;
       public         heap    jes.87kXz4RuQk    false            G           1259    147523    tags_id_seq    SEQUENCE     �   ALTER TABLE public.tags ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.tags_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    281            *           1259    147337    tasks    TABLE     n  CREATE TABLE public.tasks (
    id bigint NOT NULL,
    done smallint DEFAULT 0 NOT NULL,
    title character varying(255) NOT NULL,
    candidate_id bigint NOT NULL,
    project_id bigint NOT NULL,
    assignee_id bigint,
    author_id bigint NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deadline_at date
);
    DROP TABLE public.tasks;
       public         heap    jes.87kXz4RuQk    false            L           1259    147556    tasks_id_seq    SEQUENCE     �   ALTER TABLE public.tasks ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.tasks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    298            (           1259    147329 
   team_edges    TABLE     k   CREATE TABLE public.team_edges (
    source_team_id bigint NOT NULL,
    target_team_id bigint NOT NULL
);
    DROP TABLE public.team_edges;
       public         heap    jes.87kXz4RuQk    false            '           1259    147328    teams    TABLE     �   CREATE TABLE public.teams (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    x double precision,
    y double precision,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.teams;
       public         heap    jes.87kXz4RuQk    false            C           1259    147504    teams_id_seq    SEQUENCE     �   ALTER TABLE public.teams ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.teams_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    295                       1259    147273 	   templates    TABLE     _  CREATE TABLE public.templates (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    subject character varying(255),
    body text NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    template_type smallint DEFAULT 1 NOT NULL,
    user_id bigint,
    data jsonb,
    team_id bigint
);
    DROP TABLE public.templates;
       public         heap    jes.87kXz4RuQk    false            A           1259    147489    templates_id_seq    SEQUENCE     �   ALTER TABLE public.templates ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.templates_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    285                       1259    147274 
   time_slots    TABLE       CREATE TABLE public.time_slots (
    id bigint NOT NULL,
    event_set_id bigint NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone NOT NULL,
    max_participants smallint DEFAULT 1 NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    uid character varying(255),
    ics_sequence bigint DEFAULT 0 NOT NULL,
    room_email character varying(255),
    room_name character varying(255),
    room_address character varying(255)
);
    DROP TABLE public.time_slots;
       public         heap    jes.87kXz4RuQk    false            V           1259    147629    time_slots_id_seq    SEQUENCE     �   ALTER TABLE public.time_slots ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.time_slots_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    286                       1259    147282    user_video_call    TABLE     h   CREATE TABLE public.user_video_call (
    video_call_id bigint NOT NULL,
    user_id bigint NOT NULL
);
 #   DROP TABLE public.user_video_call;
       public         heap    jes.87kXz4RuQk    false            !           1259    147297    users    TABLE     �  CREATE TABLE public.users (
    id bigint NOT NULL,
    name character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    email_verified_at timestamp without time zone,
    password character varying(255) NOT NULL,
    api_token character varying(80),
    smtp_server character varying(255),
    smtp_port character varying(255),
    smtp_email character varying(255),
    smtp_username character varying(255),
    smtp_password text,
    remember_token character varying(100),
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    signature text,
    role character varying(255) DEFAULT 'admin'::character varying NOT NULL,
    smtp_encryption character varying(255) DEFAULT 'tls'::character varying,
    accepted_tos date,
    accepted_privacy_policy date,
    active smallint DEFAULT 1 NOT NULL,
    phone character varying(255),
    job_title character varying(255),
    department character varying(255),
    use_text_signature smallint DEFAULT 0 NOT NULL,
    project_search_filters jsonb,
    pinned_project_ids jsonb,
    two_factor_phone character varying(255),
    last_active_at timestamp without time zone,
    disable_activity_notifications smallint DEFAULT 0 NOT NULL,
    allow_password_login smallint DEFAULT 1 NOT NULL,
    sso_uid character varying(255),
    calendar_search_filters jsonb,
    team_id bigint,
    is_teams_admin smallint DEFAULT 0 NOT NULL
);
    DROP TABLE public.users;
       public         heap    jes.87kXz4RuQk    false            T           1259    147605    users_id_seq    SEQUENCE     �   ALTER TABLE public.users ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    289                        1259    147294    video_calls    TABLE     �   CREATE TABLE public.video_calls (
    id bigint NOT NULL,
    token character varying(255) NOT NULL,
    project_id bigint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.video_calls;
       public         heap    jes.87kXz4RuQk    false            H           1259    147526    video_calls_id_seq    SEQUENCE     �   ALTER TABLE public.video_calls ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.video_calls_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    288            "           1259    147298    video_interview_invites    TABLE     L  CREATE TABLE public.video_interview_invites (
    id bigint NOT NULL,
    video_interview_id bigint NOT NULL,
    candidate_id bigint NOT NULL,
    message_id bigint,
    project_id bigint NOT NULL,
    token character varying(255) NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
 +   DROP TABLE public.video_interview_invites;
       public         heap    jes.87kXz4RuQk    false            7           1259    147419    video_interview_invites_id_seq    SEQUENCE     �   ALTER TABLE public.video_interview_invites ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.video_interview_invites_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    290            )           1259    147330    video_interview_questions    TABLE     �  CREATE TABLE public.video_interview_questions (
    id bigint NOT NULL,
    video_interview_id bigint NOT NULL,
    type character varying(255) DEFAULT 'text'::character varying NOT NULL,
    title character varying(255),
    contents text,
    video_id bigint,
    max_response_duration integer DEFAULT 60 NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    sort_order integer DEFAULT 0 NOT NULL
);
 -   DROP TABLE public.video_interview_questions;
       public         heap    jes.87kXz4RuQk    false            @           1259    147483     video_interview_questions_id_seq    SEQUENCE     �   ALTER TABLE public.video_interview_questions ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.video_interview_questions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    297            %           1259    147320    video_interview_responses    TABLE     @  CREATE TABLE public.video_interview_responses (
    id bigint NOT NULL,
    video_interview_invite_id bigint NOT NULL,
    video_interview_question_id bigint NOT NULL,
    video_id bigint,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    attempts integer DEFAULT 0 NOT NULL
);
 -   DROP TABLE public.video_interview_responses;
       public         heap    jes.87kXz4RuQk    false            Y           1259    147645     video_interview_responses_id_seq    SEQUENCE     �   ALTER TABLE public.video_interview_responses ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.video_interview_responses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    293            $           1259    147313    video_interviews    TABLE        CREATE TABLE public.video_interviews (
    id bigint NOT NULL,
    title character varying(255) NOT NULL,
    intro_contents text,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    status smallint DEFAULT 1 NOT NULL,
    max_attempts smallint
);
 $   DROP TABLE public.video_interviews;
       public         heap    jes.87kXz4RuQk    false            8           1259    147427    video_interviews_id_seq    SEQUENCE     �   ALTER TABLE public.video_interviews ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.video_interviews_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    292            #           1259    147312    videos    TABLE       CREATE TABLE public.videos (
    id bigint NOT NULL,
    cf_uid character varying(255) NOT NULL,
    processed_at timestamp without time zone,
    duration integer DEFAULT 0 NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);
    DROP TABLE public.videos;
       public         heap    jes.87kXz4RuQk    false            2           1259    147394 
   videos_id_seq    SEQUENCE     �   ALTER TABLE public.videos ALTER COLUMN id ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME public.videos_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);
            public          jes.87kXz4RuQk    false    291            �          0    147169 
   migrations 
   TABLE DATA           :   COPY public.migrations (id, migration, batch) FROM stdin;
    public          jes.87kXz4RuQk    false    261   Ȅ      �           0    0    actions_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.actions_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    302            �           0    0    activities_id_seq    SEQUENCE SET     ?   SELECT pg_catalog.setval('public.activities_id_seq', 9, true);
          public          jes.87kXz4RuQk    false    357            �           0    0    activity_receipts_id_seq    SEQUENCE SET     G   SELECT pg_catalog.setval('public.activity_receipts_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    359            �           0    0    activity_subscriptions_id_seq    SEQUENCE SET     L   SELECT pg_catalog.setval('public.activity_subscriptions_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    358            �           0    0    activity_types_id_seq    SEQUENCE SET     C   SELECT pg_catalog.setval('public.activity_types_id_seq', 3, true);
          public          jes.87kXz4RuQk    false    346            �           0    0    api_keys_id_seq    SEQUENCE SET     >   SELECT pg_catalog.setval('public.api_keys_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    338            �           0    0 
   audits_id_seq    SEQUENCE SET     <   SELECT pg_catalog.setval('public.audits_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    352            �           0    0    auth_tokens_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.auth_tokens_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    356            �           0    0    calls_id_seq    SEQUENCE SET     ;   SELECT pg_catalog.setval('public.calls_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    318            �           0    0    candidate_cv_ai_summary_id_seq    SEQUENCE SET     M   SELECT pg_catalog.setval('public.candidate_cv_ai_summary_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    355                        0    0    candidate_filters_id_seq    SEQUENCE SET     G   SELECT pg_catalog.setval('public.candidate_filters_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    347                       0    0    candidate_summaries_id_seq    SEQUENCE SET     I   SELECT pg_catalog.setval('public.candidate_summaries_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    307                       0    0    candidates_id_seq    SEQUENCE SET     ?   SELECT pg_catalog.setval('public.candidates_id_seq', 4, true);
          public          jes.87kXz4RuQk    false    365                       0    0    comments_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.comments_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    366                       0    0    consent_renewals_id_seq    SEQUENCE SET     F   SELECT pg_catalog.setval('public.consent_renewals_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    354                       0    0    consents_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.consents_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    353                       0    0    crm_contacts_id_seq    SEQUENCE SET     B   SELECT pg_catalog.setval('public.crm_contacts_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    336                       0    0    crm_offices_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.crm_offices_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    364                       0    0    crm_organizations_id_seq    SEQUENCE SET     G   SELECT pg_catalog.setval('public.crm_organizations_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    363            	           0    0    custom_stage_categories_id_seq    SEQUENCE SET     M   SELECT pg_catalog.setval('public.custom_stage_categories_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    362            
           0    0    dropout_reasons_id_seq    SEQUENCE SET     E   SELECT pg_catalog.setval('public.dropout_reasons_id_seq', 12, true);
          public          jes.87kXz4RuQk    false    361                       0    0    educations_id_seq    SEQUENCE SET     @   SELECT pg_catalog.setval('public.educations_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    360                       0    0    employments_id_seq    SEQUENCE SET     @   SELECT pg_catalog.setval('public.employments_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    348            
           0    0    event_sets_id_seq    SEQUENCE SET     @   SELECT pg_catalog.setval('public.event_sets_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    339                       0    0 
   events_id_seq    SEQUENCE SET     <   SELECT pg_catalog.setval('public.events_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    319                       0    0    failed_jobs_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.failed_jobs_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    310                       0    0    files_id_seq    SEQUENCE SET     ;   SELECT pg_catalog.setval('public.files_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    326                       0    0    forms_id_seq    SEQUENCE SET     ;   SELECT pg_catalog.setval('public.forms_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    316                       0    0    imports_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.imports_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    337                       0    0    integrations_id_seq    SEQUENCE SET     B   SELECT pg_catalog.setval('public.integrations_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    329                       0    0    invites_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.invites_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    350                       0    0    job_ad_feeds_id_seq    SEQUENCE SET     B   SELECT pg_catalog.setval('public.job_ad_feeds_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    315                       0    0    landing_dei_reports_id_seq    SEQUENCE SET     I   SELECT pg_catalog.setval('public.landing_dei_reports_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    335                       0    0    landing_tags_id_seq    SEQUENCE SET     B   SELECT pg_catalog.setval('public.landing_tags_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    351                       0    0    landings_id_seq    SEQUENCE SET     >   SELECT pg_catalog.setval('public.landings_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    305                       0    0    messages_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.messages_id_seq', 2, true);
          public          jes.87kXz4RuQk    false    324                       0    0    migrations_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.migrations_id_seq', 223, true);
          public          jes.87kXz4RuQk    false    300                       0    0    nps_surveys_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.nps_surveys_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    304                       0    0    presentations_id_seq    SEQUENCE SET     C   SELECT pg_catalog.setval('public.presentations_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    334                       0    0    project_logs_id_seq    SEQUENCE SET     B   SELECT pg_catalog.setval('public.project_logs_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    322                       0    0    projects_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.projects_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    331                       0    0 
   reacts_id_seq    SEQUENCE SET     <   SELECT pg_catalog.setval('public.reacts_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    343                        0    0    requisition_approvals_id_seq    SEQUENCE SET     K   SELECT pg_catalog.setval('public.requisition_approvals_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    330            !           0    0    requisitions_id_seq    SEQUENCE SET     B   SELECT pg_catalog.setval('public.requisitions_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    317            "           0    0    scorecard_comments_id_seq    SEQUENCE SET     H   SELECT pg_catalog.setval('public.scorecard_comments_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    314            #           0    0    scorecard_questions_id_seq    SEQUENCE SET     I   SELECT pg_catalog.setval('public.scorecard_questions_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    309            $           0    0    scorecard_responses_id_seq    SEQUENCE SET     I   SELECT pg_catalog.setval('public.scorecard_responses_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    349            %           0    0    scorecards_id_seq    SEQUENCE SET     @   SELECT pg_catalog.setval('public.scorecards_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    325            &           0    0    settings_id_seq    SEQUENCE SET     >   SELECT pg_catalog.setval('public.settings_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    333            '           0    0 
   sms_id_seq    SEQUENCE SET     9   SELECT pg_catalog.setval('public.sms_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    301            (           0    0    source_budgets_id_seq    SEQUENCE SET     D   SELECT pg_catalog.setval('public.source_budgets_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    308            )           0    0 
   stages_id_seq    SEQUENCE SET     ;   SELECT pg_catalog.setval('public.stages_id_seq', 6, true);
          public          jes.87kXz4RuQk    false    303            *           0    0    structured_job_ads_id_seq    SEQUENCE SET     H   SELECT pg_catalog.setval('public.structured_job_ads_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    341            +           0    0    submissions_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.submissions_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    313            ,           0    0    surveys_id_seq    SEQUENCE SET     =   SELECT pg_catalog.setval('public.surveys_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    344            -           0    0    tags_id_seq    SEQUENCE SET     9   SELECT pg_catalog.setval('public.tags_id_seq', 4, true);
          public          jes.87kXz4RuQk    false    327            .           0    0    tasks_id_seq    SEQUENCE SET     ;   SELECT pg_catalog.setval('public.tasks_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    332            /           0    0    teams_id_seq    SEQUENCE SET     ;   SELECT pg_catalog.setval('public.teams_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    323            0           0    0    templates_id_seq    SEQUENCE SET     ?   SELECT pg_catalog.setval('public.templates_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    321            1           0    0    time_slots_id_seq    SEQUENCE SET     @   SELECT pg_catalog.setval('public.time_slots_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    342            2           0    0    users_id_seq    SEQUENCE SET     :   SELECT pg_catalog.setval('public.users_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    340            3           0    0    video_calls_id_seq    SEQUENCE SET     A   SELECT pg_catalog.setval('public.video_calls_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    328            4           0    0    video_interview_invites_id_seq    SEQUENCE SET     L   SELECT pg_catalog.setval('public.video_interview_invites_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    311            5           0    0     video_interview_questions_id_seq    SEQUENCE SET     N   SELECT pg_catalog.setval('public.video_interview_questions_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    320            6           0    0     video_interview_responses_id_seq    SEQUENCE SET     O   SELECT pg_catalog.setval('public.video_interview_responses_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    345            7           0    0    video_interviews_id_seq    SEQUENCE SET     E   SELECT pg_catalog.setval('public.video_interviews_id_seq', 1, true);
          public          jes.87kXz4RuQk    false    312            8           0    0 
   videos_id_seq    SEQUENCE SET     <   SELECT pg_catalog.setval('public.videos_id_seq', 1, false);
          public          jes.87kXz4RuQk    false    306            �           2606    147366    actions actions_pkey 
   CONSTRAINT     R   ALTER TABLE ONLY public.actions
    ADD CONSTRAINT actions_pkey PRIMARY KEY (id);
 >   ALTER TABLE ONLY public.actions DROP CONSTRAINT actions_pkey;
       public            jes.87kXz4RuQk    false    238            �           2606    147719    activities activities_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_pkey;
       public            jes.87kXz4RuQk    false    223            �           2606    147746 (   activity_receipts activity_receipts_pkey 
   CONSTRAINT     f   ALTER TABLE ONLY public.activity_receipts
    ADD CONSTRAINT activity_receipts_pkey PRIMARY KEY (id);
 R   ALTER TABLE ONLY public.activity_receipts DROP CONSTRAINT activity_receipts_pkey;
       public            jes.87kXz4RuQk    false    224            �           2606    147738 2   activity_subscriptions activity_subscriptions_pkey 
   CONSTRAINT     p   ALTER TABLE ONLY public.activity_subscriptions
    ADD CONSTRAINT activity_subscriptions_pkey PRIMARY KEY (id);
 \   ALTER TABLE ONLY public.activity_subscriptions DROP CONSTRAINT activity_subscriptions_pkey;
       public            jes.87kXz4RuQk    false    233            �           2606    147658 "   activity_types activity_types_pkey 
   CONSTRAINT     `   ALTER TABLE ONLY public.activity_types
    ADD CONSTRAINT activity_types_pkey PRIMARY KEY (id);
 L   ALTER TABLE ONLY public.activity_types DROP CONSTRAINT activity_types_pkey;
       public            jes.87kXz4RuQk    false    227            �           2606    147588    api_keys api_keys_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.api_keys
    ADD CONSTRAINT api_keys_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.api_keys DROP CONSTRAINT api_keys_pkey;
       public            jes.87kXz4RuQk    false    229            �           2606    147691    audits audits_pkey 
   CONSTRAINT     P   ALTER TABLE ONLY public.audits
    ADD CONSTRAINT audits_pkey PRIMARY KEY (id);
 <   ALTER TABLE ONLY public.audits DROP CONSTRAINT audits_pkey;
       public            jes.87kXz4RuQk    false    225            �           2606    147714    auth_tokens auth_tokens_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.auth_tokens
    ADD CONSTRAINT auth_tokens_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.auth_tokens DROP CONSTRAINT auth_tokens_pkey;
       public            jes.87kXz4RuQk    false    228            �           2606    147469    calls calls_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.calls
    ADD CONSTRAINT calls_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.calls DROP CONSTRAINT calls_pkey;
       public            jes.87kXz4RuQk    false    242            �           2606    147709 4   candidate_cv_ai_summary candidate_cv_ai_summary_pkey 
   CONSTRAINT     r   ALTER TABLE ONLY public.candidate_cv_ai_summary
    ADD CONSTRAINT candidate_cv_ai_summary_pkey PRIMARY KEY (id);
 ^   ALTER TABLE ONLY public.candidate_cv_ai_summary DROP CONSTRAINT candidate_cv_ai_summary_pkey;
       public            jes.87kXz4RuQk    false    249            �           2606    147662 (   candidate_filters candidate_filters_pkey 
   CONSTRAINT     f   ALTER TABLE ONLY public.candidate_filters
    ADD CONSTRAINT candidate_filters_pkey PRIMARY KEY (id);
 R   ALTER TABLE ONLY public.candidate_filters DROP CONSTRAINT candidate_filters_pkey;
       public            jes.87kXz4RuQk    false    232            �           2606    147401 ,   candidate_summaries candidate_summaries_pkey 
   CONSTRAINT     j   ALTER TABLE ONLY public.candidate_summaries
    ADD CONSTRAINT candidate_summaries_pkey PRIMARY KEY (id);
 V   ALTER TABLE ONLY public.candidate_summaries DROP CONSTRAINT candidate_summaries_pkey;
       public            jes.87kXz4RuQk    false    237            _           2606    147774    candidates candidates_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.candidates
    ADD CONSTRAINT candidates_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.candidates DROP CONSTRAINT candidates_pkey;
       public            jes.87kXz4RuQk    false    215            c           2606    147782    comments comments_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.comments DROP CONSTRAINT comments_pkey;
       public            jes.87kXz4RuQk    false    216            �           2606    147703 &   consent_renewals consent_renewals_pkey 
   CONSTRAINT     d   ALTER TABLE ONLY public.consent_renewals
    ADD CONSTRAINT consent_renewals_pkey PRIMARY KEY (id);
 P   ALTER TABLE ONLY public.consent_renewals DROP CONSTRAINT consent_renewals_pkey;
       public            jes.87kXz4RuQk    false    235            �           2606    147697    consents consents_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.consents
    ADD CONSTRAINT consents_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.consents DROP CONSTRAINT consents_pkey;
       public            jes.87kXz4RuQk    false    250            �           2606    147581    crm_contacts crm_contacts_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.crm_contacts
    ADD CONSTRAINT crm_contacts_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.crm_contacts DROP CONSTRAINT crm_contacts_pkey;
       public            jes.87kXz4RuQk    false    251            j           2606    147767    crm_offices crm_offices_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.crm_offices
    ADD CONSTRAINT crm_offices_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.crm_offices DROP CONSTRAINT crm_offices_pkey;
       public            jes.87kXz4RuQk    false    217            m           2606    147763 (   crm_organizations crm_organizations_pkey 
   CONSTRAINT     f   ALTER TABLE ONLY public.crm_organizations
    ADD CONSTRAINT crm_organizations_pkey PRIMARY KEY (id);
 R   ALTER TABLE ONLY public.crm_organizations DROP CONSTRAINT crm_organizations_pkey;
       public            jes.87kXz4RuQk    false    218            o           2606    147760 4   custom_stage_categories custom_stage_categories_pkey 
   CONSTRAINT     r   ALTER TABLE ONLY public.custom_stage_categories
    ADD CONSTRAINT custom_stage_categories_pkey PRIMARY KEY (id);
 ^   ALTER TABLE ONLY public.custom_stage_categories DROP CONSTRAINT custom_stage_categories_pkey;
       public            jes.87kXz4RuQk    false    219            q           2606    147757 $   dropout_reasons dropout_reasons_pkey 
   CONSTRAINT     b   ALTER TABLE ONLY public.dropout_reasons
    ADD CONSTRAINT dropout_reasons_pkey PRIMARY KEY (id);
 N   ALTER TABLE ONLY public.dropout_reasons DROP CONSTRAINT dropout_reasons_pkey;
       public            jes.87kXz4RuQk    false    220            t           2606    147753    educations educations_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.educations
    ADD CONSTRAINT educations_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.educations DROP CONSTRAINT educations_pkey;
       public            jes.87kXz4RuQk    false    221            �           2606    147668    employments employments_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.employments
    ADD CONSTRAINT employments_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.employments DROP CONSTRAINT employments_pkey;
       public            jes.87kXz4RuQk    false    226            �           2606    147595    event_sets event_sets_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.event_sets
    ADD CONSTRAINT event_sets_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.event_sets DROP CONSTRAINT event_sets_pkey;
       public            jes.87kXz4RuQk    false    234            �           2606    147479    events events_pkey 
   CONSTRAINT     P   ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);
 <   ALTER TABLE ONLY public.events DROP CONSTRAINT events_pkey;
       public            jes.87kXz4RuQk    false    241            �           2606    147418    failed_jobs failed_jobs_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.failed_jobs
    ADD CONSTRAINT failed_jobs_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.failed_jobs DROP CONSTRAINT failed_jobs_pkey;
       public            jes.87kXz4RuQk    false    243            �           2606    147519    files files_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.files DROP CONSTRAINT files_pkey;
       public            jes.87kXz4RuQk    false    245            +           2606    147455    forms forms_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.forms
    ADD CONSTRAINT forms_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.forms DROP CONSTRAINT forms_pkey;
       public            jes.87kXz4RuQk    false    275            �           2606    147585    imports imports_pkey 
   CONSTRAINT     R   ALTER TABLE ONLY public.imports
    ADD CONSTRAINT imports_pkey PRIMARY KEY (id);
 >   ALTER TABLE ONLY public.imports DROP CONSTRAINT imports_pkey;
       public            jes.87kXz4RuQk    false    246                       2606    147533    integrations integrations_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT integrations_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.integrations DROP CONSTRAINT integrations_pkey;
       public            jes.87kXz4RuQk    false    271            �           2606    147681    invites invites_pkey 
   CONSTRAINT     R   ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_pkey PRIMARY KEY (id);
 >   ALTER TABLE ONLY public.invites DROP CONSTRAINT invites_pkey;
       public            jes.87kXz4RuQk    false    252            �           2606    147449    job_ad_feeds job_ad_feeds_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.job_ad_feeds
    ADD CONSTRAINT job_ad_feeds_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.job_ad_feeds DROP CONSTRAINT job_ad_feeds_pkey;
       public            jes.87kXz4RuQk    false    254            �           2606    147577 ,   landing_dei_reports landing_dei_reports_pkey 
   CONSTRAINT     j   ALTER TABLE ONLY public.landing_dei_reports
    ADD CONSTRAINT landing_dei_reports_pkey PRIMARY KEY (id);
 V   ALTER TABLE ONLY public.landing_dei_reports DROP CONSTRAINT landing_dei_reports_pkey;
       public            jes.87kXz4RuQk    false    255            �           2606    147688    landing_tags landing_tags_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.landing_tags
    ADD CONSTRAINT landing_tags_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.landing_tags DROP CONSTRAINT landing_tags_pkey;
       public            jes.87kXz4RuQk    false    257            �           2606    147389    landings landings_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.landings
    ADD CONSTRAINT landings_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.landings DROP CONSTRAINT landings_pkey;
       public            jes.87kXz4RuQk    false    258            �           2606    147509    messages messages_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.messages DROP CONSTRAINT messages_pkey;
       public            jes.87kXz4RuQk    false    260            �           2606    147352    migrations migrations_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.migrations DROP CONSTRAINT migrations_pkey;
       public            jes.87kXz4RuQk    false    261                       2606    147380    nps_surveys nps_surveys_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.nps_surveys
    ADD CONSTRAINT nps_surveys_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.nps_surveys DROP CONSTRAINT nps_surveys_pkey;
       public            jes.87kXz4RuQk    false    264            �           2606    147571     presentations presentations_pkey 
   CONSTRAINT     ^   ALTER TABLE ONLY public.presentations
    ADD CONSTRAINT presentations_pkey PRIMARY KEY (id);
 J   ALTER TABLE ONLY public.presentations DROP CONSTRAINT presentations_pkey;
       public            jes.87kXz4RuQk    false    263            '           2606    147497    project_logs project_logs_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.project_logs
    ADD CONSTRAINT project_logs_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.project_logs DROP CONSTRAINT project_logs_pkey;
       public            jes.87kXz4RuQk    false    274            G           2606    147552    projects projects_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.projects DROP CONSTRAINT projects_pkey;
       public            jes.87kXz4RuQk    false    283            A           2606    147636    reacts reacts_pkey 
   CONSTRAINT     P   ALTER TABLE ONLY public.reacts
    ADD CONSTRAINT reacts_pkey PRIMARY KEY (id);
 <   ALTER TABLE ONLY public.reacts DROP CONSTRAINT reacts_pkey;
       public            jes.87kXz4RuQk    false    282                       2606    147539 0   requisition_approvals requisition_approvals_pkey 
   CONSTRAINT     n   ALTER TABLE ONLY public.requisition_approvals
    ADD CONSTRAINT requisition_approvals_pkey PRIMARY KEY (id);
 Z   ALTER TABLE ONLY public.requisition_approvals DROP CONSTRAINT requisition_approvals_pkey;
       public            jes.87kXz4RuQk    false    266                       2606    147460    requisitions requisitions_pkey 
   CONSTRAINT     \   ALTER TABLE ONLY public.requisitions
    ADD CONSTRAINT requisitions_pkey PRIMARY KEY (id);
 H   ALTER TABLE ONLY public.requisitions DROP CONSTRAINT requisitions_pkey;
       public            jes.87kXz4RuQk    false    269                       2606    147440 *   scorecard_comments scorecard_comments_pkey 
   CONSTRAINT     h   ALTER TABLE ONLY public.scorecard_comments
    ADD CONSTRAINT scorecard_comments_pkey PRIMARY KEY (id);
 T   ALTER TABLE ONLY public.scorecard_comments DROP CONSTRAINT scorecard_comments_pkey;
       public            jes.87kXz4RuQk    false    268                       2606    147413 ,   scorecard_questions scorecard_questions_pkey 
   CONSTRAINT     j   ALTER TABLE ONLY public.scorecard_questions
    ADD CONSTRAINT scorecard_questions_pkey PRIMARY KEY (id);
 V   ALTER TABLE ONLY public.scorecard_questions DROP CONSTRAINT scorecard_questions_pkey;
       public            jes.87kXz4RuQk    false    270                        2606    147674 ,   scorecard_responses scorecard_responses_pkey 
   CONSTRAINT     j   ALTER TABLE ONLY public.scorecard_responses
    ADD CONSTRAINT scorecard_responses_pkey PRIMARY KEY (id);
 V   ALTER TABLE ONLY public.scorecard_responses DROP CONSTRAINT scorecard_responses_pkey;
       public            jes.87kXz4RuQk    false    272            %           2606    147516    scorecards scorecards_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.scorecards
    ADD CONSTRAINT scorecards_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.scorecards DROP CONSTRAINT scorecards_pkey;
       public            jes.87kXz4RuQk    false    273            1           2606    147565    settings settings_pkey 
   CONSTRAINT     T   ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_pkey PRIMARY KEY (id);
 @   ALTER TABLE ONLY public.settings DROP CONSTRAINT settings_pkey;
       public            jes.87kXz4RuQk    false    277            3           2606    147358    sms sms_pkey 
   CONSTRAINT     J   ALTER TABLE ONLY public.sms
    ADD CONSTRAINT sms_pkey PRIMARY KEY (id);
 6   ALTER TABLE ONLY public.sms DROP CONSTRAINT sms_pkey;
       public            jes.87kXz4RuQk    false    278            7           2606    147409 "   source_budgets source_budgets_pkey 
   CONSTRAINT     `   ALTER TABLE ONLY public.source_budgets
    ADD CONSTRAINT source_budgets_pkey PRIMARY KEY (id);
 L   ALTER TABLE ONLY public.source_budgets DROP CONSTRAINT source_budgets_pkey;
       public            jes.87kXz4RuQk    false    279            ;           2606    147375    stages stages_pkey 
   CONSTRAINT     P   ALTER TABLE ONLY public.stages
    ADD CONSTRAINT stages_pkey PRIMARY KEY (id);
 <   ALTER TABLE ONLY public.stages DROP CONSTRAINT stages_pkey;
       public            jes.87kXz4RuQk    false    280            �           2606    147619 *   structured_job_ads structured_job_ads_pkey 
   CONSTRAINT     h   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_pkey PRIMARY KEY (id);
 T   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_pkey;
       public            jes.87kXz4RuQk    false    299            p           2606    147435    submissions submissions_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.submissions DROP CONSTRAINT submissions_pkey;
       public            jes.87kXz4RuQk    false    294            M           2606    147641    surveys surveys_pkey 
   CONSTRAINT     R   ALTER TABLE ONLY public.surveys
    ADD CONSTRAINT surveys_pkey PRIMARY KEY (id);
 >   ALTER TABLE ONLY public.surveys DROP CONSTRAINT surveys_pkey;
       public            jes.87kXz4RuQk    false    284            >           2606    147525    tags tags_pkey 
   CONSTRAINT     L   ALTER TABLE ONLY public.tags
    ADD CONSTRAINT tags_pkey PRIMARY KEY (id);
 8   ALTER TABLE ONLY public.tags DROP CONSTRAINT tags_pkey;
       public            jes.87kXz4RuQk    false    281            }           2606    147558    tasks tasks_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_pkey;
       public            jes.87kXz4RuQk    false    298            r           2606    147507    teams teams_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.teams
    ADD CONSTRAINT teams_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.teams DROP CONSTRAINT teams_pkey;
       public            jes.87kXz4RuQk    false    295            P           2606    147491    templates templates_pkey 
   CONSTRAINT     V   ALTER TABLE ONLY public.templates
    ADD CONSTRAINT templates_pkey PRIMARY KEY (id);
 B   ALTER TABLE ONLY public.templates DROP CONSTRAINT templates_pkey;
       public            jes.87kXz4RuQk    false    285            U           2606    147631    time_slots time_slots_pkey 
   CONSTRAINT     X   ALTER TABLE ONLY public.time_slots
    ADD CONSTRAINT time_slots_pkey PRIMARY KEY (id);
 D   ALTER TABLE ONLY public.time_slots DROP CONSTRAINT time_slots_pkey;
       public            jes.87kXz4RuQk    false    286            [           2606    147607    users users_pkey 
   CONSTRAINT     N   ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);
 :   ALTER TABLE ONLY public.users DROP CONSTRAINT users_pkey;
       public            jes.87kXz4RuQk    false    289            W           2606    147528    video_calls video_calls_pkey 
   CONSTRAINT     Z   ALTER TABLE ONLY public.video_calls
    ADD CONSTRAINT video_calls_pkey PRIMARY KEY (id);
 F   ALTER TABLE ONLY public.video_calls DROP CONSTRAINT video_calls_pkey;
       public            jes.87kXz4RuQk    false    288            a           2606    147422 4   video_interview_invites video_interview_invites_pkey 
   CONSTRAINT     r   ALTER TABLE ONLY public.video_interview_invites
    ADD CONSTRAINT video_interview_invites_pkey PRIMARY KEY (id);
 ^   ALTER TABLE ONLY public.video_interview_invites DROP CONSTRAINT video_interview_invites_pkey;
       public            jes.87kXz4RuQk    false    290            v           2606    147485 8   video_interview_questions video_interview_questions_pkey 
   CONSTRAINT     v   ALTER TABLE ONLY public.video_interview_questions
    ADD CONSTRAINT video_interview_questions_pkey PRIMARY KEY (id);
 b   ALTER TABLE ONLY public.video_interview_questions DROP CONSTRAINT video_interview_questions_pkey;
       public            jes.87kXz4RuQk    false    297            i           2606    147648 8   video_interview_responses video_interview_responses_pkey 
   CONSTRAINT     v   ALTER TABLE ONLY public.video_interview_responses
    ADD CONSTRAINT video_interview_responses_pkey PRIMARY KEY (id);
 b   ALTER TABLE ONLY public.video_interview_responses DROP CONSTRAINT video_interview_responses_pkey;
       public            jes.87kXz4RuQk    false    293            g           2606    147429 &   video_interviews video_interviews_pkey 
   CONSTRAINT     d   ALTER TABLE ONLY public.video_interviews
    ADD CONSTRAINT video_interviews_pkey PRIMARY KEY (id);
 P   ALTER TABLE ONLY public.video_interviews DROP CONSTRAINT video_interviews_pkey;
       public            jes.87kXz4RuQk    false    292            e           2606    147396    videos videos_pkey 
   CONSTRAINT     P   ALTER TABLE ONLY public.videos
    ADD CONSTRAINT videos_pkey PRIMARY KEY (id);
 <   ALTER TABLE ONLY public.videos DROP CONSTRAINT videos_pkey;
       public            jes.87kXz4RuQk    false    291            Z           1259    147769    action_candidate_action_id_idx    INDEX     `   CREATE INDEX action_candidate_action_id_idx ON public.action_candidate USING btree (action_id);
 2   DROP INDEX public.action_candidate_action_id_idx;
       public            jes.87kXz4RuQk    false    214            [           1259    147770 !   action_candidate_candidate_id_idx    INDEX     f   CREATE INDEX action_candidate_candidate_id_idx ON public.action_candidate USING btree (candidate_id);
 5   DROP INDEX public.action_candidate_candidate_id_idx;
       public            jes.87kXz4RuQk    false    214            w           1259    147717    activities_activity_type_id_idx    INDEX     b   CREATE INDEX activities_activity_type_id_idx ON public.activities USING btree (activity_type_id);
 3   DROP INDEX public.activities_activity_type_id_idx;
       public            jes.87kXz4RuQk    false    223            x           1259    147732    activities_call_id_idx    INDEX     P   CREATE INDEX activities_call_id_idx ON public.activities USING btree (call_id);
 *   DROP INDEX public.activities_call_id_idx;
       public            jes.87kXz4RuQk    false    223            y           1259    147730 .   activities_candidate_id_project_id_user_id_idx    INDEX     �   CREATE INDEX activities_candidate_id_project_id_user_id_idx ON public.activities USING btree (candidate_id, project_id, user_id);
 B   DROP INDEX public.activities_candidate_id_project_id_user_id_idx;
       public            jes.87kXz4RuQk    false    223    223    223            z           1259    147735 5   activities_candidate_id_stage_id_activity_type_id_idx    INDEX     �   CREATE INDEX activities_candidate_id_stage_id_activity_type_id_idx ON public.activities USING btree (candidate_id, stage_id, activity_type_id);
 I   DROP INDEX public.activities_candidate_id_stage_id_activity_type_id_idx;
       public            jes.87kXz4RuQk    false    223    223    223            {           1259    147724    activities_comment_id_idx    INDEX     V   CREATE INDEX activities_comment_id_idx ON public.activities USING btree (comment_id);
 -   DROP INDEX public.activities_comment_id_idx;
       public            jes.87kXz4RuQk    false    223            |           1259    147733     activities_dropout_reason_id_idx    INDEX     d   CREATE INDEX activities_dropout_reason_id_idx ON public.activities USING btree (dropout_reason_id);
 4   DROP INDEX public.activities_dropout_reason_id_idx;
       public            jes.87kXz4RuQk    false    223            }           1259    147721    activities_form_id_idx    INDEX     P   CREATE INDEX activities_form_id_idx ON public.activities USING btree (form_id);
 *   DROP INDEX public.activities_form_id_idx;
       public            jes.87kXz4RuQk    false    223            ~           1259    147731    activities_integration_id_idx    INDEX     ^   CREATE INDEX activities_integration_id_idx ON public.activities USING btree (integration_id);
 1   DROP INDEX public.activities_integration_id_idx;
       public            jes.87kXz4RuQk    false    223                       1259    147723    activities_message_id_idx    INDEX     V   CREATE INDEX activities_message_id_idx ON public.activities USING btree (message_id);
 -   DROP INDEX public.activities_message_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147722    activities_project_id_idx    INDEX     V   CREATE INDEX activities_project_id_idx ON public.activities USING btree (project_id);
 -   DROP INDEX public.activities_project_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147729    activities_scorecard_id_idx    INDEX     Z   CREATE INDEX activities_scorecard_id_idx ON public.activities USING btree (scorecard_id);
 /   DROP INDEX public.activities_scorecard_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147728    activities_sms_id_idx    INDEX     N   CREATE INDEX activities_sms_id_idx ON public.activities USING btree (sms_id);
 )   DROP INDEX public.activities_sms_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147727    activities_stage_id_idx    INDEX     R   CREATE INDEX activities_stage_id_idx ON public.activities USING btree (stage_id);
 +   DROP INDEX public.activities_stage_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147726    activities_submission_id_idx    INDEX     \   CREATE INDEX activities_submission_id_idx ON public.activities USING btree (submission_id);
 0   DROP INDEX public.activities_submission_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147720    activities_task_id_idx    INDEX     P   CREATE INDEX activities_task_id_idx ON public.activities USING btree (task_id);
 *   DROP INDEX public.activities_task_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147734    activities_team_id_idx    INDEX     P   CREATE INDEX activities_team_id_idx ON public.activities USING btree (team_id);
 *   DROP INDEX public.activities_team_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147725    activities_user_id_idx    INDEX     P   CREATE INDEX activities_user_id_idx ON public.activities USING btree (user_id);
 *   DROP INDEX public.activities_user_id_idx;
       public            jes.87kXz4RuQk    false    223            �           1259    147748 !   activity_receipts_activity_id_idx    INDEX     f   CREATE INDEX activity_receipts_activity_id_idx ON public.activity_receipts USING btree (activity_id);
 5   DROP INDEX public.activity_receipts_activity_id_idx;
       public            jes.87kXz4RuQk    false    224            �           1259    147747    activity_receipts_user_id_idx    INDEX     ^   CREATE INDEX activity_receipts_user_id_idx ON public.activity_receipts USING btree (user_id);
 1   DROP INDEX public.activity_receipts_user_id_idx;
       public            jes.87kXz4RuQk    false    224            �           1259    147739 %   activity_subscriptions_project_id_idx    INDEX     n   CREATE INDEX activity_subscriptions_project_id_idx ON public.activity_subscriptions USING btree (project_id);
 9   DROP INDEX public.activity_subscriptions_project_id_idx;
       public            jes.87kXz4RuQk    false    233            �           1259    147740 "   activity_subscriptions_user_id_idx    INDEX     h   CREATE INDEX activity_subscriptions_user_id_idx ON public.activity_subscriptions USING btree (user_id);
 6   DROP INDEX public.activity_subscriptions_user_id_idx;
       public            jes.87kXz4RuQk    false    233            �           1259    147692 &   audits_auditable_type_auditable_id_idx    INDEX     q   CREATE INDEX audits_auditable_type_auditable_id_idx ON public.audits USING btree (auditable_type, auditable_id);
 :   DROP INDEX public.audits_auditable_type_auditable_id_idx;
       public            jes.87kXz4RuQk    false    225    225            �           1259    147693    audits_user_id_user_type_idx    INDEX     ]   CREATE INDEX audits_user_id_user_type_idx ON public.audits USING btree (user_id, user_type);
 0   DROP INDEX public.audits_user_id_user_type_idx;
       public            jes.87kXz4RuQk    false    225    225            �           1259    147715    auth_tokens_user_id_idx    INDEX     R   CREATE INDEX auth_tokens_user_id_idx ON public.auth_tokens USING btree (user_id);
 +   DROP INDEX public.auth_tokens_user_id_idx;
       public            jes.87kXz4RuQk    false    228            �           1259    147513 
   cache_key_idx    INDEX     E   CREATE UNIQUE INDEX cache_key_idx ON public.cache USING btree (key);
 !   DROP INDEX public.cache_key_idx;
       public            jes.87kXz4RuQk    false    231            �           1259    147470    calls_candidate_id_idx    INDEX     P   CREATE INDEX calls_candidate_id_idx ON public.calls USING btree (candidate_id);
 *   DROP INDEX public.calls_candidate_id_idx;
       public            jes.87kXz4RuQk    false    242            �           1259    147471    calls_project_id_idx    INDEX     L   CREATE INDEX calls_project_id_idx ON public.calls USING btree (project_id);
 (   DROP INDEX public.calls_project_id_idx;
       public            jes.87kXz4RuQk    false    242            �           1259    147472    calls_user_id_idx    INDEX     F   CREATE INDEX calls_user_id_idx ON public.calls USING btree (user_id);
 %   DROP INDEX public.calls_user_id_idx;
       public            jes.87kXz4RuQk    false    242            �           1259    147711 (   candidate_cv_ai_summary_candidate_id_idx    INDEX     t   CREATE INDEX candidate_cv_ai_summary_candidate_id_idx ON public.candidate_cv_ai_summary USING btree (candidate_id);
 <   DROP INDEX public.candidate_cv_ai_summary_candidate_id_idx;
       public            jes.87kXz4RuQk    false    249            �           1259    147710 !   candidate_cv_ai_summary_cv_id_idx    INDEX     f   CREATE INDEX candidate_cv_ai_summary_cv_id_idx ON public.candidate_cv_ai_summary USING btree (cv_id);
 5   DROP INDEX public.candidate_cv_ai_summary_cv_id_idx;
       public            jes.87kXz4RuQk    false    249            �           1259    147663    candidate_filters_user_id_idx    INDEX     ^   CREATE INDEX candidate_filters_user_id_idx ON public.candidate_filters USING btree (user_id);
 1   DROP INDEX public.candidate_filters_user_id_idx;
       public            jes.87kXz4RuQk    false    232            �           1259    147542 &   candidate_integration_candidate_id_idx    INDEX     p   CREATE INDEX candidate_integration_candidate_id_idx ON public.candidate_integration USING btree (candidate_id);
 :   DROP INDEX public.candidate_integration_candidate_id_idx;
       public            jes.87kXz4RuQk    false    244            �           1259    147541 (   candidate_integration_integration_id_idx    INDEX     t   CREATE INDEX candidate_integration_integration_id_idx ON public.candidate_integration USING btree (integration_id);
 <   DROP INDEX public.candidate_integration_integration_id_idx;
       public            jes.87kXz4RuQk    false    244            u           1259    147750 '   candidate_presentation_candidate_id_idx    INDEX     r   CREATE INDEX candidate_presentation_candidate_id_idx ON public.candidate_presentation USING btree (candidate_id);
 ;   DROP INDEX public.candidate_presentation_candidate_id_idx;
       public            jes.87kXz4RuQk    false    222            v           1259    147749 *   candidate_presentation_presentation_id_idx    INDEX     x   CREATE INDEX candidate_presentation_presentation_id_idx ON public.candidate_presentation USING btree (presentation_id);
 >   DROP INDEX public.candidate_presentation_presentation_id_idx;
       public            jes.87kXz4RuQk    false    222            �           1259    147367    candidate_sms_candidate_id_idx    INDEX     `   CREATE INDEX candidate_sms_candidate_id_idx ON public.candidate_sms USING btree (candidate_id);
 2   DROP INDEX public.candidate_sms_candidate_id_idx;
       public            jes.87kXz4RuQk    false    236            �           1259    147368    candidate_sms_sms_id_idx    INDEX     T   CREATE INDEX candidate_sms_sms_id_idx ON public.candidate_sms USING btree (sms_id);
 ,   DROP INDEX public.candidate_sms_sms_id_idx;
       public            jes.87kXz4RuQk    false    236            �           1259    147652 )   candidate_stage_candidate_id_stage_id_idx    INDEX     ~   CREATE UNIQUE INDEX candidate_stage_candidate_id_stage_id_idx ON public.candidate_stage USING btree (candidate_id, stage_id);
 =   DROP INDEX public.candidate_stage_candidate_id_stage_id_idx;
       public            jes.87kXz4RuQk    false    248    248            �           1259    147653 %   candidate_stage_dropout_reason_id_idx    INDEX     n   CREATE INDEX candidate_stage_dropout_reason_id_idx ON public.candidate_stage USING btree (dropout_reason_id);
 9   DROP INDEX public.candidate_stage_dropout_reason_id_idx;
       public            jes.87kXz4RuQk    false    248            �           1259    147654    candidate_stage_stage_id_idx    INDEX     \   CREATE INDEX candidate_stage_stage_id_idx ON public.candidate_stage USING btree (stage_id);
 0   DROP INDEX public.candidate_stage_stage_id_idx;
       public            jes.87kXz4RuQk    false    248            �           1259    147403 $   candidate_summaries_candidate_id_idx    INDEX     l   CREATE INDEX candidate_summaries_candidate_id_idx ON public.candidate_summaries USING btree (candidate_id);
 8   DROP INDEX public.candidate_summaries_candidate_id_idx;
       public            jes.87kXz4RuQk    false    237            �           1259    147402 "   candidate_summaries_project_id_idx    INDEX     h   CREATE INDEX candidate_summaries_project_id_idx ON public.candidate_summaries USING btree (project_id);
 6   DROP INDEX public.candidate_summaries_project_id_idx;
       public            jes.87kXz4RuQk    false    237            �           1259    147671    candidate_tag_candidate_id_idx    INDEX     `   CREATE INDEX candidate_tag_candidate_id_idx ON public.candidate_tag USING btree (candidate_id);
 2   DROP INDEX public.candidate_tag_candidate_id_idx;
       public            jes.87kXz4RuQk    false    240            �           1259    147670    candidate_tag_tag_id_idx    INDEX     T   CREATE INDEX candidate_tag_tag_id_idx ON public.candidate_tag USING btree (tag_id);
 ,   DROP INDEX public.candidate_tag_tag_id_idx;
       public            jes.87kXz4RuQk    false    240            \           1259    147776    candidates_deleted_at_idx    INDEX     V   CREATE INDEX candidates_deleted_at_idx ON public.candidates USING btree (deleted_at);
 -   DROP INDEX public.candidates_deleted_at_idx;
       public            jes.87kXz4RuQk    false    215            ]           1259    147777    candidates_entered_by_id_idx    INDEX     \   CREATE INDEX candidates_entered_by_id_idx ON public.candidates USING btree (entered_by_id);
 0   DROP INDEX public.candidates_entered_by_id_idx;
       public            jes.87kXz4RuQk    false    215            `           1259    147775    candidates_team_id_idx    INDEX     P   CREATE INDEX candidates_team_id_idx ON public.candidates USING btree (team_id);
 *   DROP INDEX public.candidates_team_id_idx;
       public            jes.87kXz4RuQk    false    215            a           1259    147783    comments_candidate_id_idx    INDEX     V   CREATE INDEX comments_candidate_id_idx ON public.comments USING btree (candidate_id);
 -   DROP INDEX public.comments_candidate_id_idx;
       public            jes.87kXz4RuQk    false    216            d           1259    147785    comments_project_id_idx    INDEX     R   CREATE INDEX comments_project_id_idx ON public.comments USING btree (project_id);
 +   DROP INDEX public.comments_project_id_idx;
       public            jes.87kXz4RuQk    false    216            e           1259    147784    comments_team_id_idx    INDEX     L   CREATE INDEX comments_team_id_idx ON public.comments USING btree (team_id);
 (   DROP INDEX public.comments_team_id_idx;
       public            jes.87kXz4RuQk    false    216            f           1259    147786    comments_user_id_idx    INDEX     L   CREATE INDEX comments_user_id_idx ON public.comments USING btree (user_id);
 (   DROP INDEX public.comments_user_id_idx;
       public            jes.87kXz4RuQk    false    216            g           1259    147787 &   comments_video_interview_invite_id_idx    INDEX     p   CREATE INDEX comments_video_interview_invite_id_idx ON public.comments USING btree (video_interview_invite_id);
 :   DROP INDEX public.comments_video_interview_invite_id_idx;
       public            jes.87kXz4RuQk    false    216            �           1259    147705 !   consent_renewals_candidate_id_idx    INDEX     f   CREATE INDEX consent_renewals_candidate_id_idx ON public.consent_renewals USING btree (candidate_id);
 5   DROP INDEX public.consent_renewals_candidate_id_idx;
       public            jes.87kXz4RuQk    false    235            �           1259    147704    consent_renewals_message_id_idx    INDEX     b   CREATE INDEX consent_renewals_message_id_idx ON public.consent_renewals USING btree (message_id);
 3   DROP INDEX public.consent_renewals_message_id_idx;
       public            jes.87kXz4RuQk    false    235            �           1259    147699    consents_candidate_id_idx    INDEX     V   CREATE INDEX consents_candidate_id_idx ON public.consents USING btree (candidate_id);
 -   DROP INDEX public.consents_candidate_id_idx;
       public            jes.87kXz4RuQk    false    250            �           1259    147698    consents_project_id_idx    INDEX     R   CREATE INDEX consents_project_id_idx ON public.consents USING btree (project_id);
 +   DROP INDEX public.consents_project_id_idx;
       public            jes.87kXz4RuQk    false    250            �           1259    147700    consents_user_id_idx    INDEX     L   CREATE INDEX consents_user_id_idx ON public.consents USING btree (user_id);
 (   DROP INDEX public.consents_user_id_idx;
       public            jes.87kXz4RuQk    false    250            �           1259    147582 $   crm_contacts_crm_organization_id_idx    INDEX     l   CREATE INDEX crm_contacts_crm_organization_id_idx ON public.crm_contacts USING btree (crm_organization_id);
 8   DROP INDEX public.crm_contacts_crm_organization_id_idx;
       public            jes.87kXz4RuQk    false    251            h           1259    147768 #   crm_offices_crm_organization_id_idx    INDEX     j   CREATE INDEX crm_offices_crm_organization_id_idx ON public.crm_offices USING btree (crm_organization_id);
 7   DROP INDEX public.crm_offices_crm_organization_id_idx;
       public            jes.87kXz4RuQk    false    217            k           1259    147764 '   crm_organizations_client_manager_id_idx    INDEX     r   CREATE INDEX crm_organizations_client_manager_id_idx ON public.crm_organizations USING btree (client_manager_id);
 ;   DROP INDEX public.crm_organizations_client_manager_id_idx;
       public            jes.87kXz4RuQk    false    218            r           1259    147754    educations_candidate_id_idx    INDEX     Z   CREATE INDEX educations_candidate_id_idx ON public.educations USING btree (candidate_id);
 /   DROP INDEX public.educations_candidate_id_idx;
       public            jes.87kXz4RuQk    false    221            �           1259    147669    employments_candidate_id_idx    INDEX     \   CREATE INDEX employments_candidate_id_idx ON public.employments USING btree (candidate_id);
 0   DROP INDEX public.employments_candidate_id_idx;
       public            jes.87kXz4RuQk    false    226            �           1259    147742    event_set_user_event_set_id_idx    INDEX     b   CREATE INDEX event_set_user_event_set_id_idx ON public.event_set_user USING btree (event_set_id);
 3   DROP INDEX public.event_set_user_event_set_id_idx;
       public            jes.87kXz4RuQk    false    230            �           1259    147741    event_set_user_user_id_idx    INDEX     X   CREATE INDEX event_set_user_user_id_idx ON public.event_set_user USING btree (user_id);
 .   DROP INDEX public.event_set_user_user_id_idx;
       public            jes.87kXz4RuQk    false    230            �           1259    147597    event_sets_author_id_idx    INDEX     T   CREATE INDEX event_sets_author_id_idx ON public.event_sets USING btree (author_id);
 ,   DROP INDEX public.event_sets_author_id_idx;
       public            jes.87kXz4RuQk    false    234            �           1259    147596    event_sets_project_id_idx    INDEX     V   CREATE INDEX event_sets_project_id_idx ON public.event_sets USING btree (project_id);
 -   DROP INDEX public.event_sets_project_id_idx;
       public            jes.87kXz4RuQk    false    234            �           1259    147477    events_message_id_idx    INDEX     N   CREATE INDEX events_message_id_idx ON public.events USING btree (message_id);
 )   DROP INDEX public.events_message_id_idx;
       public            jes.87kXz4RuQk    false    241            �           1259    147521 #   files_fileable_id_fileable_type_idx    INDEX     k   CREATE INDEX files_fileable_id_fileable_type_idx ON public.files USING btree (fileable_id, fileable_type);
 7   DROP INDEX public.files_fileable_id_fileable_type_idx;
       public            jes.87kXz4RuQk    false    245    245            �           1259    147520    files_project_id_idx    INDEX     L   CREATE INDEX files_project_id_idx ON public.files USING btree (project_id);
 (   DROP INDEX public.files_project_id_idx;
       public            jes.87kXz4RuQk    false    245            ,           1259    147457    forms_stage_id_idx    INDEX     H   CREATE INDEX forms_stage_id_idx ON public.forms USING btree (stage_id);
 &   DROP INDEX public.forms_stage_id_idx;
       public            jes.87kXz4RuQk    false    275            -           1259    147456    forms_team_id_idx    INDEX     F   CREATE INDEX forms_team_id_idx ON public.forms USING btree (team_id);
 %   DROP INDEX public.forms_team_id_idx;
       public            jes.87kXz4RuQk    false    275            �           1259    147664 &   integration_message_integration_id_idx    INDEX     p   CREATE INDEX integration_message_integration_id_idx ON public.integration_message USING btree (integration_id);
 :   DROP INDEX public.integration_message_integration_id_idx;
       public            jes.87kXz4RuQk    false    247            �           1259    147665 "   integration_message_message_id_idx    INDEX     h   CREATE INDEX integration_message_message_id_idx ON public.integration_message USING btree (message_id);
 6   DROP INDEX public.integration_message_message_id_idx;
       public            jes.87kXz4RuQk    false    247            
           1259    147353 6   integration_structured_job_ad_destination_stage_id_idx    INDEX     �   CREATE INDEX integration_structured_job_ad_destination_stage_id_idx ON public.integration_structured_job_ad USING btree (destination_stage_id);
 J   DROP INDEX public.integration_structured_job_ad_destination_stage_id_idx;
       public            jes.87kXz4RuQk    false    267                       1259    147355 0   integration_structured_job_ad_integration_id_idx    INDEX     �   CREATE INDEX integration_structured_job_ad_integration_id_idx ON public.integration_structured_job_ad USING btree (integration_id);
 D   DROP INDEX public.integration_structured_job_ad_integration_id_idx;
       public            jes.87kXz4RuQk    false    267                       1259    147354 6   integration_structured_job_ad_structured_job_ad_id_idx    INDEX     �   CREATE INDEX integration_structured_job_ad_structured_job_ad_id_idx ON public.integration_structured_job_ad USING btree (structured_job_ad_id);
 J   DROP INDEX public.integration_structured_job_ad_structured_job_ad_id_idx;
       public            jes.87kXz4RuQk    false    267                       1259    147534    integrations_team_id_idx    INDEX     T   CREATE INDEX integrations_team_id_idx ON public.integrations USING btree (team_id);
 ,   DROP INDEX public.integrations_team_id_idx;
       public            jes.87kXz4RuQk    false    271                       1259    147535    integrations_user_id_idx    INDEX     T   CREATE INDEX integrations_user_id_idx ON public.integrations USING btree (user_id);
 ,   DROP INDEX public.integrations_user_id_idx;
       public            jes.87kXz4RuQk    false    271            �           1259    147684    invites_candidate_id_idx    INDEX     T   CREATE INDEX invites_candidate_id_idx ON public.invites USING btree (candidate_id);
 ,   DROP INDEX public.invites_candidate_id_idx;
       public            jes.87kXz4RuQk    false    252            �           1259    147683    invites_event_set_id_idx    INDEX     T   CREATE INDEX invites_event_set_id_idx ON public.invites USING btree (event_set_id);
 ,   DROP INDEX public.invites_event_set_id_idx;
       public            jes.87kXz4RuQk    false    252            �           1259    147682 !   invites_selected_time_slot_id_idx    INDEX     f   CREATE INDEX invites_selected_time_slot_id_idx ON public.invites USING btree (selected_time_slot_id);
 5   DROP INDEX public.invites_selected_time_slot_id_idx;
       public            jes.87kXz4RuQk    false    252            �           1259    147501 0   job_ad_feed_structured_job_ad_job_ad_feed_id_idx    INDEX     �   CREATE INDEX job_ad_feed_structured_job_ad_job_ad_feed_id_idx ON public.job_ad_feed_structured_job_ad USING btree (job_ad_feed_id);
 D   DROP INDEX public.job_ad_feed_structured_job_ad_job_ad_feed_id_idx;
       public            jes.87kXz4RuQk    false    253            �           1259    147500 6   job_ad_feed_structured_job_ad_structured_job_ad_id_idx    INDEX     �   CREATE INDEX job_ad_feed_structured_job_ad_structured_job_ad_id_idx ON public.job_ad_feed_structured_job_ad USING btree (structured_job_ad_id);
 J   DROP INDEX public.job_ad_feed_structured_job_ad_structured_job_ad_id_idx;
       public            jes.87kXz4RuQk    false    253            �           1259    147451    job_ad_feeds_integration_id_idx    INDEX     b   CREATE INDEX job_ad_feeds_integration_id_idx ON public.job_ad_feeds USING btree (integration_id);
 3   DROP INDEX public.job_ad_feeds_integration_id_idx;
       public            jes.87kXz4RuQk    false    254            �           1259    147450    job_ad_feeds_slug_idx    INDEX     U   CREATE UNIQUE INDEX job_ad_feeds_slug_idx ON public.job_ad_feeds USING btree (slug);
 )   DROP INDEX public.job_ad_feeds_slug_idx;
       public            jes.87kXz4RuQk    false    254            �           1259    147578     landing_dei_reports_block_id_idx    INDEX     d   CREATE INDEX landing_dei_reports_block_id_idx ON public.landing_dei_reports USING btree (block_id);
 4   DROP INDEX public.landing_dei_reports_block_id_idx;
       public            jes.87kXz4RuQk    false    255            �           1259    147575 "   landing_dei_reports_landing_id_idx    INDEX     h   CREATE INDEX landing_dei_reports_landing_id_idx ON public.landing_dei_reports USING btree (landing_id);
 6   DROP INDEX public.landing_dei_reports_landing_id_idx;
       public            jes.87kXz4RuQk    false    255            �           1259    147404    landing_tag_landing_id_idx    INDEX     X   CREATE INDEX landing_tag_landing_id_idx ON public.landing_tag USING btree (landing_id);
 .   DROP INDEX public.landing_tag_landing_id_idx;
       public            jes.87kXz4RuQk    false    256            �           1259    147405    landing_tag_landing_tag_id_idx    INDEX     `   CREATE INDEX landing_tag_landing_tag_id_idx ON public.landing_tag USING btree (landing_tag_id);
 2   DROP INDEX public.landing_tag_landing_tag_id_idx;
       public            jes.87kXz4RuQk    false    256            �           1259    147390    landings_stage_id_idx    INDEX     N   CREATE INDEX landings_stage_id_idx ON public.landings USING btree (stage_id);
 )   DROP INDEX public.landings_stage_id_idx;
       public            jes.87kXz4RuQk    false    258            �           1259    147391    landings_team_id_idx    INDEX     L   CREATE INDEX landings_team_id_idx ON public.landings USING btree (team_id);
 (   DROP INDEX public.landings_team_id_idx;
       public            jes.87kXz4RuQk    false    258            �           1259    147392    landings_url_token_idx    INDEX     P   CREATE INDEX landings_url_token_idx ON public.landings USING btree (url_token);
 *   DROP INDEX public.landings_url_token_idx;
       public            jes.87kXz4RuQk    false    258            �           1259    147349    messageables_message_id_idx    INDEX     Z   CREATE INDEX messageables_message_id_idx ON public.messageables USING btree (message_id);
 /   DROP INDEX public.messageables_message_id_idx;
       public            jes.87kXz4RuQk    false    259            �           1259    147348    messageables_messageable_id_idx    INDEX     b   CREATE INDEX messageables_messageable_id_idx ON public.messageables USING btree (messageable_id);
 3   DROP INDEX public.messageables_messageable_id_idx;
       public            jes.87kXz4RuQk    false    259            �           1259    147347    messageables_survey_id_idx    INDEX     X   CREATE INDEX messageables_survey_id_idx ON public.messageables USING btree (survey_id);
 .   DROP INDEX public.messageables_survey_id_idx;
       public            jes.87kXz4RuQk    false    259            �           1259    147510    messages_team_id_idx    INDEX     L   CREATE INDEX messages_team_id_idx ON public.messages USING btree (team_id);
 (   DROP INDEX public.messages_team_id_idx;
       public            jes.87kXz4RuQk    false    260            �           1259    147511    messages_user_id_idx    INDEX     L   CREATE INDEX messages_user_id_idx ON public.messages USING btree (user_id);
 (   DROP INDEX public.messages_user_id_idx;
       public            jes.87kXz4RuQk    false    260                       1259    147381    nps_surveys_user_id_idx    INDEX     R   CREATE INDEX nps_surveys_user_id_idx ON public.nps_surveys USING btree (user_id);
 +   DROP INDEX public.nps_surveys_user_id_idx;
       public            jes.87kXz4RuQk    false    264            �           1259    147512    password_resets_email_idx    INDEX     V   CREATE INDEX password_resets_email_idx ON public.password_resets USING btree (email);
 -   DROP INDEX public.password_resets_email_idx;
       public            jes.87kXz4RuQk    false    262            �           1259    147569    presentations_message_id_idx    INDEX     \   CREATE INDEX presentations_message_id_idx ON public.presentations USING btree (message_id);
 0   DROP INDEX public.presentations_message_id_idx;
       public            jes.87kXz4RuQk    false    263                        1259    147568    presentations_project_id_idx    INDEX     \   CREATE INDEX presentations_project_id_idx ON public.presentations USING btree (project_id);
 0   DROP INDEX public.presentations_project_id_idx;
       public            jes.87kXz4RuQk    false    263            (           1259    147499    project_logs_project_id_idx    INDEX     Z   CREATE INDEX project_logs_project_id_idx ON public.project_logs USING btree (project_id);
 /   DROP INDEX public.project_logs_project_id_idx;
       public            jes.87kXz4RuQk    false    274            )           1259    147498    project_logs_user_id_idx    INDEX     T   CREATE INDEX project_logs_user_id_idx ON public.project_logs USING btree (user_id);
 ,   DROP INDEX public.project_logs_user_id_idx;
       public            jes.87kXz4RuQk    false    274                       1259    147444    project_user_project_id_idx    INDEX     Z   CREATE INDEX project_user_project_id_idx ON public.project_user USING btree (project_id);
 /   DROP INDEX public.project_user_project_id_idx;
       public            jes.87kXz4RuQk    false    265                       1259    147445    project_user_user_id_idx    INDEX     T   CREATE INDEX project_user_user_id_idx ON public.project_user USING btree (user_id);
 ,   DROP INDEX public.project_user_user_id_idx;
       public            jes.87kXz4RuQk    false    265            C           1259    147548    projects_crm_office_id_idx    INDEX     X   CREATE INDEX projects_crm_office_id_idx ON public.projects USING btree (crm_office_id);
 .   DROP INDEX public.projects_crm_office_id_idx;
       public            jes.87kXz4RuQk    false    283            D           1259    147547     projects_crm_organization_id_idx    INDEX     d   CREATE INDEX projects_crm_organization_id_idx ON public.projects USING btree (crm_organization_id);
 4   DROP INDEX public.projects_crm_organization_id_idx;
       public            jes.87kXz4RuQk    false    283            E           1259    147550 ?   projects_id_project_manage8b377-6fbd-4fd8-8588-60b4264311ec_idx    INDEX     �   CREATE INDEX "projects_id_project_manage8b377-6fbd-4fd8-8588-60b4264311ec_idx" ON public.projects USING btree (id, project_manager_id, accessible_only_members, deleted_at);
 U   DROP INDEX public."projects_id_project_manage8b377-6fbd-4fd8-8588-60b4264311ec_idx";
       public            jes.87kXz4RuQk    false    283    283    283    283            H           1259    147549    projects_project_manager_id_idx    INDEX     b   CREATE INDEX projects_project_manager_id_idx ON public.projects USING btree (project_manager_id);
 3   DROP INDEX public.projects_project_manager_id_idx;
       public            jes.87kXz4RuQk    false    283            I           1259    147553    projects_scorecard_id_idx    INDEX     V   CREATE INDEX projects_scorecard_id_idx ON public.projects USING btree (scorecard_id);
 -   DROP INDEX public.projects_scorecard_id_idx;
       public            jes.87kXz4RuQk    false    283            J           1259    147554    projects_team_id_idx    INDEX     L   CREATE INDEX projects_team_id_idx ON public.projects USING btree (team_id);
 (   DROP INDEX public.projects_team_id_idx;
       public            jes.87kXz4RuQk    false    283            ?           1259    147637    reacts_comment_id_idx    INDEX     N   CREATE INDEX reacts_comment_id_idx ON public.reacts USING btree (comment_id);
 )   DROP INDEX public.reacts_comment_id_idx;
       public            jes.87kXz4RuQk    false    282            B           1259    147638    reacts_user_id_idx    INDEX     H   CREATE INDEX reacts_user_id_idx ON public.reacts USING btree (user_id);
 &   DROP INDEX public.reacts_user_id_idx;
       public            jes.87kXz4RuQk    false    282                       1259    147540 (   requisition_approvals_requisition_id_idx    INDEX     t   CREATE INDEX requisition_approvals_requisition_id_idx ON public.requisition_approvals USING btree (requisition_id);
 <   DROP INDEX public.requisition_approvals_requisition_id_idx;
       public            jes.87kXz4RuQk    false    266            	           1259    147537 !   requisition_approvals_user_id_idx    INDEX     f   CREATE INDEX requisition_approvals_user_id_idx ON public.requisition_approvals USING btree (user_id);
 5   DROP INDEX public.requisition_approvals_user_id_idx;
       public            jes.87kXz4RuQk    false    266                       1259    147462    requisitions_project_id_idx    INDEX     Z   CREATE INDEX requisitions_project_id_idx ON public.requisitions USING btree (project_id);
 /   DROP INDEX public.requisitions_project_id_idx;
       public            jes.87kXz4RuQk    false    269                       1259    147461    requisitions_recruiter_id_idx    INDEX     ^   CREATE INDEX requisitions_recruiter_id_idx ON public.requisitions USING btree (recruiter_id);
 1   DROP INDEX public.requisitions_recruiter_id_idx;
       public            jes.87kXz4RuQk    false    269                       1259    147463    requisitions_user_id_idx    INDEX     T   CREATE INDEX requisitions_user_id_idx ON public.requisitions USING btree (user_id);
 ,   DROP INDEX public.requisitions_user_id_idx;
       public            jes.87kXz4RuQk    false    269            
           1259    147442 #   scorecard_comments_candidate_id_idx    INDEX     j   CREATE INDEX scorecard_comments_candidate_id_idx ON public.scorecard_comments USING btree (candidate_id);
 7   DROP INDEX public.scorecard_comments_candidate_id_idx;
       public            jes.87kXz4RuQk    false    268                       1259    147441 !   scorecard_comments_project_id_idx    INDEX     f   CREATE INDEX scorecard_comments_project_id_idx ON public.scorecard_comments USING btree (project_id);
 5   DROP INDEX public.scorecard_comments_project_id_idx;
       public            jes.87kXz4RuQk    false    268                       1259    147443 6   scorecard_comments_user_id_project_id_candidate_id_idx    INDEX     �   CREATE UNIQUE INDEX scorecard_comments_user_id_project_id_candidate_id_idx ON public.scorecard_comments USING btree (user_id, project_id, candidate_id);
 J   DROP INDEX public.scorecard_comments_user_id_project_id_candidate_id_idx;
       public            jes.87kXz4RuQk    false    268    268    268                       1259    147414 $   scorecard_questions_scorecard_id_idx    INDEX     l   CREATE INDEX scorecard_questions_scorecard_id_idx ON public.scorecard_questions USING btree (scorecard_id);
 8   DROP INDEX public.scorecard_questions_scorecard_id_idx;
       public            jes.87kXz4RuQk    false    270                       1259    147676 $   scorecard_responses_candidate_id_idx    INDEX     l   CREATE INDEX scorecard_responses_candidate_id_idx ON public.scorecard_responses USING btree (candidate_id);
 8   DROP INDEX public.scorecard_responses_candidate_id_idx;
       public            jes.87kXz4RuQk    false    272            !           1259    147678 "   scorecard_responses_project_id_idx    INDEX     h   CREATE INDEX scorecard_responses_project_id_idx ON public.scorecard_responses USING btree (project_id);
 6   DROP INDEX public.scorecard_responses_project_id_idx;
       public            jes.87kXz4RuQk    false    272            "           1259    147675 ?   scorecard_responses_scorec0dc13-c25e-4924-bf16-99fc0ecb86b4_idx    INDEX     �   CREATE UNIQUE INDEX "scorecard_responses_scorec0dc13-c25e-4924-bf16-99fc0ecb86b4_idx" ON public.scorecard_responses USING btree (scorecard_question_id, user_id, project_id, candidate_id);
 U   DROP INDEX public."scorecard_responses_scorec0dc13-c25e-4924-bf16-99fc0ecb86b4_idx";
       public            jes.87kXz4RuQk    false    272    272    272    272            #           1259    147677    scorecard_responses_user_id_idx    INDEX     b   CREATE INDEX scorecard_responses_user_id_idx ON public.scorecard_responses USING btree (user_id);
 3   DROP INDEX public.scorecard_responses_user_id_idx;
       public            jes.87kXz4RuQk    false    272            .           1259    147430    sessions_id_idx    INDEX     I   CREATE UNIQUE INDEX sessions_id_idx ON public.sessions USING btree (id);
 #   DROP INDEX public.sessions_id_idx;
       public            jes.87kXz4RuQk    false    276            /           1259    147566    settings_key_idx    INDEX     K   CREATE UNIQUE INDEX settings_key_idx ON public.settings USING btree (key);
 $   DROP INDEX public.settings_key_idx;
       public            jes.87kXz4RuQk    false    277            4           1259    147359    sms_project_id_idx    INDEX     H   CREATE INDEX sms_project_id_idx ON public.sms USING btree (project_id);
 &   DROP INDEX public.sms_project_id_idx;
       public            jes.87kXz4RuQk    false    278            5           1259    147360    sms_user_id_idx    INDEX     B   CREATE INDEX sms_user_id_idx ON public.sms USING btree (user_id);
 #   DROP INDEX public.sms_user_id_idx;
       public            jes.87kXz4RuQk    false    278            8           1259    147407    source_budgets_project_id_idx    INDEX     ^   CREATE INDEX source_budgets_project_id_idx ON public.source_budgets USING btree (project_id);
 1   DROP INDEX public.source_budgets_project_id_idx;
       public            jes.87kXz4RuQk    false    279            9           1259    147376 #   stages_custom_stage_category_id_idx    INDEX     j   CREATE INDEX stages_custom_stage_category_id_idx ON public.stages USING btree (custom_stage_category_id);
 7   DROP INDEX public.stages_custom_stage_category_id_idx;
       public            jes.87kXz4RuQk    false    280            <           1259    147377    stages_project_id_idx    INDEX     N   CREATE INDEX stages_project_id_idx ON public.stages USING btree (project_id);
 )   DROP INDEX public.stages_project_id_idx;
       public            jes.87kXz4RuQk    false    280                       1259    147626 *   structured_job_ads_cvb_target_stage_id_idx    INDEX     x   CREATE INDEX structured_job_ads_cvb_target_stage_id_idx ON public.structured_job_ads USING btree (cvb_target_stage_id);
 >   DROP INDEX public.structured_job_ads_cvb_target_stage_id_idx;
       public            jes.87kXz4RuQk    false    299            �           1259    147625 *   structured_job_ads_cvk_target_stage_id_idx    INDEX     x   CREATE INDEX structured_job_ads_cvk_target_stage_id_idx ON public.structured_job_ads USING btree (cvk_target_stage_id);
 >   DROP INDEX public.structured_job_ads_cvk_target_stage_id_idx;
       public            jes.87kXz4RuQk    false    299            �           1259    147623 +   structured_job_ads_cvo_remote_source_id_idx    INDEX     z   CREATE INDEX structured_job_ads_cvo_remote_source_id_idx ON public.structured_job_ads USING btree (cvo_remote_source_id);
 ?   DROP INDEX public.structured_job_ads_cvo_remote_source_id_idx;
       public            jes.87kXz4RuQk    false    299            �           1259    147624 *   structured_job_ads_cvo_target_stage_id_idx    INDEX     x   CREATE INDEX structured_job_ads_cvo_target_stage_id_idx ON public.structured_job_ads USING btree (cvo_target_stage_id);
 >   DROP INDEX public.structured_job_ads_cvo_target_stage_id_idx;
       public            jes.87kXz4RuQk    false    299            �           1259    147622 !   structured_job_ads_landing_id_idx    INDEX     f   CREATE INDEX structured_job_ads_landing_id_idx ON public.structured_job_ads USING btree (landing_id);
 5   DROP INDEX public.structured_job_ads_landing_id_idx;
       public            jes.87kXz4RuQk    false    299            �           1259    147621 !   structured_job_ads_project_id_idx    INDEX     f   CREATE INDEX structured_job_ads_project_id_idx ON public.structured_job_ads USING btree (project_id);
 5   DROP INDEX public.structured_job_ads_project_id_idx;
       public            jes.87kXz4RuQk    false    299            �           1259    147620 &   structured_job_ads_target_stage_id_idx    INDEX     p   CREATE INDEX structured_job_ads_target_stage_id_idx ON public.structured_job_ads USING btree (target_stage_id);
 :   DROP INDEX public.structured_job_ads_target_stage_id_idx;
       public            jes.87kXz4RuQk    false    299            m           1259    147436    submissions_candidate_id_idx    INDEX     \   CREATE INDEX submissions_candidate_id_idx ON public.submissions USING btree (candidate_id);
 0   DROP INDEX public.submissions_candidate_id_idx;
       public            jes.87kXz4RuQk    false    294            n           1259    147437    submissions_form_id_idx    INDEX     R   CREATE INDEX submissions_form_id_idx ON public.submissions USING btree (form_id);
 +   DROP INDEX public.submissions_form_id_idx;
       public            jes.87kXz4RuQk    false    294            K           1259    147642    surveys_candidate_id_idx    INDEX     T   CREATE INDEX surveys_candidate_id_idx ON public.surveys USING btree (candidate_id);
 ,   DROP INDEX public.surveys_candidate_id_idx;
       public            jes.87kXz4RuQk    false    284            N           1259    147643    surveys_project_id_idx    INDEX     P   CREATE INDEX surveys_project_id_idx ON public.surveys USING btree (project_id);
 *   DROP INDEX public.surveys_project_id_idx;
       public            jes.87kXz4RuQk    false    284            y           1259    147560    tasks_assignee_id_idx    INDEX     N   CREATE INDEX tasks_assignee_id_idx ON public.tasks USING btree (assignee_id);
 )   DROP INDEX public.tasks_assignee_id_idx;
       public            jes.87kXz4RuQk    false    298            z           1259    147562    tasks_author_id_idx    INDEX     J   CREATE INDEX tasks_author_id_idx ON public.tasks USING btree (author_id);
 '   DROP INDEX public.tasks_author_id_idx;
       public            jes.87kXz4RuQk    false    298            {           1259    147559    tasks_candidate_id_idx    INDEX     P   CREATE INDEX tasks_candidate_id_idx ON public.tasks USING btree (candidate_id);
 *   DROP INDEX public.tasks_candidate_id_idx;
       public            jes.87kXz4RuQk    false    298            ~           1259    147561    tasks_project_id_idx    INDEX     L   CREATE INDEX tasks_project_id_idx ON public.tasks USING btree (project_id);
 (   DROP INDEX public.tasks_project_id_idx;
       public            jes.87kXz4RuQk    false    298            s           1259    147432    team_edges_source_team_id_idx    INDEX     ^   CREATE INDEX team_edges_source_team_id_idx ON public.team_edges USING btree (source_team_id);
 1   DROP INDEX public.team_edges_source_team_id_idx;
       public            jes.87kXz4RuQk    false    296            t           1259    147431    team_edges_target_team_id_idx    INDEX     ^   CREATE INDEX team_edges_target_team_id_idx ON public.team_edges USING btree (target_team_id);
 1   DROP INDEX public.team_edges_target_team_id_idx;
       public            jes.87kXz4RuQk    false    296            Q           1259    147493    templates_team_id_idx    INDEX     N   CREATE INDEX templates_team_id_idx ON public.templates USING btree (team_id);
 )   DROP INDEX public.templates_team_id_idx;
       public            jes.87kXz4RuQk    false    285            R           1259    147492    templates_user_id_idx    INDEX     N   CREATE INDEX templates_user_id_idx ON public.templates USING btree (user_id);
 )   DROP INDEX public.templates_user_id_idx;
       public            jes.87kXz4RuQk    false    285            S           1259    147632    time_slots_event_set_id_idx    INDEX     Z   CREATE INDEX time_slots_event_set_id_idx ON public.time_slots USING btree (event_set_id);
 /   DROP INDEX public.time_slots_event_set_id_idx;
       public            jes.87kXz4RuQk    false    286            X           1259    147608    users_api_token_idx    INDEX     Q   CREATE UNIQUE INDEX users_api_token_idx ON public.users USING btree (api_token);
 '   DROP INDEX public.users_api_token_idx;
       public            jes.87kXz4RuQk    false    289            Y           1259    147611    users_email_idx    INDEX     I   CREATE UNIQUE INDEX users_email_idx ON public.users USING btree (email);
 #   DROP INDEX public.users_email_idx;
       public            jes.87kXz4RuQk    false    289            \           1259    147610    users_sso_uid_idx    INDEX     M   CREATE UNIQUE INDEX users_sso_uid_idx ON public.users USING btree (sso_uid);
 %   DROP INDEX public.users_sso_uid_idx;
       public            jes.87kXz4RuQk    false    289            ]           1259    147609    users_team_id_idx    INDEX     F   CREATE INDEX users_team_id_idx ON public.users USING btree (team_id);
 %   DROP INDEX public.users_team_id_idx;
       public            jes.87kXz4RuQk    false    289            ^           1259    147420 (   video_interview_invites_candidate_id_idx    INDEX     t   CREATE INDEX video_interview_invites_candidate_id_idx ON public.video_interview_invites USING btree (candidate_id);
 <   DROP INDEX public.video_interview_invites_candidate_id_idx;
       public            jes.87kXz4RuQk    false    290            _           1259    147424 &   video_interview_invites_message_id_idx    INDEX     p   CREATE INDEX video_interview_invites_message_id_idx ON public.video_interview_invites USING btree (message_id);
 :   DROP INDEX public.video_interview_invites_message_id_idx;
       public            jes.87kXz4RuQk    false    290            b           1259    147425 &   video_interview_invites_project_id_idx    INDEX     p   CREATE INDEX video_interview_invites_project_id_idx ON public.video_interview_invites USING btree (project_id);
 :   DROP INDEX public.video_interview_invites_project_id_idx;
       public            jes.87kXz4RuQk    false    290            c           1259    147423 .   video_interview_invites_video_interview_id_idx    INDEX     �   CREATE INDEX video_interview_invites_video_interview_id_idx ON public.video_interview_invites USING btree (video_interview_id);
 B   DROP INDEX public.video_interview_invites_video_interview_id_idx;
       public            jes.87kXz4RuQk    false    290            w           1259    147487 &   video_interview_questions_video_id_idx    INDEX     p   CREATE INDEX video_interview_questions_video_id_idx ON public.video_interview_questions USING btree (video_id);
 :   DROP INDEX public.video_interview_questions_video_id_idx;
       public            jes.87kXz4RuQk    false    297            x           1259    147486 0   video_interview_questions_video_interview_id_idx    INDEX     �   CREATE INDEX video_interview_questions_video_interview_id_idx ON public.video_interview_questions USING btree (video_interview_id);
 D   DROP INDEX public.video_interview_questions_video_interview_id_idx;
       public            jes.87kXz4RuQk    false    297            j           1259    147649 &   video_interview_responses_video_id_idx    INDEX     p   CREATE INDEX video_interview_responses_video_id_idx ON public.video_interview_responses USING btree (video_id);
 :   DROP INDEX public.video_interview_responses_video_id_idx;
       public            jes.87kXz4RuQk    false    293            k           1259    147646 7   video_interview_responses_video_interview_invite_id_idx    INDEX     �   CREATE INDEX video_interview_responses_video_interview_invite_id_idx ON public.video_interview_responses USING btree (video_interview_invite_id);
 K   DROP INDEX public.video_interview_responses_video_interview_invite_id_idx;
       public            jes.87kXz4RuQk    false    293            l           1259    147650 9   video_interview_responses_video_interview_question_id_idx    INDEX     �   CREATE INDEX video_interview_responses_video_interview_question_id_idx ON public.video_interview_responses USING btree (video_interview_question_id);
 M   DROP INDEX public.video_interview_responses_video_interview_question_id_idx;
       public            jes.87kXz4RuQk    false    293            �           2606    147928 0   action_candidate action_candidate_action_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.action_candidate
    ADD CONSTRAINT action_candidate_action_id_fkey FOREIGN KEY (action_id) REFERENCES public.actions(id) ON DELETE CASCADE;
 Z   ALTER TABLE ONLY public.action_candidate DROP CONSTRAINT action_candidate_action_id_fkey;
       public          jes.87kXz4RuQk    false    3765    214    238            �           2606    147938 3   action_candidate action_candidate_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.action_candidate
    ADD CONSTRAINT action_candidate_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 ]   ALTER TABLE ONLY public.action_candidate DROP CONSTRAINT action_candidate_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    214    215    3679            �           2606    148443 +   activities activities_activity_type_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_activity_type_id_fkey FOREIGN KEY (activity_type_id) REFERENCES public.activity_types(id) ON DELETE CASCADE;
 U   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_activity_type_id_fkey;
       public          jes.87kXz4RuQk    false    223    227    3734            �           2606    148457 "   activities activities_call_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_call_id_fkey FOREIGN KEY (call_id) REFERENCES public.calls(id);
 L   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_call_id_fkey;
       public          jes.87kXz4RuQk    false    242    223    3773            �           2606    148464 '   activities activities_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id) ON DELETE CASCADE;
 Q   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    215    223            �           2606    148473 %   activities activities_comment_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_comment_id_fkey FOREIGN KEY (comment_id) REFERENCES public.comments(id) ON DELETE CASCADE;
 O   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_comment_id_fkey;
       public          jes.87kXz4RuQk    false    3683    216    223            �           2606    148483 ,   activities activities_dropout_reason_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_dropout_reason_id_fkey FOREIGN KEY (dropout_reason_id) REFERENCES public.dropout_reasons(id);
 V   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_dropout_reason_id_fkey;
       public          jes.87kXz4RuQk    false    3697    220    223            �           2606    148488 "   activities activities_form_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_form_id_fkey FOREIGN KEY (form_id) REFERENCES public.forms(id);
 L   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_form_id_fkey;
       public          jes.87kXz4RuQk    false    275    3883    223            �           2606    148498 )   activities activities_integration_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES public.integrations(id);
 S   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_integration_id_fkey;
       public          jes.87kXz4RuQk    false    3867    223    271            �           2606    148478 %   activities activities_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id) ON DELETE CASCADE;
 O   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_message_id_fkey;
       public          jes.87kXz4RuQk    false    3831    260    223            �           2606    148508 %   activities activities_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON DELETE CASCADE;
 O   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_project_id_fkey;
       public          jes.87kXz4RuQk    false    3911    283    223            �           2606    148493 '   activities activities_scorecard_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_scorecard_id_fkey FOREIGN KEY (scorecard_id) REFERENCES public.scorecards(id) ON DELETE CASCADE;
 Q   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_scorecard_id_fkey;
       public          jes.87kXz4RuQk    false    223    273    3877            �           2606    148513 !   activities activities_sms_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_sms_id_fkey FOREIGN KEY (sms_id) REFERENCES public.sms(id);
 K   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_sms_id_fkey;
       public          jes.87kXz4RuQk    false    278    223    3891            �           2606    148523 #   activities activities_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_stage_id_fkey FOREIGN KEY (stage_id) REFERENCES public.stages(id) ON DELETE CASCADE;
 M   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_stage_id_fkey;
       public          jes.87kXz4RuQk    false    280    3899    223            �           2606    148518 (   activities activities_submission_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_submission_id_fkey FOREIGN KEY (submission_id) REFERENCES public.submissions(id);
 R   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_submission_id_fkey;
       public          jes.87kXz4RuQk    false    294    223    3952            �           2606    148503 "   activities activities_task_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.tasks(id);
 L   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_task_id_fkey;
       public          jes.87kXz4RuQk    false    223    3965    298            �           2606    148533 "   activities activities_team_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 L   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_team_id_fkey;
       public          jes.87kXz4RuQk    false    295    3954    223            �           2606    148528 "   activities activities_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activities
    ADD CONSTRAINT activities_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
 L   ALTER TABLE ONLY public.activities DROP CONSTRAINT activities_user_id_fkey;
       public          jes.87kXz4RuQk    false    223    289    3931            �           2606    147828 4   activity_receipts activity_receipts_activity_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activity_receipts
    ADD CONSTRAINT activity_receipts_activity_id_fkey FOREIGN KEY (activity_id) REFERENCES public.activities(id) ON DELETE CASCADE;
 ^   ALTER TABLE ONLY public.activity_receipts DROP CONSTRAINT activity_receipts_activity_id_fkey;
       public          jes.87kXz4RuQk    false    224    223    3713            �           2606    147823 0   activity_receipts activity_receipts_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activity_receipts
    ADD CONSTRAINT activity_receipts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
 Z   ALTER TABLE ONLY public.activity_receipts DROP CONSTRAINT activity_receipts_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    224    3931            �           2606    147813 =   activity_subscriptions activity_subscriptions_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activity_subscriptions
    ADD CONSTRAINT activity_subscriptions_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 g   ALTER TABLE ONLY public.activity_subscriptions DROP CONSTRAINT activity_subscriptions_project_id_fkey;
       public          jes.87kXz4RuQk    false    233    3911    283            �           2606    147818 :   activity_subscriptions activity_subscriptions_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.activity_subscriptions
    ADD CONSTRAINT activity_subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 d   ALTER TABLE ONLY public.activity_subscriptions DROP CONSTRAINT activity_subscriptions_user_id_fkey;
       public          jes.87kXz4RuQk    false    3931    233    289            �           2606    147788 $   auth_tokens auth_tokens_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.auth_tokens
    ADD CONSTRAINT auth_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 N   ALTER TABLE ONLY public.auth_tokens DROP CONSTRAINT auth_tokens_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    3931    228            �           2606    147978    calls calls_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.calls
    ADD CONSTRAINT calls_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 G   ALTER TABLE ONLY public.calls DROP CONSTRAINT calls_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    242    215    3679            �           2606    147983    calls calls_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.calls
    ADD CONSTRAINT calls_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 E   ALTER TABLE ONLY public.calls DROP CONSTRAINT calls_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    3911    242            �           2606    147988    calls calls_user_id_fkey 
   FK CONSTRAINT     w   ALTER TABLE ONLY public.calls
    ADD CONSTRAINT calls_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 B   ALTER TABLE ONLY public.calls DROP CONSTRAINT calls_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    3931    242            �           2606    147868 A   candidate_cv_ai_summary candidate_cv_ai_summary_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_cv_ai_summary
    ADD CONSTRAINT candidate_cv_ai_summary_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id) ON DELETE CASCADE;
 k   ALTER TABLE ONLY public.candidate_cv_ai_summary DROP CONSTRAINT candidate_cv_ai_summary_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    249    3679    215            �           2606    147873 :   candidate_cv_ai_summary candidate_cv_ai_summary_cv_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_cv_ai_summary
    ADD CONSTRAINT candidate_cv_ai_summary_cv_id_fkey FOREIGN KEY (cv_id) REFERENCES public.files(id) ON DELETE CASCADE;
 d   ALTER TABLE ONLY public.candidate_cv_ai_summary DROP CONSTRAINT candidate_cv_ai_summary_cv_id_fkey;
       public          jes.87kXz4RuQk    false    249    245    3782            �           2606    147808 0   candidate_filters candidate_filters_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_filters
    ADD CONSTRAINT candidate_filters_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 Z   ALTER TABLE ONLY public.candidate_filters DROP CONSTRAINT candidate_filters_user_id_fkey;
       public          jes.87kXz4RuQk    false    232    289    3931            �           2606    147903 =   candidate_integration candidate_integration_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_integration
    ADD CONSTRAINT candidate_integration_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 g   ALTER TABLE ONLY public.candidate_integration DROP CONSTRAINT candidate_integration_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    244    215    3679            �           2606    147908 ?   candidate_integration candidate_integration_integration_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_integration
    ADD CONSTRAINT candidate_integration_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES public.integrations(id);
 i   ALTER TABLE ONLY public.candidate_integration DROP CONSTRAINT candidate_integration_integration_id_fkey;
       public          jes.87kXz4RuQk    false    244    271    3867            �           2606    147838 ?   candidate_presentation candidate_presentation_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_presentation
    ADD CONSTRAINT candidate_presentation_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 i   ALTER TABLE ONLY public.candidate_presentation DROP CONSTRAINT candidate_presentation_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    222    3679    215            �           2606    147829 B   candidate_presentation candidate_presentation_presentation_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_presentation
    ADD CONSTRAINT candidate_presentation_presentation_id_fkey FOREIGN KEY (presentation_id) REFERENCES public.presentations(id);
 l   ALTER TABLE ONLY public.candidate_presentation DROP CONSTRAINT candidate_presentation_presentation_id_fkey;
       public          jes.87kXz4RuQk    false    3839    222    263            �           2606    147798 -   candidate_sms candidate_sms_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_sms
    ADD CONSTRAINT candidate_sms_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 W   ALTER TABLE ONLY public.candidate_sms DROP CONSTRAINT candidate_sms_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    215    3679    236            �           2606    147803 '   candidate_sms candidate_sms_sms_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_sms
    ADD CONSTRAINT candidate_sms_sms_id_fkey FOREIGN KEY (sms_id) REFERENCES public.sms(id);
 Q   ALTER TABLE ONLY public.candidate_sms DROP CONSTRAINT candidate_sms_sms_id_fkey;
       public          jes.87kXz4RuQk    false    236    278    3891            �           2606    147993 1   candidate_stage candidate_stage_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_stage
    ADD CONSTRAINT candidate_stage_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 [   ALTER TABLE ONLY public.candidate_stage DROP CONSTRAINT candidate_stage_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    248    215            �           2606    148003 6   candidate_stage candidate_stage_dropout_reason_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_stage
    ADD CONSTRAINT candidate_stage_dropout_reason_id_fkey FOREIGN KEY (dropout_reason_id) REFERENCES public.dropout_reasons(id) ON DELETE RESTRICT;
 `   ALTER TABLE ONLY public.candidate_stage DROP CONSTRAINT candidate_stage_dropout_reason_id_fkey;
       public          jes.87kXz4RuQk    false    220    248    3697            �           2606    147998 -   candidate_stage candidate_stage_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_stage
    ADD CONSTRAINT candidate_stage_stage_id_fkey FOREIGN KEY (stage_id) REFERENCES public.stages(id);
 W   ALTER TABLE ONLY public.candidate_stage DROP CONSTRAINT candidate_stage_stage_id_fkey;
       public          jes.87kXz4RuQk    false    280    3899    248            �           2606    147858 9   candidate_summaries candidate_summaries_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_summaries
    ADD CONSTRAINT candidate_summaries_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 c   ALTER TABLE ONLY public.candidate_summaries DROP CONSTRAINT candidate_summaries_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    215    237            �           2606    147863 7   candidate_summaries candidate_summaries_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_summaries
    ADD CONSTRAINT candidate_summaries_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 a   ALTER TABLE ONLY public.candidate_summaries DROP CONSTRAINT candidate_summaries_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    237    3911            �           2606    147943 -   candidate_tag candidate_tag_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_tag
    ADD CONSTRAINT candidate_tag_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 W   ALTER TABLE ONLY public.candidate_tag DROP CONSTRAINT candidate_tag_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    215    240            �           2606    147949 '   candidate_tag candidate_tag_tag_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidate_tag
    ADD CONSTRAINT candidate_tag_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tags(id);
 Q   ALTER TABLE ONLY public.candidate_tag DROP CONSTRAINT candidate_tag_tag_id_fkey;
       public          jes.87kXz4RuQk    false    3902    281    240            �           2606    147918 (   candidates candidates_entered_by_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidates
    ADD CONSTRAINT candidates_entered_by_id_fkey FOREIGN KEY (entered_by_id) REFERENCES public.users(id);
 R   ALTER TABLE ONLY public.candidates DROP CONSTRAINT candidates_entered_by_id_fkey;
       public          jes.87kXz4RuQk    false    289    3931    215            �           2606    147923 "   candidates candidates_team_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.candidates
    ADD CONSTRAINT candidates_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 L   ALTER TABLE ONLY public.candidates DROP CONSTRAINT candidates_team_id_fkey;
       public          jes.87kXz4RuQk    false    215    295    3954            �           2606    148063 #   comments comments_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 M   ALTER TABLE ONLY public.comments DROP CONSTRAINT comments_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    215    216    3679            �           2606    148073 !   comments comments_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 K   ALTER TABLE ONLY public.comments DROP CONSTRAINT comments_project_id_fkey;
       public          jes.87kXz4RuQk    false    216    283    3911            �           2606    148068    comments comments_team_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 H   ALTER TABLE ONLY public.comments DROP CONSTRAINT comments_team_id_fkey;
       public          jes.87kXz4RuQk    false    295    216    3954            �           2606    148093    comments comments_user_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 H   ALTER TABLE ONLY public.comments DROP CONSTRAINT comments_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    216    3931            �           2606    148083 0   comments comments_video_interview_invite_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.comments
    ADD CONSTRAINT comments_video_interview_invite_id_fkey FOREIGN KEY (video_interview_invite_id) REFERENCES public.video_interview_invites(id);
 Z   ALTER TABLE ONLY public.comments DROP CONSTRAINT comments_video_interview_invite_id_fkey;
       public          jes.87kXz4RuQk    false    3937    290    216            �           2606    147843 3   consent_renewals consent_renewals_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.consent_renewals
    ADD CONSTRAINT consent_renewals_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 ]   ALTER TABLE ONLY public.consent_renewals DROP CONSTRAINT consent_renewals_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    235    215    3679            �           2606    147848 1   consent_renewals consent_renewals_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.consent_renewals
    ADD CONSTRAINT consent_renewals_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id);
 [   ALTER TABLE ONLY public.consent_renewals DROP CONSTRAINT consent_renewals_message_id_fkey;
       public          jes.87kXz4RuQk    false    3831    235    260            �           2606    147888 #   consents consents_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.consents
    ADD CONSTRAINT consents_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 M   ALTER TABLE ONLY public.consents DROP CONSTRAINT consents_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    250    215            �           2606    147883 !   consents consents_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.consents
    ADD CONSTRAINT consents_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 K   ALTER TABLE ONLY public.consents DROP CONSTRAINT consents_project_id_fkey;
       public          jes.87kXz4RuQk    false    250    283    3911            �           2606    147893    consents consents_user_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.consents
    ADD CONSTRAINT consents_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 H   ALTER TABLE ONLY public.consents DROP CONSTRAINT consents_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    250    3931            �           2606    147793 2   crm_contacts crm_contacts_crm_organization_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.crm_contacts
    ADD CONSTRAINT crm_contacts_crm_organization_id_fkey FOREIGN KEY (crm_organization_id) REFERENCES public.crm_organizations(id);
 \   ALTER TABLE ONLY public.crm_contacts DROP CONSTRAINT crm_contacts_crm_organization_id_fkey;
       public          jes.87kXz4RuQk    false    218    251    3693            �           2606    147853 0   crm_offices crm_offices_crm_organization_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.crm_offices
    ADD CONSTRAINT crm_offices_crm_organization_id_fkey FOREIGN KEY (crm_organization_id) REFERENCES public.crm_organizations(id);
 Z   ALTER TABLE ONLY public.crm_offices DROP CONSTRAINT crm_offices_crm_organization_id_fkey;
       public          jes.87kXz4RuQk    false    217    218    3693            �           2606    147878 :   crm_organizations crm_organizations_client_manager_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.crm_organizations
    ADD CONSTRAINT crm_organizations_client_manager_id_fkey FOREIGN KEY (client_manager_id) REFERENCES public.users(id);
 d   ALTER TABLE ONLY public.crm_organizations DROP CONSTRAINT crm_organizations_client_manager_id_fkey;
       public          jes.87kXz4RuQk    false    3931    218    289            �           2606    147948 '   educations educations_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.educations
    ADD CONSTRAINT educations_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 Q   ALTER TABLE ONLY public.educations DROP CONSTRAINT educations_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    221    215    3679            �           2606    147898 )   employments employments_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.employments
    ADD CONSTRAINT employments_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 S   ALTER TABLE ONLY public.employments DROP CONSTRAINT employments_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    215    226            �           2606    147973 /   event_set_user event_set_user_event_set_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.event_set_user
    ADD CONSTRAINT event_set_user_event_set_id_fkey FOREIGN KEY (event_set_id) REFERENCES public.event_sets(id) ON DELETE CASCADE;
 Y   ALTER TABLE ONLY public.event_set_user DROP CONSTRAINT event_set_user_event_set_id_fkey;
       public          jes.87kXz4RuQk    false    230    234    3752            �           2606    147968 *   event_set_user event_set_user_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.event_set_user
    ADD CONSTRAINT event_set_user_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;
 T   ALTER TABLE ONLY public.event_set_user DROP CONSTRAINT event_set_user_user_id_fkey;
       public          jes.87kXz4RuQk    false    3931    230    289            �           2606    148153 $   event_sets event_sets_author_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.event_sets
    ADD CONSTRAINT event_sets_author_id_fkey FOREIGN KEY (author_id) REFERENCES public.users(id);
 N   ALTER TABLE ONLY public.event_sets DROP CONSTRAINT event_sets_author_id_fkey;
       public          jes.87kXz4RuQk    false    3931    234    289            �           2606    148143 %   event_sets event_sets_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.event_sets
    ADD CONSTRAINT event_sets_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 O   ALTER TABLE ONLY public.event_sets DROP CONSTRAINT event_sets_project_id_fkey;
       public          jes.87kXz4RuQk    false    234    283    3911            �           2606    147933    events events_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id);
 G   ALTER TABLE ONLY public.events DROP CONSTRAINT events_message_id_fkey;
       public          jes.87kXz4RuQk    false    3831    260    241            �           2606    147958    files files_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 E   ALTER TABLE ONLY public.files DROP CONSTRAINT files_project_id_fkey;
       public          jes.87kXz4RuQk    false    245    283    3911            �           2606    148043    forms forms_stage_id_fkey 
   FK CONSTRAINT     z   ALTER TABLE ONLY public.forms
    ADD CONSTRAINT forms_stage_id_fkey FOREIGN KEY (stage_id) REFERENCES public.stages(id);
 C   ALTER TABLE ONLY public.forms DROP CONSTRAINT forms_stage_id_fkey;
       public          jes.87kXz4RuQk    false    3899    275    280            �           2606    148053    forms forms_team_id_fkey 
   FK CONSTRAINT     w   ALTER TABLE ONLY public.forms
    ADD CONSTRAINT forms_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 B   ALTER TABLE ONLY public.forms DROP CONSTRAINT forms_team_id_fkey;
       public          jes.87kXz4RuQk    false    275    295    3954            �           2606    148112 ;   integration_message integration_message_integration_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integration_message
    ADD CONSTRAINT integration_message_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES public.integrations(id);
 e   ALTER TABLE ONLY public.integration_message DROP CONSTRAINT integration_message_integration_id_fkey;
       public          jes.87kXz4RuQk    false    247    3867    271            �           2606    148123 7   integration_message integration_message_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integration_message
    ADD CONSTRAINT integration_message_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id);
 a   ALTER TABLE ONLY public.integration_message DROP CONSTRAINT integration_message_message_id_fkey;
       public          jes.87kXz4RuQk    false    247    260    3831            �           2606    148018 U   integration_structured_job_ad integration_structured_job_ad_destination_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integration_structured_job_ad
    ADD CONSTRAINT integration_structured_job_ad_destination_stage_id_fkey FOREIGN KEY (destination_stage_id) REFERENCES public.stages(id);
    ALTER TABLE ONLY public.integration_structured_job_ad DROP CONSTRAINT integration_structured_job_ad_destination_stage_id_fkey;
       public          jes.87kXz4RuQk    false    280    3899    267            �           2606    148028 O   integration_structured_job_ad integration_structured_job_ad_integration_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integration_structured_job_ad
    ADD CONSTRAINT integration_structured_job_ad_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES public.integrations(id);
 y   ALTER TABLE ONLY public.integration_structured_job_ad DROP CONSTRAINT integration_structured_job_ad_integration_id_fkey;
       public          jes.87kXz4RuQk    false    267    3867    271            �           2606    148023 U   integration_structured_job_ad integration_structured_job_ad_structured_job_ad_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integration_structured_job_ad
    ADD CONSTRAINT integration_structured_job_ad_structured_job_ad_id_fkey FOREIGN KEY (structured_job_ad_id) REFERENCES public.structured_job_ads(id);
    ALTER TABLE ONLY public.integration_structured_job_ad DROP CONSTRAINT integration_structured_job_ad_structured_job_ad_id_fkey;
       public          jes.87kXz4RuQk    false    3973    267    299            �           2606    148008 &   integrations integrations_team_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT integrations_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 P   ALTER TABLE ONLY public.integrations DROP CONSTRAINT integrations_team_id_fkey;
       public          jes.87kXz4RuQk    false    295    271    3954            �           2606    148013 &   integrations integrations_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT integrations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 P   ALTER TABLE ONLY public.integrations DROP CONSTRAINT integrations_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    3931    271            �           2606    148213 !   invites invites_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id) ON DELETE CASCADE;
 K   ALTER TABLE ONLY public.invites DROP CONSTRAINT invites_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    215    3679    252            �           2606    148208 !   invites invites_event_set_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_event_set_id_fkey FOREIGN KEY (event_set_id) REFERENCES public.event_sets(id) ON DELETE CASCADE;
 K   ALTER TABLE ONLY public.invites DROP CONSTRAINT invites_event_set_id_fkey;
       public          jes.87kXz4RuQk    false    234    252    3752            �           2606    148218 *   invites invites_selected_time_slot_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.invites
    ADD CONSTRAINT invites_selected_time_slot_id_fkey FOREIGN KEY (selected_time_slot_id) REFERENCES public.time_slots(id) ON DELETE CASCADE;
 T   ALTER TABLE ONLY public.invites DROP CONSTRAINT invites_selected_time_slot_id_fkey;
       public          jes.87kXz4RuQk    false    286    3925    252            �           2606    148133 O   job_ad_feed_structured_job_ad job_ad_feed_structured_job_ad_job_ad_feed_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.job_ad_feed_structured_job_ad
    ADD CONSTRAINT job_ad_feed_structured_job_ad_job_ad_feed_id_fkey FOREIGN KEY (job_ad_feed_id) REFERENCES public.job_ad_feeds(id);
 y   ALTER TABLE ONLY public.job_ad_feed_structured_job_ad DROP CONSTRAINT job_ad_feed_structured_job_ad_job_ad_feed_id_fkey;
       public          jes.87kXz4RuQk    false    253    254    3812            �           2606    148138 U   job_ad_feed_structured_job_ad job_ad_feed_structured_job_ad_structured_job_ad_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.job_ad_feed_structured_job_ad
    ADD CONSTRAINT job_ad_feed_structured_job_ad_structured_job_ad_id_fkey FOREIGN KEY (structured_job_ad_id) REFERENCES public.structured_job_ads(id);
    ALTER TABLE ONLY public.job_ad_feed_structured_job_ad DROP CONSTRAINT job_ad_feed_structured_job_ad_structured_job_ad_id_fkey;
       public          jes.87kXz4RuQk    false    3973    299    253            �           2606    147913 -   job_ad_feeds job_ad_feeds_integration_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.job_ad_feeds
    ADD CONSTRAINT job_ad_feeds_integration_id_fkey FOREIGN KEY (integration_id) REFERENCES public.integrations(id);
 W   ALTER TABLE ONLY public.job_ad_feeds DROP CONSTRAINT job_ad_feeds_integration_id_fkey;
       public          jes.87kXz4RuQk    false    271    3867    254            �           2606    148048 7   landing_dei_reports landing_dei_reports_landing_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.landing_dei_reports
    ADD CONSTRAINT landing_dei_reports_landing_id_fkey FOREIGN KEY (landing_id) REFERENCES public.landings(id);
 a   ALTER TABLE ONLY public.landing_dei_reports DROP CONSTRAINT landing_dei_reports_landing_id_fkey;
       public          jes.87kXz4RuQk    false    258    255    3823            �           2606    148033 '   landing_tag landing_tag_landing_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.landing_tag
    ADD CONSTRAINT landing_tag_landing_id_fkey FOREIGN KEY (landing_id) REFERENCES public.landings(id);
 Q   ALTER TABLE ONLY public.landing_tag DROP CONSTRAINT landing_tag_landing_id_fkey;
       public          jes.87kXz4RuQk    false    3823    256    258            �           2606    148038 +   landing_tag landing_tag_landing_tag_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.landing_tag
    ADD CONSTRAINT landing_tag_landing_tag_id_fkey FOREIGN KEY (landing_tag_id) REFERENCES public.landing_tags(id);
 U   ALTER TABLE ONLY public.landing_tag DROP CONSTRAINT landing_tag_landing_tag_id_fkey;
       public          jes.87kXz4RuQk    false    3821    256    257            �           2606    148084    landings landings_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.landings
    ADD CONSTRAINT landings_stage_id_fkey FOREIGN KEY (stage_id) REFERENCES public.stages(id);
 I   ALTER TABLE ONLY public.landings DROP CONSTRAINT landings_stage_id_fkey;
       public          jes.87kXz4RuQk    false    280    258    3899            �           2606    148074    landings landings_team_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.landings
    ADD CONSTRAINT landings_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 H   ALTER TABLE ONLY public.landings DROP CONSTRAINT landings_team_id_fkey;
       public          jes.87kXz4RuQk    false    295    3954    258            �           2606    148188 )   messageables messageables_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.messageables
    ADD CONSTRAINT messageables_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id);
 S   ALTER TABLE ONLY public.messageables DROP CONSTRAINT messageables_message_id_fkey;
       public          jes.87kXz4RuQk    false    260    3831    259            �           2606    148193 (   messageables messageables_survey_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.messageables
    ADD CONSTRAINT messageables_survey_id_fkey FOREIGN KEY (survey_id) REFERENCES public.surveys(id);
 R   ALTER TABLE ONLY public.messageables DROP CONSTRAINT messageables_survey_id_fkey;
       public          jes.87kXz4RuQk    false    3917    259    284            �           2606    148148    messages messages_team_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 H   ALTER TABLE ONLY public.messages DROP CONSTRAINT messages_team_id_fkey;
       public          jes.87kXz4RuQk    false    260    295    3954            �           2606    148158    messages messages_user_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.messages
    ADD CONSTRAINT messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 H   ALTER TABLE ONLY public.messages DROP CONSTRAINT messages_user_id_fkey;
       public          jes.87kXz4RuQk    false    260    289    3931            �           2606    147963 $   nps_surveys nps_surveys_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.nps_surveys
    ADD CONSTRAINT nps_surveys_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 N   ALTER TABLE ONLY public.nps_surveys DROP CONSTRAINT nps_surveys_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    3931    264            �           2606    148178 +   presentations presentations_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.presentations
    ADD CONSTRAINT presentations_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id);
 U   ALTER TABLE ONLY public.presentations DROP CONSTRAINT presentations_message_id_fkey;
       public          jes.87kXz4RuQk    false    260    263    3831            �           2606    148183 +   presentations presentations_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.presentations
    ADD CONSTRAINT presentations_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 U   ALTER TABLE ONLY public.presentations DROP CONSTRAINT presentations_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    263    3911            �           2606    148108 )   project_logs project_logs_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.project_logs
    ADD CONSTRAINT project_logs_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 S   ALTER TABLE ONLY public.project_logs DROP CONSTRAINT project_logs_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    274    3911            �           2606    148118 &   project_logs project_logs_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.project_logs
    ADD CONSTRAINT project_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 P   ALTER TABLE ONLY public.project_logs DROP CONSTRAINT project_logs_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    274    3931            �           2606    148168 )   project_user project_user_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.project_user
    ADD CONSTRAINT project_user_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 S   ALTER TABLE ONLY public.project_user DROP CONSTRAINT project_user_project_id_fkey;
       public          jes.87kXz4RuQk    false    3911    283    265            �           2606    148173 &   project_user project_user_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.project_user
    ADD CONSTRAINT project_user_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 P   ALTER TABLE ONLY public.project_user DROP CONSTRAINT project_user_user_id_fkey;
       public          jes.87kXz4RuQk    false    3931    289    265            �           2606    148323 $   projects projects_crm_office_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_crm_office_id_fkey FOREIGN KEY (crm_office_id) REFERENCES public.crm_offices(id);
 N   ALTER TABLE ONLY public.projects DROP CONSTRAINT projects_crm_office_id_fkey;
       public          jes.87kXz4RuQk    false    3690    283    217            �           2606    148331 *   projects projects_crm_organization_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_crm_organization_id_fkey FOREIGN KEY (crm_organization_id) REFERENCES public.crm_organizations(id);
 T   ALTER TABLE ONLY public.projects DROP CONSTRAINT projects_crm_organization_id_fkey;
       public          jes.87kXz4RuQk    false    3693    218    283            �           2606    148303 )   projects projects_project_manager_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_project_manager_id_fkey FOREIGN KEY (project_manager_id) REFERENCES public.users(id);
 S   ALTER TABLE ONLY public.projects DROP CONSTRAINT projects_project_manager_id_fkey;
       public          jes.87kXz4RuQk    false    3931    289    283            �           2606    148313 #   projects projects_scorecard_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_scorecard_id_fkey FOREIGN KEY (scorecard_id) REFERENCES public.scorecards(id);
 M   ALTER TABLE ONLY public.projects DROP CONSTRAINT projects_scorecard_id_fkey;
       public          jes.87kXz4RuQk    false    3877    273    283            �           2606    148308    projects projects_team_id_fkey 
   FK CONSTRAINT     }   ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 H   ALTER TABLE ONLY public.projects DROP CONSTRAINT projects_team_id_fkey;
       public          jes.87kXz4RuQk    false    3954    283    295            �           2606    148203    reacts reacts_comment_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.reacts
    ADD CONSTRAINT reacts_comment_id_fkey FOREIGN KEY (comment_id) REFERENCES public.comments(id) ON DELETE CASCADE;
 G   ALTER TABLE ONLY public.reacts DROP CONSTRAINT reacts_comment_id_fkey;
       public          jes.87kXz4RuQk    false    282    3683    216            �           2606    148198    reacts reacts_user_id_fkey 
   FK CONSTRAINT     y   ALTER TABLE ONLY public.reacts
    ADD CONSTRAINT reacts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 D   ALTER TABLE ONLY public.reacts DROP CONSTRAINT reacts_user_id_fkey;
       public          jes.87kXz4RuQk    false    282    3931    289            �           2606    148103 ?   requisition_approvals requisition_approvals_requisition_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.requisition_approvals
    ADD CONSTRAINT requisition_approvals_requisition_id_fkey FOREIGN KEY (requisition_id) REFERENCES public.requisitions(id);
 i   ALTER TABLE ONLY public.requisition_approvals DROP CONSTRAINT requisition_approvals_requisition_id_fkey;
       public          jes.87kXz4RuQk    false    269    3859    266            �           2606    148098 8   requisition_approvals requisition_approvals_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.requisition_approvals
    ADD CONSTRAINT requisition_approvals_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 b   ALTER TABLE ONLY public.requisition_approvals DROP CONSTRAINT requisition_approvals_user_id_fkey;
       public          jes.87kXz4RuQk    false    266    289    3931            �           2606    148253 )   requisitions requisitions_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.requisitions
    ADD CONSTRAINT requisitions_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 S   ALTER TABLE ONLY public.requisitions DROP CONSTRAINT requisitions_project_id_fkey;
       public          jes.87kXz4RuQk    false    3911    269    283            �           2606    148258 +   requisitions requisitions_recruiter_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.requisitions
    ADD CONSTRAINT requisitions_recruiter_id_fkey FOREIGN KEY (recruiter_id) REFERENCES public.users(id);
 U   ALTER TABLE ONLY public.requisitions DROP CONSTRAINT requisitions_recruiter_id_fkey;
       public          jes.87kXz4RuQk    false    269    3931    289            �           2606    148263 &   requisitions requisitions_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.requisitions
    ADD CONSTRAINT requisitions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 P   ALTER TABLE ONLY public.requisitions DROP CONSTRAINT requisitions_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    269    3931            �           2606    148233 7   scorecard_comments scorecard_comments_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_comments
    ADD CONSTRAINT scorecard_comments_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 a   ALTER TABLE ONLY public.scorecard_comments DROP CONSTRAINT scorecard_comments_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    268    215            �           2606    148223 5   scorecard_comments scorecard_comments_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_comments
    ADD CONSTRAINT scorecard_comments_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 _   ALTER TABLE ONLY public.scorecard_comments DROP CONSTRAINT scorecard_comments_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    268    3911            �           2606    148228 2   scorecard_comments scorecard_comments_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_comments
    ADD CONSTRAINT scorecard_comments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 \   ALTER TABLE ONLY public.scorecard_comments DROP CONSTRAINT scorecard_comments_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    268    3931            �           2606    148058 9   scorecard_questions scorecard_questions_scorecard_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_questions
    ADD CONSTRAINT scorecard_questions_scorecard_id_fkey FOREIGN KEY (scorecard_id) REFERENCES public.scorecards(id);
 c   ALTER TABLE ONLY public.scorecard_questions DROP CONSTRAINT scorecard_questions_scorecard_id_fkey;
       public          jes.87kXz4RuQk    false    270    3877    273            �           2606    148368 9   scorecard_responses scorecard_responses_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_responses
    ADD CONSTRAINT scorecard_responses_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 c   ALTER TABLE ONLY public.scorecard_responses DROP CONSTRAINT scorecard_responses_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    272    3679    215            �           2606    148373 7   scorecard_responses scorecard_responses_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_responses
    ADD CONSTRAINT scorecard_responses_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 a   ALTER TABLE ONLY public.scorecard_responses DROP CONSTRAINT scorecard_responses_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    3911    272            �           2606    148383 B   scorecard_responses scorecard_responses_scorecard_question_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_responses
    ADD CONSTRAINT scorecard_responses_scorecard_question_id_fkey FOREIGN KEY (scorecard_question_id) REFERENCES public.scorecard_questions(id);
 l   ALTER TABLE ONLY public.scorecard_responses DROP CONSTRAINT scorecard_responses_scorecard_question_id_fkey;
       public          jes.87kXz4RuQk    false    272    3864    270            �           2606    148392 4   scorecard_responses scorecard_responses_user_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.scorecard_responses
    ADD CONSTRAINT scorecard_responses_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 ^   ALTER TABLE ONLY public.scorecard_responses DROP CONSTRAINT scorecard_responses_user_id_fkey;
       public          jes.87kXz4RuQk    false    289    3931    272            �           2606    148293    sms sms_project_id_fkey 
   FK CONSTRAINT     |   ALTER TABLE ONLY public.sms
    ADD CONSTRAINT sms_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 A   ALTER TABLE ONLY public.sms DROP CONSTRAINT sms_project_id_fkey;
       public          jes.87kXz4RuQk    false    3911    283    278            �           2606    148298    sms sms_user_id_fkey 
   FK CONSTRAINT     s   ALTER TABLE ONLY public.sms
    ADD CONSTRAINT sms_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 >   ALTER TABLE ONLY public.sms DROP CONSTRAINT sms_user_id_fkey;
       public          jes.87kXz4RuQk    false    278    3931    289            �           2606    148128 -   source_budgets source_budgets_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.source_budgets
    ADD CONSTRAINT source_budgets_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 W   ALTER TABLE ONLY public.source_budgets DROP CONSTRAINT source_budgets_project_id_fkey;
       public          jes.87kXz4RuQk    false    279    283    3911            �           2606    148163 +   stages stages_custom_stage_category_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.stages
    ADD CONSTRAINT stages_custom_stage_category_id_fkey FOREIGN KEY (custom_stage_category_id) REFERENCES public.custom_stage_categories(id);
 U   ALTER TABLE ONLY public.stages DROP CONSTRAINT stages_custom_stage_category_id_fkey;
       public          jes.87kXz4RuQk    false    219    280    3695                       2606    148423 >   structured_job_ads structured_job_ads_cvb_target_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_cvb_target_stage_id_fkey FOREIGN KEY (cvb_target_stage_id) REFERENCES public.stages(id);
 h   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_cvb_target_stage_id_fkey;
       public          jes.87kXz4RuQk    false    299    280    3899                       2606    148428 >   structured_job_ads structured_job_ads_cvk_target_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_cvk_target_stage_id_fkey FOREIGN KEY (cvk_target_stage_id) REFERENCES public.stages(id);
 h   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_cvk_target_stage_id_fkey;
       public          jes.87kXz4RuQk    false    299    280    3899                       2606    148433 ?   structured_job_ads structured_job_ads_cvo_remote_source_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_cvo_remote_source_id_fkey FOREIGN KEY (cvo_remote_source_id) REFERENCES public.integrations(id);
 i   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_cvo_remote_source_id_fkey;
       public          jes.87kXz4RuQk    false    299    271    3867                       2606    148438 >   structured_job_ads structured_job_ads_cvo_target_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_cvo_target_stage_id_fkey FOREIGN KEY (cvo_target_stage_id) REFERENCES public.stages(id);
 h   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_cvo_target_stage_id_fkey;
       public          jes.87kXz4RuQk    false    3899    280    299                       2606    148448 5   structured_job_ads structured_job_ads_landing_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_landing_id_fkey FOREIGN KEY (landing_id) REFERENCES public.landings(id);
 _   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_landing_id_fkey;
       public          jes.87kXz4RuQk    false    299    3823    258                       2606    148463 5   structured_job_ads structured_job_ads_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 _   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_project_id_fkey;
       public          jes.87kXz4RuQk    false    3911    299    283                       2606    148453 :   structured_job_ads structured_job_ads_target_stage_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.structured_job_ads
    ADD CONSTRAINT structured_job_ads_target_stage_id_fkey FOREIGN KEY (target_stage_id) REFERENCES public.stages(id);
 d   ALTER TABLE ONLY public.structured_job_ads DROP CONSTRAINT structured_job_ads_target_stage_id_fkey;
       public          jes.87kXz4RuQk    false    280    299    3899            
           2606    148273 )   submissions submissions_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 S   ALTER TABLE ONLY public.submissions DROP CONSTRAINT submissions_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    294    3679    215                       2606    148278 $   submissions submissions_form_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.submissions
    ADD CONSTRAINT submissions_form_id_fkey FOREIGN KEY (form_id) REFERENCES public.forms(id);
 N   ALTER TABLE ONLY public.submissions DROP CONSTRAINT submissions_form_id_fkey;
       public          jes.87kXz4RuQk    false    3883    294    275                        2606    148358 !   surveys surveys_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.surveys
    ADD CONSTRAINT surveys_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 K   ALTER TABLE ONLY public.surveys DROP CONSTRAINT surveys_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    284    3679    215                       2606    148363    surveys surveys_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.surveys
    ADD CONSTRAINT surveys_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 I   ALTER TABLE ONLY public.surveys DROP CONSTRAINT surveys_project_id_fkey;
       public          jes.87kXz4RuQk    false    3911    283    284                       2606    148318    tasks tasks_assignee_id_fkey 
   FK CONSTRAINT        ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_assignee_id_fkey FOREIGN KEY (assignee_id) REFERENCES public.users(id);
 F   ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_assignee_id_fkey;
       public          jes.87kXz4RuQk    false    298    3931    289                       2606    148348    tasks tasks_author_id_fkey 
   FK CONSTRAINT     {   ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_author_id_fkey FOREIGN KEY (author_id) REFERENCES public.users(id);
 D   ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_author_id_fkey;
       public          jes.87kXz4RuQk    false    3931    289    298                       2606    148330    tasks tasks_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 G   ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    3679    298    215                       2606    148353    tasks tasks_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 E   ALTER TABLE ONLY public.tasks DROP CONSTRAINT tasks_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    298    3911                       2606    148243 )   team_edges team_edges_source_team_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.team_edges
    ADD CONSTRAINT team_edges_source_team_id_fkey FOREIGN KEY (source_team_id) REFERENCES public.teams(id) ON DELETE CASCADE;
 S   ALTER TABLE ONLY public.team_edges DROP CONSTRAINT team_edges_source_team_id_fkey;
       public          jes.87kXz4RuQk    false    3954    296    295                       2606    148248 )   team_edges team_edges_target_team_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.team_edges
    ADD CONSTRAINT team_edges_target_team_id_fkey FOREIGN KEY (target_team_id) REFERENCES public.teams(id) ON DELETE CASCADE;
 S   ALTER TABLE ONLY public.team_edges DROP CONSTRAINT team_edges_target_team_id_fkey;
       public          jes.87kXz4RuQk    false    296    295    3954                       2606    148283     templates templates_team_id_fkey 
   FK CONSTRAINT        ALTER TABLE ONLY public.templates
    ADD CONSTRAINT templates_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 J   ALTER TABLE ONLY public.templates DROP CONSTRAINT templates_team_id_fkey;
       public          jes.87kXz4RuQk    false    295    285    3954                       2606    148288     templates templates_user_id_fkey 
   FK CONSTRAINT        ALTER TABLE ONLY public.templates
    ADD CONSTRAINT templates_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);
 J   ALTER TABLE ONLY public.templates DROP CONSTRAINT templates_user_id_fkey;
       public          jes.87kXz4RuQk    false    3931    289    285                       2606    148238 '   time_slots time_slots_event_set_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.time_slots
    ADD CONSTRAINT time_slots_event_set_id_fkey FOREIGN KEY (event_set_id) REFERENCES public.event_sets(id);
 Q   ALTER TABLE ONLY public.time_slots DROP CONSTRAINT time_slots_event_set_id_fkey;
       public          jes.87kXz4RuQk    false    286    234    3752                       2606    148268    users users_team_id_fkey 
   FK CONSTRAINT     w   ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_team_id_fkey FOREIGN KEY (team_id) REFERENCES public.teams(id);
 B   ALTER TABLE ONLY public.users DROP CONSTRAINT users_team_id_fkey;
       public          jes.87kXz4RuQk    false    289    295    3954                       2606    148403 A   video_interview_invites video_interview_invites_candidate_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_invites
    ADD CONSTRAINT video_interview_invites_candidate_id_fkey FOREIGN KEY (candidate_id) REFERENCES public.candidates(id);
 k   ALTER TABLE ONLY public.video_interview_invites DROP CONSTRAINT video_interview_invites_candidate_id_fkey;
       public          jes.87kXz4RuQk    false    215    290    3679                       2606    148418 ?   video_interview_invites video_interview_invites_message_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_invites
    ADD CONSTRAINT video_interview_invites_message_id_fkey FOREIGN KEY (message_id) REFERENCES public.messages(id);
 i   ALTER TABLE ONLY public.video_interview_invites DROP CONSTRAINT video_interview_invites_message_id_fkey;
       public          jes.87kXz4RuQk    false    260    290    3831                       2606    148408 ?   video_interview_invites video_interview_invites_project_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_invites
    ADD CONSTRAINT video_interview_invites_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id);
 i   ALTER TABLE ONLY public.video_interview_invites DROP CONSTRAINT video_interview_invites_project_id_fkey;
       public          jes.87kXz4RuQk    false    283    290    3911            	           2606    148413 G   video_interview_invites video_interview_invites_video_interview_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_invites
    ADD CONSTRAINT video_interview_invites_video_interview_id_fkey FOREIGN KEY (video_interview_id) REFERENCES public.video_interviews(id);
 q   ALTER TABLE ONLY public.video_interview_invites DROP CONSTRAINT video_interview_invites_video_interview_id_fkey;
       public          jes.87kXz4RuQk    false    290    3943    292                       2606    148328 A   video_interview_questions video_interview_questions_video_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_questions
    ADD CONSTRAINT video_interview_questions_video_id_fkey FOREIGN KEY (video_id) REFERENCES public.videos(id);
 k   ALTER TABLE ONLY public.video_interview_questions DROP CONSTRAINT video_interview_questions_video_id_fkey;
       public          jes.87kXz4RuQk    false    3941    291    297                       2606    148343 K   video_interview_questions video_interview_questions_video_interview_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_questions
    ADD CONSTRAINT video_interview_questions_video_interview_id_fkey FOREIGN KEY (video_interview_id) REFERENCES public.video_interviews(id);
 u   ALTER TABLE ONLY public.video_interview_questions DROP CONSTRAINT video_interview_questions_video_interview_id_fkey;
       public          jes.87kXz4RuQk    false    297    292    3943            
           2606    148378 A   video_interview_responses video_interview_responses_video_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_responses
    ADD CONSTRAINT video_interview_responses_video_id_fkey FOREIGN KEY (video_id) REFERENCES public.videos(id);
 k   ALTER TABLE ONLY public.video_interview_responses DROP CONSTRAINT video_interview_responses_video_id_fkey;
       public          jes.87kXz4RuQk    false    291    3941    293                       2606    148398 R   video_interview_responses video_interview_responses_video_interview_invite_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_responses
    ADD CONSTRAINT video_interview_responses_video_interview_invite_id_fkey FOREIGN KEY (video_interview_invite_id) REFERENCES public.video_interview_invites(id);
 |   ALTER TABLE ONLY public.video_interview_responses DROP CONSTRAINT video_interview_responses_video_interview_invite_id_fkey;
       public          jes.87kXz4RuQk    false    293    290    3937                       2606    148388 T   video_interview_responses video_interview_responses_video_interview_question_id_fkey 
   FK CONSTRAINT     �   ALTER TABLE ONLY public.video_interview_responses
    ADD CONSTRAINT video_interview_responses_video_interview_question_id_fkey FOREIGN KEY (video_interview_question_id) REFERENCES public.video_interview_questions(id);
 ~   ALTER TABLE ONLY public.video_interview_responses DROP CONSTRAINT video_interview_responses_video_interview_question_id_fkey;
       public          jes.87kXz4RuQk    false    3958    297    293            �   �  x��Z�r�8��]�0_�
�]���4U��n����-%���G��#H�'�ȋ��R�R����_�i8���O����2]�/U
�J�9���u�mڧ�!� ]/\OV��0/�����������9l/�nu�į�c�G����,mU�^��Q��9�v*?����L�B�=˚|��ދ����O��r,�ՂV���sY���i�`��6B�$���:���������UYTJ:G�߆q�{/ȭI$�YXC�
�d���Ӊ�r`�脳6	o�}%ݭ��Z�ۖ)�2�����n���~Єh�����n�~��i���B�`+*�:%���x��>< ����ҕH%�6�GNw0���������]U�[Xk���e�3y/"�If��C
�&�x���|̅��<���,��^��Y�J)FYX�4�����[��@hF�DcmeE��rieN����0�����$ް����7Y�M@��s����m����D>�h�!+EV�1=�}���J�k�s��
��S�=��b@���S�xld89n ��������e��I^����+�ج�k$~�{�:Y�x����Z��1?�aS
��V�6����m�m�Fd�"dS�"نe���xz��p���Y�d���v�OvԮ�WBw��1!:K8O�Q��(.��<U��y���!��΁лx�x-KJd2#Ȫ�*���B����I�����V�G
��fA��j):���%�,8X3zeOc+a
�����׶ � M�b@�@�(4>{�����B[WƱҨƱ,�m`�-�
�uCԫ�C��V4*��m LjK;
K������c�^p� 2C�%ڷ&:��x����=�[UA�p1�Q�^��1��12���T��CV�� �sR/Lg�����r�C-RWXM�&�	Ѫ���4�����3��[���Ŭ��~��B���� J�-�m�k�t���_	��dl%S��B0$�3Sl�Q(��/�yF��f����G���Fjd�%iՈ�
�`���+w�R&
-0זO�:�*
�����zN���䭔2P4� ��eY��&>��q�|2,t&8��6�<U^������O�6#�3ᐇ�,���_��b��])/�6�Vo�v|�×w�\��c����ų��5_��e(��
�h^PTPn�F&�͟>�$H��V2��x�b���=�Q"�d�娾���,��ႎn��$�c��&"Q?b`jM�խ2u�0
���pG�)�6Cɗ�\��Ϫǉ��-�#:4D���݊�Ӷ���%w��8�!���9O
�� ���H�N�2��� �S{��ڎ������-�`"KR�sʃ�P�p�LT���\E0�_��Bii�����̜��q*��F��J�Oũ=;��,MZ�[D�M���y�Kb�翁[/���j<�eQjЌ�Qq'�T!۰,��BF��34A+��;�|��-�k$���+�Y��.
+^-�{{J?g~�cTGUo����1�ES���D�P�n��_O_���mx-�Kp�e�,��J�Q6j3	�i����"�b4��B���m�����.����ֿV�݇�x���ᥕ>�dy�3�P.�*�n<�����4���;��D����J+y�Ȏ�+ ��B�5�
�A�ו����Aś�(FZ�0<�����c`�R4>@8jj�r����~b"�'H�5�{�o.�+���B�|����6Mc�Ht0T܈η���o�\����=?М�>��	$<Rρ�#���m	i���,F>'���!�m(.O���m����:F��k��|�ĥH4��mj��L�����oKyj"t������&I�pl(*�8~�Z��|hd2��16Echh��J9�u�x[�m�c/�*O,e,b):�p*����K�K"!c�x��d<4ҢRst����9J��	�j�X�h�ߧ#�p+��C�&��2�G���pT���6#|�O5�i2��=�?C�LLh}�F�����C��uZ�Q��P����}����ʙ�6��e "<
M�et,d���BE�X
w��x�Ƌ:����l���-EQ��_R	�i���"�"��?p��9$>�N�-��č�4(�*�-������@y��}`T�hm_�l�=�~��d�D�~����l~pzt���<��.	r� ݰ4����Y8>��|._>(\��l�:�;��餍'���
�����"i��J���E��h��L�$�aP�)�#.t\Ӣ#��j �kq�𨬀�%����
�ʌU�9is��n���I����	��ȅX�����u�2�f/�I�� R��"�|�6�X "i�e}9g�
��g�!F�{���V�Vp+8�\���#(�E�%��Y,�>/
^��n��/��<7Q�@<��X���'��r}�䬭��0�S��B�*f�A�G�h�A_-&DĐK�K;�S ڇeH��8]��.օ�J�c�����*��y���(�&��T����@��^�snVgl�X�y*�J��푏L��
t	�=���YY��%و]
�	>�|h�u�r��k���J;�~���;?V� NQ:*H�Q~+�4}0:M� (��E�'�+��lt�B-~���K�,6��]Fc�nx�\RK�2Jfi��Aq`S�N�G�j%�\4�aU��4y�.*h�ȵ��{[�~��[l�zȼ8`�X�}�D� �M&wdrJ71��Z�ճe� �A���=�qX��!�d́.6�?NF����kZ�M�gl�A8�
�#^㜇�.��T���b�&%<(�h��FH���"6VA�>�Un@�FTH!�
M����?U�%���]��T[�rjr1�H�F+
�y	��a1�7�F��uHL�.8[�����^Ҙ&#i��|1�Gc+�Љ��eY���z[{xZ�-�4M�
�q1-����{��_����Ҵ�aQ}
^g��Y���sP�䶢��*��ŀ+M�J�tǱp�m���İ:�XФDk'�c9U�R:�P尜�$K+g���Q���*�
��T'��k��0XԾ���3�Б�.,�;�G�	�F��+}Ӧ����X�#��`�ަk��۫�ɏ�r��g��|��#��8p*O~(�-��\<���P��u'f�y�łLa�p�
"W*�nӶ���|�ƌV����o����EP��X�
n̮��ql_)gY�0��<�P����Ö���,#F�6� Ťr^2��2�*	eoKYqe�j�^�KTN����u�ǒ' ��H�tK�1���<�ie̟Wx�e�p������o��z�����`7.�ù���E=d�6��Χ���6C����$܆?a�Be������c���?d3��2�Ƿ{��4`�c���n����k�o��NT�0�Z���Ҁ%����i�BWK�b����v�&&�kaE���\e�N
���;��~6�Hc�Yi�fh24�
X�~�O���˂���5�<+kA��f }њ.�J��5D��-}N�T8�۸!�L�e�kA�����19���q*V[��s|�;�"n���>h����c�v�]�/%�U�BW"�!T�2ty�?��;d����
h�.W�vv�+��BV��X�]쵠� ;���du�U�C�Z$ڹ�Moj�6��>��$��m}�����mx�eE�G�YC�4 d�Ȏ����=��9�a���u@%&4^%ҧ�늢g����N"*���Wi�IZ�4sY�/�p]~����H�l��s������:����Du^��l��4����~I`���c�9�J�1�#�Bڿ
���+}��:䕚/苮�f�o�_I#��4�"J�6U\?�
RL��J�h�PEJA��!�V���!�)���
��Xy�SY��-�xq��LL�3I��.�d� ��q�Pu$���һB,Ԏ�
߲�8�q �L�q��AW�8����%Ԥ���t`��=�)�����t��/ e��2��h/?�V��3�Q���F�����!���P�>}q���8�3���_���~��7���     