<?php

use App\Models\Presentation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /** Run the migrations. */
    public function up(): void
    {
        Schema::table('presentations', function (Blueprint $table) {
            $table->boolean('is_internal')->default(false);
        });

        DB::table('presentations')
            ->whereExists(function (\Illuminate\Database\Query\Builder $query) {
                $query->from('user_presentation')
                    ->whereColumn('user_presentation.presentation_id', 'presentations.id')
                    ->selectRaw(1);
            })
            ->update(['is_internal' => true]);


        DB::statement('ALTER TABLE presentations ALTER COLUMN is_internal DROP DEFAULT');
    }

    /** Reverse the migrations. */
    public function down(): void
    {
        Schema::table('presentations', function (Blueprint $table) {
            $table->dropColumn('is_internal');
        });
    }
};
