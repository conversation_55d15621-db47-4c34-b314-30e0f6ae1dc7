<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('time_slots', function (Blueprint $table) {
            $table->string('room_email')->nullable();
            $table->string('room_name')->nullable();
            $table->string('room_address')->nullable();
        });
        Schema::table('events', function (Blueprint $table) {
            $table->string('room_email')->nullable();
            $table->string('room_name')->nullable();
            $table->string('room_address')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('time_slots', function (Blueprint $table) {
            //
        });
    }
};
