<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('integrations', function (Blueprint $table) {
            $table->jsonb('job_ad_fields')->default('[]');
            $table->jsonb('job_ad_custom_field_configuration')->nullable();
        });

        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->jsonb('custom_fields')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('integrations', function (Blueprint $table) {
            $table->dropColumn('job_ad_fields');
            $table->dropColumn('job_ad_custom_field_configuration');
        });

        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->dropColumn('custom_fields');
        });
    }
};
