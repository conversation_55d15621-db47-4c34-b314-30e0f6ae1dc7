<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    private string $oldColumn = 'prof_hu_working_schedule';
    private string $newColumn = 'prof_hu_working_schedules';

    /** Run the migrations. */
    public function up(): void
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->jsonb($this->newColumn)->nullable();
        });

        DB::table('structured_job_ads')
            ->whereNotNull($this->oldColumn)
            ->update([$this->newColumn => DB::raw("jsonb_build_array($this->oldColumn)")]);

        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->dropColumn($this->oldColumn);
        });
    }

    /** Reverse the migrations. */
    public function down(): void
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->integer($this->oldColumn)->nullable();
        });

        DB::table('structured_job_ads')
            ->whereNotNull($this->newColumn)
            ->whereJsonLength($this->newColumn, '>', 0)
            ->update([$this->oldColumn => DB::raw("($this->newColumn->0)::int")]);

        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->dropColumn($this->newColumn);
        });
    }
};
