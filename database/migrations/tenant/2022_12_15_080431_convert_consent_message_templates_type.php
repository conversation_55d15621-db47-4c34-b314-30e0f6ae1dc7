<?php

use App\Models\Template;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('templates')
            ->where('template_type', Template::TYPE_CONSENT_RENEWAL)
            ->update([
                "template_type" => Template::TYPE_CANDIDATE_MESSAGE
            ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
