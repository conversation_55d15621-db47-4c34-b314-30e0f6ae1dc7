<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class MigrateOldFileUploads extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (\Illuminate\Support\Facades\DB::connection(config('tenancy.db.tenant-connection-name'))->table('migrations')->get()->pluck('batch')->max() === 1) {
            return;
        }
        $tpl = \App\Models\Setting::get(\App\Models\Setting::KEY_FORM_TEMPLATE);

        if (isset($tpl['formFields'])) {
            foreach ($tpl['formFields'] as &$formField) {
                if ($formField['field_type'] === 'file') {
                    $formField['field_type'] = 'instantfile';
                }
            }

            \App\Models\Setting::set(\App\Models\Setting::KEY_FORM_TEMPLATE, $tpl);
        }

        \App\Models\Form::query()->get()->each(function (\App\Models\Form $form) {
            $formData = $form->form_data;
            foreach ($formData['formFields'] as &$formField) {
                if ($formField['field_type'] === 'file') {
                    $formField['field_type'] = 'instantfile';
                }
            }
            $form->form_data = $formData;

            $schema = $form->schema;

            foreach ($schema['schema'] as $key => $field) {
                if ($schema['schema'][$key]['type'] === 'file') {
                    $schema['schema'][$key]['type'] = 'instantfile';
                }
            }

            $form->schema = $schema;
            $form->save();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
