<?php

use App\Models\Presentation;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /** Run the migrations. */
    public function up(): void
    {
        Schema::table('presentations', function (Blueprint $table) {
            $table->foreignId('project_id')->nullable()->change()->references('id')->on('projects');
        });
    }

    /** Reverse the migrations. */
    public function down(): void
    {
        Schema::table('presentations', function (Blueprint $table) {
            Presentation::whereNull('project_id')
                ->get()
                // Ensure audit entries are created
                ->each(fn(Presentation $presentation) => $presentation->delete());

            $table->foreignId('project_id')->nullable(false)->change()->references('id')->on('projects');
        });
    }
};
