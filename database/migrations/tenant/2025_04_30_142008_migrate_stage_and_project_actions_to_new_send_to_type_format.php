<?php

use App\Models\Action;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('project_actions')->get()->each(function ($action) {
            $data = json_decode($action->data, true);
            $data['send_to_type'] = Action::SEND_TO_TYPE_ALL_USERS;

            DB::table('project_actions')->where('id', $action->id)->update(['data' => json_encode($data)]);
        });

        // Stage actions next
        DB::table('actions')->where('action_type', Action::TYPE_PING_USER)->get()->each(function ($action) {
            $data = json_decode($action->data, true);

            if (empty($data['user_ids'])) {
                $data['send_to_type'] = Action::SEND_TO_TYPE_ALL_USERS;
            } else {
                $data['send_to_type'] = Action::SEND_TO_TYPE_SELECT_USERS;
            }

            DB::table('actions')->where('id', $action->id)->update(['data' => json_encode($data)]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('new_send_to_type_format', function (Blueprint $table) {
            //
        });
    }
};
