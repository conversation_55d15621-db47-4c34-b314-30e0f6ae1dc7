<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /** Run the migrations. */
    public function up(): void
    {
        Schema::create('user_presentation', function (Blueprint $table) {
            $table->foreignId('presentation_id')->constrained('presentations');
            $table->foreignId('user_id')->constrained('users');
        });
    }

    /** Reverse the migrations. */
    public function down(): void
    {
        Schema::dropIfExists('user_presentation');
    }
};
