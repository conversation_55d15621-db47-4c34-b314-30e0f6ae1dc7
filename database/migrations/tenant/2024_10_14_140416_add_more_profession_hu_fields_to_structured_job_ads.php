<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->integer('prof_hu_working_schedule')->nullable();
            $table->integer('prof_hu_work_schedule')->nullable();
            $table->integer('prof_hu_qualification')->nullable();
            $table->integer('prof_hu_drivers_license')->nullable();
            $table->jsonb('prof_hu_langs')->default('[]');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            //
        });
    }
};
