<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('screening_criteria', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects');
            $table->string('criterion');
            $table->boolean('is_required')->default(false);
            $table->timestamps();
        });

        Schema::create('screening_criterion_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('application_id')->constrained('applications');
            $table->foreignId('screening_criterion_id')->constrained('screening_criteria');
            $table->string('result')->nullable();
            $table->text('details')->nullable();
            $table->timestamp('error_at')->nullable();
            $table->string('error_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('screening_criterion_responses');
        Schema::dropIfExists('screening_criteria');
    }
};
