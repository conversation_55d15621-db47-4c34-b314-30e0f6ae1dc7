<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndicesForDashboard extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /**
         * stages.project_id
         */
        Schema::table('candidates', function (Blueprint $table) {
            $table->index('deleted_at');
        });
        Schema::table('stages', function (Blueprint $table) {
            $table->index('project_id');
        });
        Schema::table('projects', function (Blueprint $table) {
            $table->index(['id', 'project_manager_id', 'accessible_only_members', 'deleted_at'], 'lookup_idx');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
