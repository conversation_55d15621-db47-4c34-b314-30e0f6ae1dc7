<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('custom_fonts', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
            $table->string('font_family');
            $table->foreignId('regular_font_file_id')->nullable()->constrained('files');
            $table->foreignId('bold_font_file_id')->nullable()->constrained('files');
            $table->foreignId('italic_font_file_id')->nullable()->constrained('files');
            $table->foreignId('bold_italic_font_file_id')->nullable()->constrained('files');

            $table->check(['regular_font_file_id', 'bold_font_file_id', 'italic_font_file_id', 'bold_italic_font_file_id'])
                ->whereNotNull('regular_font_file_id')
                ->whereNotNull('bold_font_file_id', 'or')
                ->whereNotNull('italic_font_file_id', 'or')
                ->whereNotNull('bold_italic_font_file_id', 'or');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_fonts');
    }
};
