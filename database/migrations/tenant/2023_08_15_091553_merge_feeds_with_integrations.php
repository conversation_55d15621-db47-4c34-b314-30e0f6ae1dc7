<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    const OUTPUT_TYPE_TO_REMOTE_TYPE_MAPPING = [
        'default' => 'feed',
        'cv_keskus' => 'cv_keskus_xml',
        'vp2' => 'vp2',
        'cv_bankas' => 'cv_bankas',
        'linkedin' => 'linkedin',
        'indeed' => 'indeed',
        'talent_com' => 'talent_com',
    ];

    // These feed types map to integration types that did not previously exist.
    const SKIP_OUTPUT_TYPES = ['default', 'vp2', 'linkedin'];

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tenantUuid = DB::connection()->getDatabaseName();

        // Add fields from feeds table to integrations
        Schema::table('integrations', function (Blueprint $table) {
            $table->string('li_company_id')->nullable();
            $table->string('feed_name')->nullable();
            $table->string('feed_slug')->nullable();
            $table->string('feed_conversion_type')->nullable();
            $table->unsignedBigInteger('feed_conversion_original_id')->nullable();
        });

        // Add fields from job ad feeds structured job ads to integration structured job ads
        Schema::table('integration_structured_job_ad', function (Blueprint $table) {
            $table->string('remote_url')->nullable()->after('remote_id');
        });

        $usedIntegrations = [];

        // Find all job ad feeds
        \DB::table('job_ad_feeds')->get()->each(function ($res) use ($tenantUuid, &$usedIntegrations) {
            // Find a directly linked integration.
            $integration = \DB::table('integrations')->find($res->integration_id);
            $feedConversionType = 'explicitly_linked';

            if (
                !$integration &&
                (!in_array($res->output_type, self::SKIP_OUTPUT_TYPES)) &&
                isset(self::OUTPUT_TYPE_TO_REMOTE_TYPE_MAPPING[$res->output_type])
            ) {
                // An integration was not directly linked to the feed, but it is possible there already is a matching integration.
                $integration = \DB::table('integrations')
                    ->where('remote_type', self::OUTPUT_TYPE_TO_REMOTE_TYPE_MAPPING[$res->output_type])
                    ->first();
                $feedConversionType = 'type_matched';
            }

            if ($integration) {
                // There was an integration corresponding to the feed. Let's move fields from the feed to the integration.
                if (in_array($integration->id, $usedIntegrations)) {
                    // More than one feed was linked to the same integration. Let's create a copy of the integration.
                    \Log::info("[{$tenantUuid}] <Feed ID {$res->id}> is linked to integration {$integration->id} that is already in use. Duplicating.");
                    $feedConversionType = $feedConversionType . '_and_duplicated';

                    $newIntegration = (array)$integration;
                    $removeKeys = ['id', 'created_at', 'updated_at', 'feed_name', 'feed_slug', 'feed_conversion_type', 'li_company_id'];

                    foreach ($removeKeys as $key) {
                        unset($newIntegration[$key]);
                    }

                    $newIntegration['created_at'] = \Carbon\Carbon::now();
                    $newIntegration['feed_conversion_original_id'] = $integration->id;

                    $newIntegrationId = \DB::table('integrations')->insertGetId($newIntegration);

                    $integration = \DB::table('integrations')->find($newIntegrationId);
                }

                \DB::table('integrations')->where('id', $integration->id)->update([
                    'feed_name' => $res->name,
                    'feed_slug' => $res->slug,
                    'li_company_id' => $res->li_company_id,
                    'updated_at' => \Carbon\Carbon::now(),
                    'feed_conversion_type' => $feedConversionType,
                ]);
            } else {
                // The feed was not linked to an integration. Let's create a new integration.
                \Log::info("[{$tenantUuid}] <Feed ID {$res->id}> does not have an integration linked. Creating a new one.");
                $feedConversionType = 'converted';

                $newIntegration = [
                    'name' => $res->name,
                    'remote_type' => self::OUTPUT_TYPE_TO_REMOTE_TYPE_MAPPING[$res->output_type] ?? 'feed',
                    'feed_name' => $res->name,
                    'feed_slug' => $res->slug,
                    'li_company_id' => $res->li_company_id,
                    'created_at' => \Carbon\Carbon::now(),
                    'updated_at' => \Carbon\Carbon::now(),
                    'feed_conversion_type' => $feedConversionType,
                ];

                $newIntegrationId = \DB::table('integrations')->insertGetId($newIntegration);
                $integration = \DB::table('integrations')->find($newIntegrationId);
            }

            // Link the feed to the integration where the fields were copied,
            // so we could easily redirect the user to the correct integration.
            \DB::table('job_ad_feeds')->where('id', $res->id)->update([
                'integration_id' => $integration->id,
            ]);

            $usedIntegrations[] = $integration->id;
            \Log::info("[{$tenantUuid}] <Feed ID {$res->id}> fields were copied into integration {$integration->id}");

            // Copy over all related job_ad_feed_structured_job_ads to integration_structured_job_ads
            $jobAdFeedSJACount = 0;
            \DB::table('job_ad_feed_structured_job_ad')->where('job_ad_feed_id', $res->id)->get()->each(function ($jobAdFeedRes) use (&$jobAdFeedSJACount, $integration) {
                \DB::table('integration_structured_job_ad')->insert([
                    'integration_id' => $integration->id,
                    'structured_job_ad_id' => $jobAdFeedRes->structured_job_ad_id,
                    'updated_at' => \Carbon\Carbon::now(),
                    'remote_url' => $jobAdFeedRes->remote_url,
                ]);
                $jobAdFeedSJACount += 1;
            });
            \Log::info("[{$tenantUuid}] <Feed ID {$res->id}> {$jobAdFeedSJACount} JobAdFeedStructuredJobAds were copied to IntegrationStructuredJobAds.");

            // Link related landings to correct integration
            \DB::table('landings')->where('data->content->feed_id', $res->id)->get()->each(function ($landingRes) use ($res, $tenantUuid, $integration) {
                $landingData = json_decode($landingRes->data, associative: true);
                $landingData['content']['integration_id'] = $integration->id;
                $landingPublishedData = json_decode($landingRes->published_data, associative: true);
                $landingPublishedData['content']['integration_id'] = $integration->id;
                \DB::table('landings')->where('id', $landingRes->id)->update([
                    'data' => json_encode($landingData),
                    'published_data' => json_encode($landingPublishedData),
                ]);
                \Log::info("[{$tenantUuid}] <Feed ID {$res->id}> Landing {$landingRes->id} content updated to link to integration {$integration->id}.");
            });
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('integrations', function (Blueprint $table) {
            $table->dropColumn('li_company_id');
            $table->dropColumn('feed_name');
            $table->dropColumn('feed_slug');
            $table->dropColumn('feed_conversion_type');
            $table->dropColumn('feed_conversion_original_id');
        });

        Schema::table('integration_structured_job_ad', function (Blueprint $table) {
            $table->dropColumn('remote_url');
        });
    }
};
