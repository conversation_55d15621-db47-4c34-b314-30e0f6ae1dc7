<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (\DB::getDriverName() !== 'sqlite') {
            \App\Classes\Database\Util::getSuperuserConnectionForCurrentTenant()
                ->statement('CREATE EXTENSION IF NOT EXISTS vector');
        }

        Schema::table('files', function (Blueprint $table) {
            if (\DB::getDriverName() !== 'sqlite') {
                $table->vector('embedding', 1536)->nullable();
            } else {
                $table->jsonb('embedding')->nullable();
            }
            $table->timestamp('embedding_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('files', function (Blueprint $table) {
            //
        });
    }
};
