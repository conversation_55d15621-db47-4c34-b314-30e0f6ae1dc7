<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->text('html_override')->nullable();
            $table->boolean('html_use_override')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->dropColumn('html_override');
            $table->dropColumn('html_use_override');
        });
    }
};
