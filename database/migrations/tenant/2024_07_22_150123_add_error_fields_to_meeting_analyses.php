<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('meeting_analyses', function (Blueprint $table) {
            $table->text('last_error')->nullable();
            $table->timestamp('error_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('meeting_analyses', function (Blueprint $table) {
            $table->dropColumn('last_error');
            $table->dropColumn('error_at');
        });
    }
};
