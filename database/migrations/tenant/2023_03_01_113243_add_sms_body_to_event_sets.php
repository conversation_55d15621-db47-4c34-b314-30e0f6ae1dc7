<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('event_sets', function (Blueprint $table) {
            $table->text('sms_body')->nullable()->after('body');
            $table->tinyInteger('send_sms')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('event_sets', function (Blueprint $table) {
            $table->dropColumn('sms_body');
            $table->dropColumn('send_sms');
        });
    }
};
