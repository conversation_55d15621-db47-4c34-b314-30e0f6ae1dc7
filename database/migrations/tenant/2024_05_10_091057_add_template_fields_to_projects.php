<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->boolean('is_template')->default(false);
            $table->foreignId('template_id')->nullable()->constrained('projects');
        });

        Schema::table('actions', function (Blueprint $table) {
            $table->boolean('is_template')->default(false);
            $table->foreignId('template_id')->nullable()->constrained('actions');
        });

        Schema::table('stages', function (Blueprint $table) {
            $table->boolean('is_template')->default(false);
            $table->foreignId('template_id')->nullable()->constrained('stages');
        });
    }

    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('is_template');
            $table->dropForeign(['template_id']);
            $table->dropColumn('template_id');
        });

        Schema::table('actions', function (Blueprint $table) {
            $table->dropColumn('is_template');
            $table->dropForeign(['template_id']);
            $table->dropColumn('template_id');
        });

        Schema::table('stages', function (Blueprint $table) {
            $table->dropColumn('is_template');
            $table->dropForeign(['template_id']);
            $table->dropColumn('template_id');
        });
    }
};
