<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEmployerFieldsToStructuredJobAds extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->string('employer_logo_location')->nullable();
            $table->string('employer_video_url')->nullable();
            $table->string('employer_web_url')->nullable();
            $table->text('employer_about')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('structured_job_ads', function (Blueprint $table) {
            $table->dropColumn('employer_logo_location');
            $table->dropColumn('employer_video_url');
            $table->dropColumn('employer_web_url');
            $table->dropColumn('employer_about');
        });
    }
}
