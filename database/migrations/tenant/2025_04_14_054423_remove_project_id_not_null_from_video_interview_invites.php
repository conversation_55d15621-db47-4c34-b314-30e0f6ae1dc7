<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /** Run the migrations. */
    public function up(): void
    {
        Schema::table('video_interview_invites', function () {
            DB::statement(<<<'SQL'
            ALTER TABLE video_interview_invites
            ALTER COLUMN project_id DROP NOT NULL;
            SQL
            );
        });
    }

    /** Reverse the migrations. */
    public function down(): void
    {
        Schema::table('video_interview_invites', function () {
            DB::statement(<<<'SQL'
            ALTER TABLE video_interview_invites
            ALTER COLUMN project_id SET NOT NULL;
            SQL
            );
        });
    }
};
