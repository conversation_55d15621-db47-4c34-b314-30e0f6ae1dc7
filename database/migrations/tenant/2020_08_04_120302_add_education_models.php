<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEducationModels extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('educations', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('candidate_id');

            $table->tinyInteger('degree');
            $table->string('programme')->nullable();
            $table->string('institution')->nullable();

            $table->integer('start_year')->nullable();
            $table->integer('end_year')->nullable();

            $table->timestamps();

            $table->foreign('candidate_id')->references('id')->on('candidates');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('educations');
    }
}
