<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /** Run the migrations. */
    public function up(): void
    {
        Schema::table('video_interview_responses', function (Blueprint $table) {
            $table->timestamp('confirmed_at')->nullable();
            $table->boolean('ran_out_of_time')->default(false);
            $table->check(['confirmed_at', 'video_id'], 'video_interview_responses_confirmed_at_video_id_check')
                ->whereNull('confirmed_at')
                ->whereNotNull('video_id', 'or');

        });

        DB::statement('ALTER TABLE video_interview_responses RENAME COLUMN attempts TO legacy_attempt_count;');
        DB::statement('
                ALTER TABLE video_interview_responses
                ALTER COLUMN legacy_attempt_count DROP NOT NULL,
                ALTER COLUMN legacy_attempt_count DROP DEFAULT;
            ');
        DB::statement('
                CREATE UNIQUE INDEX video_interview_responses_question_invite_idx
                ON video_interview_responses (video_interview_question_id, video_interview_invite_id)
                WHERE confirmed_at IS NOT NULL;
            ');

        DB::table('video_interview_responses', 'main_table')
            ->wherenotExists(function (Builder $query) {
                $query->selectRaw(1)
                    ->from('video_interview_responses', 'secondary_table')
                    ->whereColumn('main_table.video_interview_question_id', 'secondary_table.video_interview_question_id')
                    ->whereColumn('main_table.video_interview_invite_id', 'secondary_table.video_interview_invite_id')
                    ->whereColumn('main_table.id', '<', 'secondary_table.id');
            })
            ->update(['confirmed_at' => DB::raw('COALESCE(main_table.created_at, NOW())')]);
    }

    /** Reverse the migrations. */
    public function down(): void
    {
        $responseCountQuery = DB::table('video_interview_responses')
            ->select('video_interview_question_id', 'video_interview_invite_id', DB::raw('COUNT(*) as response_count'))
            ->groupBy('video_interview_question_id', 'video_interview_invite_id');

        DB::table('video_interview_responses', 'main_table')
            ->whereNull('legacy_attempt_count')
            ->joinSub($responseCountQuery, 'secondary_table', function (JoinClause $join) {
                $join->on('main_table.video_interview_question_id', '=', 'secondary_table.video_interview_question_id')
                    ->on('main_table.video_interview_invite_id', '=', 'secondary_table.video_interview_invite_id');
            })
            ->updateFrom(['legacy_attempt_count' => DB::raw('secondary_table.response_count')]);

        DB::statement('DROP INDEX IF EXISTS video_interview_responses_question_invite_idx;');
        DB::statement('
            ALTER TABLE video_interview_responses
            ALTER COLUMN legacy_attempt_count SET DEFAULT 0,
            ALTER COLUMN legacy_attempt_count SET NOT NULL;
        ');
        DB::statement('ALTER TABLE video_interview_responses RENAME COLUMN legacy_attempt_count TO attempts;');

        Schema::table('video_interview_responses', function (Blueprint $table) {
            $table->dropCheck('video_interview_responses_confirmed_at_video_id_check');
            $table->dropColumn('confirmed_at');
            $table->dropColumn('ran_out_of_time');
        });
    }
};
